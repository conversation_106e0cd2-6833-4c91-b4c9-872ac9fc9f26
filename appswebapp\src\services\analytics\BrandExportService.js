import BaseService from '@/services/BaseService'
import analyticsConstants from '@/shared/analytics/constants'

class BrandExportService extends BaseService {
  constructor (apiPathBase) {
    super()
    this.apiPathBase = apiPathBase
  }

  getBrandExports () {
    return this.axios.get(`${this.apiPathBase}brandexports`)
  };
  getBrandExportsLogs (filter) {
    return this.axios.get(`${this.apiPathBase}brandexports/logs`, { params: filter })
  };
  getBrandExportsLogDetails (logId) {
    return this.axios.get(`${this.apiPathBase}brandexports/logs/${logId}/details`)
  };
  getBrandExportsServiceSettings () {
    return this.axios.get(`${this.apiPathBase}brandexports/servicesettings`)
  };
  updateBrandExportServiceSettings (data) {
    return this.axios.post(`${this.apiPathBase}brandexports/servicesettings`, data)
  };
  rebuildBrandExportSettings (data) {
    return this.axios.post(`${this.apiPathBase}brandexports/rebuild`, data)
  };
  updateBrandExportSettings (data) {
    return this.axios.post(`${this.apiPathBase}brandexports/update`, data)
  };
  updateBrandExportReportSettings (data) {
    return this.axios.post(`${this.apiPathBase}brandexports/${data.exportId}/report/update`, data)
  };
}

export default new BrandExportService(analyticsConstants.googleAnalyticsDefaultApiEndpointsBasePath)
