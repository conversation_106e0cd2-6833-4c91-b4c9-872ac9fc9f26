{"runtimeTarget": {"name": ".NETStandard,Version=v2.0/", "signature": ""}, "compilationOptions": {}, "targets": {".NETStandard,Version=v2.0": {}, ".NETStandard,Version=v2.0/": {"EBizAutos.Apps.Authentication.CommonLib/*******": {"dependencies": {"Amazon.AspNetCore.DataProtection.SSM": "1.1.0", "CommonLibCore": "1.0.0", "EBizAutos.ApplicationCommonLib": "1.0.0", "EBizAutos.Apps.CommonLib": "1.0.0", "Microsoft.AspNetCore.Authentication.Cookies": "2.1.2", "Microsoft.AspNetCore.Authentication.JwtBearer": "2.1.2", "Microsoft.AspNetCore.Authorization": "2.1.2", "Microsoft.AspNetCore.DataProtection.Extensions": "2.1.1", "Microsoft.AspNetCore.Mvc": "2.1.3", "Microsoft.AspNetCore.Mvc.Core": "2.1.3", "Microsoft.IdentityModel.Tokens": "5.3.0", "NETStandard.Library": "2.0.3", "System.Security.Claims": "4.3.0", "CommonLibCore.Reference": "*******", "FoundationCommonLib.Reference": "*******"}, "runtime": {"EBizAutos.Apps.Authentication.CommonLib.dll": {}}}, "Amazon.AspNetCore.DataProtection.SSM/1.1.0": {"dependencies": {"AWSSDK.Extensions.NETCore.Setup": "3.7.1", "AWSSDK.SimpleSystemsManagement": "3.7.3.24", "Microsoft.AspNetCore.DataProtection.Extensions": "2.1.1"}, "runtime": {"lib/netstandard2.0/Amazon.AspNetCore.DataProtection.SSM.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.1.0.0"}}}, "Amazon.Extensions.Configuration.SystemsManager/2.1.1": {"dependencies": {"AWSSDK.Extensions.NETCore.Setup": "3.7.1", "AWSSDK.SimpleSystemsManagement": "3.7.3.24", "Microsoft.Extensions.Configuration": "2.1.1", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Amazon.Extensions.Configuration.SystemsManager.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AWSSDK.Core/**********": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0"}, "runtime": {"lib/netstandard2.0/AWSSDK.Core.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "**********"}}}, "AWSSDK.Extensions.NETCore.Setup/3.7.1": {"dependencies": {"AWSSDK.Core": "**********", "Microsoft.Extensions.Configuration.Abstractions": "2.1.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1"}, "runtime": {"lib/netstandard2.0/AWSSDK.Extensions.NETCore.Setup.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.7.1.0"}}}, "AWSSDK.S3/**********": {"dependencies": {"AWSSDK.Core": "**********"}, "runtime": {"lib/netstandard2.0/AWSSDK.S3.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "**********"}}}, "AWSSDK.SecurityToken/**********": {"dependencies": {"AWSSDK.Core": "**********"}, "runtime": {"lib/netstandard2.0/AWSSDK.SecurityToken.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "**********"}}}, "AWSSDK.SimpleSystemsManagement/3.7.3.24": {"dependencies": {"AWSSDK.Core": "**********"}, "runtime": {"lib/netstandard2.0/AWSSDK.SimpleSystemsManagement.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.7.3.24"}}}, "Castle.Core/4.4.1": {"dependencies": {"NETStandard.Library": "2.0.3", "System.Collections.Specialized": "4.3.0", "System.ComponentModel": "4.3.0", "System.ComponentModel.TypeConverter": "4.3.0", "System.Diagnostics.TraceSource": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.TypeExtensions": "4.4.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard1.5/Castle.Core.dll": {"assemblyVersion": "*******", "fileVersion": "4.4.1.0"}}}, "CompareNETObjects/4.57.0": {"dependencies": {"Microsoft.CSharp": "4.5.0"}, "runtime": {"lib/netstandard2.0/KellermanSoftware.Compare-NET-Objects.dll": {"assemblyVersion": "4.57.0.0", "fileVersion": "4.57.0.0"}}}, "Dapper/1.50.5": {"dependencies": {"System.Data.SqlClient": "4.6.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.TypeExtensions": "4.4.0"}, "runtime": {"lib/netstandard2.0/Dapper.dll": {"assemblyVersion": "1.50.5.0", "fileVersion": "1.50.5.0"}}}, "DeepCloner/0.10.2": {"dependencies": {"NETStandard.Library": "2.0.3"}, "runtime": {"lib/netstandard1.3/DeepCloner.dll": {"assemblyVersion": "0.10.0.0", "fileVersion": "0.10.2.0"}}}, "DnsClient/1.6.1": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0", "System.Buffers": "4.5.1"}, "runtime": {"lib/netstandard2.0/DnsClient.dll": {"assemblyVersion": "1.6.1.0", "fileVersion": "1.6.1.0"}}}, "Elasticsearch.Net/7.1.0": {"dependencies": {"System.Buffers": "4.5.1", "System.Diagnostics.DiagnosticSource": "4.5.1", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.Lightweight": "4.3.0"}, "runtime": {"lib/netstandard2.0/Elasticsearch.Net.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.1.0.0"}}}, "EnyimMemcachedCore/2.1.8": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.1", "Microsoft.Extensions.Caching.Abstractions": "2.1.1", "Microsoft.Extensions.Configuration.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "2.1.1", "Newtonsoft.Json": "13.0.1", "Newtonsoft.Json.Bson": "1.0.1"}, "runtime": {"lib/netstandard2.0/EnyimMemcachedCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Experimental.System.Messaging/1.0.0": {"runtime": {"lib/netstandard2.0/Experimental.System.Messaging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "GreenPipes/4.0.1": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "System.Reflection.Emit": "4.7.0"}, "runtime": {"lib/netstandard2.0/GreenPipes.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Magick.NET-Q16-AnyCPU/7.11.0": {"runtime": {"lib/netstandard20/Magick.NET-Q16-AnyCPU.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.AspNetCore.Antiforgery/2.1.1": {"dependencies": {"Microsoft.AspNetCore.DataProtection": "2.1.1", "Microsoft.AspNetCore.Http.Abstractions": "2.1.1", "Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.AspNetCore.WebUtilities": "2.1.1", "Microsoft.Extensions.ObjectPool": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Antiforgery.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Authentication/2.1.2": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.1.1", "Microsoft.AspNetCore.DataProtection": "2.1.1", "Microsoft.AspNetCore.Http": "2.1.1", "Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1", "Microsoft.Extensions.WebEncoders": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.dll": {"assemblyVersion": "2.1.2.0", "fileVersion": "2.1.2.18207"}}}, "Microsoft.AspNetCore.Authentication.Abstractions/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Authentication.Cookies/2.1.2": {"dependencies": {"Microsoft.AspNetCore.Authentication": "2.1.2"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Cookies.dll": {"assemblyVersion": "2.1.2.0", "fileVersion": "2.1.2.18207"}}}, "Microsoft.AspNetCore.Authentication.Core/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.1.1", "Microsoft.AspNetCore.Http": "2.1.1", "Microsoft.AspNetCore.Http.Extensions": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/2.1.2": {"dependencies": {"Microsoft.AspNetCore.Authentication": "2.1.2", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "5.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "2.1.2.0", "fileVersion": "2.1.2.18207"}}}, "Microsoft.AspNetCore.Authorization/2.1.2": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll": {"assemblyVersion": "2.1.2.0", "fileVersion": "2.1.2.18207"}}}, "Microsoft.AspNetCore.Authorization.Policy/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.1.1", "Microsoft.AspNetCore.Authorization": "2.1.2"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.Policy.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Cors/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.Extensions.Configuration.Abstractions": "2.1.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Cors.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Cryptography.Internal/2.1.1": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.DataProtection/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "2.1.1", "Microsoft.AspNetCore.DataProtection.Abstractions": "2.1.1", "Microsoft.AspNetCore.Hosting.Abstractions": "2.1.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1", "Microsoft.Win32.Registry": "5.0.0", "System.Security.Cryptography.Xml": "4.5.0", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.DataProtection.Abstractions/2.1.1": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.DataProtection.Extensions/2.1.1": {"dependencies": {"Microsoft.AspNetCore.DataProtection": "2.1.1", "Microsoft.Extensions.DependencyInjection": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Diagnostics.Abstractions/2.1.1": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Hosting.Abstractions/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.1.1", "Microsoft.AspNetCore.Http.Abstractions": "2.1.1", "Microsoft.Extensions.Hosting.Abstractions": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.1.1", "Microsoft.Extensions.Configuration.Abstractions": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Html.Abstractions/2.1.1": {"dependencies": {"System.Text.Encodings.Web": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Html.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Http/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.1", "Microsoft.AspNetCore.WebUtilities": "2.1.1", "Microsoft.Extensions.ObjectPool": "2.1.1", "Microsoft.Extensions.Options": "2.1.1", "Microsoft.Net.Http.Headers": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Http.Abstractions/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.1.1", "System.Text.Encodings.Web": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Http.Extensions/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.1", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.1", "Microsoft.Net.Http.Headers": "2.1.1", "System.Buffers": "4.5.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Http.Features/2.1.1": {"dependencies": {"Microsoft.Extensions.Primitives": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.JsonPatch/2.1.1": {"dependencies": {"Microsoft.CSharp": "4.5.0", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.JsonPatch.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Localization/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.Extensions.Localization.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Mvc/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Mvc.ApiExplorer": "2.1.3", "Microsoft.AspNetCore.Mvc.Cors": "2.1.3", "Microsoft.AspNetCore.Mvc.DataAnnotations": "2.1.3", "Microsoft.AspNetCore.Mvc.Formatters.Json": "2.1.3", "Microsoft.AspNetCore.Mvc.Localization": "2.1.3", "Microsoft.AspNetCore.Mvc.Razor.Extensions": "2.1.1", "Microsoft.AspNetCore.Mvc.RazorPages": "2.1.3", "Microsoft.AspNetCore.Mvc.TagHelpers": "2.1.3", "Microsoft.AspNetCore.Mvc.ViewFeatures": "2.1.3", "Microsoft.AspNetCore.Razor.Design": "2.1.1", "Microsoft.Extensions.Caching.Memory": "2.1.1", "Microsoft.Extensions.DependencyInjection": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.3.18263"}}}, "Microsoft.AspNetCore.Mvc.Abstractions/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Routing.Abstractions": "2.1.1", "Microsoft.Net.Http.Headers": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.3.18263"}}}, "Microsoft.AspNetCore.Mvc.ApiExplorer/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.1.3"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.3.18263"}}}, "Microsoft.AspNetCore.Mvc.Core/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.1.1", "Microsoft.AspNetCore.Authorization.Policy": "2.1.1", "Microsoft.AspNetCore.Hosting.Abstractions": "2.1.1", "Microsoft.AspNetCore.Http": "2.1.1", "Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.AspNetCore.Mvc.Abstractions": "2.1.3", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "2.1.1", "Microsoft.AspNetCore.Routing": "2.1.1", "Microsoft.Extensions.DependencyInjection": "2.1.1", "Microsoft.Extensions.DependencyModel": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "System.Diagnostics.DiagnosticSource": "4.5.1", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Core.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.3.18263"}}}, "Microsoft.AspNetCore.Mvc.Cors/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Cors": "2.1.1", "Microsoft.AspNetCore.Mvc.Core": "2.1.3"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Cors.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.3.18263"}}}, "Microsoft.AspNetCore.Mvc.DataAnnotations/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.1.3", "Microsoft.Extensions.Localization": "2.1.1", "System.ComponentModel.Annotations": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.3.18263"}}}, "Microsoft.AspNetCore.Mvc.Formatters.Json/2.1.3": {"dependencies": {"Microsoft.AspNetCore.JsonPatch": "2.1.1", "Microsoft.AspNetCore.Mvc.Core": "2.1.3"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Formatters.Json.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.3.18263"}}}, "Microsoft.AspNetCore.Mvc.Localization/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Localization": "2.1.1", "Microsoft.AspNetCore.Mvc.Razor": "2.1.3", "Microsoft.Extensions.DependencyInjection": "2.1.1", "Microsoft.Extensions.Localization": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.3.18263"}}}, "Microsoft.AspNetCore.Mvc.Razor/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Mvc.Razor.Extensions": "2.1.1", "Microsoft.AspNetCore.Mvc.ViewFeatures": "2.1.3", "Microsoft.AspNetCore.Razor.Runtime": "2.1.1", "Microsoft.CodeAnalysis.CSharp": "2.8.0", "Microsoft.CodeAnalysis.Razor": "2.1.1", "Microsoft.Extensions.Caching.Memory": "2.1.1", "Microsoft.Extensions.FileProviders.Composite": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.3.18263"}}}, "Microsoft.AspNetCore.Mvc.Razor.Extensions/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "2.1.1", "Microsoft.CodeAnalysis.Razor": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Mvc.RazorPages/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Mvc.Razor": "2.1.3"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.RazorPages.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.3.18263"}}}, "Microsoft.AspNetCore.Mvc.TagHelpers/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Mvc.Razor": "2.1.3", "Microsoft.AspNetCore.Razor.Runtime": "2.1.1", "Microsoft.AspNetCore.Routing.Abstractions": "2.1.1", "Microsoft.Extensions.Caching.Memory": "2.1.1", "Microsoft.Extensions.FileSystemGlobbing": "2.1.1", "Microsoft.Extensions.Primitives": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.TagHelpers.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.3.18263"}}}, "Microsoft.AspNetCore.Mvc.ViewFeatures/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Antiforgery": "2.1.1", "Microsoft.AspNetCore.Diagnostics.Abstractions": "2.1.1", "Microsoft.AspNetCore.Html.Abstractions": "2.1.1", "Microsoft.AspNetCore.Mvc.Core": "2.1.3", "Microsoft.AspNetCore.Mvc.DataAnnotations": "2.1.3", "Microsoft.AspNetCore.Mvc.Formatters.Json": "2.1.3", "Microsoft.Extensions.WebEncoders": "2.1.1", "Newtonsoft.Json.Bson": "1.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.ViewFeatures.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.3.18263"}}}, "Microsoft.AspNetCore.Razor/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Html.Abstractions": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Razor.Design/2.1.1": {}, "Microsoft.AspNetCore.Razor.Language/2.1.1": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Razor.Runtime/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Html.Abstractions": "2.1.1", "Microsoft.AspNetCore.Razor": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.1.1": {"dependencies": {"Microsoft.Extensions.Primitives": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Routing/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.AspNetCore.Routing.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.ObjectPool": "2.1.1", "Microsoft.Extensions.Options": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Routing.Abstractions/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.StaticFiles/2.0.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.1.1", "Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.WebEncoders": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.StaticFiles.dll": {"assemblyVersion": "*******", "fileVersion": "2.0.0.17205"}}}, "Microsoft.AspNetCore.WebUtilities/2.1.1": {"dependencies": {"Microsoft.Net.Http.Headers": "2.1.1", "System.Text.Encodings.Web": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Bcl.AsyncInterfaces/5.0.0": {"dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.CodeAnalysis.Analyzers/1.1.0": {}, "Microsoft.CodeAnalysis.Common/2.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "1.1.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Collections.Immutable": "1.3.1", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.FileVersionInfo": "4.3.0", "System.Diagnostics.StackTrace": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Globalization": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Metadata": "1.4.2", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.CodePages": "5.0.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Parallel": "4.3.0", "System.Threading.Thread": "4.3.0", "System.ValueTuple": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0", "System.Xml.XPath.XDocument": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard1.3/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "2.8.0.0", "fileVersion": "2.8.0.62830"}}}, "Microsoft.CodeAnalysis.CSharp/2.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "2.8.0"}, "runtime": {"lib/netstandard1.3/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "2.8.0.0", "fileVersion": "2.8.0.62830"}}}, "Microsoft.CodeAnalysis.Razor/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "2.1.1", "Microsoft.CodeAnalysis.CSharp": "2.8.0", "Microsoft.CodeAnalysis.Common": "2.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.CSharp/4.5.0": {"runtime": {"lib/netstandard2.0/Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "Microsoft.DotNet.PlatformAbstractions/2.1.0": {"dependencies": {"System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Reflection.TypeExtensions": "4.4.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0"}, "runtime": {"lib/netstandard1.3/Microsoft.DotNet.PlatformAbstractions.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "2.1.0.0"}}}, "Microsoft.Extensions.Caching.Abstractions/2.1.1": {"dependencies": {"Microsoft.Extensions.Primitives": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.Caching.Memory/2.1.1": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "2.1.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.Configuration/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.Configuration.Abstractions/2.1.1": {"dependencies": {"Microsoft.Extensions.Primitives": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.Configuration.Binder/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.Configuration.FileExtensions/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration": "2.1.1", "Microsoft.Extensions.FileProviders.Physical": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.Configuration.Json/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration": "2.1.1", "Microsoft.Extensions.Configuration.FileExtensions": "2.1.1", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.DependencyInjection/2.1.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/2.1.1": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.DependencyModel/2.1.0": {"dependencies": {"Microsoft.DotNet.PlatformAbstractions": "2.1.0", "Newtonsoft.Json": "13.0.1", "System.Diagnostics.Debug": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Linq": "4.3.0"}, "runtime": {"lib/netstandard1.6/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "2.1.0.0"}}}, "Microsoft.Extensions.FileProviders.Abstractions/2.1.1": {"dependencies": {"Microsoft.Extensions.Primitives": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.FileProviders.Composite/2.1.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Composite.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.FileProviders.Embedded/2.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.dll": {"assemblyVersion": "*******", "fileVersion": "2.0.0.17205"}}}, "Microsoft.Extensions.FileProviders.Physical/2.1.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "2.1.1", "Microsoft.Extensions.FileSystemGlobbing": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.FileSystemGlobbing/2.1.1": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.Hosting.Abstractions/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "2.1.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.Localization/2.1.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1", "Microsoft.Extensions.Localization.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.Localization.Abstractions/2.1.1": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.Localization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.Logging.Abstractions/2.1.1": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.ObjectPool/2.1.1": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.Options/2.1.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1", "Microsoft.Extensions.Primitives": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "2.1.1", "Microsoft.Extensions.Configuration.Binder": "2.1.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.Primitives/2.1.1": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.WebEncoders/2.1.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1", "System.Text.Encodings.Web": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.WebEncoders.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.IdentityModel.Logging/5.3.0": {"dependencies": {"System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "5.3.0.0", "fileVersion": "5.3.0.51005"}}}, "Microsoft.IdentityModel.Protocols/5.2.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "5.3.0", "Microsoft.IdentityModel.Tokens": "5.3.0", "NETStandard.Library": "2.0.3", "System.Collections.Specialized": "4.3.0", "System.Diagnostics.Contracts": "4.3.0", "System.Net.Http": "4.3.0"}, "runtime": {"lib/netstandard1.4/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "5.2.0.0", "fileVersion": "5.2.0.50129"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/5.2.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "5.2.0", "NETStandard.Library": "2.0.3", "Newtonsoft.Json": "13.0.1", "System.Dynamic.Runtime": "4.3.0", "System.IdentityModel.Tokens.Jwt": "5.2.0"}, "runtime": {"lib/netstandard1.4/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "5.2.0.0", "fileVersion": "5.2.0.50129"}}}, "Microsoft.IdentityModel.Tokens/5.3.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "5.3.0", "Newtonsoft.Json": "13.0.1", "System.Collections": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Runtime.Serialization.Xml": "4.3.0", "System.Security.Claims": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "5.3.0.0", "fileVersion": "5.3.0.51005"}}}, "Microsoft.Net.Http.Headers/2.1.1": {"dependencies": {"Microsoft.Extensions.Primitives": "2.1.1", "System.Buffers": "4.5.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.NETCore.Platforms/2.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "MongoDB.Bson/2.21.0": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "runtime": {"lib/netstandard2.0/MongoDB.Bson.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MongoDB.Driver/2.21.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "2.1.1", "MongoDB.Bson": "2.21.0", "MongoDB.Driver.Core": "2.21.0", "MongoDB.Libmongocrypt": "1.8.0"}, "runtime": {"lib/netstandard2.0/MongoDB.Driver.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MongoDB.Driver.Core/2.21.0": {"dependencies": {"AWSSDK.SecurityToken": "**********", "DnsClient": "1.6.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "MongoDB.Bson": "2.21.0", "MongoDB.Libmongocrypt": "1.8.0", "SharpCompress": "0.30.1", "Snappier": "1.0.0", "System.Buffers": "4.5.1", "ZstdSharp.Port": "0.6.2"}, "runtime": {"lib/netstandard2.0/MongoDB.Driver.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MongoDB.Driver.GridFS/2.21.0": {"dependencies": {"MongoDB.Bson": "2.21.0", "MongoDB.Driver": "2.21.0", "MongoDB.Driver.Core": "2.21.0"}, "runtime": {"lib/netstandard2.0/MongoDB.Driver.GridFS.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MongoDB.Libmongocrypt/1.8.0": {"runtime": {"lib/netstandard2.0/MongoDB.Libmongocrypt.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}}}, "MongolianBarbecue/1.0.0": {"dependencies": {"MongoDB.Driver": "2.21.0"}, "runtime": {"lib/netstandard2.0/MongolianBarbecue.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NEST/7.1.0": {"dependencies": {"Elasticsearch.Net": "7.1.0"}, "runtime": {"lib/netstandard2.0/Nest.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.1.0.0"}}}, "NETStandard.Library/2.0.3": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0"}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "1*******", "fileVersion": "13.0.1.25517"}}}, "Newtonsoft.Json.Bson/1.0.1": {"dependencies": {"NETStandard.Library": "2.0.3", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard1.3/Newtonsoft.Json.Bson.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.1.20722"}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Data.SqlClient.sni/4.5.0": {"dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.native.System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {}, "Scrutor/3.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1", "Microsoft.Extensions.DependencyModel": "2.1.0"}, "runtime": {"lib/netstandard2.0/Scrutor.dll": {"assemblyVersion": "3.0.1.0", "fileVersion": "3.0.1.0"}}}, "SharpCompress/0.30.1": {"dependencies": {"System.Memory": "4.5.5", "System.Text.Encoding.CodePages": "5.0.0"}, "runtime": {"lib/netstandard2.0/SharpCompress.dll": {"assemblyVersion": "0.30.1.0", "fileVersion": "0.30.1.0"}}}, "Snappier/1.0.0": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Snappier.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore/4.0.1": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "4.0.1", "Swashbuckle.AspNetCore.SwaggerGen": "4.0.1", "Swashbuckle.AspNetCore.SwaggerUI": "4.0.1"}, "runtime": {"lib/netstandard2.0/Swashbuckle.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.Annotations/4.0.1": {"dependencies": {"Swashbuckle.AspNetCore.SwaggerGen": "4.0.1"}, "runtime": {"lib/netstandard2.0/Swashbuckle.AspNetCore.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.Filters/4.5.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "2.1.1", "Scrutor": "3.0.1", "Swashbuckle.AspNetCore.Annotations": "4.0.1", "Swashbuckle.AspNetCore.SwaggerGen": "4.0.1"}, "runtime": {"lib/netstandard2.0/Swashbuckle.AspNetCore.Filters.dll": {"assemblyVersion": "4.5.5.0", "fileVersion": "4.5.5.0"}}}, "Swashbuckle.AspNetCore.Swagger/4.0.1": {"dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.1.3", "Microsoft.AspNetCore.Mvc.Formatters.Json": "2.1.3"}, "runtime": {"lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerGen/4.0.1": {"dependencies": {"Microsoft.AspNetCore.Mvc.ApiExplorer": "2.1.3", "Microsoft.AspNetCore.Mvc.Core": "2.1.3", "Microsoft.AspNetCore.Mvc.DataAnnotations": "2.1.3", "Swashbuckle.AspNetCore.Swagger": "4.0.1"}, "runtime": {"lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerUI/4.0.1": {"dependencies": {"Microsoft.AspNetCore.Routing": "2.1.1", "Microsoft.AspNetCore.StaticFiles": "2.0.0", "Microsoft.Extensions.FileProviders.Embedded": "2.0.0", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.AppContext/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.6/System.AppContext.dll": {"assemblyVersion": "4.1.1.0", "fileVersion": "4.6.24705.1"}}}, "System.Buffers/4.5.1": {"runtime": {"lib/netstandard2.0/System.Buffers.dll": {"assemblyVersion": "4.0.3.0", "fileVersion": "4.6.28619.1"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Collections.Concurrent.dll": {"assemblyVersion": "4.0.13.0", "fileVersion": "4.6.24705.1"}}}, "System.Collections.Immutable/1.3.1": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.Collections.Immutable.dll": {"assemblyVersion": "1.2.1.0", "fileVersion": "4.6.24816.1"}}}, "System.Collections.NonGeneric/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Collections.Specialized/4.3.0": {"dependencies": {"System.Collections.NonGeneric": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.ComponentModel/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.ComponentModel.Annotations/4.5.0": {"runtime": {"lib/netstandard2.0/System.ComponentModel.Annotations.dll": {"assemblyVersion": "4.2.1.0", "fileVersion": "4.6.26515.6"}}}, "System.ComponentModel.Primitives/4.3.0": {"dependencies": {"System.ComponentModel": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.ComponentModel.Primitives.dll": {"assemblyVersion": "4.1.1.0", "fileVersion": "4.6.24705.1"}}}, "System.ComponentModel.TypeConverter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Collections.NonGeneric": "4.3.0", "System.Collections.Specialized": "4.3.0", "System.ComponentModel": "4.3.0", "System.ComponentModel.Primitives": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.4.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.5/System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "4.1.1.0", "fileVersion": "4.6.24705.1"}}}, "System.Configuration.ConfigurationManager/4.5.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "4.5.0", "System.Security.Permissions": "4.5.0"}, "runtime": {"lib/netstandard2.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.Console/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Data.SqlClient/4.6.0": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0", "System.Buffers": "4.5.1", "System.Diagnostics.DiagnosticSource": "4.5.1", "System.Memory": "4.5.5", "System.Security.Principal.Windows": "5.0.0", "System.Text.Encoding.CodePages": "5.0.0", "runtime.native.System.Data.SqlClient.sni": "4.5.0"}, "runtime": {"lib/netstandard2.0/System.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.27110.4"}}}, "System.Diagnostics.Contracts/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/4.5.1": {"runtime": {"lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26919.2"}}}, "System.Diagnostics.EventLog/4.5.0": {"dependencies": {"System.Security.Permissions": "4.5.0"}, "runtime": {"lib/netstandard2.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.Diagnostics.FileVersionInfo/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Reflection.Metadata": "1.4.2", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.Diagnostics.StackTrace/4.3.0": {"dependencies": {"System.IO.FileSystem": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Metadata": "1.4.2", "System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Diagnostics.StackTrace.dll": {"assemblyVersion": "4.0.3.0", "fileVersion": "4.6.24705.1"}}}, "System.Diagnostics.Tools/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.TraceSource/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Drawing.Common/4.5.1": {"runtime": {"lib/netstandard2.0/System.Drawing.Common.dll": {"assemblyVersion": "4.0.0.1", "fileVersion": "4.6.26919.2"}}}, "System.Dynamic.Runtime/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.7.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.4.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Dynamic.Runtime.dll": {"assemblyVersion": "4.0.12.0", "fileVersion": "4.6.24705.1"}}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/5.2.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "5.3.0", "NETStandard.Library": "2.0.3", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard1.4/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "5.2.0.0", "fileVersion": "5.2.0.50129"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "System.Buffers": "4.5.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}, "runtime": {"lib/netstandard1.6/System.Linq.dll": {"assemblyVersion": "4.1.1.0", "fileVersion": "4.6.24705.1"}}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.7.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.4.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.6/System.Linq.Expressions.dll": {"assemblyVersion": "4.1.1.0", "fileVersion": "4.6.24705.1"}}}, "System.Memory/4.5.5": {"dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.4.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Memory.dll": {"assemblyVersion": "4.0.1.2", "fileVersion": "4.6.31308.1"}}}, "System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "4.5.1", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Numerics.Vectors/4.4.0": {"runtime": {"lib/netstandard2.0/System.Numerics.Vectors.dll": {"assemblyVersion": "4.1.3.0", "fileVersion": "4.6.25519.3"}}}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.ObjectModel.dll": {"assemblyVersion": "4.0.13.0", "fileVersion": "4.6.24705.1"}}}, "System.Private.DataContractSerialization/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.7.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.4.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0", "System.Xml.XmlDocument": "4.3.0", "System.Xml.XmlSerializer": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Private.ServiceModel/4.5.3": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "System.Reflection.DispatchProxy": "4.5.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.DispatchProxy/4.5.0": {"runtime": {"lib/netstandard2.0/System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.Reflection.Emit/4.7.0": {"dependencies": {"System.Reflection.Emit.ILGeneration": "4.7.0"}, "runtime": {"lib/netstandard2.0/System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Reflection.Emit.ILGeneration/4.7.0": {"runtime": {"lib/netstandard2.0/System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.7.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Metadata/1.4.2": {"dependencies": {"System.Collections": "4.3.0", "System.Collections.Immutable": "1.3.1", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.1/System.Reflection.Metadata.dll": {"assemblyVersion": "1.4.1.0", "fileVersion": "4.6.24816.1"}}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.4.0": {"runtime": {"lib/netstandard2.0/System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.25519.3"}}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"runtime": {"lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}, "runtime": {"lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Runtime.Serialization.Primitives/4.3.0": {"dependencies": {"System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Runtime.Serialization.Xml/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Private.DataContractSerialization": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Security.AccessControl/5.0.0": {"dependencies": {"System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Security.Claims/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Security.Principal": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Cng/4.4.0": {"runtime": {"lib/netstandard2.0/System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.25519.3"}}}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}, "runtime": {"lib/netstandard1.6/System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.24212.1"}}}, "System.Security.Cryptography.Pkcs/4.5.0": {"dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Security.Cryptography.Cng": "4.4.0"}, "runtime": {"lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "4.0.3.0", "fileVersion": "4.6.26515.6"}}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Security.Cryptography.ProtectedData/4.5.0": {"dependencies": {"System.Memory": "4.5.5"}, "runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "4.0.3.0", "fileVersion": "4.6.26515.6"}}}, "System.Security.Cryptography.X509Certificates/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.4.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Xml/4.5.0": {"dependencies": {"System.Security.Cryptography.Pkcs": "4.5.0", "System.Security.Permissions": "4.5.0"}, "runtime": {"lib/netstandard2.0/System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.Security.Permissions/4.5.0": {"dependencies": {"System.Security.AccessControl": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.Security.Principal/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Security.Principal.Windows/5.0.0": {"runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.ServiceModel.Duplex/4.5.3": {"dependencies": {"System.Private.ServiceModel": "4.5.3", "System.ServiceModel.Primitives": "4.5.3"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.Duplex.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}}}, "System.ServiceModel.Http/4.5.3": {"dependencies": {"System.Private.ServiceModel": "4.5.3", "System.ServiceModel.Primitives": "4.5.3"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.Http.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}}}, "System.ServiceModel.NetTcp/4.5.3": {"dependencies": {"System.Private.ServiceModel": "4.5.3", "System.ServiceModel.Primitives": "4.5.3"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.NetTcp.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}}}, "System.ServiceModel.Primitives/4.5.3": {"dependencies": {"System.Private.ServiceModel": "4.5.3"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}, "lib/netstandard2.0/System.ServiceModel.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}}}, "System.ServiceModel.Security/4.5.3": {"dependencies": {"System.Private.ServiceModel": "4.5.3", "System.ServiceModel.Primitives": "4.5.3"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.Security.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}}}, "System.ServiceProcess.ServiceController/4.5.0": {"dependencies": {"System.Diagnostics.EventLog": "4.5.0"}, "runtime": {"lib/netstandard2.0/System.ServiceProcess.ServiceController.dll": {"assemblyVersion": "4.2.1.0", "fileVersion": "4.6.26515.6"}}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/5.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/4.5.0": {"runtime": {"lib/netstandard2.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "4.0.3.0", "fileVersion": "4.6.26515.6"}}}, "System.Text.RegularExpressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.6/System.Text.RegularExpressions.dll": {"assemblyVersion": "4.1.1.0", "fileVersion": "4.6.24705.1"}}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Threading.dll": {"assemblyVersion": "4.0.12.0", "fileVersion": "4.6.24705.1"}}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.4": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "4.2.0.1", "fileVersion": "4.6.28619.1"}}}, "System.Threading.Tasks.Parallel/4.3.0": {"dependencies": {"System.Collections.Concurrent": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Threading.Thread/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.ValueTuple/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard1.3/System.Xml.ReaderWriter.dll": {"assemblyVersion": "4.1.0.0", "fileVersion": "4.6.24705.1"}}}, "System.Xml.XDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Xml.XDocument.dll": {"assemblyVersion": "4.0.12.0", "fileVersion": "4.6.24705.1"}}}, "System.Xml.XmlDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Xml.XmlSerializer/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.7.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.4.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Xml.XmlSerializer.dll": {"assemblyVersion": "4.0.12.0", "fileVersion": "4.6.24705.1"}}}, "System.Xml.XPath/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "System.Xml.XPath.XDocument/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0", "System.Xml.XPath": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24705.1"}}}, "TimeZoneConverter/5.0.0": {"runtime": {"lib/netstandard2.0/TimeZoneConverter.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Twilio/5.20.1": {"dependencies": {"Microsoft.IdentityModel.Logging": "5.3.0", "Microsoft.IdentityModel.Tokens": "5.3.0", "NETStandard.Library": "2.0.3", "Newtonsoft.Json": "13.0.1", "System.Collections.Specialized": "4.3.0", "System.IdentityModel.Tokens.Jwt": "5.2.0"}, "runtime": {"lib/netstandard1.4/Twilio.dll": {"assemblyVersion": "5.20.1.0", "fileVersion": "5.20.1.0"}}}, "Twilio.AspNet.Common/5.20.1": {"dependencies": {"NETStandard.Library": "2.0.3"}, "runtime": {"lib/netstandard1.0/Twilio.AspNet.Common.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Twilio.AspNet.Core/5.20.1": {"dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.1.3", "NETStandard.Library": "2.0.3", "Twilio": "5.20.1", "Twilio.AspNet.Common": "5.20.1"}, "runtime": {"lib/netstandard1.6/Twilio.AspNet.Core.dll": {"assemblyVersion": "5.20.1.0", "fileVersion": "5.20.1.0"}}}, "UAParser/3.0.0": {"dependencies": {"NETStandard.Library": "2.0.3"}, "runtime": {"lib/netstandard1.3/UAParser.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "ZstdSharp.Port/0.6.2": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "System.Memory": "4.5.5"}, "runtime": {"lib/netstandard2.0/ZstdSharp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "CommonLibCore/1.0.0": {"dependencies": {"Castle.Core": "4.4.1", "CompareNETObjects": "4.57.0", "Dapper": "1.50.5", "DeepCloner": "0.10.2", "EnyimMemcachedCore": "2.1.8", "Experimental.System.Messaging": "1.0.0", "GreenPipes": "4.0.1", "Microsoft.AspNetCore.Http.Abstractions": "2.1.1", "Microsoft.AspNetCore.WebUtilities": "2.1.1", "Microsoft.Extensions.Primitives": "2.1.1", "MongoDB.Driver": "2.21.0", "MongoDB.Driver.GridFS": "2.21.0", "MongolianBarbecue": "1.0.0", "NEST": "7.1.0", "Newtonsoft.Json": "13.0.1", "System.Configuration.ConfigurationManager": "4.5.0", "System.Data.SqlClient": "4.6.0", "System.Private.ServiceModel": "4.5.3", "System.ServiceModel.Duplex": "4.5.3", "System.ServiceModel.Http": "4.5.3", "System.ServiceModel.NetTcp": "4.5.3", "System.ServiceModel.Security": "4.5.3", "System.ServiceProcess.ServiceController": "4.5.0", "TimeZoneConverter": "5.0.0", "UAParser": "3.0.0"}, "runtime": {"CommonLibCore.dll": {"assemblyVersion": "1.0.0", "fileVersion": ""}}}, "EBizAutos.ApplicationCommonLib/1.0.0": {"dependencies": {"AWSSDK.S3": "**********", "Amazon.Extensions.Configuration.SystemsManager": "2.1.1", "CommonLibCore": "1.0.0", "Magick.NET-Q16-AnyCPU": "7.11.0", "Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.Extensions.Configuration": "2.1.1", "Microsoft.Extensions.Configuration.Abstractions": "2.1.1", "Microsoft.Extensions.Configuration.Binder": "2.1.1", "Microsoft.Extensions.Configuration.EnvironmentVariables": "2.1.1", "Microsoft.Extensions.Configuration.FileExtensions": "2.1.1", "Microsoft.Extensions.Configuration.Json": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "System.Drawing.Common": "4.5.1"}, "runtime": {"ApplicationCommonLibCore.dll": {"assemblyVersion": "1.0.0", "fileVersion": ""}}}, "EBizAutos.Apps.CommonLib/1.0.0": {"dependencies": {"CommonLibCore": "1.0.0", "EBizAutos.ApplicationCommonLib": "1.0.0", "FoundationCommonLib": "1.0.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.1.1", "Microsoft.AspNetCore.Routing.Abstractions": "2.1.1", "Microsoft.Extensions.DependencyInjection": "2.1.1", "Newtonsoft.Json": "13.0.1", "Swashbuckle.AspNetCore": "4.0.1", "Swashbuckle.AspNetCore.Filters": "4.5.5", "System.ComponentModel.Annotations": "4.5.0", "Twilio.AspNet.Core": "5.20.1"}, "runtime": {"EBizAutos.Apps.CommonLib.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "FoundationCommonLib/1.0.0": {"dependencies": {"CommonLibCore": "1.0.0", "EBizAutos.ApplicationCommonLib": "1.0.0", "Microsoft.Extensions.Caching.Abstractions": "2.1.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "2.1.1"}, "runtime": {"FoundationCommonLib.dll": {"assemblyVersion": "1.0.0", "fileVersion": ""}}}, "CommonLibCore.Reference/*******": {"runtime": {"CommonLibCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "FoundationCommonLib.Reference/*******": {"runtime": {"FoundationCommonLib.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"EBizAutos.Apps.Authentication.CommonLib/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Amazon.AspNetCore.DataProtection.SSM/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-2OG9McdQ0zheFSp0bVpHRzYik40W6rgvLZS94A/BCXhuxD8m9GaKuVoFQUZc43egJUSQli/J0gesxaBpeUmNmw==", "path": "amazon.aspnetcore.dataprotection.ssm/1.1.0", "hashPath": "amazon.aspnetcore.dataprotection.ssm.1.1.0.nupkg.sha512"}, "Amazon.Extensions.Configuration.SystemsManager/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-ejZvpCO9fifoM+14uy+BKKazW+9My61IpVuYp+b0MfaPJoPkz6LNKTgRXjNiZWO/9mRr3Zi/fEKsSDwEtMnvaA==", "path": "amazon.extensions.configuration.systemsmanager/2.1.1", "hashPath": "amazon.extensions.configuration.systemsmanager.2.1.1.nupkg.sha512"}, "AWSSDK.Core/**********": {"type": "package", "serviceable": true, "sha512": "sha512-akydySw5e74IM9Y95X8XQ21e4b9oCZmz756ZsLW4eSt/ZI988zdoq8NObVr6nbLNlG7DHNM53d6AAAIx7q/0tQ==", "path": "awssdk.core/**********", "hashPath": "awssdk.core.**********.nupkg.sha512"}, "AWSSDK.Extensions.NETCore.Setup/3.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-cm7D75uispA3h/WwMEQVBMmFPPFwRvFQz8fyLwJEB9uqWYfTOMrRTcynMxHnrdoMyQnfCjByRTzHuFgWELmB6Q==", "path": "awssdk.extensions.netcore.setup/3.7.1", "hashPath": "awssdk.extensions.netcore.setup.3.7.1.nupkg.sha512"}, "AWSSDK.S3/**********": {"type": "package", "serviceable": true, "sha512": "sha512-rAath6wuVmv8kaeXJK2vUzjvEpe2qMt7dkL5wWgQ11juqh3wyizWzytlsaBKyAmXs1EKqc9gF5GVW5PbjbWnaA==", "path": "awssdk.s3/**********", "hashPath": "awssdk.s3.**********.nupkg.sha512"}, "AWSSDK.SecurityToken/**********": {"type": "package", "serviceable": true, "sha512": "sha512-dGCVuVo0CFUKWW85W8YENO+aREf8sCBDjvGbnNvxJuNW4Ss+brEU9ltHhq2KfZze2VUNK1/wygbPG1bmbpyXEw==", "path": "awssdk.securitytoken/**********", "hashPath": "awssdk.securitytoken.**********.nupkg.sha512"}, "AWSSDK.SimpleSystemsManagement/3.7.3.24": {"type": "package", "serviceable": true, "sha512": "sha512-fyGmcOFFitWMm+smKJj6MvNR/xU+jJZheP7K4hId33PQjokJVsNPLfRKUyvyPe7iUGdSMPWuU923lc1bbr+7eA==", "path": "awssdk.simplesystemsmanagement/3.7.3.24", "hashPath": "awssdk.simplesystemsmanagement.3.7.3.24.nupkg.sha512"}, "Castle.Core/4.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-zanbjWC0Y05gbx4eGXkzVycOQqVOFVeCjVsDSyuao9P4mtN1w3WxxTo193NGC7j3o2u3AJRswaoC6hEbnGACnQ==", "path": "castle.core/4.4.1", "hashPath": "castle.core.4.4.1.nupkg.sha512"}, "CompareNETObjects/4.57.0": {"type": "package", "serviceable": true, "sha512": "sha512-0uCvlZwCtYAjEvMwRZAqFnXtkGiXZCCgW1Y/fzH6k/yIVlN4pqaRwSfxRSKezFlg6UG1XXoSZtvmAEt7X2AKhg==", "path": "comparenetobjects/4.57.0", "hashPath": "comparenetobjects.4.57.0.nupkg.sha512"}, "Dapper/1.50.5": {"type": "package", "serviceable": true, "sha512": "sha512-1vPpX7WQmQCIb7rwlGOUoVs/yWZhVKvdhuG7WrJV+V+qsP8btnrrCqVWHENAlJxBAnUw5rhWfmuba9/Egei9MA==", "path": "dapper/1.50.5", "hashPath": "dapper.1.50.5.nupkg.sha512"}, "DeepCloner/0.10.2": {"type": "package", "serviceable": true, "sha512": "sha512-QJTEz5Y9m74S0gzarkIdljlbyx3gNOKM/UT9WZR5bS/pYZb6UX59QQWzLnu8KZc5jajQXtr/rHJcAGB39Ne6CA==", "path": "deepcloner/0.10.2", "hashPath": "deepcloner.0.10.2.nupkg.sha512"}, "DnsClient/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-4H/f2uYJOZ+YObZjpY9ABrKZI+JNw3uizp6oMzTXwDw6F+2qIPhpRl/1t68O/6e98+vqNiYGu+lswmwdYUy3gg==", "path": "dnsclient/1.6.1", "hashPath": "dnsclient.1.6.1.nupkg.sha512"}, "Elasticsearch.Net/7.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-MFjokKp8YrdnB1CR+LSveVa6j+pKWrHJtGV/b9A300eOyq+RWacJfIa0Qv/SsDqaUErga63J6RmqMM/ICi4vHA==", "path": "elasticsearch.net/7.1.0", "hashPath": "elasticsearch.net.7.1.0.nupkg.sha512"}, "EnyimMemcachedCore/2.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-BjyIhnS1/NVJ2WIxNaFQMFqsAIFgz9AUTOdvDkdRqJn09zb7QnW5hvWH9OJi/RLGQvI52I+hn6OFTUJRU/EAXw==", "path": "enyimmemcachedcore/2.1.8", "hashPath": "enyimmemcachedcore.2.1.8.nupkg.sha512"}, "Experimental.System.Messaging/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3iV3yEL3cq5UL0onKmdyF8Z180uRF4bQzOcKwS798Tx8L792aNXTLWiYuViKOT2J6tNqVpJh9agfPa2GnIWNsQ==", "path": "experimental.system.messaging/1.0.0", "hashPath": "experimental.system.messaging.1.0.0.nupkg.sha512"}, "GreenPipes/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-nc90y7DAhj8isRbqioVQF3/ExztBZSXRrRoplZvEjckNFC5wP1r+ssfsgl8BptWdQrnMdgkOYhQ6EnHetyFW1Q==", "path": "greenpipes/4.0.1", "hashPath": "greenpipes.4.0.1.nupkg.sha512"}, "Magick.NET-Q16-AnyCPU/7.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-F6usuiffkAblVeAJ2Yq1InH0XW6XhNi0PzYLjQoVEszOy2jRJLzKWleOR+tIs0H4sDqCpbtXD/MZ5sLMMSK2Og==", "path": "magick.net-q16-anycpu/7.11.0", "hashPath": "magick.net-q16-anycpu.7.11.0.nupkg.sha512"}, "Microsoft.AspNetCore.Antiforgery/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-De4NysQJXeWiyzjCH+zE+hVeB7mgCelz00zsBFqkrFtgLWaint5Xt/4qACxRVLUGHQsUo48V6lG0entMJMwv3Q==", "path": "microsoft.aspnetcore.antiforgery/2.1.1", "hashPath": "microsoft.aspnetcore.antiforgery.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-RYM3HHMm/MNwsbUh1xnrhosAGNeZV2Q/FmNQrblgytIK1HIZ6UqNMorFI+kz2MW7gNKHKn6TBLTUXPRmqC6iRQ==", "path": "microsoft.aspnetcore.authentication/2.1.2", "hashPath": "microsoft.aspnetcore.authentication.2.1.2.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Smj5TGeE9629+hGHPk/DZUfCMYGvQwCajAsU/OVExRb8JXfeua4uXZFzT9Kh3pJY2MThPSt1lbDnkL2KaDyw/A==", "path": "microsoft.aspnetcore.authentication.abstractions/2.1.1", "hashPath": "microsoft.aspnetcore.authentication.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Cookies/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-XwE4/p9QHJOkoSWYdgx3u3Jhx6+NQZuRWGJT7jsdlpfDJeS3gJWEqIM9pBmrdt803sX2WZDpgm8hxGIAtiJcQQ==", "path": "microsoft.aspnetcore.authentication.cookies/2.1.2", "hashPath": "microsoft.aspnetcore.authentication.cookies.2.1.2.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Core/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Zo6SLzqxrW0PFg1AB0xSb+Rta4hCuX8hgOY425ldhFq4kKcmw45oJQ2zOIeeW/6EuBtEy+hwDB96baxTmXtfeA==", "path": "microsoft.aspnetcore.authentication.core/2.1.1", "hashPath": "microsoft.aspnetcore.authentication.core.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-hedJ6YxyZRIJPXC2olml9YM5t3+yIFt9hcyBXp/q5+z4XpJMY5gUwbc1F5QRGEdcvwSCpFmmRLVaPPh4Kiaq0g==", "path": "microsoft.aspnetcore.authentication.jwtbearer/2.1.2", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.2.1.2.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-EiMovaF/QszHjp9SBJH2gUPecAmBDUZCm7sNsCSAf32dhfJMrsbXtiFz91LPXkQOz483u9P5fz8q24INJP/WTQ==", "path": "microsoft.aspnetcore.authorization/2.1.2", "hashPath": "microsoft.aspnetcore.authorization.2.1.2.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization.Policy/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-6Gy9rFN1/4pKgjcbb2yaOmwpjV282dGnl7ewcCvcLxQmywpolkwxe5PPI6K/VPC2sovL5BtzhxnRl3OkwJZxwg==", "path": "microsoft.aspnetcore.authorization.policy/2.1.1", "hashPath": "microsoft.aspnetcore.authorization.policy.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Cors/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-5b3xfO8ycP9fEm76HGdExptlxURKNbmGnlA2mN+FQMaWPEuFH1te6GReBcKCQp4oeSSWuLfV9xSo+8LpU24u1A==", "path": "microsoft.aspnetcore.cors/2.1.1", "hashPath": "microsoft.aspnetcore.cors.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.Internal/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-guY3jMNkcUi2hrMJ4/vPnUUFwudxTVSJ809gCfpq+xR0UgV6P9ZHZLOI5q/07QHDZY+kKPXxipXGyJXQpq2k0g==", "path": "microsoft.aspnetcore.cryptography.internal/2.1.1", "hashPath": "microsoft.aspnetcore.cryptography.internal.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-OPZDPAAL3OwOCrz870F9goq//NJOmPl4Lv3dz+v0cRQe8EpsbCe0c6IRI8vdlFwM13Qy57D5rLQlysb+tLpENA==", "path": "microsoft.aspnetcore.dataprotection/2.1.1", "hashPath": "microsoft.aspnetcore.dataprotection.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-dcH52SMIIUOwBeDZ2QQEY3hWXJz50Dk2YzC/B2hxDLB78Il75BHGOhClIw6/0H+dKZCwItUytxoMNYtCSmG+aQ==", "path": "microsoft.aspnetcore.dataprotection.abstractions/2.1.1", "hashPath": "microsoft.aspnetcore.dataprotection.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection.Extensions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-ceD0XYTCxNACik38XmHEIPgjqMdL66jDOu68pjLm9R+VPT2PWAWww3ihTmGOfLPnQuCnf9gCcQxR33rwRcdR9Q==", "path": "microsoft.aspnetcore.dataprotection.extensions/2.1.1", "hashPath": "microsoft.aspnetcore.dataprotection.extensions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Diagnostics.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-W4V3uJY3mIUZbmon6MKOVr16r/NPgn/ey06L+BKf6uzXPua1Tzwlkz5h101b/Ncaown0iEJz5Pm6heYj+Fr/WQ==", "path": "microsoft.aspnetcore.diagnostics.abstractions/2.1.1", "hashPath": "microsoft.aspnetcore.diagnostics.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-76cKcp2pWhvdV2TXTqMg/DyW7N6cDzTEhtL8vVWFShQN+Ylwv3eO/vUQr2BS3Hz4IZHEpL+FOo2T+MtymHDqDQ==", "path": "microsoft.aspnetcore.hosting.abstractions/2.1.1", "hashPath": "microsoft.aspnetcore.hosting.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-+vD7HJYzAXNq17t+NgRkpS38cxuAyOBu8ixruOiA3nWsybozolUdALWiZ5QFtGRzajSLPFA2YsbO3NPcqoUwcw==", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.1.1", "hashPath": "microsoft.aspnetcore.hosting.server.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Html.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-CS/2N0d0JUdhYOrnd9Ll6O2Lb++CQaToKem6NyF+9RIgdL3tEZJOJHXcFWSXUSDqML98XQzbtnV+dCT22cBrRw==", "path": "microsoft.aspnetcore.html.abstractions/2.1.1", "hashPath": "microsoft.aspnetcore.html.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Http/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-pPDcCW8spnyibK3krpxrOpaFHf5fjV6k1Hsl6gfh77N/8gRYlLU7MOQDUnjpEwdlHmtxwJKQJNxZqVQOmJGRUw==", "path": "microsoft.aspnetcore.http/2.1.1", "hashPath": "microsoft.aspnetcore.http.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-kQUEVOU4loc8CPSb2WoHFTESqwIa8Ik7ysCBfTwzHAd0moWovc9JQLmhDIHlYLjHbyexqZAlkq/FPRUZqokebw==", "path": "microsoft.aspnetcore.http.abstractions/2.1.1", "hashPath": "microsoft.aspnetcore.http.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Extensions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-ncAgV+cqsWSqjLXFUTyObGh4Tr7ShYYs3uW8Q/YpRwZn7eLV7dux5Z6GLY+rsdzmIHiia3Q2NWbLULQi7aziHw==", "path": "microsoft.aspnetcore.http.extensions/2.1.1", "hashPath": "microsoft.aspnetcore.http.extensions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-VklZ7hWgSvHBcDtwYYkdMdI/adlf7ebxTZ9kdzAhX+gUs5jSHE9mZlTamdgf9miSsxc1QjNazHXTDJdVPZKKTw==", "path": "microsoft.aspnetcore.http.features/2.1.1", "hashPath": "microsoft.aspnetcore.http.features.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.JsonPatch/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-VjTsHQQG5H8Gjw6oi3jLUc6Wnc9Gnj1alQIwVsbfxuoXS5j0rTpzIKcRNyppEf0eQfI5fV/IDPJxgxV0NK5Xgw==", "path": "microsoft.aspnetcore.jsonpatch/2.1.1", "hashPath": "microsoft.aspnetcore.jsonpatch.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Localization/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-vq/zYk4PxqLdhQq269RgmT9Tp44cEMYFm4aFU6B61TMzUyHIjiIYTvNcuAI+5VVBU6n6GfExxeF11J3U4Pzupw==", "path": "microsoft.aspnetcore.localization/2.1.1", "hashPath": "microsoft.aspnetcore.localization.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-3FMnUZwgIr6VmvkLvozRuSEYuPgUHC1l81PLN4SmR/UDdJYKFDwIknGVYHZBAHDUB3WB/joCYHjuUENM6PvuYg==", "path": "microsoft.aspnetcore.mvc/2.1.3", "hashPath": "microsoft.aspnetcore.mvc.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Abstractions/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-G0oQiJ9okq+QbH9HBbmxu8/+Vhv063Dt06RzJPzsw7/uFT7Tvq5XHU5LI3b9qudyotJIRfYBbJRNeZyXEc+ALw==", "path": "microsoft.aspnetcore.mvc.abstractions/2.1.3", "hashPath": "microsoft.aspnetcore.mvc.abstractions.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.ApiExplorer/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-KQVzqCWhizoCkXDXe6DU1NkSfN7/X9v8juVRV/yRh2HbsLZlEwiMPLI8qA6x/OIjP3U6dwHMemsMQCbNH3YbCw==", "path": "microsoft.aspnetcore.mvc.apiexplorer/2.1.3", "hashPath": "microsoft.aspnetcore.mvc.apiexplorer.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Core/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-qu2EOWIqz/KFw2WV0IDltHLoKjfWr60mWl9waPJwuwpjwycaDimu8fjOEigY941tMZoWjv/ZUi2kQGKHov10/g==", "path": "microsoft.aspnetcore.mvc.core/2.1.3", "hashPath": "microsoft.aspnetcore.mvc.core.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Cors/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-apH1jf7/D88YJbVRGnzrslOgEMKBRDnDtfSudQiMH/w13uu7FwfdJSAvFFwHQvODgGPVnmeZetsUXcvd4ySATQ==", "path": "microsoft.aspnetcore.mvc.cors/2.1.3", "hashPath": "microsoft.aspnetcore.mvc.cors.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.DataAnnotations/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-ume8mo6v/hCk2OmkYP45Au5rg+FUYCpSWSbDQGHlAo4NLspHa6MB+D4INiiEzvTXC4d738E4DzkdaKc7+PYcAQ==", "path": "microsoft.aspnetcore.mvc.dataannotations/2.1.3", "hashPath": "microsoft.aspnetcore.mvc.dataannotations.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Formatters.Json/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-a3tyBmMy1onYZbDHrbJ7nuE4xEQUSdD76T2KlE68s7xtANhIdbC/mW1FGTEZKzXawBygOaVVS7A1OzIiduxjUw==", "path": "microsoft.aspnetcore.mvc.formatters.json/2.1.3", "hashPath": "microsoft.aspnetcore.mvc.formatters.json.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Localization/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-sTG7gccAlN5xcwYgR+DLwoGL2Nsf9YTo7JpNHnLz44BdcoIt2WrsQnBy6Xg0+X0iK6sCn2JcB6i8sHMA/QAxgA==", "path": "microsoft.aspnetcore.mvc.localization/2.1.3", "hashPath": "microsoft.aspnetcore.mvc.localization.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Razor/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-A6H7GV9NdMswPXui/MKi337kk3uKqXyLCQ6qjjL3XXmS4kn5G7AOhkOJ5YswbtW1ssP/rBC1E5iK2QX3bFCHew==", "path": "microsoft.aspnetcore.mvc.razor/2.1.3", "hashPath": "microsoft.aspnetcore.mvc.razor.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Razor.Extensions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-dX6QcZLUbIQj2BC+lkmlAvHPrDzrknmO1YW1AUNh2GKk9iEAhlVraxzsQo10IvYdXOhJGhiqa6gVyq9fledK1g==", "path": "microsoft.aspnetcore.mvc.razor.extensions/2.1.1", "hashPath": "microsoft.aspnetcore.mvc.razor.extensions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.RazorPages/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-9XAjyPY2n1V9jRDm0XqvsgURtOI99N+Wvhu1C719lOM0dmst6tMYmed2MJCdZ7EzzLxGoK112It33/zZheXWig==", "path": "microsoft.aspnetcore.mvc.razorpages/2.1.3", "hashPath": "microsoft.aspnetcore.mvc.razorpages.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.TagHelpers/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-YQ3L/NDtmYXO36hcsvFmG0bBDpO1U2mPtQb9h8ueOKVHrwYJQX1oBKTKyNoW1CxGkunmXoi2IeVwXeTl7HrVkA==", "path": "microsoft.aspnetcore.mvc.taghelpers/2.1.3", "hashPath": "microsoft.aspnetcore.mvc.taghelpers.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.ViewFeatures/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-UwFP+BjvqzF5V9NVga0kLb4oS5LceNkYPFtMvd9imezn0+/vHKSxiIp0cWvHuksNGMld/9JjSH2KQMt0i3zkzA==", "path": "microsoft.aspnetcore.mvc.viewfeatures/2.1.3", "hashPath": "microsoft.aspnetcore.mvc.viewfeatures.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Razor/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-2yYunEgYC7hOyasvMiiH+a8250l+l1R79jB6VarZ6I8fiXDNCrJ/mEEn9TS0vDidAzesOshFigepa6+qI5Cb0w==", "path": "microsoft.aspnetcore.razor/2.1.1", "hashPath": "microsoft.aspnetcore.razor.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Design/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-1XHObHLx6A0/57ZmLG9gfKMO/Z/gQjRXPFWQDMlPZGYwcfgufvSdmI2+RYvR5DGkbba9HIHC35ClNQ2yVNIohw==", "path": "microsoft.aspnetcore.razor.design/2.1.1", "hashPath": "microsoft.aspnetcore.razor.design.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Language/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-NbDH62ez/AZzSAGZuy6dIMBDMV0HmBlbWJqPw/ZX+Ooz8x1oZq6i/LbPbt34CQlAkrm7lnAlWZq+cE7dzkvGiQ==", "path": "microsoft.aspnetcore.razor.language/2.1.1", "hashPath": "microsoft.aspnetcore.razor.language.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Runtime/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-m+lFv8BGZiR/1mtuBCwCtwvoQlx0QpjUbH6ixqqm7v8+uhXo6RKGV4CHBDozuJhhI4qb9dxNyyWhVm3S0bY8Zw==", "path": "microsoft.aspnetcore.razor.runtime/2.1.1", "hashPath": "microsoft.aspnetcore.razor.runtime.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-sTJvhc408h4J8ml66gfhuN/r2WfrasvgERq2ZLIDz3YZYqSXmkpwDjbxSlhzuHQFKMlyx1Tg1uWoF+6eRrKjDA==", "path": "microsoft.aspnetcore.responsecaching.abstractions/2.1.1", "hashPath": "microsoft.aspnetcore.responsecaching.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Routing/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-U39z3M0oTrquVBohK32Nh20PWQkb9fuO1dbVPTI43Dr3n6qCx6vAFNGWuCzFeINLy152LivmVlLn4rMOzWudug==", "path": "microsoft.aspnetcore.routing/2.1.1", "hashPath": "microsoft.aspnetcore.routing.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Routing.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Aa88Bi0/HI8dPReC0XqByPiVGYDRfj6Xh2eVsNCisnlgFHonDdW9CQsNPhVSK+uWQl3kDMFxFpeJ1ktz/wUHsQ==", "path": "microsoft.aspnetcore.routing.abstractions/2.1.1", "hashPath": "microsoft.aspnetcore.routing.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.StaticFiles/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-HBMJQ1WPdHO0thltwi7agvfis3c1Kd5HFQDgyy0nV3X4mJvhVGUnymT0+QQU+GGNK9FG/uEQNE3YDSrrBamP7w==", "path": "microsoft.aspnetcore.staticfiles/2.0.0", "hashPath": "microsoft.aspnetcore.staticfiles.2.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-PGKIZt4+412Z/XPoSjvYu/QIbTxcAQuEFNoA1Pw8a9mgmO0ZhNBmfaNyhgXFf7Rq62kP0tT/2WXpxdcQhkFUPA==", "path": "microsoft.aspnetcore.webutilities/2.1.1", "hashPath": "microsoft.aspnetcore.webutilities.2.1.1.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-W8<PERSON>QjkMScOMTtJbPwmPyj9c3zYSFGawDW3jwlBOOsnY+EzZFLgNQ/UMkK35JmkNOVPdCyPr2Tw7Vv9N+KA3ZQ==", "path": "microsoft.bcl.asyncinterfaces/5.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.5.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-HS3iRWZKcUw/8eZ/08GXKY2Bn7xNzQPzf8gRPHGSowX7u7XXu9i9YEaBeBNKUXWfI7qjvT2zXtLUvbN0hds8vg==", "path": "microsoft.codeanalysis.analyzers/1.1.0", "hashPath": "microsoft.codeanalysis.analyzers.1.1.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/2.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-06AzG7oOLKTCN1EnoVYL1bQz+Zwa10LMpUn7Kc+PdpN8CQXRqXTyhfxuKIz6t0qWfoatBNXdHD0OLcEYp5pOvQ==", "path": "microsoft.codeanalysis.common/2.8.0", "hashPath": "microsoft.codeanalysis.common.2.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/2.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-RizcFXuHgGmeuZhxxE1qQdhFA9lGOHlk0MJlCUt6LOnYsevo72gNikPcbANFHY02YK8L/buNrihchY0TroGvXQ==", "path": "microsoft.codeanalysis.csharp/2.8.0", "hashPath": "microsoft.codeanalysis.csharp.2.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Razor/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-hc29VUVlF2t2TfOR3c5X2mun3h5KkswkarpWBffEG4iHoSdoEueo82dplwoXg9lH2vw0mK7VYPyawcKy6YHv3A==", "path": "microsoft.codeanalysis.razor/2.1.1", "hashPath": "microsoft.codeanalysis.razor.2.1.1.nupkg.sha512"}, "Microsoft.CSharp/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-kaj6Wb4qoMuH3HySFJhxwQfe8R/sJsNJnANrvv8WdFPMoNbKY5htfNscv+LHCu5ipz+49m2e+WQXpLXr9XYemQ==", "path": "microsoft.csharp/4.5.0", "hashPath": "microsoft.csharp.4.5.0.nupkg.sha512"}, "Microsoft.DotNet.PlatformAbstractions/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-9KPDwvb/hLEVXYruVHVZ8BkebC8j17DmPb56LnqRF74HqSPLjCkrlFUjOtFpQPA2DeADBRTI/e69aCfRBfrhxw==", "path": "microsoft.dotnet.platformabstractions/2.1.0", "hashPath": "microsoft.dotnet.platformabstractions.2.1.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-LbT7Ry1waNBksnngFNdaNmEglQMJ8g7F6tbSoyoqpEW35W/Cj4YwURDVwoRS+jtyf6YKsTdPHV643jMMuJBi9g==", "path": "microsoft.extensions.caching.abstractions/2.1.1", "hashPath": "microsoft.extensions.caching.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-jR14GhHGmPzq7QChnYa3Uiu+s/QerwxbMPAlA0Ei0shDJlrRoD6FSb9hP8rmSX6oai9Z64SWbXlwBhi3L/vj9g==", "path": "microsoft.extensions.caching.memory/2.1.1", "hashPath": "microsoft.extensions.caching.memory.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-LjVKO6P2y52c5ZhTLX/w8zc5H4Y3J/LJsgqTBj49TtFq/hAtVNue/WA0F6/7GMY90xhD7K0MDZ4qpOeWXbLvzg==", "path": "microsoft.extensions.configuration/2.1.1", "hashPath": "microsoft.extensions.configuration.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-VfuZJNa0WUshZ/+8BFZAhwFKiKuu/qOUCFntfdLpHj7vcRnsGHqd3G2Hse78DM+pgozczGM63lGPRLmy+uhUOA==", "path": "microsoft.extensions.configuration.abstractions/2.1.1", "hashPath": "microsoft.extensions.configuration.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-fcLCTS03poWE4v9tSNBr3pWn0QwGgAn1vzqHXlXgvqZeOc7LvQNzaWcKRQZTdEc3+YhQKwMsOtm3VKSA2aWQ8w==", "path": "microsoft.extensions.configuration.binder/2.1.1", "hashPath": "microsoft.extensions.configuration.binder.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-6xMxFIfKL+7J/jwlk8zV8I61sF3+DRG19iKQxnSfYQU+iMMjGbcWNCHFF/3MHf3o4sTZPZ8D6Io+GwKFc3TIZA==", "path": "microsoft.extensions.configuration.environmentvariables/2.1.1", "hashPath": "microsoft.extensions.configuration.environmentvariables.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-CDk5CwG0YzlRgvl65J0iK6ahrX12yMRrEat3yVTXjWC+GN9Jg9zHZu2IE4cQIPAMA/IiAI5KjgL08fhP3fPCkw==", "path": "microsoft.extensions.configuration.fileextensions/2.1.1", "hashPath": "microsoft.extensions.configuration.fileextensions.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-IFpONpvdhVEE3S3F4fTYkpT/GyIHtumy2m0HniQanJ80Pj/pUF3Z4wjrHEp1G78rPD+WTo5fRlhdJfuU1Tv2GQ==", "path": "microsoft.extensions.configuration.json/2.1.1", "hashPath": "microsoft.extensions.configuration.json.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-RVdgNWT/73M0eCpreGpWv5NmbHFGQzzW+G7nChK8ej84m+d1nzeWrtqcRYnEpKNx3B8V/Uek4tNP0WCaCNjYnQ==", "path": "microsoft.extensions.dependencyinjection/2.1.1", "hashPath": "microsoft.extensions.dependencyinjection.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-MgYpU5cwZohUMKKg3sbPhvGG+eAZ/59E9UwPwlrUkyXU+PGzqwZg9yyQNjhxuAWmoNoFReoemeCku50prYSGzA==", "path": "microsoft.extensions.dependencyinjection.abstractions/2.1.1", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-nS2XKqi+1A1umnYNLX2Fbm/XnzCxs5i+zXVJ3VC6r9t2z0NZr9FLnJN4VQpKigdcWH/iFTbMuX6M6WQJcTjVIg==", "path": "microsoft.extensions.dependencymodel/2.1.0", "hashPath": "microsoft.extensions.dependencymodel.2.1.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-UEQB5/QPuLYaCvScZQ9llhcks5xyEUKh41D615FoehRAF9UgGVmXHcCSOH8idHHLRoKm+OJJjEy1oywvuaL33w==", "path": "microsoft.extensions.fileproviders.abstractions/2.1.1", "hashPath": "microsoft.extensions.fileproviders.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Composite/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-fduNXRROUeV1bvFr7xkeRkTU/gVfqu5hmfqxiJiciOjwH3Q+UOADiXAWoPfnQiwpZEmsCC6z+hIIyBOnO4i5Yw==", "path": "microsoft.extensions.fileproviders.composite/2.1.1", "hashPath": "microsoft.extensions.fileproviders.composite.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Embedded/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-A1pniIZjS/8z8HQWIzm/datI6J0X4R9wngmVLGbfZ1LIj78oOR+sdqNHo5yvAwJz38TR9fG2E3b410wuoGxBKw==", "path": "microsoft.extensions.fileproviders.embedded/2.0.0", "hashPath": "microsoft.extensions.fileproviders.embedded.2.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-kVCvLm1ePchUgRrQZrno07Mn6knDAzR7vl6eRaI/fem0u6ODg+RTwOYLs4XL39Ttuu+BzEwqzHu3DtDgXT8+vQ==", "path": "microsoft.extensions.fileproviders.physical/2.1.1", "hashPath": "microsoft.extensions.fileproviders.physical.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-4QDzyCN8cJnThY6mK9SnzovyCZ8KCG9jmC9KqHfFGtazJvmNZP1gcyBkPmqMjP0qwbmEUUyqyA9LLn3FrYXTGw==", "path": "microsoft.extensions.filesystemglobbing/2.1.1", "hashPath": "microsoft.extensions.filesystemglobbing.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-kVVdHnOFJbcXxgZzrT6nwkrWZTHL+47LT59S9J2Jp0BNO3EQWNEZHUUZMb/kKFV7LtW+bp+EuAOPNUqEcqI++Q==", "path": "microsoft.extensions.hosting.abstractions/2.1.1", "hashPath": "microsoft.extensions.hosting.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Localization/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-6v66lA0RqutBDseLtX6MAZHUcaTBk2xfhnfHpcBeLtlx7jySHg/CNociGLPW7oHJtrJ+POZ8xDEoAyQp5RbWXw==", "path": "microsoft.extensions.localization/2.1.1", "hashPath": "microsoft.extensions.localization.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Localization.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-bsDw+b5BaiFej/Nei6IiJFhsOtiXdDmJCabkU45WC3DQafHOLUWuArpVar8Vv2VxHrXGkOWRA7gX31LASqcaMA==", "path": "microsoft.extensions.localization.abstractions/2.1.1", "hashPath": "microsoft.extensions.localization.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-XRzK7ZF+O6FzdfWrlFTi1Rgj2080ZDsd46vzOjadHUB0Cz5kOvDG8vI7caa5YFrsHQpcfn0DxtjS4E46N4FZsA==", "path": "microsoft.extensions.logging.abstractions/2.1.1", "hashPath": "microsoft.extensions.logging.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-SErON45qh4ogDp6lr6UvVmFYW0FERihW+IQ+2JyFv1PUyWktcJytFaWH5zarufJvZwhci7Rf1IyGXr9pVEadTw==", "path": "microsoft.extensions.objectpool/2.1.1", "hashPath": "microsoft.extensions.objectpool.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Options/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-V7lXCU78lAbzaulCGFKojcCyG8RTJicEbiBkPJjFqiqXwndEBBIehdXRMWEVU3UtzQ1yDvphiWUL9th6/4gJ7w==", "path": "microsoft.extensions.options/2.1.1", "hashPath": "microsoft.extensions.options.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-NpGh3Y/VOBs6hvjKHMsdbtrvGvMO+cBqZ7YT/Rc4iFy0C4ogSnl1lBAq69L1LS6gzlwDBZDZ7WcvzSDzk5zfzA==", "path": "microsoft.extensions.options.configurationextensions/2.1.1", "hashPath": "microsoft.extensions.options.configurationextensions.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Primitives/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-scJ1GZNIxMmjpENh0UZ8XCQ6vzr/LzeF9WvEA51Ix2OQGAs9WPgPu8ABVUdvpKPLuor/t05gm6menJK3PwqOXg==", "path": "microsoft.extensions.primitives/2.1.1", "hashPath": "microsoft.extensions.primitives.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.WebEncoders/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-XIuJXPNUAX/ZV/onarixNoq3kO7Q9/RXXOY8hhYydsDwHI9PqPeJH6WE3LmPJJDmB+7y3+MT6ZmW78gZZDApBA==", "path": "microsoft.extensions.webencoders/2.1.1", "hashPath": "microsoft.extensions.webencoders.2.1.1.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/5.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-o+bBauEMOi6ZI0MlJEC69Sw9UPwKLFmN+lD942g9UCx5pfiLFvJBKp8OPmxtGFL02ZxzXCIUyhyKn85izBDsnQ==", "path": "microsoft.identitymodel.logging/5.3.0", "hashPath": "microsoft.identitymodel.logging.5.3.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/5.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-pakGqbE3FRort3vb0qqWI0Qfy84IOXs8sG7ygANUpoRT+544svQ62JfvCX4UPnqf5bCUpSxVc3rDh8yCQBtc7w==", "path": "microsoft.identitymodel.protocols/5.2.0", "hashPath": "microsoft.identitymodel.protocols.5.2.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/5.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-hMjsfdvgI/Gk/HWPgyVnju6fy3iULralgn1XU6eL17KkkFN2rJ1fDzJX3RKrjr888Y5S+hTSQAUcGzb4Fe3aBA==", "path": "microsoft.identitymodel.protocols.openidconnect/5.2.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.5.2.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/5.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/piauST4FL0qzVI6oqLWxqhFReg12KwVGy0jRlnVOpGMeOVSKdtNVtHsN/hARc25hOOPEp9WKMce5ILzyMx/tQ==", "path": "microsoft.identitymodel.tokens/5.3.0", "hashPath": "microsoft.identitymodel.tokens.5.3.0.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-lP<PERSON>phl8b2EuhOE9dMH6EZDmu7pS882O+HMi5BJNsigxHaWlBrYxZHFZgE18cyaPp6SSZcTkKkuzfjV/RRQKlA==", "path": "microsoft.net.http.headers/2.1.1", "hashPath": "microsoft.net.http.headers.2.1.1.nupkg.sha512"}, "Microsoft.NETCore.Platforms/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-ok+RPAtESz/9MUXeIEz6Lv5XAGQsaNmEYXMsgVALj4D7kqC8gveKWXWXbufLySR2fWrwZf8smyN5RmHu0e4BHA==", "path": "microsoft.netcore.platforms/2.1.0", "hashPath": "microsoft.netcore.platforms.2.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "MongoDB.Bson/2.21.0": {"type": "package", "serviceable": true, "sha512": "sha512-QT+D1I3Jz6r6S6kCgJD1L9dRCLVJCKlkGRkA+tJ7uLpHRmjDNcNKy4D1T+L9gQrjl95lDN9PHdwEytdvCW/jzA==", "path": "mongodb.bson/2.21.0", "hashPath": "mongodb.bson.2.21.0.nupkg.sha512"}, "MongoDB.Driver/2.21.0": {"type": "package", "serviceable": true, "sha512": "sha512-VxKj1wuhadiXhaXkykCWRgsYOysdaOYJ202hJFz25UjkrqC/tHA8RS4hdS5HYfGWoI//fypBXnxZCkEjXLXdfw==", "path": "mongodb.driver/2.21.0", "hashPath": "mongodb.driver.2.21.0.nupkg.sha512"}, "MongoDB.Driver.Core/2.21.0": {"type": "package", "serviceable": true, "sha512": "sha512-Ac44U3bQfinmdH5KNFjTidJe9LKW87SxkXJ3YuIUJQMITEc4083YF1yvjJxaSeYF9er0YgHSmwhHpsZv0Fwplg==", "path": "mongodb.driver.core/2.21.0", "hashPath": "mongodb.driver.core.2.21.0.nupkg.sha512"}, "MongoDB.Driver.GridFS/2.21.0": {"type": "package", "serviceable": true, "sha512": "sha512-8a6n05xXWGh/CsY/egbZi8NF5S14c7IO05/eqi6cEsInlu4Dd1ZofFd3e4v0vRYOjtjUXImQ4xFTgOFvFTIAgg==", "path": "mongodb.driver.gridfs/2.21.0", "hashPath": "mongodb.driver.gridfs.2.21.0.nupkg.sha512"}, "MongoDB.Libmongocrypt/1.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-fgNw8Dxpkq7mpoaAYes8cfnPRzvFIoB8oL9GPXwi3op/rONftl0WAeg4akRLcxfoVuUvuUO2wGoVBr3JzJ7Svw==", "path": "mongodb.libmongocrypt/1.8.0", "hashPath": "mongodb.libmongocrypt.1.8.0.nupkg.sha512"}, "MongolianBarbecue/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Qeg8JGFCOGh8CmBMVJiE2ABujgFXtX1lDQtXJ+3cUfp2Ut55EO0Da9sMxKUBSx4Lzpivzy27TONGUMbgoAAiEQ==", "path": "mongolianbarbecue/1.0.0", "hashPath": "mongolianbarbecue.1.0.0.nupkg.sha512"}, "NEST/7.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-/Ij6XS5SnTeeZudTHypO8IlopiMZmyeljim7fz02UxwQPZ7duSmuw/bZu+ixKgXSWXGDH0thkdawv/81RZZKdA==", "path": "nest/7.1.0", "hashPath": "nest.7.1.0.nupkg.sha512"}, "NETStandard.Library/2.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-st47PosZSHrjECdjeIzZQbzivYBJFv6P2nv4cj2ypdI204DO+vZ7l5raGMiX4eXMJ53RfOIg+/s4DHVZ54Nu2A==", "path": "netstandard.library/2.0.3", "hashPath": "netstandard.library.2.0.3.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "Newtonsoft.Json.Bson/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-5PYT/IqQ+UK31AmZiSS102R6EsTo+LGTSI8bp7WAUqDKaF4wHXD8U9u4WxTI1vc64tYi++8p3dk3WWNqPFgldw==", "path": "newtonsoft.json.bson/1.0.1", "hashPath": "newtonsoft.json.bson.1.0.1.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-HdSSp5MnJSsg08KMfZThpuLPJpPwE5hBXvHwoKWosyHHfe8Mh5WKT0ylEOf6yNzX6Ngjxe4Whkafh5q7Ymac4Q==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+yH1a49wJMy8Zt4yx5RhJrxO/DBDByAiCzNwiETI+1S4mPdCu0OY4djdciC7Vssk0l22wQaDLrXxXkp+3+7bVA==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c3YNH1GQJbfIPJeCnr4avseugSqPrxwIqzthYyZDN6EuOyNOzq+y2KSUfRcXauya1sF4foESTgwM5e1A8arAKw==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.Data.SqlClient.sni/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-AJfX7owAAkMjWQYhoml5IBfXh8UyYPjktn8pK0BFGAdKgBS7HqMz1fw5vdzfZUWfhtTPDGCjgNttt46ZyEmSjg==", "path": "runtime.native.system.data.sqlclient.sni/4.5.0", "hashPath": "runtime.native.system.data.sqlclient.sni.4.5.0.nupkg.sha512"}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "path": "runtime.native.system.io.compression/4.3.0", "hashPath": "runtime.native.system.io.compression.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-NS1U+700m4KFRHR5o4vo9DSlTmlCKu/u7dtE5sUHVIPB+xpXxYQvgBgA6wEIeCz6Yfn0Z52/72WYsToCEPJnrw==", "path": "runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-b3pthNgxxFcD+Pc0WSEoC0+md3MyhRS6aCEeenvNE3Fdw1HyJ18ZhRFVJJzIeR/O/jpxPboB805Ho0T3Ul7w8A==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KeLz4HClKf+nFS7p/6Fi/CqyLXh81FpiGzcmuS8DGi9lUqSnZ6Es23/gv2O+1XVGfrbNmviF7CckBpavkBoIFQ==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X7IdhILzr4ROXd8mI1BUCQMSHSQwelUlBjF1JyTKCjXaOGn2fB4EKBxQbCK2VjO3WaWIdlXZL3W6TiIVnrhX4g==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-nyFNiCk/r+VOiIqreLix8yN+q3Wga9+SE8BCgkf+2BwEKiNx6DyvFjCgkfV743/grxv8jHJ8gUK4XEQw7yzRYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ytoewC6wGorL7KoCAvRfsgoJPJbNq+64k2SqW6JcOAebWsFUvCCYgfzQMrnpvPiEl4OrblUlhF2ji+Q1+SVLrQ==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I8bKw2I8k58Wx7fMKQJn2R8lamboCAiHfHeV/pS65ScKWMMI0+wJkLYlEKvgW1D/XvSl/221clBoR2q9QNNM7A==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VB5cn/7OzUfzdnC8tqAIMQciVLiq2epm2NrAm1E9OjNRyG4lVhfR61SMcLizejzQP8R8Uf/0l5qOIbUEi+RdEg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "Scrutor/3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-biheXROWXbciLzPOg/PttVH4w4Q8ADx89bQP8eKiGf1IJj0EOLYRjoctsMGQzi4mB+e4ICMqFeA8Spr0NKN4ZA==", "path": "scrutor/3.0.1", "hashPath": "scrutor.3.0.1.nupkg.sha512"}, "SharpCompress/0.30.1": {"type": "package", "serviceable": true, "sha512": "sha512-XqD4TpfyYGa7QTPzaGlMVbcecKnXy4YmYLDWrU+JIj7IuRNl7DH2END+Ll7ekWIY8o3dAMWLFDE1xdhfIWD1nw==", "path": "sharpcompress/0.30.1", "hashPath": "sharpcompress.0.30.1.nupkg.sha512"}, "Snappier/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rFtK2KEI9hIe8gtx3a0YDXdHOpedIf9wYCEYtBEmtlyiWVX3XlCNV03JrmmAi/Cdfn7dxK+k0sjjcLv4fpHnqA==", "path": "snappier/1.0.0", "hashPath": "snappier.1.0.0.nupkg.sha512"}, "Swashbuckle.AspNetCore/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Zq6lkFieuFPNDwXwQ+e8i5zy2VMrexcRFU8mQORxqIc8r7Y+qKX63vg57yL1HeGCINHQGGzxGfw2rP63IeEqhg==", "path": "swashbuckle.aspnetcore/4.0.1", "hashPath": "swashbuckle.aspnetcore.4.0.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.Annotations/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Eke+OE1T+ogLsSmVFdhFPaVlsCBvGfkO/qpOgmD8pBns5vUMaI3pHjKq4sg9FfK8lLK/cxFbLx8Q+/iGogJ+Xw==", "path": "swashbuckle.aspnetcore.annotations/4.0.1", "hashPath": "swashbuckle.aspnetcore.annotations.4.0.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.Filters/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-/nLPAqxdWu9KbpXSNJn3uchv/Ct8gNrNF3zaAiTGG2KdWC8DodgX+1kIKoHO/yGoHM5cHKS/z2233wFL3dGMvg==", "path": "swashbuckle.aspnetcore.filters/4.5.5", "hashPath": "swashbuckle.aspnetcore.filters.4.5.5.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-rqzS3vHrjz+tR5j0nZOKZyaMTDfLGbVYkwMq205aYuGbsiGwbOlNU0Q8lq4Q0ptQPMKVkUf8XouCIdJ3qpK17w==", "path": "swashbuckle.aspnetcore.swagger/4.0.1", "hashPath": "swashbuckle.aspnetcore.swagger.4.0.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ztAj0T1U+2AqQNA8b+nr8yVkDW9XzNaAfez6d1jO13sdn2A/JW5Syn9TThsakrHxYNLt6y6aQCXbyBfQXpcQwA==", "path": "swashbuckle.aspnetcore.swaggergen/4.0.1", "hashPath": "swashbuckle.aspnetcore.swaggergen.4.0.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-d2U7NyV0e4UhyCzAVK9QHm0iz2QoVPUa9XzJ/Gr0rn/jBZWFpVLvigKv0vxFzO2E793sY605+4h885gvCdKSxQ==", "path": "swashbuckle.aspnetcore.swaggerui/4.0.1", "hashPath": "swashbuckle.aspnetcore.swaggerui.4.0.1.nupkg.sha512"}, "System.AppContext/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fKC+rmaLfeIzUhagxY17Q9siv/sPrjjKcfNg1Ic8IlQkZLipo8ljcaZQu4VtI4Jqbzjc2VTjzGLF6WmsRXAEgA==", "path": "system.appcontext/4.3.0", "hashPath": "system.appcontext.4.3.0.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/1.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-n+AGX7zmiZumW9aggOkXaHzUeAS3EfeTErnkKCusyONUozbTv+kMb8VE36m+ldV6kF9g57G2c641KCdgH9E0pg==", "path": "system.collections.immutable/1.3.1", "hashPath": "system.collections.immutable.1.3.1.nupkg.sha512"}, "System.Collections.NonGeneric/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-prtjIEMhGUnQq6RnPEYLpFt8AtLbp9yq2zxOSrY7KJJZrw25Fi97IzBqY7iqssbM61Ek5b8f3MG/sG1N2sN5KA==", "path": "system.collections.nongeneric/4.3.0", "hashPath": "system.collections.nongeneric.4.3.0.nupkg.sha512"}, "System.Collections.Specialized/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Epx8PoVZR0iuOnJJDzp7pWvdfMMOAvpUo95pC4ScH2mJuXkKA2Y4aR3cG9qt2klHgSons1WFh4kcGW7cSXvrxg==", "path": "system.collections.specialized/4.3.0", "hashPath": "system.collections.specialized.4.3.0.nupkg.sha512"}, "System.ComponentModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyGn1jGRZVfxnh8EdvDCi71v3bMXrsu8aYJOwoV7SNDLVhiEqwP86pPMyRGsDsxhXAm2b3o9OIqeETfN5qfezw==", "path": "system.componentmodel/4.3.0", "hashPath": "system.componentmodel.4.3.0.nupkg.sha512"}, "System.ComponentModel.Annotations/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-UxYQ3FGUOtzJ7LfSdnYSFd7+oEv6M8NgUatatIN2HxNtDdlcvFAf+VIq4Of9cDMJEJC0aSRv/x898RYhB4Yppg==", "path": "system.componentmodel.annotations/4.5.0", "hashPath": "system.componentmodel.annotations.4.5.0.nupkg.sha512"}, "System.ComponentModel.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-j8GUkCpM8V4d4vhLIIoBLGey2Z5bCkMVNjEZseyAlm4n5arcsJOeI3zkUP+zvZgzsbLTYh4lYeP/ZD/gdIAPrw==", "path": "system.componentmodel.primitives/4.3.0", "hashPath": "system.componentmodel.primitives.4.3.0.nupkg.sha512"}, "System.ComponentModel.TypeConverter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-16pQ6P+EdhcXzPiEK4kbA953Fu0MNG2ovxTZU81/qsCd1zPRsKc3uif5NgvllCY598k6bI0KUyKW8fanlfaDQg==", "path": "system.componentmodel.typeconverter/4.3.0", "hashPath": "system.componentmodel.typeconverter.4.3.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-UIFvaFfuKhLr9u5tWMxmVoDPkFeD+Qv8gUuap4aZgVGYSYMdERck4OhLN/2gulAc0nYTEigWXSJNNWshrmxnng==", "path": "system.configuration.configurationmanager/4.5.0", "hashPath": "system.configuration.configurationmanager.4.5.0.nupkg.sha512"}, "System.Console/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHDrIxiqk1h03m6khKWV2X8p/uvN79rgSqpilL6uzpmSfxfU5ng8VcPtW4qsDsQDHiTv6IPV9TmD5M/vElPNLg==", "path": "system.console/4.3.0", "hashPath": "system.console.4.3.0.nupkg.sha512"}, "System.Data.SqlClient/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-gwItUWW1BMCckicFO85c8frFaMK8SGqYn5IeA3GSX4Lmid+CjXETfoHz7Uv+Vx6L0No7iRc/7cBL8gd6o9k9/g==", "path": "system.data.sqlclient/4.6.0", "hashPath": "system.data.sqlclient.4.6.0.nupkg.sha512"}, "System.Diagnostics.Contracts/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-eelRRbnm+OloiQvp9CXS0ixjNQldjjkHO4iIkR5XH2VIP8sUB/SIpa1TdUW6/+HDcQ+MlhP3pNa1u5SbzYuWGA==", "path": "system.diagnostics.contracts/4.3.0", "hashPath": "system.diagnostics.contracts.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-zCno/m44ymWhgLFh7tELDG9587q0l/EynPM0m4KgLaWQbz/TEKvNRX2YT5ip2qXW/uayifQ2ZqbnErsKJ4lYrQ==", "path": "system.diagnostics.diagnosticsource/4.5.1", "hashPath": "system.diagnostics.diagnosticsource.4.5.1.nupkg.sha512"}, "System.Diagnostics.EventLog/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QaQAhEk18QSBPSu4VjXcznvjlg45IoXcJJNS5hcoqyyLj58g/SzQwpYXUrdzo+UtHV0grmOzFwABxhCYSTTp5Q==", "path": "system.diagnostics.eventlog/4.5.0", "hashPath": "system.diagnostics.eventlog.4.5.0.nupkg.sha512"}, "System.Diagnostics.FileVersionInfo/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-omCF64wzQ3Q2CeIqkD6lmmxeMZtGHUmzgFMPjfVaOsyqpR66p/JaZzManMw1s33osoAb5gqpncsjie67+yUPHQ==", "path": "system.diagnostics.fileversioninfo/4.3.0", "hashPath": "system.diagnostics.fileversioninfo.4.3.0.nupkg.sha512"}, "System.Diagnostics.StackTrace/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiHg0vgtd35/DM9jvtaC1eKRpWZxr0gcQd643ABG7GnvSlf5pOkY2uyd42mMOJoOmKvnpNj0F4tuoS1pacTwYw==", "path": "system.diagnostics.stacktrace/4.3.0", "hashPath": "system.diagnostics.stacktrace.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "path": "system.diagnostics.tools/4.3.0", "hashPath": "system.diagnostics.tools.4.3.0.nupkg.sha512"}, "System.Diagnostics.TraceSource/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VnYp1NxGx8Ww731y2LJ1vpfb/DKVNKEZ8Jsh5SgQTZREL/YpWRArgh9pI8CDLmgHspZmLL697CaLvH85qQpRiw==", "path": "system.diagnostics.tracesource/4.3.0", "hashPath": "system.diagnostics.tracesource.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.Drawing.Common/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-GiyeGi/v4xYDz1vCNFwFvhz9k1XddOG7VD3jxRqzRBCbTHji+s3HxxbxtoymuK4OadEpgotI8zQ5+GEEH9sUEQ==", "path": "system.drawing.common/4.5.1", "hashPath": "system.drawing.common.4.5.1.nupkg.sha512"}, "System.Dynamic.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SNVi1E/vfWUAs/WYKhE9+qlS6KqK0YVhnlT0HQtr8pMIA8YX3lwy3uPMownDwdYISBdmAF/2holEIldVp85Wag==", "path": "system.dynamic.runtime/4.3.0", "hashPath": "system.dynamic.runtime.4.3.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "path": "system.globalization.calendars/4.3.0", "hashPath": "system.globalization.calendars.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/5.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-E8tNMfMWPvlSF5fvmMIVZZHlGuIZzE5uktuR+GN2gFdngh0k6xoZquxfjKC02d0NqfsshNQVTCdSKXD5e9/lpA==", "path": "system.identitymodel.tokens.jwt/5.2.0", "hashPath": "system.identitymodel.tokens.jwt.5.2.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "path": "system.io.compression/4.3.0", "hashPath": "system.io.compression.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-sYg+FtILtRQuYWSIAuNOELwVuVsxVyJGWQyOnlAzhV4xvhyFnON1bAzYYC+jjRW8JREM45R0R5Dgi8MTC5sEwA==", "path": "system.net.http/4.3.0", "hashPath": "system.net.http.4.3.0.nupkg.sha512"}, "System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "path": "system.net.primitives/4.3.0", "hashPath": "system.net.primitives.4.3.0.nupkg.sha512"}, "System.Numerics.Vectors/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-UiLzLW+Lw6HLed1Hcg+8jSRttrbuXv7DANVj0DkL9g6EnnzbL75EB7EWsw5uRbhxd/4YdG8li5XizGWepmG3PQ==", "path": "system.numerics.vectors/4.4.0", "hashPath": "system.numerics.vectors.4.4.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Private.DataContractSerialization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yDaJ2x3mMmjdZEDB4IbezSnCsnjQ4BxinKhRAaP6kEgL6Bb6jANWphs5SzyD8imqeC/3FxgsuXT6ykkiH1uUmA==", "path": "system.private.datacontractserialization/4.3.0", "hashPath": "system.private.datacontractserialization.4.3.0.nupkg.sha512"}, "System.Private.ServiceModel/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-ancrQgJagx+yC4SZbuE+eShiEAUIF0E1d21TRSoy1C/rTwafAVcBr/fKibkq5TQzyy9uNil2tx2/iaUxsy0S9g==", "path": "system.private.servicemodel/4.5.3", "hashPath": "system.private.servicemodel.4.5.3.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.DispatchProxy/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-+UW1hq11TNSeb+16rIk8hRQ02o339NFyzMc4ma/FqmxBzM30l1c2IherBB4ld1MNcenS48fz8tbt50OW4rVULA==", "path": "system.reflection.dispatchproxy/4.5.0", "hashPath": "system.reflection.dispatchproxy.4.5.0.nupkg.sha512"}, "System.Reflection.Emit/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-VR4kk8XLKebQ4MZuKuIni/7oh+QGFmZW3qORd1GvBq/8026OpW501SzT/oypwiQl4TvT8ErnReh/NzY9u+C6wQ==", "path": "system.reflection.emit/4.7.0", "hashPath": "system.reflection.emit.4.7.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-AucBYo3DSI0IDxdUjKksBcQJXPHyoPyrCXYURW1WDsLI4M65Ar/goSHjdnHOAY9MiYDNKqDlIgaYm+zL2hA1KA==", "path": "system.reflection.emit.ilgeneration/4.7.0", "hashPath": "system.reflection.emit.ilgeneration.4.7.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Metadata/1.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-KYPNMDrLB2R+G5JJiJ2fjBpihtktKVIjsirmyyv+VDo5rQkIR9BWeCYM1wDSzbQatWNZ/NQfPsQyTB1Ui3qBfQ==", "path": "system.reflection.metadata/1.4.2", "hashPath": "system.reflection.metadata.1.4.2.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-dkmh/ySlwnXJp/1qYP9uyKkCK1CXR/REFzl7abHcArxBcV91mY2CgrrzSRA5Z/X4MevJWwXsklGRdR3A7K9zbg==", "path": "system.reflection.typeextensions/4.4.0", "hashPath": "system.reflection.typeextensions.4.4.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZD9TMpsmYJLrxbbmdvhwt9YEgG5WntEnZ/d1eH8JBX9LBp+Ju8BSBhUGbZMNVHHomWo2KVImJhTDl2hIgw/6MA==", "path": "system.runtime.compilerservices.unsafe/5.0.0", "hashPath": "system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wz+0KOukJGAlXjtKr+5Xpuxf8+c8739RI1C+A2BoQZT+wMCCoMDDdO8/4IRHfaVINqL78GO8dW8G2lW/e45Mcw==", "path": "system.runtime.serialization.primitives/4.3.0", "hashPath": "system.runtime.serialization.primitives.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Xml/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-nUQx/5OVgrqEba3+j7OdiofvVq9koWZAC7Z3xGI8IIViZqApWnZ5+lLcwYgTlbkobrl/Rat+Jb8GeD4WQESD2A==", "path": "system.runtime.serialization.xml/4.3.0", "hashPath": "system.runtime.serialization.xml.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "path": "system.security.accesscontrol/5.0.0", "hashPath": "system.security.accesscontrol.5.0.0.nupkg.sha512"}, "System.Security.Claims/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-P/+BR/2lnc4PNDHt/TPBAWHVMLMRHsyYZbU1NphW4HIWzCggz8mJbTQQ3MKljFE7LS3WagmVFuBgoLcFzYXlkA==", "path": "system.security.claims/4.3.0", "hashPath": "system.security.claims.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-3d/d+7sdNpfYfqJFzuE/o6Pl/reaMbH7rlUMNvtm4+XVYHY32tdFa45yjB3vhb6q0YY+IV8GUuiBPRsBFP3yaw==", "path": "system.security.cryptography.cng/4.4.0", "hashPath": "system.security.cryptography.cng.4.4.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "path": "system.security.cryptography.openssl/4.3.0", "hashPath": "system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-TGQX51gxpY3K3I6LJlE2LAftVlIMqJf0cBGhz68Y89jjk3LJCB6SrwiD+YN1fkqemBvWGs+GjyMJukl6d6goyQ==", "path": "system.security.cryptography.pkcs/4.5.0", "hashPath": "system.security.cryptography.pkcs.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-wLBKzFnDCxP12VL9ANydSYhk59fC4cvOr9ypYQLPnAj48NQIhqnjdD2yhP8yEKyBJEjERWS9DisKL7rX5eU25Q==", "path": "system.security.cryptography.protecteddata/4.5.0", "hashPath": "system.security.cryptography.protecteddata.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "path": "system.security.cryptography.x509certificates/4.3.0", "hashPath": "system.security.cryptography.x509certificates.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Xml/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-i2Jn6rGXR63J0zIklImGRkDIJL4b1NfPSEbIVHBlqoIb12lfXIigCbDRpDmIEzwSo/v1U5y/rYJdzZYSyCWxvg==", "path": "system.security.cryptography.xml/4.5.0", "hashPath": "system.security.cryptography.xml.4.5.0.nupkg.sha512"}, "System.Security.Permissions/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-9gdyuARhUR7H+p5CjyUB/zPk7/Xut3wUSP8NJQB6iZr8L3XUXTMdoLeVAg9N4rqF8oIpE7MpdqHdDHQ7XgJe0g==", "path": "system.security.permissions/4.5.0", "hashPath": "system.security.permissions.4.5.0.nupkg.sha512"}, "System.Security.Principal/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I1tkfQlAoMM2URscUtpcRo/hX0jinXx6a/KUtEQoz3owaYwl3qwsO8cbzYVVnjxrzxjHo3nJC+62uolgeGIS9A==", "path": "system.security.principal/4.3.0", "hashPath": "system.security.principal.4.3.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.ServiceModel.Duplex/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-cXn6zfl2od9Au3sDpArjUXo7zmNPLw77sjOrAUqjrh3TsImy8SPMSC4/F58jJGJrxUiyPo0DDwalRaF5JXZqsQ==", "path": "system.servicemodel.duplex/4.5.3", "hashPath": "system.servicemodel.duplex.4.5.3.nupkg.sha512"}, "System.ServiceModel.Http/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-zxR4z6G/FFK/uAUbo7+3IJOqm0w4/lyfHSQDf+hhUHRTc7XSeReGS5iKQq95gyl1ighHEuayqOiB7iacrB6ZUg==", "path": "system.servicemodel.http/4.5.3", "hashPath": "system.servicemodel.http.4.5.3.nupkg.sha512"}, "System.ServiceModel.NetTcp/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-Bx4oVK4ApBvZ0C3J62A8p3j6U0XK54JjN0byK52Qw4EgK89Uc48XzbF+0m1Oysc2bnnbrur+SwFWw7J8co3jTQ==", "path": "system.servicemodel.nettcp/4.5.3", "hashPath": "system.servicemodel.nettcp.4.5.3.nupkg.sha512"}, "System.ServiceModel.Primitives/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-Wc9Hgg4Cmqi416zvEgq2sW1YYCGuhwWzspDclJWlFZqY6EGhFUPZU+kVpl5z9kAgrSOQP7/Uiik+PtSQtmq+5A==", "path": "system.servicemodel.primitives/4.5.3", "hashPath": "system.servicemodel.primitives.4.5.3.nupkg.sha512"}, "System.ServiceModel.Security/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-e0c5f536zJ2qEp8sDbI88Tm+NLkx9eqGiXQbQx5fQEtCfQ/dqPOwluu/3aAj/9Bc5XdBAaQcElmr1kyjr2j3EA==", "path": "system.servicemodel.security/4.5.3", "hashPath": "system.servicemodel.security.4.5.3.nupkg.sha512"}, "System.ServiceProcess.ServiceController/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-8DGUtcNHf9TlvSVemKMFiqcOWJ4OdGBgvpcGL/cYossGf5ApMQdPUQS8vXHTBmlbYAcG+JXsjMFGAHp2oJrr+Q==", "path": "system.serviceprocess.servicecontroller/4.5.0", "hashPath": "system.serviceprocess.servicecontroller.4.5.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NyscU59xX6Uo91qvhOs2Ccho3AR2TnZPomo1Z0K6YpyztBPM/A5VbkzOO19sy3A3i1TtEnTxA7bCe3Us+r5MWg==", "path": "system.text.encoding.codepages/5.0.0", "hashPath": "system.text.encoding.codepages.5.0.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-Xg4G4Indi4dqP1iuAiMSwpiWS54ZghzR644OtsRCm/m/lBMG8dUBhLVN7hLm8NNrNTR+iGbshCPTwrvxZPlm4g==", "path": "system.text.encodings.web/4.5.0", "hashPath": "system.text.encodings.web.4.5.0.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RpT2DA+L660cBt1FssIE9CAGpLFdFPuheB7pLpKpn6ZXNby7jDERe8Ua/Ne2xGiwLVG2JOqziiaVCGDon5sKFA==", "path": "system.text.regularexpressions/4.3.0", "hashPath": "system.text.regularexpressions.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Threading.Tasks.Parallel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbjBNZHf/vQCfcdhzx7knsiygoCKgxL8mZOeocXZn5gWhCdzHIq6bYNKWX0LAJCWYP7bds4yBK8p06YkP0oa0g==", "path": "system.threading.tasks.parallel/4.3.0", "hashPath": "system.threading.tasks.parallel.4.3.0.nupkg.sha512"}, "System.Threading.Thread/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OHmbT+Zz065NKII/ZHcH9XO1dEuLGI1L2k7uYss+9C1jLxTC9kTZZuzUOyXHayRk+dft9CiDf3I/QZ0t8JKyBQ==", "path": "system.threading.thread/4.3.0", "hashPath": "system.threading.thread.4.3.0.nupkg.sha512"}, "System.ValueTuple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cNLEvBX3d6MMQRZe3SMFNukVbitDAEpVZO17qa0/2FHxZ7Y7PpFRpr6m2615XYM/tYYYf0B+WyHNujqIw8Luwg==", "path": "system.valuetuple/4.3.0", "hashPath": "system.valuetuple.4.3.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5zJ0XDxAIg8iy+t4aMnQAu0MqVbqyvfoUVl1yDV61xdo3Vth45oA2FoY4pPkxYAH5f8ixpmTqXeEIya95x0aCQ==", "path": "system.xml.xdocument/4.3.0", "hashPath": "system.xml.xdocument.4.3.0.nupkg.sha512"}, "System.Xml.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lJ8AxvkX7GQxpC6GFCeBj8ThYVyQczx2+f/cWHJU8tjS7YfI6Cv6bon70jVEgs2CiFbmmM8b9j1oZVx0dSI2Ww==", "path": "system.xml.xmldocument/4.3.0", "hashPath": "system.xml.xmldocument.4.3.0.nupkg.sha512"}, "System.Xml.XmlSerializer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-MYoTCP7EZ98RrANESW05J5ZwskKDoN0AuZ06ZflnowE50LTpbR5yRg3tHckTVm5j/m47stuGgCrCHWePyHS70Q==", "path": "system.xml.xmlserializer/4.3.0", "hashPath": "system.xml.xmlserializer.4.3.0.nupkg.sha512"}, "System.Xml.XPath/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-v1JQ5SETnQusqmS3RwStF7vwQ3L02imIzl++sewmt23VGygix04pEH+FCj1yWb+z4GDzKiljr1W7Wfvrx0YwgA==", "path": "system.xml.xpath/4.3.0", "hashPath": "system.xml.xpath.4.3.0.nupkg.sha512"}, "System.Xml.XPath.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-jw9oHHEIVW53mHY9PgrQa98Xo2IZ0ZjrpdOTmtvk+Rvg4tq7dydmxdNqUvJ5YwjDqhn75mBXWttWjiKhWP53LQ==", "path": "system.xml.xpath.xdocument/4.3.0", "hashPath": "system.xml.xpath.xdocument.4.3.0.nupkg.sha512"}, "TimeZoneConverter/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-U7Oilf3Ya6Rmu6gOaBfWyT3q0kwy2av6a5PfTn05CF54C+7DvuLsE3ljASvYmCpsSQeJvpnqU5Uzag6+ysWUeA==", "path": "timezoneconverter/5.0.0", "hashPath": "timezoneconverter.5.0.0.nupkg.sha512"}, "Twilio/5.20.1": {"type": "package", "serviceable": true, "sha512": "sha512-UKfpDK6032qFzcSAFZbSUlvphr/XZcOQ2epWXaV3w6O3d6DAUWA+pKtIxIBlocbCRJqVxvIJeKzF9DFgrX0Atw==", "path": "twilio/5.20.1", "hashPath": "twilio.5.20.1.nupkg.sha512"}, "Twilio.AspNet.Common/5.20.1": {"type": "package", "serviceable": true, "sha512": "sha512-Bexs6ktjjteOZWFWOSp6JHynA9WW2SeOZxjzQTBGBwlNrKvp+d0neRSpbbPGwakHd7BMCWA/SiKR6GC/Hc4eHA==", "path": "twilio.aspnet.common/5.20.1", "hashPath": "twilio.aspnet.common.5.20.1.nupkg.sha512"}, "Twilio.AspNet.Core/5.20.1": {"type": "package", "serviceable": true, "sha512": "sha512-1s/8rqdfyR5pUaoYDdzm3XADTePiJ1qcp2mQsUOYeAWSuKeJ5eDjsPRDydx8BJdQUPVSJVbBi3WdOOFFoh3MYQ==", "path": "twilio.aspnet.core/5.20.1", "hashPath": "twilio.aspnet.core.5.20.1.nupkg.sha512"}, "UAParser/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-q3HQawDOf6+8lzYyc+WDSPO2Sz0F5SpD7jaBm22bBy1i1/DtHTw7rPfuX5J7IRDVXxkJXp5QTL1NIvXCaKTkvQ==", "path": "uaparser/3.0.0", "hashPath": "uaparser.3.0.0.nupkg.sha512"}, "ZstdSharp.Port/0.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-jPao/LdUNLUz8rn3H1D8W7wQbZsRZM0iayvWI4xGejJg3XJHT56gcmYdgmCGPdJF1UEBqUjucCRrFB+4HbJsbw==", "path": "zstdsharp.port/0.6.2", "hashPath": "zstdsharp.port.0.6.2.nupkg.sha512"}, "CommonLibCore/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "EBizAutos.ApplicationCommonLib/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "EBizAutos.Apps.CommonLib/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "FoundationCommonLib/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "CommonLibCore.Reference/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "FoundationCommonLib.Reference/*******": {"type": "reference", "serviceable": false, "sha512": ""}}}