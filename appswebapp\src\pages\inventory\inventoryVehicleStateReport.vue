<template>
  <div class="position-relative">
    <h4>Vehicle State Report</h4>
    <paging
      class="inventory-vehicle-state-report-paging"
      :totalItems="totalItemsCount"
      :pageNumber="filter.page"
      :pageSize="filter.pageSize"
      @numberChanged="onPageChanged"
      @changePageSize="onPageSizeChanged" />
    <b-card>
      <b-form @submit.prevent="onFilterApply">
        <b-row>
          <b-col class="my-2" xl="4" lg="12" md="12" sm="12">
            <b-form-input v-model="filter.search" placeholder="Search..." autocomplete="off"></b-form-input>
          </b-col>
          <b-col class="my-2" xl="2" lg="6" md="6" sm="6">
            <b-input-group class="flex-nowrap">
              <b-input-group-prepend is-text>
                <i class="ion ion-md-calendar" slot="prepend"></i>
              </b-input-group-prepend>
              <date-time-picker
                ref="timeFrom"
                v-model="filter.dateFrom"
                :options="filterTimeOptions"
                format="MM/DD/YYYY HH:mm"
                placeholder="Date From"
                className="form-control"
                @change="onTimeFromInputChange"
              />
              <b-input-group-append
                is-text
                v-show="filter.dateFrom"
                @click="filter.dateFrom = null"
              >
                <i class="ion ion-md-close"></i>
              </b-input-group-append>
            </b-input-group>
          </b-col>
          <b-col class="my-2" xl="2" lg="6" md="6" sm="6">
            <b-input-group class="flex-nowrap">
              <b-input-group-prepend is-text>
                <i class="ion ion-md-calendar" slot="prepend"></i>
              </b-input-group-prepend>
              <date-time-picker
                ref="timeTo"
                v-model="filter.dateTo"
                :options="filterTimeOptions"
                format="MM/DD/YYYY HH:mm"
                placeholder="Date To"
                className="form-control"
                @change="onTimeToInputChange"
              />
              <b-input-group-append
                is-text
                v-show="filter.dateTo"
                @click="filter.dateTo = null"
              >
                <i class="ion ion-md-close"></i>
              </b-input-group-append>
            </b-input-group>
          </b-col>
          <b-col class="my-2" xl="2" lg="6" md="6" sm="12">
            <b-btn class="w-100" variant="primary" type="submit">Apply</b-btn>
          </b-col>
          <b-col class="my-2" xl="2" lg="6" md="6" sm="12">
            <b-btn class="w-100" variant="secondary" @click="showForcingModal"><i class="ion ion-md-sync mr-1" style="transform: rotate(90deg);"></i>Force Sync</b-btn>
          </b-col>
        </b-row>
      </b-form>
      <div class="width-100 ml-2" v-if="notificationMessage">
        <span class="text-primary font-weight-bold force-account-notice">{{ notificationMessage }}</span>
      </div>
    </b-card>
    <b-card v-if="!isLoading">
      <inventorySynchronizationLogListing :items='items' :totalItems='totalItemsCount' @onSortChanged="onSortChanged"/>
      <paging
        :totalItems="totalItemsCount"
        :pageNumber="filter.page"
        :pageSize="filter.pageSize"
        titled
        pageSizeSelector
        @numberChanged="onPageChanged"
        @changePageSize="onPageSizeChanged" />
    </b-card>
    <loader v-else size="lg" class="mt-4"/>

    <b-modal
      :visible="isVisibleForcingModal"
      hide-footer
      hide-header
      centered
      @hide="onHideForcingModal"
    >
      <div class="w-100 pb-5">
        <b-button-close class="border float-right py-1 px-2" @click="onHideForcingModal"></b-button-close>
      </div>
      <b-input-group class="d-none d-md-flex w-100">
        <b-form-input placeholder="VIN" v-model="newVehicleVinToForce"></b-form-input>
        <b-input-group-append>
          <b-button variant="primary" @click="forceNewVehicle" :disabled="isNewVehicleForceRequested">Force Newly Added Sync</b-button>
        </b-input-group-append>
      </b-input-group>
      <b-row class="d-block d-md-none">
        <b-col class="my-1" cols="12">
          <b-form-input placeholder="VIN" v-model="newVehicleVinToForce"></b-form-input>
        </b-col>
        <b-col class="my-1" cols="12">
          <b-button class="w-100" variant="primary" @click="forceNewVehicle" :disabled="isNewVehicleForceRequested">Force Newly Added Sync</b-button>
        </b-col>
      </b-row>
      <template v-if="user && user.isEbizDev">
        <ebiz-divider/>
        <b-btn class="mb-2 w-100" variant="secondary" @click="onForceAccountVehicles()" :disabled="isForceAccountVehiclesProcessing">
          Force Account Synchronization
        </b-btn>
        <b-btn class="w-100" variant="secondary" @click="rebuildVehiclesData" :disabled="isForceAccountVehiclesProcessing">Rebuild Account Vehicles Data</b-btn>
        <b-btn class="mt-2 w-100" variant="secondary" @click="forceAccountVehiclesVideoRegeneration" :disabled="isForceAccountVehiclesProcessing">
          Force Account Auto Video Encoding
        </b-btn>
      </template>
    </b-modal>
  </div>
</template>

<script>
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import inventorySynchronizationLogListing from '@/components/inventory/inventoryLogs/inventorySynchronizationLogListing'
import loader from '@/components/_shared/loader'
import paging from '@/components/_shared/paging.vue'
import InventoryLogService from '../../services/logs/InventoryLogService'
import SyncService from '@/services/inventory/SyncService'
import {mapGetters} from 'vuex'

const defaultValues = new ObjectSchema({
  pageSize: { type: Number, default: 25 },
  page: { type: Number, default: 1 },
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  sortType: { type: Number, default: 4 },
  search: { type: String, default: '' }
})
const queryHelper = new QueryStringHelper(defaultValues)

export default {
  name: 'inventory-vehicle-state-report',
  metaInfo: {
    title: 'Vehicle State Report'
  },
  props: {
    accountId: { type: Number, required: true }
  },
  data () {
    return {
      isVisibleForcingModal: false,
      filter: defaultValues.getObject(),
      items: null,
      notificationMessage: '',
      totalItemsCount: 0,
      isLoading: true,
      isForceAccountVehiclesProcessing: false,
      newVehicleVinToForce: '',
      isNewVehicleForceRequested: false,
      filterTimeOptions: {
        autoUpdateInput: false,
        startDate: new Date(),
        singleDatePicker: true,
        timePicker: true,
        timePicker24Hour: true,
        maxDate: new Date()
      },
      newVehicleLoadingLogInterval: null
    }
  },
  created () {
    this.filter = queryHelper.parseQueryStringToObject(this.$router)
    this.populateData()
  },
  beforeDestroy () {
    clearInterval(this.newVehicleLoadingLogInterval)
  },
  components: {
    'date-time-picker': () => import('@gravitano/vue-date-range-picker/src/components/DateRangePicker'),
    paging,
    inventorySynchronizationLogListing,
    loader
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {}
    },
    isEbizDev () {
      return this.user.isEbizDev
    },
    refDateTimeFrom () {
      return (this.$refs.timeFrom || {}).$el || {}
    },
    refDateTimeTo () {
      return (this.$refs.timeTo || {}).$el || {}
    }
  },
  methods: {
    onPageChanged (newPage) {
      this.filter.page = newPage
      this.isLoading = true
      this.synchronizeUrlAndReload()
    },
    onPageSizeChanged (newSize) {
      this.filter.pageSize = newSize
      this.filter.page = 1
      this.isLoading = true
      this.synchronizeUrlAndReload()
    },
    onSortChanged (sortType) {
      this.filter.sortType = sortType
      this.synchronizeUrlAndReload()
    },
    onFilterApply () {
      this.isLoading = true
      this.filter.page = 1
      this.synchronizeUrlAndReload()
    },
    synchronizeUrlAndReload () {
      queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.populateData()
    },
    onTimeFromInputChange (newVal) {
      this.refDateTimeFrom.value = newVal || this.filter.dateFrom || null
    },
    onTimeToInputChange (newVal) {
      this.refDateTimeTo.value = newVal || this.filter.dateTo || null
    },
    onForceAccountVehicles () {
      this.notificationMessage = ''
      this.isForceAccountVehiclesProcessing = true
      SyncService.forceSynchronizeAccountVehicles(this.accountId).then(res => {
        this.notificationMessage = `Task has been received. ${res.data} vehicles have been queued for emergency synchronization. Please do not click again`
      }).catch(ex => {
        this.$toaster.error(`Somethings is wrong. Exception message: ${ex.message}`)
        this.$logger.handleError(ex, 'Failed to force account synchronization')
      }).finally(() => {
        this.onHideForcingModal()
        this.isForceAccountVehiclesProcessing = false
      })
    },
    populateData () {
      clearInterval(this.newVehicleLoadingLogInterval)
      let apiFilter = {
        ...this.filter,
        accountId: this.accountId
      }
      InventoryLogService.getSynchronizationLogs(apiFilter).then(res => {
        this.items = res.data.items
        this.totalItemsCount = res.data.itemsTotalCount
      }).catch(ex => {
        this.$toaster.exception(ex, 'Failed to get data from server')
        this.$logger.handleError(ex, 'Failed on getting data from server', this.filter)
      }).finally(() => {
        this.isLoading = false
      })
    },
    rebuildVehiclesData () {
      this.notificationMessage = ''
      this.isForceAccountVehiclesProcessing = true
      SyncService.forceRebuildAccountVehiclesData(this.accountId).then(res => {
        this.notificationMessage = `Task has been received. ${res.data} vehicles have been queued for emergency vehicle data rebuild. Please do not click again`
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something is wrong!', {timeout: 5000})
        this.$logger.handleError(ex, 'Failed to force account vehicles rebuild data')
      }).finally(() => {
        this.isForceAccountVehiclesProcessing = false
        this.onHideForcingModal()
      })
    },
    forceAccountVehiclesVideoRegeneration () {
      this.isForceAccountVehiclesProcessing = true
      SyncService.forceAccountVehiclesVideoRegeneration(this.accountId).then(res => {
        this.$toaster.success('Sent to process')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something is wrong!', {timeout: 5000})
        this.$logger.handleError(ex, 'Failed to force account vehicles video')
      }).finally(() => {
        this.isForceAccountVehiclesProcessing = false
        this.onHideForcingModal()
      })
    },
    forceNewVehicle () {
      if (!this.newVehicleVinToForce) {
        this.$toaster.error('VIN is required')
      }
      this.isNewVehicleForceRequested = true
      SyncService.forceSynchronizeNewVehicle(this.accountId, this.newVehicleVinToForce).then(res => {
        this.$toaster.success('Moved To Process Successfully')
        this.filter.page = 1
        this.items = [{
          vehicleTitle: 'New Vehicle',
          vehicleState: {
            accountId: this.accountId,
            vin: this.newVehicleVinToForce
          }
        }]
        this.totalItemsCount = 1
        this.createNewVehicleLogLoadInterval(this.newVehicleVinToForce)
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something is wrong!', {timeout: 5000})
      }).finally(() => {
        this.isNewVehicleForceRequested = false
        this.newVehicleVinToForce = ''
        this.onHideForcingModal()
      })
    },
    createNewVehicleLogLoadInterval (vin) {
      this.newVehicleLoadingLogInterval = setInterval(function () {
        let apiFilter = {
          ...this.filter,
          accountId: this.accountId
        }
        apiFilter.search = vin
        apiFilter.page = 1
        InventoryLogService.getSynchronizationLogs(apiFilter).then(res => {
          if (res.data.items && res.data.items.some(x => x.vehicleState.vin === vin)) {
            this.items = res.data.items
            this.totalItemsCount = res.data.itemsTotalCount
            clearInterval(this.newVehicleLoadingLogInterval)
          }
        }).catch(ex => {
          this.$logger.handleError(ex, 'Failed on getting data from server', this.filter)
        })
      }.bind(this), 5000)
    },
    onHideForcingModal () {
      this.isVisibleForcingModal = false
    },
    showForcingModal () {
      this.isVisibleForcingModal = true
    }
  }
}
</script>

<style>
.inventory-vehicle-state-report-paging {
  position: absolute;
  right: -5px;
  top: -10px;
  z-index: 2;
}
</style>
