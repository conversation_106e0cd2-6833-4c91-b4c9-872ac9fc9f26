<template>
    <b-table
      :fields='getTableFields'
      :items="items"
      :sort-by="tableSortBy"
      :sort-desc="tableSortDesc"
      @sort-changed="onSortChanged"
      :no-local-sorting="true"
      :no-sort-reset="true"
      striped
      hover
      responsive
      class="products-table card-table"
    >
      <template #cell(campaign)="data">
        <div class="d-flex flex-column">
          <span v-for="campaign in data.item.campaignNames" :key='campaign'>{{campaign}}</span>
        </div>
      </template>
      <template #cell(type)="data">
        <span v-if='data.item.communicationType === communicationTypes.sms.value'>
          <i class="ion ion-ios-chatboxes h4 m-0 opacity-100 d-none d-sm-inline"></i>
          {{communicationTypes.sms.label}}
        </span>
        <span v-if='data.item.communicationType === communicationTypes.voice.value'>
          <i class='ion ion-ios-call h4 m-0 opacity-100 d-none d-sm-inline'></i>
          {{communicationTypes.voice.label}}
        </span>
        <span v-if='data.item.communicationType === communicationTypes.email.value'>
          <i class='ion ion-ios-mail h4 m-0 opacity-100 d-none d-sm-inline'></i>
          {{communicationTypes.email.label}}
        </span>
        <span v-if='data.item.communicationType === communicationTypes.webForm.value'>
          <i class='ion ion-ios-mail h4 m-0 opacity-100 d-none d-sm-inline'></i>
          {{communicationTypes.webForm.label}}
        </span>
        <span v-if='data.item.communicationType === communicationTypes.eBay.value'>{{communicationTypes.eBay.label}}</span>
      </template>
      <template #cell(department)="data">
        <div class="d-flex flex-column">
          <span v-for='type in data.item.dealerContact.departmentTypes' :key='type'>{{getDepartmentDesc(type)}}</span>
        </div>
      </template>
      <template #cell(vehicleTitle)="data">
        <b-link v-if="data.item.vdpLink" class="vdp-link" target="_blank" rel="noopener noreferrer" :href="data.item.vdpLink">{{data.item.vehicleTitle}}</b-link>
        <span v-else>
          {{data.item.vehicleTitle}}
        </span>
      </template>
      <template #cell(manage)="data">
        <b-button size="sm" @click.stop="data.toggleDetails" class="text-center">
          {{ data.detailsShowing ? 'Hide' : 'Show' }} Details
        </b-button>
      </template>
      <template #row-details="data">
        <b-card>
          <h6 class="mb-2">Message:</h6>
          <hr/>
          <div v-if='data.item.communicationType === communicationTypes.sms.value'>
            <sms :model='data.item' />
          </div>
          <div v-if='data.item.communicationType === communicationTypes.voice.value'>
            <voice :model='data.item' />
          </div>
          <div v-if='data.item.communicationType === communicationTypes.email.value'>
            <email :model='data.item' />
          </div>
          <div v-if='data.item.communicationType === communicationTypes.webForm.value'>
            <web-form :model='data.item' />
          </div>
          <div v-if='data.item.communicationType === communicationTypes.eBay.value'>
            <ebay :model='data.item' />
          </div>
          <div v-if="hasSendMessage">
            <b-btn class="float-right mt-2 ml-2" size="sm" variant="primary" :to="{ path: getPath }">Reply in Messenger</b-btn>
          </div>
          <b-btn v-if="hasArchiveMessage" size="sm" class="float-right mt-2" @click="archive(data.item.id)">Archive Lead</b-btn>
        </b-card>
      </template>
    </b-table>
</template>

<script>
import moment from 'moment'
import permissions from '@/shared/common/permissions'
import applicationTypes from '@/shared/common/applicationTypes'
import { communicationTypes, contactTypes, conversationSortTypes, conversationTabTypes } from '@/shared/leads/common'
import webForm from '@/components/leads/messenger/messageTypes/webForm'
import voice from '@/components/leads/messenger/messageTypes/voice'
import email from '@/components/leads/messenger/messageTypes/email'
import sms from '@/components/leads/messenger/messageTypes/sms'
import ebay from '@/components/leads/messenger/messageTypes/ebay'
import { mapGetters } from 'vuex'

export default {
  name: 'leads-contact-history-listing',
  props: {
    items: { type: Array, required: true },
    phone: { type: String },
    isClosed: { type: Boolean }
  },
  data () {
    return {
      communicationTypes,
      conversationTabTypes,
      accountId: +this.$route.params.accountId
    }
  },
  components: {
    'sms': sms,
    'ebay': ebay,
    'web-form': webForm,
    'voice': voice,
    'email': email
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {
        canManageAccountApplicationType: () => false
      }
    },
    hasArchiveMessage () {
      if (this.user && this.user.hasPermissions) {
        return this.user.hasPermissions(permissions.LeadsFullAccess) || (this.user.hasPermissions(permissions.LeadsSendMessages) && this.user.canManageAccountApplicationType(this.accountId, applicationTypes.AppsLeads.Id, applicationTypes.AppsLeads.PermissionFullAccess))
      }
    },
    hasSendMessage () {
      if (this.user && this.user.hasPermissions) {
        return (this.user.hasPermissions(permissions.LeadsFullAccess) || (this.user.hasPermissions(permissions.LeadsSendMessages) && this.user.canManageAccountApplicationType(this.accountId, applicationTypes.AppsLeads.Id, applicationTypes.AppsLeads.PermissionFullAccess))) &&
          !this.isClosed && this.phone
      }
      return false
    },
    getPath () {
      return `/leads/${this.accountId}/messenger?conversationid=${this.$route.params.conversationId}`
    },
    getTableFields () {
      return [
        {
          key: 'dateTime',
          label: 'Received',
          sortable: true,
          sortTypeAsc: conversationSortTypes.dateCreatedAsc,
          sortTypeDesc: conversationSortTypes.dateCreatedDesc,
          tdClass: 'py-2 align-middle',
          formatter: value => moment(value).format('MM/DD/YYYY hh:mm A')
        },
        {
          key: 'campaign',
          label: 'Campaign',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'type',
          label: 'Type',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'leadTypeName',
          label: 'Form Type',
          sortable: true,
          sortTypeAsc: conversationSortTypes.leadTypeAsc,
          sortTypeDesc: conversationSortTypes.leadTypeDesc,
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'department',
          label: 'Department',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'vehicleTitle',
          label: 'Vehicle',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    tableSortBy () {
      const sortedColumn = this.getTableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.getTableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sortType
      } else {
        return false
      }
    }
  },
  methods: {
    getDepartmentDesc (type) {
      let res = contactTypes.find(x => x.value === type)
      if (res) {
        return res.label
      }

      return ''
    },
    onSortChanged (value) {
      const sortingColumn = this.getTableFields.find(x => x.key === value.sortBy)
      if (value.sortDesc) {
        this.$emit('sortChange', sortingColumn.sortTypeDesc)
      } else {
        this.$emit('sortChange', sortingColumn.sortTypeAsc)
      }
    },
    archive (id) {
      this.$emit('archive', id)
    }
  }
}
</script>
