import BaseService from '@/services/BaseService'

class SystemToolsService extends BaseService {
  getUserAuthorizationLogs (filters) {
    return this.axios.get('/api/user_auth_logs/', {params: filters})
  };
  getUserAuthorizationLogDetails (id) {
    return this.axios.get(`/api/user_auth_logs/${id}/details`)
  };
  getUserAuthorizationLogsGroupByIpAddress () {
    return this.axios.get(`/api/user_auth_logs/group_by_ipaddress`)
  };
  getWhiteListedIpAddresses () {
    return this.axios.get('/api/users/whitelistedip')
  };
  addWhiteListedIpAddress (ipAddress) {
    return this.axios.post('/api/users/whitelistedip/add', {whitelistedIpAddresses: [ipAddress]})
  };
  deleteWhiteListedIpAddress (ipAddress) {
    return this.axios.post('/api/users/whitelistedip/delete', {whitelistedIpAddresses: [ipAddress]})
  };
  getUserAuthWhitelistRules () {
    return this.axios.get('/api/user_auth_whitelist_rule')
  };
  addNewUserAuthWhitelistRule (userAuthWhitelistRule) {
    return this.axios.post('/api/user_auth_whitelist_rule/add', userAuthWhitelistRule)
  };
  updateUserAuthWhitelistRule (id, userAuthWhitelistRule) {
    return this.axios.post(`/api/user_auth_whitelist_rule/${id}/update`, userAuthWhitelistRule)
  };
  deleteUserAuthWhitelistRule (id) {
    return this.axios.post(`/api/user_auth_whitelist_rule/${id}/delete`)
  };
  getUserSuggestions (searchPhrase) {
    return this.axios.get('/api/user_auth_whitelist_rule/user/suggestions', {params: {searchPhrase: searchPhrase}})
  }
}

export default new SystemToolsService()
