@import '~@/vendor/styles/_custom-variables/libs';

$pwd-strength-meter-spacer: 6px !default;

div.Password {
  max-width: 100%;
}

div.Password .Password__strength-meter {
  &::before,
  &::after {
    width: calc(25% + #{$pwd-strength-meter-spacer});
  }

  &::before {
    left: calc(25% - #{$pwd-strength-meter-spacer / 2});
  }

  &::after {
    right: calc(25% - #{$pwd-strength-meter-spacer / 2});
  }
}

div.Password .Password__badge {
  position: absolute;
  top: 50%;
  right: 10px;
  bottom: auto;
  padding: .25rem 0;
  width: 1.5rem;
  height: auto;
  border-radius: .125rem;
  text-align: center;
  font-weight: bold;
  font-size: .75rem;
  line-height: 1;
  transform: translateY(-50%);

  [dir=rtl] & {
    right: auto;
    left: 10px;
  }

  .material-style & {
    right: 0;
  }

  .material-style[dir=rtl] & {
    left: 0;
  }
}

.default-style {
  @import "~@/vendor/styles/_appwork/include";

  .Password .Password__strength-meter {
    background: $border-color;
  }
}

.material-style {
  @import "~@/vendor/styles/_appwork/include-material";

  .Password .Password__strength-meter {
    background: $border-color;
  }
}
