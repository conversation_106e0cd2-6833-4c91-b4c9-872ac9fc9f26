<template>
  <div>
    <website-engagement-summary
      :barItems="bar.items"
      :summary="summary"
      :barTimeFormat="barTimeFormat"
      :deviceFilter="page.deviceFilter"
      @deviceFilterChanged="onDeviceFilterChanged"
    ></website-engagement-summary>

    <template v-if="isAccountLevel">
      <account-level-card v-if="isAccountLevel" @backToGroup="onBackToGroup">
        {{accountName}} ({{page.filter.accountId}})
      </account-level-card>

      <website-engagement-account-level-table
        :tableItems="table.items"
        :totalItems="table.totalItems"
        :pageNumber="page.filter.pageNumber"
        :pageSize="page.filter.pageSize"
        :sortType="page.filter.sortType"
        @pageNumberChanged="onPageNumberChanged"
        @pageSizeChanged="onPageSizeChanged"
        @sortTypeChanged="onSortTypeChanged"
      ></website-engagement-account-level-table>
    </template>
    <group-website-engagement-by-account-table
      v-else-if="rangeInfo"
      :tableItems="table.items"
      :totalItems="table.totalItems"
      :pageNumber="page.filter.pageNumber"
      :pageSize="page.filter.pageSize"
      :sortType="page.filter.sortType"
      @pageNumberChanged="onPageNumberChanged"
      @pageSizeChanged="onPageSizeChanged"
      @sortTypeChanged="onSortTypeChanged"
      @accountNameClicked="onAccountNameClicked"
    ></group-website-engagement-by-account-table>
  </div>
</template>

<style src="@/vendor/styles/pages/analytics.scss" lang="scss"></style>

<script>
import analyticsConstants from './../../../shared/analytics/constants'
import analyticsBuilders from './../../../shared/analytics/builders'
import analyticsHelper from '../helpers.js'
import baseGroupReportPage from './baseGroupReportPage.js'

import WebsiteEngagementSummary from '../../../components/analytics_ga4/summaries/websiteEngagementSummary'
import AccountLevelCard from '../../../components/analytics_ga4/accountLevelCard'
import WebsiteEngagementAccountLevelTable from '../../../components/analytics_ga4/tables/websiteEngagementAccountLevelTable'
import GroupWebsiteEngagementByAccountTable from '../../../components/analytics_ga4/tables/groupWebsiteEngagementByAccountTable'

const filterManager = analyticsBuilders.getFilterManager({
  accountId: { type: Number, default: 0 },
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  pageNumber: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  sortType: { type: Number, default: analyticsConstants.websiteEngagementSortTypes.accountNameAsc }
})

const defaultAccountLevelSortType = analyticsConstants.websiteEngagementSortTypes.dateDesc

export default {
  mixins: [baseGroupReportPage],
  name: 'group-website-engagement',
  metaInfo: {
    title: 'Analytics - Website Engagement'
  },
  components: {
    GroupWebsiteEngagementByAccountTable,
    WebsiteEngagementAccountLevelTable,
    AccountLevelCard,
    WebsiteEngagementSummary
  },
  created () {
    this.$store.commit('analyticsGa4/setAnalyticsName', 'Website Engagement')
    this.$store.commit('analyticsGa4/setBlackThemeOn', true)
  },
  data () {
    return {
      page: {
        filter: filterManager.defaultValue
      },
      summary: {
        pageViewsPerSession: 0,
        pageViewsPerSessionDelta: null,
        avgSessionDuration: 0,
        avgSessionDurationDelta: null,
        bounceRate: 0,
        bounceRateDelta: null
      }
    }
  },
  computed: {
    filterManager () {
      return filterManager
    },
    defaultAccountLevelSortType () {
      return defaultAccountLevelSortType
    }
  },
  methods: {
    getAccountNameSortTypeCondition () {
      return this.page.filter.sortType === analyticsConstants.websiteEngagementSortTypes.accountNameAsc ||
        this.page.filter.sortType === analyticsConstants.websiteEngagementSortTypes.accountNameDesc
    },
    async updateStatistics () {
      try {
        await Promise.all([
          !this.isAccountLevel ? this.updateGroupLevelGraphAndSummary() : null,
          !this.isAccountLevel ? this.updateGroupLevelDetails() : null,
          this.isAccountLevel ? this.updateAccountLevelGraphAndSummary() : null,
          this.isAccountLevel ? this.updateAccountLevelDetails() : null
        ])
      } catch (err) {
        this.$toaster.error('Failed to get data from server', { timeout: 8000 })
        this.$logger.handleError(err, 'Can\'t update statistics', { filter: this.page.filter, cache: this.cache })
      }
    },
    async updateGroupLevelGraphAndSummary () {
      let storePath = 'analyticsGa4/'
      switch (this.page.deviceFilter) {
        case 'mobile':
          storePath += 'getGroupMobileEngagementGraphAndSummary'
          break
        case 'desktop':
          storePath += 'getGroupDesktopEngagementGraphAndSummary'
          break
        default:
          storePath += 'getGroupWebsiteEngagementGraphAndSummary'
          break
      }

      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        storePath,
        {
          reportGroupId: this.reportGroupId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo
        }
      )

      this.summary = {
        ...this.summary,
        ...store.summary.data
      }
      this.bar.items = store.graph.data.items
    },
    async updateAccountLevelGraphAndSummary () {
      let storePath = 'analyticsGa4/'
      switch (this.page.deviceFilter) {
        case 'mobile':
          storePath += 'getMobileEngagementGraphAndSummary'
          break
        case 'desktop':
          storePath += 'getDesktopEngagementGraphAndSummary'
          break
        default:
          storePath += 'getWebsiteEngagementGraphAndSummary'
          break
      }

      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        storePath,
        {
          accountId: this.page.filter.accountId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo
        }
      )

      this.summary = {
        ...this.summary,
        ...store.summary.data
      }
      this.bar.items = store.graph.data.items
    },
    async updateGroupLevelDetails () {
      let storePath = ''
      switch (this.page.deviceFilter) {
        case 'mobile':
          storePath = 'analyticsGa4/getGroupMobileEngagementDetailsPage'
          break
        case 'desktop':
          storePath = 'analyticsGa4/getGroupDesktopEngagementDetailsPage'
          break
        default:
          storePath = 'analyticsGa4/getGroupWebsiteEngagementDetailsPage'
          break
      }

      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        storePath,
        {
          reportGroupId: this.reportGroupId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo,
          pageNumber: this.page.filter.pageNumber,
          pageSize: this.page.filter.pageSize,
          sortType: this.page.filter.sortType
        }
      )

      this.table.items = store.detailedData.data.items.map(x => {
        x.account.isAccessAllowed = this.isAccessToAccountAllowed(x.account.accountId)
        return x
      })
      this.table.totalItems = store.detailedData.data.totalItems
    },
    async updateAccountLevelDetails () {
      let storePath = ''
      switch (this.page.deviceFilter) {
        case 'mobile':
          storePath = 'analyticsGa4/getMobileEngagementDetailsPage'
          break
        case 'desktop':
          storePath = 'analyticsGa4/getDesktopEngagementDetailsPage'
          break
        default:
          storePath = 'analyticsGa4/getWebsiteEngagementDetailsPage'
          break
      }

      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        storePath,
        {
          accountId: this.page.filter.accountId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo,
          pageNumber: this.page.filter.pageNumber,
          pageSize: this.page.filter.pageSize,
          sortType: this.page.filter.sortType
        }
      )

      this.table.items = store.detailedData.data.items
      this.table.totalItems = store.detailedData.data.totalItems
    }
  }
}
</script>
