import BaseService from '@/services/BaseService'

class ConversationService extends BaseService {
  getAccountConversations (accountId, filter) {
    return this.axios.get(`/api/leads/${accountId}/conversations`, { params: filter })
  };
  getAccountConversationsWithDetails (accountId, conversationId, filter) {
    return this.axios.get(`/api/leads/${accountId}/conversations/${conversationId}/details`, { params: filter })
  };
  getAccountUsers (accountId, filter) {
    return this.axios.get(`/api/leads/${accountId}/conversations/users`, { params: filter })
  };
  getAccountConversationDetailsMany (accountId, filter) {
    return this.axios.get(`/api/leads/${accountId}/conversations/details`, { params: filter })
  };
  getConversationDetails (accountId, conversationId, conversationDetailsId) {
    return this.axios.get(`/api/leads/${accountId}/conversations/${conversationId}/details/${conversationDetailsId}`)
  };
  getCampaignTypes (accountId, filter) {
    return this.axios.get(`/api/leads/${accountId}/conversations/campaign_types`, {params: filter})
  };
  getArchivedCampaignTypes (accountId, filter) {
    return this.axios.get(`/api/leads/${accountId}/conversations/archive/campaign_types`, {params: filter})
  };
  getArchivedConversationDetailsMany (accountId, filter) {
    return this.axios.get(`/api/leads/${accountId}/conversations/archive`, { params: filter })
  };
  sendNotificationsForConversationsDetails (parameters) {
    return this.axios.post(`/api/leads/${parameters.accountId}/conversations/${parameters.conversationId}/details/${parameters.conversationDetailsId}/notify`, parameters.data)
  };
  sendSMSFromDealer (parameters) {
    return this.axios.post(`/api/leads/${parameters.accountId}/conversations/${parameters.conversationId}/text`, parameters.message)
  };
  sendEmailFromDealer (accountId, conversationId, email) {
    return this.axios.post(`/api/leads/${accountId}/conversations/${conversationId}/email`, email)
  };
  updateConversationUser (parameters) {
    return this.axios.post(`/api/leads/${parameters.accountId}/conversations/${parameters.conversationId}/user/update`, parameters.user)
  };
  deleteConversation (accountId, conversationId) {
    return this.axios.post(`/api/leads/${accountId}/conversations/${conversationId}/delete`)
  };
  archiveConversationDetails (accountId, conversationId, conversationDetailsId) {
    return this.axios.post(`/api/leads/${accountId}/conversations/${conversationId}/details/${conversationDetailsId}/archive`)
  };
  deleteConversationDetailsFromArchive (accountId, conversationId, conversationDetailsId) {
    return this.axios.post(`/api/leads/${accountId}/conversations/archive/${conversationId}/details/${conversationDetailsId}/delete`)
  };
}

export default new ConversationService()
