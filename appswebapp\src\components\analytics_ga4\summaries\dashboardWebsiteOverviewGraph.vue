<template>
  <b-card no-body class="w-100 mb-3">
    <b-card-header header-tag="h5" class="with-elements border-0 pl-3">
      <div class="card-header-title">Website Overview</div>
      <div class="card-header-elements ml-auto">
        <b-dropdown right variant="outline-secondary icon-btn btn-round md-btn-flat" size="sm" no-caret>
          <template slot="button-content">
            <i class="ion ion-ios-more m-0"></i><span class="sr-only">Actions</span>
          </template>
          <b-dropdown-header>Filters</b-dropdown-header>
          <b-dropdown-item
            :active="isDeviceScopeActive('all')"
            @click="onDeviceScopeChanged('all')"
            class="d-flex">
            All <i class="ion ion-ios-apps opacity-50 align-self-center ml-auto"></i>
          </b-dropdown-item>
          <b-dropdown-item
            :active="isDeviceScopeActive('desktop')"
            @click="onDeviceScopeChanged('desktop')"
            class="d-flex">
            Desktop <i class="ion ion-ios-laptop opacity-50 align-self-center ml-auto"></i>
          </b-dropdown-item>
          <b-dropdown-item
            :active="isDeviceScopeActive('mobile')"
            @click="onDeviceScopeChanged('mobile')"
            class="d-flex">
            Mobile <i class="ion ion-ios-phone-portrait opacity-50 align-self-center ml-auto mr-1"></i>
          </b-dropdown-item>
          <b-dropdown-divider></b-dropdown-divider>
          <div class="my-2 mx-3">
            <analytics-router-link :to="websiteOverviewReportLinkName" class="w-100 btn d-block btn-outline-secondary btn-sm">Overview Details</analytics-router-link>
          </div>
        </b-dropdown>
      </div>
    </b-card-header>
    <div>
      <vue-echart :options="sessionsTimelineOptions" :auto-resize="true"></vue-echart>
    </div>
  </b-card>
</template>

<script>
import dateHelper from '@/plugins/locale/date'

import 'echarts/lib/chart/line'
import 'echarts/lib/component/tooltip'
import 'echarts/lib/component/legend'
import VueEchart from 'vue-echarts/components/ECharts.vue'
import AnalyticsRouterLink from '../analyticsRouterLink'

const colors = ['#dc3545', '#007bff']

export default {
  name: 'dashboard-website-overview-graph',
  props: {
    mobileTimelineItems: { type: Array, required: true },
    desktopTimelineItems: { type: Array, required: true },
    barTimeFormat: { type: String, required: true },
    accountId: Number,
    reportGroupId: String
  },
  components: {
    AnalyticsRouterLink,
    VueEchart
  },
  data () {
    return {
      isMobileActive: true,
      isDesktopActive: true
    }
  },
  computed: {
    websiteOverviewReportLinkName () {
      if (this.accountId) {
        return { name: 'ga4WebsiteOverview', params: { accountId: this.accountId } }
      } else if (this.reportGroupId) {
        return { name: 'g4GroupWebsiteOverview', params: { reportGroupId: this.reportGroupId } }
      }
      return ''
    },
    sessionsTimelineOptions () {
      return {
        grid: {
          left: '50px',
          top: '20px',
          right: '0',
          bottom: '30px'
        },
        color: colors,
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            let tooltip = params[0].name

            params.forEach(x => {
              tooltip += '<br />' +
                  `${x.marker}${x.seriesName}: ${this.$locale.formatNumber(x.value)}`
            })

            return tooltip
          },
          textStyle: {
            fontSize: 13
          }
        },
        xAxis: {
          data: this.mobileTimelineItems.map(x => {
            return dateHelper.getDayFormatted(x.dateFrom, this.barTimeFormat)
          }),
          show: true,
          boundaryGap: false,
          axisLine: {
            show: false,
            lineStyle: { color: 'rgba(0, 0, 0, .5)' }
          },
          axisTick: {
            show: false
          },
          axisLabel: { color: 'rgba(0, 0, 0, .5)' }
        },
        yAxis: {
          show: true,
          boundaryGap: false,
          splitLine: { show: false },
          axisLine: {
            show: false,
            lineStyle: { color: 'rgba(0, 0, 0, .5)' }
          },
          axisTick: {
            show: false
          },
          axisLabel: { color: 'rgba(0, 0, 0, .5)' }
        },
        series: [
          {
            name: 'Desktop',
            type: 'line',
            stack: 'referrals',
            data: this.isDesktopActive
              ? this.desktopTimelineItems.map(x => x.sessions)
              : [],
            areaStyle: {},
            smooth: 0.4,
            symbolSize: 7,
            showSymbol: false
          },
          {
            name: 'Mobile',
            type: 'line',
            stack: 'referrals',
            data: this.isMobileActive
              ? this.mobileTimelineItems.map(x => x.sessions)
              : [],
            areaStyle: {},
            smooth: 0.4,
            symbolSize: 7,
            showSymbol: false
          }
        ],
        animationDuration: 2000
      }
    }
  },
  methods: {
    isDeviceScopeActive (scope) {
      let currentScope = ''
      if (this.isMobileActive && this.isDesktopActive) {
        currentScope = 'all'
      } else if (this.isMobileActive) {
        currentScope = 'mobile'
      } else if (this.isDesktopActive) {
        currentScope = 'desktop'
      }

      return currentScope === scope
    },
    async onDeviceScopeChanged (newScope) {
      this.isMobileActive = false
      this.isDesktopActive = false

      switch (newScope) {
        case 'all':
          this.isMobileActive = true
          this.isDesktopActive = true
          break
        case 'mobile':
          this.isMobileActive = true
          break
        case 'desktop':
          this.isDesktopActive = true
          break
      }
    }
  }
}
</script>

<style scoped>

</style>
