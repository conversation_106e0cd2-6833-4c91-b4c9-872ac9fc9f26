import axios from 'axios'
import craigslistConstants from './../../shared/craigslist/constants'
import commonConstants from './../../shared/common/constants'
import moment from 'moment'
import {ObjectSchema} from './../../shared/common/objectHelpers'
import QueryStringHelper from './../../shared/common/queryStringHelper'
import { getField, updateField } from 'vuex-map-fields'

const defaultValues = new ObjectSchema({
  postingType: { type: Number, default: craigslistConstants.craigslistPostingTypes.default.value }
})
let queryHelper = new QueryStringHelper(defaultValues)

export default {
  namespaced: true,
  state: {
    vehicle: null,
    craigslistAccountSettings: null,
    craigslistAreas: null,
    craigslistPostHistory: null,
    craigslistPostData: {},
    delayedJobData: null
  },
  getters: {
    vehicle: state => state.vehicle,
    craigslistAccountSettings: state => state.craigslistAccountSettings,
    craigslistAreas: state => state.craigslistAreas,
    craigslistPostHistory: state => state.craigslistPostHistory,
    getCraigslistPostData (state) {
      return getField(state.craigslistPostData)
    },
    craigslistPostData: state => state.craigslistPostData
  },
  mutations: {
    setCraigslistBaseInformation (state, data) {
      state.vehicle = data.vehicle
      state.craigslistAccountSettings = data.craigslistAccountSettings
      state.craigslistPostHistory = data.postHistory
    },
    setCraigslistDelayedJobBaseInformation (state, data) {
      state.vehicle = data.vehicle
      state.craigslistAccountSettings = data.craigslistAccountSettings
      state.delayedJobData = data.delayedJob
    },
    setCraigslistAreas (state, data) {
      state.craigslistAreas = data
    },
    initializeCraigslistPostData (state, router) {
      if (!state.vehicle || !state.craigslistAreas) {
        return
      }

      const area = state.craigslistAreas && state.craigslistAreas.length > 0 ? state.craigslistAreas[0] : null
      const subAreaCode = area && area.subAreas && area.subAreas.length > 0 ? area.subAreas[0].key : ''

      let postingTitle = `${state.vehicle.year} *${state.vehicle.make}* *${state.vehicle.model}`
      if (state.vehicle.trim) {
        postingTitle += ` *${state.vehicle.trim}*`
      }
      if (state.craigslistAccountSettings.hasToIncludeExteriorColor) {
        postingTitle += ` ${state.vehicle.exteriorColorLabel}`
      }

      const filters = queryHelper.parseQueryStringToObject(router)

      state.craigslistPostData = {
        accountId: state.vehicle.accountId,
        vin: state.vehicle.vin,
        price: state.vehicle.price,
        postingTitle: postingTitle,
        specificLocation: state.craigslistAccountSettings.craigslistSpecificLocation,
        areaCode: area.area,
        subAreaCode: subAreaCode,
        categoryType: craigslistConstants.craigslistCategoryTypes.default.value,
        postingType: filters.postingType,
        postingDate: moment().format('MM/DD/YYYY'),
        postingTime: commonConstants.hoursOptions[0]
      }
    },
    initializeCraigslistPostDataDelayedJob (state) {
      state.craigslistPostData.postingDate = moment(state.delayedJobData.dateTimeToPost).format('MM/DD/YYYY')
      state.craigslistPostData.postingTime = moment(state.delayedJobData.dateTimeToPost).format('h A')
      state.craigslistPostData.postingType = 1
      const area = state.craigslistAreas && state.craigslistAreas.length > 0 ? state.craigslistAreas.find(x => x.area === state.delayedJobData.area) : null
      const subAreaCode = area && area.subAreas && area.subAreas.length > 0 ? area.subAreas.find(x => x.key === state.delayedJobData.subArea).key : ''
      state.craigslistPostData.areaCode = area.area
      state.craigslistPostData.subAreaCode = subAreaCode
      state.craigslistPostData.categoryType = state.delayedJobData.craigslistPostingCategoryType
    },
    updateCraigslistPostData (state, field) {
      updateField(state.craigslistPostData, field)
    }
  },
  actions: {
    async populateCraigslistPostData ({ commit }, parameters) {
      const result = await axios.get(`/api/craigslist/inventory/${parameters.accountId}/${parameters.vin}/post`)

      commit('setCraigslistBaseInformation', result.data.model)

      return result.data
    },
    async populateCraigslistAreas ({ commit }, parameters) {
      const result = await axios.get(`/api/craigslist/inventory/${parameters.accountId}/areas`)

      commit('setCraigslistAreas', result.data.model)

      return result.data
    },
    async populateCraigslistPostDataByJobId ({ commit }, parameters) {
      const result = await axios.put(`/api/craigslist/inventory/${parameters.accountId}/updatemodal/${parameters.id}`)

      commit('setCraigslistDelayedJobBaseInformation', result.data.model)

      return result.data
    },
    async postVehicleToCraigslist ({ commit }, parameters) {
      const result = await axios.post('/api/craigslist/inventory/bulkpost', parameters.data)

      return result.data
    },
    async postUpdateDelayedJob ({ commit }, parameters) {
      const result = await axios.put(`/api/craigslist/inventory/${parameters.accountId}/updatejob/${parameters.id}`, parameters.data)

      return result.data
    }
  }
}
