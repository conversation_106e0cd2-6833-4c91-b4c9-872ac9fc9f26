<template>
  <div class="area" contenteditable="true" @keydown='insertTabAtCaret' @blur="updateHTML"></div>
</template>

<script>
export default {
  name: 'html-textarea',
  props: {
    value: { type: String }
  },
  mounted () {
    this.$el.innerHTML = this.value
  },
  methods: {
    updateHTML (e) {
      this.$emit('input', e.target.innerHTML)
    },
    insertTabAtCaret (event) {
      if (event.keyCode === 9) {
        event.preventDefault()
        let range = window.getSelection().getRangeAt(0)

        let tabNode = document.createTextNode('\u00a0\u00a0\u00a0\u00a0\u00a0\u00a0')
        range.insertNode(tabNode)

        range.setStartAfter(tabNode)
        range.setEndAfter(tabNode)
      }
      if (event.keyCode === 13) {
        event.preventDefault()
        let range = window.getSelection().getRangeAt(0)

        let tabNode = document.createTextNode('\n\u00a0\u00a0\u00a0\u00a0\u00a0\u00a0')
        range.insertNode(tabNode)

        range.setStartAfter(tabNode)
        range.setEndAfter(tabNode)
      }
    }
  },
  watch: {
    value (val) {
      this.$el.innerHTML = val
    }
  }
}
</script>

<style>
.area{
  padding: 15px;
}
pre{
  font-size: 14.5px;
}
</style>
