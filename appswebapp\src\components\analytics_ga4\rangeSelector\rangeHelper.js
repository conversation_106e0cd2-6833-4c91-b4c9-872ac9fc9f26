import moment from 'moment/moment'
import {NamedRange, NamedRangeSet} from '../../_shared/dateRangeSelector/rangeModels'
import analyticsConstants from '@/shared/analytics/constants'

let defaultRangeName = new Date().getDate() === 1
  ? 'Last Month'
  : 'This Month'

const predefinedRanges = new NamedRangeSet(defaultRangeName, 'All time')

predefinedRanges.add(new NamedRange('All time', [moment(analyticsConstants.beginOfTime), moment()]))
predefinedRanges.add(new NamedRange('Last Month', [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]))
predefinedRanges.add(new NamedRange('Last 30 Days', [moment().subtract(30, 'days'), moment().subtract(1, 'days')]))

if (moment().date() !== 1) {
  predefinedRanges.add(new NamedRange('This Month', [moment().startOf('month'), moment().subtract(1, 'days')]))
}

predefinedRanges.add(new NamedRange('Last 14 Days', [moment().subtract(14, 'days'), moment().subtract(1, 'days')]))

predefinedRanges.add(new NamedRange('Last Business week',
  moment().day() <= 5
    ? [moment().subtract(1, 'week').startOf('isoWeek'), moment().subtract(1, 'week').endOf('week').subtract(1, 'days')]
    : [moment().startOf('week'), moment().endOf('week').subtract(1, 'days')]
))

predefinedRanges.add(new NamedRange('Last week (Mon - Sun)', [moment().subtract(1, 'week').startOf('isoWeek'), moment().subtract(1, 'week').endOf('isoWeek')]))
predefinedRanges.add(new NamedRange('Last week (Sun - Sat)', [moment().subtract(1, 'week').startOf('week'), moment().subtract(1, 'week').endOf('week')]))
predefinedRanges.add(new NamedRange('Last 7 Days', [moment().subtract(7, 'days'), moment().subtract(1, 'days')]))

if (moment().day() > 2) {
  predefinedRanges.add(new NamedRange('This week (Mon - Yesterday)', [moment().startOf('isoWeek'), moment().subtract(1, 'days')]))
}

if (moment().day() !== 7 && moment().day() !== 1) {
  predefinedRanges.add(new NamedRange('This week (Sun - Yesterday)', [moment().startOf('week'), moment().subtract(1, 'days')]))
}

predefinedRanges.add(new NamedRange('Yesterday', [moment().subtract(1, 'days'), moment().subtract(1, 'days')]))

export default predefinedRanges
