import BaseService from '../BaseService'

class IntegrityReportService extends BaseService {
  getMissedTwilioPhones (filter) {
    return this.axios.get('/api/leads/integrity_report/missed_twilio_phones', { params: filter })
  };
  getMissedTwilioCampaigns (filter) {
    return this.axios.get('/api/leads/integrity_report/missed_twilio_campaigns', { params: filter })
  };
  getMissedTwilioConversations (filter) {
    return this.axios.get('/api/leads/integrity_report/missed_twilio_conversations', { params: filter })
  };
  getNotMatchedEmails (filter) {
    return this.axios.get('/api/leads/integrity_report/not_matched_emails', { params: filter })
  };
  sendToReprocessNotMatchedEmail (id) {
    return this.axios.post(`/api/leads/integrity_report/not_matched_emails/${id}/reprocess`)
  }
  startReportProcessing () {
    return this.axios.post('/api/leads/integrity_report/run')
  };
  deleteReportItem (id) {
    return this.axios.post(`/api/leads/integrity_report/${id}/delete`)
  };
}

export default new IntegrityReportService()
