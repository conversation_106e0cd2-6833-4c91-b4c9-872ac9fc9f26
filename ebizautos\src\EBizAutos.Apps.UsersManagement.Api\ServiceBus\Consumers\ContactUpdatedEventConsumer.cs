﻿using EBizAutos.Apps.ServiceBus.Events.Contact;
using EBizAutos.Apps.UsersManagement.Api.ServiceBus.Handlers;
using EBizAutos.CommonLib.Exceptions;

namespace EBizAutos.Apps.UsersManagement.Api.ServiceBus.Consumers {
	internal class ContactUpdatedEventConsumer : BaseEventConsumer<IContactUpdatedEvent> {
		public ContactUpdatedEventConsumer(IEventHandler<IContactUpdatedEvent, bool> eventHandler, ExceptionHandler exceptionHandler) : base(eventHandler, exceptionHandler) {
		}
	}
}