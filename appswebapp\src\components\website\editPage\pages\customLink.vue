<template>
  <div>
    <page-status-section :page-edit-model="pageEditModel"/>
    <div class="mt-2">
      <div class="border-bottom">
        <b-row>
          <b-col xs="12" sm="6" md="6" lg="6" xl="6" class="m-0"><h6 class="float-left">Custom Link Settings</h6></b-col>
        </b-row>
      </div>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Link Target:</span>
        <b-form-select v-model="pageEditModel.navigationSettings.isTargetBlank" :options="getLinkTargetOptions" slot="payload"></b-form-select>
      </detail-row>
      <detail-row :large-payload-width="true">
        <span slot="title">Custom Link:</span>
        <b-form-input v-model="pageEditModel.navigationSettings.externalUrl" type="url" slot="payload"></b-form-input>
      </detail-row>
    </div>
  </div>
</template>

<script>
import pageStatusSection from '../sections/pageStatusSection'
import textHtmlContentSection from '../sections/textHtmlContentSection'
import detailRow from '@/components/details/helpers/detailRow'

export default {
  props: {
    pageEditModel: {type: Object, required: true}
  },
  computed: {
    getLinkTargetOptions () {
      return [
        {
          value: false,
          text: 'Same Window'
        },
        {
          value: true,
          text: 'New Window'
        }
      ]
    }
  },
  components: {
    pageStatusSection,
    textHtmlContentSection,
    detailRow
  }
}
</script>
