<template>
<div class="d-flex justify-content-center">
  <div v-if="type === 'plane'" class="sk-plane" :class="`sk-plane-${size}`"></div>
  <div v-else-if="type === 'chase'" class="sk-chase" :class="`sk-chase-${size}`">
    <div class="sk-chase-dot"></div>
    <div class="sk-chase-dot"></div>
    <div class="sk-chase-dot"></div>
    <div class="sk-chase-dot"></div>
    <div class="sk-chase-dot"></div>
    <div class="sk-chase-dot"></div>
  </div>
  <div v-else-if="type === 'bounce'" class="sk-bounce" :class="`sk-bounce-${size}`">
    <div class="sk-bounce-dot"></div>
    <div class="sk-bounce-dot"></div>
  </div>
  <div v-else-if="type === 'wave'" class="sk-wave" :class="`sk-wave-${size}`">
    <div class="sk-wave-rect"></div>
    <div class="sk-wave-rect"></div>
    <div class="sk-wave-rect"></div>
    <div class="sk-wave-rect"></div>
    <div class="sk-wave-rect"></div>
  </div>
  <div v-else-if="type === 'pulse'" class="sk-pulse" :class="`sk-pulse-${size}`"></div>
  <div v-else-if="type === 'flow'" class="sk-flow" :class="`sk-flow-${size}`">
    <div class="sk-flow-dot"></div>
    <div class="sk-flow-dot"></div>
    <div class="sk-flow-dot"></div>
  </div>
  <div v-else-if="type === 'swing'" class="sk-swing" :class="`sk-swing-${size}`">
    <div class="sk-swing-dot"></div>
    <div class="sk-swing-dot"></div>
  </div>
  <div v-else-if="type === 'circle'" class="sk-circle" :class="`sk-circle-${size}`">
    <div class="sk-circle-dot"></div>
    <div class="sk-circle-dot"></div>
    <div class="sk-circle-dot"></div>
    <div class="sk-circle-dot"></div>
    <div class="sk-circle-dot"></div>
    <div class="sk-circle-dot"></div>
    <div class="sk-circle-dot"></div>
    <div class="sk-circle-dot"></div>
    <div class="sk-circle-dot"></div>
    <div class="sk-circle-dot"></div>
    <div class="sk-circle-dot"></div>
    <div class="sk-circle-dot"></div>
  </div>
  <div v-else-if="type === 'circle-fade'" class="sk-circle-fade" :class="`sk-circle-fade-${size}`">
    <div class="sk-circle-fade-dot"></div>
    <div class="sk-circle-fade-dot"></div>
    <div class="sk-circle-fade-dot"></div>
    <div class="sk-circle-fade-dot"></div>
    <div class="sk-circle-fade-dot"></div>
    <div class="sk-circle-fade-dot"></div>
    <div class="sk-circle-fade-dot"></div>
    <div class="sk-circle-fade-dot"></div>
    <div class="sk-circle-fade-dot"></div>
    <div class="sk-circle-fade-dot"></div>
    <div class="sk-circle-fade-dot"></div>
    <div class="sk-circle-fade-dot"></div>
  </div>
  <div v-else-if="type === 'grid'" class="sk-grid" :class="`sk-grid-${size}`">
    <div class="sk-grid-cube"></div>
    <div class="sk-grid-cube"></div>
    <div class="sk-grid-cube"></div>
    <div class="sk-grid-cube"></div>
    <div class="sk-grid-cube"></div>
    <div class="sk-grid-cube"></div>
    <div class="sk-grid-cube"></div>
    <div class="sk-grid-cube"></div>
    <div class="sk-grid-cube"></div>
  </div>
  <div v-else-if="type === 'fold'" class="sk-fold" :class="`sk-fold-${size}`">
    <div class="sk-fold-cube"></div>
    <div class="sk-fold-cube"></div>
    <div class="sk-fold-cube"></div>
    <div class="sk-fold-cube"></div>
  </div>
  <div v-else-if="type === 'wander'" class="sk-wander" :class="`sk-wander-${size}`">
    <div class="sk-wander-cube"></div>
    <div class="sk-wander-cube"></div>
    <div class="sk-wander-cube"></div>
    <div class="sk-wander-cube"></div>
  </div>
</div>
</template>

<script>
export default {
  name: 'loader',
  props: {
    size: { type: String, default: 'sm' },
    type: { type: String, default: 'chase' }
  }
}
</script>

<style>
</style>
