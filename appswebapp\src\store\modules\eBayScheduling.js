import axios from 'axios'

const actions = {
  getScheduledVehicle (_, parameters) {
    return axios.get(`/api/ebay/${parameters.accountId}/scheduling/${parameters.vin}/vehicle`)
  },
  getScheduledItem (_, parameters) {
    return axios.get(`/api/ebay/${parameters.accountId}/scheduling/${parameters.vin}/item`)
  },
  getEBayCategories (_, parameters) {
    return axios.get(`/api/ebay/${parameters.accountId}/scheduling/categories`, { params: parameters.filter })
  },
  getEBayTopCategories (_, parameters) {
    return axios.get(`/api/ebay/${parameters.accountId}/scheduling/topcategories`, { params: parameters.filter })
  },
  checkScheduledEnabled (_, parameters) {
    return axios.get(`/api/ebay/${parameters.accountId}/scheduling/enabled`)
  },
  scheduleItem (_, parameters) {
    return axios.post(`/api/ebay/${parameters.accountId}/scheduling/${parameters.vin}/item/schedule`, parameters.data)
  },
  unscheduleItem (_, parameters) {
    return axios.post(`/api/ebay/${parameters.accountId}/scheduling/${parameters.vin}/item/unschedule`)
  }
}

export default {
  namespaced: true,
  actions: actions
}
