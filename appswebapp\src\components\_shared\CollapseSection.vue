<template>
  <div class="collapse-section">
    <section class="collapse-section_header collapse-control">
      <span class="collapse-control_title" @click="toggleVisibility">{{title}}</span>
      <show-hide-helper :visible="isVisible" variant="arrow" @visibilityChange="onVisibilityChange"/>
    </section>
    <b-collapse :visible="isVisible" class="collapse-section_body" :id="id">
      <slot></slot>
    </b-collapse>
  </div>
</template>

<script>
import showHideHelper from '../details/helpers/showHideHelper'

export default {
  name: 'collapseSection',
  props: {
    title: String,
    visible: Boolean,
    id: {
      required: false,
      type: String,
      default () {
        return this.$uuid.v4()
      }
    }
  },
  data () {
    return {
      isVisible: this.visible
    }
  },
  methods: {
    onVisibilityChange (val) {
      this.isVisible = val
    },
    toggleVisibility () {
      this.isVisible = !this.isVisible
    }
  },
  components: {
    'show-hide-helper': showHideHelper
  }
}
</script>

<style scoped lang="scss">
 .collapse-section_header {
   display: flex;
   justify-content: space-between;

   padding-bottom: 10px;
   margin-bottom: 5px;
   border-bottom: 1px solid #eee;

   .collapse-control_title {
     cursor: pointer;
   }
 }
</style>
