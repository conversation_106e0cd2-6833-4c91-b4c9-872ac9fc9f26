<template>
  <div class="position-relative">
    <b-row class="mb-2">
      <b-col>
        <b-btn v-if="hasUserAccessToUserDisplaySettings" class="float-right" @click="showUserSettingsModal" size="sm" variant="primary"><span class="ion ion-ios-settings"></span> Settings</b-btn>
      </b-col>
    </b-row>
    <b-card class="bg-light mb-3" v-if="hasUserFullAccess">
      <b-row>
        <b-col class="py-1" xl="6" lg="6" md="7" sm="12" xs="12">
          <b-input-group>
            <b-form-input list="user-sync-login-datalist" @input.native="onInputSyncUserLogin" v-model="synchronizeUserFilter.userName" autocomplete="off" placeholder="User Login"></b-form-input>
            <b-input-group-append>
              <l-button :loading="isSynchronizeUserLoading"
                @click="synchronizeUser"
              >
                Synchronize User
              </l-button>
            </b-input-group-append>
          </b-input-group>
          <datalist id="user-sync-login-datalist">
            <option v-for="(opt, i) in syncUsersSuggestions" :key="`user-sync-login-datalist-${i}-option`" :value="opt.value">{{opt.text}}</option>
          </datalist>
        </b-col>
        <b-col class="py-1" xl="6" lg="6" md="5" sm="12" xs="12">
          <l-button v-if="hasFullAccess" :loading="isSynchronizeAllUserLoading" @click="synchronizeAllUser" variant="primary">Synchronize All Users</l-button>
        </b-col>
      </b-row>
      <b-row class="mt-1">
        <b-col>
          <b-form-group>
            <b-form-radio-group
              id="user-sync-types-group"
              v-model="synchronizeUserFilter.userType"
              :options="getUserSynchronizeTypeOptions"
              name="user-type-options"
            ></b-form-radio-group>
          </b-form-group>
        </b-col>
      </b-row>
    </b-card>
    <b-row>
      <b-col class="py-2 align-middle">
        <h4>
          Users Management
          <b-dropdown v-if="hasFullAccess" variant="primary btn-round" size="sm">
            <template slot="button-content">
              <span class="ion ion-ios-add"></span><span class="d-none d-md-inline">&nbsp; Add New User</span>
            </template>
            <b-dropdown-item @click="onAddNewUser(userTypes.ebizServiceUser.value)">Add Service User</b-dropdown-item>
          </b-dropdown>
        </h4>
      </b-col>
      <b-col>
        <paging
          class="d-none d-md-block p-0"
          :pageNumber="filters.page"
          :pageSize="filters.pageSize"
          :totalItems="totalCount"
          @numberChanged="pageChanged"
          @changePageSize="changePageSize"
        />
      </b-col>
    </b-row>
    <b-card v-if="hasUserFullAccess">
      <b-row>
        <b-col xl="6" lg="7" md="8" sm="12" xs="12">
          <b-input-group>
            <b-form-input v-model="filters.search" @input.native="onInputUserSearchFilter" list="user-search-datalist" name="search" autocomplete="off" placeholder="Search User"></b-form-input>
            <b-input-group-append>
              <b-btn variant="primary" @click="applySearch">Search</b-btn>
            </b-input-group-append>
          </b-input-group>
          <datalist id="user-search-datalist">
            <option v-for="(opt, i) in searchUsersSuggestions" :key="`user-search-datalist-${i}-option`" :value="opt.value">{{opt.text}}</option>
          </datalist>
        </b-col>
      </b-row>
      <b-row class="mt-2">
        <b-col>
          <b-form-group>
            <b-form-radio-group
              id="user-types-filter"
              @input="onChangeUserType"
              v-model="filters.userType"
              :options="getUserTypeOptions"
              name="user-type-filter-option"
              :disabled="isLoading"
            ></b-form-radio-group>
          </b-form-group>
        </b-col>
      </b-row>
    </b-card>
    <b-card v-if="!isLoading && items && items.length > 0">
      <b-table
        :items="items"
        :fields="getTableFields"
        striped
        hover
        responsive
      >
        <template #cell(manage)="data">
          <div class="d-flex flex-column">
            <b-btn class="text-center" @click="onEditUser(data.item)" size="sm">Edit</b-btn>
            <b-btn v-if="(hasUserFullAccess && data.item.userType === userTypes.cpUser.value) || hasFullAccess" @click="onDeleteUser(data.item.id)" class="text-center mt-2" size="sm" variant="danger">Delete</b-btn>
          </div>
        </template>
      </b-table>
      <paging
        v-if="totalCount > 0"
        class="p-0"
        :pageNumber="filters.page"
        :pageSize="filters.pageSize"
        :totalItems="totalCount"
        titled
        pageSizeSelector
        @numberChanged="pageChanged"
        @changePageSize="changePageSize"
      />
    </b-card>
    <div v-else-if="isLoading" class="py-5">
      <loader size="lg"/>
    </div>
    <span v-else class="text-muted">
      Not Found
    </span>
    <userSettingsModal v-if="hasUserAccessToUserDisplaySettings"
      :visible="isUserSettingsModalVisible"
      @hide="hideUserSettingsModal"
    >
    </userSettingsModal>
    <userManageModal
      v-if="isUserManageModalVisible"
      :visible="isUserManageModalVisible"
      :isEditMode="isUserManageEditMode"
      :userToManage="userInfoToManage"
      @hide="hideUserManageModal"
      @submit="submitUser"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import permissions from '@/shared/common/permissions'
import paging from '@/components/_shared/paging'
import loader from '@/components/_shared/loader'
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import {userTypes, syncUserStatus} from '@/shared/users/constants'
import UserManagementService from '@/services/users/UserManagementService'
import userSettingsModal from '@/components/users/userSettingsModal'
import userManageModal from '@/components/users/userManageModal'

const defaultValues = new ObjectSchema({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  search: { type: String, default: '' },
  userType: { type: Number, default: 1 }
})

const queryHelper = new QueryStringHelper(defaultValues)

export default {
  name: 'users',
  metaInfo: {
    title: 'Users Management'
  },
  data () {
    return {
      synchronizeUserFilter: {
        userType: userTypes.cpUser.value,
        userName: ''
      },
      isSynchronizeUserLoading: false,
      isSynchronizeAllUserLoading: false,
      isUserSettingsModalVisible: false,
      isUserManageModalVisible: false,
      isUserManageEditMode: false,
      isLoading: true,
      userTypes,
      filters: defaultValues.getObject(),
      items: [],
      totalCount: 0,
      userInfoToManage: {},
      searchUsersSuggestions: [],
      syncUsersSuggestions: []
    }
  },
  components: {
    paging,
    loader,
    userSettingsModal,
    userManageModal
  },
  created () {
    this.filters = queryHelper.parseQueryStringToObject(this.$router)
    this.populateData()
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {
        canManageAccountApplicationType: () => false
      }
    },
    hasUserAccessToUserDisplaySettings () {
      return this.user && this.user.hasPermissions && this.user.hasPermissions(permissions.ManageMultipleUserAccounts)
    },
    hasUserFullAccess () {
      return this.user && this.user.hasPermissions && this.user.hasPermissions(permissions.UsersFullAccess)
    },
    hasFullAccess () {
      return this.user && this.user.hasPermissions && this.user.hasPermissions(permissions.FullAccess)
    },
    getUserTypeOptions () {
      let options = [userTypes.cpUser]
      if (this.hasUserFullAccess) {
        options.push(userTypes.adminUser)
      }
      if (this.hasFullAccess) {
        options.push(userTypes.ebizServiceUser)
      }

      return options
    },
    getUserSynchronizeTypeOptions () {
      let options = [userTypes.cpUser]
      if (this.hasUserFullAccess) {
        options.push(userTypes.adminUser)
      }

      return options
    },
    getTableFields () {
      return [
        {
          key: 'userName',
          label: 'User Name',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'firstName',
          label: 'First Name',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'lastName',
          label: 'Last Name',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle'
        }
      ]
    }
  },
  methods: {
    applySearch () {
      this.filters.page = 1
      this.synchronizeUrlAndReload()
    },
    pageChanged (newPage) {
      this.filters.page = newPage
      this.isLoading = true
      this.synchronizeUrlAndReload()
    },
    changePageSize (newPageSize) {
      this.filters.pageSize = newPageSize
      this.filters.page = 1
      this.isLoading = true
      this.synchronizeUrlAndReload()
    },
    onChangeUserType () {
      this.isLoading = true
      this.filters.page = 1
      this.synchronizeUrlAndReload()
      this.onInputUserSearchFilter()
    },
    synchronizeUrlAndReload () {
      queryHelper.rebuildParamsInQueryString(this.$router, this.filters)
      this.populateData()
    },
    populateData () {
      UserManagementService.getUsersListing(this.filters).then(res => {
        this.items = res.data.items
        this.totalCount = res.data.totalCount
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
      }).finally(() => {
        this.isLoading = false
      })
    },
    showUserSettingsModal () {
      this.isUserSettingsModalVisible = true
    },
    hideUserSettingsModal () {
      this.isUserSettingsModalVisible = false
    },
    onEditUser (item) {
      this.userInfoToManage = item
      this.isUserManageEditMode = true
      this.isUserManageModalVisible = true
    },
    onAddNewUser (userType) {
      this.isUserManageEditMode = false
      this.userInfoToManage = { userType: userType, roles: [] }
      this.isUserManageModalVisible = true
    },
    hideUserManageModal () {
      this.isUserManageModalVisible = false
    },
    submitUser (userInfo) {
      if (this.isUserManageEditMode) {
        UserManagementService.updateUser(userInfo.id, userInfo).then(res => {
          this.$toaster.success('Updated User Successfully')
        }).catch(ex => {
          this.$toaster.exception(ex, 'Something went wrong!')
        }).finally(() => {
          this.populateData()
          this.hideUserManageModal()
        })
      } else {
        UserManagementService.createUser(userInfo).then(res => {
          this.$toaster.success('Created User Successfully')
        }).catch(ex => {
          this.$toaster.exception(ex, 'Something went wrong!')
        }).finally(() => {
          this.populateData()
          this.hideUserManageModal()
        })
      }
    },
    onDeleteUser (id) {
      UserManagementService.deleteUser(id).then(res => {
        this.$toaster.success('Delete User Successfully')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
      }).finally(() => {
        this.populateData()
      })
    },
    synchronizeUser () {
      if (!this.synchronizeUserFilter.userName || !this.synchronizeUserFilter.userName.trim()) {
        this.$toaster.error('User login is required for synchronization')
        return
      }
      UserManagementService.synchronizeUser(this.synchronizeUserFilter).then(res => {
        this.isSynchronizeUserLoading = true
        this.$toaster.success('Started Synchronize User Successfully')
        setTimeout(() => { this.checkSynchronizeUserStatus(res.data) })
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
      })
    },
    synchronizeAllUser () {
      UserManagementService.synchronizeAll(this.synchronizeUserFilter).then(res => {
        this.isSynchronizeAllUserLoading = true
        this.$toaster.success('Started Synchronize All Users Successfully')
        setTimeout(() => this.checkSynchronizeAllUserStatus(res.data), 5000)
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
      })
    },
    checkSynchronizeAllUserStatus (id) {
      UserManagementService.getSynchronizeStatus(id).then(res => {
        if (res.data === syncUserStatus.completed) {
          this.$toaster.success('Finished Synchronize All Users Successfully')
          this.isSynchronizeAllUserLoading = false
        } else if (res.data === syncUserStatus.failed) {
          this.$toaster.error('Failed on Synchronize All Users')
          this.isSynchronizeAllUserLoading = false
        } else if (res.data === syncUserStatus.notFound) {
          this.isSynchronizeAllUserLoading = false
          this.$toaster.warning('Task was not found')
        } else {
          setTimeout(() => this.checkSynchronizeAllUserStatus(id), 5000)
        }
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
        this.isSynchronizeAllUserLoading = false
      })
    },
    checkSynchronizeUserStatus (id) {
      UserManagementService.getSynchronizeStatus(id).then(res => {
        if (res.data === syncUserStatus.completed) {
          this.$toaster.success('Finished Synchronize User Successfully')
          this.isSynchronizeUserLoading = false
        } else if (res.data === syncUserStatus.failed) {
          this.$toaster.error('Failed on Synchronize User')
          this.isSynchronizeUserLoading = false
        } else if (res.data === syncUserStatus.notFound) {
          this.isSynchronizeUserLoading = false
          this.$toaster.warning('Task was not found')
        } else {
          setTimeout(() => this.checkSynchronizeUserStatus(id), 5000)
        }
      }).catch(ex => {
        this.isSynchronizeUserLoading = false
        this.$toaster.exception(ex, 'Something went wrong!')
      })
    },
    onInputSyncUserLogin () {
      if (!this.synchronizeUserFilter.userName || this.synchronizeUserFilter.userName.length < 2) {
        return
      }
      UserManagementService.getSynUsersSuggestions(this.synchronizeUserFilter).then(res => {
        if (Array.isArray(res.data)) {
          this.syncUsersSuggestions = res.data
        }
      }).catch(ex => {
        console.error(ex)
      })
    },
    onInputUserSearchFilter () {
      if (!this.filters.search || this.filters.search.length < 2) {
        return
      }
      UserManagementService.getSearchUsersSuggestions(this.filters).then(res => {
        if (Array.isArray(res.data)) {
          this.searchUsersSuggestions = res.data
        }
      }).catch(ex => {
        console.error(ex)
      })
    }
  }
}
</script>

<style scoped>
.custom-paging {
  position: absolute;
  right: 5px;
  top: 35px;
  z-index: 2;
}
</style>
