﻿using EBizAutos.Apps.ServiceBus.Events.User;
using EBizAutos.CommonLib.Exceptions;
using EBizAutos.Apps.AccountManagement.Api.ServiceBus.Handlers;

namespace EBizAutos.Apps.AccountManagement.Api.ServiceBus.Consumers {
	internal class UserUpdatedEventConsumer : BaseEventConsumer<IUserUpdatedEvent> {
		public UserUpdatedEventConsumer(IEventHandler<IUserUpdatedEvent, bool> eventHandler, ExceptionHandler exceptionHandler) : base(eventHandler, exceptionHandler) {
		}
	}
}