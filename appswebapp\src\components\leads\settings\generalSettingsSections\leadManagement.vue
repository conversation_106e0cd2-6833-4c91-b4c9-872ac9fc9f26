<template>
  <div class='mb-4'>
    <editSettingsHelper @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="Leads Management" :isLoading="isUpdatingProcessed" :isDisabled="isDisabled" :isViewMode="isViewMode">
      <div slot="settings-content">
        <detail-row fixedPayloadWidth>
          <span slot="title">Auto Archive Leads After:</span>
          <div slot="payload">
            <b-form-checkbox v-model='dealerAccountSettingsToUpdate.hasToArchiveLeadMessages' class='align-middle d-inline-flex' style='margin:0; padding-left: 20px;' :disabled='isViewMode'/>
            <b-form-input v-model='dealerAccountSettingsToUpdate.archiveLeadMessagesAfterInDays'  class='align-middle d-inline-flex' type="number" style='width:75px' :disabled='isViewMode || !dealerAccountSettingsToUpdate.hasToArchiveLeadMessages' />
            <span class='align-middle d-inline-flex'>Days</span>
          </div>
        </detail-row>
        <detail-row fixedPayloadWidth>
          <span slot="title">Delete Archived Leads After:</span>
          <div slot="payload">
            <b-form-checkbox v-model='dealerAccountSettingsToUpdate.hasToDeleteArchivedLeadMessages' class='align-middle d-inline-flex' style='margin:0; padding-left: 20px;' :disabled='isViewMode'/>
            <b-form-input v-model='dealerAccountSettingsToUpdate.deleteArchivedLeadMessagesAfterInDays' type="number" class='align-middle d-inline-flex' style='width:75px;' :disabled='isViewMode || !dealerAccountSettingsToUpdate.hasToDeleteArchivedLeadMessages'/>
            <span class='align-middle d-inline-flex'>Days</span>
          </div>
        </detail-row>
      </div>
    </editSettingsHelper>
  </div>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import { mapGetters } from 'vuex'
import globals from '../../../../globals'

export default {
  name: 'leads-management-settings',
  props: {
    isUpdatingProcessed: {
      type: Boolean,
      required: true
    },
    isDisabled: Boolean
  },
  data () {
    return {
      isViewMode: true,
      dealerAccountSettingsToUpdate: {}
    }
  },
  created () {
    this.initData()
  },
  components: {
    'detail-row': detailRow,
    editSettingsHelper: () => import('@/components/_shared/editSettingsHelper')
  },
  computed: {
    ...mapGetters('leadsAccountSettings', ['dealerAccountSettings'])
  },
  methods: {
    initData () {
      if (this.dealerAccountSettings) {
        this.dealerAccountSettingsToUpdate = globals().getClonedValue(this.dealerAccountSettings)
      }
    },
    saveSettings () {
      this.$emit('save', this.dealerAccountSettingsToUpdate)
      this.isViewMode = true
    },
    changeMode (mode) {
      this.isViewMode = mode
    },
    cancel () {
      this.initData()
      this.changeMode(true)
    }
  }
}
</script>
