{"format": 1, "restore": {"D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.MongoDbRepository\\EBizAutos.Apps.Authentication.MongoDbRepository.csproj": {}}, "projects": {"D:\\Work\\ebizautos\\src\\EBizAutos.ApplicationCommonLib\\EBizAutos.ApplicationCommonLib.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\ebizautos\\src\\EBizAutos.ApplicationCommonLib\\EBizAutos.ApplicationCommonLib.csproj", "projectName": "EBizAutos.ApplicationCommonLib", "projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.ApplicationCommonLib\\EBizAutos.ApplicationCommonLib.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\ebizautos\\src\\EBizAutos.ApplicationCommonLib\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net461", "net6.0", "netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\Work\\ebizautos\\packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\Work\\ebizautos\\src\\EBizAutos.CommonLib\\EBizAutos.CommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.CommonLib\\EBizAutos.CommonLib.csproj"}}}, "net461": {"targetAlias": "net461", "projectReferences": {"D:\\Work\\ebizautos\\src\\EBizAutos.CommonLib\\EBizAutos.CommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.CommonLib\\EBizAutos.CommonLib.csproj"}}}, "netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {"D:\\Work\\ebizautos\\src\\EBizAutos.CommonLib\\EBizAutos.CommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.CommonLib\\EBizAutos.CommonLib.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"AWSSDK.S3": {"target": "Package", "version": "[3.7.305.30, )"}, "Amazon.Extensions.Configuration.SystemsManager": {"target": "Package", "version": "[2.1.1, )"}, "Magick.NET-Q16-AnyCPU": {"target": "Package", "version": "[13.6.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.Configuration.EnvironmentVariables": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.FileExtensions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "System.Drawing.Common": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}, "net461": {"targetAlias": "net461", "dependencies": {"AWSSDK.S3": {"target": "Package", "version": "[3.7.104.13, )"}, "Amazon.Extensions.Configuration.SystemsManager": {"target": "Package", "version": "[2.1.1, )"}, "Magick.NET-Q16-AnyCPU": {"target": "Package", "version": "[7.11.0, )"}, "Microsoft.AspNetCore.Http.Extensions": {"target": "Package", "version": "[1.0.1, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[2.1.1, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[2.1.1, )"}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[2.1.1, )"}, "Microsoft.Extensions.Configuration.EnvironmentVariables": {"target": "Package", "version": "[2.1.1, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[2.1.1, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[2.1.1, )"}, "Microsoft.NETFramework.ReferenceAssemblies": {"suppressParent": "All", "target": "Package", "version": "[1.0.3, )", "autoReferenced": true}, "System.Drawing.Common": {"target": "Package", "version": "[4.5.1, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}, "netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"AWSSDK.S3": {"target": "Package", "version": "[3.7.104.13, )"}, "Amazon.Extensions.Configuration.SystemsManager": {"target": "Package", "version": "[2.1.1, )"}, "Magick.NET-Q16-AnyCPU": {"target": "Package", "version": "[7.11.0, )"}, "Microsoft.AspNetCore.Http.Extensions": {"target": "Package", "version": "[2.1.1, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[2.1.1, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[2.1.1, )"}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[2.1.1, )"}, "Microsoft.Extensions.Configuration.EnvironmentVariables": {"target": "Package", "version": "[2.1.1, )"}, "Microsoft.Extensions.Configuration.FileExtensions": {"target": "Package", "version": "[2.1.1, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[2.1.1, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[2.1.1, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "System.Drawing.Common": {"target": "Package", "version": "[4.5.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.CommonLib\\EBizAutos.Apps.Authentication.CommonLib.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.CommonLib\\EBizAutos.Apps.Authentication.CommonLib.csproj", "projectName": "EBizAutos.Apps.Authentication.CommonLib", "projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.CommonLib\\EBizAutos.Apps.Authentication.CommonLib.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.CommonLib\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net462", "net6.0", "netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\Work\\ebizautos\\packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\Work\\ebizautos\\src\\EBizAutos.ApplicationCommonLib\\EBizAutos.ApplicationCommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.ApplicationCommonLib\\EBizAutos.ApplicationCommonLib.csproj"}, "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.CommonLib\\EBizAutos.Apps.CommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.CommonLib\\EBizAutos.Apps.CommonLib.csproj"}, "D:\\Work\\ebizautos\\src\\EBizAutos.CommonLib\\EBizAutos.CommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.CommonLib\\EBizAutos.CommonLib.csproj"}}}, "net462": {"targetAlias": "net462", "projectReferences": {"D:\\Work\\ebizautos\\src\\EBizAutos.ApplicationCommonLib\\EBizAutos.ApplicationCommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.ApplicationCommonLib\\EBizAutos.ApplicationCommonLib.csproj"}, "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.CommonLib\\EBizAutos.Apps.CommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.CommonLib\\EBizAutos.Apps.CommonLib.csproj"}, "D:\\Work\\ebizautos\\src\\EBizAutos.CommonLib\\EBizAutos.CommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.CommonLib\\EBizAutos.CommonLib.csproj"}}}, "netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {"D:\\Work\\ebizautos\\src\\EBizAutos.ApplicationCommonLib\\EBizAutos.ApplicationCommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.ApplicationCommonLib\\EBizAutos.ApplicationCommonLib.csproj"}, "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.CommonLib\\EBizAutos.Apps.CommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.CommonLib\\EBizAutos.Apps.CommonLib.csproj"}, "D:\\Work\\ebizautos\\src\\EBizAutos.CommonLib\\EBizAutos.CommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.CommonLib\\EBizAutos.CommonLib.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Amazon.AspNetCore.DataProtection.SSM": {"target": "Package", "version": "[1.1.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[6.0.27, )"}, "Microsoft.IdentityModel.Tokens": {"target": "Package", "version": "[7.4.0, )"}, "System.Security.Claims": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}, "net462": {"targetAlias": "net462", "dependencies": {"Amazon.AspNetCore.DataProtection.SSM": {"target": "Package", "version": "[1.1.0, )"}, "Microsoft.AspNetCore.Authentication.Cookies": {"target": "Package", "version": "[2.1.2, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[2.1.2, )"}, "Microsoft.AspNetCore.Authorization": {"target": "Package", "version": "[2.1.2, )"}, "Microsoft.AspNetCore.DataProtection.Extensions": {"target": "Package", "version": "[2.1.1, )"}, "Microsoft.AspNetCore.Mvc": {"target": "Package", "version": "[2.1.3, )"}, "Microsoft.AspNetCore.Mvc.Core": {"target": "Package", "version": "[2.1.3, )"}, "Microsoft.IdentityModel.Tokens": {"target": "Package", "version": "[5.3.0, )"}, "Microsoft.NETFramework.ReferenceAssemblies": {"suppressParent": "All", "target": "Package", "version": "[1.0.3, )", "autoReferenced": true}, "System.Security.Claims": {"target": "Package", "version": "[4.3.0, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}, "netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"Amazon.AspNetCore.DataProtection.SSM": {"target": "Package", "version": "[1.1.0, )"}, "Microsoft.AspNetCore.Authentication.Cookies": {"target": "Package", "version": "[2.1.2, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[2.1.2, )"}, "Microsoft.AspNetCore.Authorization": {"target": "Package", "version": "[2.1.2, )"}, "Microsoft.AspNetCore.DataProtection.Extensions": {"target": "Package", "version": "[2.1.1, )"}, "Microsoft.AspNetCore.Mvc": {"target": "Package", "version": "[2.1.3, )"}, "Microsoft.AspNetCore.Mvc.Core": {"target": "Package", "version": "[2.1.3, )"}, "Microsoft.IdentityModel.Tokens": {"target": "Package", "version": "[5.3.0, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "System.Security.Claims": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.MongoDbRepository\\EBizAutos.Apps.Authentication.MongoDbRepository.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.MongoDbRepository\\EBizAutos.Apps.Authentication.MongoDbRepository.csproj", "projectName": "EBizAutos.Apps.Authentication.MongoDbRepository", "projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.MongoDbRepository\\EBizAutos.Apps.Authentication.MongoDbRepository.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.MongoDbRepository\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net462", "net6.0", "netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\Work\\ebizautos\\packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.CommonLib\\EBizAutos.Apps.Authentication.CommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.CommonLib\\EBizAutos.Apps.Authentication.CommonLib.csproj"}, "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.CommonLib\\EBizAutos.Apps.CommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.CommonLib\\EBizAutos.Apps.CommonLib.csproj"}}}, "net462": {"targetAlias": "net462", "projectReferences": {"D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.CommonLib\\EBizAutos.Apps.Authentication.CommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.CommonLib\\EBizAutos.Apps.Authentication.CommonLib.csproj"}, "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.CommonLib\\EBizAutos.Apps.CommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.CommonLib\\EBizAutos.Apps.CommonLib.csproj"}}}, "netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {"D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.CommonLib\\EBizAutos.Apps.Authentication.CommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.CommonLib\\EBizAutos.Apps.Authentication.CommonLib.csproj"}, "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.CommonLib\\EBizAutos.Apps.CommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.CommonLib\\EBizAutos.Apps.CommonLib.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}, "net462": {"targetAlias": "net462", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies": {"suppressParent": "All", "target": "Package", "version": "[1.0.3, )", "autoReferenced": true}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}, "netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.CommonLib\\EBizAutos.Apps.CommonLib.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.CommonLib\\EBizAutos.Apps.CommonLib.csproj", "projectName": "EBizAutos.Apps.CommonLib", "projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.CommonLib\\EBizAutos.Apps.CommonLib.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.CommonLib\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net462", "net6.0", "netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\Work\\ebizautos\\packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\Work\\ebizautos\\src\\EBizAutos.ApplicationCommonLib\\EBizAutos.ApplicationCommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.ApplicationCommonLib\\EBizAutos.ApplicationCommonLib.csproj"}, "D:\\Work\\ebizautos\\src\\EBizAutos.CommonLib\\EBizAutos.CommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.CommonLib\\EBizAutos.CommonLib.csproj"}, "D:\\Work\\ebizautos\\src\\EBizAutos.Foundation.CommonLib\\EBizAutos.Foundation.CommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.Foundation.CommonLib\\EBizAutos.Foundation.CommonLib.csproj"}}}, "net462": {"targetAlias": "net462", "projectReferences": {"D:\\Work\\ebizautos\\src\\EBizAutos.ApplicationCommonLib\\EBizAutos.ApplicationCommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.ApplicationCommonLib\\EBizAutos.ApplicationCommonLib.csproj"}, "D:\\Work\\ebizautos\\src\\EBizAutos.CommonLib\\EBizAutos.CommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.CommonLib\\EBizAutos.CommonLib.csproj"}, "D:\\Work\\ebizautos\\src\\EBizAutos.Foundation.CommonLib\\EBizAutos.Foundation.CommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.Foundation.CommonLib\\EBizAutos.Foundation.CommonLib.csproj"}}}, "netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {"D:\\Work\\ebizautos\\src\\EBizAutos.ApplicationCommonLib\\EBizAutos.ApplicationCommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.ApplicationCommonLib\\EBizAutos.ApplicationCommonLib.csproj"}, "D:\\Work\\ebizautos\\src\\EBizAutos.CommonLib\\EBizAutos.CommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.CommonLib\\EBizAutos.CommonLib.csproj"}, "D:\\Work\\ebizautos\\src\\EBizAutos.Foundation.CommonLib\\EBizAutos.Foundation.CommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.Foundation.CommonLib\\EBizAutos.Foundation.CommonLib.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"target": "Package", "version": "[6.0.27, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[4.0.1, )"}, "Swashbuckle.AspNetCore.Filters": {"target": "Package", "version": "[4.5.5, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}, "Twilio.AspNet.Core": {"target": "Package", "version": "[5.20.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}, "net462": {"targetAlias": "net462", "dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": {"target": "Package", "version": "[2.1.1, )"}, "Microsoft.AspNetCore.Routing.Abstractions": {"target": "Package", "version": "[2.1.1, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[2.1.1, )"}, "Microsoft.NETFramework.ReferenceAssemblies": {"suppressParent": "All", "target": "Package", "version": "[1.0.3, )", "autoReferenced": true}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.1, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[4.0.1, )"}, "Swashbuckle.AspNetCore.Filters": {"target": "Package", "version": "[4.5.5, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[4.5.0, )"}, "Twilio.AspNet.Core": {"target": "Package", "version": "[5.20.1, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}, "netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": {"target": "Package", "version": "[2.1.1, )"}, "Microsoft.AspNetCore.Routing.Abstractions": {"target": "Package", "version": "[2.1.1, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[2.1.1, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.1, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[4.0.1, )"}, "Swashbuckle.AspNetCore.Filters": {"target": "Package", "version": "[4.5.5, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[4.5.0, )"}, "Twilio.AspNet.Core": {"target": "Package", "version": "[5.20.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\ebizautos\\src\\EBizAutos.CommonLib\\EBizAutos.CommonLib.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\ebizautos\\src\\EBizAutos.CommonLib\\EBizAutos.CommonLib.csproj", "projectName": "CommonLibCore", "projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.CommonLib\\EBizAutos.CommonLib.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\ebizautos\\src\\EBizAutos.CommonLib\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net461", "net6.0", "netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\Work\\ebizautos\\packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}, "net461": {"targetAlias": "net461", "projectReferences": {}}, "netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Castle.Core": {"target": "Package", "version": "[4.4.1, )"}, "CompareNETObjects": {"target": "Package", "version": "[4.57.0, )"}, "Dapper": {"target": "Package", "version": "[1.50.5, )"}, "DeepCloner": {"target": "Package", "version": "[0.10.2, )"}, "Experimental.System.Messaging": {"target": "Package", "version": "[1.0.0, )"}, "GreenPipes": {"target": "Package", "version": "[4.0.1, )"}, "Microsoft.Extensions.Primitives": {"target": "Package", "version": "[8.0.0, )"}, "MongoDB.Driver": {"target": "Package", "version": "[2.21.0, )"}, "MongoDB.Driver.GridFS": {"target": "Package", "version": "[2.21.0, )"}, "MongolianBarbecue": {"target": "Package", "version": "[1.0.0, )"}, "NEST": {"target": "Package", "version": "[7.13.2, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Configuration.ConfigurationManager": {"target": "Package", "version": "[4.5.0, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.6.0, )"}, "System.Private.ServiceModel": {"target": "Package", "version": "[4.10.3, )"}, "System.ServiceModel.Duplex": {"target": "Package", "version": "[4.10.3, )"}, "System.ServiceModel.Http": {"target": "Package", "version": "[4.10.3, )"}, "System.ServiceModel.NetTcp": {"target": "Package", "version": "[4.10.3, )"}, "System.ServiceModel.Security": {"target": "Package", "version": "[4.10.3, )"}, "System.ServiceProcess.ServiceController": {"target": "Package", "version": "[4.5.0, )"}, "TimeZoneConverter": {"target": "Package", "version": "[5.0.0, )"}, "UAParser": {"target": "Package", "version": "[3.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}, "net461": {"targetAlias": "net461", "dependencies": {"Castle.Core": {"target": "Package", "version": "[4.4.1, )"}, "CompareNETObjects": {"target": "Package", "version": "[4.57.0, )"}, "Dapper": {"target": "Package", "version": "[1.50.5, )"}, "DeepCloner": {"target": "Package", "version": "[0.10.2, )"}, "EnyimMemcached": {"target": "Package", "version": "[2.16.0, )"}, "Experimental.System.Messaging": {"target": "Package", "version": "[1.0.0, )"}, "GreenPipes": {"target": "Package", "version": "[4.0.1, )"}, "Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[1.0.4, )"}, "Microsoft.AspNetCore.WebUtilities": {"target": "Package", "version": "[1.0.4, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[1.0.1, )"}, "Microsoft.Extensions.Primitives": {"target": "Package", "version": "[1.0.1, )"}, "Microsoft.NETFramework.ReferenceAssemblies": {"suppressParent": "All", "target": "Package", "version": "[1.0.3, )", "autoReferenced": true}, "MongoDB.Driver": {"target": "Package", "version": "[2.21.0, )"}, "MongoDB.Driver.GridFS": {"target": "Package", "version": "[2.21.0, )"}, "MongolianBarbecue": {"target": "Package", "version": "[1.0.0, )"}, "NEST": {"target": "Package", "version": "[7.1.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[12.0.1, )"}, "System.Configuration.ConfigurationManager": {"target": "Package", "version": "[4.5.0, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.6.0, )"}, "System.ServiceModel.Duplex": {"target": "Package", "version": "[4.4.0, )"}, "System.ServiceModel.Http": {"target": "Package", "version": "[4.4.0, )"}, "System.ServiceModel.NetTcp": {"target": "Package", "version": "[4.4.0, )"}, "System.ServiceModel.Security": {"target": "Package", "version": "[4.4.0, )"}, "System.ServiceProcess.ServiceController": {"target": "Package", "version": "[4.5.0, )"}, "TimeZoneConverter": {"target": "Package", "version": "[5.0.0, )"}, "UAParser": {"target": "Package", "version": "[3.0.0, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}, "netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"Castle.Core": {"target": "Package", "version": "[4.4.1, )"}, "CompareNETObjects": {"target": "Package", "version": "[4.57.0, )"}, "Dapper": {"target": "Package", "version": "[1.50.5, )"}, "DeepCloner": {"target": "Package", "version": "[0.10.2, )"}, "EnyimMemcachedCore": {"target": "Package", "version": "[2.1.8, )"}, "Experimental.System.Messaging": {"target": "Package", "version": "[1.0.0, )"}, "GreenPipes": {"target": "Package", "version": "[4.0.1, )"}, "Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.1.1, )"}, "Microsoft.AspNetCore.WebUtilities": {"target": "Package", "version": "[2.1.1, )"}, "Microsoft.Extensions.Primitives": {"target": "Package", "version": "[2.1.1, )"}, "MongoDB.Driver": {"target": "Package", "version": "[2.21.0, )"}, "MongoDB.Driver.GridFS": {"target": "Package", "version": "[2.21.0, )"}, "MongolianBarbecue": {"target": "Package", "version": "[1.0.0, )"}, "NEST": {"target": "Package", "version": "[7.1.0, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "Newtonsoft.Json": {"target": "Package", "version": "[12.0.1, )"}, "System.Configuration.ConfigurationManager": {"target": "Package", "version": "[4.5.0, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.6.0, )"}, "System.Private.ServiceModel": {"target": "Package", "version": "[4.5.3, )"}, "System.ServiceModel.Duplex": {"target": "Package", "version": "[4.5.3, )"}, "System.ServiceModel.Http": {"target": "Package", "version": "[4.5.3, )"}, "System.ServiceModel.NetTcp": {"target": "Package", "version": "[4.5.3, )"}, "System.ServiceModel.Security": {"target": "Package", "version": "[4.5.3, )"}, "System.ServiceProcess.ServiceController": {"target": "Package", "version": "[4.5.0, )"}, "TimeZoneConverter": {"target": "Package", "version": "[5.0.0, )"}, "UAParser": {"target": "Package", "version": "[3.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\ebizautos\\src\\EBizAutos.Foundation.CommonLib\\EBizAutos.Foundation.CommonLib.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\ebizautos\\src\\EBizAutos.Foundation.CommonLib\\EBizAutos.Foundation.CommonLib.csproj", "projectName": "FoundationCommonLib", "projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.Foundation.CommonLib\\EBizAutos.Foundation.CommonLib.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\ebizautos\\src\\EBizAutos.Foundation.CommonLib\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net461", "net6.0", "netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\Work\\ebizautos\\packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\Work\\ebizautos\\src\\EBizAutos.ApplicationCommonLib\\EBizAutos.ApplicationCommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.ApplicationCommonLib\\EBizAutos.ApplicationCommonLib.csproj"}, "D:\\Work\\ebizautos\\src\\EBizAutos.CommonLib\\EBizAutos.CommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.CommonLib\\EBizAutos.CommonLib.csproj"}}}, "net461": {"targetAlias": "net461", "projectReferences": {"D:\\Work\\ebizautos\\src\\EBizAutos.ApplicationCommonLib\\EBizAutos.ApplicationCommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.ApplicationCommonLib\\EBizAutos.ApplicationCommonLib.csproj"}, "D:\\Work\\ebizautos\\src\\EBizAutos.CommonLib\\EBizAutos.CommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.CommonLib\\EBizAutos.CommonLib.csproj"}}}, "netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {"D:\\Work\\ebizautos\\src\\EBizAutos.ApplicationCommonLib\\EBizAutos.ApplicationCommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.ApplicationCommonLib\\EBizAutos.ApplicationCommonLib.csproj"}, "D:\\Work\\ebizautos\\src\\EBizAutos.CommonLib\\EBizAutos.CommonLib.csproj": {"projectPath": "D:\\Work\\ebizautos\\src\\EBizAutos.CommonLib\\EBizAutos.CommonLib.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}, "net461": {"targetAlias": "net461", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": {"target": "Package", "version": "[1.0.1, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[1.0.1, )"}, "Microsoft.NETFramework.ReferenceAssemblies": {"suppressParent": "All", "target": "Package", "version": "[1.0.3, )", "autoReferenced": true}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}, "netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": {"target": "Package", "version": "[2.1.1, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[2.1.1, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}}}}}