<template>
  <div>
    <website-overview-summary
      :barItems="bar.items"
      :summary="summary"
      :barTimeFormat="barTimeFormat"
      :deviceFilter="page.deviceFilter"
      @deviceFilterChanged="onDeviceFilterChanged"
    ></website-overview-summary>

    <div v-if="isAccountLevel">
      <account-level-card v-if="isAccountLevel" @backToGroup="onBackToGroup">
        {{accountName}} ({{page.filter.accountId}})
      </account-level-card>

      <website-overview-account-level-table
        :tableItems="table.items"
        :totalItems="table.totalItems"
        :pageNumber="page.filter.pageNumber"
        :pageSize="page.filter.pageSize"
        :sortType="page.filter.sortType"
        @pageNumberChanged="onPageNumberChanged"
        @pageSizeChanged="onPageSizeChanged"
        @sortTypeChanged="onSortTypeChanged"
      ></website-overview-account-level-table>
    </div>
    <div v-else>
      <group-website-overview-by-account-table
        :tableItems="table.items"
        :totalItems="table.totalItems"
        :pageNumber="page.filter.pageNumber"
        :pageSize="page.filter.pageSize"
        :sortType="page.filter.sortType"
        @pageNumberChanged="onPageNumberChanged"
        @pageSizeChanged="onPageSizeChanged"
        @sortTypeChanged="onSortTypeChanged"
        @accountNameClicked="onAccountNameClicked"
      ></group-website-overview-by-account-table>
    </div>
  </div>
</template>

<style src="@/vendor/styles/pages/analytics.scss" lang="scss"></style>

<script>
import analyticsConstants from './../../../shared/analytics/constants'
import analyticsBuilders from './../../../shared/analytics/builders'
import analyticsHelper from '../helpers.js'
import baseGroupReportPage from './baseGroupReportPage.js'

import WebsiteOverviewSummary from '../../../components/analytics_ga4/summaries/websiteOverviewSummary'
import AccountLevelCard from '../../../components/analytics_ga4/accountLevelCard'
import WebsiteOverviewAccountLevelTable from '../../../components/analytics_ga4/tables/websiteOverviewAccountLevelTable'
import GroupWebsiteOverviewByAccountTable from '../../../components/analytics_ga4/tables/groupWebsiteOverviewByAccountTable'

const filterManager = analyticsBuilders.getFilterManager({
  accountId: { type: Number, default: 0 },
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  pageNumber: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  sortType: { type: Number, default: analyticsConstants.websiteOverviewSortTypes.accountNameAsc }
})

const defaultAccountLevelSortType = analyticsConstants.websiteOverviewSortTypes.dateDesc

export default {
  mixins: [baseGroupReportPage],
  name: 'group-website-overview',
  metaInfo: {
    title: 'Analytics - Website Overview'
  },
  components: {
    GroupWebsiteOverviewByAccountTable,
    WebsiteOverviewAccountLevelTable,
    AccountLevelCard,
    WebsiteOverviewSummary
  },
  created () {
    this.$store.commit('analyticsGa4/setAnalyticsName', 'Website Overview')
    this.$store.commit('analyticsGa4/setBlackThemeOn', true)
  },
  data () {
    return {
      page: {
        filter: filterManager.defaultValue
      },
      summary: {
        sessions: 0,
        sessionsDelta: null,
        pageViews: 0,
        pageViewsDelta: null,
        totalLeads: 0,
        totalLeadsDelta: null,
        convRatePerSession: 0,
        convRatePerSessionDelta: null
      }
    }
  },
  computed: {
    filterManager () {
      return filterManager
    },
    defaultAccountLevelSortType () {
      return defaultAccountLevelSortType
    }
  },
  methods: {
    getAccountNameSortTypeCondition () {
      return this.page.filter.sortType === analyticsConstants.websiteOverviewSortTypes.accountNameAsc ||
        this.page.filter.sortType === analyticsConstants.websiteOverviewSortTypes.accountNameDesc
    },
    async updateStatistics () {
      try {
        await Promise.all([
          !this.isAccountLevel ? this.updateGroupLevelGraphAndSummary() : null,
          !this.isAccountLevel ? this.updateGroupLevelDetails() : null,
          this.isAccountLevel ? this.updateAccountLevelGraphAndSummary() : null,
          this.isAccountLevel ? this.updateAccountLevelDetails() : null
        ])
      } catch (err) {
        this.$toaster.error('Failed to get data from server', { timeout: 8000 })
        this.$logger.handleError(err, 'Can\'t update statistics', { filter: this.page.filter, cache: this.cache })
      }
    },
    async updateGroupLevelGraphAndSummary () {
      let storePath = 'analyticsGa4/'
      switch (this.page.deviceFilter) {
        case 'mobile':
          storePath += 'getGroupMobileOverviewGraphAndSummary'
          break
        case 'desktop':
          storePath += 'getGroupDesktopOverviewGraphAndSummary'
          break
        default:
          storePath += 'getGroupWebsiteOverviewGraphAndSummary'
          break
      }

      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        storePath,
        {
          reportGroupId: this.reportGroupId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo
        }
      )

      this.summary = {
        ...this.summary,
        ...store.summary.data
      }
      this.bar.items = store.graph.data.items
    },
    async updateAccountLevelGraphAndSummary () {
      let storePath = 'analyticsGa4/'
      switch (this.page.deviceFilter) {
        case 'mobile':
          storePath += 'getMobileOverviewGraphAndSummary'
          break
        case 'desktop':
          storePath += 'getDesktopOverviewGraphAndSummary'
          break
        default:
          storePath += 'getWebsiteOverviewGraphAndSummary'
          break
      }

      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        storePath,
        {
          accountId: this.page.filter.accountId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo
        }
      )

      this.summary = {
        ...this.summary,
        ...store.summary.data
      }
      this.bar.items = store.graph.data.items
    },
    async updateGroupLevelDetails () {
      let storePath = ''
      switch (this.page.deviceFilter) {
        case 'mobile':
          storePath = 'analyticsGa4/getGroupMobileOverviewDetailsPage'
          break
        case 'desktop':
          storePath = 'analyticsGa4/getGroupDesktopOverviewDetailsPage'
          break
        default:
          storePath = 'analyticsGa4/getGroupWebsiteOverviewDetailsPage'
          break
      }

      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        storePath,
        {
          reportGroupId: this.reportGroupId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo,
          pageNumber: this.page.filter.pageNumber,
          pageSize: this.page.filter.pageSize,
          sortType: this.page.filter.sortType
        }
      )

      this.table.items = store.detailedData.data.items.map(x => {
        x.account.isAccessAllowed = this.isAccessToAccountAllowed(x.account.accountId)
        return x
      })
      this.table.totalItems = store.detailedData.data.totalItems
    },
    async updateAccountLevelDetails () {
      let storePath = ''
      switch (this.page.deviceFilter) {
        case 'mobile':
          storePath = 'analyticsGa4/getMobileOverviewDetailsPage'
          break
        case 'desktop':
          storePath = 'analyticsGa4/getDesktopOverviewDetailsPage'
          break
        default:
          storePath = 'analyticsGa4/getWebsiteOverviewDetailsPage'
          break
      }

      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        storePath,
        {
          accountId: this.page.filter.accountId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo,
          pageNumber: this.page.filter.pageNumber,
          pageSize: this.page.filter.pageSize,
          sortType: this.page.filter.sortType
        }
      )

      this.table.items = store.detailedData.data.items
      this.table.totalItems = store.detailedData.data.totalItems
    }
  }
}
</script>
