<template>
  <div>
    <b-row class="m-4">
      <b-col lg="5" md="12" sm="12">
        <b-input-group>
          <b-form-input v-model="filters.search"  @keydown.native='applySearchForEnter' placeholder="Search..."></b-form-input>
          <b-input-group-append>
            <b-button type="submit" @click="applySearch" variant="primary">Submit</b-button>
          </b-input-group-append>
        </b-input-group>
      </b-col>
      <b-col lg="7" md="12" sm="12" class="py-2 d-flex justify-content-lg-end justify-content-md-start justify-content-sm-start">
        <b-form-radio-group
          v-model="filters.errorreporttype"
          :options="errorReportTypeOptions"
          name="rule-error-report-types"
          @input="onErrorReportTypeInput"
        />
      </b-col>
    </b-row>
    <b-card no-body>
      <b-table
        :items="items"
        :fields="tableFields"
        striped
      >
      <template #cell(errorReportType)="{value}">
        {{getErrorReportTypeDesc(value)}}
      </template>
      <template #cell(errorRuleBody)="{ item, value }">
        <span v-if="!item.editing">
          {{value}}
        </span>
        <b-input size="sm" v-else v-model="item.temp['errorRuleBody']" @keydown.enter.exact="save(item)"></b-input>
      </template>
      <template #cell(errorRuleComment)="{ item, value }">
        <span v-if="!item.editing">
          {{value}}
        </span>
        <b-input size="sm" v-else v-model="item.temp['errorRuleComment']" @keydown.enter.exact="save(item)"></b-input>
      </template>
      <template #cell(manage)="{ item }" >
        <div class="d-flex flex-row">
          <b-btn v-if="!item.editing" @click="edit(item)" size="sm" variant="secondary">
            Edit
          </b-btn>
          <b-btn v-if="item.editing" @click="save(item)" size="sm" variant="primary">
            Save
          </b-btn>
          <b-btn v-if="item.editing" @click="cancel(item)" class="ml-2" size="sm" variant="secondary">
            Cancel
          </b-btn>
          <b-btn variant="dark" @click="deleteErrorRule(item)" class="ml-2" size="sm">Delete</b-btn>
        </div>
      </template>
      </b-table>
      <paging
        :pageNumber="filters.page"
        :pageSize="filters.pageSize"
        :totalItems="itemsTotalCount"
        titled
        pageSizeSelector
        @numberChanged="pageChanged"
        @changePageSize="changePageSize"
      />
    </b-card>
  </div>
</template>

<script>
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import { errorReportTypes } from '@/shared/errorReport/constants'
import paging from '@/components/_shared/paging'

const defaultFilters = new ObjectSchema({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  search: { type: String, default: '' },
  errorreporttype: { type: String, default: '1' }
})
const queryHelper = new QueryStringHelper(defaultFilters)

export default {
  name: 'error-report-rule-manager',
  data () {
    return {
      items: [],
      itemsTotalCount: 0,
      filters: defaultFilters.getObject()
    }
  },
  components: {
    paging
  },
  created () {
    this.filters = queryHelper.parseQueryStringToObject(this.$router)
    this.loadContent()
  },
  computed: {
    tableFields () {
      return [
        {
          key: 'userName',
          label: 'User',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'errorReportType',
          label: 'Type',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'errorRuleBody',
          label: 'Error Regex',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'errorRuleComment',
          label: 'Comment',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    errorReportTypeOptions () {
      return Object.values(errorReportTypes)
    }
  },
  methods: {
    getErrorReportTypeDesc (value) {
      let res = this.errorReportTypeOptions.find(x => x.value === value)
      if (res) {
        return res.text
      }
      return 'Undefined'
    },
    applySearchForEnter (event) {
      if (event.which === 13) {
        this.filters.page = 1
        this.synchronizeUrlAndReload()
      }
    },
    applySearch () {
      this.filters.page = 1
      this.synchronizeUrlAndReload()
    },
    synchronizeUrlAndReload () {
      queryHelper.rebuildParamsInQueryString(this.$router, this.filters)
      this.loadContent()
    },
    pageChanged (newPage) {
      this.filters.page = newPage
      this.synchronizeUrlAndReload()
    },
    changePageSize (newSize) {
      this.filters.pageSize = newSize
      this.filters.page = 1
      this.synchronizeUrlAndReload()
    },
    onErrorReportTypeInput () {
      this.filters.page = 1
      this.synchronizeUrlAndReload()
    },
    loadContent () {
      this.$store.dispatch('systemTools/getErrorReportRules', this.filters).then(res => {
        this.items = res.data.model.errorReportRules
        this.itemsTotalCount = res.data.model.rulesTotalCount
      }).catch(ex => {
        this.$toaster.error('Cannot get error rules')
        this.$logger.handleError(ex, 'Cannot get error rules', this.filters)
      })
    },
    updateRule (item) {
      this.$store.dispatch('systemTools/updateErrorRule', item).then(res => {
        this.$toaster.success('Error Rule Successfully Updated')
      }).catch(ex => {
        if (ex.response && ex.response.status === 400 && ex.response.data && ex.response.data.executionResultMessage === 'Such rule already exists') {
          this.$toaster.error(ex.response.data.executionResultMessage)
        } else {
          let message = ex.response ? ex.response.data : ex.message
          this.$toaster.error(`Cannot update error rule. ${message}`)
          this.$logger.handleError(ex, 'Error Report Exception. Method: onAddNewErrorRule')
        }
      }).finally(() => {
        this.synchronizeUrlAndReload()
      })
    },
    deleteErrorRule (item) {
      this.$store.dispatch('systemTools/deleteErrorRule', item.id).then(res => {
        this.$toaster.success('Error Rule Successfully Deleted')
      }).catch(ex => {
        this.$toaster.error('Cannot delate error rule')
        this.$logger.handleError(ex, 'Cannot get delete rule', item)
      }).finally(() => {
        this.synchronizeUrlAndReload()
      })
    },
    edit (item) {
      this.$set(item, 'temp', JSON.parse(JSON.stringify(item)))
      this.$set(item, 'editing', true)
    },
    save (item) {
      this.$set(item, 'editing', false)
      for (let key in item.temp) {
        if (item[key] !== item.temp[key]) {
          item[key] = item.temp[key]
        }
      }
      this.updateRule(item)
    },
    cancel (item) {
      this.$set(item, 'editing', false)
      this.$delete(item, 'temp')
    }
  }
}
</script>
