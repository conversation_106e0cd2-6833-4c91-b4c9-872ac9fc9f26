/* eslint-disable no-useless-escape,func-call-spacing */
/* eslint no-extend-native: 0 */

applyArrayExtensions()
applyDateExtensions()
applyStringExtensions()

function applyArrayExtensions () {
  Array.prototype.equals = function (right, hasToOrder = false) {
    let array1 = this
    let array2 = right

    if (!Array.isArray(array2)) {
      return false
    }

    if (array1 === array2) {
      return true
    }

    if (array1.length !== array2.length) {
      return false
    }

    if (hasToOrder) {
      array1 = array1.sort()
      array2 = array2.sort()
    }

    for (let i = 0; i < array1.length; i++) {
      if (array1[i] !== array2[i]) {
        return array1[i] === array2[i]
      }
    }

    return true
  }
}

function applyDateExtensions () {
  Date.daysBetween = function (date1, date2, isAbsoluteValue) {
    // Get 1 day in milliseconds
    const oneDay = 1000 * 60 * 60 * 24

    // Convert both dates to milliseconds
    const date1InMS = date1.getTime()
    const date2InMS = date2.getTime()

    // Calculate the difference in milliseconds
    const differenceInMS = date1InMS - date2InMS

    // Convert back to days and return
    const dayDifference = Math.round(differenceInMS / oneDay)

    return isAbsoluteValue ? Math.abs(dayDifference) : dayDifference
  }
}

function applyStringExtensions () {
  const escapeRegExp = function (strToEscape) {
    // Escape special characters for use in a regular expression
    return strToEscape.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g, '\\$&')
  }

  String.prototype.trimChar = function (charToTrim) {
    const origString = this

    charToTrim = escapeRegExp(charToTrim)
    const regEx = new RegExp('^[' + charToTrim + ']+|[' + charToTrim + ']+$', 'g')
    return origString.replace(regEx, '')
  }
}
