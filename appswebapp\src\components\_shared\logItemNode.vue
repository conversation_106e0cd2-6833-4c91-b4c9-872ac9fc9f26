<template>
  <div class="ml-4 log-node-holder">
    <b-row class="log-node-row">
      <b-col lg="4" sm="3" v-if="data.name">
        <span v-if="data.name" class="font-weight-bolder">{{data.name}}</span>
      </b-col>

      <b-col>
        <span v-if="hasSubNodes || hasSubValues">
          <b-button v-if="isLocallyExpandedDeep || isLocallyExpandedShallow" variant="light" size="xs" @click="collapseAllLevels">
            <i class="ion ion-md-arrow-dropdown"></i>
          </b-button>
          <b-button v-if="!isLocallyExpandedShallow" variant="light" size="xs" @click="expandOneLevel">
            <i class="ion ion-md-arrow-dropright"></i>
          </b-button>
          <b-button v-if="!isLocallyExpandedDeep" variant="light" size="xs" @click="expandAllLevels">
            <i class="ion ion-md-arrow-dropright"></i>
            <i class="ion ion-md-arrow-dropright"></i>
          </b-button>
        </span>

        <!-- Simple value -->
        <div v-if="data.value">
          <a v-if="data.isLink" :href="data.value">{{data.linkLabel}}</a>
          <a v-else-if="data.isPathAndQueryOnly" :to="data.value">{{data.linkLabel}}</a>
          <span v-else style="word-break: break-word;">{{data.value}}</span>
        </div>
        <span v-else-if="hasEmptySubNodes || hasEmptySubValues">[ ]</span>
      </b-col>
    </b-row>

    <!-- Subtree -->
    <div v-if="data.nodes && data.nodes.length > 0 && (isLocallyExpandedDeep || isLocallyExpandedShallow)" class="log-node-child">
      <log-node
        v-for="(subnode, i) in data.nodes"
        :key="i"
        :data="subnode"
        :isExpandedShallow="false"
        :isExpandedDeep="isLocallyExpandedDeep"
        class="log-subnode"
      />
    </div>

    <!-- Array value -->
    <div v-if="data.values && data.values.length > 0 && (isLocallyExpandedDeep || isLocallyExpandedShallow)" class="log-node-child">
      <log-node
        v-for="(subValue, i) in data.values"
        :key="i"
        :data="subValue"
        :isExpandedShallow="false"
        :isExpandedDeep="isLocallyExpandedDeep"
        class="log-subnode"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'log-node',
  props: {
    data: Object,
    isExpandedShallow: {
      type: Boolean,
      default: true
    },
    isExpandedDeep: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      isLocallyExpandedShallow: this.isExpandedShallow,
      isLocallyExpandedDeep: this.isExpandedDeep
    }
  },
  computed: {
    hasSubNodes () {
      return this.data.nodes && this.data.nodes.length > 0
    },
    hasSubValues () {
      return this.data.values && this.data.values.length > 0
    },
    hasEmptySubNodes () {
      return this.data.nodes && this.data.nodes.length === 0
    },
    hasEmptySubValues () {
      return this.data.values && this.data.values.length === 0
    }
  },
  methods: {
    expandOneLevel () {
      this.isLocallyExpandedShallow = true
      this.isLocallyExpandedDeep = false
    },
    expandAllLevels () {
      this.isLocallyExpandedShallow = false
      this.isLocallyExpandedDeep = true
    },
    collapseAllLevels () {
      this.isLocallyExpandedShallow = false
      this.isLocallyExpandedDeep = false
    }
  },
  watch: {
    isExpandedDeep (newVal) {
      this.isLocallyExpandedShallow = false
      this.isLocallyExpandedDeep = newVal
    }
  }
}
</script>

<style>
  .log-node-holder .log-node-child {
    background-color: white;
    border-left: 1px solid #eee;
  }

  .log-node-holder .log-subnode:nth-child(2n) {
    background-color: #eeeeee;
  }
</style>
