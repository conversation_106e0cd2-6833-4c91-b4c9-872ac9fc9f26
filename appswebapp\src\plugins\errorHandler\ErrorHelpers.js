
const getComponentName = (component) => {
  return (component.$options || {}).name || 'Unnamed'
}

const retrieveParentComponentNames = (vueComponent) => {
  let names = []
  let parent = vueComponent.$parent
  while (parent) {
    names.push(getComponentName(parent))
    parent = parent.$parent
  }
  return names
}

export default {
  getErrorInfoFromVueInstance (vm) {
    if (!vm) {
      return {}
    }
    let componentName = getComponentName(vm)
    let componentParens = retrieveParentComponentNames(vm)
    return {componentName: componentName, componentParens: componentParens}
  }
}
