import BaseService from '@/services/BaseService'
import analyticsConstants from '@/shared/analytics/constants'

class OAuth2TokenService extends BaseService {
  constructor (apiPathBase) {
    super()
    this.apiPathBase = apiPathBase
  }

  getGoogleSignUrl ({
    authorizationSuccessfulRedirectionUrl,
    authorizationFailedRedirectionUrl
  }) {
    return this.axios.get(
      `${this.apiPathBase}oauth2/sign_in_url`,
      {
        params: {
          google_service: analyticsConstants.googleAnalyticsServiceId,
          successful_redirection_url: authorizationSuccessfulRedirectionUrl,
          failed_redirection_url: authorizationFailedRedirectionUrl
        }
      })
  };

  getSignInStatus () {
    return this.axios.get(
      `${this.apiPathBase}oauth2/${analyticsConstants.googleAnalyticsServiceId}`)
  };

  revokeToken () {
    return this.axios.post(
      `${this.apiPathBase}oauth2/${analyticsConstants.googleAnalyticsServiceId}/revoke_token`)
  }
}

const ga4OAuthTokenService = new OAuth2TokenService(analyticsConstants.googleAnalytics4ApiEndpointsBasePath)

export default ga4OAuthTokenService
