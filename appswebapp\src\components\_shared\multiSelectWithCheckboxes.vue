<template>
<div :class="customClass">
  <multi-select
    ref="multiselect"
    v-model="selectedValues"
    :options="options"
    :trackBy="valueField"
    :multiple="true"
    :show-labels="false"
    :close-on-select="false"
    :customLabel="customLabel"
    :searchable="searchable"
    selectLabel=""
    selectedLabel=""
    deselectLabel=""
    placeholder=""
    :showNoResults="false"
    :allowEmpty="allowEmpty"
    :disabled="disabled"
  >
    <template slot="selection">
      <span>{{getSelectionText}}</span>
    </template>
    <template slot="beforeList">
      <b-btn class="btn-round my-1" :style="btnSelectUnselectAllStyle" variant="outline-primary" size="sm" @click="selectUnselectAll">{{isAllSelected && allowEmpty ? 'Unselect': 'Select All'}}</b-btn>
    </template>
    <template slot="option" slot-scope="props">
      <div>
        <div class="text-primary bg-white float-left border mr-1" style="width: 1rem; height: 1rem;">
          <font-awesome-icon v-if="isCheckedValue(props.option[valueField])" icon="check" size="sm"/>
        </div>
        <span style="font-size: 0.894rem;">{{props.option[labelField]}}</span>
      </div>
    </template>
  </multi-select>
</div>
</template>

<script>
import Multiselect from 'vue-multiselect'

export default {
  name: 'multi-select-with-checkboxes',
  props: {
    customClass: { type: String, default: '' },
    valueField: { type: String, default: 'value' },
    labelField: { type: String, default: 'text' },
    value: { type: Array, default: () => [] },
    options: { type: Array, default: () => [] },
    name: {type: String, default: 'Items'},
    allowEmpty: {type: Boolean, default: true},
    customMessageOfNoneSelectedItems: {type: String, default: ''},
    disabled: {type: Boolean, default: false},
    searchable: {type: Boolean, default: true}
  },
  data () {
    return {
      btnSelectUnselectAllStyle: ''
    }
  },
  created () {
    window.addEventListener('resize', this.rebuildStyles)
    this.$nextTick(() => {
      this.rebuildStyles()
    })
  },
  destroyed () {
    window.removeEventListener('resize', this.rebuildStyles)
  },
  computed: {
    selectedValues: {
      get () {
        return this.options.filter(op => this.value.some(val => val === op[this.valueField]))
      },
      set (values) {
        this.$emit('input', values.map(x => x[this.valueField]))
        this.$emit('change', values)
      }
    },
    isAllSelected () {
      return this.value.length === this.options.length
    },
    isNoneSelected () {
      return this.value.length === 0
    },
    getSelectionText () {
      if (this.isNoneSelected && this.customMessageOfNoneSelectedItems) {
        return this.customMessageOfNoneSelectedItems
      }
      if (this.isAllSelected) {
        return `Selected All ${this.name}`
      }
      return `Selected ${this.value.length} of ${this.options.length} ${this.name}`
    }
  },
  methods: {
    customLabel (val) {
      return val[this.labelField]
    },
    isCheckedValue (val) {
      return this.value.some(x => x === val)
    },
    selectUnselectAll () {
      if (this.isAllSelected && this.allowEmpty) {
        this.$emit('input', [])
      } else {
        this.$emit('input', this.options.map(x => x[this.valueField]))
      }
      this.$emit('change')
    },
    rebuildStyles () {
      let width = this.$refs.multiselect.$el.clientWidth
      this.btnSelectUnselectAllStyle = `position: sticky; width: 100px; left: ${width - 105}px;`
    }
  },
  components: {
    'multi-select': Multiselect
  }
}
</script>
