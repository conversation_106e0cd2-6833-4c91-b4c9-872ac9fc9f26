const conditionTypes = Object.freeze({
  new: { value: 1, text: 'New' },
  used: { value: 2, text: 'Used' },
  cpo: { value: 3, text: 'CPO' },
  dealerCpo: { value: 4, text: 'Dealer CPO Label' },
  dealerGroupCpo: { value: 5, text: 'Group CPO Label' }
})

const vehicleStatusTypes = Object.freeze({
  live: { value: 3, text: 'Live' },
  pendingSell: { value: 4, text: 'Pending' },
  sold: { value: 5, text: 'Sold' }
})

const fuelTypeTypes = Object.freeze({
  notDefined: { value: 0, text: 'Not Specified' },
  gasoline: { value: 1, text: 'Gasoline' },
  diesel: { value: 2, text: 'Diesel' },
  flexFuel: { value: 3, text: 'Flex Fuel' },
  hybrid: { value: 4, text: 'Hybrid' },
  naturalGas: { value: 5, text: 'Natural Gas' },
  propaneGas: { value: 6, text: 'Propane Gas' },
  ethanol: { value: 7, text: 'Ethanol' },
  electric: { value: 8, text: 'Electric' },
  methanol: { value: 9, text: 'Methanol' }
})

const vehicleTypeTypes = Object.freeze({
  atv: { value: 1, text: 'Atv' },
  boat: { value: 2, text: 'Boat' },
  bus: { value: 3, text: 'Bus' },
  motorcycle: { value: 4, text: 'Motorcycle' },
  passenger: { value: 5, text: 'Passenger' },
  plane: { value: 6, text: 'Plane' },
  rv: { value: 7, text: 'Rv' },
  scooter: { value: 8, text: 'Scooter' },
  snow: { value: 9, text: 'Snow' },
  truck: { value: 10, text: 'Truck' },
  misc: { value: 11, text: 'Miscellaneous' }
})

const transmissionTypeTypes = Object.freeze({
  manual: { value: 1, text: 'Manual' },
  automatic: { value: 2, text: 'Automatic' }
})

const drivetrainTypes = Object.freeze({
  fourWheelDrive: { value: 1, text: 'Four Wheel Drive' },
  frontWheelDrive: { value: 2, text: 'Front Wheel Drive' },
  allWheelDrive: { value: 3, text: 'All Wheel Drive' },
  rearWheelDrive: { value: 4, text: 'Rear Wheel Drive' },
  twoWheelDrive: { value: 5, text: 'Two Wheel Drive' },
  sixXTwo: { value: 6, text: '6x2' },
  sixXFour: { value: 7, text: '6x4' },
  sixXSix: { value: 8, text: '6x6' },
  eightXTwo: { value: 9, text: '8x2' },
  eightXFour: { value: 10, text: '8x4' },
  eightXSix: { value: 11, text: '8x6' }
})

const bodyStyleTypes = Object.freeze({
  convertible: { value: 1, text: 'Convertible' },
  coupe: { value: 2, text: 'Coupe' },
  hatchback: { value: 3, text: 'Hatchback' },
  sedan: { value: 4, text: 'Sedan' },
  suv: { value: 5, text: 'SUV' },
  truck: { value: 6, text: 'Truck' },
  van: { value: 7, text: 'Van' },
  wagon: { value: 8, text: 'Wagon' },
  car: { value: 9, text: 'Car' },
  sav: { value: 10, text: 'SAV' },
  liftback: { value: 11, text: 'Liftback' },
  sac: { value: 12, text: 'SAC' },
  fastback: { value: 13, text: 'Fastback' }
})

const rvClassTypes = Object.freeze({
  fifthWheel: { value: 1, text: 'Fifth Wheel' },
  foldingCamper: { value: 2, text: 'Folding Camper' },
  motorizedClassA: { value: 3, text: 'Motorized Class A' },
  motorizedClassB: { value: 4, text: 'Motorized Class B' },
  motorizedClassC: { value: 5, text: 'Motorized Class C' },
  toyHauler: { value: 6, text: 'Toy Hauler' },
  travelTrailer: { value: 7, text: 'Travel Trailer' },
  truckCamper: { value: 8, text: 'Truck Camper' }
})

const cabStyleTypes = Object.freeze({
  accessCab: { value: 1, text: 'Access Cab' },
  clubCab: { value: 2, text: 'Club Cab' },
  crewCab: { value: 3, text: 'Crew Cab' },
  doubleCab: { value: 4, text: 'Double Cab' },
  extendedCab: { value: 5, text: 'Extended Cab' },
  extraCab: { value: 6, text: 'Extra Cab' },
  kingCab: { value: 7, text: 'King Cab' },
  megaCab: { value: 8, text: 'Mega Cab' },
  quadCab: { value: 9, text: 'Quad Cab' },
  regularCab: { value: 10, text: 'Regular Cab' },
  superCab: { value: 11, text: 'Super Cab' },
  superCrewCab: { value: 12, text: 'Super Crew Cab' },
  xtraCab: { value: 13, text: 'Xtra Cab' }
})

const statusCodeFilterTypes = Object.freeze({
  filterNotUsed: { value: null, text: 'Filter Not Used' },
  displayOnlyStatusCode: { value: 1, text: 'Display Only Status Code' },
  excludeStatusCode: { value: 2, text: 'Exclude Status Code' }
})

const featuredVehicleFilterTypes = Object.freeze({
  filterNotUsed: { value: null, text: 'Filter Not Used' },
  include: { value: 1, text: 'Show Only Featured Vehicles' },
  exclude: { value: 2, text: 'Exclude Featured Vehicles' }
})

const promotionalFlagFilterTypes = Object.freeze({
  filterNotUsed: { value: null, text: 'Filter Not Used' },
  include: { value: 1, text: 'Show Only Promo Vehicles' },
  exclude: { value: 2, text: 'Exclude Promo Vehicles' }
})

const ebayListingsTypes = Object.freeze({
  filterNotUsed: { value: null, text: 'Filter Not Used' },
  include: { value: 1, text: 'Show Only Vehicles on eBay' },
  exclude: { value: 2, text: 'Exclude Vehicles on eBay' }
})

const carfaxReportFilterTypes = Object.freeze({
  filterNotUsed: { value: null, text: 'Filter Not Used' },
  activeVehiclesCarfaxReport: { value: 0, text: 'Show only vehicles having an active CARFAX Report' },
  activeVehiclesOneOwnerCarfaxReport: { value: 1, text: 'Show only vehicles having an active One Owner CARFAX Report' }
})

const makeModelFilterTypes = Object.freeze({
  onlyMakeModelTrim: { value: 1, text: 'Show only the make / model / trim(s) selected below' },
  notShowMake: { value: 2, text: 'DO NOT Show the make selected below (all models/trims automatically excluded)' },
  notShowModel: { value: 3, text: 'Show the make selected below but DO NOT show the selected models' }
})

export default {
  conditionTypes,
  vehicleStatusTypes,
  fuelTypeTypes,
  vehicleTypeTypes,
  transmissionTypeTypes,
  drivetrainTypes,
  bodyStyleTypes,
  rvClassTypes,
  cabStyleTypes,
  statusCodeFilterTypes,
  featuredVehicleFilterTypes,
  promotionalFlagFilterTypes,
  ebayListingsTypes,
  carfaxReportFilterTypes,
  makeModelFilterTypes
}
