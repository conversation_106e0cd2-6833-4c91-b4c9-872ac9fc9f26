<template>
  <editSettingsHelper @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="Voice Settings" :isLoading="isUpdatingProcessed" :isViewMode="isViewMode" :readOnlyMode="readOnlyMode">
    <template slot="settings-content">
      <detail-row :large-payload-width="true" :title-position="'start'">
        <span slot="title">Choose Voice:</span>
        <div slot="payload" class="w-1oo d-flex flex-column">
          <div v-for="voiceOpt in getVoiceOptions" :key="voiceOpt.value" class="d-flex flex-row mb-2">
            <b-form-radio v-model="updatedSettings.voiceType" :value="voiceOpt.value" name="inventory-autovideo-voice-option" :disabled="isViewMode">
              <span>{{voiceOpt.text}}</span>
            </b-form-radio>
            <audioPlayer class="ml-2" v-if="isAudioPlayerEnabled(voiceOpt.value)" :loadSourceOnPlay="true"  :loadSourceFunc="getLoadVoiceFunc(voiceOpt.value)" type="mp3"/>
          </div>
        </div>
      </detail-row>
    </template>
  </editSettingsHelper>
</template>

<script>
import editSettingsHelper from '@/components/_shared/editSettingsHelper'
import editSettingsMixin from './/editSettingsMixin'
import detailRow from '@/components/details/helpers/detailRow'
import audioPlayer from '../../_shared/audioPlayer.vue'
import globals from '@/globals'
import videoEncoderTypes from '../../../shared/inventory/videoEncoderTypes'
import VideoEncoderService from '../../../services/inventory/VideoEncoderService'

export default {
  name: 'inventory-account-autovideo-voice-settings-section',
  props: {
    settings: {
      type: Object,
      required: true
    },
    readOnlyMode: {
      type: Boolean
    }
  },
  data () {
    return {
      updatedSettings: globals().getClonedValue(this.settings),
      accountId: +this.$route.params.accountId,
      isViewMode: true,
      isUpdatingProcessed: false
    }
  },
  mixins: [editSettingsMixin],
  components: {
    editSettingsHelper,
    detailRow,
    audioPlayer
  },
  computed: {
    getVoiceOptions () {
      return [videoEncoderTypes.voiceTypes.joanna, videoEncoderTypes.voiceTypes.matthew, videoEncoderTypes.voiceTypes.random, videoEncoderTypes.voiceTypes.none]
    }
  },
  methods: {
    isAudioPlayerEnabled (voiceType) {
      return [videoEncoderTypes.voiceTypes.joanna.value, videoEncoderTypes.voiceTypes.matthew.value].includes(voiceType)
    },
    updateSettings () {
      this.isUpdatingProcessed = true
      VideoEncoderService.updateAccountPhotoToVideoVoiceSettings(this.accountId, this.updatedSettings).then(res => {
        this.$toaster.success('Updated Voice Settings Successfully')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Failed on Updating Voice Settings', {timeout: 5000})
      }).finally(() => {
        this.isUpdatingProcessed = false
        this.isViewMode = true
        this.$emit('refresh')
      })
    },
    getLoadVoiceFunc (voiceType) {
      return async () => {
        let url = ''
        try {
          let response = await VideoEncoderService.downloadExampleVoiceSound(this.accountId, voiceType)
          if (response.status === 200) {
            let blob = this.$locale.b64toBlob(response.data.base64String, 'audio/mp3')
            url = URL.createObjectURL(blob)
          } else {
            console.log(response)
            this.$toaster.error('Failed on loading voice audio')
          }
        } catch (ex) {
          console.error(ex)
          this.$toaster.error('Failed on loading voice audio')
        }

        return url
      }
    }
  }
}
</script>
