<template>
  <div class='mb-4'>
    <editSettingsHelper @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="eBay Bids Notification Send Options" :isDisabled="isDisabled" :isLoading="isUpdatingProcessed" :isViewMode="isViewMode">
      <div slot="settings-content">
        <detail-row fixedPayloadWidth>
          <span slot="title">Displayed Contact %:</span>
          <b-form-input slot="payload" :state='displayContactState' type='number' :disabled='isViewMode' v-model='adminAccountSettingsToUpdate.eBayPercentRequiredToNotifyDisplayedContact'/>
        </detail-row>
        <detail-row fixedPayloadWidth>
          <span slot="title">Copy to Email Addresses %:</span>
          <b-form-input slot="payload" :state='copyToEmailAddressState' type='number' :disabled='isViewMode' v-model='adminAccountSettingsToUpdate.eBayPercentRequiredToNotifyCopyToEmail'/>
        </detail-row>
        <detail-row fixedPayloadWidth>
          <span slot="title">Copy to Adf Addresses %:</span>
          <b-form-input slot="payload" :state='copyToAdfAddressState' type='number' :disabled='isViewMode' v-model='adminAccountSettingsToUpdate.eBayReservePricePercentageRequiredToNotifyAdf'/>
        </detail-row>
      </div>
    </editSettingsHelper>
  </div>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import { mapGetters } from 'vuex'
import globals from '../../../../globals'

export default {
  name: 'leads-ebay-bids-notification-send-settings',
  props: {
    isUpdatingProcessed: {
      type: Boolean,
      required: true
    },
    isDisabled: Boolean
  },
  data () {
    return {
      isViewMode: true,
      adminAccountSettingsToUpdate: {}
    }
  },
  created () {
    this.initData()
  },
  computed: {
    ...mapGetters('leadsAccountSettings', ['adminAccountSettings']),
    displayContactState () {
      if (this.adminAccountSettingsToUpdate.eBayPercentRequiredToNotifyDisplayedContact >= 0 && this.adminAccountSettingsToUpdate.eBayPercentRequiredToNotifyDisplayedContact <= 100) {
        return true
      }

      return false
    },
    copyToEmailAddressState () {
      if (this.adminAccountSettingsToUpdate.eBayPercentRequiredToNotifyCopyToEmail >= 0 && this.adminAccountSettingsToUpdate.eBayPercentRequiredToNotifyCopyToEmail <= 100) {
        return true
      }

      return false
    },
    copyToAdfAddressState () {
      if (this.adminAccountSettingsToUpdate.eBayReservePricePercentageRequiredToNotifyAdf >= 0 && this.adminAccountSettingsToUpdate.eBayReservePricePercentageRequiredToNotifyAdf <= 100) {
        return true
      }

      return false
    }
  },
  components: {
    'detail-row': detailRow,
    editSettingsHelper: () => import('@/components/_shared/editSettingsHelper')
  },
  methods: {
    initData () {
      if (this.adminAccountSettings) {
        this.adminAccountSettingsToUpdate = globals().getClonedValue(this.adminAccountSettings)
      }
    },
    saveSettings () {
      if (this.displayContactState && this.copyToEmailAddressState && this.copyToAdfAddressState) {
        this.$emit('save', this.adminAccountSettingsToUpdate)
        this.isViewMode = true
      }
    },
    changeMode (mode) {
      this.isViewMode = mode
    },
    cancel () {
      this.initData()
      this.changeMode(true)
    }
  }
}
</script>
