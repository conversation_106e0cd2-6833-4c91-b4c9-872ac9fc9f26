<template>
  <div v-if="!isException">
    <b-row>
      <b-col>
        <h4 class="float-left" v-if="userModel">{{userModel.firstName}} {{userModel.lastName}}</h4>
      </b-col>
      <b-col>
        <b-btn class="float-right" :to='{name: "leads-messenger", params: {accountId: this.accountId}}' variant="primary">Open Messenger</b-btn>
      </b-col>
    </b-row>
    <b-tabs v-model="selectedTab" class="nav-tabs-top nav-responsive-sm" no-fade>
      <b-tab v-for="(value, key) in tabOptions" :key="key" :title="value.title">
        <div class="p-4 position-relative" v-if="!isLoading && !isException">
          <div v-if="value.key === tabOptions.history.key">
            <div v-if="total > 0">
              <history-listing @archive="archive" :isClosed="isClosed" :phone="userModel.phone" :items="items" @sortChange="sortChange"/>
              <paging
                :pageNumber="filter.page"
                :pageSize="filter.pageSize"
                :totalItems="total"
                titled
                pageSizeSelector
                @numberChanged="onPageChanged"
                @changePageSize="onPageSizeChanged" />
            </div>
            <div v-else>
              <span class="text-muted">Not Found</span>
            </div>
          </div>
          <div v-else-if="value.key === tabOptions.contactInfo.key">
            <contact-info :model='userModel' @updateUser="onUpdateUser" @deleteUser="onDeleteUser"/>
          </div>
        </div>
        <div v-else-if="isLoading && !isException" class="mt-3">
          <loader size="lg"/>
        </div>
      </b-tab>
    </b-tabs>
  </div>
</template>

<script>
import contactInfo from '@/components/leads/userContact/contactInfo'
import historyListing from '@/components/leads/userContact/historyListing'
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import paging from '@/components/_shared/paging.vue'
import loader from '@/components/_shared/loader'
import ConversationService from '@/services/leads/ConversationService'

const defaultValues = new ObjectSchema({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  directions: { type: String, default: '0' },
  tabType: { type: Number, default: 0 }
})

const queryHelper = new QueryStringHelper(defaultValues)

export default {
  name: 'leads-user-contact',
  props: {
    accountId: { type: Number, required: true },
    conversationId: { type: String, required: true }
  },
  data () {
    return {
      tabOptions: { history: { key: 0, title: 'History' }, contactInfo: { key: 1, title: 'Contact Info' } },
      filter: defaultValues.getObject(),
      total: 0,
      items: [],
      isClosed: true,
      userModel: null,
      isLoading: true,
      isException: false,
      siteUrl: ''
    }
  },
  created () {
    this.filter = queryHelper.parseQueryStringToObject(this.$router)
    this.populateSiteInfo().then(() => {
      this.loadContent()
    })
  },
  computed: {
    selectedTab: {
      get () {
        let res = Object.values(this.tabOptions).find(x => x.key === this.filter.tabType)
        if (res) {
          return res.key
        }

        return 0
      },
      set (index) {
        this.filter.tabType = index
        this.synchronizeUrlAndReload()
      }
    }
  },
  components: {
    'contact-info': contactInfo,
    'history-listing': historyListing,
    'paging': paging,
    'loader': loader
  },
  methods: {
    synchronizeUrlAndReload () {
      queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.isLoading = true
      this.loadContent()
    },
    sortChange (newSort) {
      this.filter.sort = newSort
      this.loadContent()
    },
    onPageChanged (newPage) {
      this.filter.page = newPage
      this.synchronizeUrlAndReload()
    },
    onPageSizeChanged (newSize) {
      this.filter.pageSize = newSize
      this.synchronizeUrlAndReload()
    },
    loadContent () {
      ConversationService.getAccountConversationsWithDetails(this.accountId, this.conversationId, this.filter).then(res => {
        this.items = (res.data.conversationDetails || []).map(x => {
          x.vdpLink = this.siteUrl && x.vin ? `${this.siteUrl}/details.aspx?vin=${x.vin}` : ''
          return x
        })
        this.isClosed = res.data.isClosed
        this.total = res.data.total
        this.userModel = res.data.user
      }).catch(ex => {
        this.isException = true
        let statusCode = parseInt(ex.status || (ex.response || {}).status || 0)
        if (statusCode === 400) {
          this.$toaster.error('This contact doesn\'t exist', {timeout: 5000})
        } else {
          this.$toaster.exception(ex, 'Something went wrong!')
          this.$logger.handleError(ex, `Cannot get history listing with conversationId: ${this.conversationId} for accountId: ${this.accountId}`)
        }
      }).finally(() => {
        this.isLoading = false
      })
    },
    async populateSiteInfo () {
      try {
        const siteSettings = await this.$store.dispatch('siteSettings/getSiteSettings', this.accountId)
        this.siteUrl = siteSettings && siteSettings.urlWithProtocol
          ? siteSettings.urlWithProtocol
          : ''
      } catch (ex) {
        this.$logger.handleError(ex, 'Cannot get site info', {accountId: this.accountId})
      }
    },
    onUpdateUser (user) {
      let params = {
        accountId: this.accountId,
        conversationId: this.conversationId,
        user: user
      }
      ConversationService.updateConversationUser(params).then(res => {
        this.$toaster.success('User Successfully Updated')
      }).catch(ex => {
        this.$toaster.error('Cannot update user')
        this.$logger.handleError(ex, `Cannot update user with conversationId: ${this.conversationId} for accountId: ${this.accountId}`, user)
      }).finally(() => {
        this.synchronizeUrlAndReload()
      })
    },
    onDeleteUser () {
      ConversationService.deleteConversation(this.accountId, this.conversationId).then(res => {
        this.$toaster.success('Conversation Successfully Deleted')
      }).catch(ex => {
        this.$toaster.error('Cannot delete conversation')
        this.$logger.handleError(ex, `Cannot delete conversation with id: ${this.conversationId} for accountId: ${this.accountId}`)
      }).finally(() => {
        this.$router.push({ name: 'leads-manager', params: {accountId: this.accountId} })
      })
    },
    archive (id) {
      ConversationService.archiveConversationDetails(this.accountId, this.conversationId, id).then(res => {
        this.$toaster.success('Lead Successfully Archived')
      }).catch(ex => {
        this.$toaster.error('Cannot archive message')
        this.$logger.handleError(ex, `Cannot archive message with conversationId: ${this.conversationId} and conversationDetailsId: ${id} for accountId: ${this.accountId}`)
      }).finally(() => {
        this.loadContent()
      })
    }
  }
}
</script>

<style>
  .vdp-link {
    color: rgba(24, 28, 33, 0.9);
  }
  .vdp-link:hover {
    color: #bf0e16;
  }
</style>
