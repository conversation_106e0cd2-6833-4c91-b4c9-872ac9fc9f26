export default {
  namespaced: true,
  state: {
    id: 0,
    sections: []
  },
  getters: {
    isEditAvailable: state => !(state.sections.reduce((acc, current) => acc || current.isEditMode, false)),
    isEditMode: state => id => state.sections.find(x => x.id === id).isEditMode
  },
  mutations: {
    setSectionId (state, id) {
      state.id = id
    },
    pushSectionId (state, section) {
      state.sections.push({
        ...section,
        isEditMode: false
      })
    },
    setMode (state, payload) {
      let sectionInfo = state.sections.find(x => x.id === payload.id)
      if (sectionInfo) {
        sectionInfo.isEditMode = payload.isEditMode
        if (payload.isEditMode && sectionInfo.onEdit) {
          sectionInfo.onEdit()
        } else if (sectionInfo.onReset) {
          sectionInfo.onReset()
        }
      }
    },
    resetStore (state) {
      state.id = 0
      state.sections = []
    },
    cancelEdit (state) {
      state.sections.forEach(x => {
        x.isEditMode = false
        x.onReset()
      })
    }
  },
  actions: {
    registerSectionId ({ commit, state }, payload) {
      let id = state.id
      commit('pushSectionId', {
        ...payload,
        id: id
      })
      commit('setSectionId', id + 1)

      return id
    }
  }
}
