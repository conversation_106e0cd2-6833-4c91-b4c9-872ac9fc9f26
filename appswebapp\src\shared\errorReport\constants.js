const availableFields = ['id', 'ApplicationName', 'Body', 'ClientIpAddress', 'ErrorCategoryFilter', 'ErrorCategoryLabel', 'ErrorLevelFilter', 'ErrorLevelLabel',
  'ErrorLocalDateTime', 'ErrorTypeFilter', 'ErrorTypeLabel', 'MachineName', 'Referrer', 'RequestInsertedDateTime', 'Subject', 'UserBrowser', 'XForwardedFor']

const errorReportTypes = Object.freeze({
  apps: { value: 1, text: 'Apps' },
  gallery: { value: 2, text: 'Gallery' },
  portals: { value: 3, text: 'Portals' },
  services: { value: 4, text: 'Services' }
})

const errorReportTypesArray = [
  { value: 1, text: 'Apps' },
  { value: 2, text: 'Gallery' },
  { value: 3, text: 'Portals' },
  { value: 4, text: 'Services' }
]

const taskStatus = Object.freeze({
  inProgress: 1,
  completed: 2,
  failed: 3
})

const taskTypes = [{ value: 1, text: 'Error Report' }, { value: 2, text: 'Mongo Query String' }]

const mongoQueryOperators = [
  {value: ' "\\" ', text: 'equals', isInput: true},
  {value: ' { $ne : "\\" } ', text: 'doesn\'t equal', isInput: true},
  {value: ' /.*\\.*/i ', text: 'contains', isInput: true},
  {value: ' { $not : /.*\\.*/i } ', text: 'doesn\'t contain', isInput: true},
  {value: ' null ', text: 'is null', isInput: false},
  {value: ' { $ne : null } }', text: 'isn\'t null', isInput: false},
  {value: ' { $exists : true } ', text: 'exists', isInput: false},
  {value: ' { $exists : false } ', text: 'doesn\'t exist', isInput: false},
  {value: ' { $gt : "\\" } ', text: '>', isInput: true},
  {value: ' { $gte : "\\" } ', text: '>=', isInput: true},
  {value: ' { $lt : "\\" } ', text: '<', isInput: true},
  {value: ' { $lte : "\\" } ', text: '<=', isInput: true}
]

const mongoProjectionOperators = [
  {value: 1, text: 'include'},
  {value: 0, text: 'exclude'}
]

const mongoSortOperators = [
  {value: 1, text: 'ascending'},
  {value: -1, text: 'descending'}
]

const errorCategories = Object.freeze({
  apps: {value: 'apps', text: 'Apps'},
  gallery: {value: 'gallery', text: 'Gallery'},
  portals: {value: 'portals', text: 'Portals'},
  services: {value: 'services', text: 'Services'}
})

const errorLevels = Object.freeze({
  live: {value: 'live', text: 'Live'},
  sandbox: {value: 'sandbox', text: 'Sandbox'},
  dev: {value: 'dev', text: 'Dev'}
})

const errorMonitorTimezone = 'America/Los_Angeles'

export {
  availableFields,
  errorReportTypes,
  errorReportTypesArray,
  taskStatus,
  taskTypes,
  mongoQueryOperators,
  mongoProjectionOperators,
  mongoSortOperators,
  errorCategories,
  errorLevels,
  errorMonitorTimezone
}
