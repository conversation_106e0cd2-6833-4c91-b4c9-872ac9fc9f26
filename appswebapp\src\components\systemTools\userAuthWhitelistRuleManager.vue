<template>
  <b-modal
    size="lg"
    :visible="isVisible"
    @hide="onHide"
  >
    <template #modal-header>
      <div class="w-100 d-flex flex-row align-items-center">
        <h4 class="p-0 m-0">User Auth Whitelist Rules</h4>
        <b-btn v-if="isListingMode" size="sm" class="btn-round ml-1" variant="primary" @click="setAddingMode">Create</b-btn>
        <b-icon @click="onHide" class="ml-auto" style="cursor: pointer" scale="1.3" icon="x"></b-icon>
      </div>
    </template>
    <template v-if="isListingMode">
      <b-table
        v-if="!isLoading"
        :items="items"
        :fields="fields"
        :per-page="pageSize"
        :current-page="currentPage"
        show-empty
        striped
        hover
      >
        <template #cell(manage)="data">
          <b-btn size="sm" variant="secondary" class="w-100" @click="setEditingMode(data.item)">Edit</b-btn>
          <b-btn size="sm" class="w-100 mt-1" @click="deleteOne(data.item)" variant="primary">Delete</b-btn>
        </template>
      </b-table>
      <loader v-else/>
    </template>
    <template v-if="isEditingMode || isAddingMode">
      <detail-row fixed-playload-width>
        <span slot="title">Name:</span>
        <b-form-input slot="payload" v-model="userAuthWhitelistRule.name"></b-form-input>
      </detail-row>
      <detail-row title-position="start" fixed-playload-width>
        <span slot="title">Allow next users to login from the same IP:</span>
        <div slot="payload" class="w-100">
          <b-list-group v-if="userAuthWhitelistRule.users.length > 0" class="user-container mb-2">
            <b-list-group-item variant="light" v-for="(user, i) in userAuthWhitelistRule.users" :key="`user-${i}`" class="d-flex align-items-center">
              {{ user.userName }} Account Id: {{ user.accountId }}
            <b-icon @click="removeUserFromReportRule(user)" class="ml-auto" variant="primary" style="cursor: pointer" scale="1.1" icon="x"></b-icon>
            </b-list-group-item>
          </b-list-group>
          <b-form-input slot="payload" placeholder="Search..." v-model="searchUserSuggestionsPhrase" @input="populateUserSuggestions"></b-form-input>
          <b-overlay
            id="user-suggestion-overlay-background"
            :show="isUserSuggestionLoading"
            rounded="sm"
          >
            <b-list-group v-if="userSuggestions.length > 0" class="user-suggestion-container">
              <b-list-group-item button variant="light" v-for="(userSuggestion, i) in userSuggestions" :key="`user-suggestion-${i}`" @click="appendUserToReportRule(userSuggestion)">
                {{ userSuggestion.userName }} Account Id: {{ userSuggestion.accountId }}
              </b-list-group-item>
            </b-list-group>
          </b-overlay>
        </div>
      </detail-row>
    </template>
    <template #modal-footer>
      <paging
        v-if="!isLoading && isListingMode"
        :pageNumber="currentPage"
        :pageSize="pageSize"
        :totalItems="getTotalItemsCount"
        titled
        @numberChanged="pageChanged"
      />
      <template v-if="isEditingMode || isAddingMode">
        <b-btn size="sm" variant="secondary" @click="cancelChanges">Cancel</b-btn>
        <l-button :loading="isSaveChangesProcessing" size="sm" variant="primary" @click="saveChanges">Save</l-button>
      </template>
    </template>
  </b-modal>
</template>

<script>
import SystemToolsService from '@/services/systemTools/SystemToolsService'
import globals from '../../globals'

export default {
  name: 'user-auth-report-rule-manager',
  props: {
    isVisible: Boolean
  },
  components: {
    'loader': () => import('@/components/_shared/loader'),
    'paging': () => import('@/components/_shared/paging'),
    'detail-row': () => import('@/components/details/helpers/detailRow'),
    'multiSelectWithCheckboxes': () => import('@/components/_shared/multiSelectWithCheckboxes')
  },
  data () {
    return {
      isLoading: true,
      isUserSuggestionLoading: false,
      isSaveChangesProcessing: false,
      items: [],
      currentPage: 1,
      pageSize: 10,
      searchUserSuggestionsPhrase: '',
      userSuggestions: [],
      userAuthWhitelistRule: {
        name: '',
        users: []
      },
      displayModes: {
        listing: 0,
        adding: 1,
        editing: 2
      },
      currentMode: 0
    }
  },
  computed: {
    fields () {
      return [
        {
          key: 'name',
          label: 'Name',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'lastModifiedByUserName',
          label: 'Created By',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle d-flex flex-column',
          thStyle: 'width: 100px;'
        }
      ]
    },
    getTotalItemsCount () {
      return this.items ? this.items.length : 0
    },
    isListingMode () {
      return this.currentMode === this.displayModes.listing
    },
    isAddingMode () {
      return this.currentMode === this.displayModes.adding
    },
    isEditingMode () {
      return this.currentMode === this.displayModes.editing
    }
  },
  methods: {
    populateData () {
      SystemToolsService.getUserAuthWhitelistRules().then(res => {
        this.items = (res.data || {items: []}).items || []
      }).catch(ex => {
        this.$toaster.exception(ex, 'Failed on retrieving data from server', {timeout: 6000})
      }).finally(() => {
        this.isLoading = false
      })
    },
    pageChanged (newPage) {
      this.currentPage = newPage
    },
    onHide () {
      this.resetData()
      this.$emit('hide')
    },
    resetData () {
      this.items = []
      this.currentMode = this.displayModes.listing
    },
    setEditingMode (item) {
      this.userAuthWhitelistRule = globals().getClonedValue(item)
      this.currentMode = this.displayModes.editing
    },
    setAddingMode () {
      this.userAuthWhitelistRule = {name: '', users: []}
      this.currentMode = this.displayModes.adding
    },
    cancelChanges () {
      this.currentMode = this.displayModes.listing
      this.userAuthWhitelistRule = {name: '', users: []}
    },
    saveChanges () {
      if (this.isAddingMode) {
        this.addOne()
      } else if (this.isEditingMode) {
        this.updateOne()
      }
    },
    removeUserFromReportRule (user) {
      let index = this.userAuthWhitelistRule.users.findIndex(x => x.userName === user.userName && x.accountId === user.accountId)
      if (index > 0) {
        this.userAuthWhitelistRule.users.splice(index, 1)
      }
    },
    appendUserToReportRule (user) {
      let existed = this.userAuthWhitelistRule.users.some(x => x.userName === user.userName && x.accountId === user.accountId)
      if (!existed) {
        this.userAuthWhitelistRule.users.push(user)
      }
      this.$set(this, 'searchUserSuggestionsPhrase', '')
      this.$set(this, 'userSuggestions', [])
    },
    deleteOne (item) {
      SystemToolsService.deleteUserAuthWhitelistRule(item.id).then(res => {
        this.$toaster.success('Deleted Successfully')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Failed on deleting user auth report rule')
      }).finally(() => {
        this.populateData()
      })
    },
    updateOne () {
      this.isSaveChangesProcessing = true
      SystemToolsService.updateUserAuthWhitelistRule(this.userAuthWhitelistRule.id, this.userAuthWhitelistRule).then(res => {
        this.$toaster.success('Updated Successfully')
        this.currentMode = this.displayModes.listing
        this.populateData()
      }).catch(ex => {
        this.$toaster.exception(ex, 'Failed on updating user auth report rule')
      }).finally(() => {
        this.isSaveChangesProcessing = false
      })
    },
    addOne () {
      this.isSaveChangesProcessing = true
      SystemToolsService.addNewUserAuthWhitelistRule(this.userAuthWhitelistRule).then(res => {
        this.$toaster.success('Added Successfully')
        this.currentMode = this.displayModes.listing
        this.populateData()
      }).catch(ex => {
        this.$toaster.exception(ex, 'Failed on adding user auth report rule')
      }).finally(() => {
        this.isSaveChangesProcessing = false
      })
    },
    populateUserSuggestions () {
      if (!this.searchUserSuggestionsPhrase) {
        this.userSuggestions = []
      }
      if (this.searchUserSuggestionsPhrase.length < 2) {
        return
      }
      this.isUserSuggestionLoading = true
      SystemToolsService.getUserSuggestions(this.searchUserSuggestionsPhrase).then(res => {
        this.userSuggestions = (res.data || {userSuggestions: []}).userSuggestions || []
      }).finally(() => {
        this.isUserSuggestionLoading = false
      })
    }
  }
}
</script>

<style>
.user-suggestion-container {
  max-height: 200px;
  overflow-y: auto;
}
.user-container {
  max-height: 200px;
  overflow-y: auto;
}
</style>
