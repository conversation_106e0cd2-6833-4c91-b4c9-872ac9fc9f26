﻿using System;
using System.Threading.Tasks;
using EBizAutos.Apps.Authentication.CommonLib.Abstract.Repositories;
using EBizAutos.Apps.Authentication.CommonLib.Models;
using EBizAutos.Apps.Authentication.CommonLib.Utilities.Attributes;
using EBizAutos.Apps.CommonLib.Enums;
using EBizAutos.Apps.CommonLib.Models.Web;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;

namespace EBizAutos.Apps.Shared.Api.Controllers {
	[Route("auth/login_settings")]
	[ApiController]
	[UserAuthorize(AuthenticationEnums.UserPermissionEnum.FullAccess)]
	public class LoginSystemSettingsController : BaseApiController {
		private readonly ILoginSystemSettingsRepository _loginSystemSettingsRepository;

		public LoginSystemSettingsController(IServiceProvider serviceProvider) : base(serviceProvider) {
			_loginSystemSettingsRepository = serviceProvider.GetRequiredService<ILoginSystemSettingsRepository>();
		}

		[HttpGet]
		public async Task<IActionResult> Get() {
			try {
				var settings = await _loginSystemSettingsRepository.GetAsync();
				return ApiResult(PromiseResultModel<LoginSystemSettings>.SuccessResult(settings));
			} catch (Exception ex) {
				return ApiResult(PromiseResultModel<LoginSystemSettings>.ExceptionResult(
					new Exception("Failed to retrieve login system settings.", ex), true));
			}
		}

		[HttpPost]
		public async Task<IActionResult> Update([FromBody] LoginSystemSettings settings) {
			if (settings == null) {
				return BadRequest("Settings cannot be null.");
			}

			//Model validation is performed automatically based on the validation attributes on the LoginSystemSettings class

			try {
				var updatedSettings = await _loginSystemSettingsRepository.UpdateAsync(settings);
				return ApiResult(PromiseResultModel<LoginSystemSettings>.SuccessResult(updatedSettings));
			} catch (Exception ex) {
				return ApiResult(PromiseResultModel<LoginSystemSettings>.ExceptionResult(
					new Exception("Failed to update login system settings.", ex), true));
			}
		}
	}
}