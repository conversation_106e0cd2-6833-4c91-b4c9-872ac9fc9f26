<template>
  <div>
    <channel-segments-summary
      :barItems="bar.items"
      :pieItems="pie.items"
      :summary="summary"
      :barTimeFormat="barTimeFormat"
    ></channel-segments-summary>

    <div v-if="isAccountLevel">
      <account-level-card v-if="isAccountLevel" @backToGroup="onBackToGroup">
        {{accountName}} ({{page.filter.accountId}})
      </account-level-card>

      <channel-segments-by-segment-table
        :tableItems="table.items"
        :sortType="page.filter.sortType"
        @sortTypeChanged="onSortTypeChanged"
      ></channel-segments-by-segment-table>
    </div>
    <div v-else>
      <group-report-tables-selector
        defaultSegmentName="Channel"
        :isSegmentedByAccountSelected="this.page.filter.segmentedByAccount"
        @reportTablesSelectorChanged="onReportTablesSelectorChanged"
      ></group-report-tables-selector>

      <channel-segments-by-segment-table
        v-if="!page.filter.segmentedByAccount"
        :tableItems="table.items"
        :sortType="page.filter.sortType"
        @sortTypeChanged="onSortTypeChanged"
      ></channel-segments-by-segment-table>

      <group-channel-segments-by-account-table
        v-else-if="page.filter.segmentedByAccount"
        :tableItems="table.items"
        :totalItems="table.totalItems"
        :pageNumber="page.filter.pageNumber"
        :pageSize="page.filter.pageSize"
        :sortType="page.filter.sortType"
        @pageNumberChanged="onPageNumberChanged"
        @pageSizeChanged="onPageSizeChanged"
        @sortTypeChanged="onSortTypeChanged"
        @accountNameClicked="onAccountNameClicked"
      ></group-channel-segments-by-account-table>
    </div>
  </div>
</template>

<script>
import analyticsConstants from './../../../shared/analytics/constants'
import analyticsBuilders from './../../../shared/analytics/builders'
import analyticsHelper from '../helpers.js'
import baseGroupReportPage from './baseGroupReportPage.js'

import ChannelSegmentsSummary from '../../../components/analytics_ga4/summaries/channelSegmentsSummary'
import AccountLevelCard from '../../../components/analytics_ga4/accountLevelCard'
import ChannelSegmentsBySegmentTable from '../../../components/analytics_ga4/tables/channelSegmentsBySegmentTable'
import GroupReportTablesSelector from '../../../components/analytics_ga4/groupReportTablesSelector'
import GroupChannelSegmentsByAccountTable from '../../../components/analytics_ga4/tables/groupChannelSegmentsByAccountTable'

const filterManager = analyticsBuilders.getFilterManager({
  accountId: { type: Number, default: 0 },
  segmentedByAccount: { type: Boolean, default: false },
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  pageNumber: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  sortType: { type: Number, default: analyticsConstants.channelSegmentSortTypes.sessionsDesc }
})

export default {
  mixins: [baseGroupReportPage],
  name: 'group-channel-segments',
  metaInfo: {
    title: 'Analytics - Channel Segments'
  },
  components: {
    GroupChannelSegmentsByAccountTable,
    GroupReportTablesSelector,
    ChannelSegmentsBySegmentTable,
    AccountLevelCard,
    ChannelSegmentsSummary
  },
  created () {
    this.$store.commit('analyticsGa4/setAnalyticsName', 'Channel Segments')
    this.$store.commit('analyticsGa4/setBlackThemeOn', true)
  },
  data () {
    return {
      page: {
        filter: filterManager.defaultValue
      },
      summary: {
        sessions: 0,
        sessionsDelta: null,
        pageViews: 0,
        pageViewsDelta: null,
        avgSessionDuration: 0,
        avgSessionDurationDelta: null,
        totalLeads: 0,
        totalLeadsDelta: null
      },
      pie: {
        items: []
      }
    }
  },
  computed: {
    filterManager () {
      return filterManager
    },
    defaultAccountLevelSortType () {
      return filterManager.defaultValue.sortType
    }
  },
  methods: {
    getAccountNameSortTypeCondition () {
      return this.page.filter.sortType === analyticsConstants.channelSegmentSortTypes.accountNameAsc ||
        this.page.filter.sortType === analyticsConstants.channelSegmentSortTypes.accountNameDesc
    },
    getReportTablesSelectorSortTypeCondition () {
      return this.page.filter.sortType === analyticsConstants.channelSegmentSortTypes.accountNameAsc ||
        this.page.filter.sortType === analyticsConstants.channelSegmentSortTypes.accountNameDesc ||
        this.page.filter.sortType === analyticsConstants.channelSegmentSortTypes.channelGroupingAsc ||
        this.page.filter.sortType === analyticsConstants.channelSegmentSortTypes.channelGroupingDesc
    },
    async updateStatistics () {
      try {
        await Promise.all([
          !this.isAccountLevel ? this.updateGroupLevelStatistics() : null,
          !this.isAccountLevel && this.page.filter.segmentedByAccount ? this.updateSegmentedByAccountTable() : null,
          this.isAccountLevel ? this.updateAccountLevelStatistics() : null
        ])
      } catch (err) {
        this.$toaster.error('Failed to get data from server', { timeout: 8000 })
        this.$logger.handleError(err, 'Can\'t update statistics', { filter: this.page.filter, cache: this.cache })
      }
    },
    async updateGroupLevelStatistics () {
      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getGroupChannelSegmentsReport',
        {
          reportGroupId: this.reportGroupId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo,
          sortType: analyticsConstants.channelSegmentSortTypes.sessionsDesc
        }
      )

      this.summary = {
        ...this.summary,
        ...store.summary.data
      }

      this.bar.items = store.graph.data
      this.pie.items = store.detailedData.data.items
      if (!this.page.filter.segmentedByAccount) {
        this.table.items = store.detailedData.data.items
      }
    },
    async updateSegmentedByAccountTable () {
      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getGroupChannelSegmentsByAccount',
        {
          reportGroupId: this.reportGroupId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo,
          pageNumber: this.page.filter.pageNumber,
          pageSize: this.page.filter.pageSize,
          sortType: this.page.filter.sortType
        }
      )

      this.table.items = store.detailedData.data.items.map(x => {
        x.account.isAccessAllowed = this.isAccessToAccountAllowed(x.account.accountId)
        return x
      })
      this.table.totalItems = store.detailedData.data.totalItems
    },
    async updateAccountLevelStatistics () {
      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getChannelSegmentsReport',
        {
          accountId: this.page.filter.accountId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo,
          sortType: analyticsConstants.channelSegmentSortTypes.sessionsDesc
        }
      )

      this.summary = { ...store.summary.data }
      this.bar.items = store.graph.data
      this.pie.items = store.detailedData.data.items
      this.table.items = store.detailedData.data.items
    }
  }
}
</script>

<style src="@/vendor/styles/pages/analytics.scss" lang="scss"></style>
