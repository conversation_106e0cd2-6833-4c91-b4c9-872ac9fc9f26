<template>
  <b-form-input :class="{ 'price-editor-border price-bg-transparent' : !active }" :size="size" v-model="moneyValue"
    type="text"
    :placeholder="placeholder"
    @keydown.native="handleKeyDown"></b-form-input>
</template>

<script>
export default {
  name: 'sign-input',
  props: {
    sign: {
      type: [String, Number, Symbol],
      default: '$'
    },
    placeholder: String,
    active: Boolean,
    value: [String, Number, Boolean, Symbol],
    size: String
  },
  computed: {
    moneyValue: {
      get () {
        return '$' + `${this.value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')}` // 1000000 => $1,000,000
      },
      set (value) {
        let number = +value.toString().replace(/\D/g, '') // $1,000,000 => 1000000
        this.$emit('input', number)
      }
    }
  },
  methods: {
    handleKeyDown (e) {
      // key.length = 1 mean that it's a letter not a control button
      if (e.key && e.key.length === 1 && !Number.isInteger(+e.key)) {
        e.preventDefault()
      }
    }
  }
}
</script>

<style lang="scss">
.price-editor-border {
  &:not(:focus):not(:hover) {
    border-color: rgba(0,0,0,0);
  }
}

.price-bg-transparent {
  transition: background-color 200ms linear;
  &:not(:focus):not(:hover){
    background-color: transparent;
  }
}
</style>
