<template>
  <div>
    <div v-if="isLoaded">
      <h4>Craigslist log detail ({{accountId}})</h4>
      <b-card>
        <log-node
          v-if="log"
          :data="log"
          :isExpandedShallow="true"
          :isExpandedDeep="false"
        />
      </b-card>
    </div>
    <div class="ml-4 font-weight-bolder" v-else-if="!isLoaded">loaded...</div>
    <div class="ml-4 font-weight-bolder" v-else>Not found</div>
  </div>
</template>
<script>
export default {
  name: 'craigslit-detail-log',
  metaInfo: {
    title: 'Craigslist detail log'
  },
  props: {
    accountId: {
      type: Number,
      required: true
    },
    guid: {
      type: String,
      required: true
    }
  },
  components: {
    'log-node': () => import('@/components/_shared/logItemNode.vue')
  },
  data () {
    return {
      log: null,
      isLoaded: false
    }
  },
  mounted () {
    if (this.accountId && this.guid) {
      this.loadContent()
    }
  },
  methods: {
    loadContent () {
      this.$store.dispatch('craigslist/getDetailLog', {accountId: this.accountId, guid: this.guid}).then(x => {
        this.log = {
          nodes: x.data.model.decomposedObjectNodes
        }
        this.isLoaded = true
      })
        .catch(ex => this.$logger.handleError(ex, 'Can\'t get craigslist detail log', {accountId: this.accountId, guid: this.guid}))
    }
  }
}
</script>>
