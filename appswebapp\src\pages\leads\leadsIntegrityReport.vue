<template>
  <div>
    <b-row>
      <b-col>
        <h4>Integrity Report</h4>
      </b-col>
      <b-col v-if="isAdmin">
        <c-button variant="primary" class="float-right" message='Are you sure? Do you want to initiate new verification' @confirm="initiateNewVerification">
          Initiate New Verification
        </c-button>
        <span class="text-muted float-right mt-2 mr-2">Last run at {{lastRunDate}}</span>
      </b-col>
    </b-row>
    <b-tabs v-model='selectedTab' class="nav-tabs-top nav-responsive-sm" no-fade>
      <b-tab v-for='tab in tabOptions' :key='tab.key' :title='tab.title'>
        <div>
          <b-input-group class="leads-custom-width flex-nowrap">
            <b-form-input v-model='filter.search' :placeholder='tab.placeholder'></b-form-input>
            <b-input-group-append>
              <b-btn @click="applySearch" variant="primary">Go</b-btn>
            </b-input-group-append>
          </b-input-group>
        </div>
      </b-tab>
    </b-tabs>
    <b-card v-if='!isLoading && items.length > 0'>
      <leads-integrity-report-listing @delete='deleteReport' :type='selectedTab' :items='items'/>
      <paging
        class="p-0"
        :pageNumber="filter.page"
        :pageSize="filter.pageSize"
        :totalItems="itemsTotalCount"
        titled
        pageSizeSelector
        @numberChanged="onPageChanged"
        @changePageSize="onChangePageSize"
      />
    </b-card>
    <div v-else-if='isLoading' class="mt-5">
      <loader size='lg'/>
    </div>
    <div v-else>
      <span class="text-muted">No Found</span>
    </div>
  </div>
</template>

<script>
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import { integrityReportTabTypes } from '@/shared/leads/common'
import loader from '@/components/_shared/loader'
import leadsIntegrityReportListing from '@/components/leads/integrityReport/leadsIntegrityReportListing'
import IntegrityReportService from '@/services/leads/IntegrityReportService'
import moment from 'moment'
import { mapGetters } from 'vuex'

const defaultFilters = new ObjectSchema({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  search: { type: String, default: '' },
  tab: { type: Number, default: 0 }
})

const queryStringHelper = new QueryStringHelper(defaultFilters)

export default {
  name: 'leads-integrity-report',
  metaInfo: {
    title: 'Integrity Report'
  },
  data () {
    return {
      tabOptions: [
        {key: '0', title: 'Campaigns missed on Twilio', placeholder: 'Search by Phone, Campaign or Account'},
        {key: '1', title: 'Dealer\'s Twilio phones missed in Campaigns', placeholder: 'Search by Phone'},
        {key: '2', title: 'User\'s Twilio phones missed in Conversations', placeholder: 'Search by Phone'},
        {key: '3', title: 'Not Matched Emails', placeholder: 'Search by Proxy Email'}
      ],
      isLoading: true,
      items: [],
      itemsTotalCount: 0,
      lastRunDate: '',
      filter: defaultFilters.getObject()
    }
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {}
    },
    isAdmin () {
      return this.user && this.user.isEbizAdmin
    },
    selectedTab: {
      get () {
        if (this.filter.tab >= 0 && this.filter.tab <= 4) {
          return this.filter.tab
        }

        return 0
      },
      set (index) {
        this.filter.tab = index
        this.filter.page = 1
        this.items = []
        this.itemsTotalCount = 0
        this.synchronizeUrlAndReload()
      }
    }
  },
  components: {
    'leads-integrity-report-listing': leadsIntegrityReportListing,
    'paging': () => import('@/components/_shared/paging'),
    'loader': loader
  },
  created () {
    this.filter = queryStringHelper.parseQueryStringToObject(this.$router)
    this.loadContent()
  },
  methods: {
    onPageChanged (newPage) {
      this.filter.page = newPage
      this.synchronizeUrlAndReload()
    },
    onChangePageSize (newSize) {
      this.filter.pageSize = newSize
      this.filter.page = 1
      this.synchronizeUrlAndReload()
    },
    applySearch () {
      this.filter.page = 1
      this.synchronizeUrlAndReload()
    },
    synchronizeUrlAndReload () {
      queryStringHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.isLoading = true
      this.loadContent()
    },
    initiateNewVerification () {
      IntegrityReportService.startReportProcessing().then(res => {
        this.$toaster.success('New verification Successfully Initiated')
      }).catch(ex => {
        if (ex.response.data) {
          this.$toaster.error(`Cannot initiate new verification Message: ${ex.response.data}`)
        } else {
          this.$toaster.error(`Cannot initiate new verification. Message: ${ex.message}`)
        }
        this.$logger.handleError(ex, 'Cannot initiate new verification')
      })
    },
    deleteReport (id) {
      IntegrityReportService.deleteReportItem(id).then(res => {
        if (res.data) {
          this.$toaster.success(res.data)
        }
      }).catch(ex => {
        this.$toaster.error(`Cannot delete report item. Message: ${ex.response.data}`)
        this.$logger.handleError(ex, `Cannot delete report item with id: ${id}`)
      }).finally(() => {
        this.synchronizeUrlAndReload()
      })
    },
    loadContent () {
      switch (this.selectedTab) {
        case integrityReportTabTypes.missedTwilioConversations.value:
          this.populateMissedTwilioConversations()
          break
        case integrityReportTabTypes.missedTwilioCampaigns.value:
          this.populateMissedTwilioCampaigns()
          break
        case integrityReportTabTypes.missedTwilioPhones.value:
          this.populateMissedTwilioPhones()
          break
        case integrityReportTabTypes.notMatchedEmails.value:
          this.populateNotMatchedEmails()
          break
      }
    },
    populateMissedTwilioConversations () {
      IntegrityReportService.getMissedTwilioConversations(this.filter).then(res => {
        this.setData(res.data)
      }).catch(ex => {
        this.$logger.handleError(ex, 'Cannot get missed twilio conversations', this.filter)
      }).finally(() => {
        this.isLoading = false
      })
    },
    populateMissedTwilioCampaigns () {
      IntegrityReportService.getMissedTwilioCampaigns(this.filter).then(res => {
        this.setData(res.data)
      }).catch(ex => {
        this.$logger.handleError(ex, 'Cannot get missed twilio campaigns', this.filter)
      }).finally(() => {
        this.isLoading = false
      })
    },
    populateMissedTwilioPhones () {
      IntegrityReportService.getMissedTwilioPhones(this.filter).then(res => {
        this.setData(res.data)
      }).catch(ex => {
        this.$logger.handleError(ex, 'Cannot get missed twilio phones', this.filter)
      }).finally(() => {
        this.isLoading = false
      })
    },
    populateNotMatchedEmails () {
      IntegrityReportService.getNotMatchedEmails(this.filter).then(res => {
        this.setData(res.data)
      }).catch(ex => {
        this.$logger.handleError(ex, 'Cannot get not matched emails', this.filter)
      }).finally(() => {
        this.isLoading = false
      })
    },
    setData (data) {
      this.items = data.reportItems
      this.itemsTotalCount = data.totalItemsCount
      this.lastRunDate = moment(data.lastRunDate).format('MM/DD/YYYY')
    }
  }
}
</script>

<style>
.leads-custom-width {
  width: 500px;
  margin: 25px 20px 25px 20px;
}
@media(max-width: 576px) {
  .leads-custom-width {
    width: auto;
    margin: 15px 10px 15px 10px;
  }
}
</style>
