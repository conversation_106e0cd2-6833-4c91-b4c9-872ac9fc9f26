export default {
  userActivitySortTypes: Object.freeze({
    userNameAsc: 1,
    userNameDesc: 2,
    actionAsc: 3,
    actionDesc: 4,
    operationResultAsc: 5,
    operationResultDesc: 6,
    dateAsc: 7,
    dateDesc: 8,
    userTypeAsc: 9,
    userTypeDesc: 10,
    ipAddressAsc: 11,
    ipAddressDesc: 12
  }),
  userActionTypes: Object.freeze({
    undefined: { value: 0, text: 'All Actions' },
    encrypt: { value: 1, text: 'Encrypt' },
    decrypt: { value: 2, text: 'Decrypt' },
    runICRVehicleReportRequest: { value: 12, text: 'Run Instant Carfax Report Request' },
    postTestVehicleToCL: { value: 13, text: 'Post Test Vehicle to CL' },
    cancelGeneratingErrorReport: { value: 5, text: 'Cancel Generating Error Report' },
    createErrorRule: { value: 7, text: 'Create Error Rule' },
    deleteErrorRule: { value: 9, text: 'Delete Error Rule' },
    generateErrorReport: { value: 4, text: 'Generate Error Report' },
    generateToken: { value: 3, text: 'Generate Token' },
    runCustomErrorsScript: { value: 10, text: 'Run Custom Errors Script' },
    sendErrorReportOnEmail: { value: 11, text: 'Send Error Report on Email' },
    updateErrorReport: { value: 6, text: 'Update Error Report' },
    updateErrorRule: { value: 8, text: 'Update Error Rule' }
  }),
  userAuthStatus: Object.freeze({
    undefined: { value: 0, text: 'Undefined' },
    success: { value: 1, text: 'Success' },
    rejected: { value: 2, text: 'Rejected' },
    errorOccurred: { value: 3, text: 'Error Occurred' },
    invalidCredentials: { value: 4, text: 'Invalid Credentials' }
  }),
  userAuthLogSortTypes: Object.freeze({
    userNameAsc: 1,
    userNameDesc: 2,
    ipAddressAsc: 3,
    ipAddressDesc: 4,
    accountNameAsc: 5,
    accountNameDesc: 6,
    dateAsc: 7,
    dateDesc: 8,
    statusAsc: 9,
    statusDesc: 10,
    applicationAsc: 11,
    applicationDesc: 12
  })
}
