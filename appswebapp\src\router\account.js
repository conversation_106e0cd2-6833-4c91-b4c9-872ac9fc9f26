import Layout2 from '@/layout/Layout2'
import LayoutBlank from '@/layout/LayoutBlank'
import permissions from '../shared/common/permissions'

export default [{
  // Layout 2
  path: '/accounts',
  component: Layout2,
  props: (route) => ({ accountId: +route.params.accountId }),
  children: [
    {
      path: '',
      name: 'account-listing',
      meta: {
        permissions: [permissions.ManageUserAccount],
        manageMultipleAccounts: true
      },
      component: () => import('@/pages/account/accountsListing')
    },
    {
      path: 'groups',
      component: () => import('@/pages/account/groups/groups'),
      props: (route) => ({ accountId: +route.params.accountId }),
      name: 'account-groups',
      meta: {
        permissions: [permissions.AMFullAccess],
        applicationFullAccess: permissions.AMFullAccess
      }
    },
    {
      path: 'groups/:groupId',
      component: () => import('@/pages/account/groups/groupmanagament'),
      props: (route) => ({ groupId: route.params.groupId }),
      meta: {
        permissions: [permissions.AMFullAccess]
      }
    },
    {
      path: 'groups/:groupId/accountpermissions/:accountId(\\d+)',
      component: () => import('@/pages/account/groups/accountgrouppermissions'),
      props: (route) => ({ groupId: route.params.groupId, accountId: +route.params.accountId }),
      meta: {
        permissions: [permissions.AMFullAccess]
      }
    },
    {
      path: 'reports',
      name: 'reports',
      component: () => import('@/pages/account/reports/reports'),
      meta: {
        permissions: [permissions.ViewReports]
      }
    },
    {
      path: 'useractivity',
      name: 'accounts-user-activity',
      component: () => import('@/pages/account/userActivity'),
      meta: {
        permissions: [permissions.ViewLogs]
      }
    },
    {
      path: 'useractivity/:type(\\d+)/:logId/details',
      name: 'accounts-user-activity-details',
      component: () => import('@/pages/account/userActivityDetails'),
      props: (route) => ({logId: route.params.logId, type: +route.params.type}),
      meta: {
        permissions: [permissions.ViewLogs]
      }
    },
    {
      path: ':accountId(\\d+)/settings',
      name: 'account-settings',
      component: () => import('@/pages/account/accountSettings'),
      props: (route) => ({ accountId: +route.params.accountId }),
      meta: {
        permissions: [permissions.EbizAutosAdmin]
      }
    }]
}, {
  // LayoutBlank
  path: '/account',
  component: LayoutBlank,
  children: [{
    path: 'login',
    name: 'login',
    component: () => import('@/pages/authentication/login')
  }, {
    path: 'password',
    name: 'password',
    component: () => import('@/pages/authentication/password')
  }, {
    path: 'emailconfirm',
    name: 'emailConfirm',
    component: () => import('@/pages/authentication/emailconfirm')
  }, {
    path: 'register',
    name: 'register',
    component: () => import('@/pages/authentication/register')
  }]
}]
