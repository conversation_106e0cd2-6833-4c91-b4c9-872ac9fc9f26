import axios from 'axios'
import AccountStatuses from '../../shared/accounts/accountStatuses'

const actions = {
  moveAccountToPending (_, parameters) {
    return axios.post(`/api/accounts/${parameters.accountId}/settings`, {
      AccountId: parameters.accountId,
      AccountStatus: AccountStatuses.Pending.Value
    })
  },
  moveAccountToOnHold (_, parameters) {
    return axios.post(`/api/accounts/${parameters.accountId}/settings`, {
      AccountId: parameters.accountId,
      AccountStatus: AccountStatuses.OnHold.Value
    })
  },
  moveAccountToActive (_, parameters) {
    return axios.post(`/api/accounts/${parameters.accountId}/settings`, {
      AccountId: parameters.accountId,
      AccountStatus: AccountStatuses.Active.Value
    })
  },
  closeAccount (_, parameters) {
    return axios.post(`/api/accounts/${parameters.accountId}/close`)
  },
  getAccountListing (_, parameters) {
    return axios.get(`/api/accounts/list/${parameters.applicationType}`, { params: parameters.filters })
  },
  getDealerInfo (_, parameters) {
    return axios.get(`/api/accounts/${parameters.accountId}/dealerinfo`)
  },
  getFranchiseMakes (_, parameters) {
    return axios.get(`/api/accounts/${parameters.accountId}/franchise_makes`)
  },
  getRelatedAccounts (_, parameters) {
    return axios.get(`/api/accounts/${parameters.accountId}/relatedaccounts`)
  },
  getInventorySettings (_, parameters) {
    return axios.get(`/api/inventory/${parameters.accountId}/settings`)
  },
  updateInventorySettings (_, parameters) {
    return axios.post(`/api/inventory/${parameters.accountId}/settings`, JSON.parse(JSON.stringify(parameters.inventorySettings)))
  },
  updateInventoryPhotoSettings (_, parameters) {
    return axios.post(`/api/inventory/${parameters.accountId}/settings/photo`, JSON.parse(JSON.stringify(parameters.photoSettings)))
  },
  updateInventorySearchSettings (_, parameters) {
    return axios.post(`/api/inventory/${parameters.accountId}/settings/search`, JSON.parse(JSON.stringify(parameters.searchSettings)))
  },
  getCarfaxCredentialsSettings (_, parameters) {
    return axios.get(`/api/inventory/${parameters.accountId}/settings/carfax/credentials`)
  },
  carfaxLogout (_, parameters) {
    return axios.post(`/api/inventory/settings/carfax/logout?account_id=${parameters.accountId}`)
  },
  updateAccountCarfaxSettingsState (_, parameters) {
    return axios.post(`/api/inventory/settings/carfax/login/state`, { accountId: parameters.accountId, redirectUrl: parameters.redirectUrl, callbackUrl: parameters.callbackUrl })
  },
  isAccountExists (_, parameters) {
    return axios.get(`/api/accounts/${parameters.accountId}/exists`)
  }
}

export default {
  namespaced: true,
  actions: actions
}
