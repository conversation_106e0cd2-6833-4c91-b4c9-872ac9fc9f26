<template>
  <div class="d-flex flex-column p-3">
    <div class="filter-section d-flex flex-row">
      <span name="custom-btn-close" class="leads-custom-btn-close" @click="onHideFilterListingSection"><i class="ion ion-ios-close h5 m-0" style="font-size: 18px;"></i></span>
      <b-form-input v-model="filter.search" placeholder="Search..."></b-form-input>
    </div>
    <div class='mt-2 d-flex flex-row filter-section'>
      <b-form-checkbox
        v-for="option in selectOptions"
        v-model="selectedCheckbox"
        :key="option.value"
        :value="option.value"
        inline
      >
        {{ option.text }}
      </b-form-checkbox>
    </div>
    <div class="mt-2 d-flex flex-row filter-section">
      <b-form-radio-group
      class="custom-messenger-filter-radio-group"
      v-model='filter.sort'
      :options='radioOptions'
      buttons
      button-variant="outline-secondary"
      @change='changeFilter'
      >
      </b-form-radio-group>
      <div class="pl-2 py-2 d-flex">
        <b-button size="sm" @click="showExtraFilterOption" :class="getExtraFilterBtnClass" variant="outline-secondary icon-btn btn-round"><i class="ion ion-ios-more m-0"></i><span class="sr-only">More</span></b-button>
      </div>
    </div>
    <div class="mt-2" :class="getExtraFilterOptionClass">
      <multiselect
        v-if="filterOptions.campaignTypes && filterOptions.campaignTypes.length > 0"
        v-model='selectedCampaigns'
        :options='filterOptions.campaignTypes'
        label="campaignTypeName"
        trackBy="campaignTypeId"
        placeholder="All Campaigns"
        :multiple="true"
        :limit="1"
        :searchable="false"
        :closeOnSelect="false"
        :showLabels='false'
        :showPointer='false'
      ></multiselect>
      <multiselect
        v-if="filterOptions.leadTypes && filterOptions.leadTypes.length > 0"
        class="mt-1"
        v-model='selectedLeadTypes'
        :options='filterOptions.leadTypes'
        trackBy="leadTypeId"
        label="leadTypeName"
        placeholder="All Lead Types"
        :multiple="true"
        :limit="1"
        :searchable="false"
        :closeOnSelect="false"
        :showLabels='false'
        :showPointer='false'
      >
      </multiselect>
      <div class="d-flex flex-row mt-1">
        <date-time-picker
          ref="timeFrom"
          v-model="filter.dateFrom"
          class="custom-date-picker"
          :options='filterTimeOptions'
          format="MM/DD/YYYY"
          @change="onTimeFromInputChange"
        >
        </date-time-picker>
        <span class="h6 mx-2 my-2">to</span>
        <date-time-picker
          ref="timeTo"
          v-model="filter.dateTo"
          class="custom-date-picker"
          :options='filterTimeOptions'
          format="MM/DD/YYYY"
          @change="onTimeToInputChange"
        >
        </date-time-picker>
      </div>
    </div>
  </div>
</template>

<script>
import { communicationTypes } from '@/shared/leads/common'
import Multiselect from 'vue-multiselect'

export default {
  name: 'leads-messenger-filter',
  props: {
    filterOptions: { type: Object, required: true },
    filter: { type: Object, required: true }
  },
  data () {
    return {
      communicationTypes,
      selectedCampaigns: [],
      selectedLeadTypes: [],
      disabled: false,
      isExtraFilterOptionShow: false,
      radioOptions: [{text: 'Latest', value: 1, class: ''}, {text: 'All', value: 2}],
      selectOptions: [
        {text: 'Email', value: communicationTypes.email.value},
        {text: 'Phone', value: communicationTypes.voice.value},
        {text: 'SMS', value: communicationTypes.sms.value}
      ],
      filterTimeOptions: {
        autoUpdateInput: false,
        singleDatePicker: true,
        maxDate: new Date()
      }
    }
  },
  mounted () {
    this.initData()
  },
  computed: {
    getExtraFilterOptionClass () {
      if (this.isExtraFilterOptionShow) {
        return 'extra-filter-option-section-show-mode'
      }

      return 'extra-filter-option-section-hide-mode'
    },
    getExtraFilterBtnClass () {
      if (this.isExtraFilterOptionShow) {
        return 'extra-filter-btn-active'
      }

      return ''
    },
    refDateTimeFrom () {
      return (this.$refs.timeFrom || {}).$el || {}
    },
    refDateTimeTo () {
      return (this.$refs.timeTo || {}).$el || {}
    },
    selectedCheckbox: {
      get () {
        if (this.filter.communicationtypes.length > 0) {
          return this.filter.communicationtypes.split(',')
        }
        return ''
      },
      set (value) {
        this.filter.communicationtypes = value.toString()
      }
    }
  },
  components: {
    'date-time-picker': () => import('@gravitano/vue-date-range-picker/src/components/DateRangePicker'),
    'multiselect': Multiselect
  },
  methods: {
    onHideFilterListingSection () {
      this.$emit('hideFilterListingSection')
    },
    showExtraFilterOption () {
      this.isExtraFilterOptionShow = !this.isExtraFilterOptionShow
      this.$emit('changeFilterMode', this.isExtraFilterOptionShow)
    },
    onTimeFromInputChange (newVal) {
      this.refDateTimeFrom.value = newVal || this.filter.dateFrom || null
    },
    onTimeToInputChange (newVal) {
      this.refDateTimeTo.value = newVal || this.filter.dateTo || null
    },
    changeFilter () {
      this.$emit('filterChange', this.filter)
    },
    selectedCampaignsChange () {
      if (this.selectedCampaigns.length > 0) {
        this.filter.campaigntypes = this.selectedCampaigns.map(x => x.campaignTypeId).toString()
      } else {
        this.filter.campaigntypes = ''
      }
    },
    selectedLeadTypesChange () {
      if (this.selectedLeadTypes.length > 0) {
        this.filter.leadtypes = this.selectedLeadTypes.map(x => x.leadTypeId).toString()
      } else {
        this.filter.leadtypes = ''
      }
    },
    initData () {
      if (this.filter.leadtypes.length > 0) {
        for (const id of this.filter.leadtypes.split(',')) {
          let res = this.filterOptions.leadTypes.find(x => x.leadTypeId === Number.parseInt(id))
          if (res) {
            this.selectedLeadTypes.push(res)
          }
        }
      }
      if (this.filter.campaigntypes.length > 0) {
        for (const id of this.filter.campaigntypes.split(',')) {
          let res = this.filterOptions.campaignTypes.find(x => x.campaignTypeId === Number.parseInt(id))
          if (res) {
            this.selectedCampaigns.push(res)
          }
        }
      }
    }
  },
  watch: {
    'filter': {
      deep: true,
      handler: function () {
        this.changeFilter()
      }
    },
    'selectedCampaigns': {
      deep: true,
      handler: function () {
        this.selectedCampaignsChange()
      }
    },
    'selectedLeadTypes': {
      deep: true,
      handler: function () {
        this.selectedLeadTypesChange()
      }
    }
  }
}
</script>

<style>
.filter-section {
  width: 215px;
}

.custom-messenger-filter-radio-group .btn {
  width: 90px;
}
.custom-date-picker {
  width: 92px;
}

.extra-filter-option-section-hide-mode {
  display: none;
}
.extra-filter-option-section-show-mode {
  display: flex;
  flex-shrink: 1;
  flex-direction: column;
  width: 215px;
}
.extra-filter-btn-active {
  background: #818fa2;
  color: white;
}

.leads-custom-btn-close {
  font-weight: bold;
  margin-right: 0.5rem;
  margin-top: 0.5rem;
  display: none;
  cursor: pointer;
}
@media (max-width: 992px) {
  .leads-custom-btn-close {
    display: block;
    border-radius: 50%;
  }
}
</style>
