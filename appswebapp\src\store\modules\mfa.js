import axios from 'axios'

export default {
  namespaced: true,
  state: {
    challengeId: null,
    availableMethods: [],
    selectedMethod: null
  },
  mutations: {
    setChallenge (state, payload) {
      state.challengeId = payload.challengeId
      state.availableMethods = payload.availableMethods || []
    },
    setSelectedMethod (state, methodName) {
      state.selectedMethod = methodName
    },
    clear (state) {
      state.challengeId = null
      state.availableMethods = []
      state.selectedMethod = null
    }
  },
  getters: {
    maskedDestination: (state) => {
      if (!state.selectedMethod) {
        // Fallback to first available method if no method is selected
        const firstMethod = state.availableMethods[0]
        return firstMethod ? firstMethod.maskedDestination : ''
      }
      const selectedMethodObj = state.availableMethods.find(m => m.name === state.selectedMethod)
      return selectedMethodObj ? selectedMethodObj.maskedDestination : ''
    },
    selectedMethodType: (state) => {
      if (!state.selectedMethod) {
        const firstMethod = state.availableMethods[0]
        return firstMethod ? firstMethod.name : 'email'
      }
      return state.selectedMethod
    }
  },
  actions: {
    send ({ state, commit }, methodName = 'email') {
      const method = state.availableMethods.find(m => m.name === methodName)
      if (!method) {
        throw new Error('Selected authentication method not found')
      }
      // Store the selected method for use in verify page
      commit('setSelectedMethod', methodName)
      return axios.post('/api/auth/mfa/send', {
        ChallengeId: state.challengeId,
        Method: method.type
      })
    },
    verify ({ state, commit, dispatch }, code) {
      return axios.post('/api/auth/mfa/verify', {
        ChallengeId: state.challengeId,
        Code: code,
        HasToCompleteSignIn: true
      })
    }
  }
}
