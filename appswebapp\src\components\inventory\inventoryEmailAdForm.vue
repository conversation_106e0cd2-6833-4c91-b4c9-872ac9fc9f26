<template>
  <div>
    <b-card>
      <b-row>
        <b-col cols="12" class="font-weight-bold mb-1">H1 Page Title:</b-col>
        <b-col xl="5" lg="6" md="6" sm="12">
          <b-form-input v-model="h1EmailPageTitle" placeholder="Enter Custom Title"></b-form-input>
        </b-col>
      </b-row>
      <b-row class="mt-2">
        <b-col cols="12" class="font-weight-bold mb-1">Selected Vehicles:</b-col>
        <b-col>
          <div class="d-flex flex-row flex-wrap">
            <div class="d-flex flex-row align-items-center bg-light text-dark selected-vehicle" v-for="vehicle in vehiclesToGenerateEmailPage" :key="vehicle.vin">
              <span class="mx-1">{{vehicle.stockNumber}}</span>
              <b-icon style="cursor: pointer; color: white;" icon="x-circle-fill" variant="dark" @click="removeVehicleFromVehiclesToGenerateEmailPage(vehicle.vin)"></b-icon>
            </div>
          </div>
        </b-col>
      </b-row>
    </b-card>
    <b-card no-body>
      <inventory-filters-form
        :buttons="getFilterOptions"
        :search="inventoryFilters.search"
        :variant="getFiltersFormVariant"
        @changeActive="onChangeActiveButton"
        @searchChanged="onChangeSearch"
      />
    </b-card>
    <b-card>
      <b-table
        v-if="isLoadedVehicles"
        :items="inventoryData.vehicles"
        :fields="fields"
        :sort-by.sync="sortBy"
        :sort-desc.sync="sortDesc"
        @sort-changed="sortingChanged"
        show-empty
        striped
        hover
        responsive="sm"
      >
        <template #cell(title)="data">
          <div class="media align-items-center">
            <img class="d-block float-left ui-w-50 mr-3" style="min-height: 1px" :src="getVehiclePhotoSrc(data.item)">
            <span>{{getVehicleTitle(data.item)}}</span>
          </div>
        </template>
        <template #cell(selection)="data">
          <b-form-checkbox @change="addOrRemoveVin(data.item)" :checked="isVinChecked(data.item.vin)" :disabled="isCheckBoxDisabled(data.item.vin)"></b-form-checkbox>
        </template>
      </b-table>
      <loader class="my-4" v-else size="lg"/>
      <paging
        :pageNumber="inventoryData.inventoryFilters.pageNumber"
        :pageSize="inventoryData.inventoryFilters.pageSize"
        :totalItems="inventoryData.vehiclesTotalCount"
        pageSizeSelector
        titled
        @numberChanged="onPageChanged"
        @changePageSize="onPageSizeChanged"
      />
    </b-card>
  </div>
</template>

<script>
import inventoryFiltersForm from '@/components/inventory/inventoryFiltersForm'
import loader from '@/components/_shared/loader'
import defaultInventoryFilters from '@/shared/inventory/inventoryFilters'
import inventoryService from '@/services/inventory/InventoryService'
import vehicleStatisticsMixin from '@/mixins/inventory/vehicleStatisticsMixin'
import inventoryFiltersMixin from '@/mixins/inventory/inventoryFiltersMixin'
import inventorySortMixin from '@/mixins/inventory/inventorySortMixin'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import paging from '@/components/_shared/paging.vue'
import conditions from '@/shared/common/vehicle/vehicleConditions'
import ConditionFilter from '@/shared/inventory/filterOption/conditionFilter'
import InventoryFiltersBuilder from '@/shared/inventory/inventoryFiltersBuilder'
import numeral from 'numeral'

const queryStringHelper = new QueryStringHelper(defaultInventoryFilters)
let filtersBuilder = null

const EMAIL_VEHICLES_LIMIT = 30

export default {
  name: 'inventory-email-page-form',
  mixins: [vehicleStatisticsMixin, inventoryFiltersMixin, inventorySortMixin],
  components: {
    'inventory-filters-form': inventoryFiltersForm,
    'paging': paging,
    'loader': loader
  },
  props: {
    value: { type: Object, required: true },
    accountId: { type: Number, required: true }
  },
  data: function () {
    return {
      isLoadedVehicles: false,
      inventoryData: {
        accountId: this.accountId,
        vehiclesTotalCount: null,
        vehicles: [],
        inventoryFilters: this.convertToViewModel(queryStringHelper.parseQueryStringToObject(this.$router))
      },
      filterOptions: [
        new ConditionFilter(0, conditions.all, false, 'Total', 0),
        new ConditionFilter(1, conditions.new, false, 'New', 0),
        new ConditionFilter(2, conditions.used, false, 'Used', 0),
        new ConditionFilter(3, conditions.cpo, false, 'CPO', 0)
      ],
      fields: [{
        key: 'title',
        label: 'Vehicle',
        sortable: true,
        tdClass: 'py-2 align-middle',
        thStyle: 'min-width: 300px'
      }, {
        key: 'stockNumber',
        label: 'Stock #',
        sortable: true,
        tdClass: 'py-2 align-middle'
      }, {
        key: 'accountId',
        label: 'Account Id',
        sortable: true,
        tdClass: 'py-2 align-middle'
      }, {
        key: 'age',
        label: 'Age',
        sortable: true,
        tdClass: 'py-2 align-middle'
      }, {
        key: 'actualPhotosCount',
        sortable: true,
        tdClass: 'py-2 align-middle'
      }, {
        key: 'lowPrice',
        label: 'Price',
        sortable: true,
        tdClass: 'py-2 align-middle',
        formatter: value => numeral(value).format('$0,0')
      }, {
        key: 'selection',
        label: 'Selection',
        sortable: false,
        tdClass: 'py-2 align-middle text-nowrap'
      }],
      vehiclesToGenerateEmailPage: []
    }
  },
  beforeMount () {
    this.filterOptions.find(x => x.id === this.inventoryFilters.optionId).isActive = true
  },
  mounted: function () {
    filtersBuilder = new InventoryFiltersBuilder(this.inventoryFilters)
    this.fetchInventoryBuilderModel()
    this.loadVehicleConditionsStatistic()
  },
  methods: {
    loadVehicleConditionsStatistic (vStatus, search) {
      let filters = this.convertToApiModel(this.inventoryFilters)
      let requestData = {
        vStatus: filters.vStatus,
        search: filters.search
      }
      let responseResult = {}
      return inventoryService.fetchEmailAdVehicleConditionsStatistic(
        this.inventoryData.accountId,
        vStatus || requestData.vStatus,
        search || requestData.search
      ).then(result => {
        let allCount = 0
        let newCount = 0
        let usedCount = 0
        let cpoCount = 0
        responseResult = result
        let responseData = result.data || []
        responseData.forEach((element) => {
          switch (element.condition) {
            case conditions.all:
              allCount = element.count
              break
            case conditions.new:
              newCount = element.count
              break
            case conditions.used:
              usedCount = element.count
              break
            case conditions.cpo:
              cpoCount = element.count
              break
          }
        })
        this.filterOptions.find(x => x.conditionType === conditions.all).total = allCount
        this.filterOptions.find(x => x.conditionType === conditions.new).total = newCount
        this.filterOptions.find(x => x.conditionType === conditions.used).total = usedCount
        this.filterOptions.find(x => x.conditionType === conditions.cpo).total = cpoCount
      }).catch(reason => {
        this.$toaster.exception(reason, 'Something went wrong.')
        this.$logger.handleError(reason, 'Can\'t fetch vehicle condition statistics', { request: requestData, response: responseResult })
      })
    },
    onPageChanged (newVal) {
      filtersBuilder.changePage(newVal)
      queryStringHelper.rebuildParamsInQueryString(this.$router, this.convertToApiModel(this.inventoryFilters))
    },
    onPageSizeChanged (newPageSize) {
      filtersBuilder.changePageSize(newPageSize)
      queryStringHelper.rebuildParamsInQueryString(this.$router, this.convertToApiModel(this.inventoryFilters))
    },
    onChangeActiveButton (newValue) {
      let filterOption = this.filterOptions.find(x => x.id === newValue)
      filtersBuilder.changeActiveButton(filterOption)

      this.setActiveById(filterOption.id)
      queryStringHelper.rebuildParamsInQueryString(this.$router, this.convertToApiModel(this.inventoryFilters))
    },
    onChangeSearch (newVal) {
      filtersBuilder.changeSearch(newVal)
      this.loadVehicleConditionsStatistic(null, newVal)
      queryStringHelper.rebuildParamsInQueryString(this.$router, this.convertToApiModel(this.inventoryFilters))
    },
    applySort (sortType) {
      filtersBuilder.changeSort(sortType)
      queryStringHelper.rebuildParamsInQueryString(this.$router, this.convertToApiModel(this.inventoryFilters))
    },
    setActiveById (id) {
      let filterOption = this.filterOptions.find(x => x.id === id)
      this.filterOptions.forEach(x => { x.isActive = false })
      filterOption.isActive = true
    },
    fetchInventoryBuilderModel () {
      this.isLoadedVehicles = false
      let filters = this.convertToApiModel(this.inventoryFilters)
      let responseResult = {}
      return inventoryService.fetchEmailAdInventoryBuilderModel(this.inventoryData.accountId, filters)
        .then(result => {
          responseResult = result
          let responseData = result.data || {}
          this.inventoryData.vehiclesTotalCount = responseData.vehiclesTotalCount || 0
          this.inventoryData.vehicles = responseData.vehicles || []
        })
        .catch(reason => {
          this.$toaster.exception(reason, 'Something went wrong.')
          this.$logger.handleError(reason, 'Can\'t fetch inventory builder model', {request: filters, response: responseResult})
        })
        .finally(() => {
          this.isLoadedVehicles = true
        })
    },
    onListingChanged () {
      this.fetchInventoryBuilderModel()
    },
    getVehiclePhotoSrc (item) {
      return (item.presentationPhoto || {}).photo107 + '?q=' + item.actualPhotosDateTimeEdited
    },
    getVehicleTitle (value) {
      if (!value) return ''
      let title = ''
      if (value.year > 0) {
        title = value.year.toString()
      }

      title = [title, value.make, value.model, value.trim].filter(v => v && v !== '').join(' ')

      return title.trim()
    },
    isVinChecked (vin) {
      return this.vehiclesToGenerateEmailPage.some(x => x.vin === vin)
    },
    isCheckBoxDisabled (vin) {
      return !this.isVinChecked(vin) && this.isEmailVehiclesLimitReached
    },
    addOrRemoveVin (item) {
      if (this.isVinChecked(item.vin)) {
        this.vehiclesToGenerateEmailPage.splice(this.vehiclesToGenerateEmailPage.findIndex(x => x.vin === item.vin), 1)
      } else {
        let vehicle = {
          stockNumber: item.stockNumber,
          vin: item.vin
        }
        this.vehiclesToGenerateEmailPage.push(vehicle)
      }
      this.$emit('input', this.getEmailPageData(this.h1EmailPageTitle))
      this.pushAlertOnReachedLimitOfVehiclesIfNeeded()
    },
    removeVehicleFromVehiclesToGenerateEmailPage (vin) {
      if (this.isVinChecked(vin)) {
        this.vehiclesToGenerateEmailPage.splice(this.vehiclesToGenerateEmailPage.findIndex(x => x.vin === vin), 1)
      }
    },
    pushAlertOnReachedLimitOfVehiclesIfNeeded () {
      if (this.isEmailVehiclesLimitReached) {
        this.$toaster.info(this.emailVehicleLimitAlertMessage, {timeout: 5000})
      }
    },
    getEmailPageData (h1EmailPageTitle) {
      return {
        vins: this.vehiclesToGenerateEmailPage.map(x => x.vin),
        h1PageTitle: h1EmailPageTitle
      }
    }
  },
  computed: {
    h1EmailPageTitle: {
      get () {
        return this.value.h1PageTitle
      },
      set (value) {
        this.$emit('input', this.getEmailPageData(value))
      }
    },
    getFiltersFormVariant () {
      return 'buttons'
    },
    inventoryFilters: {
      get () {
        return this.inventoryData.inventoryFilters
      },
      set (value) {
        this.inventoryData.inventoryFilters = value
      }
    },
    getFilterOptions () {
      return this.filterOptions
    },
    isEmailVehiclesLimitReached () {
      return this.vehiclesToGenerateEmailPage.length >= EMAIL_VEHICLES_LIMIT
    },
    emailVehicleLimitAlertMessage () {
      return `You've reached the limit of ${EMAIL_VEHICLES_LIMIT} vehicles to generate email ad.`
    },
    sortType () {
      return this.inventoryData.inventoryFilters.sortType
    }
  },
  watch: {
    $route: {
      handler () {
        this.inventoryFilters = this.convertToViewModel(queryStringHelper.parseQueryStringToObject(this.$router))
        filtersBuilder = new InventoryFiltersBuilder(this.inventoryFilters)
        this.fetchInventoryBuilderModel()
      },
      deep: true
    }
  }
}
</script>

<style lang="scss">
.selected-vehicle {
  margin-right: 0.1rem;
  margin-bottom: 0.1rem;
  border-radius: 1.5rem;
  height: 50px;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (max-width: 575px) {
  .nav-responsive-sm > .nav, .nav-responsive-sm > div > .nav {
      flex-wrap: nowrap!important;
      -webkit-box-orient: horizontal;
      -webkit-box-direction: normal;
      -ms-flex-direction: row;
      flex-direction: row;
      border: 0;
      overflow-x: scroll;
      white-space: nowrap;
      -webkit-overflow-scrolling: touch;
  }
  .nav-responsive-sm > div > .nav-tabs .nav-item {
    -webkit-box-flex: 1 !important;
    -ms-flex: 1 1 auto !important;
    flex: 1 1 auto !important;
  }
}
</style>
