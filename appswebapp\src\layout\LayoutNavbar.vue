<template>
  <b-navbar toggleable="lg" variant="white" class="layout-navbar align-items-lg-center container-p-x">
    <!-- Navbar toggle button -->
    <b-navbar-nav class="align-items-lg-start hamburger-menu mb-2" v-if="sidenavToggle">
      <b-navbar-brand>
        <b-row>
          <b-col class="app-brand-logo-custom-display">
            <span class="app-brand-logo" v-if="user && user.accountId">
              <img src="/static/img/logo-ebizautos-cp-black.svg" alt="" style="display: inline">
            </span>
            <router-link v-else to="/" class="app-brand-logo">
              <img src="/static/img/logo-ebizautos-cp-black.svg" alt="" style="display: inline">
            </router-link>
          </b-col>
          <b-col>
            <a class="nav-item nav-link py-1" href="javascript:void(0)" @click="toggleSidenav"><i class="ion ion-md-menu text-large" /></a>
          </b-col>
        </b-row>
      </b-navbar-brand>
    </b-navbar-nav>
    <b-navbar-toggle target="nav-collapse">
    </b-navbar-toggle>
    <b-collapse id="nav-collapse" :is-nav="true">
      <b-navbar-nav class="align-items-lg-center ml-auto">
        <b-nav-item-dropdown v-if="user && user.accountId" class="notification-dropdown" target="body" right no-caret boundary='viewport'>
          <template slot="button-content">
            <i class="ion ion-ios-notifications h4 m-0 text-secondary"></i><span class="sr-only">Manage</span>
            <b-badge class="indicator" variant="primary">{{totalAlerts}}</b-badge>
            <span class="d-lg-none ml-3 text-mute">Alerts</span>
          </template>
          <b-dropdown-header class="notification-header text-center m-0 py-2 text-white font-weight-bold">
            <span>{{totalAlerts}} New Alerts</span>
          </b-dropdown-header>
          <b-dd-item v-for="(alertVehicle, index) in vehicleALerts" :key="index" @click="goAlertEditPath(alertVehicle)">
            <small class="d-flex bd-highlight">
              <span class="mr-auto p-2 bd-highlight text-truncate vehicle-title-max-width"><strong>{{alertVehicle.vehicleTitle}}</strong></span>
              <span class="p-2 bd-highlight text-primary"><u>{{alertVehicle.alertTitle}}</u></span>
            </small>
          </b-dd-item>
          <b-dd-divider v-if="totalAlerts > 0"></b-dd-divider>
          <b-dd-item v-if="totalAlerts > 0" :to="getAlertPath()" class="notification-footer bg-white text-center text-light" variant="light">View All Alerts</b-dd-item>
        </b-nav-item-dropdown>
        <b-nav-text v-if="user && user.accountId" class="d-none d-lg-block text-big font-weight-light opacity-25 mx-3">|</b-nav-text>
        <b-nav-item-dropdown target="body" right>
          <template slot="button-content">
            <span class="d-inline-flex flex-lg-row-reverse align-items-center align-middle">
              <span class="px-1 mr-lg-2 ml-lg-0">
                {{ dealershipInfo.userName }}
              </span>
            </span>
          </template>
          <b-dropdown-menu right>
            <template v-if="dealershipInfo.hasDealerInfo">
              <b-dropdown-header tag="h6">{{ dealershipInfo.dealershipName }}</b-dropdown-header>
              <b-dropdown-header>Account ID {{ dealershipInfo.accountId }}</b-dropdown-header>
              <b-dropdown-divider></b-dropdown-divider>
            </template>
            <b-dropdown-item
              v-if="dealershipInfo.hasWebsiteUrl"
              :href="dealershipInfo.websiteUrl"
              target="_blank">
              <i class="ion ion-md-tv text-danger"></i> &nbsp; View Gallery
            </b-dropdown-item>
            <b-dropdown-item @click="logOutUser">
              <i class="ion ion-ios-log-out text-danger"></i> &nbsp; Log Out
            </b-dropdown-item>
          </b-dropdown-menu>
        </b-nav-item-dropdown>
      </b-navbar-nav>
    </b-collapse>
  </b-navbar>
</template>

<script>
import {mapGetters} from 'vuex'
import InventoryService from '@/services/inventory/InventoryService'
import defaultInventoryTasksFilters from '@/shared/inventory/inventoryTasksFilters'

export default {
  name: 'app-layout-navbar',
  props: {
    sidenavToggle: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      alertFilters: defaultInventoryTasksFilters.getObject(),
      vehicleALerts: [],
      totalAlerts: 0
    }
  },
  methods: {
    toggleSidenav () {
      this.layoutHelpers.toggleCollapsed()
    },
    logOutUser () {
      this.$store.dispatch('userAnnouncements/cleanUp').catch(ex => {
        console.error(ex)
      })
      this.$announcement.tryToStopTour()
      this.$cacheProvider.clean()
      this.$store.dispatch('authentication/logOut')
        .then(() => {
          this.$router.push('/login')
        })
    },
    getLayoutNavbarBg () {
      return this.layoutNavbarBg
    },
    getAlertPath () {
      return `/inventory/${this.user.accountId}/alerts`
    },
    getVehicleTitle (value) {
      if (!value) return ''
      let title = ''
      if (value.year > 0) {
        title = value.year.toString()
      }

      title = [title, value.make, value.model, value.trim].filter(v => v && v !== '').join(' ')

      return title.trim()
    },
    goAlertEditPath (alertVehicle) {
      this.$router.push({ path: this.getVehicleAlertEditPath(alertVehicle) })
    },
    getVehicleAlertEditPath (alertVehicle) {
      if (alertVehicle.hasDescriptionAlert) {
        return `/inventory/${alertVehicle.accountId}/edit/${alertVehicle.vin}?tabindex=3`
      }
      if (alertVehicle.hasPhotoAlert) {
        return `/inventory/${alertVehicle.accountId}/edit/${alertVehicle.vin}?tabindex=2`
      }
      if (alertVehicle.hasVideoAlert) {
        return `/inventory/${alertVehicle.accountId}/edit/${alertVehicle.vin}?tabindex=2`
      }
      if (alertVehicle.hasPriceAlert) {
        return `/inventory/${alertVehicle.accountId}/edit/${alertVehicle.vin}?tabindex=1`
      }
    }
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    dealershipInfo () {
      let infoForUser = this.userInfo

      if (!infoForUser) {
        return ''
      }

      let currentUser = infoForUser.user
      let dealershipName = (infoForUser.accountSettings || {}).dealershipName
      let websiteUrl = (infoForUser.siteSettings || {}).urlWithProtocol

      return {
        dealershipName: dealershipName,
        websiteUrl: websiteUrl,
        userName: currentUser.firstName + ' ' + currentUser.lastName,
        accountId: currentUser.accountId,
        hasDealerInfo: !!dealershipName,
        hasWebsiteUrl: !!websiteUrl
      }
    },
    user () {
      return (this.userInfo || {}).user
    }
  },
  async created () {
    if (this.user && this.user.accountId > 0) {
      InventoryService.getAlertsListing(this.user.accountId, this.alertFilters)
        .then(res => {
          this.totalAlerts = (res.data || {}).alertsTotalCount || 0
          let vehicles = (res.data || {}).vehicles || []
          for (let vehicle of vehicles) {
            let vehicleTitle = this.getVehicleTitle(vehicle)
            if (vehicle.hasDescriptionAlert && this.vehicleALerts.length <= 3) {
              this.vehicleALerts.push({
                vehicleTitle: vehicleTitle,
                accountId: vehicle.accountId,
                vin: vehicle.vin,
                hasDescriptionAlert: vehicle.hasDescriptionAlert,
                alertTitle: 'Missing Description'
              })
            }
            if (vehicle.hasPhotoAlert && this.vehicleALerts.length <= 3) {
              this.vehicleALerts.push({
                vehicleTitle: vehicleTitle,
                accountId: vehicle.accountId,
                vin: vehicle.vin,
                hasPhotoAlert: vehicle.hasPhotoAlert,
                alertTitle: 'Missing Photos'
              })
            }
            if (vehicle.hasVideoAlert && this.vehicleALerts.length <= 3) {
              this.vehicleALerts.push({
                vehicleTitle: vehicleTitle,
                accountId: vehicle.accountId,
                vin: vehicle.vin,
                hasVideoAlert: vehicle.hasVideoAlert,
                alertTitle: 'Missing Videos'
              })
            }
            if (vehicle.hasPriceAlert && this.vehicleALerts.length <= 3) {
              this.vehicleALerts.push({
                vehicleTitle: vehicleTitle,
                accountId: vehicle.accountId,
                vin: vehicle.vin,
                hasPriceAlert: vehicle.hasPriceAlert,
                alertTitle: 'Missing Pricing'
              })
            }
            if (this.vehicleALerts.length > 3) {
              break
            }
          }
        }).catch(ex => {
          this.$logger.handleError(ex, `Cannot populate alerts for ${this.user.accountId}`, { filters: this.alertFilters, userInfo: this.user })
        })
    }
  }
}
</script>

<style lang="scss">
  .demo-navbar-user {
    .dropdown-menu.dropdown-menu-right.show {
      position: absolute;
    }
  }

  .notification-dropdown .dropdown-menu {
    padding: 0;
    border: none;
  }

  .notification-header {
    background: black;
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
    display: block;
  }

  .notification-footer {
    font-size: 10px;
  }

  .app-brand-logo img {
    width: 11rem;
  }

  .hamburger-menu {
    height: 41px;
  }

  .bg-navbar-theme .navbar-nav .nav-link {
    color: #4e5155;
    padding: 0;
  }

  .bg-navbar-theme .navbar-nav .nav-link:hover {
    color: #a3a4a6;
  }

  .vehicle-title-max-width {
    max-width: 150px;
  }
  @media (max-width: 397px) {
    .app-brand-logo-custom-display {
      display: none;
    }
  }
  @media (min-width: 992px){
    .hamburger-menu {
      visibility: hidden;
    }
  }
  @media (max-width: 992px){
    .vehicle-title-max-width {
    max-width: 100%;
  }
  }
</style>
