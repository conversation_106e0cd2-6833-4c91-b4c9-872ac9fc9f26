class CopyProvider {
  async fallbackCopyTextToClipboard (text) {
    var textArea = document.createElement('textarea')
    textArea.value = text
    textArea.style.top = '0'
    textArea.style.left = '0'
    textArea.style.position = 'fixed'
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()
    try {
      return document.execCommand('copy')
    } catch (ex) {
      return false
    } finally {
      document.body.removeChild(textArea)
    }
  };
  copyTextToClipboard (text) {
    if (!navigator.clipboard) {
      return this.fallbackCopyTextToClipboard(text)
    }
    return navigator.clipboard.writeText(text)
  }
}

export default CopyProvider
