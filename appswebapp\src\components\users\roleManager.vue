<template>
  <div>
    <b-row>
      <b-col>
        <h4>{{isEditMode ? 'Edit': 'Add'}} Role</h4>
      </b-col>
      <b-col>
        <b-btn size="sm" class="float-right ml-2" @click="cancel">Cancel</b-btn>
        <l-button :loading="isProcessing" size="sm" variant="primary" class="float-right" @click="save">Save</l-button>
      </b-col>
    </b-row>
    <ValidationObserver ref="validator">
      <b-card>
        <ValidationProvider name="Name" rules="required" v-slot="{errors}">
          <detail-row :fixed-payload-width="true" :title-position="'start'" :error="errors[0]">
            <span slot="title">Name:</span>
            <b-form-input name="name" slot="payload" v-model="role.name"></b-form-input>
          </detail-row>
        </ValidationProvider>
        <h6 class="text-muted">Permissions:</h6>
        <ValidationProvider name="Permissions" rules="required" v-slot="{errors}">
          <b-form-group>
            <b-form-checkbox-group
              name="permissions"
              v-model="role.permissions"
            >
              <b-tabs class="nav-tabs-left" no-fade>
                <b-tab class="p-4" :title="permissionsBySection.title" v-for="permissionsBySection in getPermissionsBySection" :key="permissionsBySection.title">
                  <template #title>
                    <div class="d-flex justify-content-between align-items-center">
                      <span>{{permissionsBySection.title}}</span>
                      <b-badge class="ml-1" pill variant="info">{{permissionsBySection.permissions.filter(x => role.permissions && role.permissions.includes(x.value)).length}}</b-badge>
                    </div>
                  </template>
                  <b-form-checkbox
                    :name="`role-permissions`"
                    v-for="permission in permissionsBySection.permissions"
                    :key="permission.title"
                    :value="permission.value">
                    {{permission.title}}
                  </b-form-checkbox>
                </b-tab>
              </b-tabs>
            </b-form-checkbox-group>
          </b-form-group>
          <b-row>
            <b-col>
              <span class="text-danger h6">{{errors[0]}}</span>
            </b-col>
          </b-row>
        </ValidationProvider>
      </b-card>
    </ValidationObserver>
  </div>
</template>

<script>
import UserManagementService from '../../services/users/UserManagementService'
import detailRow from '../details/helpers/detailRow'
import {permissionsBySection} from '@/shared/users/constants'

export default {
  name: 'role-manager',
  props: {
    isEditMode: {type: Boolean},
    role: {type: Object, required: true}
  },
  computed: {
    getPermissionsBySection () {
      return Object.values(permissionsBySection)
    }
  },
  data () {
    return {
      isProcessing: false
    }
  },
  components: {
    detailRow
  },
  methods: {
    save () {
      this.$refs.validator.validate().then(isValid => {
        if (isValid) {
          this.isProcessing = true
          if (this.isEditMode) {
            this.update()
          } else {
            this.create()
          }
        }
      })
    },
    cancel () {
      this.$emit('cancel')
    },
    create () {
      UserManagementService.createNewUserRole(this.role).then(res => {
        this.$toaster.success('New Role Created Successfully')
        setTimeout(() => this.cancel(), 1000)
      }).catch(ex => {
        this.$toaster.exception(ex, 'Failed To Create New Role')
        this.$logger.handleError(ex, 'Exception occurred on create new role')
      }).finally(() => {
        this.isProcessing = false
      })
    },
    update () {
      UserManagementService.updateUserRole(this.role).then(res => {
        this.$toaster.success('Role Updated Successfully')
        setTimeout(() => this.cancel(), 1000)
      }).catch(ex => {
        this.$toaster.exception(ex, 'Failed To Update Role')
        this.$logger.handleError(ex, 'Exception occurred on update role')
      }).finally(() => {
        this.isProcessing = false
      })
    }
  }
}
</script>
