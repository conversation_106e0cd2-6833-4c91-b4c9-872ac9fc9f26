<template>
  <sidenav :orientation="orientation" :class="curClasses" class="side-nav" v-if="user">
    <div class="app-brand" v-if="orientation !== 'horizontal'">
      <span class="app-brand-logo" v-if="user.accountId">
        <img src="/static/img/logo-ebizautos-cp.svg" alt="" style="display: inline">
      </span>
      <router-link v-else to="/" class="app-brand-logo">
        <img src="/static/img/logo-ebizautos-cp.svg" alt="" style="display: inline">
      </router-link>
      <a v-if="sidenavToggle" class="layout-sidenav-toggle sidenav-link text-small mt-1" href="javascript:void(0)" @click="toggleSidenav"><i class="ion ion-md-menu text-large align-middle" /></a>
      <span v-else></span>
    </div>

    <!-- Inner -->
    <div class="sidenav-inner" :class="{ 'my': orientation !== 'horizontal' }">
      <!-- Account -->
      <template>
        <sidenav-divider class="my-0"/>
        <sidenav-menu id="ebiz-accounts-sidenav-menu" v-if="canManageMultipleAccount" icon="ion ion-ios-people" :active="isMenuActive('/accounts')" :open="isMenuOpen('/accounts')">
          <span @click="onAccountsTitleClick" class="sidenav-item d-block sidenav-link p-0" slot="link-text">Accounts</span>

          <sidenav-router-link id="ebiz-accounts-accounts-router-link" :to="{name: 'account-listing'}" :exact="true">*Accounts</sidenav-router-link>
          <sidenav-router-link id="ebiz-accounts-groups-router-link" v-can:to="{name: 'account-groups'}" :to="{name: 'account-groups'}">*Account Groups</sidenav-router-link>
          <sidenav-router-link id="ebiz-accounts-reports-router-link" v-can:to="{name: 'reports'}" :to="{name: 'reports'}">*Reports</sidenav-router-link>
          <sidenav-router-link id="ebiz-accounts-user-activity-router-link" v-can:to="{name: 'accounts-user-activity'}" :to="{name: 'accounts-user-activity', query: combinedQuery}" :exact="true">
            *User Activity
          </sidenav-router-link>
          <template v-if='getAccountId && user.isEbizAdmin'>
            <sidenav-router-link id="ebiz-account-settings-router-link" v-can:to="{name: 'account-settings', params: { accountId: getAccountId }}" :to="{name: 'account-settings', params: { accountId: getAccountId }}">* Settings</sidenav-router-link>
          </template>
        </sidenav-menu>
      </template>

      <!-- Inventory -->
      <template v-if="hasInventoryManagementFullAccess || canManageInventoryMultiplyAccounts || getAccountId && hasAccessToInventory" >
        <sidenav-divider class="my-0"/>
        <sidenav-menu id="ebiz-inventory-sidenav-menu" icon="ion ion-ios-car" :active="isMenuActive('/inventory')" :open="isMenuOpen('/inventory')">
          <span class="sidenav-item d-block sidenav-link p-0" slot="link-text">Inventory</span>
          <sidenav-router-link id="ebiz-inventory-accounts-router-link" v-can:to="{name: 'inventory-accounts'}" :to="{name: 'inventory-accounts'}" :exact="true">
            *Accounts
          </sidenav-router-link>
          <template v-if="getAccountId">
            <sidenav-router-link
              v-if="isInventoryEmailAdEnabled"
              id="ebiz-inventory-manage-router-link"
              :to="{name: 'inventory-email-ad-generator', params: { accountId: getAccountId }}"
            >
              Email Ad Generator
            </sidenav-router-link>
            <sidenav-router-link
              id="ebiz-inventory-manage-router-link"
              v-can:to="{name: 'inventory-description', params: { accountId: getAccountId }}"
              :to="{name: 'inventory-description', params: { accountId: getAccountId }}"
            >
              Manage
            </sidenav-router-link>
            <sidenav-router-link
              id="ebiz-inventory-alerts-router-link"
              v-can:to="{name: 'inventory-tasks', params: { accountId: getAccountId }}"
              :to="{name: 'inventory-tasks', params: { accountId: getAccountId }}"
            >
              Alerts<span><b-badge pill variant="primary"><span class="text-light">{{alertsTotal}}</span></b-badge></span>
            </sidenav-router-link>
            <sidenav-router-link
              id="ebiz-inventory-pricing-router-link"
              v-can:to="{name: 'inventory-pricing', params: { accountId: getAccountId } }"
              :to="{name: 'inventory-pricing', params: { accountId: getAccountId } }"
            >
              Pricing
            </sidenav-router-link>
            <sidenav-router-link
              id="ebiz-inventory-merchandising-router-link"
              v-can:to="{name: 'inventory-merchandising', params: { accountId: getAccountId }}"
              :to="{name: 'inventory-merchandising', params: { accountId: getAccountId }}"
            >
              Merchandising
            </sidenav-router-link>
            <sidenav-router-link
              id="ebiz-inventory-settings-router-link"
              v-can:to="{name: 'inventory-settings', params: { accountId: getAccountId }}"
              :to="{name: 'inventory-settings', params: { accountId: getAccountId }}"
            >
              Settings
            </sidenav-router-link>
            <sidenav-router-link
              id="ebiz-inventory-account-autovideo-settings"
              v-can:to="{name: 'inventory-account-autovideo-settings', params: { accountId: getAccountId }}"
              :to="{name: 'inventory-account-autovideo-settings', params: { accountId: getAccountId }}"
            >
              Account AutoVideo Settings
            </sidenav-router-link>
          </template>
          <sidenav-router-link id="ebiz-inventory-integrity-report-router-link" v-can:to="{name: 'inventory-integrity-report'}" :to="{name: 'inventory-integrity-report'}">*Integrity Report</sidenav-router-link>
          <sidenav-router-link id="ebiz-inventory-service-settings-router-link" v-can:to="{name: 'inventory-service-settings'}" :to="{name: 'inventory-service-settings'}">*AutoCheck Service Settings</sidenav-router-link>
          <sidenav-router-link
            v-if="getAccountId"
            id="ebiz-inventory-vehicle-state-report-router-link"
            v-can:to="{name: 'inventory-vehicle-state-report', params: { accountId: getAccountId }}"
            :to="{name: 'inventory-vehicle-state-report', params: { accountId: getAccountId }}"
          >
            *Vehicle State Report
          </sidenav-router-link>
          <sidenav-router-link id="ebiz-inventory-autovideo-queue-router-link" v-can:to="{name: 'inventory-autovideo-queue'}" :to="{name: 'inventory-autovideo-queue'}">*AutoVideo Queue</sidenav-router-link>
          <sidenav-router-link id="ebiz-inventory-global-autovideo-settings-router-link" v-can:to="{name: 'inventory-global-autovideo-settings'}" :to="{name: 'inventory-global-autovideo-settings'}">*Global AutoVideo Settings</sidenav-router-link>
          <sidenav-router-link v-if="getAccountId"
            id="ebiz-inventory-logs-router-link"
            v-can:to="{name: 'inventory-logs', params: { accountId: getAccountId }}" :to="{name: 'inventory-logs', params: { accountId: getAccountId }}">*Logs</sidenav-router-link>
          <sidenav-router-link
            id="ebiz-inventory-autovideo-logs-router-link"
            v-can:to="{name: 'inventory-autovideo-logs'}"
            :to="{name: 'inventory-autovideo-logs'}"
          >
            *AutoVideo Logs
          </sidenav-router-link>
          <template v-if="!getAccountId">
            <sidenav-router-link id="ebiz-inventory-mobile-logs-router-link" v-can:to="{name: 'inventory-mobile-log'}" :to="{name: 'inventory-mobile-log'}">*Mobile Logs</sidenav-router-link>
            <sidenav-router-link id="ebiz-inventory-mobile-logs-router-link" v-can:to="{name: 'inventory-desktop-log'}" :to="{name: 'inventory-desktop-log'}">*Desktop Logs</sidenav-router-link>
          </template>
        </sidenav-menu>
      </template>

      <!-- Craigslist -->
      <template  v-if="hasCraigslistFullAccess || getAccountId && hasAccessToCraigslist">
        <sidenav-divider class="my-0"/>
        <sidenav-menu id="ebiz-craigslist-sidenav-menu" icon="ion ion-ios-folder" :active="isMenuActive(`/craigslist`)" :open="isMenuOpen(`/craigslist`)">
          <span class="sidenav-item d-block sidenav-link p-0" slot="link-text">Craigslist</span>
          <template v-if="hasCraigslistFullAccess">
            <sidenav-router-link id="ebiz-craigslist-accounts-router-link" :to="{name: 'craigslist-dashboard-all-accounts', query: combinedQuery}" :exact="true">*Accounts</sidenav-router-link>
          </template>
          <template v-if="getAccountId">
            <sidenav-router-link id="ebiz-craigslist-dashboard-router-link" :to="{name: 'craigslist-dashboard-for-account', params: { accountId: getAccountId }}">Dashboard</sidenav-router-link>
            <sidenav-router-link id="ebiz-craigslist-posts-router-link" :to="{name: 'craigslist-posts-for-account', params: { accountId: getAccountId }}">Posts</sidenav-router-link>
            <sidenav-router-link id="ebiz-craigslist-inventory-router-link" :to="{name: 'craigslist-inventory-for-account', params: { accountId: getAccountId }}" :exact="true">Inventory</sidenav-router-link>
            <sidenav-router-link id="ebiz-craigslist-settings-router-link" :to="{name: 'craigslist-settings-for-account', params: { accountId: getAccountId }}" :exact="true">Settings</sidenav-router-link>
            <sidenav-router-link id="ebiz-craigslist-logs-router-link" v-if="hasCraigslistFullAccess" :to="{name: 'craigslist-logs-for-account', params: { accountId: getAccountId }}" :exact="true">*Logs</sidenav-router-link>
          </template>
          <sidenav-router-link id="ebiz-craigslist-service-settings-router-link" v-can:to="{name: 'craigslist-service-settings'}" :to="{name: 'craigslist-service-settings'}" :exact="true">*Craigslist Service Settings</sidenav-router-link>
          <sidenav-router-link id="ebiz-craigslist-user-activity-router-link" v-can:to="{name: 'craigslist-user-activity', query: combinedQuery}" :to="{name: 'craigslist-user-activity', query: combinedQuery}" :exact="true">*User Activity</sidenav-router-link>
        </sidenav-menu>
      </template>

      <!-- eBay -->
      <template v-if="hasEBayFullAccess || getAccountId && hasAccessToEBay && hasAccountManagedByApps">
        <sidenav-divider class="my-0"/>
        <sidenav-menu id="ebiz-ebay-sidenav-menu" icon="ion ion-ios-folder-open" :active="isMenuActive(`/ebay`)" :open="isMenuOpen(`/ebay`)">
          <span class="sidenav-item d-block sidenav-link p-0" slot="link-text">eBay</span>
          <template v-if="hasEBayFullAccess">
            <sidenav-router-link id="ebiz-ebay-accounts-router-link" :to="{name: 'ebay-accounts'}" :exact="true">*Accounts</sidenav-router-link>
            <sidenav-router-link id="ebiz-ebay-errors-scheduled-router-link" :to="{name: 'ebay-errors-and-scheduled'}">*Errors &amp; Scheduled</sidenav-router-link>
          </template>
          <template v-if="getAccountId">
            <sidenav-router-link id="ebiz-ebay-posts-router-link" :to="{name: 'ebay-posts', params: { accountId: getAccountId }, query: combinedQuery }">Posts</sidenav-router-link>
            <sidenav-router-link id="ebiz-ebay-inventory-router-link" :to="{name: 'ebay-inventory', params: { accountId: getAccountId }, query: combinedQuery }">Inventory</sidenav-router-link>
            <sidenav-router-link id="ebiz-ebay-automated-router-link" v-can:to="{name: 'ebay-automated', params: { accountId: getAccountId }}" :to="{name: 'ebay-automated', params: { accountId: getAccountId }}">*Automated eBay</sidenav-router-link>
            <sidenav-router-link id="ebiz-ebay-settings-router-link" :to="{name: 'ebay-settings', params: { accountId: getAccountId }}">Settings</sidenav-router-link>
          </template>
        </sidenav-menu>
      </template>

      <!-- Analytics GA4-->
      <template v-if="hasAnalyticsFullAccess || getAccountId && hasAccessToAnalyticsGa4 || canReadAnalyticsGa4 && hasAccessToAnalyticsReportGroups" >
        <sidenav-divider class="my-0"/>
        <sidenav-menu id="ebiz-analytics-ga4-sidenav-menu" icon="ion ion-ios-analytics" :active="isMenuActive(`/ebiz_analytics_ga4`)" :open="isMenuOpen(`/ebiz_analytics_ga4`)" >
          <span class="sidenav-item d-block sidenav-link p-0" slot="link-text">Analytics (New GA4)</span>
          <sidenav-router-link id="ebiz-analytics-ga4-accounts-router-link" v-can:to="{name: 'analytics-ga4-accounts'}" :to="{name: 'analytics-ga4-accounts'}" :exact="true">*Accounts</sidenav-router-link>

          <template v-if="getAccountId || !reportGroupId && hasAccountLevelDefaultView">
            <sidenav-router-link id="ebiz-analytics-ga4-dashboard-router-link" :to="{name: 'analyticsGa4Dashboard', params: { accountId: getAccountId }, query: combinedQuery }">Dashboard</sidenav-router-link>
            <sidenav-router-link id="ebiz-analytics-ga4-website-overview-router-link" :to="{name: 'ga4WebsiteOverview', params: { accountId: getAccountId } , query: combinedQuery }">Website Overview</sidenav-router-link>
            <sidenav-router-link id="ebiz-analytics-ga4-website-engagement-router-link" :to="{name: 'ga4WebsiteEngagement', params: { accountId: getAccountId } , query: combinedQuery }">Website Engagement</sidenav-router-link>
            <sidenav-router-link id="ebiz-analytics-ga4-vehicles-router-link" :to="{name: 'ga4Vehicles', params: { accountId: getAccountId }, query: combinedQuery }">Vehicles</sidenav-router-link>
            <sidenav-router-link id="ebiz-analytics-ga4-channel-segments-router-link" :to="{name: 'ga4ChannelSegments', params: { accountId: getAccountId }, query: combinedQuery }">Channel Segments</sidenav-router-link>
            <sidenav-router-link id="ebiz-analytics-ga4-traffic-sources-router-link" :to="{name: 'ga4TrafficSources', params: { accountId: getAccountId }, query: combinedQuery }">Traffic Sources</sidenav-router-link>
            <sidenav-router-link id="ebiz-analytics-ga4-paid-search-router-link" :to="{name: 'ga4PaidSearch', params: { accountId: getAccountId }, query: combinedQuery }">Paid Search</sidenav-router-link>
            <sidenav-router-link id="ebiz-analytics-ga4-display-router-link" :to="{name: 'ga4Display', params: { accountId: getAccountId }, query: combinedQuery }">Display / YouTube</sidenav-router-link>
            <sidenav-router-link id="ebiz-analytics-ga4-remarketing-router-link" :to="{name: 'ga4Remarketing', params: { accountId: getAccountId }, query: combinedQuery }">Remarketing</sidenav-router-link>
            <sidenav-router-link id="ebiz-analytics-ga4-account-settings-router-link" v-if="user.accountId" v-can:to="{name: 'ga4AnalyticsAccountSettings', params: { accountId: user.accountId }}" :to="{name: 'ga4AnalyticsAccountSettings', params: { accountId: user.accountId }}" :exact="true">*Settings</sidenav-router-link>
            <sidenav-router-link id="ebiz-analytics-ga4-account-settings-router-link" v-else v-can:to="{name: 'ga4AnalyticsAccountSettings', params: { accountId: getAccountId }}" :to="{name: 'ga4AnalyticsAccountSettings', params: { accountId: getAccountId }}" :exact="true">*Settings</sidenav-router-link>
          </template>
          <template v-else-if="reportGroupId || hasReportGroupLevelDefaultView">
            <sidenav-router-link id="ebiz-analytics-ga4-group-dashboard-router-link" :to="{name: 'ga4GroupAnalyticsDashboard',params: { reportGroupId: reportGroupIdRouterProp }, query: combinedQuery }">Dashboard</sidenav-router-link>
            <sidenav-router-link id="ebiz-analytics-ga4-group-website-overview-router-link" :to="{name: 'ga4GroupWebsiteOverview',params: { reportGroupId: reportGroupIdRouterProp } , query: combinedQuery }">Website Overview</sidenav-router-link>
            <sidenav-router-link id="ebiz-analytics-ga4-group-website-engagement-router-link"  :to="{name: 'ga4GroupWebsiteEngagement',params: { reportGroupId: reportGroupIdRouterProp } , query: combinedQuery }">Website Engagement</sidenav-router-link>
            <sidenav-router-link id="ebiz-analytics-ga4-group-vehicles-router-link" :to="{name: 'ga4GroupVehicles',params: { reportGroupId: reportGroupIdRouterProp }, query: combinedQuery }">Vehicles</sidenav-router-link>
            <sidenav-router-link id="ebiz-analytics-ga4-group-channel-segments-router-link" :to="{name: 'ga4GroupChannelSegments',params: { reportGroupId: reportGroupIdRouterProp }, query: combinedQuery }">Channel Segments</sidenav-router-link>
            <sidenav-router-link id="ebiz-analytics-ga4-group-traffic-sources-router-link" :to="{name: 'ga4GroupTrafficSources',params: { reportGroupId: reportGroupIdRouterProp }, query: combinedQuery }">Traffic Sources</sidenav-router-link>
            <sidenav-router-link id="ebiz-analytics-ga4-group-paid-search-router-link" :to="{name: 'ga4GroupPaidSearch',params: { reportGroupId: reportGroupIdRouterProp }, query: combinedQuery }">Paid Search</sidenav-router-link>
            <sidenav-router-link id="ebiz-analytics-ga4-group-display-router-link" :to="{name: 'ga4GroupDisplay',params: { reportGroupId: reportGroupIdRouterProp }, query: combinedQuery }">Display / YouTube</sidenav-router-link>
            <sidenav-router-link id="ebiz-analytics-ga4-group-remarketing-router-link" :to="{name: 'ga4GroupRemarketing',params: { reportGroupId: reportGroupIdRouterProp }, query: combinedQuery }">Remarketing</sidenav-router-link>
            <sidenav-router-link id="ebiz-analytics-ga4-account-settings-router-link" v-if="user.accountId" v-can:to="{name: 'ga4AnalyticsAccountSettings', params: { accountId: user.accountId }}" :to="{name: 'ga4AnalyticsAccountSettings', params: { accountId: user.accountId }}" :exact="true">*Settings</sidenav-router-link>
          </template>
          <sidenav-router-link id="ebiz-analytics-ga4-groups-router-link" v-can:to="{name: 'analytics-ga4-groups'}" :to="{name: 'analytics-ga4-groups'}" :exact="true">*Analytics Groups</sidenav-router-link>
          <sidenav-router-link id="ebiz-analytics-ga4-brand-exports-router-link" v-can:to="{name: 'analytics-ga4-brand-exports'}" :to="{name: 'analytics-ga4-brand-exports'}" :exact="true">*Brand Exports</sidenav-router-link>
          <sidenav-router-link id="ebiz-analytics-ga4-pag-report-router-link" v-can:to="{name: 'ga4PAGGroupReport'}" :to="{name: 'ga4PAGGroupReport'}" :exact="true">*Penske Report</sidenav-router-link>
          <sidenav-router-link id="ebiz-analytics-ga4-ads-rebuild-router-link" v-can:to="{name: 'ga4AdsRebuild'}" :to="{name: 'ga4AdsRebuild'}" :exact="true">*Rebuild Google Ads</sidenav-router-link>
          <sidenav-router-link id="ebiz-analytics-ga4-authorization-router-link" v-can:to="{name: 'analytics-ga4-authorization'}" :to="{name: 'analytics-ga4-authorization'}" :exact="true">*Authorization</sidenav-router-link>
          <sidenav-router-link id="ebiz-analytics-ga4-rebuild-logs-router-link" v-can:to="{name: 'analytics-ga4-logs'}" :to="{name: 'analytics-ga4-logs'}" :exact="true">*Logs</sidenav-router-link>
          <sidenav-router-link id="ebiz-analytics-ga4-user-activity-router-link" v-can:to="{name: 'analytics-ga4-user-activity'}" :to="{name: 'analytics-ga4-user-activity'}" :exact="true">*User Activity</sidenav-router-link>
        </sidenav-menu>
      </template>

      <!-- Leads -->
      <template v-if="hasLeadsFullAccess || (getAccountId && hasAccessToLeads) || canManageLeadsMultipleAccount">
        <sidenav-divider class="my-0"/>
        <sidenav-menu id="ebiz-leads-sidenav-menu" icon="ion ion-ios-mail" :active="isMenuActive(`/leads`)" :open="isMenuOpen(`/leads`)">
          <span class="sidenav-item d-block sidenav-link p-0" slot="link-text">Leads</span>
          <sidenav-router-link id="ebiz-leads-accounts-router-link" v-if="hasLeadsFullAccess || canManageLeadsMultipleAccount" :to="{name: 'leads-accounts', query: combinedQuery}" :exact="true">*Accounts</sidenav-router-link>

          <template v-if='getAccountId'>
            <sidenav-router-link id="ebiz-leads-dashboard-router-link" v-can:to="{name: 'leads-dashboard', params: { accountId: getAccountId }}" :to="{name: 'leads-dashboard', params: { accountId: getAccountId }, query: combinedQuery}">Dashboard</sidenav-router-link>
            <sidenav-router-link id="ebiz-leads-manager-router-link" v-can:to="{name: 'leads-manager', params: { accountId: getAccountId }}" :to="{name: 'leads-manager', params: { accountId: getAccountId }}">Leads Manager</sidenav-router-link>
            <sidenav-router-link id="ebiz-leads-messenger-router-link" v-can:to="{name: 'leads-messenger', params: { accountId: getAccountId }}"  :to="{name: 'leads-messenger', params: { accountId: getAccountId }}">Messenger</sidenav-router-link>
            <sidenav-router-link id="ebiz-leads-settings-router-link" v-can:to="{name: 'leads-settings', params: { accountId: getAccountId }}" :to="{name: 'leads-settings', params: { accountId: getAccountId }}">Settings</sidenav-router-link>
          </template>
          <sidenav-router-link id="ebiz-leads-integrity-report-router-link" v-can:to="{name: 'leads-integrity-report'}" :to="{name: 'leads-integrity-report'}">*Integrity Report</sidenav-router-link>
          <sidenav-router-link id="ebiz-leads-campaign-types-router-link" v-can:to="{name: 'leads-campaign-types'}" :to="{name: 'leads-campaign-types'}">*Campaign Types</sidenav-router-link>
          <sidenav-router-link id="ebiz-leads-spam-router-link" v-can:to="{name: 'leads-spam'}" :to="{name: 'leads-spam'}">*Spam</sidenav-router-link>
          <sidenav-router-link id="ebiz-leads-logs-router-link" v-can:to="{name: 'leads-logs'}" :to="{name: 'leads-logs'}">*Logs</sidenav-router-link>
          <sidenav-router-link id="ebiz-leads-user-activity-router-link" v-can:to="{name: 'leads-user-activity'}" :to="{name: 'leads-user-activity'}">*User Activity</sidenav-router-link>
        </sidenav-menu>
      </template>

      <!-- Website -->
      <template v-if="hasAccessToSiteManagement">
        <sidenav-divider class="my-0"/>
        <sidenav-menu id="ebiz-website-sidenav-menu" icon="ion ion-md-globe" :active="isMenuActive(`/website`)" :open="isMenuOpen(`/website`)">
          <span class="sidenav-item d-block sidenav-link p-0" slot="link-text">Website</span>
          <sidenav-router-link id="ebiz-website-accounts-router-link" v-can:to="{name: 'website-accounts-listing'}"  :to="{name: 'website-accounts-listing', query: combinedQuery}" :exact="true">*Accounts</sidenav-router-link>
          <sidenav-router-link id="ebiz-website-page-builder-router-link" v-if="getAccountId" :to="{name: 'website-page-builder', params: { accountId: getAccountId }}">Page Builder</sidenav-router-link>
          <sidenav-router-link
            id="ebiz-website-user-activity-router-link"
            v-can:to="{name: 'website-user-activity', query: combinedQuery}"
            :to="{name: 'website-user-activity', query: combinedQuery}"
            :exact="true"
          >
            *User Activity
          </sidenav-router-link>
        </sidenav-menu>
      </template>

      <!-- Users -->
      <template v-if="hasAccessToUsers">
        <sidenav-divider class="my-0"/>
        <sidenav-menu id="ebiz-users-sidenav-menu" icon="ion ion-md-person" :active="isMenuActive(`/users`)" :open="isMenuOpen(`/users`)">
          <span class="sidenav-item d-block sidenav-link p-0" slot="link-text">User Panel</span>
          <sidenav-router-link id="ebiz-users-management-router-link" :to="{ name: 'users-management' }" :exact="true">Users Management</sidenav-router-link>
          <sidenav-router-link id="ebiz-users-roles-router-link" v-can:to="{name: 'user-roles-management'}" :to="{ name: 'user-roles-management' }" :exact="true">*Roles Management</sidenav-router-link>
          <sidenav-router-link id="ebiz-users-announcements-router-link" v-can:to="{name: 'announcements-tool'}" :to="{name: 'announcements-tool'}">*Announcements</sidenav-router-link>
          <sidenav-router-link id="ebiz-users-user-activity-router-link" v-can:to="{name: 'users-user-activity'}" :to="{ name: 'users-user-activity' }" :exact="true">*User Activity</sidenav-router-link>
        </sidenav-menu>
      </template>

      <!-- SiteBoxes Manager -->
      <template v-if="hasAccessToSiteBoxesManager">
        <sidenav-divider class="my-0"/>
        <sidenav-menu icon="ion ion-md-cube" :active="isMenuActive(`/siteboxmanager`)" :open="isMenuOpen(`/siteboxmanager`)">
          <span class="sidenav-item d-block sidenav-link p-0" slot="link-text">*SiteBox Manager</span>
          <sidenav-router-link v-can:to="{name: 'site-hostings'}" :to="{name: 'site-hostings'}">Site Hostings</sidenav-router-link>
          <sidenav-router-link v-can:to="{name: 'siteboxes'}" :to="{name: 'siteboxes'}">SiteBoxes</sidenav-router-link>
          <sidenav-router-link v-can:to="{name: 'databases'}" :to="{name: 'databases'}">Databases</sidenav-router-link>
          <sidenav-router-link v-can:to="{name: 'sitebox-manager-fetcher'}" :to="{name: 'sitebox-manager-fetcher'}">Fetcher</sidenav-router-link>
          <sidenav-router-link v-can:to="{name: 'sitebox-synchronization-monitor'}" :to="{name: 'sitebox-synchronization-monitor', query: combinedQuery}">Synchronization Monitor</sidenav-router-link>
          <sidenav-router-link v-can:to="{name: 'sitebox-manager-user-activity'}" :to="{name: 'sitebox-manager-user-activity'}">User Activity</sidenav-router-link>
        </sidenav-menu>
      </template>

      <!-- System tools -->
      <template v-if="hasAccessToSystemTools">
        <sidenav-divider class="my-0"/>
        <sidenav-menu id="ebiz-system-tools-sidenav-menu" icon="ion ion-ios-settings" :active="isMenuActive(`/system`)" :open="isMenuOpen(`/system`)">
          <span class="sidenav-item d-block sidenav-link p-0" slot="link-text">*System Tools</span>
          <sidenav-router-link id="ebiz-system-tools-encryption-router-link" v-can:to="{name: 'encryptor'}" :to="{name: 'encryptor'}" :exact="true">Encryptor/Decryptor</sidenav-router-link>
          <sidenav-router-link id="ebiz-system-tools-generatetoken-router-link" v-can:to="{name: 'generatetoken'}" :to="{name: 'generatetoken'}" :exact="true">Generate Token</sidenav-router-link>
          <sidenav-router-link id="ebiz-system-tools-error-report-router-link" v-can:to="{name: 'error-report'}" :to="{name: 'error-report'}" >Error Report</sidenav-router-link>
          <sidenav-router-link id="ebiz-system-tools-error-monitor-router-link" v-can:to="{name: 'error-monitor'}" :to="{name: 'error-monitor'}">Error Monitor</sidenav-router-link>
          <sidenav-router-link id="ebiz-system-tools-api-ping-router-link" v-can:to="{name: 'api-ping'}" :to="{name: 'api-ping'}">API Ping</sidenav-router-link>
          <sidenav-router-link id="ebiz-system-tools-user-auth-logs"
            v-can:to="{name: 'system-tools-user-auth-logs'}"
            :to="{name: 'system-tools-user-auth-logs'}"
          >
            User Auth Logs
          </sidenav-router-link>
          <sidenav-router-link v-can:to="{name: 'system-tools-user-activity'}" :to="{name: 'system-tools-user-activity'}">User Activity</sidenav-router-link>
        </sidenav-menu>
      </template>
    </div>
  </sidenav>
</template>

<script>
import { Sidenav, SidenavLink, SidenavRouterLink, SidenavMenu, SidenavHeader, SidenavBlock, SidenavDivider } from '@/vendor/libs/sidenav'
import permission from '../shared/common/permissions'
import applicationTypes from '../shared/common/applicationTypes'
import ExternalLinkItem from '../components/_shared/ExternalLinkItem'
import {mapGetters} from 'vuex'

export default {
  name: 'app-layout-sidenav',
  props: {
    orientation: {
      type: String,
      default: 'vertical'
    },
    accountId: {
      type: Number,
      required: false
    },
    reportGroupId: {
      type: String,
      required: false
    },
    query: {
      type: Object,
      required: false
    },
    sidenavToggle: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      permission,
      applicationTypes,
      isEBayAccountManagedByApps: false
    }
  },
  components: {
    Sidenav,
    SidenavLink,
    SidenavRouterLink,
    SidenavMenu,
    SidenavHeader,
    SidenavBlock,
    SidenavDivider,
    ExternalLinkItem
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    ...mapGetters('analyticsGa4', ['dateRangeQuery']),
    ...mapGetters('userInventoryGeneralData', ['isInventoryEmailAdEnabled', 'alertsTotal']),
    curClasses () {
      let bg = this.layoutSidenavBg

      if (this.orientation === 'horizontal' && (bg.indexOf(' sidenav-dark') !== -1 || bg.indexOf(' sidenav-light') !== -1)) {
        bg = bg
          .replace(' sidenav-dark', '')
          .replace(' sidenav-light', '')
          .replace('-darker', '')
          .replace('-dark', '')
      }

      return `bg-${bg} ` + (
        this.orientation !== 'horizontal'
          ? 'layout-sidenav'
          : 'layout-sidenav-horizontal container-p-x flex-grow-0'
      )
    },
    user () {
      return (this.userInfo || {}).user || {}
    },
    isEbizAdmin () {
      return this.user.isEbizAdmin
    },
    isEbizDev () {
      return this.user.isEbizDev
    },
    hasAccessToSystemTools () {
      return this.user.hasPermissions && (this.user.hasPermissions(permission.EbizAutosAdmin) || this.user.hasPermissions(permission.ErrorsViewErrors))
    },
    hasAnalyticsFullAccess () {
      return this.user.hasPermissions && this.user.hasPermissions(permission.AnalyticsFullAccess)
    },
    hasCraigslistFullAccess () {
      return this.user.hasPermissions && this.user.hasPermissions(permission.CraigslistFullAccess)
    },
    hasInventoryManagementFullAccess () {
      return this.user.hasPermissions && this.user.hasPermissions(permission.IMFullAccess)
    },
    hasLeadsFullAccess () {
      return this.user.hasPermissions && this.user.hasPermissions(permission.LeadsFullAccess)
    },
    hasEBayFullAccess () {
      return this.user.hasPermissions && this.user.hasPermissions(permission.EBayFullAccess)
    },
    hasUsersFullAccess () {
      return this.user.hasPermissions && this.user.hasPermissions(permission.UsersFullAccess)
    },
    canManageMultipleAccount () {
      return this.user.canManageMultipleAccount
    },
    canManageLeadsMultipleAccount () {
      return this.user.canManageLeadsMultipleAccount
    },
    canManageInventoryMultiplyAccounts () {
      return this.user && this.user.canManageApplicationType(applicationTypes.InventoryManagement.Id, permission.IMFullAccess)
    },
    hasAccessToCraigslist () {
      return this.user.canManageAccountApplicationType(this.getAccountId, applicationTypes.AppsCraigslist.Id, permission.CraigslistFullAccess)
    },
    hasAccessToAnalytics () {
      return this.user.canManageAccountApplicationType(this.getAccountId, applicationTypes.AppsAnalytics.Id, permission.AnalyticsFullAccess)
    },
    canReadAnalyticsGa4 () {
      return this.user.hasPermissions &&
        this.user.hasPermissions(permission.GoogleAnalytics4View) && this.user.isGa4EnabledForUser
    },
    hasAccessToAnalyticsGa4 () {
      return this.canReadAnalyticsGa4 &&
        this.user.canManageAccountApplicationType(
          this.getAccountId,
          applicationTypes.AppsAnalytics.Id,
          permission.AnalyticsFullAccess
        )
    },
    hasAccessToInventory () {
      return this.user.canManageAccountApplicationType(this.getAccountId, applicationTypes.InventoryManagement.Id, permission.IMFullAccess)
    },
    hasAccessToLeads () {
      return this.user.canManageAccountApplicationType(this.getAccountId, applicationTypes.AppsLeads.Id, permission.LeadsFullAccess)
    },
    hasAccessToEBay () {
      return this.user.canManageAccountApplicationType(this.getAccountId, applicationTypes.AppsEBay.Id, permission.EBayFullAccess)
    },
    hasAccessToSiteManagement () {
      return this.user.canManageAccountApplicationType(this.getAccountId, applicationTypes.SiteManagement.Id, permission.SMFullAccess)
    },
    hasAccessToSiteBoxesManager () {
      return this.user.hasPermissions && this.user.hasPermissions(permission.SiteBoxManagerChangeSiteHosting)
    },
    hasAccessToUsers () {
      return this.user.hasPermissions && this.user.hasPermissions(permission.ManageUserAccount)
    },
    defaultAnalyticsViewId () {
      return this.user.defaultAnalyticsViewId
    },
    hasAccessToAnalyticsReportGroups () {
      return this.user.hasReportGroupsAccess
    },
    hasAccountLevelDefaultView () {
      return this.user.accountId && this.hasAccessToAnalyticsReportGroups && !this.defaultAnalyticsViewId
    },
    hasReportGroupLevelDefaultView () {
      return this.user.accountId && this.hasAccessToAnalyticsReportGroups && this.defaultAnalyticsViewId
    },
    combinedQuery () {
      if (this.query) {
        return this.query
      } else {
        return this.dateRangeQuery
      }
    },
    accountIdRouterProp () {
      return this.accountId || (this.hasAccountLevelDefaultView ? this.user.accountId : null)
    },
    reportGroupIdRouterProp () {
      return this.reportGroupId || (this.hasReportGroupLevelDefaultView ? this.defaultAnalyticsViewId : null)
    },
    hasAccountManagedByApps () {
      return this.isEBayAccountManagedByApps
    },
    getAccountId () {
      return this.accountId || this.user.accountId
    }
  },
  methods: {
    isMenuActive (url) {
      return this.$route.path.indexOf(url) === 0
    },
    isMenuOpen (url) {
      return this.$route.path.indexOf(url) === 0 && this.orientation !== 'horizontal'
    },
    toggleSidenav () {
      this.layoutHelpers.toggleCollapsed()
    },
    onAccountsTitleClick () {
      let path = `/accounts`

      setTimeout(() => this.$router.push(path), 50)
    },
    checkEBayAccountManagedByApps () {
      if (this.user && this.user.accountId && !this.hasEBayFullAccess) {
        this.$store.dispatch('eBay/checkAccountManagedByApps', {accountId: this.user.accountId}).then(res => {
          this.isEBayAccountManagedByApps = res.data
        }).catch(ex => {
        })
      }
    }
  },
  created () {
    this.checkEBayAccountManagedByApps()
  }
}
</script>

<style>
  .side-nav .app-brand-logo {
    width: 100%;
    margin-right: -25px;
  }

  .side-nav .app-brand-logo img {
    width: 11rem;
  }

  .side-nav .app-brand {
    height: 59px;
    padding-right: 0.85rem !important;
    padding-left: 1.25rem !important;
  }

  .loader,
  .loader:after {
    border-radius: 50%;
    width: 1.7em;
    height: 1.7em;
  }
  .loader {
    font-size: 10px;
    margin-top: 2.5px;
    position: relative;
    text-indent: -9999em;
    border-top: 0.21em solid rgba(255, 255, 255, 0.2);
    border-right: 0.21em solid rgba(255, 255, 255, 0.2);
    border-bottom: 0.21em solid rgba(255, 255, 255, 0.2);
    border-left: 0.21em solid #ffffff;
    -webkit-transform: translateZ(0);
    -ms-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-animation: load8 1.1s infinite linear;
    animation: load8 1.1s infinite linear;
  }
  @-webkit-keyframes load8 {
    0% {
      -webkit-transform: rotate(0deg);
      transform: rotate(0deg);
    }
    100% {
      -webkit-transform: rotate(360deg);
      transform: rotate(360deg);
    }
  }
  @keyframes load8 {
    0% {
      -webkit-transform: rotate(0deg);
      transform: rotate(0deg);
    }
    100% {
      -webkit-transform: rotate(360deg);
      transform: rotate(360deg);
    }
  }
</style>
