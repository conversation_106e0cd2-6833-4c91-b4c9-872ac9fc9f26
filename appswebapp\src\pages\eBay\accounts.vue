<template>
  <div>
    <h4>Account Listing</h4>
    <b-card>
      <b-row>
        <b-col xl="6" lg="6" md="5" sm="12">
          <b-form @submit.prevent="applySearch">
            <b-input-group>
              <b-form-input v-model="filters.search" placeholder="Search..."/>
              <b-input-group-append>
                <b-btn type="submit">Go</b-btn>
              </b-input-group-append>
            </b-input-group>
          </b-form>
        </b-col>
        <b-col xl="6" lg="6" md="7" sm="12" class="py-2 d-flex justify-content-md-end justify-content-sm-start mt-sm-3 mt-md-0">
          <b-form-checkbox-group
            v-model="selectedFilters"
            :options="filterOptions"
            @input="onChangeCategory"
          >
          </b-form-checkbox-group>
        </b-col>
      </b-row>
    </b-card>
    <b-card v-if="!isLoading && items && items.length > 0">
      <b-table
        :items="items"
        :fields="tableFields"
        :sort-by="tableSortBy"
        :sort-desc="tableSortDesc"
        @sort-changed="onSortChanged"
        :striped="true"
        :bordered="false"
        :no-sort-reset="true"
        :no-local-sorting="true"
        responsive
        class="products-table card-table"
      >
        <template #cell(AccountId)="data">
          <router-link :to="{name: 'ebay-inventory', params: { accountId: data.item.AccountId }} ">{{data.item.AccountId}}</router-link>
        </template>
        <template #cell(AccountName)="data">
          <router-link :to="{name: 'ebay-inventory', params: { accountId: data.item.AccountId }} ">{{data.item.AccountName}}</router-link>
        </template>
        <template #cell(Manage)="data">
          <b-dropdown v-if="data.item.HasEBayLocalAuctions || data.item.HasEBayLocalScheduledItemApiErrors"
            variant="outline-secondary icon-btn btn-round" size="sm" right no-caret boundary='viewport'>
            <template slot="button-content">
              <i class="ion ion-ios-more m-0"></i><span class="sr-only">Manage</span>
            </template>
            <b-dropdown-item v-if="data.item.HasEBayLocalAuctions" @click="onReviseLocalAuctions(data.item.AccountId)">Revise eBay Local Auctions</b-dropdown-item>
            <b-dropdown-item v-if="data.item.HasEBayLocalAuctions" @click="onEndLocalAuctions(data.item.AccountId)">End eBay Local Auctions</b-dropdown-item>
            <b-dropdown-item v-if="data.item.HasEBayLocalScheduledItemApiErrors" @click="onUnscheduleLocalApiErrors(data.item.AccountId)">Unschedule Local API Errors</b-dropdown-item>
          </b-dropdown>
          <span v-else>No Actions</span>
        </template>
      </b-table>
      <paging
        class="p-0"
        :pageNumber="filters.page"
        :pageSize="filters.pageSize"
        :totalItems="totalItems"
        titled
        pageSizeSelector
        @numberChanged="onPageChanged"
        @changePageSize="onChangePageSize"
      />
    </b-card>
    <div v-else-if="!isLoading">
      <span class="text-muted">Not Found</span>
    </div>
    <div v-else class="mt-3 pt-3">
      <loader size="lg"/>
    </div>
  </div>
</template>

<script>
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import paging from '@/components/_shared/paging'
import loader from '@/components/_shared/loader'
import Constants from '@/shared/ebay/constants'
import {mapGetters} from 'vuex'

const defaultValues = new ObjectSchema({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  search: { type: String, default: '' },
  categories: { type: String, default: '' },
  sort: { type: Number, default: 2 }
})

const queryHelper = new QueryStringHelper(defaultValues)

export default {
  name: 'ebay-accounts',
  metaInfo: {
    title: 'eBay Accounts'
  },
  data () {
    return {
      isLoading: true,
      selectedFilters: [],
      items: [],
      totalItems: 0,
      filters: defaultValues.getObject()
    }
  },
  created () {
    this.filters = queryHelper.parseQueryStringToObject(this.$router)
    this.selectedFilters = this.filters.categories ? this.filters.categories.split(',') : []
    this.populateData()
  },
  components: {
    paging,
    loader
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {}
    },
    filterOptions () {
      return [
        {
          value: 1,
          text: 'Errors'
        },
        {
          value: 2,
          text: 'Scheduled'
        },
        {
          value: 3,
          text: 'Active'
        }
      ]
    },
    tableFields () {
      let tabFields = [
        {
          key: 'AccountId',
          label: 'Account Id',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'AccountName',
          label: 'Account Name',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'ErrorsCount',
          label: 'Errors',
          tdClass: 'py-2 align-middle',
          sortTypeAsc: Constants.accountListingSortTypes.errorsAsc,
          sortTypeDesc: Constants.accountListingSortTypes.errorsDesc,
          sortable: true
        },
        {
          key: 'ScheduledCount',
          label: 'Scheduled',
          tdClass: 'py-2 align-middle',
          sortTypeAsc: Constants.accountListingSortTypes.scheduledAsc,
          sortTypeDesc: Constants.accountListingSortTypes.scheduledDesc,
          sortable: true
        },
        {
          key: 'ActiveListingsCount',
          label: 'Active Listings',
          tdClass: 'py-2 align-middle',
          sortTypeAsc: Constants.accountListingSortTypes.activeListingsAsc,
          sortTypeDesc: Constants.accountListingSortTypes.activeListingsDesc,
          sortable: true
        }
      ]
      if (this.user.isEbizAdmin) {
        tabFields.push({
          key: 'Manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle'
        })
      }

      return tabFields
    },
    tableSortBy () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sortType
      } else {
        return false
      }
    }
  },
  methods: {
    applySearch () {
      this.filters.page = 1
      this.synchronizeUrlAndReload()
    },
    onPageChanged (newPage) {
      this.filters.page = newPage
      this.synchronizeUrlAndReload()
    },
    onSortChanged (value) {
      const sortingColumn = this.tableFields.find(x => x.key === value.sortBy)
      if (value.sortDesc) {
        this.filters.sort = sortingColumn.sortTypeDesc
      } else {
        this.filters.sort = sortingColumn.sortTypeAsc
      }
      this.synchronizeUrlAndReload()
    },
    onChangeCategory () {
      this.filters.categories = (this.selectedFilters || []).join(',')
      this.page = 1
      this.isLoading = true
      this.synchronizeUrlAndReload()
    },
    onChangePageSize (newSize) {
      this.filters.pageSize = newSize
      this.filters.page = 1
      this.synchronizeUrlAndReload()
    },
    synchronizeUrlAndReload () {
      queryHelper.rebuildParamsInQueryString(this.$router, this.filters)
      this.populateData()
    },
    populateData () {
      this.$store.dispatch('eBay/getAccounts', { filters: this.filters }).then(res => {
        this.items = res.data.Items
        this.totalItems = res.data.TotalItems
      }).catch(ex => {
        this.$toaster.error('Something went wrong. Please try again later')
        this.$logger.handleError(ex, 'Cannot populate eBay account listing')
      }).finally(() => {
        this.isLoading = false
      })
    },
    onReviseLocalAuctions (accountId) {
      this.$store.dispatch('eBay/reviseLocalAuctions', accountId).then(res => {
        if (res.data) {
          this.$toaster.success('Moved Revise eBay Local Auctions in Processed Successfully')
        } else {
          this.$toaster.error('No Active eBay Local Auctions')
        }
      }).catch(ex => {
        this.$toaster.error(`Something went wrong! <br/>${(ex.response || {}).data ? 'Message: ' + ex.response.data : ''}`)
        this.$logger.handleError(ex, 'Exception occurred on revise local auctions')
      })
    },
    onEndLocalAuctions (accountId) {
      this.$store.dispatch('eBay/endLocalAuctions', accountId).then(res => {
        if (res.data) {
          this.$toaster.success('Moved End eBay Local Auctions in Processed Successfully')
        } else {
          this.$toaster.error('No Active eBay Local Auctions')
        }
      }).catch(ex => {
        this.$toaster.error(`Something went wrong! <br/>${(ex.response || {}).data ? 'Message: ' + ex.response.data : ''}`)
        this.$logger.handleError(ex, 'Exception occurred on end local auctions')
      })
    },
    onUnscheduleLocalApiErrors (accountId) {
      this.$store.dispatch('eBay/unscheduleErrorLocalItems', accountId).then(res => {
        this.$toaster.success('Unscheduled Local API Errors Successfully')
      }).catch(ex => {
        this.$toaster.error(`Something went wrong! <br/>${(ex.response || {}).data ? 'Message: ' + ex.response.data : ''}`)
        this.$logger.handleError(ex, 'Exception occurred on unschedule local API errors')
      }).finally(() => {
        this.populateData()
      })
    }
  }
}
</script>
