<template>
  <div>
    <b-table
    class="products-table card-table"
    :fields="tableFields"
    :items="items"
    :striped="true"
    :bordered="false"
    :no-sort-reset="true"
    :no-local-sorting="true"
    :sort-by.sync="sortBy"
    :sort-desc.sync="sortDesc"
    @sort-changed="onSortChanged"
    responsive
    >
      <template #cell(title)="data">
        <div class="media align-items-center">
          <router-link class="media-body text-dark d-flex align-items-center" :to="{ path: getDetailsPath(data.item) }">
            <img class="d-block float-left ui-w-50 mr-3" style="min-height: 1px" :src="getVehiclePhotoSrc(data.item)">
            <span>{{data.item | getVehicleTitle}}</span>
          </router-link>
        </div>
      </template>
      <template #cell(galleryDescription)="data">
        <i class="ion ion-ios-checkmark text-success zoomeds" v-if="!data.item.hasDescriptionAlert"></i>
        <i class="ion ion-ios-close text-danger zoomeds" v-else></i>
      </template>
      <template #cell(missingPrices)="data">
        <i class="ion ion-ios-checkmark text-success zoomeds" v-if="!data.item.hasPriceAlert"></i>
        <i class="ion ion-ios-close text-danger zoomeds" v-else></i>
      </template>
      <template #cell(missingPhotos)="data">
        <i class="ion ion-ios-checkmark text-success zoomeds" v-if="!data.item.hasPhotoAlert"></i>
        <i class="ion ion-ios-close text-danger zoomeds" v-else></i>
      </template>
      <template #cell(missingVideos)="data">
        <i class="ion ion-ios-checkmark text-success zoomeds" v-if="!data.item.hasVideoAlert"></i>
        <i class="ion ion-ios-close text-danger zoomeds" v-else></i>
      </template>
      <template #cell(actions)="data">
         <b-dropdown variant="outline-secondary icon-btn btn-round" v-if="hasAtLeastOneAlert(data.item)" size="sm" right no-caret boundary='viewport'>
            <template slot="button-content">
              <i class="ion ion-ios-more m-0"></i><span class="sr-only">Manage</span>
            </template>
            <b-dropdown-item v-if="data.item.hasPhotoAlert" :to="getPhotoEditPath(data.item)">Add Photos</b-dropdown-item>
            <b-dropdown-item v-if="data.item.hasVideoAlert" :to="getVideoEditPath(data.item)">Add Videos</b-dropdown-item>
            <b-dropdown-item v-if="data.item.hasPriceAlert" :to="getPriceEditPath(data.item)">Add Pricing</b-dropdown-item>
            <b-dropdown-item v-if="data.item.hasDescriptionAlert" :to="getDescriptionEditPath(data.item)">Edit Description</b-dropdown-item>
          </b-dropdown>
      </template>
    </b-table>
  </div>
</template>

<script>
export default {
  name: 'inventory-task-all-listing',
  props: {
    sortField: String,
    sortOrderDesc: Boolean,
    items: Array
  },
  filters: {
    getVehicleTitle: function (value) {
      if (!value) return ''
      let title = ''
      if (value.year > 0) {
        title = value.year.toString()
      }

      title = [title, value.make, value.model, value.trim].filter(v => v && v !== '').join(' ')

      return title.trim()
    }
  },
  computed: {
    tableFields () {
      return [
        {
          key: 'title',
          label: 'Vehicle',
          tdClass: 'py-2 align-middle',
          sortable: true
        },
        {
          key: 'stockNumber',
          label: 'Stock #',
          tdClass: 'py-2 align-middle',
          sortable: true
        },
        {
          key: 'totalAlerts',
          label: 'Total Alerts',
          tdClass: 'py-2 align-middle',
          sortable: true
        },
        {
          key: 'missingPhotos',
          label: 'Missing Photos',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'missingVideos',
          label: 'Missing Videos',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'missingPrices',
          label: 'Missing Price',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'galleryDescription',
          label: 'Description',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'actions',
          label: 'Actions',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    sortBy: {
      get () {
        return this.sortField
      },
      set () {}
    },
    sortDesc: {
      get () {
        return this.sortOrderDesc
      },
      set () {}
    }
  },
  methods: {
    onSortChanged (value) {
      this.$emit('sortChanged', value)
    },
    getVehiclePhotoSrc (item) {
      return (item.presentationPhoto || {}).photo107 + '?q=' + item.actualPhotosDateTimeEdited
    },
    getDetailsPath (item) {
      return `/inventory/${item.accountId}/edit/${item.vin}`
    },
    getDescriptionEditPath (item) {
      return `/inventory/${item.accountId}/edit/${item.vin}?tabindex=3`
    },
    getPhotoEditPath (item) {
      return `/inventory/${item.accountId}/edit/${item.vin}?tabindex=2`
    },
    getVideoEditPath (item) {
      return `/inventory/${item.accountId}/edit/${item.vin}?tabindex=2`
    },
    getPriceEditPath (item) {
      return `/inventory/${item.accountId}/edit/${item.vin}?tabindex=1`
    },
    hasGalleryDescription (item) {
      let div = document.createElement('div')
      div.innerHTML = item.galleryDescription
      return div.textContent.trim() !== ''
    },
    hasAtLeastOneAlert (item) {
      return item.hasPhotoAlert || item.hasVideoAlert || item.hasPriceAlert || item.hasDescriptionAlert
    }
  }
}
</script>

<style lang="scss" scoped>
  .zoomeds {
    font-size: 2.2rem;
  }
</style>
