<template>
  <div>
    <b-row>
      <b-col><h4>User Activity</h4></b-col>
      <b-col>
        <paging
          v-if="totalItemsCount > 0"
          class="custom-accounts-tab-paging d-none d-md-block"
          :pageNumber="filters.page"
          :pageSize="filters.pageSize"
          :totalItems="totalItemsCount"
          @numberChanged="changePage"
          @changePageSize="changePageSize"
        />
      </b-col>
    </b-row>
     <b-tabs class="nav-tabs-top nav-responsive-sm" v-model="selectedTab" no-fade>
      <b-tab v-for="tab in getTabTypeOptions" :key="tab.value" :title="tab.text">
        <userActivityFiltersForm :filters="filters" :type="filters.tabType" @filtersChange="filtersChange">
        </userActivityFiltersForm>
      </b-tab>
    </b-tabs>
    <b-card v-if="!isLoading">
      <userActivityListing :items="items" :sortType="filters.sort" :type="filters.tabType" @sortChange="sortChange">
      </userActivityListing>
      <paging
        v-if="totalItemsCount > 0"
        :pageNumber="filters.page"
        :pageSize="filters.pageSize"
        :totalItems="totalItemsCount"
        @numberChanged="changePage"
        @changePageSize="changePageSize"
        titled
        pageSizeSelector
      />
    </b-card>
    <div v-else>
      <loader class="mt-4" size="lg"/>
    </div>
  </div>
</template>

<script>
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import globals from '../../globals'
import AccountUserActivityService from '@/services/accounts/AccountUserActivityService'
import userActivityConstants from '@/shared/accounts/userActivityConstants'
import userActivityFiltersForm from '../../components/account/userActivity/userActivityFiltersForm.vue'
import userActivityListing from '@/components/account/userActivity/userActivityListing'
import {userTypes} from '@/shared/users/constants'
import paging from '../../components/_shared/paging.vue'
import loader from '../../components/_shared/loader.vue'

const defaultFilters = new ObjectSchema({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  search: { type: String, default: '' },
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  sort: { type: Number, default: 6 },
  action: { type: Number, default: 0 },
  userTypes: { type: String, default: `${userTypes.cpUser.value},${userTypes.adminUser.value}` },
  tabType: { type: Number, default: 1 }
})
const queryHelper = new QueryStringHelper(defaultFilters)

export default {
  name: 'accounts-user-activity',
  metaInfo: {
    title: 'User Activity'
  },
  data () {
    return {
      isLoading: true,
      items: [],
      totalItemsCount: 0,
      filters: globals().getClonedValue(defaultFilters.getObject())
    }
  },
  components: {
    userActivityFiltersForm,
    userActivityListing,
    paging,
    loader
  },
  computed: {
    selectedTab: {
      get () {
        if (this.filters.tabType < 1 || this.filters.tabType > this.getTabTypeOptions.length) {
          return 0
        }
        return this.filters.tabType - 1
      },
      set (value) {
        this.filters.tabType = value + 1
        this.setDefaultFilters()
        this.isLoading = true
        this.synchronizeUrlAndReload()
      }
    },
    getTabTypeOptions () {
      let options = []
      Object.values(userActivityConstants.userActivityTypes).forEach(x => {
        if (x.value !== 0) {
          options.push(x)
        }
      })
      return options
    }
  },
  created () {
    this.filters = queryHelper.parseQueryStringToObject(this.$router)
    this.populateData()
  },
  methods: {
    synchronizeUrlAndReload () {
      queryHelper.rebuildParamsInQueryString(this.$router, this.filters)
      this.populateData()
    },
    filtersChange () {
      this.filters.page = 1
      this.isLoading = true
      this.synchronizeUrlAndReload()
    },
    changePage (newPage) {
      this.filters.page = newPage
      this.isLoading = true
      this.synchronizeUrlAndReload()
    },
    changePageSize (newPageSize) {
      this.filters.page = 1
      this.filters.pageSize = newPageSize
      this.isLoading = true
      this.synchronizeUrlAndReload()
    },
    sortChange (newSort) {
      this.filters.sort = newSort
      this.synchronizeUrlAndReload()
    },
    async populateData () {
      let getterMethod = AccountUserActivityService.getUserActivityGetterMethodByType(this.filters.tabType)
      if (getterMethod) {
        getterMethod(this.filters).then(res => {
          this.items = res.data.items
          this.totalItemsCount = res.data.itemsTotalCount
        }).catch(ex => {
          this.$logger.handleError(ex, 'Exception occurred on api call', { params: this.filters })
        }).finally(() => {
          this.isLoading = false
        })
      } else {
        console.error(`Not found getter method for user activity type: ${this.filters.tabType}`)
        this.isLoading = false
      }
    },
    setDefaultFilters () {
      let tabType = globals().getClonedValue(this.filters.tabType)
      this.filters = globals().getClonedValue(defaultFilters.getObject())
      this.filters.tabType = tabType
    }
  }
}
</script>

<style scoped>
.custom-accounts-tab-paging {
  position: absolute;
  right: 10px;
  z-index: 2;
}
</style>
