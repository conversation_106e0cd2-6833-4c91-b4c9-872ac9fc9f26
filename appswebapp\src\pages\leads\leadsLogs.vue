<template>
  <div>
    <b-row class="ml-2">
      <h4 class="float-left">Logs</h4>
    </b-row>
    <paging
      class="custom-leads-logs-paging p-0"
      :pageNumber="filter.page"
      :pageSize="filter.pageSize"
      :totalItems="itemsTotalCount"
      @numberChanged="onPageChanged"
      @changePageSize="onChangePageSize"
    />
    <b-tabs v-model='selectedTabIndex' class="nav-tabs-top nav-responsive-sm" @activate-tab="onActivateTab" no-fade>
      <b-tab v-for='tab in getTabsOption' :key='tab.key' :title='tab.title'>
        <leads-log-filter-form :filter='filter' :logType='tab.key' @applyFilter='applyFilter'/>
      </b-tab>
    </b-tabs>
    <b-card v-if='!isLoading && items.length > 0'>
      <leads-log-listing :logType='filter.logType' :sort='filter.sort' @sortChange='logsSortChange' :items='items'/>
      <paging
        class="p-0"
        :pageNumber="filter.page"
        :pageSize="filter.pageSize"
        :totalItems="itemsTotalCount"
        titled
        pageSizeSelector
        @numberChanged="onPageChanged"
        @changePageSize="onChangePageSize"
      />
    </b-card>
    <div v-else-if='isLoading' class="leads-log-loader-margin">
      <loader size='lg'/>
    </div>
    <div v-else>
      <span class="text-muted">No Logs Found</span>
    </div>
  </div>
</template>

<script>
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import { logTypes } from '@/shared/leads/common'
import {mapGetters} from 'vuex'
import leadsLogFilterForm from '@/components/leads/logs/leadsLogFilterForm'
import leadsLogListing from '@/components/leads/logs/leadsLogListing'
import LeadsLogService from '@/services/logs/LeadsLogService'
import loader from '@/components/_shared/loader'

const defaultFilters = new ObjectSchema({
  accountId: {type: Number, default: 0},
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  search: { type: String, default: '' },
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  sort: { type: Number, default: 0 },
  logType: { type: Number, default: 0 },
  resultstatus: { type: String, default: '' },
  communicationtype: { type: String, default: '' },
  operationtype: { type: String, default: '' },
  statistictasktype: { type: String, default: '' },
  processingstatus: { type: String, default: '' },
  spamscanstatus: { type: String, default: '' },
  turnstilevalidationstatus: { type: String, default: '' },
  bodyscantype: { type: String, default: '' }
})

const queryStringHelper = new QueryStringHelper(defaultFilters)

export default {
  name: 'leads-logs',
  metaInfo: {
    title: 'Logs'
  },
  props: {
  },
  data () {
    return {
      filter: defaultFilters.getObject(),
      isLoading: true,
      items: [],
      itemsTotalCount: 0
    }
  },
  components: {
    'paging': () => import('@/components/_shared/paging'),
    'loader': loader,
    'leads-log-filter-form': leadsLogFilterForm,
    'leads-log-listing': leadsLogListing
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user
    },
    selectedTabIndex: {
      get () {
        return this.getTabIndex(this.filter.logType)
      },
      set (index) {
        this.filter.logType = index
        this.filter.page = 1
        this.filter.search = ''
        this.filter.sort = 0
        this.items = []
        this.itemsTotalCount = 0
        this.synchronizeUrlAndReload()
      }
    },
    getTabsOption () {
      let tabOptions = [
        { key: 0, title: 'Voice' },
        { key: 1, title: 'SMS' },
        { key: 2, title: 'Email' },
        { key: 3, title: 'Gallery' },
        { key: 4, title: 'eBay' },
        { key: 5, title: 'Statistic' },
        { key: 6, title: 'Leads Notification' }
      ]

      if (this.user && this.user.isEbizAdmin) {
        tabOptions.push({ key: 7, title: 'Service Log' })
        tabOptions.push({ key: 8, title: 'Synchronization' })
      }

      return tabOptions
    }
  },
  created () {
    this.filter = queryStringHelper.parseQueryStringToObject(this.$router)
    this.loadContent()
  },
  methods: {
    onActivateTab (newTabIndex, prevTabIndex, bvEvent) {
      if (this.isLoading) {
        bvEvent.preventDefault()
      }
    },
    getTabIndex (index) {
      if (index < 0 || index > this.getTabsOption.length) {
        return 0
      }

      let tab = this.getTabsOption.find(x => x.key === index)
      if (tab) {
        return tab.key
      }

      return 0
    },
    onPageChanged (newPage) {
      this.filter.page = newPage
      this.synchronizeUrlAndReload()
    },
    onChangePageSize (newSize) {
      this.filter.pageSize = newSize
      this.filter.page = 1
      this.synchronizeUrlAndReload()
    },
    applyFilter () {
      this.filter.page = 1
      this.synchronizeUrlAndReload()
    },
    logsSortChange (newSort) {
      this.filter.sort = newSort
      queryStringHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.loadContent()
    },
    synchronizeUrlAndReload () {
      queryStringHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.isLoading = true
      this.loadContent()
    },
    async getLogsItem () {
      let result = null
      switch (this.selectedTabIndex) {
        case logTypes.voice.key:
          result = await LeadsLogService.getLeadsVoiceApiLogs(this.filter)
          break
        case logTypes.sms.key:
          result = await LeadsLogService.getLeadsSMSApiLogs(this.filter)
          break
        case logTypes.email.key:
          result = await LeadsLogService.getLeadsEmailApiLogs(this.filter)
          break
        case logTypes.gallery.key:
          result = await LeadsLogService.getLeadsGalleryApiLogs(this.filter)
          break
        case logTypes.eBay.key:
          result = await LeadsLogService.getLeadsEbayApiLogs(this.filter)
          break
        case logTypes.statistic.key:
          result = await LeadsLogService.getLeadsStatisticApiLogs(this.filter)
          break
        case logTypes.leadsNotification.key:
          result = await LeadsLogService.getLeadsNotificationApiLogs(this.filter)
          break
        case logTypes.serviceLog.key:
          result = await LeadsLogService.getLeadsServiceApiLogs(this.filter)
          break
        case logTypes.synchronization.key:
          result = await LeadsLogService.getLeadsSynchronizationApiLogs(this.filter)
          break
      }

      return result
    },
    loadContent () {
      this.getLogsItem().then(result => {
        let responseData = (result || {data: {}}).data || {}
        this.items = responseData.logItems || []
        this.itemsTotalCount = responseData.totalItemsCount || 0
      }).catch(ex => {
        this.$toaster.exception(ex, `Cannot get logs`)
        this.$logger.handleError(ex, `Cannot get leads logs`)
      }).finally(() => {
        this.isLoading = false
      })
    }
  }
}
</script>

<style>
.custom-leads-logs-paging {
  position: absolute;
  right: 33px;
  top: 120px;
  z-index: 2;
}
.leads-log-loader-margin {
  margin-top: 150px;
}
@media(max-width: 1376px) {
  .custom-leads-logs-paging {
    display: none;
  }
}
</style>
