﻿using EBizAutos.Apps.AccountManagement.Api.ServiceBus.Handlers;
using EBizAutos.CommonLib.Exceptions;
using EBizAutos.CommonLib.ServiceBus;
using EBizAutos.CommonLibCore;
using System;
using System.Threading.Tasks;

namespace EBizAutos.Apps.AccountManagement.Api.ServiceBus.Consumers {
	internal abstract class BaseEventConsumer<TEvent> : IConsumer<TEvent> where TEvent : class, IServiceBusEvent {

		private readonly IEventHandler<TEvent, bool> _eventHandler = null;
		private readonly ExceptionHandler _exceptionHandler = null;

		protected BaseEventConsumer(IEventHandler<TEvent, bool> eventHandler, ExceptionHandler exceptionHandler) {
			_eventHandler = eventHandler;
			_exceptionHandler = exceptionHandler;
		}

		public virtual async Task Consume(IConsumeContext<TEvent> context) {
			PromiseResult<bool> promise = await _eventHandler.HandleAsync(context);

			if (promise.IsRejected && promise.Exception != null) {
				LogException(promise.Exception);
				throw promise.Exception;
			}
		}

		private void LogException(Exception ex) {
			_exceptionHandler.InsertError(ex, $"Exception occurred on process event: {typeof(TEvent).Name}");
		}
	}
}