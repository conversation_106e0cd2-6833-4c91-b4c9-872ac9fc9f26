import axios from 'axios'

const actions = {
  getSpecialsCategories (_, parameters) {
    return axios.get('/api/inventory/categories/specials/categories', {params: parameters.params})
  },
  getSpecialsMakes (_, parameters) {
    return axios.get(`/api/inventory/categories/specials/makes`, {params: parameters.params})
  },
  getSpecialsModels (_, parameters) {
    return axios.get(`/api/inventory/categories/specials/models`, {params: parameters.params})
  },
  getSpecialsTrims (_, parameters) {
    return axios.get(`/api/inventory/categories/specials/trims`, {params: parameters.params})
  },
  getSpecialsYears (_, parameters) {
    return axios.get(`/api/inventory/categories/specials/years`, {params: parameters.params})
  }
}

export default {
  namespaced: true,
  actions: actions
}
