<template>
  <div>
    <div class="d-flex flex-row">
      <h4 class="mt-3">User Activity</h4>
      <!-- Pagination -->
      <paging
        class="d-none d-md-block p-0"
        :pageNumber="filters.page"
        :pageSize="filters.pageSize"
        :totalItems="totalItemsCount"
        @numberChanged="pageChanged"
        @changePageSize="changePageSize"
      />
    </div>
    <b-card>
      <b-form  v-on:submit.prevent="applyFilter">
        <div class="form-row">
          <b-col lg="2 mt-2 mb-2">
            <b-form-input
              max='200'
              v-model="filters.search"
              placeholder="User Name"
              autocomplete="off"
            >
            </b-form-input>
          </b-col>

          <b-col lg="3 mt-2 mb-2">
            <b-input-group class="flex-nowrap">
              <b-input-group-prepend is-text>
                <i class="ion ion-md-calendar" slot="prepend"></i>
              </b-input-group-prepend>
              <date-time-picker
                ref="timeFrom"
                v-model="filters.dateFrom"
                :options="filterTimeOptions"
                format="MM/DD/YYYY HH:mm"
                placeholder="Date From"
                className="form-control"
                @change="onTimeFromInputChange"
              />
              <b-input-group-append
                is-text
                v-show="filters.dateFrom"
                @click="filters.dateFrom = null"
              >
                <i class="ion ion-md-close"></i>
              </b-input-group-append>
            </b-input-group>
          </b-col>

          <b-col lg="3 mt-2 mb-2">
            <b-input-group class="flex-nowrap">
              <b-input-group-prepend is-text>
                <i class="ion ion-md-calendar" slot="prepend"></i>
              </b-input-group-prepend>
              <date-time-picker
                ref="timeTo"
                v-model="filters.dateTo"
                :options="filterTimeOptions"
                format="MM/DD/YYYY HH:mm"
                placeholder="Date To"
                className="form-control"
                @change="onTimeToInputChange"
              />
              <b-input-group-append
                is-text
                v-show="filters.dateTo"
                @click="filters.dateTo = null"
              >
                <i class="ion ion-md-close"></i>
              </b-input-group-append>
            </b-input-group>
          </b-col>
          <b-col lg="2 mt-2 mb-2">
            <b-form-select
              v-model="filters.userActionType"
              :options="getUserActionTypeOptions"
            >
            </b-form-select>
          </b-col>
          <b-col lg="2 mt-2 mb-2">
            <b-btn block  variant="primary" type="submit">Submit</b-btn>
          </b-col>
        </div>
      </b-form>
    </b-card>
    <b-card v-if="!isLoading">
      <b-table
        :items="items"
        :fields="getTableFields"
        :sort-by="tableSortBy"
        :sort-desc="tableSortDesc"
        @sort-changed="onSortChanged"
        :striped="true"
        :bordered="false"
        :no-sort-reset="true"
        :no-local-sorting="true"
        responsive
        show-empty
        class="products-table card-table"
      >
        <template #cell(manage)="data">
          <b-btn size="sm" @click="onShowDetails(data)">{{ data.detailsShowing ? 'Hide' : 'Show' }} Details</b-btn>
        </template>
        <template #row-details="data">
          <b-card>
            <log-node
              v-if='data.item.nodes'
              :data="data.item.nodes"
              :isExpandedShallow="true"
              :isExpandedDeep="false"
            />
          </b-card>
        </template>
      </b-table>
      <paging
        :pageNumber="filters.page"
        :pageSize="filters.pageSize"
        :totalItems="totalItemsCount"
        titled
        pageSizeSelector
        @numberChanged="pageChanged"
        @changePageSize="changePageSize"
      />
    </b-card>
    <loader v-else class="mt-4" size="lg"/>
  </div>
</template>

<script>
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import loader from '@/components/_shared/loader'
import moment from 'moment'
import UserActivityService from '@/services/users/UserActivityService'
import { userActivitySortTypes, userActivityActionTypes } from '@/shared/users/constants'

const defaultFilters = new ObjectSchema({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  search: { type: String, default: '' },
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  sort: { type: Number, default: 6 },
  userActionType: { type: Number, default: 0 }
})
const queryHelper = new QueryStringHelper(defaultFilters)

export default {
  name: 'website-user-activity',
  metaInfo: {
    title: 'User Activity'
  },
  data () {
    return {
      filters: defaultFilters.getObject(),
      items: [],
      totalItemsCount: 0,
      isLoading: true,
      filterTimeOptions: {
        autoUpdateInput: false,
        singleDatePicker: true,
        timePicker: true,
        timePicker24Hour: true,
        maxDate: new Date()
      }
    }
  },
  computed: {
    getUserActionTypeOptions () {
      return userActivityActionTypes
    },
    getTableFields () {
      return [
        {
          key: 'requestInformation.userName',
          label: 'User Name',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: userActivitySortTypes.userNameAsc,
          sortTypeDesc: userActivitySortTypes.userNameDesc
        },
        {
          key: 'userActionType',
          label: 'Action',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: userActivitySortTypes.userActionTypeAsc,
          sortTypeDesc: userActivitySortTypes.userActionTypeDesc,
          formatter: val => {
            if (val) {
              let userActionType = userActivityActionTypes.find(x => x.value === val)
              return (userActionType || {text: '-'}).text
            }
            return '-'
          }
        },
        {
          key: 'startProcessingDateTime',
          label: 'Date',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: userActivitySortTypes.dateAsc,
          sortTypeDesc: userActivitySortTypes.dateDesc,
          formatter: val => val ? moment(val).format('MM/DD/YYYY hh:mm:ss A') : '-'
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    refDateTimeFrom () {
      return (this.$refs.timeFrom || {}).$el || {}
    },
    refDateTimeTo () {
      return (this.$refs.timeTo || {}).$el || {}
    },
    tableSortBy () {
      const sortedColumn = this.getTableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.getTableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sortType
      } else {
        return false
      }
    }
  },
  created () {
    this.filters = queryHelper.parseQueryStringToObject(this.$router)
    this.populateData()
  },
  components: {
    'paging': () => import('@/components/_shared/paging'),
    'date-time-picker': () => import('@gravitano/vue-date-range-picker/src/components/DateRangePicker'),
    'loader': loader,
    'log-node': () => import('@/components/_shared/logItemNode.vue')
  },
  methods: {
    pageChanged (newPage) {
      this.filters.page = newPage
      this.synchronizeUrlAndReload()
    },
    changePageSize (newSize) {
      this.filters.pageSize = newSize
      this.filters.page = 1
      this.synchronizeUrlAndReload()
    },
    onSortChanged (value) {
      const sortingColumn = this.getTableFields.find(x => x.key === value.sortBy)
      if (value.sortDesc) {
        this.filters.sort = sortingColumn.sortTypeDesc
      } else {
        this.filters.sort = sortingColumn.sortTypeAsc
      }
      this.populateData()
    },
    onTimeFromInputChange (newVal) {
      this.refDateTimeFrom.value = newVal || this.filters.dateFrom || null
    },
    onTimeToInputChange (newVal) {
      this.refDateTimeTo.value = newVal || this.filters.dateTo || null
    },
    applyFilter () {
      this.filters.page = 1
      this.synchronizeUrlAndReload()
    },
    synchronizeUrlAndReload () {
      this.isLoading = false
      queryHelper.rebuildParamsInQueryString(this.$router, this.filters)
      this.populateData()
    },
    populateData () {
      UserActivityService.getUserActivityListing(this.filters).then(res => {
        this.items = res.data.items
        this.totalItemsCount = res.data.itemsTotalCount
      }).catch(ex => {
        this.$logger.handleError(ex, 'Exception occurred on api calling')
      }).finally(() => {
        this.isLoading = false
      })
    },
    onShowDetails (data) {
      if (data.item.nodes) {
        data.toggleDetails()
      } else {
        UserActivityService.getUserActivityDetails(data.item.id).then(res => {
          this.$set(data.item, 'nodes', { nodes: res.data.details })

          data.toggleDetails()
        }).catch(ex => {
          this.$toaster.exception(ex, 'Failed on getting User Activity details')
          this.$logger.handleError(ex, 'Exception occurred on api calling')
        })
      }
    }
  }
}
</script>
