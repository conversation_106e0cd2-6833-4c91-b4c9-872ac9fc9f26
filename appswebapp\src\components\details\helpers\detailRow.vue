<template>
  <b-row class="detail-row" :class="{ 'edit-mode': editMode, 'mobile-wrap': mobileWrap, 'error': hasErrors }">

    <b-col v-if="hasTitleSlot" class="detail-row__title text-muted" :class="getTitleClasses">
      <slot name="title"></slot>
    </b-col>

    <b-col class="detail-row__payload" :class="{ 'col-sm-5 col-md-4 col-xl-3': isShortEditCol,
      'detail-row__fixed-width': fixedPayloadWidth,
      'detail-row__big-fixed-width': bigPayloadWidth,
      'detail-row__large-fixed-width': largePayloadWidth,
      'detail-row__extra-large-fixed-width': extraLargePayloadWidth
      } ">
      <slot name="payload"></slot>
      <span class="w-100 detail-row__payload__error">{{getError}}</span>
    </b-col>

  </b-row>
</template>

<script>
export default {
  props: {
    bootstrapMode: Boolean,
    fixedPayloadWidth: <PERSON><PERSON>an,
    bigPayloadWidth: Boolean,
    largePayloadWidth: <PERSON><PERSON><PERSON>,
    extraLargePayloadWidth: Boolean,
    editMode: Boolean,
    mobileWrap: Boolean,
    error: [String, Array],
    titlePosition: {
      type: String,
      validator (val) {
        return ['start', 'end', 'center', 'baseline', 'stretch', ''].includes(val)
      }
    }
  },
  computed: {
    hasTitleSlot () {
      return !!this.$slots.title
    },
    isShortEditCol () {
      return this.bootstrapMode && this.editMode
    },
    getError () {
      if (Array.isArray(this.error)) {
        return this.error[0]
      }
      return this.error
    },
    hasErrors () {
      return !!this.getError
    },
    getTitleClasses () {
      let classes = []
      if (this.bootstrapMode) {
        classes.push('col-sm-5')
        classes.push('col-md-3')
        classes.push('col-xl-2')
      } else {
        classes.push('detail-row__title__custom__size')
      }

      if (this.titlePosition) {
        classes.push(`align-self-${this.titlePosition}`)
      }

      return classes
    }
  }
}
</script>

<style>
  .error .detail-row__payload__error {
    font-size: 0.7rem;
    color: red;
    padding-left: 0.1rem;
    padding-top: 0.5rem;
  }
</style>
<style scoped lang="scss">

.info-row {
  padding: 10px 0;
}

.detail-row {
  display: flex;
  padding: 0.714rem 0;
  box-sizing: border-box;

  .detail-row__title {
    align-self: center;
  }

  .detail-row__title__custom__size {
    flex: none;
    width: 13.37rem;
  }

  .detail-row__fixed-width {
    flex: none;
    width: 20.25rem;
  }

  .detail-row__big-fixed-width {
    flex: none;
    width: 26.3rem;
  }

  .detail-row__large-fixed-width {
    flex: none;
    width: 32.3rem;
  }

  .detail-row__extra-large-fixed-width {
    flex: none;
    width: 62.3rem;
  }

  &.edit-mode{
    padding: 0.3rem 0;
    min-height: 2.37rem;
  }

  .detail-row__payload {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
  }

  @media (max-width:576px) {
    &.mobile-wrap {
      .detail-row__title {
        flex-basis: 100%;
      }
    }
  }

  @media (max-width:576px) {
    .detail-row__title {
      flex-basis: 100%;
      flex-grow: 0;
    }
    .detail-row__payload {
      flex-grow: 1;
    }
    &.edit-mode {
      .detail-row__title {
        padding-bottom: 0.5rem;
      }
    }
  }
}
</style>
