<template>
  <editSettingsHelper @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="Admin Settings" :isLoading="isUpdatingProcessed" :isViewMode="isViewMode">
    <template slot="settings-content">
      <detail-row :large-payload-width="true">
        <span slot="title">Managed by Apps:</span>
        <b-checkbox v-if="!isViewMode" slot="payload" v-model="settingsToUpdate.IsManagedByApps"></b-checkbox>
        <span slot="payload" v-else>{{settings.IsManagedByApps ? 'Yes' : 'No'}}</span>
      </detail-row>
    </template>
  </editSettingsHelper>
</template>

<script>
import editSettingsHelper from '../../_shared/editSettingsHelper.vue'
import detailRow from '@/components/details/helpers/detailRow'
import editSettingsMixin from '@/mixins/eBay/editSettingsMixin'
import globals from '@/globals'

export default {
  props: {
    settings: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      isUpdatingProcessed: false,
      isViewMode: true,
      settingsToUpdate: globals().getClonedValue(this.settings)
    }
  },
  components: {
    editSettingsHelper,
    detailRow
  },
  mixins: [editSettingsMixin],
  methods: {
    updateSettings () {
      this.isUpdatingProcessed = true
      this.$store.dispatch('eBay/updateAccountAdminSettings', { accountId: this.$route.params.accountId, data: this.settingsToUpdate }).then(res => {
        this.$toaster.success('Admin Settings Updated Successfully')
      }).catch(ex => {
        this.$toaster.error('Something went wrong!')
        this.$logger.handleError(ex, 'Cannot update eBay account admin setting')
      }).finally(() => {
        this.isUpdatingProcessed = false
        this.isViewMode = true
        this.$emit('refresh')
      })
    }
  }
}
</script>
