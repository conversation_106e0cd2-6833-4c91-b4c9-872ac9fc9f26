<template>
  <div>
    <div class="py-2">
      <b-row>
        <b-col class="d-flex align-items-center">
          <h4 class="m-0">Brand Export Daily Reports Management</h4>
        </b-col>
        <b-col class="d-flex justify-content-end">
          <b-btn variant="primary" @click="editExportService"><font-awesome-icon  icon="pencil-alt" size="sm" /> Edit Export Service</b-btn>
        </b-col>
      </b-row>
    </div>
    <b-card>
      <brand-export-listing v-if="!isLoadingBrandExport" :items="brandExportItems"/>
      <brand-export-logs @applyFilter="applyFilter" v-if="!isLoadingLogs" :items="logItems" :totalItem="totalLogItems" :filter="filter" />
    </b-card>
    <b-modal
      v-if="serviceSettings"
      :visible="isShowModal"
      title="Edit Brand Export Service Settings"
      @hide="hide"
      no-close-on-backdrop
    >
      <detail-row fixedPlayloadWidth>
        <span slot="title">Has to send to FTP:</span>
        <b-form-checkbox slot="payload" v-model="serviceSettings.hasToSendToFtp"></b-form-checkbox>
      </detail-row>
      <detail-row fixedPlayloadWidth>
        <span slot="title">Has to notify about delivery exceptions:</span>
        <b-form-checkbox slot="payload" v-model="serviceSettings.hasToNotifyAboutFtpExceptions"></b-form-checkbox>
      </detail-row>
      <detail-row largePayloadWidth>
        <span slot="title">Emails to be notified about delivery exceptions:</span>
        <multi-input
          slot="payload"
          type="email"
          validateRules='email'
          :values="serviceSettings.emailsToNotifyAboutFtpExceptions"
          v-model="serviceSettings.emailsToNotifyAboutFtpExceptions"
        ></multi-input>
      </detail-row>
      <template #modal-footer>
        <b-btn @click="hide">Close</b-btn>
        <b-btn variant="primary" @click="updateServiceSettings">Save</b-btn>
      </template>
    </b-modal>
  </div>
</template>

<script>
import analyticsBuilders from './../../shared/analytics/builders'
import brandExportListing from '@/components/analytics_ga4/brandExport/brandExportListing'
import brandExportLogs from '@/components/analytics_ga4/brandExport/brandExportLogs'
import BrandExportService from '@/services/analytics/BrandExportService'
import detailRow from '@/components/details/helpers/detailRow'
import multiInput from '@/components/_shared/multiInput'

const filterManager = analyticsBuilders.getFilterManager({
  pageSize: { type: Number, default: 25 },
  page: { type: Number, default: 1 },
  exportname: { type: String, default: '' },
  reporttype: { type: Number, default: 0 },
  processingdatefrom: { type: String, default: '' },
  processingdateto: { type: String, default: '' },
  resultstatus: { type: Number, default: 0 },
  reportdatefrom: { type: String, default: '' },
  reportdateto: { type: String, default: '' }
})

export default {
  name: 'analytics-brand-exports',
  data () {
    return {
      filter: filterManager.defaultValue,
      brandExportItems: [],
      logItems: [],
      totalLogItems: 0,
      isLoadingBrandExport: true,
      isLoadingLogs: true,
      isShowModal: false,
      serviceSettings: null
    }
  },
  components: {
    'brand-export-listing': brandExportListing,
    'brand-export-logs': brandExportLogs,
    'detail-row': detailRow,
    'multi-input': multiInput
  },
  created () {
    this.filter = filterManager.urlHelper.parseQueryStringToObject(this.$router)
    this.loadBrandExportItems()
    this.loadBrandExportLogItems()
  },
  methods: {
    editExportService () {
      BrandExportService.getBrandExportsServiceSettings().then(res => {
        this.serviceSettings = res.data.model
        this.isShowModal = true
      }).catch(ex => {
        this.$toaster.error('Something went wrong')
        this.$logger.handleError(ex, 'Cannot get brand export service settings')
      })
    },
    hide () {
      this.isShowModal = false
    },
    applyFilter (newFilter) {
      this.filter = newFilter
      filterManager.urlHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.loadBrandExportLogItems()
    },
    loadBrandExportItems () {
      BrandExportService.getBrandExports().then(res => {
        this.brandExportItems = res.data.model.exportSettings
      }).catch(ex => {
        this.$toaster.error('Something went wrong')
        this.$logger.handleError(ex, 'Cannot get brand export items')
      }).finally(() => {
        this.isLoadingBrandExport = false
      })
    },
    loadBrandExportLogItems () {
      BrandExportService.getBrandExportsLogs(this.filter).then(res => {
        this.logItems = res.data.model.logs
        this.totalLogItems = res.data.model.totalCount
      }).catch(ex => {
        this.$toaster.error('Something went wrong')
        this.$logger.handleError(ex, 'Cannot get brand export logs items')
      }).finally(() => {
        this.isLoadingLogs = false
      })
    },
    updateServiceSettings () {
      BrandExportService.updateBrandExportServiceSettings(this.serviceSettings).then(res => {
        this.$toaster.success('Brand Export Service Settings Successfully Updated')
      }).catch(ex => {
        this.$toaster.error('Something went wrong')
        this.$logger.handleError(ex, 'Cannot update brand export service settings')
      }).finally(() => {
        this.isShowModal = false
      })
    }
  }
}
</script>
