import BaseService from '../BaseService'

class VehicleManagementService extends BaseService {
  deleteVehicle (accountId, vin, detailLevel = 0) {
    return this.axios.post(`/api/inventory/${accountId}/${vin}/delete?detailLevel=${detailLevel}`)
  };
  updateVehicle (accountId, vehicle, detailLevel = 0) {
    return this.axios.post(`/api/inventory/${accountId}?detailLevel=${detailLevel}`, vehicle)
  };
  createVehicle (accountId, vehicle, detailLevel = 0) {
    return this.axios.post(`/api/inventory/${accountId}/create?detailLevel=${detailLevel}`, vehicle)
  };
  updateVehiclePhotoOrder (accountId, vin, newPhotosOrder, detailLevel = 0) {
    return this.axios.post(`/api/inventory/${accountId}/${vin}/photos/modify?detailLevel=${detailLevel}`, newPhotosOrder)
  };
}

export default new VehicleManagementService()
