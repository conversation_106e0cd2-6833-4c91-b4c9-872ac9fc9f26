<template>
  <div>
    <div class="d-flex flex-row">
      <h4 class="mt-3">User Activity</h4>
      <!-- Pagination -->
      <paging
        class="d-none d-md-block p-0"
        :pageNumber="filter.page"
        :pageSize="filter.pageSize"
        :totalItems="itemsTotalCount"
        @numberChanged="pageChanged"
        @changePageSize="changePageSize"
      />
    </div>
    <!---Filters-->
    <b-card>
      <b-form  v-on:submit.prevent="applyFilter">
        <div class="form-row">
          <b-col class="my-1" sm="4" lg="4" xl="2">
            <b-form-input
              max='200'
              v-model="filter.search"
              placeholder="Search..."
              autocomplete="off"
            >
            </b-form-input>
          </b-col>

          <b-col class="my-1" sm="4" lg="4" xl="2">
            <b-input-group class="flex-nowrap">
              <b-input-group-prepend is-text>
                <i class="ion ion-md-calendar" slot="prepend"></i>
              </b-input-group-prepend>
              <date-time-picker
                ref="timeFrom"
                v-model="filter.dateFrom"
                :options="filterTimeOptions"
                format="MM/DD/YYYY HH:mm"
                placeholder="Date From"
                className="form-control"
                @change="onTimeFromInputChange"
              />
              <b-input-group-append
                is-text
                v-show="filter.dateFrom"
                @click="filter.dateFrom = null"
              >
                <i class="ion ion-md-close"></i>
              </b-input-group-append>
            </b-input-group>
          </b-col>

          <b-col class="my-1" sm="4" lg="4" xl="2">
            <b-input-group class="flex-nowrap">
              <b-input-group-prepend is-text>
                <i class="ion ion-md-calendar" slot="prepend"></i>
              </b-input-group-prepend>
              <date-time-picker
                ref="timeTo"
                v-model="filter.dateTo"
                :options="filterTimeOptions"
                format="MM/DD/YYYY HH:mm"
                placeholder="Date To"
                className="form-control"
                @change="onTimeToInputChange"
              />
              <b-input-group-append
                is-text
                v-show="filter.dateTo"
                @click="filter.dateTo = null"
              >
                <i class="ion ion-md-close"></i>
              </b-input-group-append>
            </b-input-group>
          </b-col>
          <b-col class="my-1" sm="4" lg="4" xl="2">
            <b-form-select v-model="filter.action" :options="getActionOptions"></b-form-select>
          </b-col>
          <b-col class="my-1" sm="4" lg="4" xl="2">
            <multiSelectWithCheckboxes
              v-model='selectedUserTypes'
              :options='getUserTypeOptions'
              name="User Types"
              customMessageOfNoneSelectedItems="All User Types"
            ></multiSelectWithCheckboxes>
          </b-col>
          <b-col class="my-1" sm="4" lg="4" xl="2">
            <b-btn block  variant="primary" type="submit">Submit</b-btn>
          </b-col>
        </div>
      </b-form>
    </b-card>
    <!-- Listing -->
    <b-card>
      <!-- Table -->
      <div class="table-responsive" v-if="isLoaded">
        <b-table
          :items="items"
          :fields="tableFields"
          :sort-by="tableSortBy"
          :sort-desc="tableSortDesc"
          @sort-changed="onSortChanged"
          :striped="true"
          :bordered="false"
          :no-sort-reset="true"
          :no-local-sorting="true"
          responsive
          show-empty
          class="products-table card-table"
        >
          <template #cell(manage)="data">
            <b-btn size="sm" @click="onShowDetails(data)">{{ data.detailsShowing ? 'Hide' : 'Show' }} Details</b-btn>
            <a class="btn btn-outline-secondary btn-sm" :href='getLogDetailsPath(data.item.logItemData.id)' target="_blank">
            <i class="ion ion-md-open"></i>
          </a>
          </template>
          <template #row-details="data">
            <b-card>
              <log-node
                v-if='data.item.nodes'
                :data="data.item.nodes"
                :isExpandedShallow="true"
                :isExpandedDeep="false"
              />
            </b-card>
          </template>
        </b-table>
        <!-- Pagination -->
        <paging
          :pageNumber="filter.page"
          :pageSize="filter.pageSize"
          :totalItems="itemsTotalCount"
          titled
          pageSizeSelector
          @numberChanged="pageChanged"
          @changePageSize="changePageSize"
        />
      </div>
      <div class="col my-5" v-else>
        <loader size='lg' />
      </div>
    </b-card>
  </div>
</template>

<script>
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import loader from '@/components/_shared/loader'
import moment from 'moment'
import { userActivitySortTypes, userActivityActionTypes } from '@/shared/leads/common'
import {userTypes} from '@/shared/users/constants'
import multiSelectWithCheckboxes from '../../components/_shared/multiSelectWithCheckboxes.vue'

const defaultFilters = new ObjectSchema({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  search: { type: String, default: '' },
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  sort: { type: Number, default: 6 },
  action: { type: Number, default: 0 },
  userTypes: { type: String, default: `${userTypes.cpUser.value},${userTypes.adminUser.value}` }
})
const queryHelper = new QueryStringHelper(defaultFilters)

export default {
  name: 'leads-user-activity',
  metaInfo: {
    title: 'User activity'
  },
  components: {
    'paging': () => import('@/components/_shared/paging'),
    'date-time-picker': () => import('@gravitano/vue-date-range-picker/src/components/DateRangePicker'),
    'loader': loader,
    'log-node': () => import('@/components/_shared/logItemNode.vue'),
    multiSelectWithCheckboxes
  },
  data () {
    return {
      items: [],
      itemsTotalCount: 10,
      isLoaded: false,
      filter: defaultFilters.getObject(),
      filterTimeOptions: {
        startDate: new Date(),
        autoUpdateInput: false,
        singleDatePicker: true,
        timePicker: true,
        timePicker24Hour: true,
        maxDate: new Date()
      }
    }
  },
  created () {
    this.filter = queryHelper.parseQueryStringToObject(this.$router)
    this.loadContent()
  },
  computed: {
    selectedUserTypes: {
      get () {
        return this.getIntegerArrayFromString(this.filter.userTypes)
      },
      set (value) {
        this.filter.userTypes = value.join()
      }
    },
    tableFields () {
      return [
        {
          key: 'logItemData.requestInformation.userName',
          label: 'User Name',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: userActivitySortTypes.userNameAsc,
          sortTypeDesc: userActivitySortTypes.userNameDesc
        },
        {
          key: 'logItemData.requestInformation.user.userType',
          label: 'User Type',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: userActivitySortTypes.userTypeAsc,
          sortTypeDesc: userActivitySortTypes.userTypeDesc,
          formatter: val => (Object.values(userTypes).find(x => x.value === val) || {text: '-'}).text
        },
        {
          key: 'logItemData.accountId',
          label: 'Account ID',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: userActivitySortTypes.accountIdAsc,
          sortTypeDesc: userActivitySortTypes.accountIdDesc,
          formatter: val => val || '-'
        },
        {
          key: 'logItemData.accountName',
          label: 'Account Name',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: userActivitySortTypes.accountNameAsc,
          sortTypeDesc: userActivitySortTypes.accountNameDesc,
          formatter: val => val || '-'
        },
        {
          key: 'logItemData.action',
          label: 'Action Name',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: userActivitySortTypes.actionAsc,
          sortTypeDesc: userActivitySortTypes.actionDesc,
          formatter: val => (Object.values(userActivityActionTypes).find(x => x.value === val) || {text: '-'}).text
        },
        {
          key: 'logItemData.startProcessingDateTime',
          label: 'Date',
          tdClass: 'py-2 align-middle',
          formatter: value => {
            return moment(value).format('MM/DD/YYYY hh:mm:ss A')
          },
          sortable: true,
          sortTypeAsc: userActivitySortTypes.dateAsc,
          sortTypeDesc: userActivitySortTypes.dateDesc
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    refDateTimeFrom () {
      return (this.$refs.timeFrom || {}).$el || {}
    },
    refDateTimeTo () {
      return (this.$refs.timeTo || {}).$el || {}
    },
    sortType () {
      return this.filter.sort
    },
    tableSortBy () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sortType
      } else {
        return false
      }
    },
    getActionOptions () {
      return Object.values(userActivityActionTypes).sort((left, right) => left.text.localeCompare(right.text))
    },
    getUserTypeOptions () {
      return Object.values(userTypes)
    }
  },
  methods: {
    loadContent () {
      this.$store.dispatch('leads/getUserActivityLogs', this.filter).then(res => {
        this.items = res.data.logItems
        this.itemsTotalCount = res.data.totalItemsCount
      }).catch(ex => {
        this.$toaster.error('Cannot get user activity logs')
        this.$logger.handleError(ex, 'Cannot get user activity logs', this.filter)
      }).finally(() => {
        this.isLoaded = true
      })
    },
    pageChanged (newPage) {
      this.filter.page = newPage
      this.synchronizeUrlAndReload()
    },
    changePageSize (newSize) {
      this.filter.pageSize = newSize
      this.filter.page = 1
      this.synchronizeUrlAndReload()
    },
    onSortChanged (value) {
      const sortingColumn = this.tableFields.find(x => x.key === value.sortBy)
      if (value.sortDesc) {
        this.filter.sort = sortingColumn.sortTypeDesc
      } else {
        this.filter.sort = sortingColumn.sortTypeAsc
      }
      this.loadContent()
    },
    onTimeFromInputChange (newVal) {
      this.refDateTimeFrom.value = newVal || this.filter.dateFrom || null
    },
    onTimeToInputChange (newVal) {
      this.refDateTimeTo.value = newVal || this.filter.dateTo || null
    },
    applyFilter () {
      this.filter.page = 1
      this.synchronizeUrlAndReload()
    },
    synchronizeUrlAndReload () {
      this.isLoaded = false
      queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.loadContent()
    },
    onShowDetails (data) {
      if (data.item.nodes) {
        data.toggleDetails()
      } else {
        this.$store.dispatch('leads/getUserActivityLogDetails', data.item.logItemData.id).then(res => {
          this.$set(data.item, 'nodes', res.data)

          data.toggleDetails()
        }).catch(ex => {
          this.$toaster.error('Cannot get user activity log details')
          this.$logger.handleError(ex, 'Cannot get user activity log details', data.item)
        })
      }
    },
    getIntegerArrayFromString (str) {
      if (!str || !str.trim()) {
        return []
      }
      let array = []
      for (let numberStr of str.split(',')) {
        let number = parseInt(numberStr)
        if (!isNaN(number)) {
          array.push(number)
        }
      }
      return array
    },
    getLogDetailsPath (id) {
      return `/leads/useractivity/${id}/details`
    }
  }
}
</script>
