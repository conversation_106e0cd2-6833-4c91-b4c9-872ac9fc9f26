import BaseService from '@/services/BaseService'
import { getSkipNumber } from '@/shared/common/paginationHelpers'

export class LogsService extends BaseService {
  constructor (apiPathBase) {
    super()
    this.apiPathBase = apiPathBase
  }
  async getAccountLogs (params) {
    return this.axios.get(`${this.apiPathBase}tasks`, {
      params: {
        skip: getSkipNumber(params.page, params.pageSize),
        limit: params.pageSize,
        search: params.search,
        sort: params.sort
      }})
  };
  async deleteAccountTask (id) {
    return this.axios.post(`${this.apiPathBase}tasks/${id}/delete`)
  };
  async getGroupLogs (params) {
    return this.axios.get(`${this.apiPathBase}tasks/group_reports`, {
      params: {
        skip: getSkipNumber(params.page, params.pageSize),
        limit: params.pageSize,
        search: params.search,
        sort: params.sort
      }})
  };
  async deleteGroupTask (id) {
    return this.axios.post(`${this.apiPathBase}tasks/group_reports/${id}/delete`)
  };
}
