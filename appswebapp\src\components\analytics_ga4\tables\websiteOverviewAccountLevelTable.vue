<template>
  <common-analytics-table
    :tableItems="tableItems"
    :totalItems="totalItems"
    :tableFields="tableFields"
    :isPaginated="true"
    :sortType.sync="sortTypeProp"
    :pageNumber.sync="pageNumberProp"
    :pageSize.sync="pageSizeProp"
  >
    <template slot="row-details" slot-scope="{ item }">
      <b-card>
        <b-row class="mb-2">
          <b-col sm="12" md="4" lg="3" xl="2"><b>Form Leads:</b></b-col>
          <b-col col>{{ $locale.formatNumber(item.item.formLeads) }}</b-col>
        </b-row>
        <b-row class="mb-2" v-if="typeof item.item.smsLeads !== 'undefined'">
          <b-col sm="12" md="4" lg="3" xl="2"><b>SMS Leads:</b></b-col>
          <b-col>{{ $locale.formatNumber(item.item.smsLeads) }}</b-col>
        </b-row>
        <b-row class="mb-2">
          <b-col sm="12" md="4" lg="3" xl="2"><b>Phone Leads:</b></b-col>
          <b-col>{{ $locale.formatNumber(item.item.phoneLeads) }}</b-col>
        </b-row>
        <b-row class="mb-2">
          <b-col sm="12" md="4" lg="3" xl="2"><b>Conv. Rate per User:</b></b-col>
          <b-col>{{ item.item.convRatePerUser }}%</b-col>
        </b-row>
        <b-button size="sm" @click="item.toggleDetails">Hide Details</b-button>
      </b-card>
    </template>
  </common-analytics-table>
</template>

<script>
import analyticsConstants from '../../../shared/analytics/constants'

export default {
  name: 'website-overview-account-level-table',
  props: {
    tableItems: { type: Array, required: true },
    totalItems: { type: Number, require: true },
    pageNumber: { type: Number, required: true },
    pageSize: { type: Number, required: true },
    sortType: { type: Number, required: true }
  },
  components: {
    'common-analytics-table': () => import('./commonAnalyticsTable.vue')
  },
  computed: {
    tableFields () {
      return [
        {
          key: 'dateFrom',
          label: 'Date',
          sortable: true,
          sortTypeAsc: analyticsConstants.websiteOverviewSortTypes.dateAsc,
          sortTypeDesc: analyticsConstants.websiteOverviewSortTypes.dateDesc
        },
        {
          key: 'sessions',
          label: 'Sessions',
          sortable: true,
          sortTypeAsc: analyticsConstants.websiteOverviewSortTypes.sessionsAsc,
          sortTypeDesc: analyticsConstants.websiteOverviewSortTypes.sessionsDesc,
          formatter: val => this.$locale.formatNumber(val)
        },
        {
          key: 'users',
          label: 'Users',
          sortable: true,
          sortTypeAsc: analyticsConstants.websiteOverviewSortTypes.usersAsc,
          sortTypeDesc: analyticsConstants.websiteOverviewSortTypes.usersDesc,
          formatter: val => this.$locale.formatNumber(val)
        },
        {
          key: 'pageViews',
          label: 'Page Views',
          sortable: true,
          sortTypeAsc: analyticsConstants.websiteOverviewSortTypes.pageViewsAsc,
          sortTypeDesc: analyticsConstants.websiteOverviewSortTypes.pageViewsDesc,
          formatter: val => this.$locale.formatNumber(val)
        },
        {
          key: 'totalLeads',
          label: 'Total Leads',
          sortable: true,
          sortTypeAsc: analyticsConstants.websiteOverviewSortTypes.totalLeadsAsc,
          sortTypeDesc: analyticsConstants.websiteOverviewSortTypes.totalLeadsDesc,
          formatter: val => this.$locale.formatNumber(val)
        },
        {
          key: 'convRatePerSession',
          label: 'Conv. Rate per Session',
          sortable: true,
          sortTypeAsc: analyticsConstants.websiteOverviewSortTypes.convRatePerSessionAsc,
          sortTypeDesc: analyticsConstants.websiteOverviewSortTypes.convRatePerSessionDesc,
          formatter: val => `${val}%`
        },
        {
          key: 'show_details'
        }
      ]
    },
    sortTypeProp: {
      get () {
        return this.sortType
      },
      set (newVal) {
        this.$emit('sortTypeChanged', newVal)
      }
    },
    pageNumberProp: {
      get () {
        return this.pageNumber
      },
      set (newVal) {
        this.$emit('pageNumberChanged', newVal)
      }
    },
    pageSizeProp: {
      get () {
        return this.pageSize
      },
      set (newVal) {
        this.$emit('pageSizeChanged', newVal)
      }
    }
  }
}
</script>

<style scoped>

</style>
