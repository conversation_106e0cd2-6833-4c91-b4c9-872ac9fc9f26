<template>
  <div v-if='type === spamTabTypes.spamMessages.value'>
    <b-form @submit.prevent="applyFilter">
      <div class='leads-spam-message-form'>
        <b-form-input class="leads-spam-message-form-item" :value="accountIdComputed" @input="accountIdComputed = $event" placeholder="Search by Account ID"></b-form-input>
        <b-form-input class="leads-spam-message-form-item" v-model='filter.searchText' placeholder="Email or Source IP or Subnet"></b-form-input>
        <b-form-select class="leads-spam-message-form-item" text-field='label' v-model='filter.communicationtype' :options='getCommunicationTypes'></b-form-select>
        <b-form-select class="leads-spam-message-form-item" text-field='label' v-model='filter.spamreasontype' :options='getSpamReasonTypes'></b-form-select>
        <b-input-group class="flex-nowrap leads-spam-message-form-item">
          <b-input-group-prepend is-text>
            <i class="ion ion-md-calendar" slot="prepend"></i>
          </b-input-group-prepend>
          <date-time-picker
            ref="timeFrom"
            v-model="filter.dateFrom"
            :options="filterTimeOptions"
            format="MM/DD/YYYY HH:mm"
            placeholder="Date From"
            className="form-control"
            @change="onTimeFromInputChange"
          />
          <b-input-group-append
            is-text
            v-show="filter.dateFrom"
            @click="filter.dateFrom = null"
          >
            <i class="ion ion-md-close"></i>
          </b-input-group-append>
        </b-input-group>
        <b-input-group class="flex-nowrap leads-spam-message-form-item">
          <b-input-group-prepend is-text>
            <i class="ion ion-md-calendar" slot="prepend"></i>
          </b-input-group-prepend>
          <date-time-picker
            ref="timeTo"
            v-model="filter.dateTo"
            :options="filterTimeOptions"
            format="MM/DD/YYYY HH:mm"
            placeholder="Date To"
            className="form-control"
            @change="onTimeToInputChange"
          />
          <b-input-group-append
            is-text
            v-show="filter.dateTo"
            @click="filter.dateTo = null"
          >
            <i class="ion ion-md-close"></i>
          </b-input-group-append>
        </b-input-group>
        <b-btn class="leads-spam-message-form-item" variant="primary" type="submit">Submit</b-btn>
      </div>
    </b-form>
  </div>
  <div v-else>
    <b-form @submit.prevent="applyFilter">
      <div class='leads-spam-filter-form'>
        <b-form-input class="leads-spam-filter-form-item" v-model='filter.search' placeholder="Search by Value"></b-form-input>
        <b-form-select class="leads-spam-message-form-item" text-field='label' v-model='filter.applicationtype' :options='getApplicationOptions'></b-form-select>
        <b-form-select class="leads-spam-message-form-item" text-field='label' v-model='filter.fieldtype' :options='getFieldOptions'></b-form-select>
        <b-btn class="leads-spam-filter-form-item-btn" variant="primary" type="submit">Submit</b-btn>
        <b-btn variant="primary" class="leads-spam-filter-form-item-btn" @click="onCreate()">
          Create New Filter
        </b-btn>
      </div>
    </b-form>
    <leads-spam-create-edit-modal v-if='item' :item='item' :isEditMode='isEditMode' :isShow='isShow' @hide='onHide'/>
  </div>
</template>

<script>
import leadsSpamCreateEditModal from './leadsSpamCreateEditModal'
import { spamTabTypes, communicationType } from '@/shared/leads/common'
import { applicationType, fieldType, spamReasonType } from '@/shared/leads/spam'

export default {
  name: 'leads-spam-filter-form',
  props: {
    filter: { type: Object, required: true },
    type: { type: Number, required: true },
    id: { type: String, return: true }
  },
  data () {
    return {
      isShow: false,
      spamTabTypes,
      item: null,
      isEditMode: false,
      currentApplication: null,
      filterTimeOptions: {
        startDate: new Date(),
        autoUpdateInput: false,
        singleDatePicker: true,
        timePicker: true,
        timePicker24Hour: true,
        maxDate: new Date()
      }
    }
  },
  computed: {
    refDateTimeFrom () {
      return (this.$refs.timeFrom || {}).$el || {}
    },
    refDateTimeTo () {
      return (this.$refs.timeTo || {}).$el || {}
    },
    getFieldOptions () {
      if (this.filter.applicationtype !== 0) {
        let application = Object.values(applicationType).find(x => x.value === this.filter.applicationtype)
        return (application || {fields: []}).fields
      }
      return Object.values(fieldType)
    },
    getApplicationOptions () {
      return Object.values(applicationType)
    },
    getCommunicationTypes () {
      return Object.values(communicationType)
    },
    getSpamReasonTypes () {
      return Object.values(spamReasonType)
    },
    accountIdComputed: {
      get () {
        return this.filter.accountId === 0 ? null : this.filter.accountId
      },
      set (value) {
        this.filter.accountId = value === null || value === '' ? 0 : value
      }
    }
  },
  components: {
    'date-time-picker': () => import('@gravitano/vue-date-range-picker/src/components/DateRangePicker'),
    'leads-spam-create-edit-modal': leadsSpamCreateEditModal
  },
  methods: {
    onTimeFromInputChange (newVal) {
      this.refDateTimeFrom.value = newVal || this.filter.dateFrom || null
    },
    onTimeToInputChange (newVal) {
      this.refDateTimeTo.value = newVal || this.filter.dateTo || null
    },
    onCreate () {
      this.isEditMode = false
      this.$store.dispatch('leads/getSpamFilterPrototype').then(res => {
        this.item = res.data
        this.isShow = true
      }).catch(ex => {
        this.$toaster.error(`Cannot get spam filter details prototype model. Message: ${ex.message}`)
      })
    },
    openModal () {
      this.isEditMode = true
      this.$store.dispatch('leads/getSpamFilterDetails', this.id).then(res => {
        this.item = res.data
        this.isShow = true
      }).catch(ex => {
        this.$toaster.error(`Cannot get spam filter details model with id: ${this.id}. Message: ${ex.message}`)
      })
    },
    onHide () {
      this.isShow = false
    },
    applyFilter () {
      this.$emit('applyFilter')
    }
  },
  watch: {
    'id': {
      deep: true,
      handler: function () {
        if (this.id !== '') {
          this.openModal()
        }
      }
    }
  }
}
</script>

<style scoped>
.leads-spam-message-form {
  display: flex;
  flex-shrink: 1;
  flex-direction: row;
  margin: 20px 10px 20px 10px;
}
.leads-spam-message-form-item {
  flex: 1;
  margin: 0 5px 0 5px;
}

.leads-spam-filter-form {
  display: flex;
  flex-shrink: 1;
  flex-direction: row;
  margin: 20px 10px 20px 10px;
}

.leads-spam-filter-form-item {
  flex: 1;
  margin: 0 5px 0 5px;
}

.leads-spam-filter-form-item-btn {
  width: 150px;
  margin: 0 5px 0 5px;
}

@media (max-width: 576px) {
  .leads-spam-filter-form {
    display: flex;
    flex-shrink: 1;
    flex-direction: column;
  }
  .leads-spam-filter-form-item, .leads-spam-filter-form-item-btn{
    width: 100%;
    margin: 5px 0 5px 0;
  }
}

@media(max-width: 1300px) {
  .leads-spam-message-form {
    display: flex;
    flex-shrink: 1;
    flex-direction: column;
  }
  .leads-spam-message-form-item {
    width: auto;
    margin: 5px 5px 5px 5px;
  }
  .leads-spam-filter-form {
    display: flex;
    flex-shrink: 1;
    flex-direction: column;
  }
  .leads-spam-filter-form-item, .leads-spam-filter-form-item-btn{
    width: auto;
    margin: 5px 5px 5px 5px;
  }
}
</style>
