<template>
  <div v-if="mode === 'view'">

    <auto-detail-row title="Sub Model / Trim" :text="vehicle.trim"/>

    <auto-detail-row :title="getVehicleOverviewFeatures.atvType.name" :text="getVehicleOverviewFeatures.atvType.value"/>

    <auto-detail-row title="Engine" :text="[vehicle.engine]"/>

    <auto-detail-row title="Engine (сс)" :text="vehicle.eBayEngineSize" />

    <auto-detail-row title="Transmission" :text="getTransmissionDescription"/>

    <auto-detail-row title="Drivetrain" :text="getDrivetrainDescription"/>

  </div>
  <div v-else-if="mode === 'edit'">
    <ValidationProvider immediate name="year" :rules="getYearValidateRules" v-slot="{errors}">
    <detail-row editMode mobileWrap :error="errors[0]">
      <span slot="title">Year:</span>
      <custom-select slot="payload"
                     v-model="vehicle.year"
                     :customSelectValue="{selectVal: vehicle.year,inputVal: vehicle.year}"
                     name="year"
                     :options="getYearsOptions"
                     @change="onInputYear"/>
    </detail-row>
    </ValidationProvider>

    <ValidationProvider immediate name="make" rules="required|max:50|xml" v-slot="{errors}">
    <detail-row editMode mobileWrap :error="errors[0]">
      <span slot="title">Make:</span>

      <custom-select slot="payload"
                     v-model="vehicle.make"
                     :customSelectValue="{selectVal: vehicle.make,inputVal: vehicle.make}"
                     :options="getMakesOptions"
                     @change="onInputMakes"
                     name="make"/>
    </detail-row>
    </ValidationProvider>

    <auto-detail-row title="Model" v-model="vehicle.model" validation-rule="max:50|xml"/>

    <auto-detail-row title="Sub Model / Trim" v-model="vehicle.trim" validation-rule="max:100|xml"/>

    <auto-detail-row :title="getVehicleOverviewFeatures.atvType.name" v-model="getVehicleOverviewFeatures.atvType.value" :options="getNameValueOptions(getVehicleOverviewFeatures.atvType.nameValueOptions)"/>

    <auto-detail-row title="Engine" v-model="vehicle.engine" validation-rule="max:50|xml"/>

    <auto-detail-row title="Engine (сс)" v-model="vehicle.eBayEngineSize" validation-rule="max:50|xml"/>

    <auto-detail-row title="Transmission" v-model="vehicle.transTypeId" :options="metadata.transmissionOptions"/>

    <auto-detail-row title="Drivetrain" v-model="vehicle.drivetrainId" :options="metadata.drivetrainOptions"/>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import detailRow from '../../helpers/detailRow'
import splitHelper from '../../helpers/spliterHelper'
import selectWithCustomValue from '../../helpers/selectWithCustomValue'
import autoDetailRow from '../../helpers/autoDetailRow'
import featuresHelper from '../../../../shared/details/featuresHelper'

export default {
  name: 'vehicle-overview-ATV',
  props: {
    mode: String
  },
  computed: {
    ...mapGetters('details', ['vehicle', 'metadata', 'atvDetails']),
    ...mapGetters('categoryData', ['makes', 'models', 'years']),

    getYearsOptions () {
      return this.years.map(x => ({
        value: x,
        text: x
      }))
    },
    getMakesOptions () {
      return this.makes.map(x => ({
        value: x,
        text: x
      }))
    },
    getTransmissionDescription () {
      return this.metadata.transmissionOptions[this.vehicle.transTypeId] || ''
    },
    getDrivetrainDescription () {
      return this.metadata.drivetrainOptions[this.vehicle.drivetrainId] || ''
    },
    getYearValidateRules () {
      let currentYear = new Date().getFullYear()
      return `required|between:${currentYear - 100},${currentYear + 1}`
    },
    getVehicleOverviewFeatures () {
      return {
        atvType: this.atvDetails.features.find(x => x.id === -1000)
      }
    }
  },
  methods: {
    onInputYear (newVal) {
      this.vehicle.year = newVal.isInputMode ? newVal.text : newVal.value
    },
    onInputMakes (newVal) {
      this.vehicle.make = newVal.isInputMode ? newVal.text : newVal.value
    },
    getFeatureById (id) {
      return featuresHelper.getFeatureById(this.atvDetails, id)
    },
    getNameValueOptions (nameValueOptions) {
      return featuresHelper.getNameValueOptions(nameValueOptions)
    },
    getSelectedAttributeOption (attribute) {
      return featuresHelper.getSelectedAttributeOption(attribute)
    }
  },
  components: {
    'custom-select': selectWithCustomValue,
    'detail-row': detailRow,
    'split-helper': splitHelper,
    'auto-detail-row': autoDetailRow
  }
}
</script>
