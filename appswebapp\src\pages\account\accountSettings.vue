<template>
  <div>
    <h4>Account Settings</h4>
    <div v-if="!isLoading && !isError">
      <b-card>
        <account-basic-settings-section :settings="settings" />
        <mfa-settings-section :settings="settings" :account-id="accountId" />
      </b-card>
    </div>
    <div v-else-if="isError" class="text-center mb-3">
      <b-alert variant="danger" show>
        <h5>Error Loading Settings</h5>
        <p>Unable to load account settings. Please try again later.</p>
        <b-button variant="outline-danger" @click="loadSettings">
          <i class="ion ion-md-refresh mr-1"></i>
          Retry
        </b-button>
      </b-alert>
    </div>
    <div v-else class="my-5 text-center">
      <b-spinner variant="primary"></b-spinner>
      <p class="mt-2">Loading account settings...</p>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import AccountBasicSettingsSection from '@/components/account/settings/accountBasicSettingsSection.vue'
import MfaSettingsSection from '@/components/account/settings/mfaSettingsSection.vue'

export default {
  name: 'AccountSettings',
  metaInfo: {
    title: 'Account Settings'
  },
  components: {
    AccountBasicSettingsSection,
    MfaSettingsSection
  },
  props: {
    accountId: {
      type: Number,
      required: true
    }
  },
  data () {
    return {
      isLoading: true,
      isError: false,
      settings: {}
    }
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {}
    }
  },
  async mounted () {
    await this.loadSettings()
  },
  methods: {
    async loadSettings () {
      this.isLoading = true
      this.isError = false

      try {
        const data = await this.$store.dispatch('accountSettings/getBasicSettings', this.accountId)
        this.settings = { ...data }
      } catch (error) {
        this.isError = true
        this.$logger.handleError(error, `Failed to load settings for account ${this.accountId}`)
        this.$toaster.error('Failed to load account settings. Please try again.')
      } finally {
        this.isLoading = false
      }
    }
  }
}
</script>
