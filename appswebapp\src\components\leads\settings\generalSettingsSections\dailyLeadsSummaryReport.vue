<template>
  <div class='mb-4'>
    <editSettingsHelper @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="Daily Leads Summary Report" :isDisabled="isDisabled" :isLoading="isUpdatingProcessed" :isViewMode="isViewMode">
      <div slot="settings-content">
        <detail-row fixedPayloadWidth>
          <span slot="title">Send Daily Leads Report:</span>
          <b-form-checkbox v-model='dealerAccountSettingsToUpdate.hasToSendDailyReports' slot="payload" :disabled='isViewMode'>
            Send
          </b-form-checkbox>
        </detail-row>
        <div v-if='dealerAccountSettingsToUpdate.hasToSendDailyReports'>
          <detail-row fixedPayloadWidth>
            <span slot="title">Report Recipient(s):</span>
            <b-form-input v-model='reportRecipientEmails' placeholder="ex: <EMAIL>,<EMAIL>" type="text" slot="payload" :disabled='isViewMode'/>
          </detail-row>
          <detail-row fixedPayloadWidth>
            <span slot="title">Report Delivery Time:</span>
            <b-form-select v-model='reportDeliveryTime' type="text" slot="payload" class="leads-custom-select-time-size" :options='hoursOptions' :disabled='isViewMode'/>
          </detail-row>
        </div>
      </div>
    </editSettingsHelper>
  </div>
</template>

<script>
import globals from '../../../../globals'
import detailRow from '@/components/details/helpers/detailRow'
import dateModule from '@/plugins/locale/date'
import commonConstants from '@/shared/common/constants'
import { mapGetters } from 'vuex'

export default {
  name: 'daily-leads-summary-report-settings',
  props: {
    isUpdatingProcessed: {
      type: Boolean,
      required: true
    },
    isDisabled: Boolean
  },
  data () {
    return {
      isViewMode: true,
      hoursOptions: commonConstants.hoursOptions,
      dealerAccountSettingsToUpdate: {}
    }
  },
  created () {
    this.initData()
  },
  components: {
    'detail-row': detailRow,
    editSettingsHelper: () => import('@/components/_shared/editSettingsHelper')
  },
  computed: {
    ...mapGetters('leadsAccountSettings', ['dealerAccountSettings']),
    reportRecipientEmails: {
      get () {
        if (this.dealerAccountSettingsToUpdate.reportRecipientEmails) {
          return this.dealerAccountSettingsToUpdate.reportRecipientEmails.toString()
        }
        return ''
      },
      set (value) {
        this.dealerAccountSettingsToUpdate.reportRecipientEmails = value.split(',')
      }
    },
    reportDeliveryTime: {
      get () {
        if (this.dealerAccountSettingsToUpdate.reportDeliveryTime) {
          return dateModule.formatAHours(this.dealerAccountSettingsToUpdate.reportDeliveryTime)
        }
        return ''
      },
      set (value) {
        this.dealerAccountSettingsToUpdate.reportDeliveryTime = dateModule.timeWithAbbreviationToTime(value + '00')
      }
    }
  },
  methods: {
    initData () {
      if (this.dealerAccountSettings) {
        this.dealerAccountSettingsToUpdate = globals().getClonedValue(this.dealerAccountSettings)
      }
    },
    saveSettings () {
      if (this.validate()) {
        this.$emit('save', this.dealerAccountSettingsToUpdate)
        this.isViewMode = true
      }
    },
    changeMode (mode) {
      this.isViewMode = mode
    },
    cancel () {
      this.initData()
      this.changeMode(true)
    },
    validate () {
      if (this.dealerAccountSettingsToUpdate.hasToSendDailyReports) {
        if (!this.dealerAccountSettingsToUpdate.reportRecipientEmails || this.dealerAccountSettingsToUpdate.reportRecipientEmails.length < 1 || this.dealerAccountSettingsToUpdate.reportRecipientEmails[0].trim().length < 1) {
          this.$toaster.error('Report Recipient(s) field is required')
          return false
        }
      }

      return true
    }
  }
}
</script>

<style>
.leads-custom-select-time-size {
  width: 100px;
}
</style>
