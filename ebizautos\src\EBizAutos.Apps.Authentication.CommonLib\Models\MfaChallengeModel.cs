using System;
using static EBizAutos.Apps.CommonLib.Enums.AuthenticationEnums;

namespace EBizAutos.Apps.Authentication.CommonLib.Models {
	public class MfaChallengeModel {
		public string Id { get; set; }
		public string UserObjectId { get; set; }
		public int AccountId { get; set; }
		public string CodeHash { get; set; }
		public string CodeSalt { get; set; }
		public DateTime CreatedAtUtc { get; set; }
		public DateTime? LastCodeExpiresAtUtc { get; set; }
		public int AttemptsUsed { get; set; }
		public int OtpCodeVerificationAttemptsLimit { get; set; } = 5;
		public DateTime? OtpCodeResendAvailableAtUtc { get; set; }
		public string ApplicationName { get; set; }
		public string DeviceId { get; set; }
		public string DeviceModel { get; set; }
		public string IpAddress { get; set; }
		public MfaMethodEnum Method { get; set; }
		public string MaskedDestination { get; set; }
		public bool IsVerified { get; set; }
		public DateTime? VerifiedAtUtc { get; set; }
	}
}