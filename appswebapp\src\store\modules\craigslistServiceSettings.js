import axios from 'axios'

export default {
  namespaced: true,
  state: {
    deactivationSettings: {},
    blackListSettings: {}
  },
  getters: {
    deactivationSettings: state => state.deactivationSettings,
    blackListSettings: state => state.blackListSettings
  },
  mutations: {
    setDeactivationSettings (state, data) {
      state.deactivationSettings = data
    },
    setBlackListSettings (state, data) {
      state.blackListSettings = data
    },
    setBaseSettings (state, data) {
      state.deactivationSettings = data.craigslistServiceDeactivationSettings
      state.blackListSettings = data.craigslistBlackListSettings
    }
  },
  actions: {
    async populateCraigslistServiceSettings ({ commit }) {
      const result = await axios.get('/api/craigslist/dashboard/craigslistservice/settings')

      commit('setBaseSettings', result.data.model)

      return result.data
    },
    async updateCraigslistServiceSettings ({ commit }, parameters) {
      const result = await axios.put('/api/craigslist/dashboard/craigslistservice/settings', parameters.data)

      return result.data
    }
  }
}
