{"Kestrel": {"EndPoints": {"Http": {"Url": "http://0.0.0.0:9003"}}}, "AppSettings": {"ApplicationName": "Apps Account Management API (dev)", "IsDev": "true", "DataAccessEncryptKey": "fpABRddPOOg0hbm1PUHIjw==", "DataAccessEncryptIV": "AAAAAAAAAAAAAAAAAAAAAA==", "IsLoggingOn": true, "ModifyAccountAttemptsCount": 3, "ModifyAccountRetryTimeOnFailInSeconds": 600}, "DbSettings": {"UsersMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/cp?retryWrites=true&w=majority", "AppsAccountSettingsMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/accounts?retryWrites=true&w=majority", "AppsContactsMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/accounts?retryWrites=true&w=majority", "AppsGroupsMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/accounts?retryWrites=true&w=majority", "AppsPhotoServerSettingsMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/accounts?retryWrites=true&w=majority", "AppsAccountManagementLogsMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/appsaccountmanagementlogs?retryWrites=true&w=majority", "AppsServiceBusLogsMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/appsservicebuslogs?retryWrites=true&w=majority", "AppsSiteboxMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/siteboxes?retryWrites=true&w=majority", "AppsSiteSettingsMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/sites?retryWrites=true&w=majority"}, "ServiceBusSettings": {"Host": "b-14921ad0-337a-4458-84ee-c042b1327094-1.mq.us-east-1.amazonaws.com", "Port": 61617, "Username": "ebizeventbus", "Password": "z$32s0d-nd8G62!js83aPuhpUh", "FailoverHosts": ["b-14921ad0-337a-4458-84ee-c042b1327094-1.mq.us-east-1.amazonaws.com"], "HasToUseSsl": true, "PublishSettings": {"RetryAttempts": 3, "MinRetryDelayInMs": 5000, "MaxRetryDelayInMs": 10000}}, "AppsApiSettings": {"AppsBaseUrl": "http://apps.dev.ebizautos/", "RequestTimeoutInMs": 300000, "AppsAuthorizationToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VyU2Vzc2lvbklkIjoiM2NkZmZjZjUtZmY4Zi00MTRhLTljNzQtZDkyNjc5MDE5ZjkwIiwiVXNlcklkIjoiNWJkOTg3NjdjMTM5NTIxYmY0YjZlZmE3IiwiaXNzIjoiQXBwc0FwaVNlcnZlciIsImF1ZCI6IkFwcHNBcGlBdWRpZW5jZSJ9.g11n0JKsOFtyVD56t1YJbb9SS7fWs7CrZf3Q0THQgf8", "InventoryApiCloseAccountPathTemplate": "api/inventory/{0}/close", "UsersApiUpdateInactiveStatusPathTemplate": "api/usersmanagement/{0}/users/changeinactivestatus?isinactive={1}", "UsersApiDeleteAccountUsersPathTemplate": "api/usersmanagement/{0}/users/delete", "SiteBoxApiFetchSitePathTemplate": "api/siteboxmanager/fetcher/{0}/fetch", "SiteBoxApiDeleteSitePathTemplate": "api/siteboxmanager/fetcher/{0}/cleanup", "SiteBoxApiDeleteSiteHostingSettingsPathTemplate": "api/siteboxmanager/sitehostings/{0}/delete", "SiteApiDeleteSettingsPathTemplate": "api/sites/{0}/delete"}, "ExceptionSettings": {"ErrorWebServiceLocation": "http://errors.aws.ebizautos.com/queuedservice.asmx", "ApplicationCategoryId": 9, "MailServer": "email-01.internal.aws.ebizautos.com", "MailPort": 25, "MailUserName": "", "MailPassword": "", "MailEnableSsl": false, "ErrorEmailFrom": "<EMAIL>", "ErrorEmailTo": ["alex.i<PERSON><PERSON><PERSON>@ebizautos.com"], "EmailCc": [], "HasToUseServiceBus": false}}