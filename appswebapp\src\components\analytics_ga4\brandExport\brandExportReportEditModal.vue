<template>
  <b-modal
    v-if="model"
    :visible="isShowModal"
    :title="getTitle"
    @hide="hide"
    size="lg"
    no-close-on-backdrop>
      <detail-row :big-payload-width="true">
        <span slot="title">File Name:</span>
        <b-form-input slot="payload" v-model="model.fileName"></b-form-input>
      </detail-row>
      <detail-row :big-payload-width="true">
        <span slot="title">File Name Date:</span>
        <b-form-input slot="payload" v-model="model.fileNameDateFormat"></b-form-input>
      </detail-row>
      <detail-row :big-payload-width="true">
        <span slot="title">Offset day(s) in file name:</span>
        <b-form-input slot="payload" type="number" v-model="model.fileNameDateShiftInDays"></b-form-input>
      </detail-row>
      <detail-row :big-payload-width="true">
        <span slot="title">Offset day(s) in report's data:</span>
        <b-form-input slot="payload" type="number" v-model="model.reportDateShiftInDays"></b-form-input>
      </detail-row>
      <detail-row :big-payload-width="true">
        <span slot="title">Row Delimiter:</span>
        <b-form-input slot="payload" v-model="model.rowDelimiter"></b-form-input>
      </detail-row>
      <detail-row :big-payload-width="true">
        <span slot="title">Column Delimiter:</span>
        <b-form-input slot="payload" v-model="model.columnDelimiter"></b-form-input>
      </detail-row>
      <detail-row :big-payload-width="true">
        <span slot="title">Ftp Subfolder:</span>
        <b-form-input slot="payload" v-model="model.ftpSubfolder"></b-form-input>
      </detail-row>
      <detail-row :big-payload-width="true">
        <span slot="title">Run Time:</span>
        <b-form-select slot="payload" v-model="model.runTime" :options="timeOptions"></b-form-select>
      </detail-row>
      <detail-row :big-payload-width="true">
        <span slot="title">Has To Include Column Names</span>
        <b-form-checkbox slot="payload" v-model="model.hasToIncludeColumnNames"></b-form-checkbox>
      </detail-row>
      <template #modal-footer>
        <b-btn @click="hide">Close</b-btn>
        <b-btn @click="save" variant="primary">Save</b-btn>
      </template>
  </b-modal>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import constants from '@/shared/analytics/constants'
import moment from 'moment'

export default {
  name: 'analytic-brand-export-report-edit-modal',
  props: {
    model: { type: Object },
    isShowModal: { type: Boolean, required: true }
  },
  components: {
    'detail-row': detailRow,
    'date-time-picker': () => import('@gravitano/vue-date-range-picker/src/components/DateRangePicker')
  },
  computed: {
    getTitle () {
      let res = constants.reportTypes.find(x => x.value === this.model.type)
      if (res) {
        return `Edit "${res.text}" Report Settings for export "${this.model.exportName}"`
      }

      return ''
    },
    timeOptions () {
      let options = []
      for (var hour = 0; hour < 24; hour++) {
        for (var minute = 0; minute < 60; minute = minute + 30) {
          options.push(
            {
              value: moment().hours(hour).minutes(minute).seconds(0).format('HH:mm:ss'),
              text: moment().hours(hour).minutes(minute).format('HH:mm')
            }
          )
        }
      }

      return options
    }
  },
  methods: {
    hide () {
      this.$emit('hide')
    },
    save () {
      this.$emit('save', this.model)
    }
  }
}
</script>
