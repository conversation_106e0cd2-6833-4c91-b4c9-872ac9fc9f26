<template>
  <div>
    <b-table
      :fields='getTableFields'
      :items="items"
      :sort-by="tableSortBy"
      :sort-desc="tableSortDesc"
      @sort-changed="onSortChanged"
      :no-local-sorting="true"
      :no-sort-reset="true"
      striped
      hover
      responsive
      class="products-table card-table"
    >
      <template #cell(account)="data">
        <span>{{getAccountDesc(data.item)}}</span>
      </template>
      <template #cell(name)="data">
        <div class="d-flex flex-column">
          <span v-if='data.item.userFirstName && data.item.userLastName' v-html="data.item.userFirstName + ' ' + data.item.userLastName"></span>
          <span v-if='data.item.userPhone'>{{data.item.userPhone}}</span>
          <span v-if='data.item.userEmail'>{{data.item.userEmail}}</span>
        </div>
      </template>
      <template #cell(campaign)="data">
        <div class="d-flex flex-column">
          <span v-for="campaign in data.item.campaignTypes" :key='campaign.campaignName'>{{campaign.campaignName}}</span>
        </div>
      </template>
      <template #cell(type)="data">
        <span v-if='data.item.communicationType === communicationTypes.sms.value'>
          <i class="ion ion-ios-chatboxes h4 m-0 opacity-100 d-none d-sm-inline"></i>
          {{communicationTypes.sms.label}}
        </span>
        <span v-if='data.item.communicationType === communicationTypes.voice.value'>
          <i class='ion ion-ios-call h4 m-0 opacity-100 d-none d-sm-inline'></i>
          {{communicationTypes.voice.label}}
        </span>
        <span v-if='data.item.communicationType === communicationTypes.email.value'>
          <i class='ion ion-ios-mail h4 m-0 opacity-100 d-none d-sm-inline'></i>
          {{communicationTypes.email.label}}
        </span>
        <span v-if='data.item.communicationType === communicationTypes.webForm.value'>
          <i class='ion ion-ios-mail h4 m-0 opacity-100 d-none d-sm-inline'></i>
          {{communicationTypes.webForm.label}}
        </span>
        <span v-if='data.item.communicationType === communicationTypes.eBay.value'>{{communicationTypes.eBay.label}}</span>
      </template>
      <template #cell(department)="data">
        <div class="d-flex flex-column">
          <span v-for='type in data.item.dealerContactDepartmentTypes' :key='type'>{{getDepartmentDesc(type)}}</span>
        </div>
      </template>
      <template #cell(vehicle)="data">
        <b-link v-if="data.item.vdpLink" class="vdp-link" target="_blank" rel="noopener noreferrer" :href="data.item.vdpLink">{{getVehicleTitle(data.item)}}</b-link>
        <span v-else>
          {{getVehicleTitle(data.item)}}
        </span>
      </template>
      <template #cell(manage)="data">
        <div class="d-flex flex-column">
          <b-btn v-if="data.item.leadType === leadType.creditApp.value && hasAccessToDownloadCreditApp(data.item.accountId)"
            size="sm"
            variant="primary"
            class="mb-1"
            @click="onDownloadCreditApp(data.item)"
          >
            Download
          </b-btn>
          <b-btn
            v-if="type === conversationTabTypes.leads.value"
            :to="{name: 'leads-messenger', params: {accountId: data.item.accountId}, query: {conversationid: data.item.conversationId}}"
            size="sm"
            variant="primary">
            Messenger
          </b-btn>
          <b-btn
            v-else-if='type === conversationTabTypes.archived.value && hasUserWritePermission(data.item.accountId, permissions.LeadsView)'
            @click="onDeleteArchiveConversation(data.item)"
            size="sm"
            variant="primary"
          >
            Delete
          </b-btn>
          <b-btn size="sm" :to='{name: "leads-contact", params: {accountId: data.item.accountId, conversationId: data.item.conversationId}}' class="mt-1">View Contact</b-btn>
        </div>
      </template>
    </b-table>
    <b-modal
     :visible="isShowDownloadCreditAppModal"
     @hide="hideDownloadCreditAppModal"
     no-close-on-backdrop
    >
      <detail-row fixedPayloadWidth>
        <span slot="title">Credit Application Password:</span>
        <b-form-input slot="payload" id="creditAppPassword" type="password" autocomplete="new-password" v-model="creditAppPassword"></b-form-input>
      </detail-row>
      <template #modal-footer>
        <b-btn variant="primary" @click="onSubmitAndDownload">Submit and Download</b-btn>
      </template>
    </b-modal>
  </div>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import applicationTypes from '../../../shared/common/applicationTypes'
import permissions from '../../../shared/common/permissions'
import moment from 'moment'
import { mapGetters } from 'vuex'
import { communicationTypes, contactTypes, conversationSortTypes, conversationTabTypes, leadType } from '@/shared/leads/common'

export default {
  name: 'leads-manager-listing',
  props: {
    hasAccountsLeadsToDisplay: Boolean,
    type: { type: Number, required: true },
    items: { type: Array, required: true }
  },
  data () {
    return {
      accountId: +this.$route.params.accountId,
      communicationTypes,
      conversationTabTypes,
      leadType,
      isShowDownloadCreditAppModal: false,
      creditAppPassword: '',
      conversationDetailsId: '',
      conversationId: '',
      permissions
    }
  },
  components: {
    'detail-row': detailRow
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {
        canManageAccountApplicationType: () => false,
        hasWritePermissions: () => false
      }
    },
    getTableFields () {
      let fields = []
      if (this.hasAccountsLeadsToDisplay) {
        fields.push({
          key: 'account',
          label: 'Account',
          sortable: true,
          sortTypeAsc: conversationSortTypes.accountIdAsc,
          sortTypeDesc: conversationSortTypes.accountIdDesc,
          tdClass: 'py-2 align-middle'
        })
      }
      return fields.concat([
        {
          key: 'dateTimeCreated',
          label: 'Received',
          sortable: true,
          sortTypeAsc: conversationSortTypes.dateCreatedAsc,
          sortTypeDesc: conversationSortTypes.dateCreatedDesc,
          tdClass: 'py-2 align-middle',
          formatter: value => moment(value).format('MM/DD/YYYY hh:mm A')
        },
        {
          key: 'name',
          label: 'Name',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'campaign',
          label: 'Campaign',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'type',
          label: 'Type',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'leadTypeName',
          label: 'Form Type',
          sortable: true,
          sortTypeAsc: conversationSortTypes.leadTypeAsc,
          sortTypeDesc: conversationSortTypes.leadTypeDesc,
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'department',
          label: 'Department',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'vehicle',
          label: 'Vehicle',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle'
        }
      ])
    },
    tableSortBy () {
      const sortedColumn = this.getTableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.getTableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sortType
      } else {
        return false
      }
    }
  },
  methods: {
    getVehicleTitle (item) {
      if (!item) return ''
      let title = ''
      if (item.vehicleYear > 0) {
        title = item.vehicleYear.toString()
      }

      title = [title, item.vehicleMake, item.vehicleModel, item.vehicleTrim].filter(v => v && v !== '').join(' ')

      return title.trim()
    },
    getDepartmentDesc (type) {
      let res = contactTypes.find(x => x.value === type)
      if (res) {
        return res.label
      }

      return ''
    },
    onSortChanged (value) {
      const sortingColumn = this.getTableFields.find(x => x.key === value.sortBy)
      if (value.sortDesc) {
        this.$emit('sortChange', sortingColumn.sortTypeDesc)
      } else {
        this.$emit('sortChange', sortingColumn.sortTypeAsc)
      }
    },
    onDeleteArchiveConversation (item) {
      const params = {
        conversationId: item.conversationId,
        conversationDetailsId: item.conversationDetailsId
      }
      this.$emit('deleteArchivedConversation', params)
    },
    onDownloadCreditApp (item) {
      this.conversationId = item.conversationId
      this.conversationDetailsId = item.conversationDetailsId
      this.isShowDownloadCreditAppModal = true
    },
    hideDownloadCreditAppModal () {
      this.isShowDownloadCreditAppModal = false
    },
    onSubmitAndDownload () {
      if (!this.creditAppPassword.trim()) {
        this.$toaster.error('Password is required')
        return
      }
      this.$store.dispatch('leads/getCreditAppPdfFile', {accountId: this.accountId, conversationId: this.conversationId, conversationDetailsId: this.conversationDetailsId, password: this.creditAppPassword}).then(res => {
        var fileURL = window.URL.createObjectURL(new Blob([res.data], { type: 'application/pdf' }))
        var fileLink = document.createElement('a')

        fileLink.href = fileURL
        fileLink.setAttribute('download', 'creditApp.pdf')
        document.body.appendChild(fileLink)

        fileLink.click()
      }).catch(ex => {
        if (ex.response && ex.response.status === 403) {
          this.$toaster.error('Invalid Credit Application password')
        } else {
          this.$toaster.error(`Something went wrong`)
          this.$logger.handleError(ex, 'Cannot download credit app pdf file')
        }
      }).finally(() => {
        this.creditAppPassword = ''
        this.isShowDownloadCreditAppModal = false
      })
    },
    hasUserWritePermission (accountId, permission) {
      return this.user.hasWritePermissions(accountId, permission, applicationTypes.AppsLeads.Id, permissions.LeadsFullAccess)
    },
    hasAccessToDownloadCreditApp (accountId) {
      return this.user &&
        this.user.accountId > 0 &&
        this.user.hasWritePermissions(accountId, permissions.LeadsView, applicationTypes.AppsLeads.Id, permissions.LeadsFullAccess)
    },
    getAccountDesc (item) {
      return item.dealershipName ? [item.dealershipName, item.accountId].join(' - ') : item.accountId
    }
  }
}
</script>

<style>
  .vdp-link {
    color: rgba(24, 28, 33, 0.9);
  }
  .vdp-link:hover {
    color: #bf0e16;
  }
</style>
