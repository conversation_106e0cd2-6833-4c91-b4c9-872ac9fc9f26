import permissions from '../shared/common/permissions'
import { displayTypes } from '../shared/inventory/inventoryTypes'
import applicationTypes from '../shared/common/applicationTypes'

const inventoryDefaultMeta = {
  applicationType: applicationTypes.InventoryManagement.Id,
  permissions: [permissions.IMManageVehicles],
  applicationFullAccess: permissions.IMFullAccess
}

export default [{
  path: '/inventory',
  meta: {
    ...inventoryDefaultMeta
  },
  component: () => import('@/layout/Layout2'),
  props: (route) => ({ accountId: +route.params.accountId }),
  children: [
    {
      path: '',
      name: 'inventory-accounts',
      meta: {
        ...inventoryDefaultMeta,
        permissions: [permissions.IMFullAccess],
        applicationFullAccess: permissions.IMFullAccess
      },
      component: () => import('../pages/inventory/inventoryAccountListing')
    }, {
      path: 'integrity',
      name: 'inventory-integrity-report',
      meta: {
        ...inventoryDefaultMeta,
        permissions: [permissions.IMFullAccess],
        applicationFullAccess: permissions.IMFullAccess
      },
      component: () => import('../pages/inventory/inventoryIntegrityReport')
    }, {
      path: 'servicesettings',
      name: 'inventory-service-settings',
      meta: {
        ...inventoryDefaultMeta,
        permissions: [permissions.IMFullAccess],
        applicationFullAccess: permissions.IMFullAccess
      },
      component: () => import('../pages/inventory/inventoryServiceSettings')
    }, {
      path: 'globalautovideosettings',
      name: 'inventory-global-autovideo-settings',
      meta: {
        ...inventoryDefaultMeta,
        permissions: [permissions.IMFullAccess],
        applicationFullAccess: permissions.IMFullAccess
      },
      component: () => import('../pages/inventory/inventoryGlobalAutoVideoSettings')
    }, {
      path: 'textconversion/:id(\\d+)/edit',
      props: (route) => ({ id: +route.params.id }),
      name: 'inventory-text-conversion-edit',
      meta: {
        ...inventoryDefaultMeta,
        permissions: [permissions.IMFullAccess],
        applicationFullAccess: permissions.IMFullAccess
      },
      component: () => import('@/pages/inventory/inventoryTextConversionManager')
    }, {
      path: 'textconversion/create',
      name: 'inventory-text-conversion-create',
      meta: {
        ...inventoryDefaultMeta,
        permissions: [permissions.IMFullAccess],
        applicationFullAccess: permissions.IMFullAccess
      },
      component: () => import('@/pages/inventory/inventoryTextConversionManager')
    }, {
      path: 'autovideoqueue',
      name: 'inventory-autovideo-queue',
      meta: {
        ...inventoryDefaultMeta,
        permissions: [permissions.IMFullAccess],
        applicationFullAccess: permissions.IMFullAccess
      },
      component: () => import('../pages/inventory/inventoryAutoVideoQueue')
    }, {
      path: ':accountId(\\d+)',
      component: () => import('@/pages/inventory/inventoryLayout'),
      props: (route) => ({ accountId: +route.params.accountId, inventoryDisplayType: displayTypes.default }),
      redirect: {
        name: 'inventory-description'
      },
      meta: {
        ...inventoryDefaultMeta
      },
      children: [{
        path: 'inventory',
        component: () => import('@/pages/inventory/inventoryDescriptions'),
        props: (route) => ({ accountId: +route.params.accountId, inventoryDisplayType: displayTypes.default }),
        name: 'inventory-description',
        meta: {
          ...inventoryDefaultMeta,
          inventoryDisplayType: displayTypes.default
        }
      }, {
        path: 'pricing',
        component: () => import('@/pages/inventory/inventoryPricing'),
        props: (route) => ({ accountId: +route.params.accountId, inventoryDisplayType: displayTypes.pricing }),
        name: 'inventory-pricing',
        meta: {
          ...inventoryDefaultMeta,
          inventoryDisplayType: displayTypes.pricing
        }
      }, {
        path: 'merchandising',
        component: () => import('@/pages/inventory/inventoryMerchandising'),
        props: (route) => ({ accountId: +route.params.accountId, inventoryDisplayType: displayTypes.merchandising }),
        name: 'inventory-merchandising',
        meta: {
          ...inventoryDefaultMeta,
          inventoryDisplayType: displayTypes.merchandising
        }
      }
      ]
    }, {
      path: ':accountId(\\d+)/edit/:vin',
      component: () => import('@/pages/detail/edit'),
      props: (route) => ({
        accountId: +route.params.accountId,
        vin: route.params.vin
      }),
      name: 'details',
      meta: {
        ...inventoryDefaultMeta
      }
    }, {
      path: ':accountId(\\d+)/settings',
      component: () => import('@/pages/inventory/inventorySettings'),
      props: (route) => ({ accountId: +route.params.accountId }),
      name: 'inventory-settings',
      meta: {
        ...inventoryDefaultMeta
      }
    }, {
      path: ':accountId(\\d+)/autovideosettings',
      component: () => import('@/pages/inventory/inventoryAccountAutoVideoSettings'),
      props: (route) => ({ accountId: +route.params.accountId }),
      name: 'inventory-account-autovideo-settings',
      meta: {
        ...inventoryDefaultMeta
      }
    }, {
      path: ':accountId(\\d+)/alerts',
      component: () => import('@/pages/inventory/inventoryTasks'),
      props: (route) => ({ accountId: +route.params.accountId }),
      name: 'inventory-tasks',
      meta: {
        ...inventoryDefaultMeta
      }
    }, {
      path: ':accountId(\\d+)/logs',
      component: () => import('@/pages/inventory/inventoryLogs'),
      props: (route) => ({ accountId: +route.params.accountId }),
      name: 'inventory-logs',
      meta: {
        ...inventoryDefaultMeta,
        permissions: [permissions.ViewLogs],
        applicationFullAccess: permissions.ViewLogs
      }
    }, {
      path: ':accountId(\\d+)/vehiclestatereport',
      name: 'inventory-vehicle-state-report',
      meta: {
        ...inventoryDefaultMeta,
        permissions: [permissions.IMFullAccess],
        applicationFullAccess: permissions.IMFullAccess
      },
      props: (route) => ({ accountId: +route.params.accountId }),
      component: () => import('../pages/inventory/inventoryVehicleStateReport')
    }, {
      path: ':accountId(\\d+)/email_ad',
      component: () => import('@/pages/inventory/inventoryEmailAdGenerator'),
      props: (route) => ({ accountId: +route.params.accountId }),
      name: 'inventory-email-ad-generator',
      meta: {
        ...inventoryDefaultMeta
      }
    }, {
      path: 'logs/autovideo',
      name: 'inventory-autovideo-logs',
      meta: {
        ...inventoryDefaultMeta,
        permissions: [permissions.ViewLogs]
      },
      component: () => import('@/pages/inventory/inventoryAutoVideoLogs')
    }, {
      path: 'logs/autovideo/:logType/:batchId/details',
      name: 'inventory-autovideo-log-details',
      meta: {
        ...inventoryDefaultMeta,
        permissions: [permissions.ViewLogs]
      },
      props: (route) => ({ batchId: route.params.batchId, logType: route.params.logType }),
      component: () => import('@/pages/inventory/inventoryAutoVideoLogDetails')
    }, {
      path: 'logs/mobile',
      name: 'inventory-mobile-log',
      meta: {
        ...inventoryDefaultMeta,
        permissions: [permissions.ViewLogs]
      },
      component: () => import('./../pages/inventory/inventoryLogMobile')
    }, {
      path: 'logs/desktop',
      name: 'inventory-desktop-log',
      meta: {
        ...inventoryDefaultMeta,
        permissions: [permissions.ViewLogs], // rewrite default permissions
        applicationFullAccess: permissions.ViewLogs
      },
      component: () => import('./../pages/inventory/inventoryLogDesktop')
    }, {
      path: 'logs/:logId',
      name: 'inventory-log-details',
      meta: {
        ...inventoryDefaultMeta,
        permissions: [permissions.ViewLogs],
        applicationFullAccess: permissions.ViewLogs
      },
      component: () => import('./../pages/inventory/inventoryLogDetails'),
      props: (route) => ({ logId: route.params.logId })
    }
  ]
}]
