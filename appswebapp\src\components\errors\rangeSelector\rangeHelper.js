import moment from 'moment-timezone'
import {NamedRange, NamedRangeSet} from '../../_shared/dateRangeSelector/rangeModels'
import { errorMonitorTimezone } from '@/shared/errorReport/constants'

let defaultRangeName = 'Today'

const predefinedRanges = new NamedRangeSet(defaultRangeName, 'Last 30 Days')

predefinedRanges.add(new NamedRange('Last 30 Days', [moment().tz(errorMonitorTimezone).subtract(30, 'days'), moment().tz(errorMonitorTimezone)]))

predefinedRanges.add(new NamedRange('Last 14 Days', [moment().tz(errorMonitorTimezone).subtract(14, 'days'), moment().tz(errorMonitorTimezone)]))

predefinedRanges.add(new NamedRange('Last 7 Days', [moment().tz(errorMonitorTimezone).subtract(7, 'days'), moment().tz(errorMonitorTimezone)]))

if (moment().tz(errorMonitorTimezone).day() === 1) {
  predefinedRanges.add(new NamedRange('Last 3 Days', [moment().tz(errorMonitorTimezone).subtract(3, 'days'), moment().tz(errorMonitorTimezone)]))
}

predefinedRanges.add(new NamedRange('Yesterday', [moment().tz(errorMonitorTimezone).subtract(1, 'days'), moment().tz(errorMonitorTimezone).subtract(1, 'days')]))

predefinedRanges.add(new NamedRange('Today', [moment().tz(errorMonitorTimezone), moment().tz(errorMonitorTimezone)]))

export default predefinedRanges
