<template>
  <div>
    <h4>Account Listing</h4>
    <b-form v-on:submit.prevent="applySearch">
      <b-card no-body>
        <b-card-body>
          <div class="form-row">
            <b-col xl="6" lg="6" md="5" sm="12">
              <b-input-group>
                <b-form-input v-model="filters.search" placeholder="Search..."/>
                <b-input-group-append>
                  <b-btn @click="applySearch">Go</b-btn>
                </b-input-group-append>
              </b-input-group>
            </b-col>
          </div>
        </b-card-body>
      </b-card>
    </b-form>
    <b-card v-if="!isLoading">
      <b-table
        :items="items"
        :fields="tableFields"
        :sort-by="tableSortBy"
        :sort-desc="tableSortDesc"
        @sort-changed="onSortChanged"
        :striped="true"
        :bordered="false"
        :no-sort-reset="true"
        :no-local-sorting="true"
        show-empty
        responsive
      >
        <template #cell(accountId)="data">
          <router-link class="website-accounts-link" :to="{name: 'website-page-builder', params: { accountId: data.item.accountId }} ">{{data.item.accountId}}</router-link>
        </template>
        <template #cell(accountName)="data">
          <router-link class="website-accounts-link" :to="{name: 'website-page-builder', params: { accountId: data.item.accountId }} ">{{data.item.accountName}}</router-link>
        </template>
        <template #cell(websiteStatus)="data">
          <checkbox-with-confirm
            :canManaged="user.isEbizAdmin"
            v-model="data.item.accountWebsiteStatus"
            :accountStatus="data.item.accountStatus"
            @input="updateAccountWebsiteStatus(data.item.accountId, $event)"
          />
        </template>

        <template #cell(pasStatus)="data">
          <checkbox-with-confirm-pas
            :canManaged="user.isEbizAdmin"
            v-model="data.item.accountPASStatus"
            :webSiteStatus="data.item.accountWebsiteStatus"
            :accountStatus="data.item.accountStatus"
            @input="updateAccountPASStatus(data.item.accountId, $event)" />
        </template>
      </b-table>
      <paging
        class="pt-0 pb-3"
        :pageNumber="filters.page"
        :pageSize="filters.pageSize"
        :totalItems="itemsTotalCount"
        titled
        pageSizeSelector
        @numberChanged="onPageChanged"
        @changePageSize="onChangePageSize"
      />
    </b-card>
    <loader v-else class="mt-5" size="lg"/>
  </div>
</template>

<script>
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import paging from '@/components/_shared/paging'
import loader from '@/components/_shared/loader'
import { accountListingSortTypes } from '@/shared/website/constants'
import { mapGetters } from 'vuex'
import CheckboxWithConfirm from '@/components/_shared/applicationAccountListing/hasActiveWebsiteCheckbox'
import CheckboxWithConfirmPas from '@/components/_shared/applicationAccountListing/pasEnableCheckbox'
import accountLevelFeatureStatuses from '@/shared/accounts/accountLevelFeatureStatuses'
import sitesSignalrMixin from '@/mixins/sites/sitesSignalrMixin'

const defaultValues = new ObjectSchema({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  search: { type: String, default: '' },
  sort: { type: Number, default: 1 }
})

const queryHelper = new QueryStringHelper(defaultValues)

export default {
  name: 'website-accounts',
  metaInfo: {
    title: 'Website Accounts'
  },
  mixins: [sitesSignalrMixin],
  data () {
    return {
      isLoading: true,
      items: [],
      itemsTotalCount: 0,
      isConnectionManuallyClosed: false,
      filters: defaultValues.getObject()
    }
  },
  beforeDestroy () {
    this.disconnectFromSignalR()
  },
  created () {
    this.filters = queryHelper.parseQueryStringToObject(this.$router)
    this.populateData()
    this.connectToSignalR()
  },
  components: {
    CheckboxWithConfirm,
    CheckboxWithConfirmPas,
    paging,
    loader
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {}
    },
    tableFields () {
      let fields = [
        {
          key: 'accountId',
          label: 'Account Id',
          sortable: true,
          sortTypeAsc: accountListingSortTypes.accountIdAsc,
          sortTypeDesc: accountListingSortTypes.accountIdDesc,
          thStyle: 'width: 15%'
        },
        {
          key: 'accountName',
          label: 'Account Name',
          sortable: true,
          sortTypeAsc: accountListingSortTypes.accountNameAsc,
          sortTypeDesc: accountListingSortTypes.accountNameDesc,
          thStyle: 'width: 35%'
        },
        {
          key: 'websiteStatus',
          label: 'Active Website',
          thStyle: 'width: 15%'
        },
        {
          key: 'pasStatus',
          label: 'PAS Enabled',
          thStyle: 'width: 15%'
        }
      ]
      return fields
    },
    tableSortBy () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sortType
      } else {
        return false
      }
    }
  },
  methods: {
    applySearch () {
      this.filters.page = 1
      this.synchronizeUrlAndReload()
    },
    onPageChanged (newPage) {
      this.filters.page = newPage
      this.synchronizeUrlAndReload()
    },
    onSortChanged (value) {
      const sortingColumn = this.tableFields.find(x => x.key === value.sortBy)
      if (value.sortDesc) {
        this.filters.sort = sortingColumn.sortTypeDesc
      } else {
        this.filters.sort = sortingColumn.sortTypeAsc
      }
      this.synchronizeUrlAndReload()
    },
    onChangePageSize (newSize) {
      this.filters.pageSize = newSize
      this.filters.page = 1
      this.synchronizeUrlAndReload()
    },
    synchronizeUrlAndReload () {
      queryHelper.rebuildParamsInQueryString(this.$router, this.filters)
      this.populateData()
    },
    populateData () {
      this.$store.dispatch('website/getAccountListing', {filters: this.filters}).then(res => {
        this.items = res.data.items
        this.itemsTotalCount = res.data.totalItemsCount
      }).catch(ex => {
        this.$toaster.error('Something went wrong', {timeout: 5000})
        this.$logger.handleError(ex, 'Exception occurred on getting account listing')
      }).finally(() => {
        this.isLoading = false
      })
    },
    async updateAccountWebsiteStatus (accountId, newStatus) {
      try {
        if (newStatus === accountLevelFeatureStatuses.ProcessDisabling.Value) {
          await this.$store.dispatch('website/disableWebsiteForAccount', {accountId})
        } else if (newStatus === accountLevelFeatureStatuses.ProcessEnabling.Value) {
          await this.$store.dispatch('website/enableWebsiteForAccount', {accountId})
        } else {
          throw new Error('Invalid command for updateAccountWebsiteStatus method')
        }
        // we don't show any success message here because signalR does
      } catch (err) {
        this.items.find(x => x.accountId === accountId).accountWebsiteStatus =
          newStatus === accountLevelFeatureStatuses.ProcessDisabling.Value
            ? accountLevelFeatureStatuses.Enabled.Value
            : newStatus === accountLevelFeatureStatuses.ProcessEnabling.Value
              ? accountLevelFeatureStatuses.Disabled.Value
              : accountLevelFeatureStatuses.Undefined.Value

        const statusLabel = Object.values(accountLevelFeatureStatuses).find(x => x.Value === newStatus).Label
        const errorMessage = `Can't update account website status for account ${accountId} to ${statusLabel}`
        if (err.response && err.response.data && err.response.data.executionResultMessage) {
          this.$toaster.error(err.response.data.executionResultMessage, { timeout: 8000 })
        } else {
          this.$toaster.error(errorMessage, { timeout: 8000 })
        }
        this.$logger.handleError(err, errorMessage)
      }
    },
    async updateAccountPASStatus (accountId, newStatus) {
      try {
        if (newStatus === accountLevelFeatureStatuses.ProcessDisabling.Value) {
          // Disabled PAS is not allowed
        } else if (newStatus === accountLevelFeatureStatuses.ProcessEnabling.Value) {
          await this.$store.dispatch('website/enablePASForAccount', {accountId})
        } else {
          throw new Error('Invalid command for updateAccountPASStatus method')
        }
        // we don't show any success message here because signalR does
      } catch (err) {
        this.items.find(x => x.accountId === accountId).accountPASStatus =
          newStatus === accountLevelFeatureStatuses.ProcessDisabling.Value
            ? accountLevelFeatureStatuses.Enabled.Value
            : newStatus === accountLevelFeatureStatuses.ProcessEnabling.Value
              ? accountLevelFeatureStatuses.Disabled.Value
              : accountLevelFeatureStatuses.Undefined.Value

        const statusLabel = Object.values(accountLevelFeatureStatuses).find(x => x.Value === newStatus).Label
        const errorMessage = `Can't update account pas status for account ${accountId} to ${statusLabel}`
        if (err.response && err.response.data && err.response.data.executionResultMessage) {
          this.$toaster.error(err.response.data.executionResultMessage, { timeout: 8000 })
        } else {
          this.$toaster.error(errorMessage, { timeout: 8000 })
        }
        this.$logger.handleError(err, errorMessage)
      }
    }
  }
}
</script>

<style>
.website-accounts-link {
  color: black;
}
</style>
