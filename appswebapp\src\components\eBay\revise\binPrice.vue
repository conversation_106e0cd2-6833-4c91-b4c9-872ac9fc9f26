<template>
  <ValidationObserver ref="validator">
  <b-card>
    <b-row class="border-bottom mb-2 py-2">
      <b-col>
        <strong>{{reviseHeader}}</strong>
      </b-col>
      <c-button v-if="!isDisabled" :message="`Are you sure you want ${btnDesc}?`" variant="primary" size="sm" @confirm="onConfirm">
        {{btnDesc}}
      </c-button>
      <loader class="mr-5" v-else size="sm"/>
    </b-row>
    <detail-row bootstrapMode :fixed-payload-width="true">
      <span slot="title">Listing ID:</span>
      <b-link slot="payload" :href="getListingUrl" class="text-info"><u>{{revise.AuctionId}}</u></b-link>
    </detail-row>
    <detail-row bootstrapMode :fixed-payload-width="true">
      <span slot="title">Bid Status:</span>
      <span slot="payload">
        {{revise.BidsTotal > 0 ? revise.BidsTotal : 'No Bids'}}<span v-if="revise.BidsTotal > 0">(<b-link :href="getViewBidsUrl" class="text-info"><u>View Bid History</u></b-link>)</span>
      </span>
    </detail-row>
    <detail-row bootstrapMode :fixed-payload-width="true">
      <span slot="title">Current High Bid:</span>
      <span slot="payload">{{revise.HighBid > 0 ? revise.HighBid : '-'}}</span>
    </detail-row>
    <detail-row bootstrapMode :fixed-payload-width="true">
      <span slot="title">Current BIN Price:</span>
      <span slot="payload">{{getBinPriceDesc}}</span>
    </detail-row>
    <ValidationProvider name="New Bin Price" :rules="getValidateRule" v-slot="{errors}">
    <detail-row :title-position="'start'" bootstrapMode v-if="!isRemovePrice" :fixed-payload-width="true" :error="errors[0]">
      <span slot="title">New BIN Price:</span>
      <b-form-input slot="payload" name="New_Bin_Price" v-model="binPriceData.NewBinPrice" type="number"></b-form-input>
    </detail-row>
    </ValidationProvider>
  </b-card>
  </ValidationObserver>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import loader from '@/components/_shared/loader'
import {mapGetters} from 'vuex'
import numeral from 'numeral'
import constants from '@/shared/ebay/constants'

export default {
  props: {
    btnDesc: {
      type: String,
      default: 'Revise'
    },
    reviseHeader: {
      type: String,
      default: ''
    },
    type: {
      type: Number,
      required: true
    }
  },
  data () {
    return {
      binPriceData: {
        NewBinPrice: null
      },
      isDisabled: false
    }
  },
  components: {
    detailRow,
    loader
  },
  computed: {
    ...mapGetters('eBayRevise', ['revise']),
    getListingUrl () {
      return constants.eBayInfoUrls.ebayItemUrl(this.revise.AuctionId)
    },
    getBinPriceDesc () {
      if (this.revise.BinPrice > 0) {
        return numeral(this.revise.BinPrice).format('$0,0')
      }

      return 'No Bin Price'
    },
    getViewBidsUrl () {
      return constants.eBayInfoUrls.ebayViewBids(this.revise.AuctionId)
    },
    getValidateRule () {
      if (this.type === constants.reviseOptions.addBinPrice.key) {
        return 'required|min_value:1'
      }
      if (this.type === constants.reviseOptions.lowerBinPrice.key) {
        return `required|min_value:1|max_value:${this.revise.BinPrice - 1}`
      }
      if (this.type === constants.reviseOptions.raiseBinPrice.key) {
        return `required|min_value:${this.revise.BinPrice + 1}`
      }
      return ''
    },
    isRemovePrice () {
      return this.type === constants.reviseOptions.removeBinPrice.key
    }
  },
  methods: {
    async onConfirm () {
      let validateRes = await this.validate()
      if (!validateRes) {
        return
      }
      let apiParams = {
        accountId: this.revise.AccountId,
        auctionId: this.revise.AuctionId,
        data: this.binPriceData
      }
      switch (this.type) {
        case constants.reviseOptions.addBinPrice.key:
          return this.addBinPrice(apiParams)
        case constants.reviseOptions.lowerBinPrice.key:
          return this.lowerBinPrice(apiParams)
        case constants.reviseOptions.raiseBinPrice.key:
          return this.raiseBinPrice(apiParams)
        case constants.reviseOptions.removeBinPrice.key:
          return this.removeBinPrice(apiParams)
      }
    },
    async validate () {
      if (this.isRemovePrice) {
        return true
      }

      let res = await this.$refs.validator.validate()

      return res
    },
    lowerBinPrice (apiParams) {
      this.isDisabled = true
      this.$store.dispatch('eBayRevise/lowerBinPrice', apiParams).then(res => {
        this.$toaster.success('Lowered Bin Price Successfully')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
        if (ex.response && ex.response.status !== 400) {
          this.$logger.handleError(ex, `Exception occurred on ${this.reviseHeader}`)
        }
      }).finally(() => {
        this.isDisabled = false
        setTimeout(() => this.$router.go(), 4000)
      })
    },
    raiseBinPrice (apiParams) {
      this.isDisabled = true
      this.$store.dispatch('eBayRevise/raiseBinPrice', apiParams).then(res => {
        this.$toaster.success('Raised Bind Price Successfully')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
        if (ex.response && ex.response.status !== 400) {
          this.$logger.handleError(ex, `Exception occurred on ${this.reviseHeader}`)
        }
      }).finally(() => {
        this.isDisabled = false
        setTimeout(() => this.$router.go(), 4000)
      })
    },
    addBinPrice (apiParams) {
      this.isDisabled = true
      this.$store.dispatch('eBayRevise/addBinPrice', apiParams).then(res => {
        this.$toaster.success('Added Bin Price Successfully')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
        if (ex.response && ex.response.status !== 400) {
          this.$logger.handleError(ex, `Exception occurred on ${this.reviseHeader}`)
        }
      }).finally(() => {
        this.isDisabled = false
        setTimeout(() => this.$router.go(), 4000)
      })
    },
    removeBinPrice (apiParams) {
      this.isDisabled = true
      this.$store.dispatch('eBayRevise/removeBinPrice', apiParams).then(res => {
        this.$toaster.success('Removed Bin Price Successfully')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
        if (ex.response && ex.response.status !== 400) {
          this.$logger.handleError(ex, `Exception occurred on ${this.reviseHeader}`)
        }
      }).finally(() => {
        this.isDisabled = false
        setTimeout(() => this.$router.go(), 4000)
      })
    }
  }
}
</script>
