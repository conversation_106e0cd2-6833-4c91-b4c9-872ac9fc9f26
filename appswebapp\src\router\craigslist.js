import permissions from '../shared/common/permissions'
import applicationTypes from '../shared/common/applicationTypes'

let craigslistDefaultMeta = {
  permissions: [permissions.ManageCraigslist],
  applicationFullAccess: permissions.CraigslistFullAccess,
  applicationType: applicationTypes.AppsCraigslist.Id
}

function getRequiredProps (route) {
  return {
    accountId: +route.params.accountId,
    reportGroupId: route.params.reportGroupId,
    query: route.query.datefrom && route.query.dateto
      ? {
        datefrom: route.query.datefrom,
        dateto: route.query.dateto
      }
      : null
  }
}

export default [
  {
    path: '/craigslist',
    component: () => import('@/layout/Layout2'),
    props: getRequiredProps,
    meta: {
      ...craigslistDefaultMeta
    },
    children: [
      {
        path: '',
        name: 'craigslist-dashboard-all-accounts',
        meta: {
          ...craigslistDefaultMeta,
          permissions: [permissions.CraigslistFullAccess],
          applicationFullAccess: permissions.CraigslistFullAccess
        },
        component: () => import('@/pages/craigslist/dashboardForAll')
      },
      {
        path: 'useractivity',
        name: 'craigslist-user-activity',
        meta: {
          ...craigslistDefaultMeta,
          permissions: [permissions.EbizAutosAdmin],
          applicationFullAccess: permissions.EbizAutosAdmin
        },
        component: () => import('@/pages/craigslist/userActivity')
      },
      {
        path: 'useractivity/:id/details',
        name: 'craigslist-user-activity-details',
        meta: {
          ...craigslistDefaultMeta,
          permissions: [permissions.EbizAutosAdmin],
          applicationFullAccess: permissions.EbizAutosAdmin
        },
        props: (route) => ({ logId: route.params.id }),
        component: () => import('@/pages/craigslist/userActivityDetails')
      },
      {
        path: 'craigslistservicesettings',
        name: 'craigslist-service-settings',
        meta: {
          ...craigslistDefaultMeta,
          permissions: [permissions.FullAccess],
          applicationFullAccess: permissions.FullAccess
        },
        component: () => import('@/pages/craigslist/craigslistServiceSettings')
      },
      {
        path: ':accountId(\\d+)',
        component: () => import('@/pages/craigslist/craigslistLayout'),
        props: (route) => ({ accountId: +route.params.accountId }),
        redirect: {
          name: 'craigslist-dashboard-for-account'
        },
        meta: {
          ...craigslistDefaultMeta
        },
        children: [
          {
            path: 'dashboard',
            name: 'craigslist-dashboard-for-account',
            component: () => import('@/pages/craigslist/dashboardForAccount'),
            props: (route) => ({ accountId: +route.params.accountId }),
            meta: {
              ...craigslistDefaultMeta
            }
          },
          {
            path: 'dashboard/campaign',
            name: 'create-campaign',
            component: () => import('@/pages/craigslist/createEditCampaign'),
            props: (route) => ({ accountId: +route.params.accountId }),
            meta: {
              ...craigslistDefaultMeta
            }
          },
          {
            path: 'dashboard/campaign/:campaignId',
            name: 'edit-campaign',
            component: () => import('@/pages/craigslist/createEditCampaign'),
            props: (route) => ({ accountId: +route.params.accountId, campaignId: route.params.campaignId }),
            meta: {
              ...craigslistDefaultMeta
            }
          },
          {
            path: 'inventory',
            name: 'craigslist-inventory-for-account',
            component: () => import('@/pages/craigslist/craigslistInventory'),
            props: (route) => ({ accountId: +route.params.accountId }),
            meta: {
              ...craigslistDefaultMeta
            }
          },
          {
            path: 'logs',
            name: 'craigslist-logs-for-account',
            component: () => import('@/pages/craigslist/craigslistLogs'),
            props: (route) => ({ accountId: +route.params.accountId }),
            meta: {
              ...craigslistDefaultMeta,
              permissions: [permissions.CraigslistFullAccess],
              applicationFullAccess: permissions.CraigslistFullAccess
            }
          },
          {
            path: 'logs/:guid',
            name: 'craigslist-log-detail',
            component: () => import('@/pages/craigslist/craigslistLogDetail'),
            props: (route) => ({accountId: +route.params.accountId, guid: route.params.guid}),
            meta: {
              ...craigslistDefaultMeta,
              permissions: [permissions.CraigslistFullAccess],
              applicationFullAccess: permissions.CraigslistFullAccess
            }
          },
          {
            path: 'post/:vin',
            name: 'craigslist-post',
            component: () => import('@/pages/craigslist/craigslistPost'),
            props: (route) => ({
              accountId: +route.params.accountId,
              vin: route.params.vin
            }),
            meta: {
              ...craigslistDefaultMeta
            }
          },
          {
            path: 'posts',
            name: 'craigslist-posts-for-account',
            component: () => import('@/pages/craigslist/craigslistPostsForAccount'),
            props: (route) => ({accountId: +route.params.accountId}),
            meta: {
              ...craigslistDefaultMeta
            }
          },
          {
            path: 'settings',
            name: 'craigslist-settings-for-account',
            component: () => import('@/pages/craigslist/craigslistSettings'),
            props: (route) => ({accountId: +route.params.accountId}),
            meta: {
              ...craigslistDefaultMeta
            }
          }
        ]
      }
    ]
  }]
