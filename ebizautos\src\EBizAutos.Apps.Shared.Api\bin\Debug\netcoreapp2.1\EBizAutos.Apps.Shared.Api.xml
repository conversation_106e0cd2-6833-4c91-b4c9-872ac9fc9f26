<?xml version="1.0"?>
<doc>
    <assembly>
        <name>EBizAutos.Apps.Shared.Api</name>
    </assembly>
    <members>
        <member name="M:EBizAutos.Apps.Shared.Api.Controllers.AuthController.Login(EBizAutos.Apps.Shared.Api.Models.LoginRequestModel)">
            <summary>
            Authenticates user via cookies
            </summary>
            <param name="model">Login credentials</param>
        </member>
        <member name="M:EBizAutos.Apps.Shared.Api.Controllers.AuthController.Logout">
            <summary>
            Terminates user cookie session
            </summary>
        </member>
        <member name="M:EBizAutos.Apps.Shared.Api.Controllers.AuthController.LogoutWithRedirect">
            <summary>
            Terminates user cookie session and redirects to home page
            </summary>
            <returns></returns>
        </member>
        <member name="M:EBizAutos.Apps.Shared.Api.Controllers.AuthController.GenerateTokenAsync(EBizAutos.Apps.Shared.Api.Models.LoginRequestModel)">
            <summary>
            Generates user authentication token
            </summary>
            <param name="model">Login credentials</param>
            <response code="200">Generated token</response>
        </member>
        <member name="M:EBizAutos.Apps.Shared.Api.Controllers.AuthController.InvalidateTokenAsync">
            <summary>
            Invalidates authentication token from header and terminates user session
            </summary>
        </member>
        <member name="M:EBizAutos.Apps.Shared.Api.Controllers.AuthController.ResetPassword(EBizAutos.Apps.Shared.Api.Models.PasswordResetRequestModel)">
            <summary>
            Sends password recovery email
            </summary>
            <param name="model">Login credentials</param>
        </member>
        <member name="M:EBizAutos.Apps.Shared.Api.Controllers.MobileController.GetLatestVersionIOS">
            <summary>
            Get ios latest available version on App Store
            </summary>
        </member>
        <member name="M:EBizAutos.Apps.Shared.Api.Controllers.MobileController.GetLatestVersionAndroid">
            <summary>
            Get ios latest available version on App Store
            </summary>
        </member>
        <member name="M:EBizAutos.Apps.Shared.Api.Controllers.UserController.InfoForUser">
            <summary>
            Returns application information for authenticated user
            </summary>
            <response code="200">Information about user and application</response>
        </member>
    </members>
</doc>
