<template>
  <div>
    <div v-if="isLoading && log">
        <div class="d-flex bd-highlight">
          <div class="mr-auto py-2 bd-highlight">
            <h4 class="float-left">Leads Details Log</h4>
          </div>
          <div class="py-2 bd-highlight">
            <b-btn @click="$router.go(-1)" class="float-right">Close</b-btn>
          </div>
        </div>
      <b-card>
        <log-node
          v-if="log"
          :data="log"
          :isExpandedShallow="true"
          :isExpandedDeep="false"
        />
      </b-card>
    </div>
    <div v-else-if="!isLoading" class="mt-4">
      <loader size='lg'/>
    </div>
    <div class="ml-4 font-weight-bolder" v-else>Not found</div>
  </div>
</template>

<script>
import LeadsLogService from '@/services/logs/LeadsLogService'
import loader from '@/components/_shared/loader'

export default {
  name: 'leads-details-log',
  props: {
    logType: { type: Number, required: true },
    logId: { type: String, required: true }
  },
  data () {
    return {
      log: null,
      isLoading: false
    }
  },
  components: {
    'loader': loader,
    'log-node': () => import('@/components/_shared/logItemNode.vue')
  },
  created () {
    this.loadContent()
  },
  methods: {
    loadContent () {
      LeadsLogService.getLeadsApiDetailsLog(this.logType, this.logId).then(result => {
        this.log = result.data
      }).catch(ex => {
        this.$toaster.exception(ex, `Failed on getting details of the log`, {timeout: 5000})
        if (ex.response && +ex.response.status === 400) {
          this.$router.push({name: 'leads-logs'})
        }
      }).finally(() => {
        this.isLoading = true
      })
    }
  }

}
</script>
