<template>
<ValidationObserver ref="validator">
<div>
  <b-row class="border-bottom pb-1">
    <b-col>
      <span>
        <b>
          Inventory Alerts Settings
        </b>
      </span>
    </b-col>

      <b-btn variant="secondary" size="sm" class="edit-btn" @click="setEditMode" v-if='!isEditMode' >
        <font-awesome-icon icon="pencil-alt" size="sm" />
        <span class="btn-title">Edit</span>
      </b-btn>
      <template v-else>
        <b-btn variant="primary" size="sm" class="save-btn" @click="onSave">
          <font-awesome-icon icon="cloud-upload-alt" />
          <span class="btn-title">Save</span>
        </b-btn>
        <b-btn size="sm" class="ml-2" @click="onCancel">
          <span class="btn-title">Cancel</span>
        </b-btn>
      </template>
  </b-row>
  <!--Photos Settings-->
  <b-row class='mt-3'>
    <b-col>
      <span>
        <b>
          Photos
        </b>
      </span>
    </b-col>
  </b-row>
  <b-row class='mt-2'>
    <b-col>
      <span>
          Set the minimum number of photos required for your vehicles. If a vehicle has more than the number of photos entered below it will not appear on the Alerts list.
      </span>
    </b-col>
  </b-row>
  <ValidationProvider name="Minimum Photos Amount" rules="required|min_value:0" v-slot="{errors}">
  <detail-row fixedPayloadWidth editMode :error="errors[0]">
    <span slot="title">Used inventory</span>
    <div slot="payload" class="w-100">
    <b-form-input type='number' name='usedMinimumPhotosAmount' v-model='inventoryAlertSettingsToUpdate.photoAlertSettings.photoAlertSettingsUsed.minimumPhotosAmount' :disabled='!isEditMode' class='inventory-task-input w-25 float-left mr-3'/>
    <b-form-checkbox class='mt-2 float-left' v-model='inventoryAlertSettingsToUpdate.photoAlertSettings.photoAlertSettingsUsed.hasToHideAlerts' :disabled='!isEditMode'>
        Do not include in alerts
    </b-form-checkbox>
    </div>
  </detail-row>
  </ValidationProvider>
  <ValidationProvider name="Minimum Photos Amount" rules="required|min_value:0" v-slot="{errors}">
  <detail-row fixedPayloadWidth editMode :error="errors[0]">
    <span slot="title">New inventory</span>
    <div slot="payload" class="w-100">
    <b-form-input type='number' name='newMinimumPhotosAmount' v-model='inventoryAlertSettingsToUpdate.photoAlertSettings.photoAlertSettingsNew.minimumPhotosAmount' :disabled='!isEditMode' class='inventory-task-input w-25 float-left mr-3'/>
    <b-form-checkbox class='mt-2 float-left' v-model='inventoryAlertSettingsToUpdate.photoAlertSettings.photoAlertSettingsNew.hasToHideAlerts' :disabled='!isEditMode'>
        Do not include in alerts
    </b-form-checkbox>
    </div>
  </detail-row>
  </ValidationProvider>
  <!--Video Settings-->
  <b-row class='mt-3'>
    <b-col>
      <span>
        <b>
          Video
        </b>
      </span>
    </b-col>
  </b-row>
  <b-row class='mt-2'>
    <b-col>
      <span>
          Set the minimum number of videos required for your vehicles. If a vehicle has more than the number of videos entered below it will not appear on the Alerts list.
      </span>
    </b-col>
  </b-row>
  <ValidationProvider name="Minimum Videos Amount" rules="required|min_value:0" v-slot="{errors}">
  <detail-row fixedPayloadWidth editMode :error="errors[0]">
    <span slot="title">Used inventory</span>
    <div slot="payload" class="w-100">
    <b-form-input type='number' name='usedMinimumVideosAmount' v-model='inventoryAlertSettingsToUpdate.videoAlertSettings.videoAlertSettingsUsed.minimumVideosAmount' :disabled='!isEditMode' class='inventory-task-input w-25 float-left mr-3'/>
    <b-form-checkbox class='mt-2 float-left' v-model='inventoryAlertSettingsToUpdate.videoAlertSettings.videoAlertSettingsUsed.hasToHideAlerts' :disabled='!isEditMode'>
        Do not include in alerts
    </b-form-checkbox>
    </div>
  </detail-row>
  </ValidationProvider>
  <ValidationProvider name="Minimum Videos Amount" rules="required|min_value:0" v-slot="{errors}">
  <detail-row fixedPayloadWidth editMode :error="errors[0]">
    <span slot="title">New inventory</span>
    <div slot="payload" class="w-100">
    <b-form-input type='number' name='newMinimumVideosAmount' v-model='inventoryAlertSettingsToUpdate.videoAlertSettings.videoAlertSettingsNew.minimumVideosAmount' :disabled='!isEditMode' class='inventory-task-input w-25 float-left mr-3'/>
    <b-form-checkbox class='mt-2 float-left' v-model='inventoryAlertSettingsToUpdate.videoAlertSettings.videoAlertSettingsNew.hasToHideAlerts' :disabled='!isEditMode'>
        Do not include in alerts
    </b-form-checkbox>
    </div>
  </detail-row>
  </ValidationProvider>
  <!--Prices Settings-->
  <b-row class='mt-3'>
    <b-col>
      <span>
        <b>
          Prices
        </b>
      </span>
    </b-col>
  </b-row>
  <b-row class='mt-2'>
    <b-col>
      <span>
          Check which prices to track. If a box is checked below a vehicle appear in the Alerts list if it does not have a value for that price in the system.
      </span>
    </b-col>
  </b-row>
  <detail-row fixedPayloadWidth editMode>
    <span slot="title">Used Inventory:</span>
    <b-form-select slot="payload" v-model='inventoryAlertSettingsToUpdate.priceAlertSettings.priceAlertTypeUsed' :options='priceAlertOptions' :disabled='!isEditMode'></b-form-select>
  </detail-row>
  <detail-row fixedPayloadWidth editMode>
    <span slot="title">New Inventory:</span>
    <b-form-select slot="payload" v-model='inventoryAlertSettingsToUpdate.priceAlertSettings.priceAlertTypeNew' :options='priceAlertOptions' :disabled='!isEditMode'></b-form-select>
  </detail-row>
  <!--Descriptions Settings-->
  <b-row class='mt-3'>
    <b-col>
      <span>
        <b>
          Descriptions
        </b>
      </span>
    </b-col>
  </b-row>
  <detail-row fixedPayloadWidth editMode>
    <span slot="title">Used inventory</span>
    <b-form-checkbox slot="payload" v-model='inventoryAlertSettingsToUpdate.descriptionAlertSettings.hasToHideAlertsUsed' :disabled='!isEditMode'>Do not include in alerts</b-form-checkbox>
  </detail-row>
  <detail-row fixedPayloadWidth editMode>
    <span slot="title">New inventory</span>
    <b-form-checkbox slot="payload" v-model='inventoryAlertSettingsToUpdate.descriptionAlertSettings.hasToHideAlertsNew' :disabled='!isEditMode'>Do not include in alerts</b-form-checkbox>
  </detail-row>
</div>
</ValidationObserver>
</template>

<script>
import { priceAlertType } from '@/shared/inventory/inventoryTypes'
import globals from '../../../globals'

export default {
  name: 'inventory-task-settings',
  components: {
    'auto-detail-row': () => import('@/components/details/helpers/autoDetailRow'),
    'detail-row': () => import('@/components/details/helpers/detailRow')
  },
  props: {
    inventoryAlertSettings: { type: Object, required: true }
  },
  data () {
    return {
      isEditMode: false,
      priceAlertOptions: priceAlertType,
      inventoryAlertSettingsToUpdate: {}
    }
  },
  created () {
    this.init()
  },
  methods: {
    init () {
      this.inventoryAlertSettingsToUpdate = globals().getClonedValue(this.inventoryAlertSettings)
    },
    onSave () {
      this.$refs.validator.validate().then((result) => {
        if (result) {
          this.$emit('onUpdateAlertSettings', this.inventoryAlertSettingsToUpdate)
          this.isEditMode = false
        }
      })
    },
    setEditMode () {
      this.isEditMode = true
    },
    onCancel () {
      this.inventoryAlertSettingsToUpdate = globals().getClonedValue(this.inventoryAlertSettings)
      this.isEditMode = false
    }
  }
}
</script>
