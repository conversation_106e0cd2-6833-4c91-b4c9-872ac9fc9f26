<template>
  <div class="inline">
    <b-check :disabled="isDisabled" v-model="checkboxValue" @change="onChangeCheckbox">{{statusMessage}}</b-check>
    <b-modal v-model="showModal" title="CONFIRM" lazy @ok="handleOk">
      <p class="confirmation-message">
        {{modalMessage}}
      </p>
    </b-modal>
  </div>
</template>

<script>
import accountLevelFeatureStatuses from '../../../shared/accounts/accountLevelFeatureStatuses'
import accountStatuses from '@/shared/accounts/accountStatuses'

export default {
  name: 'checkbox-with-confirm-pas',
  props: {
    value: {
      type: Number,
      required: true
    },
    webSiteStatus: {
      type: Number,
      required: true
    },
    accountStatus: {
      type: Number,
      required: true
    },
    canManaged: {
      type: Boolean,
      required: true
    }
  },
  data () {
    return {
      showModal: false,
      checkboxValue: this.getCheckboxValue()
    }
  },
  computed: {
    isDisabled () {
      if (!this.canManaged) {
        return true
      }
      return this.getCheckboxValue() || this.webSiteStatus !== accountLevelFeatureStatuses.Enabled.Value || this.value === accountLevelFeatureStatuses.ProcessEnabling.Value ||
        this.value === accountLevelFeatureStatuses.ProcessDisabling.Value || this.accountStatus === accountStatuses.Pending.Value || this.accountStatus === accountStatuses.Closed.Value
    },
    modalMessage () {
      return 'Are you sure? Please be aware PAS will be activated!'
    },
    statusMessage () {
      return this.value === accountLevelFeatureStatuses.ProcessDisabling.Value
        ? 'Disabling PAS is in progress'
        : this.value === accountLevelFeatureStatuses.ProcessEnabling.Value
          ? 'Enable PAS is in progress'
          : ''
    }
  },
  methods: {
    getCheckboxValue () {
      return this.value === accountLevelFeatureStatuses.ProcessEnabling.Value || this.value === accountLevelFeatureStatuses.Enabled.Value
    },
    onChangeCheckbox () {
      this.$nextTick(() => {
        this.checkboxValue = this.getCheckboxValue()
      })
      this.showModal = true
    },
    handleOk () {
      const val = this.value === accountLevelFeatureStatuses.Enabled.Value
        ? accountLevelFeatureStatuses.ProcessDisabling.Value
        : this.value === accountLevelFeatureStatuses.Disabled.Value
          ? accountLevelFeatureStatuses.ProcessEnabling.Value
          : 0

      this.$emit('input', val)
      this.showModal = false
    }
  },
  watch: {
    value () {
      this.checkboxValue = this.getCheckboxValue()
    }
  }
}
</script>

<style scoped>
  .inline{
    display: inline-block
  }

  .confirmation-message{
    font-weight: 600;
    margin: 0;
  }
</style>
