import permissions from '../shared/common/permissions'
import applicationTypes from '../shared/common/applicationTypes'

const leadsDefaultMeta = {
  applicationType: applicationTypes.AppsLeads.Id,
  permissions: [permissions.LeadsSendMessages],
  applicationFullAccess: permissions.LeadsFullAccess
}

export default [{
  path: '/leads',
  meta: {
    ...leadsDefaultMeta
  },
  component: () => import('@/layout/Layout2'),
  props: (route) => ({ accountId: +route.params.accountId }),
  children: [{
    path: '',
    name: 'leads-accounts',
    meta: {
      ...leadsDefaultMeta
    },
    component: () => import('@/pages/leads/leadsAccountListing')
  }, {
    path: 'useractivity',
    name: 'leads-user-activity',
    meta: {
      ...leadsDefaultMeta,
      permissions: [permissions.LeadsFullAccess],
      applicationFullAccess: permissions.LeadsFullAccess
    },
    component: () => import('@/pages/leads/leadsUserActivity')
  }, {
    path: 'useractivity/:id/details',
    name: 'leads-user-activity',
    meta: {
      ...leadsDefaultMeta,
      permissions: [permissions.LeadsFullAccess],
      applicationFullAccess: permissions.LeadsFullAccess
    },
    component: () => import('@/pages/leads/leadsUserActivityDetails'),
    props: (route) => ({logId: route.params.id})
  }, {
    path: 'logs',
    name: 'leads-logs',
    meta: {
      ...leadsDefaultMeta,
      permissions: [permissions.LeadsLogView],
      applicationFullAccess: permissions.LeadsLogView
    },
    component: () => import('@/pages/leads/leadsLogs')
  }, {
    path: 'logs/:logType(\\d+)/:logId',
    name: 'leads-details-log',
    meta: {
      ...leadsDefaultMeta,
      permissions: [permissions.LeadsLogView],
      applicationFullAccess: permissions.LeadsLogView
    },
    props: (route) => ({ logType: +route.params.logType, logId: route.params.logId }),
    component: () => import('@/pages/leads/leadsDetailsLog')
  }, {
    path: 'campaigntypes',
    name: 'leads-campaign-types',
    meta: {
      ...leadsDefaultMeta,
      permissions: [permissions.EbizAutosAdmin],
      applicationFullAccess: permissions.EbizAutosAdmin
    },
    component: () => import('@/pages/leads/leadsCampaignTypes')
  }, {
    path: 'spam',
    name: 'leads-spam',
    meta: {
      ...leadsDefaultMeta,
      permissions: [permissions.LeadsFullAccess],
      applicationFullAccess: permissions.LeadsFullAccess
    },
    component: () => import('@/pages/leads/leadsSpam')
  }, {
    path: 'integrityreport',
    name: 'leads-integrity-report',
    meta: {
      ...leadsDefaultMeta,
      permissions: [permissions.LeadsFullAccess],
      applicationFullAccess: permissions.LeadsFullAccess
    },
    component: () => import('@/pages/leads/leadsIntegrityReport')
  }, {
    path: ':accountId(\\d+)',
    meta: {
      ...leadsDefaultMeta
    },
    props: (route) => ({ accountId: +route.params.accountId }),
    component: () => import('@/pages/leads/leadsLayout'),
    redirect: {
      name: 'leads-dashboard'
    },
    children: [{
      path: 'dashboard',
      name: 'leads-dashboard',
      meta: {
        ...leadsDefaultMeta
      },
      component: () => import('@/pages/leads/leadsDashboardForAccount'),
      props: (route) => ({ accountId: +route.params.accountId })
    }, {
      path: 'messenger',
      name: 'leads-messenger',
      meta: {
        ...leadsDefaultMeta
      },
      component: () => import('@/pages/leads/leadsMessenger'),
      props: (route) => ({ accountId: +route.params.accountId })
    }, {
      path: 'manager',
      name: 'leads-manager',
      meta: {
        ...leadsDefaultMeta
      },
      props: (route) => ({ accountId: +route.params.accountId }),
      component: () => import('@/pages/leads/leadsManager')
    }, {
      path: 'contact/:conversationId',
      name: 'leads-contact',
      meta: {
        ...leadsDefaultMeta
      },
      props: (route) => ({ accountId: +route.params.accountId, conversationId: route.params.conversationId }),
      component: () => import('@/pages/leads/leadsUserContact')
    }, {
      path: 'settings',
      name: 'leads-settings',
      meta: {
        ...leadsDefaultMeta
      },
      props: (route) => ({ accountId: +route.params.accountId }),
      component: () => import('@/pages/leads/leadsSettings')
    }]
  }, {
    path: ':accountId(\\d+)/campaign/phone',
    meta: {
      ...leadsDefaultMeta,
      permissions: [permissions.LeadsManageCommunications],
      applicationFullAccess: permissions.LeadsManageCommunications
    },
    component: () => import('@/pages/leads/campaign/addEditPhoneCampaign'),
    props: (route) => ({ accountId: +route.params.accountId })
  }, {
    path: ':accountId(\\d+)/campaign/phone/:communicationId',
    meta: {
      ...leadsDefaultMeta,
      permissions: [permissions.LeadsManageCommunications],
      applicationFullAccess: permissions.LeadsManageCommunications
    },
    component: () => import('@/pages/leads/campaign/addEditPhoneCampaign'),
    props: (route) => ({ accountId: +route.params.accountId, communicationId: route.params.communicationId })
  }, {
    path: ':accountId(\\d+)/campaign/emailproxy',
    meta: {
      ...leadsDefaultMeta,
      permissions: [permissions.LeadsManageCommunications],
      applicationFullAccess: permissions.LeadsManageCommunications
    },
    component: () => import('@/pages/leads/campaign/addEditEmailProxyCampaign'),
    props: (route) => ({ accountId: +route.params.accountId })
  }, {
    path: ':accountId(\\d+)/campaign/emailproxy/:communicationId',
    meta: {
      ...leadsDefaultMeta,
      permissions: [permissions.LeadsManageCommunications],
      applicationFullAccess: permissions.LeadsManageCommunications
    },
    component: () => import('@/pages/leads/campaign/addEditEmailProxyCampaign'),
    props: (route) => ({ accountId: +route.params.accountId, communicationId: route.params.communicationId })
  }, {
    path: ':accountId(\\d+)/campaign/webform',
    meta: {
      ...leadsDefaultMeta
    },
    component: () => import('@/pages/leads/campaign/addEditWebFormCampaign'),
    props: (route) => ({ accountId: +route.params.accountId })
  }, {
    path: ':accountId(\\d+)/campaign/webform/:communicationId',
    meta: {
      ...leadsDefaultMeta
    },
    component: () => import('@/pages/leads/campaign/addEditWebFormCampaign'),
    props: (route) => ({ accountId: +route.params.accountId, communicationId: route.params.communicationId })
  }, {
    path: ':accountId(\\d+)/campaign/sms',
    meta: {
      ...leadsDefaultMeta,
      permissions: [permissions.LeadsManageCommunications],
      applicationFullAccess: permissions.LeadsManageCommunications
    },
    component: () => import('@/pages/leads/campaign/addEditSMSCampaign'),
    props: (route) => ({ accountId: +route.params.accountId })
  }, {
    path: ':accountId(\\d+)/campaign/sms/:communicationId',
    meta: {
      ...leadsDefaultMeta,
      permissions: [permissions.LeadsManageCommunications],
      applicationFullAccess: permissions.LeadsManageCommunications
    },
    component: () => import('@/pages/leads/campaign/addEditSMSCampaign'),
    props: (route) => ({ accountId: +route.params.accountId, communicationId: route.params.communicationId })
  }, {
    path: ':accountId(\\d+)/campaign/ebay/:communicationId',
    meta: {
      ...leadsDefaultMeta
    },
    component: () => import('@/pages/leads/campaign/editEBayCampaign'),
    props: (route) => ({ accountId: +route.params.accountId, communicationId: route.params.communicationId })
  }]
}]
