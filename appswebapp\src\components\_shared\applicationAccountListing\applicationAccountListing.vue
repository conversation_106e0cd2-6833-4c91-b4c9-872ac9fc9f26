<template>
  <div v-if="readyToShow" class="account-listing">
    <account-listing
      title="Account Listing"
      :filters="getFilters"
      :listing="listing"
      @searchChange="applySearch"
      @pageChange="pageChanged"
      @pageSizeChange="changePageSize"
      @sortChange="sortChange"
      :hasActions="hasActions && user.isEbizAdmin"
    >
      <div class="media align-items-center" slot="accountId" slot-scope="data">
        <listing-link :account-id="data.data.accountId" :force-link-name="routeName" :query="query">{{data.data.accountId}}</listing-link>
      </div>

      <div class="media align-items-center" slot="accountName" slot-scope="data">
        <listing-link :account-id="data.data.accountId" :force-link-name="routeName" :query="query">{{data.data.accountName}}</listing-link>
      </div>

      <div slot="status" slot-scope="data">
        {{getAccountStatusLabel(data.data.accountStatus)}}
      </div>

      <div slot="websiteStatus" slot-scope="data">
        <span v-if="data.data.accountWebsiteStatus === 1">Process Disabling</span>
        <span v-else-if="data.data.accountWebsiteStatus === 2">Disabled</span>
        <span v-else-if="data.data.accountWebsiteStatus === 3">Process Enabling</span>
        <span v-else-if="data.data.accountWebsiteStatus === 4">Enabled</span>
        <span v-else>-</span>
      </div>

      <div slot="mfaStatus" slot-scope="data">
        <span v-if="data.data.isMfaEnabled">Enabled</span>
        <span v-else>Disabled</span>
      </div>

      <div slot="websiteUrl" slot-scope="data">
        <a v-if="data.data.defaultWebsiteUrl" :href="data.data.defaultWebsiteUrl" target="_blank">{{data.data.defaultWebsiteUrl}}</a>
        <span v-else>-</span>
      </div>

      <div slot="actions" slot-scope="data">
        <div class="d-flex flex-column">
          <router-link
            :to="{ name: 'inventory-description', params: { accountId: data.data.accountId } }"
            class="btn btn-secondary btn-sm mb-1 w-100"
            v-if="user.isEbizAdmin"
          >
            <i class="ion ion-md-cube mr-1"></i>
            Manage Inventory
          </router-link>
          <router-link
            :to="{ name: 'account-settings', params: { accountId: data.data.accountId } }"
            class="btn btn-primary btn-sm mb-1 w-100"
            v-if="user.isEbizAdmin"
          >
            <i class="ion ion-md-settings mr-1"></i>
            Admin Settings
          </router-link>

        </div>
      </div>

    </account-listing>
  </div>
</template>

<script>
import {ObjectSchema} from '@/shared/common/objectHelpers'
import QueryStringHelper from '../../../shared/common/queryStringHelper'
import paging from '@/components/_shared/paging.vue'
import accountListingRaw from '../accountListingRaw'
import accountStatuses from '@/shared/accounts/accountStatuses'
import AppsListingLink from './AppsListingLink'
import {mapGetters} from 'vuex'

const defaultValues = new ObjectSchema({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  sort: { type: Number, default: 0 },
  search: { type: String, default: '' }
})

let queryHelper = new QueryStringHelper(defaultValues)

export default {
  name: 'AccountListing',
  props: ['routeName', 'applicationType', 'query', 'hasActions'],
  components: {
    'paging': paging,
    'account-listing': accountListingRaw,
    'listing-link': AppsListingLink
  },
  data () {
    return {
      filters: defaultValues.getObject(),
      listing: null,
      count: 0,
      readyToShow: false,
      showModal: false
    }
  },
  mounted () {
    this.filters = queryHelper.parseQueryStringToObject(this.$router)
    this.populateAccountListing()
      .then(x => { this.readyToShow = true })
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {}
    },
    getFilters () {
      return {
        ...this.filters,
        count: this.count
      }
    }
  },
  methods: {
    populateAccountListing () {
      return this.$store.dispatch(
        'accountManagement/getAccountListing',
        {
          applicationType: this.applicationType,
          filters: this.filters
        })
        .then(x => {
          let result = x.data

          this.filters.page = result.pageNumber
          this.filters.pageSize = result.pageSize
          this.filters.sort = result.sortType
          this.filters.search = result.search
          this.listing = result.accountsSummaries
          // this.listing = this.listing.map(x => ({...x, IsPASEnabled: this.listing.indexOf(x) % 5}))
          this.count = result.accountsCount

          queryHelper.rebuildParamsInQueryString(this.$router, this.filters)
        })
        .catch(ex => this.$logger.handleError(ex, 'Can\'t get account listing', {filters: this.filters}))
    },
    applySearch (search) {
      this.filters.page = 1
      this.filters.search = search
      this.populateAccountListing()
    },
    pageChanged (newPage) {
      this.filters.page = newPage
      this.populateAccountListing()
    },
    changePageSize (newSize) {
      this.filters.pageSize = newSize
      this.filters.page = 1
      this.populateAccountListing()
    },
    sortChange (newSort) {
      this.filters.sort = newSort
      this.populateAccountListing()
    },
    getAccountStatusLabel (accountStatus) {
      switch (accountStatus) {
        case accountStatuses.Active.Value:
          return accountStatuses.Active.Label
        case accountStatuses.Pending.Value:
          return accountStatuses.Pending.Label
        case accountStatuses.OnHold.Value:
          return accountStatuses.OnHold.Label
        case accountStatuses.Closed.Value:
          return accountStatuses.Closed.Label
        default:
          return accountStatuses.Undefined.Label
      }
    }

  }
}
</script>
