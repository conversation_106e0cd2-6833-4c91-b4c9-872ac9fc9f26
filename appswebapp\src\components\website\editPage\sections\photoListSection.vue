<template>
  <div>
    <div class="border-bottom">
      <b-row>
        <b-col class="m-0"><h6>Photo List</h6></b-col>
      </b-row>
    </div>
    <b-row class="d-flex justify-content-center mt-2" v-if="pageSettings.photoListing.length > 3">
      <b-btn variant="primary" v-if="!isUploadPhotMode" @click="addNewPhotoItem">Add photo</b-btn>
    </b-row>
    <b-row class="mt-3" v-if="!isUploadPhotMode">
      <b-col class="mt-2" xl="4" lg="4" md="6" sm="12" v-for="(photoItem, index) in pageSettings.photoListing" :key="photoItem.id">
        <photoCard @photoItemDelete="photoItemDelete" :imageHost="photoHost" :photo-item="photoItem" :index="index" :page-id="pageSettings.id" />
      </b-col>
      <b-col cols="4" v-if="pageSettings.photoListing.length < 3" class="mt-2">
       <file-upload
          message="Drop photo here"
          ref="fileUpload"
          acceptedFiles="image/*"
          autoUpload
          :uploadPath="getUploadPath"
          @uploadComplete="uploadComplete"
          :maxFiles="1"
        />
      </b-col>
    </b-row>
    <file-upload
      v-else
      message="Drop photo here"
      ref="fileUpload"
      acceptedFiles="image/*"
      autoUpload
      :uploadPath="getUploadPath"
      @uploadComplete="uploadComplete"
      :maxFiles="1"
    />
    <b-row class="d-flex justify-content-center mt-2"  v-if="pageSettings.photoListing.length >= 3">
      <b-btn variant="primary" v-if="!isUploadPhotMode" @click="addNewPhotoItem">Add photo</b-btn>
      <b-btn v-else @click="onCancelPhotoUpload">Cancel</b-btn>
    </b-row>
  </div>
</template>

<script>
import photoCard from '@/components/website/editPage/helpers/photoCard'
import dropzone from '@/components/details/helpers/dropzoneWrapperComponent'

export default {
  props: {
    pageSettings: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      accountId: +this.$route.params.accountId,
      photoHost: '',
      isUploadPhotMode: false
    }
  },
  components: {
    photoCard,
    'file-upload': dropzone
  },
  created () {
    this.populateImageHost()
  },
  computed: {
    getUploadPath () {
      return `/api/sites/${this.accountId}/page/${this.pageSettings.id}/photo/upload`
    }
  },
  methods: {
    addNewPhotoItem () {
      this.isUploadPhotMode = true
    },
    uploadComplete () {
      this.isUploadPhotMode = false
      this.$emit('refreshData')
    },
    populateImageHost () {
      this.$store.dispatch('website/getPhotoHost', { accountId: this.accountId }).then(res => {
        this.photoHost = res.data
      }).catch(ex => {
        this.$logger.handleError(ex, 'Cannot get image host')
      })
    },
    onCancelPhotoUpload () {
      this.isUploadPhotMode = false
    },
    photoItemDelete () {
      this.$emit('refreshData')
    }
  }
}
</script>
