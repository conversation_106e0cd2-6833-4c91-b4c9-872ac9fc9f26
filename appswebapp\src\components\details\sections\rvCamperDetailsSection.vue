<template>
  <details-section title="RV / Camper Details" v-model="mode" v-if="rvDetails" @cancel="onCancel">
    <div class="view" v-if="mode === 'view'">

      <auto-detail-row :title="rvFeatures.rvLength.name" :text="getSelectedAttributeOption(rvFeatures.rvLength).key"/>

      <auto-detail-row :title="rvFeatures.sleeps.name" :text="getSelectedAttributeOption(rvFeatures.sleeps).key"/>

      <auto-detail-row :title="rvFeatures.awningCount.name" :text="getSelectedAttributeOption(rvFeatures.awningCount).key"/>

      <auto-detail-row :title="rvFeatures.airConditioners.name" :text="getSelectedAttributeOption(rvFeatures.airConditioners).key"/>

      <auto-detail-row :title="rvFeatures.levelingJacks.name" :text="getSelectedAttributeOption(rvFeatures.levelingJacks).key"/>

      <auto-detail-row :title="rvFeatures.slideoutCount.name" :text="getSelectedAttributeOption(rvFeatures.slideoutCount).key"/>

      <auto-detail-row :title="rvFeatures.dryWeight.name" :text="getSelectedAttributeOption(rvFeatures.dryWeight).key"/>

      <auto-detail-row :title="rvFeatures.waterCapacity.name" :text="getSelectedAttributeOption(rvFeatures.waterCapacity).key"/>

    </div>

    <div class="edit" v-else-if="mode === 'edit'">

      <auto-detail-row :title="rvFeatures.rvLength.name" v-model="rvFeatures.rvLength.value" validation-rule="xml"/>

      <auto-detail-row :title="rvFeatures.sleeps.name" v-model="rvFeatures.sleeps.value" :options="getNameValueOptions(rvFeatures.sleeps.nameValueOptions)"/>

      <auto-detail-row :title="rvFeatures.awningCount.name" v-model="rvFeatures.awningCount.value" :options="getNameValueOptions(rvFeatures.awningCount.nameValueOptions)"/>

      <auto-detail-row :title="rvFeatures.airConditioners.name" v-model="rvFeatures.airConditioners.value" :options="getNameValueOptions(rvFeatures.airConditioners.nameValueOptions)"/>

      <auto-detail-row :title="rvFeatures.levelingJacks.name" v-model="rvFeatures.levelingJacks.value" :options="getNameValueOptions(rvFeatures.levelingJacks.nameValueOptions)"/>

      <auto-detail-row :title="rvFeatures.slideoutCount.name" v-model="rvFeatures.slideoutCount.value" :options="getNameValueOptions(rvFeatures.slideoutCount.nameValueOptions)"/>

      <auto-detail-row :title="rvFeatures.dryWeight.name" v-model="rvFeatures.dryWeight.value" validation-rule="xml"/>

      <auto-detail-row :title="rvFeatures.waterCapacity.name" v-model="rvFeatures.waterCapacity.value" :options="getNameValueOptions(rvFeatures.waterCapacity.nameValueOptions)"/>

    </div>
  </details-section>
</template>

<script>
import { mapGetters } from 'vuex'
import detailsSection from '@/components/details/detailsSection'
import autoDetailRow from '../helpers/autoDetailRow'
import detailRow from '../helpers/detailRow'
import featuresHelper from '../../../shared/details/featuresHelper'

export default {
  name: 'rv-camper-details-section',
  data () {
    return {
      mode: 'view'
    }
  },
  computed: {
    ...mapGetters('details', ['rvDetails']),
    rvFeatures () {
      return {
        rvLength: this.getFeatureById(-7004),
        sleeps: this.getFeatureById(-7005),
        awningCount: this.getFeatureById(-7001),
        airConditioners: this.getFeatureById(-7000),
        levelingJacks: this.getFeatureById(-7003),
        slideoutCount: this.getFeatureById(-7006),
        dryWeight: this.getFeatureById(-7002),
        waterCapacity: this.getFeatureById(-7007)
      }
    }
  },
  methods: {
    getFeatureById (id) {
      return featuresHelper.getFeatureById(this.rvDetails, id)
    },
    getNameValueOptions (nameValueOptions) {
      return featuresHelper.getNameValueOptions(nameValueOptions)
    },
    getSelectedAttributeOption (attribute) {
      return featuresHelper.getSelectedAttributeOption(attribute)
    },
    onCancel () {
      this.$forceUpdate()
    }
  },
  components: {
    'details-section': detailsSection,
    'auto-detail-row': autoDetailRow,
    'detail-row': detailRow
  }
}
</script>
