<template>
  <div class="position-relative">
    <div class="d-flex flex-row custom-analytics-ga4-logs-box">
      <h4 class="mt-3">Logs</h4>
      <loader size="sm" v-if="isAutoRefreshInProgress"/>
      <!-- Pagination -->
      <paging
        class="custom-analytics-ga4-logs-paging d-none d-md-block p-0"
        :pageNumber="filters.page"
        :pageSize="filters.pageSize"
        :totalItems="itemsTotalCount"
        @numberChanged="pageChangedAsync"
        @changePageSize="changePageSizeAsync"
      />
    </div>
    <b-tabs v-model="selectedTabIndex" :value="selectedTabIndex" class="nav-tabs-top nav-responsive-sm" no-fade>
      <b-tab v-for='tab in logsTabTypes' :key='tab.index' :title="tab.label" class="p-4">
        <div>
          <keep-alive>
            <component :is="currentFiltersComponent" @applyFilter='applyFiltersAsync' :filter='filters'></component>
          </keep-alive>
        </div>
      </b-tab>
    </b-tabs>
    <div v-if="!isLoading && itemsTotalCount > 0">
      <b-card>
        <keep-alive>
          <component :is="currentListingComponent"
                     :items='logItems'
                     :sortType='filters.sort'
                     @delete="onDelete"
                     @sort-changed='changeSortAsync'>
          </component>
        </keep-alive>
        <paging
          class="p-0"
          :pageNumber="filters.page"
          :pageSize="filters.pageSize"
          :totalItems="itemsTotalCount"
          titled
          pageSizeSelector
          @numberChanged="pageChangedAsync"
          @changePageSize="changePageSizeAsync"
        />
      </b-card>
    </div>
    <div v-else-if='isLoading' class="my-5">
      <loader size='lg'/>
    </div>
    <div v-else class="my-2">
      <span class="text-muted">Not Found</span>
    </div>
  </div>
</template>

<script>
import accountLogsFilterForm from '@/components/analytics_ga4/logs/accountLogsFilterForm.vue'
import accountLogsFilterListing from '@/components/analytics_ga4/logs/accountLogsFilterListing.vue'
import groupLogsFilterForm from '@/components/analytics_ga4/logs/groupLogsFilterForm.vue'
import groupLogsFilterListing from '@/components/analytics_ga4/logs/groupLogsFilterListing.vue'
import loader from '@/components/_shared/loader'
import paging from '@/components/_shared/paging'
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import analyticsConstants from '@/shared/analytics/constants'
import globals from '@/globals'
import { setIntervalAsync, clearIntervalAsync } from 'set-interval-async/fixed'

const defaultTabType = analyticsConstants.analyticsLogsTabTypes.getDefaultTabType()
const defaults = Object.freeze({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  search: { type: String, default: '' },
  typeTab: { type: Number, default: defaultTabType.index },
  sort: { type: Number, default: defaultTabType.defaultSort }
})
const filtersSchema = new ObjectSchema(defaults)
const queryHelper = new QueryStringHelper(filtersSchema)
const filters = filtersSchema.getObject()

export default {
  name: 'analytics-ga4-logs',
  metaInfo: {
    title: 'Analytics Logs'
  },
  data () {
    return {
      isLoading: true,
      isAutoRefreshInProgress: false,
      filters: filters,
      selectedTabIndex: 0,
      logItems: [],
      itemsTotalCount: 0,
      logsTabTypes: analyticsConstants.analyticsLogsTabTypes.tabTypes,
      timer: null,
      latestPromise: null
    }
  },
  computed: {
    selectedTab: {
      get () {
        const selectedTab = analyticsConstants.analyticsLogsTabTypes.getTab(this.filters.typeTab)
        if (!selectedTab) {
          return defaultTabType
        }
        return selectedTab
      }
    },
    currentFiltersComponent: {
      get () {
        return `analytics-${this.selectedTab.value}-logs-filters-form`
      }
    },
    currentListingComponent: {
      get () {
        return `analytics-${this.selectedTab.value}-logs-listing`
      }
    }
  },
  components: {
    'analytics-account-logs-filters-form': accountLogsFilterForm,
    'analytics-account-logs-listing': accountLogsFilterListing,
    'analytics-group-logs-filters-form': groupLogsFilterForm,
    'analytics-group-logs-listing': groupLogsFilterListing,
    'paging': paging,
    'loader': loader
  },
  async created () {
    this.filters = queryHelper.parseQueryStringToObject(this.$router)
    this.selectedTabIndex = this.selectedTab.index
    this.$watch('selectedTabIndex', this.updateTabIndexAsync)
    await this.loadContentAsync()
  },
  methods: {
    async updateTabIndexAsync (newValue) {
      filtersSchema.reset(defaults)
      this.filters = filtersSchema.getObject()
      this.filters.typeTab = newValue
      this.itemsTotalCount = 0
      this.logItems = []
      await this.synchronizeUrlAndReloadAsync()
    },
    async loadContentAsync () {
      await this.populateLogsListingAsync(this.filters)
      await this.startAutoRefreshAsync(globals().getClonedValue(this.filters))
    },
    async startAutoRefreshAsync (filter) {
      await this.cancelAutoRefreshAsync()
      if (filter.page === 1) {
        this.timer = await setIntervalAsync(this.populateLogsListingAsync, 30000, filter, true)
      }
    },
    async cancelAutoRefreshAsync () {
      if (!this.timer) {
        return
      }
      await clearIntervalAsync(this.timer)
      this.timer = null
    },
    async synchronizeUrlAndReloadAsync () {
      queryHelper.rebuildParamsInQueryString(this.$router, this.filters)
      this.isLoading = true
      await this.loadContentAsync()
    },
    async applyFiltersAsync () {
      this.filters.page = 1
      await this.synchronizeUrlAndReloadAsync()
    },
    async pageChangedAsync (newPage) {
      this.filters.page = newPage
      await this.synchronizeUrlAndReloadAsync()
    },
    async changePageSizeAsync (newSize) {
      this.filters.pageSize = newSize
      this.filters.page = 1
      await this.synchronizeUrlAndReloadAsync()
    },
    async changeSortAsync (newSort) {
      this.filters.sort = newSort
      await this.synchronizeUrlAndReloadAsync()
    },
    async populateLogsListingAsync (filters, isAutoRefreshCall = false) {
      this.isAutoRefreshInProgress = isAutoRefreshCall
      const promise = this.selectedTab.populateListing(filters)
      this.latestPromise = promise
      let result = null
      let exception = null

      try {
        result = await promise
      } catch (ex) {
        exception = ex
      } finally {
        if (isAutoRefreshCall) {
          this.isAutoRefreshInProgress = false
        }
      }

      if (this.latestPromise !== promise) {
        return
      }

      if (result) {
        this.logItems = result.data.model.items
        this.itemsTotalCount = result.data.model.total
      } else if (exception) {
        if (exception.response && exception.response.status === 400) {
          this.$toaster.warning(`Invalid Request`)
        } else {
          this.$toaster.error(`Cannot get logs listing. Message: ${exception.message}`)
          if (!isAutoRefreshCall) {
            this.$logger.handleError(exception, `Cannot get logs listing`)
          }
        }
      }
      if (!isAutoRefreshCall) {
        this.isLoading = false
      }
    },
    async onDelete (id) {
      try {
        let response = await this.selectedTab.deleteTask(id)
        if (response.data && response.data.model) {
          this.$toaster.success('Task Deleted Successfully')
        } else {
          this.$toaster.warning('Failed. Only Waiting tasks can be deleted')
        }
      } catch (ex) {
        this.$toaster.exception(ex, 'Failed on deleting task')
        this.$logger.handleError(ex, 'Failed on deleting')
      } finally {
        this.loadContentAsync()
      }
    }
  },
  async destroyed () {
    await this.cancelAutoRefreshAsync()
  }
}
</script>

<style>
.custom-analytics-ga4-logs-paging {
  position: absolute;
  right: 5px;
  top: 40px;
  z-index: 2;
}

.custom-analytics-ga4-logs-box {
  display: flex;
  justify-content: space-between;
}
</style>
