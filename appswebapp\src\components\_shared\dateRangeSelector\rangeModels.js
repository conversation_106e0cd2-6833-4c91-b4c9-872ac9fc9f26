import date from '../../../plugins/locale/date'
import * as moment from 'moment/moment'

class DateRange {
  constructor ({from, to, range}) {
    if (range && range.length !== 2) {
      throw new Error('Parameter "range" contains invalid amount of elements')
    }

    const fromDate = range ? range[0] : from
    const toDate = range ? range[1] : to

    this.from = moment.isMoment(fromDate)
      ? fromDate
      : date.toMomentDate(fromDate)

    this.to = moment.isMoment(toDate)
      ? toDate
      : date.toMomentDate(toDate)
  }

  get fromFormatted () {
    return date.getDayFormatted(this.from)
  }

  get toFormatted () {
    return date.getDayFormatted(this.to)
  }

  asFormattedStrings () {
    return [this.fromFormatted, this.toFormatted]
  }

  asArray () {
    return [this.from, this.to]
  }
}

class NamedRange extends DateRange {
  constructor (label, range) {
    if (Array.isArray(range)) {
      range = { from: range[0], to: range[1] }
    }

    if (range instanceof DateRange) {
      range = { from: range.from, to: range.to }
    }

    super(range)

    this.label = label
  }

  get rangeArray () {
    return this.asArray()
  }

  asLabelRangePare () {
    return {
      [this.label]: this.asArray()
    }
  }
}

class NamedRangeSet {
  constructor (defaultRangeName, biggestRangeName) {
    this.defaultRangeName = defaultRangeName
    this.biggestRangeName = biggestRangeName
    this.ranges = []
  };
  add (labeledRange) {
    this.ranges.push(labeledRange)
  };

  get rangePresets () {
    return this.ranges
  };

  get defaultRange () {
    return this.rangePresets.find(x => x.label === this.defaultRangeName)
  };

  get biggestRange () {
    return this.rangePresets.find(x => x.label === this.biggestRangeName)
  };

  asNameRangeObject () {
    return this.ranges.reduce((acc, i) => ({
      ...acc,
      ...i.asLabelRangePare()
    }), {})
  }

  isDefaultRangeLabel (label) {
    return label && label.toLowerCase() === this.defaultRangeName.toLowerCase()
  }

  isBiggestRangeLabel (label) {
    return label && label.toLowerCase() === this.biggestRangeName.toLowerCase()
  }
}

export {
  DateRange,
  NamedRange,
  NamedRangeSet
}
