<template>
  <div class="penske-report-page">

    <h4 class="d-flex justify-content-between align-items-center w-100 font-weight-bold pb-0 mb-4">
      <div>Penske Report</div>
    </h4>

    <b-row>
      <b-col>
        <b-tabs class="nav-tabs-top nav-responsive-sm" v-model="tabIndex" no-fade>
          <b-tab title="Month Data">
            <div class="d-flex justify-content-end p-3" v-if="filters.rangeType === rangeTypes.month">
              <a :href="getDownloadLink" class="btn btn-primary btn-round btn-md mr-2">
                <font-awesome-icon icon="download" size="sm" class="mr-1" />
                Download Excel
              </a>
              <date-selector min-view="month" max-view="month" :max-date="maxDate" :value="filters.dateTo" @input="onDataChanged" />
            </div>
          </b-tab>

          <b-tab title="Year to Date">
            <div class="d-flex justify-content-end p-3" v-if="filters.rangeType === rangeTypes.yearToDate">
              <a :href="getDownloadLink" class="btn btn-primary btn-round btn-md mr-2">
                <font-awesome-icon icon="download" size="sm" class="mr-1" />
                Download Excel
              </a>
              <date-selector min-view="month" max-view="month" :max-date="maxDate" :value="filters.dateTo" @input="onDataChanged" />
            </div>
          </b-tab>
        </b-tabs>
        <b-card no-body v-if="items.length > 0">
          <div class="table-wrapper">
            <penske-table :items="items" @sortTypeChanged="onPageSortChanged" :sort="filters.sortType" />
          </div>
        </b-card>
        <div v-else>
          No reports found
        </div>
      </b-col>
    </b-row>
  </div>
</template>

<script>
import dateSelector from '../../../components/_shared/dataSelector/dateSelector'
import penskeDataTable from './penskeDataTable'
import analyticsBuilders from '../../../shared/analytics/builders'
import dateHelper from '../../../plugins/locale/date'
import penskeTableSortTypes from './penskeTableSortTypes'
import constants from '../../../shared/analytics/constants'

const filterManager = analyticsBuilders.getFilterManager({
  dateTo: { type: String, default: dateHelper.endOfMonthFormattedDay(dateHelper.getPrevMonthStartDayFormatted()) },
  sortType: { type: Number, default: penskeTableSortTypes.accountNameAsc },
  pageSize: { type: Number, default: 0 }, // if page size and page number eq to 0 then we get all records
  pageNumber: { type: Number, default: 0 }, // if page size and page number eq to 0 then we get all records
  rangeType: { type: Number, default: constants.rangeTypes.month }
})

export default {
  name: 'penske',
  created () {
    this.setFilters()
    this.resetUi()
  },
  data () {
    return {
      isFirstLoad: true,
      filters: filterManager.defaultValue,
      items: [],
      maxDate: dateHelper.endOfMonth(dateHelper.getPrevMonthStart()),
      rangeTypes: constants.rangeTypes
    }
  },
  computed: {
    dateFrom () {
      return this.filters.rangeType === constants.rangeTypes.month
        ? dateHelper.startOfMonthFormattedDay(this.filters.dateTo)
        : dateHelper.startOfYearDayFormatted(this.filters.dateTo)
    },
    tabIndex: {
      get () {
        return this.filters.rangeType === constants.rangeTypes.month ? 0 : 1
      },
      set (value) {
        this.filters.rangeType = value === 0 ? constants.rangeTypes.month : constants.rangeTypes.yearToDate
        this.synchronizeUrl()
      }
    },
    getDownloadLink () {
      const dateFrom = this.dateFrom
      const dateTo = this.filters.dateTo
      const rangeType = this.filters.rangeType
      return `${constants.googleAnalytics4ApiEndpointsBasePath}download/pagreport?datefrom=${dateFrom}&dateto=${dateTo}&reportRangeType=${rangeType}`
    }
  },
  methods: {
    synchronizeUrl () {
      filterManager.urlHelper.rebuildParamsInQueryString(this.$router, this.filters)
    },
    onPageSortChanged (sortType) {
      this.filters.sortType = sortType
      this.synchronizeUrl()
    },
    setFilters () {
      this.filters = filterManager.urlHelper.normalizeObject(this.$route.query)
    },
    onDataChanged (val) {
      this.filters.dateTo = dateHelper.endOfMonthFormattedDay(val)

      this.synchronizeUrl()
    },
    async resetUi () {
      try {
        const store = await this.$store.dispatch(
          'analyticsGa4/getPAGReport',
          {
            dateFrom: this.dateFrom,
            dateTo: this.filters.dateTo,
            sortType: this.filters.sortType
          }
        )

        this.items = store.pagGroupReport.data.items
      } catch (err) {
        this.$toaster.error('Failed to get data from server', { timeout: 8000 })
        this.$logger.handleError(err, 'Can\'t get PAG report', this.filters)
      }
    }
  },
  watch: {
    '$route' (to, from) {
      this.setFilters()
      this.resetUi()
    }
  },
  components: {
    'penske-table': penskeDataTable,
    'dateSelector': dateSelector
  }
}
</script>

<style scoped>
  .table-wrapper {
    margin: 1rem;
  }

  @media (min-width: 576px) {
    .table-wrapper {
      max-height: calc(100vh - 355px);
      overflow: scroll;
    }
  }

  @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    .penske-report-page .table-wrapper {
      overflow-y: scroll;
      overflow-x: hidden;
    }
  }

  @supports (-ms-ime-align:auto) {
    .penske-report-page .table-wrapper {
      overflow-y: scroll;
      overflow-x: hidden;
    }
  }
</style>

<style>
  .penske-report-page .table-wrapper .table-responsive{
    width: max-content;
  }
  @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    .penske-report-page .table-wrapper .table-responsive{
      width: 100%;
    }
  }
</style>
