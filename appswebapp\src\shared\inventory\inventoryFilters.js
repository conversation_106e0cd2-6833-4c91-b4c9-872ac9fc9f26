import vehicleStatuses from '@/shared/common/vehicle/vehicleStatuses'
import vehicleConditions from '@/shared/common/vehicle/vehicleConditions'
import { sortTypes, priceTypes } from './inventoryTypes'
import { ObjectSchema } from '../common/objectHelpers'

export default new ObjectSchema({
  vStatus: { type: Number, default: [vehicleStatuses.live.value] },
  condition: { type: Number, default: vehicleConditions.all },
  pageNumber: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  sortType: { type: Number, default: sortTypes.yearDescMakeAsc },
  search: { type: String, default: '' },
  make: { type: String, default: null },
  model: { type: String, default: null },
  priceTo: { type: Number, default: null },
  highPriceTo: { type: Number, default: null },
  lowPriceTo: { type: Number, default: null },
  priceType: { type: Number, default: priceTypes.all },
  optionId: { type: Number, default: 0 }
})
