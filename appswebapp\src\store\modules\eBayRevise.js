import axios from 'axios'
import moment from 'moment'
import constants from '@/shared/ebay/constants'

export default {
  namespaced: true,
  state: {
    revise: {},
    reviseOptions: []
  },
  getters: {
    revise: state => state.revise,
    reviseOptions: state => state.reviseOptions
  },
  mutations: {
    setRevise (state, revise) {
      state.revise = revise
    },
    setReviseOptions (state, reviseOptions) {
      state.reviseOptions = reviseOptions
    }
  },
  actions: {
    async populateReviseData ({commit}, params) {
      const response = await axios.get(`/api/ebay/${params.accountId}/revise/${params.auctionId}`)

      commit('setRevise', response.data)

      let reviseOptions = buildReviseOptions(response.data)
      commit('setReviseOptions', reviseOptions)
    },
    async modifyTitlePrivateCategory ({commit}, params) {
      const response = await axios.post(`/api/ebay/${params.accountId}/revise/${params.auctionId}/modify_title_private_category`, params.data)

      return response
    },
    async addEBayUpgrades ({commit}, params) {
      const response = await axios.post(`/api/ebay/${params.accountId}/revise/${params.auctionId}/add_ebay_upgrades`, params.data)

      return response
    },
    async addToItemDescription ({commit}, params) {
      const response = await axios.post(`/api/ebay/${params.accountId}/revise/${params.auctionId}/add_to_item_description`, params.data)

      return response
    },
    async addReservePrice ({commit}, params) {
      const response = await axios.post(`/api/ebay/${params.accountId}/revise/${params.auctionId}/add_reserve_price`, params.data)

      return response
    },
    async changeReservePrice ({commit}, params) {
      const response = await axios.post(`/api/ebay/${params.accountId}/revise/${params.auctionId}/change_reserve_price`, params.data)

      return response
    },
    async lowerReservePrice ({commit}, params) {
      const response = await axios.post(`/api/ebay/${params.accountId}/revise/${params.auctionId}/lower_reserve_price`, params.data)

      return response
    },
    async removeReservePrice ({commit}, params) {
      const response = await axios.post(`/api/ebay/${params.accountId}/revise/${params.auctionId}/remove_reserve_price`)

      return response
    },
    async addBinPrice ({commit}, params) {
      const response = await axios.post(`/api/ebay/${params.accountId}/revise/${params.auctionId}/add_bin_price`, params.data)

      return response
    },
    async lowerBinPrice ({commit}, params) {
      const response = await axios.post(`/api/ebay/${params.accountId}/revise/${params.auctionId}/lower_bin_price`, params.data)

      return response
    },
    async raiseBinPrice ({commit}, params) {
      const response = await axios.post(`/api/ebay/${params.accountId}/revise/${params.auctionId}/raise_bin_price`, params.data)

      return response
    },
    async removeBinPrice ({commit}, params) {
      const response = await axios.post(`/api/ebay/${params.accountId}/revise/${params.auctionId}/remove_bin_price`)

      return response
    },
    async changeFixedPrice ({commit}, params) {
      const response = await axios.post(`/api/ebay/${params.accountId}/revise/${params.auctionId}/change_fixed_price`, params.data)

      return response
    },
    async changeStartingBid ({commit}, params) {
      const response = await axios.post(`/api/ebay/${params.accountId}/revise/${params.auctionId}/change_starting_bid`, params.data)

      return response
    },
    async changeAdvertisedPrice ({commit}, params) {
      const response = await axios.post(`/api/ebay/${params.accountId}/revise/${params.auctionId}/change_advertised_price`, params.data)

      return response
    },
    async modifyVehicleDescription ({commit}, params) {
      const response = await axios.post(`/api/ebay/${params.accountId}/revise/${params.auctionId}/modify_vehicle_description`, params.data)

      return response
    }
  }
}

function buildReviseOptions (reviseAuction) {
  let reviseOptions = []
  let endDateTime = moment(reviseAuction.EndDateTime)
  if (moment() > endDateTime) {
    return reviseOptions
  }

  let hasBids = reviseAuction.BidsTotal > 0 && !reviseAuction.IsLocal
  let isLocal = reviseAuction.IsLocal
  let isFixedPriceListing = reviseAuction.HasFixedPriceListing
  let reservePriceMet = hasBids && reviseAuction.HighBid > reviseAuction.ReservePrice
  let isUnder12Hours = moment(endDateTime).diff(moment(), 'minutes') < 721
  for (let reviseOption of Object.values(constants.reviseOptions)) {
    let isEnabled = false
    switch (reviseOption.key) {
      case constants.reviseOptions.modifyTitlePrivateCategory.key:
        isEnabled = !hasBids && !isUnder12Hours && !reservePriceMet
        break
      case constants.reviseOptions.addEBayUpgrades.key:
        isEnabled = !isLocal && (!reviseAuction.IsBold || !reviseAuction.SubTitle)
        break
      case constants.reviseOptions.addToItemDescription.key:
        isEnabled = !hasBids || !isUnder12Hours
        break
      case constants.reviseOptions.changeReservePrice.key:
        isEnabled = !hasBids && !isFixedPriceListing && !isLocal && !reservePriceMet
        break
      case constants.reviseOptions.lowerReservePrice.key:
        isEnabled = hasBids && !reservePriceMet && reviseAuction.ReservePrice > 0
        break
      case constants.reviseOptions.removeReservePrice.key:
        isEnabled = !hasBids && !isFixedPriceListing && !isLocal && reviseAuction.ReservePrice > 0 && !reservePriceMet
        break
      case constants.reviseOptions.addBinPrice.key:
        isEnabled = ((!hasBids && !isFixedPriceListing && !isLocal) || (hasBids && !reservePriceMet && !isUnder12Hours)) && reviseAuction.BinPrice === 0
        break
      case constants.reviseOptions.lowerBinPrice.key:
        isEnabled = !isFixedPriceListing && !isLocal && reviseAuction.BinPrice !== 0 && !reservePriceMet
        break
      case constants.reviseOptions.raiseBinPrice.key:
        isEnabled = !isFixedPriceListing && !isLocal && !hasBids && !isUnder12Hours && reviseAuction.BinPrice !== 0
        break
      case constants.reviseOptions.removeBinPrice.key:
        isEnabled = !isFixedPriceListing && !isLocal && !hasBids && !isUnder12Hours && reviseAuction.BinPrice !== 0
        break
      case constants.reviseOptions.changeFixedPrice.key:
        isEnabled = isFixedPriceListing && !hasBids
        break
      case constants.reviseOptions.changeStartingBid.key:
        isEnabled = !(reservePriceMet || hasBids || isFixedPriceListing)
        break
      case constants.reviseOptions.changeAdvertisedPrice.key:
        isEnabled = !hasBids && isLocal
        break
      case constants.reviseOptions.addReservePrice.key:
        isEnabled = !hasBids && !isFixedPriceListing && !isLocal && !isUnder12Hours && reviseAuction.ReservePrice === 0
        break
      case constants.reviseOptions.modifyVehicleDescription.key:
        isEnabled = !hasBids && !isUnder12Hours
        break
    }
    reviseOption.isEnabled = isEnabled
    reviseOptions.push(reviseOption)
  }

  return reviseOptions
}
