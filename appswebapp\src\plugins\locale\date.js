import moment from 'moment'

const FORMAT_DAY = 'MM/DD/YYYY'
const FORMAT_MONTH_YEAR = 'MMMM, YYYY'

export default {
  getMonthYearFormatted: function (date) {
    return moment(date).format(FORMAT_MONTH_YEAR)
  },
  getPrevMonthStart: function () {
    return moment().startOf('month').subtract(1, 'months').toDate()
  },
  getPrevMonthStartDayFormatted: function () {
    return moment().startOf('month').subtract(1, 'months').format(FORMAT_DAY)
  },
  getThisMonthStartDayFormatted: function () {
    return moment().startOf('month').format(FORMAT_DAY)
  },
  getNextMonthStartDayFormatted: function () {
    return moment().startOf('month').add(1, 'months').format(FORMAT_DAY)
  },
  getDayFormatted: function (date, format) {
    return moment(date).format(format || FORMAT_DAY)
  },
  getSecondsDurationFormatted: function (seconds) {
    const duration = moment.duration(seconds, 'seconds')

    const days = Math.trunc(duration.asDays()).toString()

    const hoursFormatted = duration.hours().toString().padStart(2, '0')
    const minutesFormatted = duration.minutes().toString().padStart(2, '0')
    const secondsFormatted = duration.seconds().toString().padStart(2, '0')

    return days > 0
      ? `${days}.${hoursFormatted}:${minutesFormatted}:${secondsFormatted}`
      : `${hoursFormatted}:${minutesFormatted}:${secondsFormatted}`
  },
  getYearPartFromFormattedDay: function (formattedDay) {
    return moment(formattedDay, FORMAT_DAY).year()
  },
  getQuarterPartFromFormattedDay: function (formattedDay) {
    return moment(formattedDay, FORMAT_DAY).quarter()
  },
  getMonthPartFromFormattedDay: function (formattedDay) {
    return moment(formattedDay, FORMAT_DAY).month()
  },
  getDateFromFormattedDay: function (formattedDay) {
    return moment(formattedDay, FORMAT_DAY).toDate()
  },
  getQuarterFromDate: function (date) {
    return moment(date).quarter()
  },
  addDaysToDate: function (date, num) {
    return moment(date).add(num, 'days').toDate()
  },
  toMomentDate: function (date) {
    return moment(date, FORMAT_DAY)
  },
  endOfMonthFormattedDay (date) {
    return moment(date).endOf('month').format(FORMAT_DAY)
  },
  startOfYearDayFormatted (date) {
    return moment(date).startOf('year').format(FORMAT_DAY)
  },
  startOfMonthFormattedDay (date) {
    return moment(date).startOf('month').format(FORMAT_DAY)
  },
  endOfMonth (date) {
    return moment(date).endOf('month').toDate()
  },
  secondsToTimestamp (seconds) {
    return moment().startOf('year') // we dont care about year here
      .seconds(seconds)
      .format('H:mm:ss')
  },
  minDate: function () {
    return moment('20100101', 'YYYYMMDD').format(FORMAT_DAY)
  },
  formatAHours: function (date) {
    return moment(date, 'HH:mm:ss').format('h A')
  },
  hours: function (date) {
    return moment(date, 'HH:mm:ss').hours()
  },
  formatHoursWithoutSuffix: function (date) {
    return moment(date, 'HH:mm:ss').format('h')
  },
  timeSuffix: function (date) {
    return moment(date, 'HH:mm:ss').format('A')
  },
  minutes: function (date) {
    return moment(date, 'HH:mm:ss').minutes()
  },
  getDayName (day) {
    return moment().weekday(day).format('dddd')
  },
  getDayNumber (day) {
    return moment().day(day).format('d')
  },
  timeWithAbbreviationToTime (time) {
    return moment(time, 'h A:mm:ss').format('HH:mm:ss')
  }
}
