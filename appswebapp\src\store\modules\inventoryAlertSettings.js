export default {
  namespaced: true,
  state: {
    inventoryAlertSettings: {
      photoAlertSettings: {
        photoAlertSettingsNew: {
          minimumPhotosAmount: 1,
          hasToHideAlerts: false
        },
        photoAlertSettingsUsed: {
          minimumPhotosAmount: 1,
          hasToHideAlerts: false
        }
      },
      videoAlertSettings: {
        videoAlertSettingsNew: {
          minimumVideosAmount: 1,
          hasToHideAlerts: false
        },
        videoAlertSettingsUsed: {
          minimumVideosAmount: 1,
          hasToHideAlerts: false
        }
      },
      priceAlertSettings: {
        priceAlertTypeNew: 0,
        priceAlertTypeUsed: 0
      },
      descriptionAlertSettings: {
        hasToHideAlertsNew: false,
        hasToHideAlertsUsed: false
      }
    }
  },
  getters: {
    inventoryAlertSettings: state => state.inventoryAlertSettings
  },
  mutations: {
    setInventoryAlertSettings (state, data) {
      state.inventoryAlertSettings = data
    }
  },
  actions: {
    populateInventoryAlertSettings ({commit}, data) {
      commit('setInventoryAlertSettings', data)
    }
  }
}
