<template>
  <b-nav pills class="justify-content-between justify-content-lg-end">
    <template v-for="tab in tabsConfiguration.tabs">
      <template v-if="tab.tabs">
        <b-nav-item-dropdown
          :key="tab.key"
          :html="`<i class='ion ${tab.iconClass} d-block d-lg-none'></i> ${tab.label}`"
          toggle-class="nav-link-custom"
          right>

          <b-dropdown-item
            v-for="subTab in tab.tabs"
            :key="subTab.key"
            @click="onSubTabClick(subTab, tab)"
            :active="subTab.key == activeSubTabs[tab.key].key"
            class="d-flex">
            {{subTab.label}} <i :class="subTab.iconClass" class="ion opacity-50 align-self-center ml-auto"></i>
          </b-dropdown-item>

        </b-nav-item-dropdown>
      </template>
      <template v-else>
        <b-nav-item
          :key="tab.key"
          :active="tab.key === activeTab.key"
          @click="onTabClick(tab)">

          <i :class="tab.iconClass" class="ion d-block d-lg-none"></i> <span v-html="tab.label"></span>

        </b-nav-item>
      </template>
    </template>
  </b-nav>
</template>

<script>
export default {
  name: 'graph-tabs',
  props: {
    tabsConfiguration: Object
  },
  data () {
    return {
      activeTab: this.tabsConfiguration.tabs.find(x => x.key === this.tabsConfiguration.defaultTabKey),
      activeSubTabs: this.tabsConfiguration.tabs.reduce((obj, tab) => {
        if (tab.tabs) {
          obj[tab.key] = tab.tabs.find(x => x.key === tab.defaultTabKey)
        }

        return obj
      }, {})
    }
  },
  methods: {
    onTabClick (tab) {
      if (tab.tabs) {
        return // clicked on dropdown
      }

      this.activeTab = tab
      this.$emit('tabSelect', tab)
    },
    onSubTabClick (subTab, tab) {
      this.activeSubTabs[tab.key] = subTab
      this.$emit('subTabSelect', subTab)
    }
  }
}
</script>
