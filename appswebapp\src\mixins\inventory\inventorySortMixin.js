import { sortTypes } from '@/shared/inventory/inventoryTypes'

export default {
  data () {
    return {
      currentTileSortType: sortTypes.yearDescMakeAsc,
      sortMappings: [
        { sortType: sortTypes.stockAsc, sortBy: 'stockNumber', sortDesc: false },
        { sortType: sortTypes.stockDesc, sortBy: 'stockNumber', sortDesc: true },

        { sortType: sortTypes.inStockAsc, sortBy: 'age', sortDesc: true },
        { sortType: sortTypes.inStockDesc, sortBy: 'age', sortDesc: false },

        { sortType: sortTypes.photosCountAsc, sortBy: 'actualPhotosCount', sortDesc: false },
        { sortType: sortTypes.photosCountDesc, sortBy: 'actualPhotosCount', sortDesc: true },

        { sortType: sortTypes.lowPriceAsc, sortBy: 'lowPrice', sortDesc: false },
        { sortType: sortTypes.lowPriceDesc, sortBy: 'lowPrice', sortDesc: true },

        { sortType: sortTypes.highPriceAsc, sortBy: 'highPrice', sortDesc: false },
        { sortType: sortTypes.highPriceDesc, sortBy: 'highPrice', sortDesc: true },

        { sortType: sortTypes.yearDescMakeAsc, sortBy: 'title', sortDesc: true },
        { sortType: sortTypes.yearAscMakeAsc, sortBy: 'title', sortDesc: false },
        { sortType: sortTypes.makeAsc, sortBy: 'title', sortDesc: true },
        { sortType: sortTypes.makeDesc, sortBy: 'title', sortDesc: false },

        { sortType: sortTypes.keywordsAsc, sortBy: 'hasKeywords', sortDesc: true },
        { sortType: sortTypes.keywordsDesc, sortBy: 'hasKeywords', sortDesc: false },

        { sortType: sortTypes.galleryDescriptionAsc, sortBy: 'galleryDescription', sortDesc: true },
        { sortType: sortTypes.galleryDescriptionDesc, sortBy: 'galleryDescription', sortDesc: false },

        { sortType: sortTypes.homepageFeaturedAsc, sortBy: 'isHomepageFeatured', sortDesc: true },
        { sortType: sortTypes.homepageFeaturedDesc, sortBy: 'isHomepageFeatured', sortDesc: false },

        { sortType: sortTypes.promotionalFlagAsc, sortBy: 'hasToIncludePromotionalFlag', sortDesc: true },
        { sortType: sortTypes.promotionalFlagDesc, sortBy: 'hasToIncludePromotionalFlag', sortDesc: false },

        { sortType: sortTypes.accountIdAsc, sortBy: 'accountId', sortDesc: false },
        { sortType: sortTypes.accountIdDesc, sortBy: 'accountId', sortDesc: true }
      ]
    }
  },
  methods: {
    getSortType (orderBy, sortDesc) {
      if (orderBy === 'title') {
        return this.currentTileSortType
      } else {
        this.currentTileSortType = undefined
        const sortMapping = this.sortMappings.find(v => v.sortBy === orderBy && v.sortDesc === sortDesc)

        if (sortMapping !== undefined) {
          return sortMapping.sortType
        } else {
          return this.sortMappings.find(x => x.sortType === sortTypes.yearDescMakeAsc).sortType
        }
      }
    },
    sortingChanged (arg) {
      if (arg.sortBy === 'title') {
        switch (this.currentTileSortType) {
          case sortTypes.yearDescMakeAsc:
            this.currentTileSortType = this.sortMappings.find(x => x.sortType === sortTypes.yearAscMakeAsc).sortType
            break
          case sortTypes.yearAscMakeAsc:
            this.currentTileSortType = this.sortMappings.find(x => x.sortType === sortTypes.makeAsc).sortType
            break
          case sortTypes.makeAsc:
            this.currentTileSortType = this.sortMappings.find(x => x.sortType === sortTypes.makeDesc).sortType
            break
          case sortTypes.makeDesc:
            this.currentTileSortType = this.sortMappings.find(x => x.sortType === sortTypes.yearDescMakeAsc).sortType
            break
          default:
            this.currentTileSortType = this.sortMappings.find(x => x.sortType === sortTypes.yearDescMakeAsc).sortType
            break
        }
      } else {
        this.currentTileSortType = this.getSortType(arg.sortBy, arg.sortDesc)
      }

      this.applySort(this.currentTileSortType)
    }
  },
  computed: {
    sortBy: {
      get () {
        const sortMapping = this.sortMappings.find(v => v.sortType === this.sortType)
        return (sortMapping !== undefined) ? sortMapping.sortBy : 'title'
      },
      set: function (value) {
      }
    },
    sortDesc: {
      get () {
        const sortMapping = this.sortMappings.find(v => v.sortType === this.sortType)
        return (sortMapping !== undefined) ? sortMapping.sortDesc : true
      },
      set (value) {
      }
    }
  }
}
