<template>
  <div class="video" :class="{'active': !inactive}" @click="setVideoMode">
    <template v-if="isPreviewMode">
      <div class="video__preview_wrapper preview">
        <div class="preview_video__media">
          <lazy-image :src="image" :preload-src="imagePreload" alt="eBizAutos video" />
        </div>
        <button class="video__button" aria-label="Play Video">
          <svg width="68" height="48" viewBox="0 0 68 48"><path class="video__button-shape" d="M66.52,7.74c-0.78-2.93-2.49-5.41-5.42-6.19C55.79,.13,34,0,34,0S12.21,.13,6.9,1.55 C3.97,2.33,2.27,4.81,1.48,7.74C0.06,13.05,0,24,0,24s0.06,10.95,1.48,16.26c0.78,2.93,2.49,5.41,5.42,6.19 C12.21,47.87,34,48,34,48s21.79-0.13,27.1-1.55c2.93-0.78,4.64-3.26,5.42-6.19C67.94,34.95,68,24,68,24S67.94,13.05,66.52,7.74z"></path><path class="video__button-icon" d="M 45,24 27,14 27,34"></path></svg>
        </button>
      </div>
    </template>
    <template v-else>
      <video ref="player" controls>
        <source :src="video" type="video/mp4" />
      </video>
    </template>
  </div>
</template>

<style src="@/vendor/libs/plyr/plyr.scss" lang="scss"></style>

<script>
import lazyImage from './lazyImage'
import * as plyr from 'plyr'

export default {
  name: 'video-lazy',
  props: {
    imagePreload: String,
    image: String,
    video: String,
    inactive: Boolean
  },
  data () {
    return {
      isPreviewMode: true,
      duration: null,
      player: null
    }
  },
  methods: {
    playerReady () { },
    setVideoMode () {
      if (!this.isPreviewMode || this.inactive) {
        return
      }

      this.isPreviewMode = false
      this.$nextTick(() => {
        const options = {
          tooltip: {
            controls: true,
            seek: true
          }
        }

        this.player = plyr.setup(this.$refs.player, options)[0]

        this.player.play()
      })
    }
  },
  components: {
    'lazy-image': lazyImage
  }

}
</script>

<style scoped>
  .video__preview_wrapper {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%;
  }

  .video.active {
    cursor: pointer;
  }

  .preview_video__media {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .video__button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 0;
    width: 68px;
    height: 48px;
    background-color: transparent;
    border: none;
  }

  .video.active .video__button {
    cursor: pointer;
  }

  .video__button-shape {
    fill: #212121;
    fill-opacity: 0.8;
  }

  .video__button {
    outline: none;
  }

  .video.active:hover .video__button-shape,
  .video.active .video__button:focus .video__button-shape{
    fill: #ff0000;
    fill-opacity: 1;
  }

  .video__button-icon {
    fill: #ffffff;
  }
</style>

// Customize video player play button
<style>
  .video .plyr .plyr__play-large {
    border: none;
    border-radius: 0;
    background: transparent !important;
    width: 68px;
    height: 48px;
    box-shadow: none;
  }

  .video .video__button-shape {
    fill: #212121;
    fill-opacity: 0.8;
  }

  .video .video__button-shape{
    fill: #ff0000;
    fill-opacity: 1;
  }

  .video__button-icon {
    fill: #ffffff;
  }
</style>
