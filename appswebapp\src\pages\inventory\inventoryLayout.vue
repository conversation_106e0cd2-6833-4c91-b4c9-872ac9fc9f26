<template>
  <div class="inventory-wrapper">
    <h4 class="d-flex justify-content-between align-items-center w-100 font-weight-bold pb-0 mb-4">
        <span>{{getInventoryTitle}}
          <c-button v-on:confirm="syncAccount()" variant="primary btn-round" size="sm" v-can="permission.IMFullAccess">
            <span class="ion ion-ios-sync"></span><span class="d-none d-sm-inline">&nbsp; Sync This Account</span>
          </c-button>
        </span>
        <span>
          <write-permission-wrapper variant="disable-overlay">
            <b-btn variant="primary btn-round" size="sm" v-on:click="openModal()"><span class="ion ion-ios-add"></span><span class="d-none d-sm-inline">&nbsp; Add Inventory</span></b-btn>
          </write-permission-wrapper>
        </span>
    </h4>

    <b-alert :show="dismissCountDown"
          variant="info"
          @dismissed="dismissCountDown=0"
          fade
          class="absolute-alert">
      All your vehicles have been added to the processing queue. Please Wait.
    </b-alert>

    <router-view></router-view>

    <b-modal id="step-one" title="Add Vehicle"
      v-model="addVehicleStage.show"
      size="lg"
      lazy
      :header-bg-variant="addVehicleStage.headerBgVariant"
      :header-text-variant="addVehicleStage.headerTextVariant">
      <b-form-group
          label="Vehicle Category"
          label-for="modal-vehicle-category"
          horizontal
      >
        <b-form-select v-model="addVehicleStage.selectedCategory" id="modal-vehicle-category" :options="vehicleTypes" class="mb-3" />
      </b-form-group>
      <b-form-group
          description="Must be 17 characters for 1981 and later vehicles. Use the stock # if you do not have a VIN."
          label="VIN for this Vehicle"
          label-for="modal-vin"
          horizontal
      >
        <b-form-input id="modal-vin" v-model.trim="addVehicleStage.vin"></b-form-input>
      </b-form-group>

      <div slot="modal-footer" class="w-10 error-message">
         <p class="text-left response-message">{{addVehicleStage.message}}</p>
      </div>

      <div slot="modal-footer" class="w-10">
         <b-btn class="float-right" variant="secondary" @click="closeModal()">
           Close
         </b-btn>
      </div>

      <div slot="modal-footer" class="w-10">
        <b-btn class="float-right" variant="primary" @click="createVehicle()">
          Create
        </b-btn>
      </div>
    </b-modal>
  </div>
</template>

<script>

import vehicleManagementService from '@/services/inventory/VehicleManagementService'
import syncService from '@/services/inventory/SyncService'
import permission from '@/shared/common/permissions'
import vehicleTypes from '@/shared/common/vehicle/vehicleTypes'
import writePermissionWrapper from '../../components/_shared/writePermissionWrapper'
import { displayTypes } from '@/shared/inventory/inventoryTypes'

export default {
  name: 'inventory-list',
  metaInfo () {
    return {
      title: this.getInventoryTitle
    }
  },
  props: {
    accountId: {
      type: Number,
      required: true
    }
  },
  data: function () {
    return {
      addVehicleStage: {
        vin: '',
        selectedCategory: vehicleTypes.Passenger.value,
        headerBgVariant: '',
        headerTextVariant: 'dark',
        show: false,
        message: ''
      },
      dismissCountDown: 0,
      permission
    }
  },

  methods: {
    syncAccount () {
      this.$toaster.success('Your vehicles added to processing queue.', { timeout: 5000 })
      return syncService.synchronizeAccountVehicles(this.accountId, true)
    },
    createVehicle () {
      this.setModalHeaderStyle('info', 'dark')
      return vehicleManagementService.createVehicle(this.accountId, {
        Vin: this.addVehicleStage.vin,
        VehicleTypeId: this.addVehicleStage.selectedCategory
      })
        .then(x => {
          this.closeModal()
          setTimeout(() => {
            this.$router.push({
              name: 'details',
              params: { accountId: this.accountId, vin: this.addVehicleStage.vin }
            })
          }, 200)
        })
        .catch(x => {
          setTimeout(x => {
            this.setModalHeaderStyle('danger', 'white')
          }, 200)
          if (x && x.response) {
            this.$toaster.error(x.response.data, { timeout: 10000 })
          }
        })
    },
    closeModal () {
      this.addVehicleStage.show = false
    },
    openModal () {
      this.addVehicleStage.vin = ''
      this.addVehicleStage.message = ''
      this.setModalHeaderStyle('', 'dark')
      this.addVehicleStage.show = true
    },
    setModalHeaderStyle (backgroundVariant, textVariant) {
      this.addVehicleStage.headerBgVariant = backgroundVariant
      this.addVehicleStage.headerTextVariant = textVariant
    }
  },
  computed: {
    vehicleTypes () {
      return [
        {
          ...vehicleTypes.Passenger,
          text: `Passenger Vehicles (Cars, Pickups, SUV's, Minivans)`
        },
        {
          ...vehicleTypes.Motorcycle,
          text: `Motorcycles (Cruisers, Sport Bikes, Dirt Bikes, Custom)`
        },
        {
          ...vehicleTypes.Truck,
          text: `Commercial Trucks`
        },
        {
          ...vehicleTypes.Rv,
          text: `RVs and Campers`
        },
        {
          ...vehicleTypes.Bus,
          text: `Buses and Motorcoaches`
        },
        {
          ...vehicleTypes.Scooter,
          text: `Scooters and Minibikes`
        },
        {
          ...vehicleTypes.Atv,
          text: `ATVs`
        },
        {
          ...vehicleTypes.Snow,
          text: `Snowmobiles`
        },
        {
          ...vehicleTypes.Boat,
          text: `Boats and Personal Watercraft`
        },
        {
          ...vehicleTypes.Plane,
          text: `Aircraft`
        },
        {
          ...vehicleTypes.Misc,
          text: vehicleTypes.Misc.title
        }
      ]
    },
    getInventoryTitle () {
      let displayType = this.$route.meta.inventoryDisplayType
      switch (displayType) {
        case displayTypes.default:
          return 'Manage Inventory'
        case displayTypes.pricing:
          return 'Inventory Pricing'
        case displayTypes.merchandising:
          return 'Inventory Merchandising'
        default:
          return 'Manage Inventory'
      }
    }
  },
  components: {
    'write-permission-wrapper': writePermissionWrapper
  }
}
</script>

<style lang="scss">
.inventory-wrapper{
  position: relative;
  .absolute-alert{
    position: absolute;
    top: -15px;
    width: 100%;
  }
}
.modal-header{
  transition: background-color 200ms linear;
}

.response-message{
  margin-bottom: 0
}

#step-one{
  .error-message{
    margin-right: 20px;
  }
}
</style>
<style src="@/vendor/styles/pages/products.scss" lang="scss"></style>
