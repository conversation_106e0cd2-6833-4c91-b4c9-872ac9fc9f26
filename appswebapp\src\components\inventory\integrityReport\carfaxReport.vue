<template>
  <div class="m-3">
    <b-row class="my-3 d-flex align-items-center">
      <b-col class="py-2" cols="12" lg="3" xl="4">
        <b-input-group>
          <b-form-input v-model="filters.accountId" type="number" placeholder="AccountId"></b-form-input>
          <b-input-group-append>
            <b-btn variant="primary" @click="applySearch">Submit</b-btn>
          </b-input-group-append>
        </b-input-group>
      </b-col>
      <b-col class="py-2 d-flex flex-row" cols="12" lg="3" xl="5">
        <b-form-checkbox @change="synchronizeUrlAndReload" v-model="filters.excludeDemoAccounts">Exclude Demo Accounts</b-form-checkbox>
      </b-col>
      <b-col v-if="isLastRunVerificationInfoLoaded" class="py-2 d-flex flex-row justify-content-start justify-content-lg-end align-items-center" cols="12" lg="6" xl="3">
        <span class="text-muted mr-1">{{getLastRunVerificationLabel}}</span>
        <l-button variant="primary" :loading="isInitiateNewVerificationRequesting"  @click="initiateNewVerification">Initiate New Verification</l-button>
      </b-col>
      <b-col v-else class="py-2">
        <loader/>
      </b-col>
    </b-row>
    <div v-if="!isLoading && items && items.length > 0">
      <paging
        :pageNumber="filters.page"
        :pageSize="filters.pageSize"
        :totalItems="itemsTotalCount"
        @numberChanged="pageChanged"
        @changePageSize="changePageSize" />
      <b-table
        :items="items"
        :fields="getTableFields"
        :sort-by="tableSortBy"
        :sort-desc="tableSortDesc"
        @sort-changed="onSortChanged"
        :no-sort-reset="true"
        :no-local-sorting="true"
        hover
        striped
        responsive
      >
        <template #cell(isAppsTryingToGetReport)="data">
          <i class="ion ion-ios-checkmark text-success zoomeds" style="font-size:2rem" v-if="data.item.isAppsTryingToRetrieveReportFromCarfaxApi"></i>
          <i class="ion ion-ios-close text-danger zoomeds" style="font-size:2rem" v-else></i>
        </template>
        <template #cell(isAccountIncludedInCarfaxExport)="data">
          <i class="ion ion-ios-checkmark text-success zoomeds" style="font-size:2rem" v-if="data.item.isAccountIncludedInCarfaxExport"></i>
          <i class="ion ion-ios-close text-danger zoomeds" style="font-size:2rem" v-else></i>
        </template>
        <template #cell(isDemoAccount)="data">
          <i class="ion ion-ios-checkmark text-success zoomeds" style="font-size:2rem" v-if="data.item.isDemoAccount"></i>
          <i class="ion ion-ios-close text-danger zoomeds" style="font-size:2rem" v-else></i>
        </template>
      </b-table>
      <paging
        :pageNumber="filters.page"
        :pageSize="filters.pageSize"
        :totalItems="itemsTotalCount"
        titled
        pageSizeSelector
        @numberChanged="pageChanged"
        @changePageSize="changePageSize" />
    </div>
    <div v-else-if="isLoading" class="py-5">
      <loader size="lg"/>
    </div>
    <div v-else>
      <span class="text-muted">Not Found</span>
    </div>
  </div>
</template>

<script>
import InventoryService from '@/services/inventory/InventoryService'
import loader from '@/components/_shared/loader'
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import paging from '@/components/_shared/paging.vue'
import moment from 'moment'
import {carfaxIntegrityReportSortTypes} from '@/shared/inventory/inventoryTypes'

const defaultValues = new ObjectSchema({
  pageSize: { type: Number, default: 25 },
  accountId: { type: Number, default: null },
  page: { type: Number, default: 1 },
  excludeExportAccounts: { type: Boolean, default: false },
  excludeAppsAccounts: { type: Boolean, default: false },
  excludeDemoAccounts: { type: Boolean, default: true },
  sort: { type: Number, default: carfaxIntegrityReportSortTypes.createdDateTimeDesc }
})
const queryHelper = new QueryStringHelper(defaultValues)

export default {
  name: 'integrity-carfax-report',
  data () {
    return {
      isLoading: true,
      items: [],
      itemsTotalCount: 0,
      filters: defaultValues.getObject(),
      lastRunStartedDateTime: null,
      isLastRunVerificationInfoLoaded: false,
      isInitiateNewVerificationRequesting: false
    }
  },
  components: {
    paging,
    loader
  },
  async created () {
    this.filters = queryHelper.parseQueryStringToObject(this.$router)
    this.populateData()
    this.populateLastRunVerificationStartDateTime()
  },
  computed: {
    getTableFields () {
      return [
        {
          key: 'accountId',
          label: 'Account Id',
          tdClass: 'py-2 align-middle',
          sortTypeAsc: carfaxIntegrityReportSortTypes.accountIdAsc,
          sortTypeDesc: carfaxIntegrityReportSortTypes.accountIdDesc,
          sortable: true
        },
        {
          key: 'isAppsTryingToGetReport',
          label: 'Is Apps Trying to get Report',
          tdClass: 'py-2 align-middle',
          sortTypeAsc: carfaxIntegrityReportSortTypes.isAppsAccountWithCarfaxAsc,
          sortTypeDesc: carfaxIntegrityReportSortTypes.isAppsAccountWithCarfaxDesc,
          sortable: true
        },
        {
          key: 'isAccountIncludedInCarfaxExport',
          label: 'Is Account Included in Carfax Export',
          tdClass: 'py-2 align-middle',
          sortTypeAsc: carfaxIntegrityReportSortTypes.isAccountIncludedInCarfaxExportAsc,
          sortTypeDesc: carfaxIntegrityReportSortTypes.isAccountIncludedInCarfaxExportDesc,
          sortable: true
        },
        {
          key: 'isDemoAccount',
          label: 'Is Demo Account',
          tdClass: 'py-2 align-middle',
          sortTypeAsc: carfaxIntegrityReportSortTypes.isDemoAccountAsc,
          sortTypeDesc: carfaxIntegrityReportSortTypes.isDemoAccountDesc,
          sortable: true
        },
        {
          key: 'createdDateTime',
          label: 'Created Date',
          tdClass: 'py-2 align-middle',
          formatter: value => moment(value).format('MM/DD/YYYY hh:mm:ss A'),
          sortTypeAsc: carfaxIntegrityReportSortTypes.createdDateTimeAsc,
          sortTypeDesc: carfaxIntegrityReportSortTypes.createdDateTimeDesc,
          sortable: true
        },
        {
          key: 'updatedDateTime',
          label: 'Updated Date',
          tdClass: 'py-2 align-middle',
          formatter: value => moment(value).format('MM/DD/YYYY hh:mm:ss A'),
          sortTypeAsc: carfaxIntegrityReportSortTypes.updatedDateTimeAsc,
          sortTypeDesc: carfaxIntegrityReportSortTypes.updatedDateTimeDesc,
          sortable: true
        }
      ]
    },
    getLastRunVerificationLabel () {
      if (this.lastRunStartedDateTime) {
        return `Last run at ${moment(this.lastRunStartedDateTime).format('MM/DD/YYYY')}`
      }

      return 'Not running yet'
    },
    tableSortBy () {
      const sortedColumn = this.getTableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.getTableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sortType
      } else {
        return false
      }
    }
  },
  methods: {
    pageChanged (newPage) {
      this.isLoading = true
      this.filters.page = newPage
      this.synchronizeUrlAndReload()
    },
    changePageSize (newSize) {
      this.isLoading = true
      this.filters.pageSize = newSize
      this.filters.page = 1
      this.synchronizeUrlAndReload()
    },
    onSortChanged (value) {
      const sortingColumn = this.getTableFields.find(x => x.key === value.sortBy)
      if (value.sortDesc) {
        this.filters.sort = sortingColumn.sortTypeDesc
      } else {
        this.filters.sort = sortingColumn.sortTypeAsc
      }
      this.synchronizeUrlAndReload()
    },
    applySearch () {
      this.filters.page = 1
      this.isLoading = true
      this.synchronizeUrlAndReload()
    },
    synchronizeUrlAndReload () {
      queryHelper.rebuildParamsInQueryString(this.$router, this.filters)
      this.populateData()
    },
    async populateData () {
      InventoryService.getCarfaxIntegrityReports(this.filters).then(res => {
        this.items = res.data.items
        this.itemsTotalCount = res.data.itemsTotalCount
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
      }).finally(() => {
        this.isLoading = false
      })
    },
    async populateLastRunVerificationStartDateTime () {
      InventoryService.getCarfaxIntegrityReportActivity().then(res => {
        this.lastRunStartedDateTime = res.data.lastStartedDateTime
      }).catch(ex => {
        console.error(ex)
      }).finally(() => {
        this.isLastRunVerificationInfoLoaded = true
      })
    },
    initiateNewVerification () {
      this.isInitiateNewVerificationRequesting = true
      InventoryService.refreshCarfaxIntegrityReports().then(res => {
        this.$toaster.success('Initiated New Verification Successfully')
        this.populateLastRunVerificationStartDateTime()
      }).catch(ex => {
        this.$toaster.exception(ex, 'Failed to Initiate New Verification')
      }).finally(() => {
        this.isInitiateNewVerificationRequesting = false
      })
    }
  }
}
</script>
