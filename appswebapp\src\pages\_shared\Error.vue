<template>
  <b-container>
    <b-row class="text-center">
      <b-col class="mt-3">
        <h1>We are sorry</h1>
        <h3>Something bad happened</h3>
        <span>Retry in {{ remainTicks }} sec</span>
      </b-col>
    </b-row>
  </b-container>
</template>

<script>
export default {
  name: 'ErrorView',
  props: ['backUrl', 'hash'],
  data () {
    return {
      remainTicks: 0,
      timerId: 0
    }
  },
  methods: {
    startCountdown: function () {
      this.remainTicks = 10

      this.timerId = window.setInterval(
        () => {
          this.remainTicks--
          if (this.remainTicks === 0) {
            window.clearInterval(this.timerId)
            this.$router.replace({ path: this.backUrl || '/' })
          }
        },
        1000
      )
    }
  },
  mounted () {
    this.startCountdown()
  },
  beforeRouteUpdate (to, from, next) {
    this.startCountdown()
    next()
  }
}
</script>
