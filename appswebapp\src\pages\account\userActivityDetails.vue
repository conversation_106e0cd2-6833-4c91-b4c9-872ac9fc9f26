<template>
  <div>
    <h4>User Activity Log Details</h4>
    <b-card>
      <log-node
        v-if='logDetails'
        :data="logDetails"
        :isExpandedShallow="true"
        :isExpandedDeep="false"
      />
    </b-card>
  </div>
</template>

<script>

import logNode from '@/components/_shared/logItemNode.vue'
import AccountUserActivityService from '@/services/accounts/AccountUserActivityService'

export default {
  name: 'account-user-activity',
  metaInfo: {
    title: 'User Activity Details'
  },
  props: {
    logId: String,
    type: Number
  },
  data () {
    return {
      logDetails: null
    }
  },
  created () {
    AccountUserActivityService.getUserActivityDetails(this.logId, this.type).then(res => {
      this.logDetails = { nodes: res.data.details }
    }).catch(ex => {
      this.$toaster.error('Failed on getting user activity details')
      this.$logger.handleError(ex, 'Exception occurred on api call', { params: {logId: this.logId} })
    })
  },
  components: {
    logNode
  }
}
</script>
