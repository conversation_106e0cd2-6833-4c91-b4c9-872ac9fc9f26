import craigslistDescriptions from './craigslistDescriptions'

class CraigslistDescriptionHelper {
  getAccountPriceDescription (key) {
    if (craigslistDescriptions.accountPriceDescription[key]) {
      return craigslistDescriptions.accountPriceDescription[key].Label
    }
    return 'Empty'
  }

  getCraigslistSourceDescription (key) {
    if (craigslistDescriptions.craigslistSourceDescription[key]) {
      return craigslistDescriptions.craigslistSourceDescription[key].Label
    }
    return 'Empty'
  }

  getCraigslistStatusDescription (key) {
    if (craigslistDescriptions.craigslistStatusDescription[key]) {
      return craigslistDescriptions.craigslistStatusDescription[key].Label
    }
    return 'Empty'
  }

  getEmailDisplayTypeDescription (key) {
    if (craigslistDescriptions.emailDisplayTypeDescription[key]) {
      return craigslistDescriptions.emailDisplayTypeDescription[key].Label
    }
    return 'Empty'
  }

  getPhoneDisplayTypeDescription (key) {
    if (craigslistDescriptions.phoneDisplayTypeDescription[key]) {
      return craigslistDescriptions.phoneDisplayTypeDescription[key].Label
    }
    return 'Empty'
  }

  getEmailDisplayTypeOptions () {
    const options = []
    Object.keys(craigslistDescriptions.emailDisplayTypeDescription).forEach(function (key) {
      options.push(
        {
          value: key,
          text: craigslistDescriptions.emailDisplayTypeDescription[key].Label
        }
      )
    })

    return options
  }

  getPhoneDisplayTypeOptions () {
    const options = []
    Object.keys(craigslistDescriptions.phoneDisplayTypeDescription).forEach(function (key) {
      options.push(
        {
          value: key,
          text: craigslistDescriptions.phoneDisplayTypeDescription[key].Label
        }
      )
    })

    return options
  }

  getAccountPriceOptions () {
    const options = []
    Object.keys(craigslistDescriptions.accountPriceDescription).forEach(function (key) {
      options.push(
        {
          value: key,
          text: craigslistDescriptions.accountPriceDescription[key].Label
        }
      )
    })

    return options
  }
}

export default CraigslistDescriptionHelper
