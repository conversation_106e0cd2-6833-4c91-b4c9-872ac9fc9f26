<template>
  <div>
    <div v-if='items.length > 0' class="p-4">
      <b-table
        :fields="getTableFields"
        :items="items"
        striped
        hover
        responsive
      >
        <template #cell(location)="data">
          <span>{{getLocationDesc(data.item.campaignTypes[0].type)}}</span>
        </template>
        <template #cell(communicationType)="data">
          <span>{{getCommunicationTypeDesc(data.item.communicationType)}}</span>
        </template>
        <template #cell(contacts)="data">
          <div v-if='data.item.contacts'>
            <span v-for="contact in data.item.contacts" :key='contact.contactId'>
              {{contact.contactNameWithDepartments}}
            </span>
          </div>
        </template>
        <template #cell(campaign)="data">
          <div v-for='campaignType in data.item.campaignTypes' :key='campaignType.campaignTypeId'>
            <div v-if="campaignType.campaignName && campaignType.campaignName !== '' && campaignType.isActive">
              <span>{{campaignType.campaignName}}</span>
            </div>
          </div>
        </template>
        <template #cell(proxyPhoneOrEmail)="data">
          <span>
            {{data.item.proxyContact}}
          </span>
        </template>
        <template #cell(forwardsTo)="data">
          <div class="d-flex flex-column" v-if='data.item.contacts && data.item.contacts.length > 0'>
            <div>
              <span>{{data.item.contacts[0].contactName}}</span>
            </div>
            <div>
              <span v-if='data.item.communicationType === communicationTypes.email.value'>{{data.item.contacts[0].email}}</span>
              <span v-else>{{data.item.contacts[0].selectedPhone.phone.phoneNumber}}</span>
            </div>
          </div>
        </template>
        <template #cell(manage)="data">
          <div v-if='!data.item.isDeletedCommunication' class="d-flex flex-column">
            <b-btn size='sm' :to=" { path: onEdit(data.item) }">Edit</b-btn>
            <b-btn v-if='!data.item.isDefaultWebFormCommunication && type !== campaignTabTypes.ebayLeads.key'
              size='sm' variant="primary" class="mt-1" @click="onDelete(data.item)">
              Delete
            </b-btn>
          </div>
        </template>
      </b-table>
    </div>
    <div v-else-if='type === campaignTabTypes.tracking.key'>
      <div class="d-flex flex-column text-center font-weight-bold my-4">
        <span>Want to track Phone and SMS Leads? Contact the Sales Team today at</span>
        <span><span class="text-primary">************</span> or <span class="text-primary"><EMAIL></span> to add this upgrade to your account.</span>
      </div>
    </div>
  </div>
</template>

<script>
import permission from '@/shared/common/permissions'
import { campaignTabTypes, campaignTableFields } from '@/shared/leads/campaign'
import { campaignTypes } from '@/shared/leads/campaignTypes'
import { leadType, communicationTypes } from '@/shared/leads/common'
import { mapGetters } from 'vuex'
import CommunicationService from '@/services/leads/CommunicationService'

export default {
  name: 'leads-campaign-listing',
  props: {
    type: { type: Number, required: true },
    items: { type: Array, required: true }
  },
  data () {
    return {
      campaignTabTypes,
      communicationTypes
    }
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {
        canManageAccountApplicationType: () => false
      }
    },
    getTableFields () {
      let tableFields = []
      switch (this.type) {
        case campaignTabTypes.tracking.key:
          tableFields = campaignTableFields.tracking
          break
        case campaignTabTypes.webForm.key:
          tableFields = campaignTableFields.webForm
          break
        case campaignTabTypes.ebayLeads.key:
          tableFields = campaignTableFields.ebayLeads
          break
      }
      if ((this.user && this.user.hasPermissions && this.user.hasPermissions(permission.LeadsManageCommunications)) ||
          this.type === campaignTabTypes.webForm.key ||
          this.type === campaignTabTypes.ebayLeads.key) {
        tableFields.push({
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle'
        })
      }

      return tableFields
    }
  },
  methods: {
    getLocationDesc (type) {
      let res = Object.values(campaignTypes).find(x => x.value === type)

      if (res) {
        return res.text
      }

      return ''
    },
    getLeadTypeDesc (settings) {
      if (!settings) {
        return ''
      }
      let res = Object.values(leadType).find(x => x.value === settings.leadType)

      if (res) {
        return res.label
      }

      return ''
    },
    getCommunicationTypeDesc (type) {
      let res = Object.values(communicationTypes).find(x => x.value === type)

      if (res) {
        return res.label
      }

      return ''
    },
    onEdit (item) {
      switch (item.communicationType) {
        case communicationTypes.sms.value:
          return `/leads/${item.accountId}/campaign/sms/${item.communicationId}`
        case communicationTypes.voice.value:
          return `/leads/${item.accountId}/campaign/phone/${item.communicationId}`
        case communicationTypes.webForm.value:
          return `/leads/${item.accountId}/campaign/webform/${item.communicationId}`
        case communicationTypes.email.value:
          return `/leads/${item.accountId}/campaign/emailproxy/${item.communicationId}`
        case communicationTypes.eBay.value:
          return `/leads/${item.accountId}/campaign/ebay/${item.communicationId}`
      }
    },
    onDelete (item) {
      CommunicationService.deleteCommunication(item.accountId, item.communicationId).then(res => {
        this.$emit('refresh')
        this.$toaster.success('Campaign Successfully Deleted')
      }).catch(ex => {
        this.$toaster.error(`Cannot delete campaign. Message: ${ex.response.data}`)
        this.$logger.handleError(ex, `Cannot delete campaign with id: ${item.communicationId} for accountId: ${item.accountId}`)
      })
    }
  }
}
</script>
