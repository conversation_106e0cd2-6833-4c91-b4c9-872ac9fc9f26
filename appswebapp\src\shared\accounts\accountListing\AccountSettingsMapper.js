import store from '../../../store'
import applicationTypes from '../../common/applicationTypes'

class AccountSettingsMapper {
  async getAccountSettings (accountId) {
    let siteSettings = await store.dispatch('accountSettings/getAccountSettings', accountId)

    let applications = siteSettings.applications

    let accountSettings = {
      accountId: accountId,
      applications: []
    }

    for (let appName in applications) {
      let app = applicationTypes.getByName(appName)
      let label = (app || {}).Label

      if (!label) {
        continue
      }

      accountSettings.applications.push({
        id: app.Id,
        label: label,
        isAvailable: applications[appName]
      })
    }

    return accountSettings
  }
  async updateAccountSettings (accountSettings) {
    let accountSettingsToUpdate = {
      accountId: accountSettings.accountId,
      applications: {}
    }

    accountSettings.applications.forEach(x => {
      const app = applicationTypes.getById(x.id)

      let name = (app || {}).Name

      if (!name) {
        return
      }

      accountSettingsToUpdate.applications[name] = x.isAvailable
    })

    await store.dispatch('accountSettings/updateAccountSettings', accountSettingsToUpdate)
  }
}

export default AccountSettingsMapper
