import vehicleStatuses from '@/shared/common/vehicle/vehicleStatuses'

export default {
  methods: {
    getStatusesStatisticByVehicles (items) {
      let vehicleStatusesArr = []
      for (let vStatus in vehicleStatuses) {
        vehicleStatusesArr.push(vehicleStatuses[vStatus])
      }

      let vehicleStatusesStatistic = items.reduce((obj, item) => {
        obj[item.vehicleStatus] = item
        return obj
      }, {})

      vehicleStatusesArr.forEach(function (element) {
        vehicleStatuses[element.key].vehiclesTotal = (vehicleStatusesStatistic[element.value] === undefined) ? 0 : vehicleStatusesStatistic[element.value].count
      })

      return vehicleStatuses
    }
  }
}
