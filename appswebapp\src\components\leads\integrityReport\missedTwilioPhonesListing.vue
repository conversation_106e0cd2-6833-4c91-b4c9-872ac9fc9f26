<template>
  <b-table
    :fields='getTableFields'
    :items='items'
    hover
    responsive>
    <template #cell(communicationId)="data">
      <router-link class="text-primary" :to="{ path: getPath(data.item) }">{{data.item.communicationId}}</router-link>
    </template>
    <template #cell(manage)="data">
      <b-btn @click="onDelete(data.item.id)" size='sm'>Delete Campaign</b-btn>
    </template>
  </b-table>
</template>

<script>
import moment from 'moment'
export default {
  name: 'missed-twilio-phones-listing',
  props: {
    items: { type: Array, required: true }
  },
  computed: {
    getTableFields () {
      return [
        {
          key: 'twilioPhone.phoneNumber',
          label: 'Phone Number',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'accountId',
          label: 'Account Id',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'communicationId',
          label: 'Campaign',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'dateTimeInserted',
          label: 'Report Date Created',
          tdClass: 'py-2 align-middle',
          formatter: value => moment(value).format('MM/DD/YYYY hh:mm:ss A')
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle'
        }
      ]
    }
  },
  methods: {
    onDelete (id) {
      this.$emit('delete', id)
    },
    getPath (item) {
      return `/leads/${item.accountId}/dashboard?communicationid=${item.communicationId}`
    }
  }
}
</script>
