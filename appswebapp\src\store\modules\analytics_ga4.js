import axios from 'axios'
import analyticsConstants from './../../shared/analytics/constants'

const graphQlEndpoint = `${analyticsConstants.googleAnalytics4ApiEndpointsBasePath}graphql`

const summaryPageSize = 10000
const summaryPageNumber = 1

const state = {
  rangeInfo: null,
  analyticsName: '',
  blackThemeOn: true
}
const getters = {
  rangeInfo: state => state.rangeInfo,
  dateRangeQuery: state => {
    if (state.rangeInfo && !state.rangeInfo.isDefaultRange) {
      return { datefrom: state.rangeInfo.range[0], dateto: state.rangeInfo.range[1] }
    }
    return null
  },
  analyticsName: state => state.analyticsName,
  blackThemeOn: state => state.blackThemeOn
}
const mutations = {
  setRangeInfo (state, rangeInfo) {
    state.rangeInfo = rangeInfo
  },
  setAnalyticsName (state, analyticsName) {
    state.analyticsName = analyticsName
  },
  setBlackThemeOn (state, blackThemeOn) {
    state.blackThemeOn = blackThemeOn
  }
}
const actions = {
  getDashboardSummaryCards (_, parameters) {
    return loadGraphQlData(
      `
      query DashboardSummaryCards($accountId: Int!, $dateFrom: DateTime!, $dateTo: DateTime!) {
        websiteOverviewSummary(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, autoRange: true) {
          data {
            sessions
            sessionsDelta
            totalLeads
            totalLeadsDelta
          }
        }
        websiteEngagementSummary(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, autoRange: true) {
          data {
            bounceRate
            bounceRateDelta
            avgSessionDuration
            avgSessionDurationDelta
          }
        }
      }
      `,
      parameters
    )
  },
  getDashboardDeviceSessionsReport (_, parameters) {
    return loadGraphQlData(
      `
      query DashboardDeviceSessionsReport($accountId: Int!, $dateFrom: DateTime!, $dateTo: DateTime!) {
        mobileOverviewSummary(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, autoRange: true) {
          data {
            sessions
          }
        }
        desktopOverviewSummary(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, autoRange: true) {
          data {
            sessions
          }
        }
      }
      `,
      parameters
    )
  },
  getDashboardDeviceSessionsTimeline (_, parameters) {
    return loadGraphQlData(
      `
      query DashboardDeviceSessionsTimeline($accountId: Int!, $dateFrom: DateTime!, $dateTo: DateTime!) {
        mobileOverviewTimeline(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, autoRange: true) {
          data {
            items {
              dateFrom
              sessions
            }
          }
        }
        desktopOverviewTimeline(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, autoRange: true) {
          data {
            items {
              dateFrom
              sessions
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getDashboardTrafficSourcesTimeline (_, parameters) {
    return loadGraphQlData(
      `
      query DashboardTrafficSourcesTimeline($accountId: Int!, $dateFrom: DateTime!, $dateTo: DateTime!, $rangeType: Int!, $count: Int!) {
        trafficSourcesTimeline(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, reportRangeType: $rangeType, count: $count) {
          data {
            source
            channelGrouping
            items {
              dateFrom
              dateTo
              sessions
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getWebsiteOverviewGraphAndSummary (_, parameters) {
    return loadGraphQlData(
      `
      query WebsiteOverviewGraphAndSummary($accountId: Int!, $dateFrom: DateTime!, $dateTo: DateTime!) {
        graph: websiteOverview(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: ${summaryPageSize}, pageNumber: ${summaryPageNumber}, sortType: ${analyticsConstants.websiteOverviewSortTypes.dateAsc}, autoRange: true) {
          statusCode
          errorMessage
          data {
            items {
              dateFrom
              sessions
              pageViews
              totalLeads
              convRatePerSession
            }
          }
        }
        summary: websiteOverviewSummary(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, autoRange: true) {
          statusCode
          errorMessage
          data {
            sessions
            sessionsDelta
            pageViews
            pageViewsDelta
            totalLeads
            totalLeadsDelta
            convRatePerSession
            convRatePerSessionDelta
          }
        }
      }
      `,
      parameters
    )
  },
  getWebsiteOverviewDetailsPage (_, parameters) {
    return loadGraphQlData(
      `
      query WebsiteOverviewDetailsPage($accountId: Int!, $dateFrom: DateTime!, $dateTo: DateTime!, $pageSize: Int!, $pageNumber: Int!, $sortType: Int!) {
        detailedData: websiteOverview(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: $pageSize, pageNumber: $pageNumber, sortType: $sortType) {
          statusCode
          errorMessage
          data {
            totalItems
            items {
              dateFrom
              sessions
              users
              pageViews
              totalLeads
              convRatePerSession
              formLeads
              smsLeads
              phoneLeads
              convRatePerUser
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getMobileOverviewGraphAndSummary (_, parameters) {
    return loadGraphQlData(
      `
      query WebsiteOverviewMobileGraphAndSummary($accountId: Int!, $dateFrom: DateTime!, $dateTo: DateTime!) {
        graph: mobileOverview(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: ${summaryPageSize}, pageNumber: ${summaryPageNumber}, sortType: ${analyticsConstants.websiteOverviewSortTypes.dateAsc}, autoRange: true) {
          statusCode
          errorMessage
          data {
            items {
              dateFrom
              sessions
              pageViews
              totalLeads
              convRatePerSession
            }
          }
        }
        summary: mobileOverviewSummary(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, autoRange: true) {
          statusCode
          errorMessage
          data {
            sessions
            sessionsDelta
            pageViews
            pageViewsDelta
            totalLeads
            totalLeadsDelta
            convRatePerSession
            convRatePerSessionDelta
          }
        }
      }
      `,
      parameters
    )
  },
  getMobileOverviewDetailsPage (_, parameters) {
    return loadGraphQlData(
      `
      query WebsiteOverviewMobileDetailsPage($accountId: Int!, $dateFrom: DateTime!, $dateTo: DateTime!, $pageSize: Int!, $pageNumber: Int!, $sortType: Int!) {
        detailedData: mobileOverview(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: $pageSize, pageNumber: $pageNumber, sortType: $sortType) {
          statusCode
          errorMessage
          data {
            totalItems
            items {
              dateFrom
              sessions
              users
              pageViews
              totalLeads
              convRatePerSession
              formLeads
              phoneLeads
              convRatePerUser
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getDesktopOverviewGraphAndSummary (_, parameters) {
    return loadGraphQlData(
      `
      query WebsiteOverviewDesktopGraphAndSummary($accountId: Int!, $dateFrom: DateTime!, $dateTo: DateTime!) {
        graph: desktopOverview(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: ${summaryPageSize}, pageNumber: ${summaryPageNumber}, sortType: ${analyticsConstants.websiteOverviewSortTypes.dateAsc}, autoRange: true) {
          statusCode
          errorMessage
          data {
            items {
              dateFrom
              sessions
              pageViews
              totalLeads
              convRatePerSession
            }
          }
        }
        summary: desktopOverviewSummary(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, autoRange: true) {
          statusCode
          errorMessage
          data {
            sessions
            sessionsDelta
            pageViews
            pageViewsDelta
            totalLeads
            totalLeadsDelta
            convRatePerSession
            convRatePerSessionDelta
          }
        }
      }
      `,
      parameters
    )
  },
  getDesktopOverviewDetailsPage (_, parameters) {
    return loadGraphQlData(
      `
      query WebsiteOverviewDesktopDetailsPage($accountId: Int!, $dateFrom: DateTime!, $dateTo: DateTime!, $pageSize: Int!, $pageNumber: Int!, $sortType: Int!) {
        detailedData: desktopOverview(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: $pageSize, pageNumber: $pageNumber, sortType: $sortType) {
          statusCode
          errorMessage
          data {
            totalItems
            items {
              dateFrom
              sessions
              users
              pageViews
              totalLeads
              convRatePerSession
              formLeads
              phoneLeads
              convRatePerUser
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getWebsiteEngagementGraphAndSummary (_, parameters) {
    return loadGraphQlData(
      `
      query WebsiteEngagementGraphAndSummary($accountId: Int!, $dateFrom: DateTime!, $dateTo: DateTime!) {
        graph: websiteEngagement(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: ${summaryPageSize}, pageNumber: ${summaryPageNumber}, sortType: ${analyticsConstants.websiteEngagementSortTypes.dateAsc}, autoRange: true) {
          statusCode
          errorMessage
          data {
            totalItems
            items {
              dateFrom
              pageViewsPerSession
              avgSessionDuration
              bounceRate
            }
          }
        }
        summary: websiteEngagementSummary(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, autoRange: true) {
          statusCode
          errorMessage
          data {
            pageViewsPerSession
            pageViewsPerSessionDelta
            avgSessionDuration
            avgSessionDurationDelta
            bounceRate
            bounceRateDelta
          }
        }
      }
      `,
      parameters
    )
  },
  getWebsiteEngagementDetailsPage (_, parameters) {
    return loadGraphQlData(
      `
      query WebsiteEngagementDetailsPage($accountId: Int!, $dateFrom: DateTime!, $dateTo: DateTime!, $pageSize: Int!, $pageNumber: Int!, $sortType: Int!) {
        detailedData: websiteEngagement(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: $pageSize, pageNumber: $pageNumber, sortType: $sortType) {
          statusCode
          errorMessage
          data {
            totalItems
            items {
              dateFrom
              pageViewsPerSession
              avgSessionDuration
              bounceRate
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getMobileEngagementGraphAndSummary (_, parameters) {
    return loadGraphQlData(
      `
      query MobileEngagementGraphAndSummary($accountId: Int!, $dateFrom: DateTime!, $dateTo: DateTime!) {
        graph: mobileEngagement(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: ${summaryPageSize}, pageNumber: ${summaryPageNumber}, sortType: ${analyticsConstants.websiteEngagementSortTypes.dateAsc}, autoRange: true) {
          statusCode
          errorMessage
          data {
            totalItems
            items {
              dateFrom
              pageViewsPerSession
              avgSessionDuration
              bounceRate
            }
          }
        }
        summary: mobileEngagementSummary(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, autoRange: true) {
          statusCode
          errorMessage
          data {
            pageViewsPerSession
            pageViewsPerSessionDelta
            avgSessionDuration
            avgSessionDurationDelta
            bounceRate
            bounceRateDelta
          }
        }
      }
      `,
      parameters
    )
  },
  getMobileEngagementDetailsPage (_, parameters) {
    return loadGraphQlData(
      `
      query MobileEngagementDetailsPage($accountId: Int!, $dateFrom: DateTime!, $dateTo: DateTime!, $pageSize: Int!, $pageNumber: Int!, $sortType: Int!) {
        detailedData: mobileEngagement(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: $pageSize, pageNumber: $pageNumber, sortType: $sortType) {
          statusCode
          errorMessage
          data {
            totalItems
            items {
              dateFrom
              pageViewsPerSession
              avgSessionDuration
              bounceRate
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getDesktopEngagementGraphAndSummary (_, parameters) {
    return loadGraphQlData(
      `
      query DesktopEngagementGraphAndSummary($accountId: Int!, $dateFrom: DateTime!, $dateTo: DateTime!) {
        graph: desktopEngagement(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: ${summaryPageSize}, pageNumber: ${summaryPageNumber}, sortType: ${analyticsConstants.websiteEngagementSortTypes.dateAsc}, autoRange: true) {
          statusCode
          errorMessage
          data {
            totalItems
            items {
              dateFrom
              pageViewsPerSession
              avgSessionDuration
              bounceRate
            }
          }
        }
        summary: desktopEngagementSummary(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, autoRange: true) {
          statusCode
          errorMessage
          data {
            pageViewsPerSession
            pageViewsPerSessionDelta
            avgSessionDuration
            avgSessionDurationDelta
            bounceRate
            bounceRateDelta
          }
        }
      }
      `,
      parameters
    )
  },
  getDesktopEngagementDetailsPage (_, parameters) {
    return loadGraphQlData(
      `
      query DesktopEngagementDetailsPage($accountId: Int!, $dateFrom: DateTime!, $dateTo: DateTime!, $pageSize: Int!, $pageNumber: Int!, $sortType: Int!) {
        detailedData: desktopEngagement(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: $pageSize, pageNumber: $pageNumber, sortType: $sortType) {
          statusCode
          errorMessage
          data {
            totalItems
            items {
              dateFrom
              pageViewsPerSession
              avgSessionDuration
              bounceRate
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getPaidSearchGraphAndSummary (_, parameters) {
    return loadGraphQlData(
      `
      query PaidSearchGraphAndSummary($accountId: Int!, $dateFrom: DateTime!, $dateTo: DateTime!) {
        graph: paidSearch(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: ${summaryPageSize}, pageNumber: ${summaryPageNumber}, sortType: ${analyticsConstants.paidSearchSortTypes.dateAsc}, autoRange: true) {
          statusCode
          errorMessage
          data {
            totalItems
            items {
              dateFrom
              sessions
              totalLeads
              conversionRate
              spend
              costPerLead
            }
          }
        }
        summary: paidSearchSummary(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo) {
          statusCode
          errorMessage
          data {
            sessions
            sessionsDelta
            totalLeads
            totalLeadsDelta
            conversionRate
            conversionRateDelta
            spend
            spendDelta
            costPerLead
            costPerLeadDelta
          }
        }
      }
      `,
      parameters
    )
  },
  getPaidSearchDetailsPage (_, parameters) {
    return loadGraphQlData(
      `
      query PaidSearchDetailsPage($accountId: Int!, $dateFrom: DateTime!, $dateTo: DateTime!, $pageSize: Int!, $pageNumber: Int!, $sortType: Int!, ) {
        detailedData: paidSearch(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: $pageSize, pageNumber: $pageNumber, sortType: $sortType) {
          statusCode
          errorMessage
          data {
            totalItems
            items {
              dateFrom
              spend
              impressions
              clicks
              costPerClick
              clickThroughRate
              sessions
              totalLeads
              conversionRate
              costPerLead
              formLeads
              smsLeads
              phoneLeads
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getVehiclesGraphAndSummary (_, parameters) {
    return loadGraphQlData(
      `
      query VehiclesGraphAndSummary($accountId: Int!, $dateFrom: DateTime!, $dateTo: DateTime!) {
        graph: vehiclesTimelineSummary(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo) {
          statusCode
          errorMessage
          data {
            dateFrom
            dateTo
            pageViews
            formLeads
          }
        }
        summary: vehiclesSummary(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo) {
          statusCode
          errorMessage
          data {
            pageViews
            pageViewsDelta
            formLeads
            formLeadsDelta
          }
        }
      }
      `,
      parameters
    )
  },
  getVehiclesDetailsPage (_, parameters) {
    return loadGraphQlData(
      `
      query VehiclesDetailsPage($accountId: Int!, $dateFrom: DateTime!, $dateTo: DateTime!, $pageSize: Int!, $pageNumber: Int!, $sortType: Int!) {
        detailedData: vehicles(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: $pageSize, pageNumber: $pageNumber, sortType: $sortType) {
          statusCode
          errorMessage
          data {
            totalItems
            items {
              year
              make
              model
              vin
              pageViews
              avgTimeOnPage
              timeOnPage
              formLeads
              pagePath
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getChannelSegmentsReport (_, parameters) {
    return loadGraphQlData(
      `
      query ChannelSegmentsReport($accountId: Int!, $dateFrom: DateTime!, $dateTo: DateTime!, $sortType: Int!) {
        graph: channelSegmentsTimelineSummary(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo) {
          statusCode
          errorMessage
          data {
            dateFrom
            sessions
            pageViews
            sessionDuration
            totalLeads
          }
        }
        summary: channelSegmentsSummary(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, autoRange: true) {
          statusCode
          errorMessage
          data {
            sessions
            sessionsDelta
            pageViews
            pageViewsDelta
            avgSessionDuration
            avgSessionDurationDelta
            totalLeads
            totalLeadsDelta
          }
        }
        detailedData: channelSegments(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, sortType: $sortType) {
          data {
            items {
             channelGrouping,
             sessions,
             users,
             pageViews,
             avgSessionDuration,
             totalLeads,
             percentNewSessions,
             pageViewsPerSession,
             sessionDuration,
             formLeads,
             smsLeads,
             phoneLeads
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getTrafficSourcesGraph (_, parameters) {
    return loadGraphQlData(
      `
      query TrafficSourcesGraph($accountId: Int!, $dateFrom: DateTime!, $dateTo: DateTime!, $count: Int!) {
        graph: trafficSourcesGraph(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, count: $count) {
          data {
            sessionsbysource {
              sourcechannelname
              count
            }
            totalleadsbysource {
              sourcechannelname
              count
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getTrafficSourcesDetailsPage (_, parameters) {
    return loadGraphQlData(
      `
      query TrafficSourcesDetailsPage($accountId: Int!, $dateFrom: DateTime!, $dateTo: DateTime!, $pageSize: Int!, $pageNumber: Int!, $sortType: Int!) {
        detailedData: trafficSources(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: $pageSize, pageNumber: $pageNumber, sortType: $sortType) {
          data {
            totalItems
            items {
              source
              channelGrouping
              sessions
              users
              pageViews
              avgSessionDuration
              totalLeads
              percentNewSessions
              pageViewsPerSession
              sessionDuration
              formLeads
              smsLeads
              phoneLeads
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getDisplayGraphAndSummary (_, parameters) {
    return loadGraphQlData(
      `
      query DisplayGraphAndSummary($accountId: Int!, $dateFrom: DateTime!, $dateTo: DateTime!) {
        graph: display(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: ${summaryPageSize}, pageNumber: ${summaryPageNumber}, sortType: ${analyticsConstants.displaySortTypes.dateAsc} autoRange: true) {
          statusCode
          errorMessage
          data {
            totalItems
            items {
              dateFrom
              sessions
              impressions
              clicks
              spend
              costPerClick
            }
          }
        }
        summary: displaySummary(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo) {
          statusCode
          errorMessage
          data {
            sessions
            sessionsDelta
            impressions
            impressionsDelta
            clicks
            clicksDelta
            spend
            spendDelta
            costPerClick
            costPerClickDelta
          }
        }
      }
      `,
      parameters
    )
  },
  getDisplayDetailsPage (_, parameters) {
    return loadGraphQlData(
      `
      query DisplayDetailsPage($accountId: Int!, $dateFrom: DateTime!, $dateTo: DateTime!, $pageSize: Int!, $pageNumber: Int!, $sortType: Int!, ) {
        detailedData: display(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: $pageSize, pageNumber: $pageNumber, sortType: $sortType) {
          statusCode
          errorMessage
          data {
            totalItems
            items {
              dateFrom
              spend
              impressions
              clicks
              costPerClick
              clickThroughRate
              sessions
              totalLeads
              conversionRate
              costPerLead
              formLeads
              smsLeads
              phoneLeads
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getRemarketingGraphAndSummary (_, parameters) {
    return loadGraphQlData(
      `
      query RemarketingGraphAndSummary($accountId: Int!, $dateFrom: DateTime!, $dateTo: DateTime!) {
        graph: remarketing(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: ${summaryPageSize}, pageNumber: ${summaryPageNumber}, sortType: ${analyticsConstants.remarketingSortTypes.dateAsc} autoRange: true) {
          statusCode
          errorMessage
          data {
            totalItems
            items {
              dateFrom
              sessions
              impressions
              clicks
              spend
              costPerClick
            }
          }
        }
        summary: remarketingSummary(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo) {
          statusCode
          errorMessage
          data {
            sessions
            sessionsDelta
            impressions
            impressionsDelta
            clicks
            clicksDelta
            spend
            spendDelta
            costPerClick
            costPerClickDelta
          }
        }
      }
      `,
      parameters
    )
  },
  getRemarketingDetailsPage (_, parameters) {
    return loadGraphQlData(
      `
      query RemarketingDetailsPage($accountId: Int!, $dateFrom: DateTime!, $dateTo: DateTime!, $pageSize: Int!, $pageNumber: Int!, $sortType: Int!, ) {
        detailedData: remarketing(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: $pageSize, pageNumber: $pageNumber, sortType: $sortType) {
          statusCode
          errorMessage
          data {
            totalItems
            items {
              dateFrom
              spend
              impressions
              clicks
              costPerClick
              clickThroughRate
              sessions
              totalLeads
              conversionRate
              costPerLead
              formLeads
              smsLeads
              phoneLeads
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getPAGReport (_, parameters) {
    return loadGraphQlData(
      `
      query PAGGroupReport($dateFrom: DateTime!, $dateTo: DateTime!, $sortType: Int!) {
        pagGroupReport(dateFrom: $dateFrom, dateTo: $dateTo, sortType: $sortType) {
          statusCode
          errorMessage
          data {
            totalItems
            items {
              accountId
              accountName
              newUsers
              sessions
              mobileSessions
              totalFormLeads
              totalLeads
              phoneLeads
              convRatePerSession
              organicSearchVisits
              organicSearchFormLeads
              paidSearchSpend
              paidSearchSessions
              paidSearchFormLeads
              paidSearchPhoneLeads
              paidSearchTotalLeads
              paidSearchConvRatePerSession
              paidSearchCostPerLead
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getGroupDashboardSummaryCards (_, parameters) {
    return loadGraphQlData(
      `
      query GroupDashboardSummaryCards($reportGroupId: String!, $dateFrom: DateTime!, $dateTo: DateTime!) {
        groupWebsiteOverviewSummary(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo) {
          data {
            sessions
            sessionsDelta
            totalLeads
            totalLeadsDelta
          }
        }
        groupWebsiteEngagementSummary(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo) {
          data {
            bounceRate
            bounceRateDelta
            avgSessionDuration
            avgSessionDurationDelta
          }
        }
      }
      `,
      parameters
    )
  },
  getGroupDashboardDeviceSessionsReport (_, parameters) {
    return loadGraphQlData(
      `
      query GroupDashboardDeviceSessionsReport($reportGroupId: String!, $dateFrom: DateTime!, $dateTo: DateTime!) {
        groupMobileOverviewSummary(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo) {
          data {
            sessions
          }
        }
        groupDesktopOverviewSummary(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo) {
          data {
            sessions
          }
        }
      }
      `,
      parameters
    )
  },
  getGroupDashboardDeviceSessionsTimeline (_, parameters) {
    return loadGraphQlData(
      `
      query GroupDashboardDeviceSessionsTimeline($reportGroupId: String!, $dateFrom: DateTime!, $dateTo: DateTime!) {
        groupMobileOverviewTimelineSummary(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo) {
          data {
            items {
              dateFrom
              sessions
            }
          }
        }
        groupDesktopOverviewTimelineSummary(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo) {
          data {
            items {
              dateFrom
              sessions
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getGroupDashboardTrafficSourcesTimeline (_, parameters) {
    return loadGraphQlData(
      `
      query GroupDashboardTrafficSourcesTimeline($reportGroupId: String!, $dateFrom: DateTime!, $dateTo: DateTime!) {
        groupTrafficSourcesTimeline(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo) {
          data {
            source
            channelGrouping
            items {
              dateFrom
              dateTo
              sessions
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getGroupWebsiteOverviewGraphAndSummary (_, parameters) {
    return loadGraphQlData(
      `
      query GroupWebsiteOverviewGraphAndSummary($reportGroupId: String!, $dateFrom: DateTime!, $dateTo: DateTime!) {
        graph: groupWebsiteOverviewTimelineSummary(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo) {
          statusCode
          errorMessage
          data {
            items {
              dateFrom
              sessions
              pageViews
              totalLeads
              convRatePerSession
            }
          }
        }
        summary: groupWebsiteOverviewSummary(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo) {
          statusCode
          errorMessage
          data {
            sessions
            sessionsDelta
            pageViews
            pageViewsDelta
            totalLeads
            totalLeadsDelta
            convRatePerSession
            convRatePerSessionDelta
          }
        }
      }
      `,
      parameters
    )
  },
  getGroupWebsiteOverviewDetailsPage (_, parameters) {
    return loadGraphQlData(
      `
      query GroupWebsiteOverviewDetailsPage($reportGroupId: String!, $dateFrom: DateTime!, $dateTo: DateTime!, $pageSize: Int!, $pageNumber: Int!, $sortType: Int!) {
        detailedData: groupWebsiteOverviewByAccount(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: $pageSize, pageNumber: $pageNumber, sortType: $sortType) {
          statusCode
          errorMessage
          data {
            totalItems
            items {
              account {
                accountId
                accountName
              }
              convRatePerSession
              formLeads
              pageViews
              phoneLeads
              sessions
              smsLeads
              totalLeads
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getGroupMobileOverviewGraphAndSummary (_, parameters) {
    return loadGraphQlData(
      `
      query GroupWebsiteOverviewMobileGraphAndSummary($reportGroupId: String!, $dateFrom: DateTime!, $dateTo: DateTime!) {
        graph: groupMobileOverviewTimelineSummary(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo) {
          statusCode
          errorMessage
          data {
            items {
              dateFrom
              sessions
              pageViews
              totalLeads
              convRatePerSession
            }
          }
        }
        summary: groupMobileOverviewSummary(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo) {
          statusCode
          errorMessage
          data {
             sessions
             sessionsDelta
             pageViews
             pageViewsDelta
             totalLeads
             totalLeadsDelta
             convRatePerSession
             convRatePerSessionDelta
          }
        }
      }
      `,
      parameters
    )
  },
  getGroupMobileOverviewDetailsPage (_, parameters) {
    return loadGraphQlData(
      `
      query GroupWebsiteOverviewMobileDetailsPage($reportGroupId: String!, $dateFrom: DateTime!, $dateTo: DateTime!, $pageSize: Int!, $pageNumber: Int!, $sortType: Int!) {
        detailedData: groupMobileOverviewByAccount(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: $pageSize, pageNumber: $pageNumber, sortType: $sortType) {
          statusCode
          errorMessage
          data {
            totalItems
            items {
              account {
                accountId
                accountName
              }
              convRatePerSession
              formLeads
              pageViews
              phoneLeads
              sessions
              totalLeads
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getGroupDesktopOverviewGraphAndSummary (_, parameters) {
    return loadGraphQlData(
      `
      query GroupWebsiteOverviewDesktopGraphAndSummary($reportGroupId: String!, $dateFrom: DateTime!, $dateTo: DateTime!) {
        graph: groupDesktopOverviewTimelineSummary(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo) {
          statusCode
          errorMessage
          data {
            items {
              dateFrom
              sessions
              pageViews
              totalLeads
              convRatePerSession
            }
          }
        }
        summary: groupDesktopOverviewSummary(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo) {
          statusCode
          errorMessage
          data {
             sessions
             sessionsDelta
             pageViews
             pageViewsDelta
             totalLeads
             totalLeadsDelta
             convRatePerSession
             convRatePerSessionDelta
          }
        }
      }
      `,
      parameters
    )
  },
  getGroupDesktopOverviewDetailsPage (_, parameters) {
    return loadGraphQlData(
      `
      query GroupWebsiteOverviewDesktopDetailsPage($reportGroupId: String!, $dateFrom: DateTime!, $dateTo: DateTime!, $pageSize: Int!, $pageNumber: Int!, $sortType: Int!) {
        detailedData: groupDesktopOverviewByAccount(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: $pageSize, pageNumber: $pageNumber, sortType: $sortType) {
          statusCode
          errorMessage
          data {
            totalItems
            items {
              account {
                accountId
                accountName
              }
              convRatePerSession
              formLeads
              pageViews
              phoneLeads
              sessions
              totalLeads
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getGroupWebsiteEngagementGraphAndSummary (_, parameters) {
    return loadGraphQlData(
      `
      query GroupWebsiteEngagementGraphAndSummary($reportGroupId: String!, $dateFrom: DateTime!, $dateTo: DateTime!) {
        graph: groupWebsiteEngagementTimelineSummary(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo) {
          statusCode
          errorMessage
          data {
            items {
              dateFrom
              pageViewsPerSession
              avgSessionDuration
              bounceRate
            }
          }
        }
        summary: groupWebsiteEngagementSummary(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo) {
          statusCode
          errorMessage
          data {
            pageViewsPerSession
            pageViewsPerSessionDelta
            avgSessionDuration
            avgSessionDurationDelta
            bounceRate
            bounceRateDelta
          }
        }
      }
      `,
      parameters
    )
  },
  getGroupWebsiteEngagementDetailsPage (_, parameters) {
    return loadGraphQlData(
      `
      query GroupWebsiteEngagementDetailsPage($reportGroupId: String!, $dateFrom: DateTime!, $dateTo: DateTime!, $pageSize: Int!, $pageNumber: Int!, $sortType: Int!) {
        detailedData: groupWebsiteEngagementByAccount(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: $pageSize, pageNumber: $pageNumber, sortType: $sortType) {
          statusCode
          errorMessage
          data {
            totalItems
            items {
              account {
                accountId
                accountName
              }
              pageViewsPerSession
              avgSessionDuration
              bounceRate
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getGroupMobileEngagementGraphAndSummary (_, parameters) {
    return loadGraphQlData(
      `
      query GroupWebsiteEngagementMobileGraphAndSummary($reportGroupId: String!, $dateFrom: DateTime!, $dateTo: DateTime!) {
        graph: groupMobileEngagementTimelineSummary(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo) {
          statusCode
          errorMessage
          data {
            items {
              dateFrom
              pageViewsPerSession
              avgSessionDuration
              bounceRate
            }
          }
        }
        summary: groupMobileEngagementSummary(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo) {
          statusCode
          errorMessage
          data {
            pageViewsPerSession
            pageViewsPerSessionDelta
            avgSessionDuration
            avgSessionDurationDelta
            bounceRate
            bounceRateDelta
          }
        }
      }
      `,
      parameters
    )
  },
  getGroupMobileEngagementDetailsPage (_, parameters) {
    return loadGraphQlData(
      `
      query GroupWebsiteEngagementMobileDetailsPage($reportGroupId: String!, $dateFrom: DateTime!, $dateTo: DateTime!, $pageSize: Int!, $pageNumber: Int!, $sortType: Int!) {
        detailedData: groupMobileEngagementByAccount(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: $pageSize, pageNumber: $pageNumber, sortType: $sortType) {
          statusCode
          errorMessage
          data {
            totalItems
            items {
              account {
                accountId
                accountName
              }
              pageViewsPerSession
              avgSessionDuration
              bounceRate
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getGroupDesktopEngagementGraphAndSummary (_, parameters) {
    return loadGraphQlData(
      `
      query GroupWebsiteEngagementDesktopGraphAndSummary($reportGroupId: String!, $dateFrom: DateTime!, $dateTo: DateTime!) {
        graph: groupDesktopEngagementTimelineSummary(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo) {
          statusCode
          errorMessage
          data {
            items {
              dateFrom
              pageViewsPerSession
              avgSessionDuration
              bounceRate
            }
          }
        }
        summary: groupDesktopEngagementSummary(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo) {
          statusCode
          errorMessage
          data {
            pageViewsPerSession
            pageViewsPerSessionDelta
            avgSessionDuration
            avgSessionDurationDelta
            bounceRate
            bounceRateDelta
          }
        }
      }
      `,
      parameters
    )
  },
  getGroupDesktopEngagementDetailsPage (_, parameters) {
    return loadGraphQlData(
      `
      query GroupWebsiteEngagementDesktopDetailsPage($reportGroupId: String!, $dateFrom: DateTime!, $dateTo: DateTime!, $pageSize: Int!, $pageNumber: Int!, $sortType: Int!) {
        detailedData: groupDesktopEngagementByAccount(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: $pageSize, pageNumber: $pageNumber, sortType: $sortType) {
          statusCode
          errorMessage
          data {
            totalItems
            items {
              account {
                accountId
                accountName
              }
              pageViewsPerSession
              avgSessionDuration
              bounceRate
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getGroupVehiclesGraphAndSummary (_, parameters) {
    return loadGraphQlData(
      `
      query GroupVehiclesGraphAndSummary($reportGroupId: String!, $dateFrom: DateTime!, $dateTo: DateTime!) {
        graph: groupVehiclesTimelineSummary(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo) {
          statusCode
          errorMessage
          data {
            dateFrom
            dateTo
            pageViews
            formLeads
          }
        }
        summary: groupVehiclesSummary(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo) {
          statusCode
          errorMessage
          data {
            pageViews
            pageViewsDelta
            formLeads
            formLeadsDelta
          }
        }
      }
      `,
      parameters
    )
  },
  getGroupVehiclesDetailsPageByAccount (_, parameters) {
    return loadGraphQlData(
      `
      query GroupVehiclesDetailsPageByAccount($reportGroupId: String!, $dateFrom: DateTime!, $dateTo: DateTime!, $pageSize: Int!, $pageNumber: Int!, $sortType: Int!) {
        detailedData: groupVehiclesByAccount(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: $pageSize, pageNumber: $pageNumber, sortType: $sortType) {
          statusCode
          errorMessage
          data {
            totalItems
            items {
              account {
                accountId
                accountName
              }
              pageViews
              avgTimeOnPage
              timeOnPage
              formLeads
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getGroupVehiclesDetailsPageByVehicle (_, parameters) {
    return loadGraphQlData(
      `
      query GroupVehiclesDetailsPageByVehicle($reportGroupId: String!, $dateFrom: DateTime!, $dateTo: DateTime!, $pageSize: Int!, $pageNumber: Int!, $sortType: Int!) {
        detailedData: groupVehiclesByVehicle(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: $pageSize, pageNumber: $pageNumber, sortType: $sortType) {
          statusCode
          errorMessage
          data {
            totalItems
            items {
              year
              make
              model
              vin
              pageViews
              avgTimeOnPage
              timeOnPage
              formLeads
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getGroupChannelSegmentsReport (_, parameters) {
    return loadGraphQlData(
      `
      query GroupChannelSegments($reportGroupId: String!, $dateFrom: DateTime!, $dateTo: DateTime!, $sortType: Int!) {
        graph: groupChannelSegmentsTimelineSummary(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo) {
          statusCode
          errorMessage
          data {
            dateFrom
            sessions
            pageViews
            sessionDuration
            totalLeads
          }
        }
        summary: groupChannelSegmentsSummary(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo) {
          statusCode
          errorMessage
          data {
            sessions
            sessionsDelta
            pageViews
            pageViewsDelta
            avgSessionDuration
            avgSessionDurationDelta
            totalLeads
            totalLeadsDelta
          }
        }
        detailedData: groupChannelSegmentsBySegmet(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo, sortType: $sortType) {
          statusCode
          errorMessage
          data {
            items {
              channelGrouping
              sessions
              users
              pageViews
              avgSessionDuration
              totalLeads
              percentNewSessions
              pageViewsPerSession
              sessionDuration
              formLeads
              smsLeads
              phoneLeads
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getGroupChannelSegmentsByAccount (_, parameters) {
    return loadGraphQlData(
      `
      query GroupChannelSegmentsByAccount($reportGroupId: String!, $dateFrom: DateTime!, $dateTo: DateTime!, $pageSize: Int!, $pageNumber: Int!, $sortType: Int!) {
        detailedData: groupChannelSegmentsByAccount(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: $pageSize, pageNumber: $pageNumber, sortType: $sortType) {
          statusCode
          errorMessage
          data {
            totalItems
            items {
              account {
                accountId
                accountName
              }
              avgSessionDuration
              dateFrom
              dateTo
              formLeads
              pageViews
              pageViewsPerSession
              percentNewSessions
              phoneLeads
              sessionDuration
              sessions
              smsLeads
              totalLeads
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getGroupTrafficSourcesGraph (_, parameters) {
    return loadGraphQlData(
      `
      query GroupTrafficSourcesGraph($reportGroupId: String!, $dateFrom: DateTime!, $dateTo: DateTime!, $count: Int!) {
        graph: groupTrafficSourcesGraph(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo, count: $count) {
          data {
            sessionsbysource {
              sourcechannelname
              count
            }
            totalleadsbysource {
              sourcechannelname
              count
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getGroupTrafficSourcesDetailsPageBySource (_, parameters) {
    return loadGraphQlData(
      `
      query GroupTrafficSourcesDetailsPageBySource($reportGroupId: String!, $dateFrom: DateTime!, $dateTo: DateTime!, $pageSize: Int!, $pageNumber: Int!, $sortType: Int!) {
        detailedData: groupTrafficSourcesBySource(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: $pageSize, pageNumber: $pageNumber, sortType: $sortType) {
          statusCode
          errorMessage
          data {
            totalItems
            items {
              source
              channelGrouping
              sessions
              users
              pageViews
              avgSessionDuration
              totalLeads
              percentNewSessions
              pageViewsPerSession
              sessionDuration
              formLeads
              smsLeads
              phoneLeads
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getGroupTrafficSourcesDetailsPageByAccount (_, parameters) {
    return loadGraphQlData(
      `
      query GroupTrafficSourcesDetailsPageByAccount($reportGroupId: String!, $dateFrom: DateTime!, $dateTo: DateTime!, $pageSize: Int!, $pageNumber: Int!, $sortType: Int!) {
        detailedData: groupTrafficSourcesByAccount(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: $pageSize, pageNumber: $pageNumber, sortType: $sortType) {
          statusCode
          errorMessage
          data {
            totalItems
            items {
              account {
                accountId
                accountName
              }
              sessions
              users
              pageViews
              avgSessionDuration
              totalLeads
              percentNewSessions
              pageViewsPerSession
              sessionDuration
              formLeads
              smsLeads
              phoneLeads
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getGroupPaidSearchGraphAndSummary (_, parameters) {
    return loadGraphQlData(
      `
      query GroupPaidSearchGraphAndSummary($reportGroupId: String!, $dateFrom: DateTime!, $dateTo: DateTime!) {
        graph: groupPaidSearchTimelineSummary(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo) {
          statusCode
          errorMessage
          data {
            items {
              dateFrom
              sessions
              totalLeads
              conversionRate
              spend
              costPerLead
            }
          }
        }
        summary: groupPaidSearchSummary(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo) {
          statusCode
          errorMessage
          data {
            sessions
            sessionsDelta
            totalLeads
            totalLeadsDelta
            conversionRate
            conversionRateDelta
            spend
            spendDelta
            costPerLead
            costPerLeadDelta
          }
        }
      }
      `,
      parameters
    )
  },
  getGroupPaidSearchDetailsPage (_, parameters) {
    return loadGraphQlData(
      `
      query GroupPaidSearchDetailsPage($reportGroupId: String!, $dateFrom: DateTime!, $dateTo: DateTime!, $pageSize: Int!, $pageNumber: Int!, $sortType: Int!, ) {
        detailedData: groupPaidSearchByAccount(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: $pageSize, pageNumber: $pageNumber, sortType: $sortType) {
          statusCode
          errorMessage
          data {
            totalItems
            items {
              account {
                accountId
                accountName
              }
              spend
              impressions
              clicks
              costPerClick
              clickThroughRate
              sessions
              totalLeads
              conversionRate
              costPerLead
              formLeads
              smsLeads
              phoneLeads
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getGroupDisplayGraphAndSummary (_, parameters) {
    return loadGraphQlData(
      `
      query GroupDisplayGraphAndSummary($reportGroupId: String!, $dateFrom: DateTime!, $dateTo: DateTime!) {
        graph: groupDisplayTimelineSummary(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo) {
          statusCode
          errorMessage
          data {
            items {
              dateFrom
              sessions
              impressions
              clicks
              spend
              costPerClick
            }
          }
        }
        summary: groupDisplaySummary(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo) {
          statusCode
          errorMessage
          data {
            sessions
            sessionsDelta
            impressions
            impressionsDelta
            clicks
            clicksDelta
            spend
            spendDelta
            costPerClick
            costPerClickDelta
          }
        }
      }
      `,
      parameters
    )
  },
  getGroupDisplayDetailsPage (_, parameters) {
    return loadGraphQlData(
      `
      query GroupDisplayDetailsPage($reportGroupId: String!, $dateFrom: DateTime!, $dateTo: DateTime!, $pageSize: Int!, $pageNumber: Int!, $sortType: Int!, ) {
        detailedData: groupDisplayByAccount(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: $pageSize, pageNumber: $pageNumber, sortType: $sortType) {
          statusCode
          errorMessage
          data {
            totalItems
            items {
              account {
                accountId
                accountName
              }
              spend
              impressions
              clicks
              costPerClick
              clickThroughRate
              sessions
              totalLeads
              conversionRate
              costPerLead
              formLeads
              smsLeads
              phoneLeads
            }
          }
        }
      }
      `,
      parameters
    )
  },
  getGroupRemarketingGraphAndSummary (_, parameters) {
    return loadGraphQlData(
      `
      query GroupRemarketingGraphAndSummary($reportGroupId: String!, $dateFrom: DateTime!, $dateTo: DateTime!) {
        graph: groupRemarketingTimelineSummary(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo) {
          statusCode
          errorMessage
          data {
            items {
              dateFrom
              sessions
              impressions
              clicks
              spend
              costPerClick
            }
          }
        }
        summary: groupRemarketingSummary(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo) {
          statusCode
          errorMessage
          data {
            sessions
            sessionsDelta
            impressions
            impressionsDelta
            clicks
            clicksDelta
            spend
            spendDelta
            costPerClick
            costPerClickDelta
          }
        }
      }
      `,
      parameters
    )
  },
  getGroupRemarketingDetailsPage (_, parameters) {
    return loadGraphQlData(
      `
      query GroupRemarketingDetailsPage($reportGroupId: String!, $dateFrom: DateTime!, $dateTo: DateTime!, $pageSize: Int!, $pageNumber: Int!, $sortType: Int!, ) {
        detailedData: groupRemarketingByAccount(reportGroupId: $reportGroupId, dateFrom: $dateFrom, dateTo: $dateTo, pageSize: $pageSize, pageNumber: $pageNumber, sortType: $sortType) {
          statusCode
          errorMessage
          data {
            totalItems
            items {
              account {
                accountId
                accountName
              }
              spend
              impressions
              clicks
              costPerClick
              clickThroughRate
              sessions
              totalLeads
              conversionRate
              costPerLead
              formLeads
              smsLeads
              phoneLeads
            }
          }
        }
      }
      `,
      parameters
    )
  },

  getAccountsList (_, parameters) {
    return axios.get(`${analyticsConstants.googleAnalytics4ApiEndpointsBasePath}accounts/`, { params: parameters.filters })
  },
  getReportGroups (_, parameters) {
    return axios.get(`${analyticsConstants.googleAnalytics4ApiEndpointsBasePath}reportgroups`, { params: parameters.params })
  },
  getReportGroup (_, parameters) {
    return axios.get(`${analyticsConstants.googleAnalytics4ApiEndpointsBasePath}reportgroups/${parameters.id}`)
  },
  getUserActivityLogs (_, parameters) {
    return axios.get(`${analyticsConstants.googleAnalytics4ApiEndpointsBasePath}useractivity`, { params: parameters.filter })
  },
  getUserActivityLogDetails (_, parameters) {
    return axios.get(`${analyticsConstants.googleAnalytics4ApiEndpointsBasePath}useractivity/${parameters.logId}/details`)
  },
  createReportGroup (_, parameters) {
    return axios.post(`${analyticsConstants.googleAnalytics4ApiEndpointsBasePath}reportgroups`, JSON.parse(JSON.stringify(parameters.reportGroup)))
  },
  updateReportGroup (_, parameters) {
    return axios.post(`${analyticsConstants.googleAnalytics4ApiEndpointsBasePath}reportgroups/update`, JSON.parse(JSON.stringify(parameters.reportGroup)))
  },
  deleteReportGroup (_, parameters) {
    return axios.post(`${analyticsConstants.googleAnalytics4ApiEndpointsBasePath}reportgroups/delete/${parameters.id}`)
  },
  async getAccessibleReportGroups (_, parameters) {
    return axios.get(`${analyticsConstants.googleAnalytics4ApiEndpointsBasePath}reportgroups/accessiblegroups/${parameters.accountId}`)
  },
  async getAccountNameFromReportGroup (_, parameters) {
    return axios.get(`${analyticsConstants.googleAnalytics4ApiEndpointsBasePath}reportgroups/${parameters.reportGroupId}/accountName/${parameters.accountId}`)
  },
  async getReportAccountsFromReportGroup (_, parameters) {
    return axios.get(`${analyticsConstants.googleAnalytics4ApiEndpointsBasePath}reportgroups/${parameters.reportGroupId}/reportaccounts`)
  },

  getDefaultViewId (_, parameters) {
    return axios.get(`${analyticsConstants.googleAnalytics4ApiEndpointsBasePath}accounts/${parameters.accountId}/defaultviewid`)
  },
  updateDefaultViewId (_, parameters) {
    return axios.post(`${analyticsConstants.googleAnalytics4ApiEndpointsBasePath}accounts/${parameters.accountId}/defaultviewid/update`, JSON.parse(JSON.stringify(parameters)))
  },

  doStandardAccountAnalyticRebuild (_, parameters) {
    return axios.post(`${analyticsConstants.googleAnalytics4ApiEndpointsBasePath}statistic/rebuild_standard`, parameters)
  },
  doCustomAccountAnalyticRebuild (_, parameters) {
    return axios.post(`${analyticsConstants.googleAnalytics4ApiEndpointsBasePath}statistic/rebuild_custom`, parameters)
  },
  doCustomAccountGoogleAdsRebuild (_, parameters) {
    return axios.post(`${analyticsConstants.googleAnalytics4ApiEndpointsBasePath}statistic/rebuild_account_ads`, parameters)
  },
  doAllAccountsGoogleAdsRebuild (_, parameters) {
    return axios.post(`${analyticsConstants.googleAnalytics4ApiEndpointsBasePath}statistic/rebuild_all_accounts_ads`, parameters)
  },
  doCustomGroupAnalyticsRebuild (_, parameters) {
    return axios.post(`${analyticsConstants.googleAnalytics4ApiEndpointsBasePath}statistic/rebuild_custom_report_group`, parameters)
  }
}

const loadGraphQlData = async function (query, variables) {
  const apiResponse = await axios.post(
    graphQlEndpoint,
    {
      query: query,
      variables: variables
    }
  )
  // Nullable empty object when api returns errors
  return ((apiResponse || {}).data || {
    data: analyticsNullableReport
  }).data
}
const analyticsNullableReport = {
  summary: {
    data: {
      clicks: 0,
      clicksDelta: null,
      costPerClick: 0,
      costPerClickDelta: null,
      impressions: 0,
      impressionsDelta: null,
      sessions: 0,
      sessionsDelta: null,
      spend: 0,
      spendDelta: null
    }
  },
  table: {
    data: {
      items: [],
      totalItems: 0
    }
  },
  graph: {
    data: (() => {
      let data = []
      data.items = []
      data.totalItems = 0
      return data
    })()
  },
  detailedData: {
    data: {
      items: [],
      totalItems: 0
    }
  },
  websiteOverviewSummary: {
    data: {
      items: [],
      totalItems: 0
    }
  },
  websiteEngagementSummary: {
    data: {
      items: [],
      totalItems: 0
    }
  },
  trafficSourcesTimeline: {
    data: []
  },
  mobileOverviewSummary: {
    data: {
      items: [],
      totalItems: 0
    }
  },
  desktopOverviewSummary: {
    data: {
      items: [],
      totalItems: 0
    }
  },
  desktopOverview: {
    data: {
      items: [],
      totalItems: 0
    }
  },
  mobileOverview: {
    data: {
      items: [],
      totalItems: 0
    }
  }
}
export default {
  namespaced: true,
  state: state,
  getters: getters,
  mutations: mutations,
  actions: actions
}
