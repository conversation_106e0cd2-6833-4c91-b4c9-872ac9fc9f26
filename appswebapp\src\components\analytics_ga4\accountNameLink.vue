<template>
  <div class="account-name-link">
    <template v-if="disabled"><slot></slot></template>
    <a v-else @click="onClick"><slot></slot></a>
  </div>
</template>

<script>
export default {
  name: 'account-name-link',
  props: {
    disabled: { type: Boolean, required: false }
  },
  methods: {
    onClick () {
      this.$emit('click')
    }
  }
}
</script>

<style scoped lang="scss">
  .account-name-link  a {
    color: #d30f17;
    cursor: pointer;
  }

  .account-name-link a:hover {
    color: #960b11;
  }
</style>
