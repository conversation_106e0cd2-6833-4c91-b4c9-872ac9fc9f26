import analyticsConstants from './constants'
import dateHelper from '@/plugins/locale/date'
import { ObjectSchema } from './../common/objectHelpers'
import QueryStringHelper from './../common/queryStringHelper'

const convertDatesToRange = function ({ dayFormatted, rangeType }) {
  const dayDate = dateHelper.getDateFromFormattedDay(dayFormatted)
  const range = {
    year: dayDate.getFullYear(),
    quarter: 0,
    month: 0,
    label: ''
  }

  switch (rangeType) {
    case analyticsConstants.rangeTypes.month:
      const monthNumber = dayDate.getMonth() + 1
      const monthOption = analyticsConstants.optionsMonth.find(x => x.value === monthNumber)

      range.month = monthNumber
      range.label = `${monthOption.text}, ${range.year}`
      break
    case analyticsConstants.rangeTypes.quarter:
      const quarterNumber = Math.floor(dayDate.getMonth() / 3 + 1)
      const quarterOption = analyticsConstants.optionsQuarter.find(x => x.value === quarterNumber)

      range.quarter = quarterNumber
      range.label = `${quarterOption.text}, ${range.year}`
      break
    default:
      range.label = `${range.year}`
      break
  }

  return range
}

const convertRangeToDates = function ({ year, quarter, month }) {
  const dates = {
    dateFrom: null,
    dateTo: null,
    rangeType: analyticsConstants.rangeTypes.undefined
  }

  if (month > 0) {
    dates.rangeType = analyticsConstants.rangeTypes.month
    dates.dateFrom = new Date(year, month - 1, 1)
    dates.dateTo = new Date(year, month, 1)
  } else if (quarter > 0) {
    dates.rangeType = analyticsConstants.rangeTypes.quarter
    dates.dateFrom = new Date(year, quarter * 3 - 3, 1)
    dates.dateTo = new Date(year, quarter * 3, 1)
  } else if (year > 0) {
    dates.rangeType = analyticsConstants.rangeTypes.year
    dates.dateFrom = new Date(year, 0, 1)
    dates.dateTo = new Date(year + 1, 0, 1)
  }

  dates.dateFrom = dateHelper.getDayFormatted(dates.dateFrom)
  dates.dateTo = dateHelper.getDayFormatted(dates.dateTo)

  return dates
}

const getMoreDetailedRangeType = function (rangeType) {
  let moreDetailedRangeType = analyticsConstants.rangeTypes.day

  switch (rangeType) {
    case analyticsConstants.rangeTypes.year:
      moreDetailedRangeType = analyticsConstants.rangeTypes.quarter
      break
    case analyticsConstants.rangeTypes.quarter:
      moreDetailedRangeType = analyticsConstants.rangeTypes.month
      break
  }

  return moreDetailedRangeType
}

const getFilterManager = function (rawSchema) {
  const filterSchema = new ObjectSchema(rawSchema)
  const defaultFilter = filterSchema.getObject()
  const queryHelper = new QueryStringHelper(filterSchema)

  return {
    schema: filterSchema,
    defaultValue: defaultFilter,
    urlHelper: queryHelper
  }
}

export default {
  convertDatesToRange: convertDatesToRange,
  convertRangeToDates: convertRangeToDates,
  getMoreDetailedRangeType: getMoreDetailedRangeType,
  getFilterManager: getFilterManager
}
