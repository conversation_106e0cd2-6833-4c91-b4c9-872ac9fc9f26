 <template>
  <div v-if="ready">
    <div class="media align-items-center py-3 mb-3">
      <img :src="getVehiclePhoto" :alt="getVehicleAlt" class="d-block ui-w-80 ui-bordered">
      <div class="media-body ml-4">
        <h4 class="font-weight-bold mb-2">{{vehicle.year}} {{vehicle.make}} {{vehicle.model}}</h4>
        <h5 class="font-weight-bold mb-2">{{vehicle.trim}} <span class="text-muted"> Stock# {{vehicle.stockNumber}}</span></h5>
      </div>
    </div>
    <b-alert class="text-center" variant="warning" show v-if="isEBay">Saving changes on this page will update your vehicle for all websites and exports. Click "Revise eBay" when done saving changes</b-alert>
    <b-tabs class="nav-tabs-top nav-responsive-sm" v-model="tabIndex" no-fade>

      <b-tab title="Description" >

        <status-section/>

        <vehicle-overview-section/>

        <truck-engine-section/>

        <truck-transmission-axles-section/>

        <truck-miscellaneous-section/>

        <rv-camper-details-section/>

        <boat-details-section/>

        <snow-details-section/>

        <colors-section/>

        <warranty-section/>

        <certification-section/>

        <vehicle-history-report/>

        <packages-and-options-section/>

        <features-checklist-section/>

        <feature-and-specifications/>

        <vehicle-condition-section/>

        <vehicle-history-section/>

        <tires-and-wheels/>

      </b-tab>

      <b-tab title="Pricing">
        <pricing-section></pricing-section>
      </b-tab>

      <b-tab title="Photos & Video">
        <b-card-body>
         <photos-section/>
         <video-section/>
       </b-card-body>
     </b-tab>

     <b-tab title="Merchandising">
       <merchandising-section></merchandising-section>
       <vehicle-description-text-section></vehicle-description-text-section>
     </b-tab>
   </b-tabs>

 </div>
</template>

<!-- Page -->
<style src="@/vendor/styles/pages/products.scss" lang="scss"></style>

<script>
// ----------Vehicle Description----------
import { mapGetters } from 'vuex'
import detailsSection from '@/components/details/detailsSection'
import statusSection from '@/components/details/sections/statusSection'
import vehicleOverviewSection from '@/components/details/sections/vehicleOverview/vehicleOverviewSection'
import colorsSection from '@/components/details/sections/colorsSection'
import warrantySection from '@/components/details/sections/warrantySection'
import certificationSection from '@/components/details/sections/certificationSection'
import standardFeaturesChecklistSection from '@/components/details/sections/standardFeaturesChecklistSection'
import vehicleConditionSection from '@/components/details/sections/vehicleConditionSection'
import vehicleHistorySection from '@/components/details/sections/vehicleHistorySection'
import tiresAndWheels from '@/components/details/sections/tiresAndWheels'
import packagesAndOptions from '@/components/details/sections/OEM/OEMPackagesAndOptionsSection'
import vehicleHistoryReport from '@/components/details/sections/vehicleHistoryReportSection'

import truckEngineSection from '../../components/details/sections/truckEngineSection'
import rvCamperDetailsSection from '../../components/details/sections/rvCamperDetailsSection'
import boatDetails from '../../components/details/sections/boatDetails'
import snowmobileDetails from '../../components/details/sections/snowmobileDetails'
import OEMFeaturesAndSpecifications from '../../components/details/sections/OEM/OEMFeaturesAndSpecifications'
// ----------END--------------

// ----------TRUCK------------
import truckTransmissionAxlesSection from '../../components/details/sections/truckTransmissionAxlesSection'
import truckMiscellaneousSection from '../../components/details/sections/truckMiscellaneousSection'
// ----------END--------------

// ----------pricing----------
import pricingSection from '@/components/details/sections/pricing/pricingSection'
// ----------END--------------

// ----------Merchandising-----
import merchandisingSection from '@/components/details/sections/merchandisingSection'
import vehicleDescriptionTextSection from '@/components/details/sections/vehicleDescriptionTextSection'
// ----------END--------------

// ----------Photo And Video---
import videoSection from '../../components/details/sections/photoAndVideo/videoSection'
import photosSection from '../../components/details/sections/photoAndVideo/photosSection'
import VehicleDifferenceBuilder from '../../shared/details/vehicleDifferenceBuilder'
// ----------END---------------

export default {
  name: 'inventoryEdit',
  metaInfo: {
    title: 'Inventory Edit'
  },
  components: {
    'details-section': detailsSection,
    'status-section': statusSection,
    'vehicle-overview-section': vehicleOverviewSection,
    'colors-section': colorsSection,
    'warranty-section': warrantySection,
    'certification-section': certificationSection,
    'features-checklist-section': standardFeaturesChecklistSection,
    'vehicle-condition-section': vehicleConditionSection,
    'vehicle-history-section': vehicleHistorySection,
    'tires-and-wheels': tiresAndWheels,
    'packages-and-options-section': packagesAndOptions,
    'vehicle-history-report': vehicleHistoryReport,
    'pricing-section': pricingSection,
    'merchandising-section': merchandisingSection,
    'vehicle-description-text-section': vehicleDescriptionTextSection,
    'truck-engine-section': truckEngineSection,
    'truck-transmission-axles-section': truckTransmissionAxlesSection,
    'truck-miscellaneous-section': truckMiscellaneousSection,
    'rv-camper-details-section': rvCamperDetailsSection,
    'boat-details-section': boatDetails,
    'snow-details-section': snowmobileDetails,
    'video-section': videoSection,
    'photos-section': photosSection,
    'feature-and-specifications': OEMFeaturesAndSpecifications
  },
  props: {
    accountId: Number,
    vin: String,
    isEBay: Boolean
  },
  data () {
    return {
      ready: false,
      currentTabIndex: 0,
      sections: {
        condition: null,
        VehicleHistory: null,
        TireAndWheels: null,
        OEM: null,
        AudioAndVideo: null,
        PowerEquipment: null,
        InteriorFeatures: null,
        ExteriorFeatures: null,
        SafetyFeatures: null,
        LuxuryFeatures: null,
        TruckAttributes: null,
        RVDetails: null,
        BoatDetails: null,
        ExteriorDescription: null,
        MotorcycleUpgrades: null,
        NotesForThisVehicle: null
      },
      itemData: {
        // Draggable Options
        draggableOptions: {
          animation: 150,
          handle: '.product-image-move'
        }
      }
    }
  },
  computed: {
    ...mapGetters('details', ['vehicle', 'vehicleOriginal', 'accountSettings']),
    ...mapGetters('editSectionController', ['isEditAvailable']),
    getVehiclePhoto () {
      let photoCandidate = ((this.actualPhotos.photoItems || [])[0] || {}).photo107

      if (!photoCandidate) {
        photoCandidate = (this.vehicle.photos.presentationPhoto || {}).photo107
      }

      return photoCandidate + '?q=' + this.actualPhotos.dateTimeEdited
    },
    actualPhotos () {
      return this.vehicle.photos.actualPhotos || {}
    },
    getVehicleAlt () {
      return this.accountSettings.dealerInformation.companyName + ' Vehicle'
    },
    tabIndex: {
      get () {
        return this.currentTabIndex
      },
      set (value) {
        let oldValue = this.currentTabIndex
        if (value === oldValue) {
          return
        }

        if (!this.isEditAvailable) {
          if (VehicleDifferenceBuilder.isVehicleChanged(this.vehicleOriginal, this.vehicle)) {
            this.$nextTick(() => {
              this.currentTabIndex = oldValue
              this.$toaster.info('Save changes before change a tab')
            })
          } else {
            this.$store.commit('editSectionController/cancelEdit')
          }
        } else {
          this.$router.push({query:
              value === 0
                ? { }
                : { tabindex: value }
          })
        }

        this.currentTabIndex = value
      }
    }
  },
  created () {
    this.currentTabIndex = +this.$route.query.tabindex || 0
    populateInitialData(this.$store, this.accountId, this.vin)
      .then(() => {
        this.ready = true
      })
  },
  destroyed () {
    this.$store.commit('categoryData/resetStore')
    this.$store.commit('editSectionController/resetStore')
  },
  watch: {
    '$route.query.tabindex': {
      deep: true,
      handler: function () {
        if (this.$route.query.tabindex && +this.$route.query.tabindex !== this.currentTabIndex) {
          this.currentTabIndex = +this.$route.query.tabindex
        }
      }
    }
  }
}

async function populateInitialData (store, accountId, vin) {
  await store.dispatch('details/populateInventoryAccountSettings', { accountId: accountId })
  let vehicleBM = await store.dispatch('details/populateBaseVehicleData', { accountId: accountId, vin: vin })

  let vehicle = vehicleBM.vehicle

  store.dispatch('categoryData/populateCategoryMakes', vehicle.vehicleType)
  store.dispatch('categoryData/populateYears')

  if (vehicle.year && vehicle.make) {
    store.dispatch('categoryData/populateCategoryModelsForMake', { make: vehicle.make, year: vehicle.year })

    if (vehicle.model) {
      store.dispatch('categoryData/populateStyles', { make: vehicle.make, model: vehicle.model, year: vehicle.year })
    }
  }

  if (vehicle.vehicleStyleId) {
    store.dispatch('categoryData/populateEngines', { vehicleStyleId: vehicle.vehicleStyleId })
  }

  if (vehicle.vehicleYearModelId) {
    store.dispatch('categoryData/populateInteriorColors', { vehicleYearModelId: vehicle.vehicleYearModelId })
    store.dispatch('categoryData/populateExteriorColors', { vehicleYearModelId: vehicle.vehicleYearModelId })
  }
}
</script>

<style>
@media (max-width: 575px) {
  .nav-responsive-sm > .nav, .nav-responsive-sm > div > .nav {
      flex-wrap: nowrap!important;
      -webkit-box-orient: horizontal;
      -webkit-box-direction: normal;
      -ms-flex-direction: row;
      flex-direction: row;
      border: 0;
      overflow-x: scroll;
      white-space: nowrap;
      -webkit-overflow-scrolling: touch;
  }
  .nav-responsive-sm > div > .nav-tabs .nav-item {
    -webkit-box-flex: 1 !important;
    -ms-flex: 1 1 auto !important;
    flex: 1 1 auto !important;
  }
}
</style>
