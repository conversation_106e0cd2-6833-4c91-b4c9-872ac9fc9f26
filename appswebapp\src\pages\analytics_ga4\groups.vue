<template>
  <div>
    <div class="d-flex align-items-center justify-content-between">
      <h4 class="d-flex justify-content-start">
        Analytics Groups
      </h4>

      <div class="d-flex justify-content-end m-4">
        <b-button @click="showModal()" variant="primary">Create a Group</b-button>
      </div>
    </div>

    <b-card no-body>
      <b-form @submit.prevent="applySearch">
          <b-card-body>
            <div class="form-row">
              <b-col lg="6">
                <b-input-group>
                  <b-form-input
                    ref="search"
                    :value="page.filter.search"
                    placeholder="Search..."
                  ></b-form-input>
                  <b-input-group-append>
                    <b-btn type="submit">Go</b-btn>
                  </b-input-group-append>
                </b-input-group>
              </b-col>
            </div>
          </b-card-body>
      </b-form>
      </b-card>

      <b-card>
        <b-table
          striped
          hover
          responsive
          :items="table.items"
          :fields="tableFields"
        >
          <template #cell(group)="{ value :{ id, name }}">
            <analytics-router-link :to="{name: 'ga4GroupAnalyticsDashboard', params: { reportGroupId: id }}">{{name}}</analytics-router-link>
          </template>
          <template #cell(actions)="data">
            <b-button @click="showModal(data.item.id)"
              size="sm"
              class="mr-1">
              Edit
            </b-button>
            <c-button
              :message="`Are you sure you want to delete this group?`"
              variant="primary"
              size="sm"
              @confirm="deleteReportGroup(data.item.id)"
            >Delete</c-button>
          </template>
        </b-table>

      <paging
        :pageNumber="page.filter.page"
        :pageSize="page.filter.pageSize"
        :totalItems="table.totalItems"
        titled
        pageSizeSelector
        @numberChanged="pageNumberChanged"
        @changePageSize="changePageSize">
      </paging>
    </b-card>

    <create-edit-modal
      :show="modal.isShown"
      :reportGroup="modal.reportGroup"
      :accountsListing="modal.accountsListing"
      :groupsListing="modal.groupsListing"
      @hide="onHideModal"
      @changeGroup="onChangeGroup"
      @sendReportGroup="onSendReportGroup"
    ></create-edit-modal>

  </div>
</template>

<script>
import groupsManagementService from '@/services/accounts/groups/GroupsManagementService'
import paging from '@/components/_shared/paging.vue'
import createEditModal from '@/components/analytics_ga4/CreateEditModal'
import Multiselect from 'vue-multiselect'
import analyticsBuilders from './../../shared/analytics/builders'
import AnalyticsRouterLink from '../../components/analytics_ga4/analyticsRouterLink'

const filterManager = analyticsBuilders.getFilterManager({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  search: { type: String, default: '' }
})

export default {
  name: 'groups',
  metaInfo: {
    title: 'Analytics Groups'
  },
  components: {
    AnalyticsRouterLink,
    paging: paging,
    multiselect: Multiselect,
    'create-edit-modal': createEditModal
  },
  created () {
    this.setFilters()
    this.getReportGroups()
  },
  data () {
    return {
      page: {
        filter: filterManager.defaultValue
      },
      table: {
        items: [],
        totalItems: 0
      },
      modal: {
        isShown: false,
        reportGroup: {},
        groupsListing: [],
        accountsListing: []
      }
    }
  },
  computed: {
    tableFields () {
      return [
        { key: 'group', label: 'Analytics Group', thStyle: { width: '40%' }, tdClass: 'groupNameTdStyle' },
        { key: 'reportAccountsCount', label: 'Accounts in View', thStyle: { width: '20%', 'text-align': 'center' }, tdClass: 'tdStyle' },
        { key: 'accessAccountsCount', label: 'Accounts with Access', thStyle: { width: '20%', 'text-align': 'center' }, tdClass: 'tdStyle' },
        { key: 'actions', label: 'Actions', thStyle: { width: '20%', 'text-align': 'end' }, tdClass: 'actionTdStyle' }
      ]
    }
  },
  methods: {
    async showModal (groupId) {
      if (groupId) {
        this.modal.reportGroup = (await this.getReportGroup(groupId)).data.model
      } else {
        this.modal.reportGroup = {}
      }
      await this.fillGroupsListing()
      await this.fillAccountsListing(this.modal.reportGroup.accountsGroupId)
      this.modal.isShown = true
    },
    onHideModal () {
      this.modal.isShown = false
    },

    onSendReportGroup (reportGroup) {
      if (reportGroup.id) {
        this.updateReportGroup(reportGroup)
      } else {
        this.createReportGroup(reportGroup)
      }
    },
    getReportGroups () {
      return this.$store.dispatch('analyticsGa4/getReportGroups', { params: this.page.filter }).then(x => {
        let result = x.data.model
        this.table.items = result.reportGroupsSummaries.map(x => {
          x.group = {
            id: x.id,
            name: x.groupName
          }
          return x
        })
        this.table.totalItems = result.totalRecords
      })
    },
    async createReportGroup (reportGroup) {
      try {
        await this.$store.dispatch('analyticsGa4/createReportGroup', {reportGroup})
        this.getReportGroups()
        this.modal.isShown = false
      } catch (err) {
        if (err.response && err.response.data && err.response.data.executionResultMessage) {
          this.$toaster.error(err.response.data.executionResultMessage, { timeout: 8000 })
        } else {
          this.$toaster.error(`Can't create report group`, { timeout: 8000 })
        }

        this.$logger.handleError(err, `Can't create report group ${reportGroup}`)
      }
    },
    updateReportGroup (reportGroup) {
      return this.$store.dispatch('analyticsGa4/updateReportGroup', {reportGroup}).then(() => {
        this.getReportGroups()
        this.modal.isShown = false
      }).catch(reason => {
        this.$toaster.exception(reason, `Can't update report group`, { timeout: 8000 })
        this.$logger.handleError(reason, `Can't update report group ${reportGroup}`)
      })
    },
    deleteReportGroup (groupId) {
      return this.$store.dispatch('analyticsGa4/deleteReportGroup', {id: groupId}).then(x => {
        this.getReportGroups()
      }).catch(reason => {
        this.$logger.handleError(reason, `Can't delete report group ${groupId}`)
      })
    },
    getReportGroup (groupId) {
      return this.$store.dispatch('analyticsGa4/getReportGroup', {id: groupId})
        .catch(reason => {
          this.$logger.handleError(reason, `Can't get report group ${groupId}`)
        })
    },
    fillGroupsListing () {
      this.modal.groupsListing = []
      return this.$store.dispatch('groups/getGroups', { params: this.page.filter })
        .then(x => {
          this.modal.groupsListing = x.data.filter(x => x.accounts && x.accounts.length > 1).map(y => ({ value: y.groupId, text: y.groupName }))
        }).catch(reason => {
          this.$logger.handleError(reason, 'Can\'t get accounts groups')
        })
    },
    fillAccountsListing (groupId) {
      this.modal.accountsListing = []
      if (groupId) {
        groupsManagementService.getGroup(groupId).then(response => {
          if (response.data && response.data.accounts) {
            this.modal.accountsListing = response.data.accounts.map(x => ({ accountId: x.accountId, accountName: x.dealershipName }))
          }
        }).catch(error => {
          this.$logger.handleError(error)
        })
      }
    },
    onChangeGroup (groupId) {
      this.fillAccountsListing(groupId)
    },
    pageNumberChanged (newPage) {
      this.page.filter.page = newPage
      this.synchronizeUrl()
    },
    changePageSize (newSize) {
      this.page.filter.pageSize = newSize
      this.page.filter.page = 1
      this.synchronizeUrl()
    },
    applySearch () {
      this.page.filter.search = this.$refs.search.$el.value
      this.page.filter.page = 1
      this.synchronizeUrl()
    },
    setFilters () {
      this.page.filter = filterManager.urlHelper.parseQueryStringToObject(this.$router)
    },
    synchronizeUrl () {
      filterManager.urlHelper.rebuildParamsInQueryString(this.$router, this.page.filter)
    }
  },
  watch: {
    '$route' (to, from) {
      this.setFilters()
      this.getReportGroups()
    }
  }
}
</script>

<style>
.groupNameTdStyle a {
  color: black;
  text-align: center;
}

.groupNameTdStyle a:hover {
  color: #3c8ae2;
}

.actionTdStyle {
   float: right;
}

.tdStyle {
   text-align: center;
}
</style>
