<template>
  <div>
    <div class="d-flex flex-row">
      <h4 class="mt-3">User Activity</h4>
      <!-- Pagination -->
      <paging
        class="d-none d-md-block p-0"
        :pageNumber="filters.page"
        :pageSize="filters.pageSize"
        :totalItems="totalItemsCount"
        @numberChanged="pageChanged"
        @changePageSize="changePageSize"
      />
    </div>
    <b-card>
      <b-form  v-on:submit.prevent="applyFilter">
        <div class="form-row">
          <b-col class="my-1" sm="4" lg="4" xl="2">
            <b-form-input
              max='200'
              v-model="filters.search"
              placeholder="Search..."
              autocomplete="off"
            >
            </b-form-input>
          </b-col>

          <b-col class="my-1" sm="4" lg="4" xl="2">
            <b-input-group class="flex-nowrap">
              <b-input-group-prepend is-text>
                <i class="ion ion-md-calendar" slot="prepend"></i>
              </b-input-group-prepend>
              <date-time-picker
                ref="timeFrom"
                v-model="filters.dateFrom"
                :options="filterTimeOptions"
                format="MM/DD/YYYY HH:mm"
                placeholder="Date From"
                className="form-control"
                @change="onTimeFromInputChange"
              />
              <b-input-group-append
                is-text
                v-show="filters.dateFrom"
                @click="filters.dateFrom = null"
              >
                <i class="ion ion-md-close"></i>
              </b-input-group-append>
            </b-input-group>
          </b-col>

          <b-col class="my-1" sm="4" lg="4" xl="2">
            <b-input-group class="flex-nowrap">
              <b-input-group-prepend is-text>
                <i class="ion ion-md-calendar" slot="prepend"></i>
              </b-input-group-prepend>
              <date-time-picker
                ref="timeTo"
                v-model="filters.dateTo"
                :options="filterTimeOptions"
                format="MM/DD/YYYY HH:mm"
                placeholder="Date To"
                className="form-control"
                @change="onTimeToInputChange"
              />
              <b-input-group-append
                is-text
                v-show="filters.dateTo"
                @click="filters.dateTo = null"
              >
                <i class="ion ion-md-close"></i>
              </b-input-group-append>
            </b-input-group>
          </b-col>
          <b-col class="my-1" sm="4" lg="4" xl="2">
            <b-form-select
              v-model="filters.action"
              :options="getActionTypeOptions"
            >
            </b-form-select>
          </b-col>
          <b-col class="my-1" sm="4" lg="4" xl="2">
            <multiSelectWithCheckboxes
              v-model='selectedUserTypes'
              :options='getUserTypeOptions'
              name="User Types"
              customMessageOfNoneSelectedItems="All User Types"
            ></multiSelectWithCheckboxes>
          </b-col>
          <b-col class="my-1" sm="4" lg="4" xl="2">
            <b-btn block  variant="primary" type="submit">Submit</b-btn>
          </b-col>
        </div>
      </b-form>
    </b-card>
    <b-card v-if="!isLoading">
      <b-table
        :items="items"
        :fields="getTableFields"
        :sort-by="tableSortBy"
        :sort-desc="tableSortDesc"
        @sort-changed="onSortChanged"
        :striped="true"
        :bordered="false"
        :no-sort-reset="true"
        :no-local-sorting="true"
        responsive
        show-empty
        class="products-table card-table"
      >
        <template #cell(manage)="data">
          <b-btn size="sm" @click="onShowDetails(data)">{{ data.detailsShowing ? 'Hide' : 'Show' }} Details</b-btn>
          <a class="btn btn-outline-secondary btn-sm" :href='getLogDetailsPath(data.item.id)' target="_blank">
            <i class="ion ion-md-open"></i>
          </a>
        </template>
        <template #row-details="data">
          <b-card>
            <log-node
              v-if='data.item.nodes'
              :data="data.item.nodes"
              :isExpandedShallow="true"
              :isExpandedDeep="false"
            />
          </b-card>
        </template>
      </b-table>
      <paging
        :pageNumber="filters.page"
        :pageSize="filters.pageSize"
        :totalItems="totalItemsCount"
        titled
        pageSizeSelector
        @numberChanged="pageChanged"
        @changePageSize="changePageSize"
      />
    </b-card>
    <loader v-else class="mt-4" size="lg"/>
  </div>
</template>

<script>
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import loader from '@/components/_shared/loader'
import multiSelectWithCheckboxes from '@/components/_shared/multiSelectWithCheckboxes'
import moment from 'moment'
import UserActivityService from '@/services/website/UserActivityService'
import {userTypes} from '@/shared/users/constants'
import { userActivitySortTypes, userActivityActionTypes } from '@/shared/website/constants'

const defaultFilters = new ObjectSchema({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  search: { type: String, default: '' },
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  sort: { type: Number, default: userActivitySortTypes.dateTimeDesc },
  action: { type: Number, default: 0 },
  userTypes: { type: String, default: `${userTypes.cpUser.value},${userTypes.adminUser.value}` }
})
const queryHelper = new QueryStringHelper(defaultFilters)

export default {
  name: 'website-user-activity',
  metaInfo: {
    title: 'User Activity'
  },
  data () {
    return {
      filters: defaultFilters.getObject(),
      items: [],
      totalItemsCount: 0,
      isLoading: true,
      filterTimeOptions: {
        autoUpdateInput: false,
        singleDatePicker: true,
        timePicker: true,
        timePicker24Hour: true,
        maxDate: new Date()
      }
    }
  },
  computed: {
    selectedUserTypes: {
      get () {
        let array = this.getIntegerArrayFromString(this.filters.userTypes)
        return array || []
      },
      set (value) {
        this.filters.userTypes = (value || []).join()
      }
    },
    getActionTypeOptions () {
      return userActivityActionTypes
    },
    getUserTypeOptions () {
      return Object.values(userTypes)
    },
    getTableFields () {
      return [
        {
          key: 'requestInformation.userName',
          label: 'User Name',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: userActivitySortTypes.userNameAsc,
          sortTypeDesc: userActivitySortTypes.userNameDesc
        },
        {
          key: 'requestInformation.user.userType',
          label: 'User Type',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: userActivitySortTypes.userTypeAsc,
          sortTypeDesc: userActivitySortTypes.userTypeDesc,
          formatter: val => (Object.values(userTypes).find(x => x.value === val) || {text: '-'}).text
        },
        {
          key: 'siteId',
          label: 'Site Id',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: userActivitySortTypes.siteIdAsc,
          sortTypeDesc: userActivitySortTypes.siteIdDesc,
          formatter: val => val || '-'
        },
        {
          key: 'siteName',
          label: 'Site Name',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: userActivitySortTypes.siteNameAsc,
          sortTypeDesc: userActivitySortTypes.siteNameDesc,
          formatter: val => val || '-'
        },
        {
          key: 'action',
          label: 'Action Name',
          tdClass: 'py-2 align-middle',
          sortable: true,
          formatter: val => (userActivityActionTypes.find(x => x.value === val) || {text: '-'}).text,
          sortTypeAsc: userActivitySortTypes.actionAsc,
          sortTypeDesc: userActivitySortTypes.actionDesc
        },
        {
          key: 'startProcessingDateTime',
          label: 'Date',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: userActivitySortTypes.dateTimeAsc,
          sortTypeDesc: userActivitySortTypes.dateTimeDesc,
          formatter: val => val ? moment(val).format('MM/DD/YYYY hh:mm:ss A') : '-'
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    refDateTimeFrom () {
      return (this.$refs.timeFrom || {}).$el || {}
    },
    refDateTimeTo () {
      return (this.$refs.timeTo || {}).$el || {}
    },
    tableSortBy () {
      const sortedColumn = this.getTableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.getTableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sortType
      } else {
        return false
      }
    }
  },
  created () {
    this.filters = queryHelper.parseQueryStringToObject(this.$router)
    this.populateData()
  },
  components: {
    'paging': () => import('@/components/_shared/paging'),
    'date-time-picker': () => import('@gravitano/vue-date-range-picker/src/components/DateRangePicker'),
    'loader': loader,
    'multiSelectWithCheckboxes': multiSelectWithCheckboxes,
    'log-node': () => import('@/components/_shared/logItemNode.vue')
  },
  methods: {
    pageChanged (newPage) {
      this.filters.page = newPage
      this.synchronizeUrlAndReload()
    },
    changePageSize (newSize) {
      this.filters.pageSize = newSize
      this.filters.page = 1
      this.synchronizeUrlAndReload()
    },
    onSortChanged (value) {
      const sortingColumn = this.getTableFields.find(x => x.key === value.sortBy)
      if (value.sortDesc) {
        this.filters.sort = sortingColumn.sortTypeDesc
      } else {
        this.filters.sort = sortingColumn.sortTypeAsc
      }
      this.populateData()
    },
    onTimeFromInputChange (newVal) {
      this.refDateTimeFrom.value = newVal || this.filters.dateFrom || null
    },
    onTimeToInputChange (newVal) {
      this.refDateTimeTo.value = newVal || this.filters.dateTo || null
    },
    applyFilter () {
      this.filters.page = 1
      this.synchronizeUrlAndReload()
    },
    synchronizeUrlAndReload () {
      this.isLoading = false
      queryHelper.rebuildParamsInQueryString(this.$router, this.filters)
      this.populateData()
    },
    populateData () {
      UserActivityService.getUserActivityListing(this.filters).then(res => {
        this.items = res.data.items
        this.totalItemsCount = res.data.totalItemsCount
      }).catch(ex => {
        this.$logger.handleError(ex, 'Exception occurred on api calling')
      }).finally(() => {
        this.isLoading = false
      })
    },
    onShowDetails (data) {
      if (data.item.nodes) {
        data.toggleDetails()
      } else {
        UserActivityService.getUserActivityDetails(data.item.id).then(res => {
          this.$set(data.item, 'nodes', { nodes: res.data.details })

          data.toggleDetails()
        }).catch(ex => {
          this.$toaster.exception(ex, 'Failed on getting User Activity details')
          this.$logger.handleError(ex, 'Exception occurred on api calling')
        })
      }
    },
    getLogDetailsPath (id) {
      return `/website/useractivity/${id}/details`
    },
    getIntegerArrayFromString (str) {
      if (!str || !str.trim()) {
        return []
      }
      let array = []
      for (let numberStr of str.split(',')) {
        let number = parseInt(numberStr)
        if (!isNaN(number)) {
          array.push(number)
        }
      }
      return array
    }
  }
}
</script>
