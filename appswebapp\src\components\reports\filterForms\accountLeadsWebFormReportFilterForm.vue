<template>
  <div>
    <b-form @submit.prevent="generate">
      <div class="leads-webform-report-filters-container">
        <div class="my-2 leads-webform-report-filters-search-item">
          <b-input-group>
            <b-form-input list="search-suggestions" @input.native="onSearchInput" v-model="copyFilters.search" placeholder="Search..."></b-form-input>
            <b-input-group-append is-text>
              <b-form-checkbox v-model="copyFilters.includeRelations" :disabled="!isRelatedFilterOptionEnabled">Show Related</b-form-checkbox>
            </b-input-group-append>
          </b-input-group>
          <datalist id="search-suggestions">
            <option v-for="opt in getSuggestions" :key="opt.value" :value="opt.value">{{opt.text}}</option>
          </datalist>
        </div>
        <div class="my-2 leads-webform-report-filters-button-item d-flex flex-row">
          <div class="w-100">
            <l-button class="w-100" :loading="isGenerationPerformed" variant="primary" type="submit">Generate</l-button>
          </div>
          <b-overlay variant="white" :show="!csvData.length > 0" class="w-100 ml-1" opacity="0.3">
            <template #overlay>
              <div></div>
            </template>
            <download-csv
              class="btn btn-secondary text-center w-100"
              :data="csvData"
              :fields="csvFields"
              :labels="csvLabels"
              :advanceOptions="{skipEmptyLines: 'greedy'}"
              :name="reportFileName"
            >
              Export CSV
            </download-csv>
          </b-overlay>
        </div>
      </div>
    </b-form>
  </div>
</template>

<script>
import globals from '@/globals'
import searchFilterSuggestionsMixin from './searchFilterSuggestionsMixin'

export default {
  name: 'account-leads-webform-report-filter-form',
  props: {
    filters: { type: Object, required: true },
    csvData: { type: Array, required: true },
    csvFields: { type: Array, required: true },
    csvLabels: { type: Object, required: true },
    isGenerationPerformed: { type: Boolean, required: true }
  },
  data () {
    return {
      reportFileName: 'leads_webform_report.csv',
      copyFilters: globals().getClonedValue(this.filters),
      isReady: false
    }
  },
  mixins: [searchFilterSuggestionsMixin],
  computed: {
    isRelatedFilterOptionEnabled () {
      return !!this.copyFilters.search
    }
  },
  methods: {
    generate () {
      if (this.validate()) {
        this.$emit('generate', this.copyFilters)
      }
    },
    validate () {
      if (!this.copyFilters.search) {
        this.$toaster.error('Please specify some filters for the report')
        return false
      }
      return true
    }
  }
}
</script>

<style>
  .leads-webform-report-filters-container {
    display: flex;
    flex-direction: row;
    flex-shrink: 2;
    flex-wrap: wrap;
  }
  .leads-webform-report-filters-search-item {
    padding-right: 2px;
    padding-left: 2px;
    flex-basis: 32%;
  }

  .leads-webform-report-filters-button-item {
    padding-right: 2px;
    padding-left: 2px;
    margin-left: auto;
    flex-basis: 20%;
  }

  @media (1300px <= width < 1745px) {
    .leads-webform-report-filters-search-item {
      flex-basis: 60%;
    }
    .leads-webform-report-filters-button-item {
      flex-basis: 30%;
    }
  }

  @media (800px <= width < 1300px) {
    .leads-webform-report-filters-search-item {
      flex-basis: 50%;
    }
    .leads-webform-report-filters-button-item {
      flex-basis: 50%;
    }
  }

  @media (width < 800px) {
    .leads-webform-report-filters-search-item {
      flex-basis: 100%;
    }
    .leads-webform-report-filters-button-item {
      margin-left: 0;
      flex-basis: 100%;
    }
  }
</style>
