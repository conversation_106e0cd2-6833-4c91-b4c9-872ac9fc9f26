<template>
  <div v-if="model.callAudioUrl" class="leads-voice-message">
    <audio controls>
      <source :src="model.callAudioUrl">
      Your browser does not support the audio element.
    </audio>
  </div>
  <div v-else>
    Record was not stored
  </div>
</template>

<script>
export default {
  name: 'leads-voice-type-message',
  props: {
    model: { type: Object, required: true }
  }
}
</script>

<style scoped>
@media (max-width: 447px) {
  .leads-voice-message, audio {
    width: 170px;
  }
}
</style>
