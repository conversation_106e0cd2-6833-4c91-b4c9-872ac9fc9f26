<template>
  <b-table
    v-if="isGenerationFinished"
    :items="items"
    :fields="fields"
    show-empty
    responsive
    bordered
    hover
    striped
  >
    <template #cell(manageLink)="data">
      <b-button
        v-if="data.item.communicationManageUrl"
        variant="default btn-xs icon-btn md-btn-flat"
        :href="data.item.communicationManageUrl"
        v-b-tooltip.hover
        :title="data.item.communicationManageUrl">
        <i class="ion ion-md-eye"></i>
      </b-button>
    </template>
  </b-table>
</template>

<script>
export default {
  name: 'contacts-report-listing',
  props: {
    items: {type: Array, required: true},
    fields: {type: Array, required: true},
    isGenerationFinished: {type: Boolean, required: true}
  }
}
</script>
