<template>
  <div v-if="!isLoading && !isExceptionOccurred">
    <h4>eBay Settings</h4>
    <b-card v-if="accountSettings">
      <eBayLoginAuthorizationSection v-if="eBayUserInfo && !isLoadingEBayUserInfo" :eBayUserInfo="eBayUserInfo" @refresh="populateEBayUserInfo"/>
      <div class="my-4 py-4" v-else>
        <loader size="md"/>
      </div>
      <globalSettingsDefaultsSection :settings="accountSettings.GlobalDefaultSettings" @refresh="populateAccountSettings"/>
      <paypalSettingsSection :settings="accountSettings.PayPalSettings" @refresh="populateAccountSettings"/>
      <shippingSettingsSection :settings="accountSettings.ShippingSettings" @refresh="populateAccountSettings"/>
      <depositSettingsSection :settings="accountSettings.DepositSettings" @refresh="populateAccountSettings"/>
      <adminSettingsSection v-if="hasToManagedAdminSettings" :settings="getAdminSettings()" @refresh="populateAccountSettings"/>
    </b-card>
  </div>
  <div v-else-if="isLoading && !isExceptionOccurred" class="pt-5">
    <loader size="lg"/>
  </div>
  <div v-else>
    <error-alert/>
  </div>
</template>

<script>
import eBayLoginAuthorizationSection from '@/components/eBay/settings/eBayLoginAuthorizationSection'
import globalSettingsDefaultsSection from '@/components/eBay/settings/globalSettingsDefaultsSection'
import paypalSettingsSection from '@/components/eBay/settings/paypalSettingsSection'
import shippingSettingsSection from '@/components/eBay/settings/shippingSettingsSection'
import depositSettingsSection from '@/components/eBay/settings/depositSettingsSection'
import adminSettingsSection from '@/components/eBay/settings/adminSettingsSection'
import loader from '@/components/_shared/loader'
import permissions from '@/shared/common/permissions'
import { mapGetters } from 'vuex'

export default {
  name: 'ebay-settings',
  metaInfo: {
    title: 'eBay Settings'
  },
  props: {
    accountId: { type: Number, required: true }
  },
  data () {
    return {
      accountSettings: null,
      eBayUserInfo: null,
      isLoading: true,
      isLoadingEBayUserInfo: true,
      isExceptionOccurred: false
    }
  },
  created () {
    this.populateData().then(() => {
      this.isLoading = false
    })
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {}
    },
    hasToManagedAdminSettings () {
      return this.user.hasPermissions && this.user.hasPermissions(permissions.EBayFullAccess)
    }
  },
  components: {
    eBayLoginAuthorizationSection,
    globalSettingsDefaultsSection,
    paypalSettingsSection,
    shippingSettingsSection,
    depositSettingsSection,
    adminSettingsSection,
    loader
  },
  methods: {
    async populateAccountSettings () {
      this.$store.dispatch('eBay/getAccountSettings', this.accountId).then(res => {
        this.accountSettings = {}
        this.accountSettings.AccountId = res.data.AccountId
        this.accountSettings.IsManagedByApps = res.data.IsManagedByApps
        this.accountSettings.DefaultSettings = res.data.DefaultSettings || {}
        this.accountSettings.GlobalDefaultSettings = res.data.GlobalDefaultSettings || {}
        this.accountSettings.PayPalSettings = res.data.PayPalSettings || {}
        this.accountSettings.ShippingSettings = res.data.ShippingSettings || {}
        this.accountSettings.DepositSettings = res.data.DepositSettings || {}
      }).catch(ex => {
        this.isExceptionOccurred = true
        this.$toaster.error('Something went wrong! Please reload page')
        this.$logger.handleError(ex, 'Cannot get eBay account settings')
      })
    },
    async populateEBayUserInfo () {
      this.$store.dispatch('eBay/getEBayUserInfo', { accountId: this.accountId, contactId: this.user.contactId || 0 }).then(res => {
        this.eBayUserInfo = res.data
      }).catch(ex => {
        this.isExceptionOccurred = true
        this.$toaster.error('Something went wrong! Please reload page')
        this.$logger.handleError(ex, 'Cannot get eBay user info')
      }).finally(() => {
        this.isLoadingEBayUserInfo = false
      })
    },
    getAdminSettings () {
      let adminSettings = {
        IsManagedByApps: this.accountSettings.IsManagedByApps
      }
      return adminSettings
    },
    async populateData () {
      await this.populateAccountSettings()
      await this.populateEBayUserInfo()
    }
  }
}
</script>
