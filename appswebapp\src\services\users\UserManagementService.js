import BaseService from '@/services/BaseService'

class UserManagementService extends BaseService {
  getUsersListing (filters) {
    return this.axios.get('/api/usersmanagement/listing', {params: filters})
  };
  createUser (user) {
    return this.axios.post('/api/usersmanagement/create', user)
  };
  updateUser (id, user) {
    return this.axios.post(`/api/usersmanagement/${id}/update`, user)
  };
  deleteUser (id) {
    return this.axios.post(`/api/usersmanagement/${id}/delete`)
  };
  synchronizeAll (syncModel) {
    return this.axios.post('/api/usersmanagement/synchronize', syncModel)
  };
  synchronizeUser (syncModel) {
    return this.axios.post('/api/usersmanagement/synchronizeuser', syncModel)
  };
  getSynchronizeStatus (taskId) {
    return this.axios.get(`/api/usersmanagement/synchronize/${taskId}/status`)
  };
  getSearchUsersSuggestions (filters) {
    return this.axios.get('/api/usersmanagement/suggestions/users', {params: filters})
  };
  getSynUsersSuggestions (filters) {
    return this.axios.get('/api/usersmanagement/suggestions/sync', {params: filters})
  };
  getUserRoles () {
    return this.axios.get('/api/usersmanagement/roles')
  };
  createNewUserRole (newUserRole) {
    return this.axios.post('/api/usersmanagement/roles/create', newUserRole)
  };
  updateUserRole (userRole) {
    return this.axios.post('/api/usersmanagement/roles/update', userRole)
  };
  deleteUserRole (id) {
    return this.axios.post(`/api/usersmanagement/roles/${id}/delete`)
  };
  updateUserDisplaySettings (displaySettings) {
    return this.axios.post(`/api/usersmanagement/display_settings/update`, displaySettings)
  };
}

export default new UserManagementService()
