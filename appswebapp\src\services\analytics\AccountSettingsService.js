import BaseService from '@/services/BaseService'
import analyticsConstants from '@/shared/analytics/constants'

class AccountSettingsService extends BaseService {
  constructor (apiPathBase) {
    super()
    this.apiPathBase = apiPathBase
  }
  getAccountSettings (accountId) {
    return this.axios.get(`${this.apiPathBase}accounts/${accountId}`)
  };
  updateAccountSettings (accountId, settings) {
    return this.axios.post(`${this.apiPathBase}accounts/${accountId}/update`, settings)
  };
  getGoogleSignUrl (accountId, params) {
    return this.axios.get(`${this.apiPathBase}${accountId}/authorization/sign_in_url`, {params: params})
  };
  revokeGoogleAuthorizationToken (accountId) {
    return this.axios.post(`${this.apiPathBase}${accountId}/authorization/revoke_token`)
  }
}

const gaUniversalAccountSettingsService = new AccountSettingsService(
  analyticsConstants.googleAnalyticsDefaultApiEndpointsBasePath
)
const ga4AccountSettingsService = new AccountSettingsService(analyticsConstants.googleAnalytics4ApiEndpointsBasePath)

export {
  gaUniversalAccountSettingsService,
  ga4AccountSettingsService
}
