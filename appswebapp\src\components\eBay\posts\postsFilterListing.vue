<template>
  <b-card no-body>
    <active-listing v-if="postsOptions.active.key === this.filters.tab" :items="items" :sortType="filters.sort" @onSortChange="onSortChange"></active-listing>
    <scheduled-listing v-else-if="postsOptions.scheduled.key === this.filters.tab" :items="items" :sortType="filters.sort" @onSortChange="onSortChange"></scheduled-listing>
    <ended-listing v-else-if="postsOptions.ended.key === this.filters.tab" :items="items" :sortType="filters.sort" @onSortChange="onSortChange"></ended-listing>
  </b-card>
</template>

<script>
import constants from '@/shared/ebay/constants'
import activeListing from './activeListing'
import endedListingVue from './endedListing.vue'
import scheduledListing from './scheduledListing'

export default {
  name: 'ebay-posts-filter-listing',
  props: {
    filters: { type: Object, required: true },
    items: { type: Array, required: true }
  },
  data () {
    return {
      postsOptions: constants.postsOptions
    }
  },
  components: {
    'active-listing': activeListing,
    'scheduled-listing': scheduledListing,
    'ended-listing': endedListingVue
  },
  methods: {
    onSortChange (sort) {
      this.$emit('onSortChange', sort)
    }
  }
}
</script>
