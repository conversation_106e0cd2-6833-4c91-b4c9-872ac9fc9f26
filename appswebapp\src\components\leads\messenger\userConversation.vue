<template>
  <div>
    <div class="leads-user-info row py-2 d-flex align-items-center">
      <div class='cols-1 pl-4' :class="leadsToggleBtnClass">
        <b-button size="sm" @click="onShowFilterListingSection" variant="outline-secondary icon-btn btn-round"><i class="ion ion-ios-more m-0"></i><span class="sr-only">More</span></b-button>
      </div>
      <div  class='col text-truncate float-left d-flex flex-column pl-4 text-muted'>
        <b v-if="hasAccountsLeadsToDisplay" class="text-dark">{{currentConversation.accountDesc}}</b>
        <template v-if="conversationDetailModel.userConversation">
          <span v-html="conversationDetailModel.userConversation.fullName"></span>
          <span v-if="conversationDetailModel.userConversation.phone">{{conversationDetailModel.userConversation.phone}}</span>
          <span v-if="conversationDetailModel.userConversation.email">{{conversationDetailModel.userConversation.email}}</span>
        </template>
      </div>
      <div class="col pr-4 align-middle" v-if="hasLeadsManageCommunications && conversationDetailModel.userConversation">
        <b-avatar
          button
          class="float-right"
          size="md"
          @click="$router.push({name: 'leads-contact', params: {accountId: currentConversation.accountId, conversationId: currentConversation.conversationId}})">
        </b-avatar>
      </div>
    </div>
    <div class="leads-user-chat border-top d-flex flex-column w-100">
      <div class="leads-user-chat-field border-bottom" @scroll="onScrollMessage">
        <p>
        <message v-for='conversation in conversationDetailModel.conversationDetails' @resend="resendNotification" @archive="archive" @downloadCreditApp="downloadCreditApp" :key='conversation.id' :conversation='conversation'/>
        </p>
      </div>
      <b-input-group class="leads-chat-footer border-left" size="sm" v-if="hasSendMessage || hasUserAccessToSendEmail">
        <b-input-group-prepend v-if="hasUserAccessToSendEmail">
          <b-btn variant="primary" @click="showSendEmailModal()" class="mr-2">
            Email Reply <i class='ion ion-ios-mail opacity-100'></i>
          </b-btn>
        </b-input-group-prepend>
        <b-form-textarea v-if="hasSendMessage" max-rows="2" v-model="textToSend" no-resize></b-form-textarea>
        <b-input-group-append v-if="hasSendMessage">
          <b-btn @click="sendText" variant="primary">
            Send
          </b-btn>
        </b-input-group-append>
      </b-input-group>
      <div v-else-if="currentConversation.conversationId" class="my-3 text-center">
        <span class="text-muted h5">Message sending is not available</span>
      </div>
    </div>
    <email-reply ref="emailReply"/>
  </div>
</template>

<script>
import ConversationService from '@/services/leads/ConversationService'
import permissions from '@/shared/common/permissions'
import applicationTypes from '@/shared/common/applicationTypes'
import signalRLeadsMessengerConnection from '@/signalR/leadsMessengerHub'
import { mapGetters } from 'vuex'

export default {
  name: 'leads-user-conversation',
  props: {
    leadsToggleBtnClass: { type: String, default: 'leads-toggle-btn-hide-mode' },
    currentConversation: { type: Object, default: () => {} },
    conversationDetailModel: { type: Object, default: () => {} }
  },
  data () {
    return {
      accountId: +this.$route.params.accountId,
      textToSend: '',
      isConnectionManuallyClosed: false
    }
  },
  components: {
    'message': () => import('./message'),
    'email-reply': () => import('./emailReply')
  },
  async created () {
    this.connectToSignalR()
  },
  beforeDestroy () {
    this.disconnectFromSignalR()
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {
        canManageAccountApplicationType: () => false,
        hasWritePermissions: () => false,
        getAllowedAccountIdsToDisplayLeads: []
      }
    },
    hasSendMessage () {
      return this.user.hasWritePermissions(this.currentConversation.accountId, permissions.LeadsSendMessages, applicationTypes.AppsLeads.Id, permissions.LeadsFullAccess) &&
        !this.conversationDetailModel.isClosed && this.conversationDetailModel.isMessagingAllowed &&
        this.conversationDetailModel.userConversation && this.conversationDetailModel.userConversation.phone
    },
    hasAccountsLeadsToDisplay () {
      return this.user.accountId === this.accountId && this.user.getAllowedAccountIdsToDisplayLeads.length > 0
    },
    hasLeadsManageCommunications () {
      return this.user && this.user.hasPermissions && this.user.hasPermissions(permissions.LeadsManageCommunications)
    },
    hasUserAccessToSendEmail () {
      return this.conversationDetailModel.userConversation && this.conversationDetailModel.userConversation.email &&
        this.user.hasWritePermissions(this.currentConversation.accountId, permissions.LeadsSendMessages, applicationTypes.AppsLeads.Id, permissions.LeadsFullAccess)
    },
    getSenderExtraOptionClass () {
      if (this.isShowExtraOption) {
        return 'sender-extra-option-show-mode'
      }

      return 'sender-extra-option-hide-mode'
    }
  },
  methods: {
    onShowFilterListingSection () {
      this.$emit('showFilterListingSection')
    },
    archive (id) {
      ConversationService.archiveConversationDetails(this.currentConversation.accountId, this.currentConversation.conversationId, id).then(res => {
        this.$toaster.success('Message Successfully Archived')
      }).catch(ex => {
        this.$toaster.error('Cannot archive message')
        this.$logger.handleError(ex, `Cannot archive message with conversationId: ${this.currentConversation.conversationId} and conversationDetailsId: ${id} for accountId: ${this.currentConversation.accountId}`)
      }).finally(() => {
        this.loadContent()
      })
    },
    downloadCreditApp (params) {
      this.$store.dispatch('leads/getCreditAppPdfFile', {accountId: this.currentConversation.accountId, conversationId: this.currentConversation.conversationId, conversationDetailsId: params.id, password: params.password}).then(res => {
        var fileURL = window.URL.createObjectURL(new Blob([res.data], { type: 'application/pdf' }))
        var fileLink = document.createElement('a')

        fileLink.href = fileURL
        fileLink.setAttribute('download', 'creditApp.pdf')
        document.body.appendChild(fileLink)

        fileLink.click()
      }).catch(ex => {
        if (ex.response && ex.response.status === 403) {
          this.$toaster.error('Invalid Credit Application password')
        } else {
          this.$toaster.error(`Something went wrong`)
          this.$logger.handleError(ex, 'Cannot download credit app pdf file')
        }
      })
    },
    showSendEmailModal () {
      this.$refs.emailReply.showEmailReplyModal(
        this.currentConversation.accountId,
        this.currentConversation.conversationId,
        this.getVehicleVin(),
        this.conversationDetailModel.userConversation
      )
    },
    resendNotification (data) {
      let params = {
        accountId: this.currentConversation.accountId,
        conversationId: this.currentConversation.conversationId,
        conversationDetailsId: data.id,
        data: {
          notificationTypes: data.selectedNotificationTypes
        }
      }
      ConversationService.sendNotificationsForConversationsDetails(params).then(res => {
        this.$toaster.success('Notification Successfully Resend')
      }).catch(ex => {
        this.$toaster.error('Cannot resend notification')
        this.$logger.handleError(ex, `Cannot resend notification`, params)
      })
    },
    async sendText () {
      if (!this.textToSend.trim()) {
        this.$toaster.error('Message cannot be empty')
        return
      }
      let params = {
        accountId: this.currentConversation.accountId,
        conversationId: this.currentConversation.conversationId,
        message: {
          accountId: this.currentConversation.accountId,
          conversationId: this.currentConversation.conversationId,
          text: this.textToSend
        }
      }
      ConversationService.sendSMSFromDealer(params).then(res => {
        this.$toaster.success('Message Successfully Sent')
      }).catch(ex => {
        this.$toaster.error('Cannot send message')
        this.$logger.handleError(ex, 'Cannot send message from dealer', params)
      }).finally(() => {
        this.textToSend = ''
      })
    },
    getVehicleVin () {
      for (let conversationDetail of this.conversationDetailModel.conversationDetails) {
        if (conversationDetail.vin && conversationDetail.vin.length > 0) {
          return conversationDetail.vin
        }
      }
      return ''
    },
    loadConversationDetails (id) {
      if (this.conversationDetailModel.conversationDetails && !this.conversationDetailModel.conversationDetails.some(x => x.id === id)) {
        ConversationService.getConversationDetails(this.currentConversation.accountId, this.currentConversation.conversationId, id).then(res => {
          this.$emit('applyNewConversationDetails', res.data)
        }).catch(ex => {
          this.$toaster.error('Cannot get new message')
          this.$logger.handleError(ex, `Cannot get conversation details with conversationId: ${this.currentConversation.conversationId} and conversationDetailsId: ${id}, for accountId: ${this.currentConversation.accountId}`)
        })
      }
    },
    onScrollMessage ({ target: { scrollTop, clientHeight, scrollHeight } }) {
      if (scrollTop + scrollHeight - clientHeight === 1) {
        this.$emit('loadExtraConversationDetails')
      }
    },
    connectToSignalR () {
      signalRLeadsMessengerConnection.on('newMessageTriggered', (accountId, conversationId, conversationDetailsId) => {
        this.newMessageTriggered(accountId, conversationId, conversationDetailsId)
      })
      signalRLeadsMessengerConnection.onclose(this.onConnectionCloseFromSignalR)
      this.establishConnection()
    },
    newMessageTriggered (accountId, conversationId, conversationDetailsId) {
      if (this.currentConversation.conversationId === conversationId) {
        this.loadConversationDetails(conversationDetailsId)
        return
      }
      this.$emit('newMessage', conversationId)
    },
    onConnectionCloseFromSignalR () {
      if (!this.isConnectionManuallyClosed) {
        this.establishConnection()
      }
    },
    async establishConnection () {
      await signalRLeadsMessengerConnection.start().then(() => {
        signalRLeadsMessengerConnection.invoke('InitConnection', this.accountId).then(res => {
        }).catch(() => {
          return new Promise((resolve, reject) =>
            setTimeout(() => this.connectToSignalR().then(resolve).catch(reject), 5000))
        })
      }).catch(() => {
        return new Promise((resolve, reject) =>
          setTimeout(() => this.connectToSignalR().then(resolve).catch(reject), 5000))
      })
    },
    disconnectFromSignalR () {
      this.isConnectionManuallyClosed = true
      signalRLeadsMessengerConnection.stop()
    }
  }
}
</script>

<style scoped>
.leads-toggle-btn {
  display: none;
}

.leads-toggle-btn-hide-mode {
  display: none;
}

.leads-user-chat {
  height: calc(100vh - 315px);
  overflow: hidden;
  background: #fff;
}
.leads-user-info {
  height: 100px;
}
.leads-chat-footer {
  height: 75px;
  padding: 1rem;
}

.leads-user-chat-field {
  height: 635px;
  max-height: 100%;
  overflow: auto;
  overflow-y: scroll;
  width: 100%;
  padding: 20px;
  display: flex;
  flex-direction: column-reverse;
  background: #fff;
}

.leads-user-chat-field::-webkit-scrollbar {
  width: 5px;
}

.leads-user-chat-field::-webkit-scrollbar-track {
  background: #fdfdfd;
}

.leads-user-chat-field::-webkit-scrollbar-thumb {
  background: rgb(207, 207, 207);
}

.leads-user-chat-field::-webkit-scrollbar-thumb:hover {
  background: rgb(168, 167, 167);
}

.extra-send-option-btn-active {
  background: #818fa2;
  color: white;
}

.sender-extra-option-hide-mode {
  display: none;
}

@media (max-width: 992px) {
  .leads-toggle-btn-hide-mode {
    display: inline-block;
  }
}
.custom-loader {
  padding-top: 1.5rem;
  margin: 0;
  background: #fff;
  height: calc(100vh - 288px);
}
</style>
