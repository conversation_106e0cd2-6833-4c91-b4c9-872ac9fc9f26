<template>
  <div>
    <div>
      <h4>User Activity Details Log</h4>
    </div>
    <b-card>
      <log-node
        v-if="log"
        :data="log"
        :isExpandedShallow="true"
        :isExpandedDeep="false"
      />
    </b-card>
  </div>
</template>

<script>
export default {
  name: 'analytics-user-activity-log-details',
  metaInfo: {
    title: 'User Activity Details'
  },
  props: {
    logId: { type: String, required: true }
  },
  data () {
    return {
      log: null,
      isLoading: true
    }
  },
  components: {
    'log-node': () => import('@/components/_shared/logItemNode.vue')
  },
  created () {
    this.loadContent()
  },
  methods: {
    loadContent () {
      this.$store.dispatch('analyticsGa4/getUserActivityLogDetails', { logId: this.logId }).then(res => {
        this.log = res.data.model
      }).catch(ex => {
        this.$toaster.error('Something went wrong!')
        this.$logger.handleError(ex, 'Cannot get analytics user activity log details')
      }).finally(() => {
        this.isLoading = false
      })
    }
  }
}
</script>
