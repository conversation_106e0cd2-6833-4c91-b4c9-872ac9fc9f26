export default{
  craigslistCategoryType: [
    { value: 1, text: 'cars & trucks - by dealer' },
    { value: 2, text: 'rvs - by dealer' },
    { value: 3, text: 'heavy equipment - by dealer' },
    { value: 4, text: 'motorcycles/scooters - by dealer' },
    { value: 5, text: 'atvs, utvs, snowmobiles - by dealer' },
    { value: 6, text: 'boats - by dealer' },
    { value: 7, text: 'trailers - by dealer' }
  ],
  craigslistPostStrategyEnum: [
    { value: 0, text: 'First In, First Out' },
    { value: 1, text: 'Last In, First Out' }
  ],
  craigsListStockSearchType: [
    { value: 0, text: 'All' },
    { value: 1, text: 'Begins with' },
    { value: 2, text: 'Contains' },
    { value: 3, text: 'Does Not Begin With' },
    { value: 4, text: 'Does Not Contain' }
  ],
  bodyStyleEnum: [
    { value: 1, text: 'Convertible' },
    { value: 2, text: 'Coupe' },
    { value: 3, text: 'Hatchback' },
    { value: 4, text: 'Sedan' },
    { value: 5, text: 'SUV' },
    { value: 6, text: 'Truck' },
    { value: 7, text: 'Van' },
    { value: 8, text: 'Wagon' },
    { value: 9, text: 'Car' },
    { value: 10, text: 'SAV' },
    { value: 11, text: 'Liftback' },
    { value: 12, text: 'SAC' },
    { value: 13, text: 'Fastback' }
  ],
  vehicleTypeEnum: [
    { value: 1, text: 'Atv' },
    { value: 2, text: 'Boat' },
    { value: 3, text: 'Bus' },
    { value: 4, text: 'Motorcycle' },
    { value: 5, text: 'Passenger' },
    { value: 6, text: 'Plane' },
    { value: 7, text: 'Rv' },
    { value: 8, text: 'Scooter' },
    { value: 9, text: 'Snow' },
    { value: 10, text: 'Truck' },
    { value: 11, text: 'Misc' }
  ],
  craigsLitConditionFilterEnum: [
    { value: 0, text: 'All' },
    { value: 1, text: 'New' },
    { value: 2, text: 'Used' },
    { value: 3, text: 'Certified' }
  ],
  campaignStatusOptions: [
    { value: 1, text: 'Active' },
    { value: 2, text: 'Paused' },
    { value: 3, text: 'Active on date range' }
  ],
  daysOfWeekOptions: [
    { value: 0, text: 'Sunday' },
    { value: 1, text: 'Monday' },
    { value: 2, text: 'Tuesday' },
    { value: 3, text: 'Wednesday' },
    { value: 4, text: 'Thursday' },
    { value: 5, text: 'Friday' },
    { value: 6, text: 'Saturday' }
  ],
  daysOfWeek: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
  hoursOptions: ['12', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11'],
  timeSuffixOptions: ['AM', 'PM'],
  minutesOptions: ['00', '10', '20', '30', '40', '50']
}
