<template>
  <div class="mb-2" v-if='!isException'>
    <b-row>
      <b-col><h4 class="float-left">Leads Manager</h4></b-col>
      <b-col>
        <range-selector
                @input="onRangeChanged"
                :value="filterDateRange"
                class="button-col float-right"
        />
      </b-col>
    </b-row>
    <div>
      <paging
        class="custom-leads-tab-paging d-none d-md-block"
        :pageNumber="filter.page"
        :pageSize="filter.pageSize"
        :totalItems="total"
        @numberChanged="onPageChanged"
        @changePageSize="onPageSizeChanged" />
      <b-tabs v-model='selectedTab' class="nav-tabs-top nav-responsive-sm mt-2" no-fade>
        <b-tab v-for='tab in tabOptions' :title='tab.label' :key='tab.value'>
          <leads-manager-filter
          v-if='isFilterOptionsLoaded'
          :hasAccountsLeadsToDisplay="hasAccountsLeadsToDisplay"
          :accessibleLeadAccounts="accessibleLeadAccounts"
          :filteredAccountIds="selectedAccountIds"
          :filterOptions="filterOptions"
          :filter="filter"
          @filterChange="filterChange"
          @changeSelectedAccounts="changeSelectedAccounts"
          @applySearch="applySearch"/>
        </b-tab>
      </b-tabs>
      <b-card no-body v-if='!isLoading && total > 0'>
        <leads-manager-listing
          :hasAccountsLeadsToDisplay="hasAccountsLeadsToDisplay"
          v-if="conversationDetails"
          :items="conversationDetails"
          @deleteArchivedConversation="onDeleteArchivedConversation"
          @sortChange="sortChange"
          :type="selectedTab"
        />
        <paging
          :pageNumber="filter.page"
          :pageSize="filter.pageSize"
          :totalItems="total"
          titled
          pageSizeSelector
          @numberChanged="onPageChanged"
          @changePageSize="onPageSizeChanged" />
      </b-card>
      <div v-else-if="isLoading" class="my-5">
        <loader size="lg"/>
      </div>
      <div v-else>
        <span class="text-muted">Not Found</span>
      </div>
    </div>
  </div>
  <div v-else class="text-center mt-4">
    <span class="text-muted h4">Something went wrong. Please reload this page</span>
  </div>
</template>

<script>
import rangeSelector from '@/components/leads/rangeSelector/leadsRangeSelector'
import rangeHelper from '@/components/leads/rangeSelector/rangeHelper'
import paging from '@/components/_shared/paging.vue'
import loader from '@/components/_shared/loader'
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import leadsManagerFilter from '@/components/leads/manager/leadsManagerFilter'
import leadsManagerListing from '@/components/leads/manager/leadsManagerListing'
import ConversationService from '@/services/leads/ConversationService'
import { conversationTabTypes } from '@/shared/leads/common'
import { mapGetters } from 'vuex'

const defaultRange = rangeHelper.defaultRange.asFormattedStrings()

const defaultValues = new ObjectSchema({
  dateFrom: { type: String, default: defaultRange[0] },
  dateTo: { type: String, default: defaultRange[1] },
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  sort: { type: Number, default: 2 },
  search: { type: String, default: '' },
  communicationtypes: { type: String, default: '' },
  leadtypes: { type: String, default: '' },
  campaigntypes: { type: String, default: '' },
  contacttypes: { type: String, default: '' },
  includeAllAccounts: { type: Boolean, default: true },
  includedaccounts: { type: String, default: '' },
  excludedaccounts: { type: String, default: '' },
  tabType: { type: Number, default: 0 }
})

const queryHelper = new QueryStringHelper(defaultValues)

export default {
  name: 'leads-manager',
  metaInfo: {
    title: 'Manager'
  },
  props: {
    accountId: { type: Number, required: true }
  },
  data () {
    return {
      isException: false,
      isLoading: true,
      isFilterOptionsLoaded: false,
      filter: defaultValues.getObject(),
      filterOptions: {
        leadTypes: [],
        campaignTypes: []
      },
      conversationDetails: [],
      total: 0,
      tabOptions: Object.values(conversationTabTypes),
      selectedAccountIds: [],
      sitesInfo: {},
      accessibleLeadAccounts: []
    }
  },
  created () {
    this.filter = queryHelper.parseQueryStringToObject(this.$router)
    this.setSelectedAccountIds()
    this.populateFilterData()
    this.populateSitesInfo().then(() => {
      this.loadContent()
    })
  },
  components: {
    'range-selector': rangeSelector,
    'paging': paging,
    'leads-manager-filter': leadsManagerFilter,
    'leads-manager-listing': leadsManagerListing,
    'loader': loader
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {getAllowedAccountIdsToDisplayLeads: () => []}
    },
    hasAccountsLeadsToDisplay () {
      return this.user.accountId === this.accountId && this.user.getAllowedAccountIdsToDisplayLeads.length > 0
    },
    filterDateRange () {
      if ((this.filter || {}).dateFrom && (this.filter || {}).dateTo) {
        return [this.filter.dateFrom, this.filter.dateTo]
      }

      return null
    },
    selectedTab: {
      get () {
        if (this.filter.tabType < 0 || this.filter.tabType > 1) {
          return 0
        }

        let res = this.tabOptions.find(x => x.value === this.filter.tabType)
        if (res) {
          return res.value
        }

        return 0
      },
      set (index) {
        this.filter.tabType = index
        this.filter.page = 1
        this.synchronizeUrlAndReload()
      }
    }
  },
  methods: {
    setSelectedAccountIds () {
      if (this.hasAccountsLeadsToDisplay) {
        let allAccessibleLeadAccounts = this.getAllAccessibleLeadAccounts()
        if (this.filter.includeAllAccounts) {
          this.selectedAccountIds = []
          return
        }
        let excludedAccountIds = this.getIntegerArrayFromString(this.filter.excludedaccounts)
        if (excludedAccountIds.length > 0) {
          this.selectedAccountIds = allAccessibleLeadAccounts.filter(x => !excludedAccountIds.includes(x))
          return
        }
        let includedAccountIds = this.getIntegerArrayFromString(this.filter.includedaccounts)
        if (includedAccountIds.length > 0) {
          this.selectedAccountIds = allAccessibleLeadAccounts.filter(x => includedAccountIds.includes(x))
        }
      }
    },
    getAllAccessibleLeadAccounts () {
      let accountIds = this.user.getAllowedAccountIdsToDisplayLeads || []
      if (this.user.accountId > 0) {
        accountIds.push(this.user.accountId)
      }
      return accountIds
    },
    async onRangeChanged (rangeInfo) {
      if (this.filter.dateFrom === rangeInfo.range[0] && this.filter.dateTo === rangeInfo.range[1]) {
        return
      }
      this.filter.dateFrom = rangeInfo.range[0]
      this.filter.dateTo = rangeInfo.range[1]
      this.synchronizeUrlAndReload()
    },
    applySearch (newSearch) {
      this.filter.search = newSearch
      this.synchronizeUrlAndReload()
    },
    filterChange (newFilter) {
      this.filter = newFilter
      this.synchronizeUrlAndReload()
    },
    onPageChanged (newPage) {
      this.filter.page = newPage
      this.synchronizeUrlAndReload()
    },
    onPageSizeChanged (newSize) {
      this.filter.pageSize = newSize
      this.synchronizeUrlAndReload()
    },
    sortChange (newSort) {
      this.filter.sort = newSort
      this.loadContent()
    },
    synchronizeUrlAndReload () {
      queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.isLoading = true
      this.loadContent()
    },
    changeSelectedAccounts (newSelectedAccountIds) {
      this.selectedAccountIds = newSelectedAccountIds
      this.calculateLeadAccountsFilters()
      this.filter.page = 1
      this.isLoading = true
      this.synchronizeUrlAndReload()
      this.populateCampaignTypes()
    },
    calculateLeadAccountsFilters () {
      let allLeadAccounts = this.getAllAccessibleLeadAccounts()
      let countOfAllLeadAccounts = allLeadAccounts.length
      let countOfSelectedAccounts = this.selectedAccountIds.length
      if (countOfAllLeadAccounts === countOfSelectedAccounts || countOfSelectedAccounts === 0) {
        this.filter.includeAllAccounts = true
        this.filter.excludedaccounts = ''
        this.filter.includedaccounts = ''
        return
      }
      this.filter.includeAllAccounts = false
      if (countOfSelectedAccounts > countOfAllLeadAccounts / 2) {
        this.filter.excludedaccounts = allLeadAccounts.filter(x => !this.selectedAccountIds.includes(x)).toString()
        this.filter.includedaccounts = ''
        return
      }
      this.filter.includedaccounts = this.selectedAccountIds.toString()
      this.filter.excludedaccounts = ''
    },
    async loadContent () {
      if (!this.hasAccountsLeadsToDisplay) {
        this.filter.includeAllAccounts = false
        this.filter.includedaccounts = ''
        this.filter.excludedaccounts = ''
      }

      if (this.selectedTab === conversationTabTypes.leads.value) {
        this.populateConversationsDetails().then(() => { this.isLoading = false })
      } else if (this.selectedTab === conversationTabTypes.archived.value) {
        this.populateArchivedConversationsDetails().then(() => { this.isLoading = false })
      } else {
        this.isException = true
      }
    },
    async populateConversationsDetails () {
      try {
        let response = await ConversationService.getAccountConversationDetailsMany(this.accountId, this.filter)
        this.total = response.data.total
        this.conversationDetails = (response.data.conversationDetails || []).map(x => {
          x.vdpLink = this.buildVDPLink(this.sitesInfo[x.accountId], x)
          return x
        })
      } catch (ex) {
        this.isException = true
        this.$toaster.error('Cannot get conversations listing')
        this.$logger.handleError(ex, `Cannot get conversations listing for accountId: ${this.accountId}`, this.filter)
      }
    },
    async populateArchivedConversationsDetails () {
      try {
        let response = await ConversationService.getArchivedConversationDetailsMany(this.accountId, this.filter)
        this.total = response.data.total
        this.conversationDetails = (response.data.conversationDetails || []).map(x => {
          x.vdpLink = this.buildVDPLink(this.sitesInfo[x.accountId], x)
          return x
        })
      } catch (ex) {
        this.isException = true
        this.$toaster.error('Cannot get archived conversations listing')
        this.$logger.handleError(ex, `Cannot get archived conversations listing for accountId: ${this.accountId}`, this.filter)
      }
    },
    async populateFilterData () {
      const leadTypesPromise = this.$store.dispatch('leads/getLeadTypes').then(res => {
        this.filterOptions.leadTypes = res.data
      }).catch(ex => {
        this.isException = true
        this.$toaster.error('Cannot populate lead types')
        this.$logger.handleError(ex, 'Cannot populate lead types')
      })

      const accessibleLeadAccountsPromise = this.populateAccessibleLeadAccounts()

      let campaignTypesPromise
      let apiCampaignTypeFilters = {
        includeAllAccounts: this.hasAccountsLeadsToDisplay ? this.filter.includeAllAccounts : false,
        includedaccounts: this.hasAccountsLeadsToDisplay ? this.filter.includedaccounts : '',
        excludedaccounts: this.hasAccountsLeadsToDisplay ? this.filter.excludedaccounts : ''
      }
      if (this.selectedTab === conversationTabTypes.leads.value) {
        campaignTypesPromise = this.populateCampaignTypes(apiCampaignTypeFilters)
      } else if (this.selectedTab === conversationTabTypes.archived.value) {
        campaignTypesPromise = this.populateArchivedCampaignTypes(apiCampaignTypeFilters)
      }

      Promise.all([leadTypesPromise, campaignTypesPromise, accessibleLeadAccountsPromise]).then(() => { this.isFilterOptionsLoaded = true })
    },
    async populateCampaignTypes (apiCampaignTypeFilters) {
      ConversationService.getCampaignTypes(this.accountId, apiCampaignTypeFilters).then(res => {
        this.filterOptions.campaignTypes = res.data
      }).catch(ex => {
        this.isException = true
        this.$toaster.error('Cannot populate campaign types')
        this.$logger.handleError(ex, 'Cannot populate campaign types')
      })
    },
    async populateArchivedCampaignTypes (apiCampaignTypeFilters) {
      ConversationService.getArchivedCampaignTypes(this.accountId, apiCampaignTypeFilters).then(res => {
        this.filterOptions.campaignTypes = res.data
      }).catch(ex => {
        this.isException = true
        this.$toaster.error('Cannot populate campaign types')
        this.$logger.handleError(ex, 'Cannot populate campaign types')
      })
    },
    async populateAccessibleLeadAccounts () {
      if (!this.hasAccountsLeadsToDisplay) {
        return
      }
      this.$store.dispatch('leads/getAccessibleLeadAccounts').then(res => {
        this.accessibleLeadAccounts = res.data || []
      }).catch(ex => {
        this.isException = true
        this.$logger.handleError(ex, 'Failed on getting accessible lead accounts')
      })
    },
    onDeleteArchivedConversation (params) {
      ConversationService.deleteConversationDetailsFromArchive(this.accountId, params.conversationId, params.conversationDetailsId).then(res => {
        this.$toaster.success('Conversation Details Successfully Deleted from Archive')
      }).catch(ex => {
        this.$toaster.error('Cannot delete conversation details from archive')
        this.$logger.handleError(ex, `Cannot delete conversation details from archive for accountId: ${this.accountId}`, params)
      }).finally(() => {
        this.synchronizeUrlAndReload()
      })
    },
    async populateSitesInfo () {
      let sites = {}
      try {
        let siteIds = this.hasAccountsLeadsToDisplay ? this.getAllAccessibleLeadAccounts() : [this.accountId]
        let response = await this.$store.dispatch('website/getSitesBasicInfo', {data: {siteIds: siteIds}})
        sites = response.data.sites
      } catch (ex) {
        this.$logger.handleError(ex, 'Failed to get sites info from server')
      }
      this.sitesInfo = sites
    },
    getIntegerArrayFromString (str) {
      if (!str || !str.trim()) {
        return []
      }
      let array = []
      for (let numberStr of str.split(',')) {
        let number = parseInt(numberStr)
        if (!isNaN(number)) {
          array.push(number)
        }
      }
      return array
    },
    buildVDPLink (siteInfo, conversationDetail) {
      let host = (siteInfo || {}).host || ''
      let vin = conversationDetail.vehicleVin || ''
      return vin && host ? `${host}/details.aspx?vin=${vin}` : ''
    }
  }
}
</script>

<style scoped>
.custom-leads-tab-paging {
  position: absolute;
  right: 10px;
  z-index: 2;
}
</style>
