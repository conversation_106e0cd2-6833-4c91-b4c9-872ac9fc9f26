export default {
  hasFlag: function (requestedPermission, userPermission) {
    let resultStr = ''
    const extendedRequestedPermission = extendWithZeros(requestedPermission)
    const extendedUserPermission = extendWithZeros(userPermission)
    for (let i = 0; i < extendedRequestedPermission.length; i++) {
      if (+extendedRequestedPermission[i] <= +extendedUserPermission[i]) {
        resultStr += extendedUserPermission[i]
      } else {
        resultStr += extendedRequestedPermission[i]
      }
    }

    return resultStr === extendedUserPermission
  },

  hasFlagIntersect: function (requestedPermissions, userPermission) {
    let hasPermission = requestedPermissions === undefined ||
    requestedPermissions.find(x => this.hasFlag(x, userPermission))

    return hasPermission !== undefined
  },

  concatFlags: function (...params) {
    let res = ''

    for (let i = 0; i < 64; i++) {
      let isOneFound = false
      for (let j = 0; j < params.length; j++) {
        if (params[j][i] === '1') {
          isOneFound = true
          break
        }
      }

      res += isOneFound ? '1' : '0'
    }

    return res
  },

  shiftLeft: function (str, shiftSize) {
    str = extendWithZeros(str)
    return str.slice(shiftSize) + '0'.repeat(shiftSize)
  },

  shiftRight: function (str, shitSize) {
    str = extendWithZeros(str)
    return '0'.repeat(shitSize) + str.substring(0, str.length - shitSize)
  }
}

function extendWithZeros (v) {
  let len = v.length
  let additionalString = '0'.repeat(64 - len)

  return additionalString + v
}
