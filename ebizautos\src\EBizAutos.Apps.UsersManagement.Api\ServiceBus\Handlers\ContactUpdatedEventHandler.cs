﻿using EBizAutos.Apps.ServiceBus.Events.Contact;
using EBizAutos.Apps.UsersManagement.Api.Managers;
using EBizAutos.CommonLib.ServiceBus;
using EBizAutos.CommonLibCore;
using System.Threading.Tasks;
using System;

namespace EBizAutos.Apps.UsersManagement.Api.ServiceBus.Handlers {
	public class ContactUpdatedEventHandler : IEventHandler<IContactUpdatedEvent, bool> {
		private readonly UserManager _manager;

		public ContactUpdatedEventHandler(UserManager manager) {
			_manager = manager;
		}

		public async Task<PromiseResult<bool>> HandleAsync(IConsumeContext<IContactUpdatedEvent> consumeContext) {
			if (consumeContext?.Message == null)
				return PromiseResult<bool>.Failed(new Exception("Event is null."));

			IContactUpdatedEvent contactUpdatedEvent = consumeContext.Message;
			PromiseResult<bool> result = await _manager.UpdateUserByContactAsync(contactUpdatedEvent);
			return result;
		}
	}
}