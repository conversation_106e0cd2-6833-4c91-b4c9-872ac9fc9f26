import BaseService from '../BaseService'

class SyncService extends BaseService {
  synchronizeAccountVehicles (accountId, hasToSyncInBackground = true) {
    return this.axios.get(`/api/inventory/synchronize/accounts/${accountId}`, { params: { hasToSyncInBackground } })
  };
  synchronizeAccountVehicle (accountId, vin) {
    return this.axios.get(`/api/inventory/synchronize/${accountId}/${vin}`)
  };
  forceSynchronizeVehicle (accountId, vin) {
    return this.axios.get(`/api/inventory/synchronize/${accountId}/${vin}/force`)
  };
  forceSynchronizeAccountVehicles (accountId) {
    return this.axios.get(`/api/inventory/synchronize/${accountId}/force`)
  };
  forceRebuildAccountVehiclesData (accountId) {
    return this.axios.get(`/api/inventory/synchronize/${accountId}/vehicledata/force`)
  };
  forceRebuildVehicleData (accountId, vin) {
    return this.axios.get(`/api/inventory/synchronize/${accountId}/${vin}/vehicledata/force`)
  };
  forceSynchronizeNewVehicle (accountId, vin) {
    return this.axios.get(`/api/inventory/synchronize/${accountId}/${vin}/force`, {params: {isNotExistedInAppsVehicleForce: true}})
  };
  forceAutoVideoRegeneration (accountId, vin) {
    return this.axios.post(`/api/inventory/video_encoder/queue/photo_to_video/force/${accountId}/${vin}`)
  };
  forceAccountVehiclesVideoRegeneration (accountId) {
    return this.axios.post(`/api/inventory/video_encoder/queue/photo_to_video/force/${accountId}`)
  };
}

export default new SyncService()
