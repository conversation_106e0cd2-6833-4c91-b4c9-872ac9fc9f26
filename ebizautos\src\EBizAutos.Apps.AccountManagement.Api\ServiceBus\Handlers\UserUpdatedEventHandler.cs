﻿using EBizAutos.Apps.AccountManagement.Api.Utilities.Managers;
using EBizAutos.Apps.ServiceBus.Events.User;
using EBizAutos.CommonLib.ServiceBus;
using EBizAutos.CommonLibCore;
using System;
using System.Threading.Tasks;

namespace EBizAutos.Apps.AccountManagement.Api.ServiceBus.Handlers {
	public class UserUpdatedEventHandler : IEventHandler<IUserUpdatedEvent, bool> {
		private readonly AppsContactManager _contactManager = null;

		public UserUpdatedEventHandler(AppsContactManager contactManager) {
			_contactManager = contactManager;
		}

		public async Task<PromiseResult<bool>> HandleAsync(IConsumeContext<IUserUpdatedEvent> consumeContext) {
			if (consumeContext?.Message == null)
				return PromiseResult<bool>.Failed(new Exception("Event is null."));

			IUserUpdatedEvent userUpdatedEvent = consumeContext.Message;
			PromiseResult<bool> result = await _contactManager.SetAccountContactByUserInfoAsync(userUpdatedEvent);
			return result;
		}
	}
}