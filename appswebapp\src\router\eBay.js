import permissions from '../shared/common/permissions'
import applicationTypes from '../shared/common/applicationTypes'

const eBayDefaultMeta = {
  applicationType: applicationTypes.AppsEBay.Id,
  permissions: [permissions.EBayViewSettings],
  applicationFullAccess: permissions.EBayFullAccess
}

export default [{
  path: '/ebay',
  meta: {
    ...eBayDefaultMeta
  },
  component: () => import('@/layout/Layout2'),
  props: (route) => ({ accountId: +route.params.accountId }),
  children: [{
    path: '',
    name: 'ebay-accounts',
    meta: {
      ...eBayDefaultMeta,
      permissions: [permissions.EBayFullAccess],
      applicationFullAccess: permissions.EBayFullAccess
    },
    component: () => import('@/pages/eBay/accounts')
  }, {
    path: 'errorsandscheduled',
    name: 'ebay-errors-and-scheduled',
    meta: {
      ...eBayDefaultMeta,
      permissions: [permissions.EBayFullAccess],
      applicationFullAccess: permissions.EBayFullAccess
    },
    component: () => import('@/pages/eBay/errorsAndScheduled')
  }, {
    path: ':accountId(\\d+)',
    meta: {
      ...eBayDefaultMeta
    },
    props: (route) => ({ accountId: +route.params.accountId }),
    component: () => import('@/pages/eBay/layout'),
    redirect: {
      name: 'ebay-inventory'
    },
    children: [
      {
        path: 'posts',
        name: 'ebay-posts',
        meta: {
          ...eBayDefaultMeta
        },
        props: (route) => ({ accountId: +route.params.accountId }),
        component: () => import('@/pages/eBay/posts')
      }, {
        path: 'posts/:auctionId(\\d+)/revise',
        name: 'ebay-revise',
        meta: {
          ...eBayDefaultMeta,
          permissions: [permissions.EBayManageSettings],
          applicationFullAccess: permissions.EBayManageSettings
        },
        props: (route) => ({ accountId: +route.params.accountId, auctionId: +route.params.auctionId }),
        component: () => import('@/pages/eBay/revise')
      }, {
        path: 'inventory',
        name: 'ebay-inventory',
        meta: {
          ...eBayDefaultMeta
        },
        props: (route) => ({ accountId: +route.params.accountId }),
        component: () => import('@/pages/eBay/inventory')
      }, {
        path: 'inventory/:auctionId/offers',
        name: 'ebay-offers',
        meta: {
          ...eBayDefaultMeta
        },
        props: (route) => ({ accountId: +route.params.accountId, auctionId: route.params.auctionId }),
        component: () => import('@/pages/eBay/offers')
      }, {
        path: 'inventory/:vin/schedule',
        name: 'ebay-schedule',
        meta: {
          ...eBayDefaultMeta,
          permissions: [permissions.EBayManageSettings],
          applicationFullAccess: permissions.EBayManageSettings
        },
        props: (route) => ({ accountId: +route.params.accountId, vin: route.params.vin }),
        component: () => import('@/pages/eBay/schedule')
      }, {
        path: 'automated',
        name: 'ebay-automated',
        meta: {
          ...eBayDefaultMeta,
          permissions: [permissions.EBayFullAccess],
          applicationFullAccess: permissions.EBayFullAccess
        },
        props: (route) => ({ accountId: +route.params.accountId }),
        component: () => import('@/pages/eBay/automated')
      }, {
        path: 'settings',
        name: 'ebay-settings',
        meta: {
          ...eBayDefaultMeta,
          permissions: [permissions.EBayManageSettings],
          applicationFullAccess: permissions.EBayManageSettings
        },
        props: (route) => ({ accountId: +route.params.accountId }),
        component: () => import('@/pages/eBay/settings')
      }, {
        path: 'posts/:vin/history',
        name: 'ebay-history',
        meta: {
          ...eBayDefaultMeta
        },
        props: (route) => ({accountId: +route.params.accountId, vin: route.params.vin}),
        component: () => import('@/pages/eBay/history')
      }
    ]
  }]
}]
