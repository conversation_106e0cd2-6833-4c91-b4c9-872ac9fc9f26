class VehicleOverviewHelper {
  constructor (vehicle, store, logger) {
    this.vehicle = vehicle
    this.store = store
    this.logger = logger
  };

  async updateYear (newYear) {
    this.resetVehicleOverviewProperties()
    this.vehicle.year = newYear
    if (this.vehicle.make && this.vehicle.year) {
      try {
        await this.populateVehicleMakeYearDependentInfo()
      } catch (e) {
        this.logger.handleError(e, `Can't populate vehicle make year dependent information`, {
          make: this.vehicle.make,
          year: this.vehicle.year
        })
      }
    }
  };

  async updateVehicleMake (newMake) {
    this.resetVehicleMakeDependentProps()
    this.vehicle.make = newMake
    if (this.vehicle.make && this.vehicle.year) {
      try {
        await this.populateVehicleMakeYearDependentInfo()
      } catch (e) {
        this.logger.handleError(e, `Can't populate vehicle make year dependent information`, {
          make: this.vehicle.make,
          year: this.vehicle.year
        })
      }
    }
  };

  async updateVehicleModel (newModel) {
    this.resetVehicleModelDependentProperties()
    this.vehicle.model = newModel
    if (this.vehicle.make && this.vehicle.year && this.vehicle.model) {
      try {
        await this.populateVehicleModelDependentInfo()
      } catch (e) {
        this.logger.handleError(e, `Can't populate vehicle model dependent information`, {
          make: this.vehicle.make,
          year: this.vehicle.year,
          model: this.vehicle.model
        })
      }
    }
  };

  async updateVehicleStyle (newStyle) {
    this.resetStyleDependentProperties()
    if (!newStyle) {
      return
    }
    this.vehicle.vehicleStyleId = newStyle.vehicleStyleId
    this.vehicle.vehicleYearModelId = newStyle.vehicleYearModelId
    this.vehicle.trim = newStyle.galleryStyleName
    this.vehicle.galleryTrimLabel = newStyle.galleryTrimLabel

    if (this.vehicle.transTypeId !== undefined) {
      this.vehicle.transTypeId = newStyle.transTypeId
    }
    if (this.vehicle.transGearsId !== undefined) {
      this.vehicle.transGearsId = newStyle.transGears
    }
    if (this.vehicle.bodyStyleId !== undefined) {
      this.vehicle.bodyStyleId = newStyle.bodyStyleId
    }
    if (this.vehicle.engineFuelId !== undefined) {
      this.vehicle.engineFuelId = newStyle.fuelTypeId
    }
    if (this.vehicle.engine !== undefined) {
      this.vehicle.engine = ''
    }
    if (this.vehicle.drivetrainId !== undefined) {
      this.vehicle.drivetrainId = newStyle.drivetrainId
    }
    if (this.vehicle.cylindersType !== undefined) {
      this.vehicle.cylindersType = newStyle.cylinders
    }
    if (this.vehicle.cabStyleId !== undefined) {
      this.vehicle.cabStyleId = newStyle.cabStyleId
    }
    if (this.vehicle.bedStyleId !== undefined) {
      this.vehicle.bedStyleId = newStyle.bedStyleId
    }

    if (this.vehicle.vehicleStyleId) {
      try {
        await this.populateVehicleStyleDependentInfo()
      } catch (e) {
        this.logger.handleError(e, `Can't populate vehicle style dependent information`, {
          make: this.vehicle.make,
          year: this.vehicle.year
        })
      }
    }

    if (this.vehicle.vehicleYearModelId) {
      try {
        await this.populateVehicleYearModelIdDependentInformation()
      } catch (e) {
        this.logger.handleError(e, `Can't populate vehicle year model id dependent information`, {
          vehicleYearModelId: this.vehicle.vehicleYearModelId
        })
      }
    }
  };

  updateBodyStyle (newBodyStyle) {
    this.vehicle.bodyStyleId = newBodyStyle
  };

  updateTransGears (newTransGearId) {
    this.vehicle.transGearsId = newTransGearId
  };

  updateTransType (newTransTypeId) {
    this.vehicle.transTypeId = newTransTypeId
  };

  updateDrivetrain (newDrivetrainId) {
    this.vehicle.drivetrainId = newDrivetrainId
  };

  updateEngine (newEngine) {
    this.vehicle.engine = newEngine
  };

  updateCylinders (newCylinderType) {
    this.vehicle.cylindersType = newCylinderType
  };

  updateFuel (newFuelType) {
    this.vehicle.engineFuelId = newFuelType
  };

  resetVehicleOverviewProperties () {
    this.resetVehicleMakeDependentProps()
  };

  resetVehicleMakeDependentProps () {
    this.vehicle.model = ''
    this.resetVehicleModelDependentProperties()
  };

  resetVehicleModelDependentProperties () {
    this.vehicle.vehicleStyleId = 0
    this.resetStyleDependentProperties()
  };

  resetStyleDependentProperties () {
    this.vehicle.vehicleYearModelId = 0
    this.vehicle.trim = ''
    this.vehicle.galleryTrimLabel = ''
    this.vehicle.vehicleStyleId = 0

    if (this.vehicle.transTypeId !== undefined) {
      this.vehicle.transTypeId = 0
    }
    if (this.vehicle.transGearsId !== undefined) {
      this.vehicle.transGearsId = 0
    }
    if (this.vehicle.bodyStyleId !== undefined) {
      this.vehicle.bodyStyleId = 0
    }
    if (this.vehicle.engineFuelId !== undefined) {
      this.vehicle.engineFuelId = 0
    }
    if (this.vehicle.engine !== undefined) {
      this.vehicle.engine = ''
    }
    if (this.vehicle.drivetrainId !== undefined) {
      this.vehicle.drivetrainId = 0
    }
    if (this.vehicle.cylindersType !== undefined) {
      this.vehicle.cylindersType = ''
    }
    if (this.vehicle.cabStyleId !== undefined) {
      this.vehicle.cabStyleId = 0
    }
    if (this.vehicle.bedStyleId !== undefined) {
      this.vehicle.bedStyleId = 0
    }
    this.store.dispatch('categoryData/resetColors')
  };

  async populateVehicleMakeYearDependentInfo () {
    await this.store.dispatch('categoryData/populateCategoryModelsForMake', { make: this.vehicle.make, year: this.vehicle.year })
  };

  async populateVehicleModelDependentInfo () {
    await this.store.dispatch('categoryData/populateStyles', { make: this.vehicle.make, model: this.vehicle.model, year: this.vehicle.year })
  };

  async populateVehicleStyleDependentInfo () {
    await this.store.dispatch('categoryData/populateEngines', { vehicleStyleId: this.vehicle.vehicleStyleId })
  };

  async populateVehicleYearModelIdDependentInformation () {
    await this.store.dispatch('categoryData/populateInteriorColors', { vehicleYearModelId: this.vehicle.vehicleYearModelId })
    await this.store.dispatch('categoryData/populateExteriorColors', { vehicleYearModelId: this.vehicle.vehicleYearModelId })
  };
}

export default VehicleOverviewHelper
