{"Kestrel": {"EndPoints": {"Http": {"Url": "http://0.0.0.0:9017"}}}, "AppSettings": {"ApplicationName": "Apps Users Management API (dev)", "IsDev": "true", "DataAccessEncryptKey": "fpABRddPOOg0hbm1PUHIjw==", "DataAccessEncryptIV": "AAAAAAAAAAAAAAAAAAAAAA==", "NotDisplayUserActivityLogsFromUsers": ["appsaccountmanager"], "UsersManagementLink": "http://apps.sandbox.ebizautos.com/users/?usertype={0}&search={1}"}, "DbSettings": {"UsersMongoDbRepositoryConnectionStrings": "mongodb+srv://ebizdev:<EMAIL>/cp?retryWrites=true&w=majority", "UsersMongoDbSecondaryRepositoryConnectionStrings": "mongodb+srv://ebizdev:<EMAIL>/cp?retryWrites=true&w=majority&readPreference=secondaryPreferred", "UserRolesMongoDbRepositoryConnectionStrings": "mongodb+srv://ebizdev:<EMAIL>/cp?retryWrites=true&w=majority", "UsersLogsMongoDbConnectionString": "mongodb+srv://ebizdev:<EMAIL>/appsuserlogs?retryWrites=true&w=majority", "UserFetcherMsSqlRepositoryConnectionString": "server=dev-sql.internal.aws.ebizautos.com; database=EBizAutos; User ID=sa; Pwd=********; Max Pool Size=300; Connection Timeout=30; Application Name=AppsNet;", "SettingsMongoDbRepositoryConnectionStrings": "mongodb+srv://ebizdev:<EMAIL>/cp?retryWrites=true&w=majority", "AppsAccountsMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/accounts?retryWrites=true&w=majority"}, "ExceptionSettings": {"ErrorWebServiceLocation": "http://errors.aws.ebizautos.com/queuedservice.asmx", "ApplicationCategoryId": 9, "MailServer": "email-01.internal.aws.ebizautos.com", "MailPort": 25, "MailUserName": "", "MailPassword": "", "MailEnableSsl": false, "ErrorEmailFrom": "<EMAIL>", "ErrorEmailTo": ["alex.i<PERSON><PERSON><PERSON>@ebizautos.com"], "EmailCc": [], "HasToUseServiceBus": false}, "ServiceBusSettings": {"Host": "b-14921ad0-337a-4458-84ee-c042b1327094-1.mq.us-east-1.amazonaws.com", "Port": 61617, "Username": "ebizeventbus", "Password": "z$32s0d-nd8G62!js83aPuhpUh", "FailoverHosts": ["b-14921ad0-337a-4458-84ee-c042b1327094-1.mq.us-east-1.amazonaws.com"], "HasToUseSsl": true, "IsLoggingOn": true, "PublishSettings": {"RetryAttempts": 3, "MinRetryDelayInMs": 5000, "MaxRetryDelayInMs": 10000}}, "EmailNotificationSettings": {"MailServer": "email-smtp.us-east-1.amazonaws.com", "MailPort": 587, "MailUserName": "AKIAJ63DJM7JPLD4746A", "MailPassword": "AkQS3wwECygPDx6tSkdHWmvtVW5P76sm5QbBak3Y6JjY", "MailEnableSsl": true, "EmailFrom": "<EMAIL>", "EmailsTo": ["alex.i<PERSON><PERSON><PERSON>@ebizautos.com"], "EmailsCc": []}}