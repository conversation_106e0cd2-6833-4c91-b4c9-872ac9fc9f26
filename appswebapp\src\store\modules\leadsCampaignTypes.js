import axios from 'axios'

export default {
  namespaced: true,
  state: {
    campaignType: null
  },
  getters: {
    campaignType: state => state.campaignType
  },
  mutations: {
    setCampaignType (state, data) {
      state.campaignType = data
      if (state.campaignType.type === 0) { // need to change Unmatched campaign type to On Site campaign type
        state.campaignType.type = 1
      }
    }
  },
  actions: {
    async populateCampaignType ({ commit }, parameters) {
      const result = await axios.get(`/api/leads/campaign_types/${parameters.type}/${parameters.id}/details`)

      if (!result.data) {
        throw new Error(`Campaign type response data is empty. Request result: ${JSON.stringify(result)}`)
      }

      commit('setCampaignType', result.data)

      return result.data
    },
    async createNewCampaignType ({ commit }, parameters) {
      const result = await axios.post('/api/leads/campaign_types/', parameters.data)

      return result.data
    },
    async updateCampaignType ({ commit }, parameters) {
      const result = await axios.post(`/api/leads/campaign_types/${parameters.type}/${parameters.id}/update`, parameters.data)

      return result.data
    },
    async moveUnmatchedCampaignTypeToOnSite ({ commit }, parameters) {
      const result = await axios.post(`/api/leads/campaign_types/unmatched/${parameters.id}/move`, parameters.data)

      return result.data
    },
    async deleteCampaignType ({ commit }, parameters) {
      const result = await axios.post(`/api/leads/campaign_types/${parameters.id}/delete`)

      return result.data
    },
    async getCampaignTypeNewPrototype ({ commit }) {
      const result = await axios.get('/api/leads/campaign_types/new')

      commit('setCampaignType', result.data)

      return result.data
    }
  }
}
