<template>
  <editSettingsHelper :readOnlyMode="readOnlyMode" @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="Economy" :isLoading="isUpdatingProcessed" :isDisabled="isDisabled" :isViewMode="isViewMode">
    <template slot="settings-content">
      <detail-row v-if="accountLevel" :fixed-payload-width="true" :title-position="'start'">
        <span slot="title">Use Custom Economy Settings:</span>
        <b-form-checkbox v-if="!isViewMode" slot="payload" v-model="updatedSettings.hasToUseCustomFuelEconomySettings"></b-form-checkbox>
        <span slot="payload" v-else>
          {{ updatedSettings.hasToUseCustomFuelEconomySettings ? 'Yes' : 'No' }}
        </span>
      </detail-row>
      <b-overlay :show="!(!accountLevel || updatedSettings.hasToUseCustomFuelEconomySettings)" opacity="0.7" blur="1.2px">
        <template #overlay>
          <span></span>
        </template>
        <ValidationProvider name="Fuel Economy Description" :rules="getAccountFuelEconomyDescriptionRules" v-slot="{errors}">
          <detail-row :fixed-payload-width="true" :title-position="'start'" :error="errors[0]">
            <span slot="title">Fuel Economy Description
            <b-icon variant="secondary" :id="`economy-description-popover-${id}`" icon="question-circle"></b-icon>:
              <b-popover :target="`economy-description-popover-${id}`" triggers="hover click blur">
                <template #title>Available Placeholders</template>
                <b-list-group>
                  <b-list-group-item @click="copyText('{CityMileage}')">{CityMileage} <b-icon icon="files"></b-icon></b-list-group-item>
                  <b-list-group-item @click="copyText('{HighwayMileage}')">{HighwayMileage} <b-icon icon="files"></b-icon></b-list-group-item>
                  <b-list-group-item @click="copyText('{RunUnits}')">{RunUnits} <b-icon icon="files"></b-icon></b-list-group-item>
                </b-list-group>
              </b-popover></span>
            <span slot="payload" v-if="isViewMode">{{ settings.fuelEconomyTextTemplate || '-' }}</span>
            <b-textarea name="Fuel Economy Description" slot="payload" v-else rows="5" no-resize v-model="updatedSettings.fuelEconomyTextTemplate"></b-textarea>
          </detail-row>
        </ValidationProvider>
      </b-overlay>
    </template>
  </editSettingsHelper>
</template>

<script>
import editSettingsHelper from '@/components/_shared/editSettingsHelper'
import detailRow from '@/components/details/helpers/detailRow'
import editSettingsMixin from '../editSettingsMixin'
import globals from '../../../../globals'

export default {
  name: 'tts-economy-settings-section',
  props: {
    settings: { type: Object, required: true },
    isUpdatingProcessed: { type: Boolean, required: true },
    isDisabled: Boolean,
    accountLevel: Boolean,
    readOnlyMode: Boolean
  },
  data () {
    return {
      id: this.$uuid.v4(),
      updatedSettings: globals().getClonedValue(this.settings),
      isViewMode: true
    }
  },
  mixins: [editSettingsMixin],
  components: {
    detailRow,
    editSettingsHelper
  },
  computed: {
    getAccountFuelEconomyDescriptionRules () {
      if (!this.accountLevel || !this.updatedSettings.hasToUseCustomFuelEconomySettings) {
        return ''
      }
      return 'required'
    }
  },
  methods: {
    updateSettings () {
      this.$emit('saveChanges', this.updatedSettings)
    },
    copyText (text) {
      this.$copyProvider.copyTextToClipboard(text).then(() => {
        this.$toaster.success(`Copied the text: ${text}`, { timeout: 4000 })
      }).catch(err => {
        console.error(err)
      })
    }
  },
  watch: {
    'updatedSettings': {
      deep: true,
      handler: function () {
        this.$emit('input', this.updatedSettings)
      }
    }
  }
}
</script>
