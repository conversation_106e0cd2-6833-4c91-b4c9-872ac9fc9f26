import axios from 'axios'

export default {
  namespaced: true,
  state: {
    settingsPutData: {},
    areas: []
  },
  getters: {
    areas: state => state.areas,
    settingsPutData: state => state.settingsPutData
  },
  mutations: {
    setContactSettings (state, data) {
      state.settingsPutData.hasToUseCustomEmail = data.hasToUseCustomEmail
      if (data.hasToUseCustomEmail) {
        state.settingsPutData.customEmail = data.customEmail
        state.settingsPutData.emailDisplayType = data.emailDisplayType
      }
      state.settingsPutData.hasToUseCustomPhone = data.hasToUseCustomPhone
      state.settingsPutData.customPhone = data.customPhone
      state.settingsPutData.phoneExtension = data.phoneExtension
      state.settingsPutData.phoneDisplayType = data.phoneDisplayType
    },
    setPostTitleSettings (state, data) {
      state.settingsPutData.hasToIncludeExteriorColor = data.hasToIncludeExteriorColor
      state.settingsPutData.accountPriceType = data.accountPriceType
      state.settingsPutData.addedTitleText = data.addedTitleText
      state.settingsPutData.craigslistSpecificLocation = data.craigslistSpecificLocation
    },
    setOtherSettings (state, data) {
      state.settingsPutData.budget = data.budget
      state.settingsPutData.hasToRemoveSoldItemsFromCraigslist = data.hasToRemoveSoldItemsFromCraigslist
      state.settingsPutData.craigslistFavoriteAreaIds = data.craigslistFavoriteAreaIds
      state.settingsPutData.hasToSendTitleStatus = data.hasToSendTitleStatus
      state.settingsPutData.hasToHideVin = !data.hasToHideVin
    },
    setAdminSettings (state, data) {
      state.settingsPutData.isPostingAllowed = data
    },
    setPostingLimitsSettings (state, data) {
      state.settingsPutData.postingLimits = data
    },
    setBaseSettings (state, data) {
      state.settingsPutData = data
    },
    setCraigslistAreas (state, data) {
      state.areas = data
    }
  },
  actions: {
    async populateAccountSettings ({ commit }, parameters) {
      const result = await axios.get(`/api/craigslist/dashboard/${parameters.accountId}/settings`)

      commit('setBaseSettings', result.data.model)

      return result.data
    },
    async populateCraigslistAreas ({ commit }, parameters) {
      const result = await axios.get(`/api/craigslist/dashboard/accountareas/${parameters.accountId}`)

      commit('setCraigslistAreas', result.data)

      return result.data
    },
    async putSettingsData ({ commit }, parameters) {
      const result = await axios.put(`/api/craigslist/dashboard/${parameters.accountId}/settings`, parameters.data)

      return result.data
    },
    async putPostingLimitSettingsData ({ commit }, parameters) {
      const result = await axios.post(`/api/craigslist/dashboard/${parameters.accountId}/settings/posting_limits`, parameters.data)

      return result.data
    }
  }
}
