<template>
<div>
  <div v-if='!isLoading && !isException'>
    <b-row class="mb-2">
      <b-col>
        <div><h4>Account Listing</h4></div>
        <b-btn v-if="isAdmin" @click='showModal' variant='primary'>Edit Lead System Settings</b-btn>
      </b-col>
      <b-col class="pt-4">
        <range-selector
          @input="onRangeChanged"
          :value="filterDateRange"
          class="button-col float-right"
        />
      </b-col>
    </b-row>
    <leads-overview v-if='leadsOverviewModel' :model='leadsOverviewModel'/>
    <b-row class="mb-2">
      <b-col xl="5" lg="7" md="8" sm="10" xs="12">
        <b-input-group>
          <b-form-input v-model="searchAccountId" type="number" @keyup.enter.native="onSearch" placeholder="Search by Account Id"></b-form-input>
          <b-input-group-append>
            <b-btn variant="primary" type="submit" @click="onSearch">Go</b-btn>
          </b-input-group-append>
        </b-input-group>
      </b-col>
    </b-row>
    <b-card>
      <account-listing :sort='filter.sort' @sortChange='sortChange' :items='accountListing'/>
      <paging
        class="p-0"
        :pageNumber="filter.page"
        :pageSize="filter.pageSize"
        :totalItems="itemsTotalCount"
        titled
        pageSizeSelector
        @numberChanged="onPageChanged"
        @changePageSize="onChangePageSize"
      />
    </b-card>
    <!-- Modal -->
    <b-modal
      :visible="isShow"
      title="Lead Settings"
      size="lg"
      @hide="hideModal"
    >
      <div v-if='globalLeadsSettings'>
        <detail-row fixedPayloadWidth>
          <span slot="title">Max Leads Per IP Address:</span>
          <b-input-group slot="payload">
            <b-form-input
              v-model.number="globalLeadsSettings.thresholdSettings.maxLeadsPerIpAddress"
              type="number"
              step="1"
              min="0"
              @input="validateNonNegativeInteger('maxLeadsPerIpAddress')">
            </b-form-input>
          </b-input-group>
        </detail-row>
        <detail-row fixedPayloadWidth>
          <span slot="title">Max Leads Per IP Class C:</span>
          <b-input-group slot="payload">
            <b-form-input
              v-model.number="globalLeadsSettings.thresholdSettings.maxLeadsPerIpClassC"
              type="number"
              step="1"
              min="0"
              @input="validateNonNegativeInteger('maxLeadsPerIpClassC')">
            </b-form-input>
          </b-input-group>
        </detail-row>
        <detail-row fixedPayloadWidth>
          <span slot="title">Time Frame (in seconds):</span>
          <b-input-group slot="payload">
            <b-form-input
              v-model.number="globalLeadsSettings.thresholdSettings.timeFrameInSeconds"
              type="number"
              step="1"
              min="0"
              @input="validateNonNegativeInteger('timeFrameInSeconds')">
            </b-form-input>
          </b-input-group>
        </detail-row>
        <detail-row fixedPayloadWidth>
          <span slot="title">Call Fee:</span>
          <b-input-group prepend="$" slot="payload">
            <b-form-input v-model="globalLeadsSettings.feesForTwilio.callFee" type="number" step="0.01"></b-form-input>
          </b-input-group>
        </detail-row>
        <detail-row fixedPayloadWidth>
          <span slot="title">SMS Fee:</span>
          <b-input-group prepend="$" slot="payload">
            <b-form-input v-model="globalLeadsSettings.feesForTwilio.smsFee" type="number" step="0.01"></b-form-input>
          </b-input-group>
        </detail-row>
        <detail-row fixedPayloadWidth>
          <span slot="title">Initial Confirmation:</span>
          <b-form-textarea v-model='globalLeadsSettings.messagesForUser.initialConfirmation' slot="payload" cols="50" rows="4"></b-form-textarea>
        </detail-row>
        <detail-row fixedPayloadWidth>
          <span slot="title">Default Disclaimer Text:</span>
          <b-form-textarea v-model='globalLeadsSettings.defaultDisclaimerText' slot="payload" cols="50" rows="4"></b-form-textarea>
        </detail-row>
        <detail-row fixedPayloadWidth>
          <span slot="title">CCPA Disclaimer Template:</span>
          <b-form-textarea v-model='globalLeadsSettings.ccpaDisclaimer' slot="payload" cols="50" rows="4"></b-form-textarea>
        </detail-row>
        <detail-row fixedPayloadWidth>
          <span slot="title">TCPA Disclaimer Template:</span>
          <b-form-textarea v-model='globalLeadsSettings.tcpaDisclaimer' slot="payload" cols="50" rows="4"></b-form-textarea>
        </detail-row>
      </div>
      <template slot="modal-footer">
        <b-button @click="hideModal" class="text-center float-left">Close</b-button>
        <b-button @click="onSaveSettings" variant="primary" class="text-center float-left">Submit</b-button>
      </template>
    </b-modal>
  </div>
  <div v-else-if='isLoading && !isException' class="mt-5">
    <loader size='lg'/>
  </div>
  <div v-else class="mt-5">
    <h4 class="text-muted text-center">Something went wrong! Please reload page</h4>
  </div>
</div>
</template>

<script>
import loader from '@/components/_shared/loader'
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import priceInput from '@/components/_shared/priceInput'
import detailRow from '@/components/details/helpers/detailRow'
import leadsOverview from '@/components/leads/dashboard/leadsOverview'
import rangeSelector from '@/components/leads/rangeSelector/leadsRangeSelector'
import rangeHelper from '@/components/leads/rangeSelector/rangeHelper'
import leadsAccountListing from '@/components/leads/dashboard/accountListing'
import { mapGetters } from 'vuex'

const defaultRange = rangeHelper.defaultRange.asFormattedStrings()

const defaultValues = new ObjectSchema({
  dateFrom: { type: String, default: defaultRange[0] },
  dateTo: { type: String, default: defaultRange[1] },
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  sort: { type: Number, default: 6 }
})

const queryHelper = new QueryStringHelper(defaultValues)

export default {
  name: 'leads-global-dashboard',
  data () {
    return {
      isLoading: true,
      isException: false,
      searchAccountId: '',
      filter: defaultValues.getObject(),
      accountListing: [],
      itemsTotalCount: 0,
      routeName: 'leads-dashboard',
      isShow: false,
      globalLeadsSettings: null,
      leadsOverviewModel: null
    }
  },
  metaInfo: {
    title: 'Dashboard'
  },
  components: {
    'paging': () => import('@/components/_shared/paging'),
    'loader': loader,
    'detail-row': detailRow,
    'price-input': priceInput,
    'range-selector': rangeSelector,
    'leads-overview': leadsOverview,
    'account-listing': leadsAccountListing
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {}
    },
    isAdmin () {
      return this.user && this.user.isEbizAdmin
    },
    filterDateRange () {
      if ((this.filter || {}).dateFrom && (this.filter || {}).dateTo) {
        return [this.filter.dateFrom, this.filter.dateTo]
      }

      return null
    }
  },
  created () {
    this.filter = queryHelper.parseQueryStringToObject(this.$router)
    this.loadLeadsOverview()
    this.loadAccountListing()
  },
  methods: {
    async onRangeChanged (rangeInfo) {
      if (this.filter.dateFrom === rangeInfo.range[0] && this.filter.dateTo === rangeInfo.range[1]) {
        return
      }
      this.filter.dateFrom = rangeInfo.range[0]
      this.filter.dateTo = rangeInfo.range[1]
      queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.loadLeadsOverview()
      this.loadAccountListing()
    },
    onPageChanged (newPage) {
      this.filter.page = newPage
      this.synchronizeUrlAndReload()
    },
    onChangePageSize (newSize) {
      this.filter.pageSize = newSize
      this.filter.page = 1
      this.synchronizeUrlAndReload()
    },
    sortChange (sort) {
      this.filter.sort = sort
      this.synchronizeUrlAndReload()
    },
    synchronizeUrlAndReload () {
      queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.loadAccountListing()
    },
    loadLeadsOverview () {
      this.$store.dispatch('leads/getGlobalStatistic', this.filter).then(res => {
        this.leadsOverviewModel = res.data
      }).catch(ex => {
        this.isException = true
        this.$toaster.error(`Cannot get global statistic. Message: ${ex.response.data}`)
        this.$logger.handleError(ex, 'Cannot get global statistic', this.filter)
      })
    },
    loadAccountListing () {
      this.$store.dispatch('leads/getLeadsAccountListing', this.filter).then(res => {
        this.accountListing = res.data.accountsSummaries
        this.itemsTotalCount = res.data.accountsCount
      }).catch(ex => {
        this.isException = true
        this.$toaster.error(`Cannot get leads account listing. Message: ${ex.response.data}`)
        this.$logger.handleError(ex, 'Cannot get leads account listing', this.filter)
      }).finally(() => {
        this.isLoading = false
      })
    },
    showModal () {
      this.$store.dispatch('leads/getGlobalLeadsSettings').then(res => {
        this.globalLeadsSettings = res.data
        this.isShow = true
      }).catch(ex => {
        if (ex.response) {
          this.$toaster.error(`Cannot get global leads settings model. Message ${ex.response.data}`)
        } else {
          this.$toaster.error(`Cannot get global leads settings model. Message ${ex.message}`)
        }
        this.$logger.handleError(ex, 'Cannot get global leads settings model')
      })
    },
    hideModal () {
      this.isShow = false
    },
    onSaveSettings () {
      this.$store.dispatch('leads/updateGlobalLeadsSettings', this.globalLeadsSettings).then(res => {
        if (res.data) {
          this.globalLeadsSettings = null
          this.$toaster.success('Global Leads Settings were updated successfully')
        }
      }).catch(ex => {
        if (ex.response) {
          this.$toaster.error(`Cannot update Global Leads Settings. Message: ${ex.response.data}`)
        }
        this.$toaster.error(`Cannot update Global Leads Settings. Message: ${ex.message}`)
        this.$logger.handleError(ex, 'Cannot update Global Leads Settings.')
      }).finally(() => {
        this.isShow = false
      })
    },
    onSearch () {
      if (this.searchAccountId) {
        this.$store.dispatch('accountManagement/isAccountExists', { accountId: this.searchAccountId }).then(res => {
          if (res.data) {
            this.$router.push({name: 'leads-dashboard', params: { accountId: this.searchAccountId }})
          } else {
            this.$toaster.error(`${this.searchAccountId} is not exist`)
          }
        }).catch(ex => {
          this.$toaster.error(`Something went wrong`)
        })
      }
    },
    validateNonNegativeInteger (field) {
      const value = this.globalLeadsSettings.thresholdSettings[field]
      if (value !== '') {
        const intValue = Math.floor(Number(value))
        this.globalLeadsSettings.thresholdSettings[field] = intValue >= 0 ? intValue : 0
      }
    }
  }
}
</script>
