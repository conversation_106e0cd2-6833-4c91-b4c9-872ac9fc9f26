<template>
  <b-modal
    :title="isEditMode ? 'Edit User' : 'Create User'"
    :visible="visible"
    @hide="hide"
    size="lg"
  >
    <ValidationObserver ref="validator">
      <ValidationProvider v-if="userToManage.userType === userTypes.ebizServiceUser.value" name="Service Name" rules="required" v-slot="{errors}">
      <detail-row :title-position="'start'" :fixed-payload-width="true" :error="errors[0]">
        <span slot="title">
          Service Name:
        </span>
        <b-form-input name="ServiceName" slot="payload" v-model="userInfoCopy.userName"/>
      </detail-row>
      </ValidationProvider>
      <ValidationProvider v-else name="Login" rules="required" v-slot="{errors}">
      <detail-row :title-position="'start'" :fixed-payload-width="true" :error="errors[0]">
        <span slot="title">
          Login:
        </span>
        <b-form-input name="Login" slot="payload" v-model="userInfoCopy.userName">
        </b-form-input>
      </detail-row>
      </ValidationProvider>
      <detail-row v-if="userToManage.userType === userTypes.cpUser.value" :fixed-payload-width="true">
        <span slot="title">AccountId:</span>
        <span slot="payload" class="text-muted">{{userInfoCopy.accountId}}</span>
      </detail-row>
      <ValidationProvider v-if="userToManage.userType === userTypes.ebizServiceUser.value" name="Service Description" rules="required" v-slot="{errors}">
      <detail-row :title-position="'start'" :fixed-payload-width="true" :error="errors[0]">
        <span slot="title">
          Service Description:
        </span>
        <b-form-input name="ServiceDescription" slot="payload" v-model="userInfoCopy.firstName">
        </b-form-input>
      </detail-row>
      </ValidationProvider>
      <ValidationProvider v-else name="First Name" rules="required" v-slot="{errors}">
        <detail-row :title-position="'start'" :fixed-payload-width="true" :error="errors[0]">
          <span slot="title">
            First Name:
          </span>
          <b-form-input name="FirstName" slot="payload" v-model="userInfoCopy.firstName">
          </b-form-input>
        </detail-row>
      </ValidationProvider>
      <ValidationProvider v-if="userToManage.userType !== userTypes.ebizServiceUser.value"  name="Last Name" rules="required" v-slot="{errors}">
        <detail-row :title-position="'start'" :fixed-payload-width="true" :error="errors[0]">
          <span slot="title">
            Last Name:
          </span>
          <b-form-input name="LastName" slot="payload" v-model="userInfoCopy.lastName">
          </b-form-input>
        </detail-row>
      </ValidationProvider>
      <ValidationProvider rules="password:@confirm" v-slot="{errors}">
        <detail-row :title-position="'start'" :fixed-payload-width="true" :error="errors[0]">
          <span slot="title">Password:</span>
          <b-form-input type="password" :placeholder="isEditMode ? 'Leave empty if same' : 'Password'" slot="payload" v-model="userInfoCopy.password">
          </b-form-input>
        </detail-row>
      </ValidationProvider>
      <ValidationProvider name="confirm" vid="confirm" v-slot="{errors}">
        <detail-row :title-position="'start'" :fixed-payload-width="true" :error="errors[0]">
          <span slot="title">Repeat Password:</span>
          <b-form-input slot="payload" autocomplete="new-password" name="confirm" type="password" :placeholder="isEditMode ? 'Leave empty if same' : 'Repeat Password'" v-model="userInfoCopy.rePassword">
          </b-form-input>
        </detail-row>
      </ValidationProvider>
      <ValidationProvider name="Email" rules="required|email" v-slot="{errors}">
      <detail-row :title-position="'start'" :fixed-payload-width="true" :error="errors[0]">
        <span slot="title">Email:</span>
        <b-form-input name="Email" type="email" slot="payload" v-model="userInfoCopy.email"></b-form-input>
      </detail-row>
      </ValidationProvider>
      <detail-row v-if="hasFullAccess && userToManage.userType !== userTypes.ebizServiceUser.value" :fixed-payload-width="true">
        <span slot="title">Roles:</span>
        <multiselect
          slot="payload"
          :options='rolesOptions'
          v-model='selectedUsersRoles'
          :multiple='true'
          track-by="value"
          label='text'
          :searchable="false"
          />
      </detail-row>
      <detail-row v-if="hasFullAccess" :fixed-payload-width="true">
        <span slot="title">Inactive:</span>
        <b-form-checkbox slot="payload" v-model="userInfoCopy.isInactive" disabled></b-form-checkbox>
      </detail-row>
      <detail-row v-if="userToManage.userType !== userTypes.ebizServiceUser.value" :fixed-payload-width="true">
        <span slot="title">Default App:</span>
        <b-form-select slot="payload" v-model="userInfoCopy.defaultApp" :options="getAppOptions"></b-form-select>
      </detail-row>
    </ValidationObserver>
    <template #modal-footer>
      <b-btn size="sm" @click="hide">Close</b-btn>
      <b-btn size="sm" variant="primary" @click="submit">Submit</b-btn>
    </template>
  </b-modal>
</template>

<script>
import permissions from '@/shared/common/permissions'
import globals from '@/globals'
import { mapGetters } from 'vuex'
import detailRow from '@/components/details/helpers/detailRow'
import {userTypes, appTypes} from '@/shared/users/constants'
import Multiselect from 'vue-multiselect'
import UserManagementService from '../../services/users/UserManagementService'

export default {
  props: {
    visible: Boolean,
    isEditMode: Boolean,
    userToManage: { type: Object, required: true }
  },
  data () {
    return {
      userTypes,
      selectedUsersRoles: [],
      userInfoCopy: {},
      roles: []
    }
  },
  components: {
    detailRow,
    multiselect: Multiselect
  },
  created () {
    this.initData()
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {
        canManageAccountApplicationType: () => false
      }
    },
    hasFullAccess () {
      return this.user && this.user.hasPermissions && this.user.hasPermissions(permissions.FullAccess)
    },
    rolesOptions () {
      let options = []
      Object.values(this.roles).map(x => {
        options.push({
          value: x.id,
          text: x.name
        })
      })
      return options
    },
    getAppOptions () {
      return Object.values(appTypes)
    }
  },
  methods: {
    hide () {
      this.$emit('hide')
    },
    submit () {
      this.$refs.validator.validate().then(isValid => {
        if (isValid) {
          this.mapUserRoles().then(() => {
            this.$emit('submit', this.userInfoCopy)
          })
        }
      })
    },
    initData () {
      if (!this.visible) {
        return
      }

      this.userInfoCopy = globals().getClonedValue(this.userToManage)

      if (!this.hasFullAccess || this.userToManage.userType === userTypes.ebizServiceUser.value) {
        return
      }

      UserManagementService.getUserRoles().then(res => {
        this.roles = res.data
        this.selectedUsersRoles = []
        if (this.userToManage.roles && this.userToManage.roles.length > 0) {
          this.userToManage.roles.map(x => {
            let res = this.rolesOptions.find(role => role.value === x)
            if (res) {
              this.selectedUsersRoles.push(res)
            }
          })
        }
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong')
        this.hide()
      })
    },
    async mapUserRoles () {
      if (!this.hasFullAccess || this.userToManage.userType === userTypes.ebizServiceUser.value) {
        return
      }

      this.userInfoCopy.roles = this.selectedUsersRoles.map(x => x.value)
    }
  }
}
</script>
