<template>
  <div v-if="!isLoading && !isExceptionOccurred">
    <b-row>
      <b-col>
        <h4>Offers of Auction <b-link :href="getAuctionUrl"><u>{{auctionId}}</u></b-link></h4>
      </b-col>
      <b-col>
        <c-button class="float-right" variant="primary" v-if="offers && offers.length > 0" @confirm="declineAllPendingOffers" message="Are you sure you want to decline all pending best offers?">
          Decline All Pending Best Offers
        </c-button>
      </b-col>
    </b-row>
    <vehicleDescription v-if="vehicle" :vehicle="vehicle"/>
    <b-card no-body>
      <b-table
        class="products-table card-table"
        :items="offers"
        :fields="getTableFields"
        :striped="true"
        :bordered="false"
        responsive
      >
        <template #cell(userName)="row">
          {{row.item.UserFirstName}} {{row.item.UserLastName}}
        </template>
        <template #cell(manage)="row">
          <b-dropdown variant="outline-secondary icon-btn btn-round" size="sm" right no-caret boundary='viewport'>
            <template slot="button-content">
              <i class="ion ion-ios-more m-0"></i><span class="sr-only">Manage</span>
            </template>
            <b-dropdown-item @click="showAcceptModal(row.item)">Accept</b-dropdown-item>
            <b-dropdown-item @click="showDeclineModal(row.item)">Decline</b-dropdown-item>
          </b-dropdown>
        </template>
        <template #cell(show_details)="row">
          <div class="media align-items-center">
            <b-button size="sm" @click.stop="row.toggleDetails" class="text-center">
              {{ row.detailsShowing ? 'Hide' : 'Show' }} Details
            </b-button>
          </div>
        </template>
        <template #row-details="row">
          <b-card>
            <detail-row :fixed-payload-width="true">
              <span slot="title">User:</span>
              <b-link slot="payload" :href="getUserOfferUrl(row.item.UserId)"><u>{{row.item.UserId}}</u></b-link>
            </detail-row>
            <detail-row :fixed-payload-width="true">
              <span slot="title">User Message:</span>
              <span slot="payload">{{row.item.BuyerMessage || '-'}}</span>
            </detail-row>
            <detail-row :fixed-payload-width="true">
              <span slot="title">User Name:</span>
              <span slot="payload">{{row.item.UserFirstName}} {{row.item.UserLastName}}</span>
            </detail-row>
            <detail-row :fixed-payload-width="true">
              <span slot="title">User Email:</span>
              <span slot="payload">{{row.item.UserEmail || '-'}}</span>
            </detail-row>
            <detail-row :fixed-payload-width="true">
              <span slot="title">User Phone:</span>
              <span slot="payload">{{row.item.UserPhone || '-'}}</span>
            </detail-row>
            <detail-row :fixed-payload-width="true">
              <span slot="title">User City:</span>
              <span slot="payload">{{row.item.UserCity || '-'}}</span>
            </detail-row>
            <detail-row :fixed-payload-width="true">
              <span slot="title">User State:</span>
              <span slot="payload">{{row.item.UserState || '-'}}</span>
            </detail-row>
          </b-card>
        </template>
      </b-table>
    </b-card>
    <b-modal
      v-if="item"
      size="lg"
      :title="`${isAcceptOffer ? 'Accept' : 'Decline'} Offer`"
      :visible="isVisibleModal"
      @hide="onHideModal"
    >
      <detail-row :fixed-payload-width="true">
        <span slot="title">Offer Price:</span>
        <span slot="payload">{{getPriceText(item.OfferPrice)}}</span>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Reserve Price:</span>
        <span slot="payload">{{getPriceText(item.ReservePrice)}}</span>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Response Message:</span>
        <b-form-input v-model="responseMessage" slot="payload"></b-form-input>
      </detail-row>
      <template #modal-footer>
        <b-btn variant="primary" @click="onAcceptOffer" v-if="isAcceptOffer">Accept Offer</b-btn>
        <b-btn variant="primary" @click="onDeclineOffer" v-else>Decline Offer</b-btn>
      </template>
    </b-modal>
  </div>
  <div v-else-if="isLoading && !isExceptionOccurred">
    <loader size="lg"/>
  </div>
  <div v-else>
    <error-alert/>
  </div>
</template>

<script>
import vehicleDescription from '@/components/eBay/helpers/vehicleDescription'
import detailRow from '@/components/details/helpers/detailRow'
import loader from '@/components/_shared/loader'
import constants from '@/shared/ebay/constants'
import numeral from 'numeral'
import moment from 'moment'

export default {
  name: 'ebay-offers',
  props: {
    accountId: { type: Number, required: true },
    auctionId: { type: String, required: true }
  },
  metaInfo: {
    title: 'eBay Offers'
  },
  data () {
    return {
      offers: [],
      vehicle: {},
      isLoading: true,
      isExceptionOccurred: false,
      isVisibleModal: false,
      isAcceptOffer: false,
      item: null,
      responseMessage: ''
    }
  },
  components: {
    vehicleDescription,
    loader,
    detailRow
  },
  computed: {
    getTableFields () {
      return [
        {
          key: 'userName',
          label: 'User Name',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'show_details',
          label: '',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'OfferPrice',
          label: 'Price',
          tdClass: 'py-2 align-middle',
          formatter: value => numeral(value).format('$0,0')
        },
        {
          key: 'ReservePrice',
          label: 'Reserve Price',
          tdClass: 'py-2 align-middle',
          formatter: value => numeral(value).format('$0,0')
        },
        {
          key: 'OfferExpirationDateTime',
          label: 'Expiration Date Time',
          tdClass: 'py-2 align-middle',
          formatter: value => {
            if (!value) {
              return '-'
            }
            return moment(value).format('MM/DD/YYYY hh:mm A')
          }
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middler'
        }
      ]
    },
    getAuctionUrl () {
      return constants.eBayInfoUrls.ebayItemUrl(this.auctionId)
    }
  },
  created () {
    this.populateData()
  },
  methods: {
    populateData () {
      this.$store.dispatch('eBay/getAuctionBestOffers', { accountId: this.accountId, auctionId: this.auctionId }).then(res => {
        this.offers = res.data || []
        this.vehicle = (res.data || [])[0] || null
      }).catch(ex => {
        this.isExceptionOccurred = true
        this.$logger.handleError(ex, 'Exception occurred on get auction best offers')
      }).finally(() => {
        this.isLoading = false
      })
    },
    getUserOfferUrl (userId) {
      return constants.eBayInfoUrls.userFeedBackUrl(userId)
    },
    onAcceptOffer () {
      this.$store.dispatch('eBay/acceptBestOffer', { accountId: this.accountId, auctionId: this.auctionId, offerId: this.item.offerId, data: { message: this.responseMessage } }).then(res => {
        this.$toaster.success('Accepted Best Offer Successfully')
      }).catch(ex => {
        this.$toaster.error('Something went wrong!')
        this.$logger.handleError(ex, 'Exception occurred on accept best offer')
      }).finally(() => {
        this.onHideModal()
      })
    },
    onDeclineOffer (item) {
      this.$store.dispatch('eBay/declineBestOffer', { accountId: this.accountId, auctionId: this.auctionId, offerId: this.item.offerId, data: { message: this.responseMessage } }).then(res => {
        this.$toaster.success('Declined Best Offer Successfully')
      }).catch(ex => {
        this.$toaster.error('Something went wrong!')
        this.$logger.handleError(ex, 'Exception occurred on decline best offer')
      }).finally(() => {
        this.onHideModal()
      })
    },
    declineAllPendingOffers () {
      let apiData = {
        AccountId: this.accountId,
        AuctionId: this.auctionId
      }
      this.$store.dispatch('eBay/declineBestOffers', { accountId: this.accountId, auctionId: this.auctionId, data: apiData }).then(res => {
        this.$toaster.success('Declined All Pending Best Offers Successfully')
      }).catch(ex => {
        this.$toaster.error('Something went wrong!')
        this.$logger.handleError(ex, 'Exception occurred on decline all pending best offers')
      })
    },
    getPriceText (value) {
      return numeral(value).format('$0,0')
    },
    showAcceptModal (item) {
      this.item = item
      this.isAcceptOffer = true
      this.isVisibleModal = true
    },
    showDeclineModal (item) {
      this.item = item
      this.isAcceptOffer = false
      this.isVisibleModal = true
    },
    onHideModal () {
      this.item = null
      this.responseMessage = ''
      this.isVisibleModal = false
    }
  }
}
</script>
