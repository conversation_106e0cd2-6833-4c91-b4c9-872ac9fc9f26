<template>
  <div>
    <paid-search-summary
      :barItems="bar.items"
      :summary="summary"
      :barTimeFormat="barTimeFormat"
    ></paid-search-summary>

    <template v-if="isAccountLevel">
      <account-level-card v-if="isAccountLevel" @backToGroup="onBackToGroup">
        {{accountName}} ({{page.filter.accountId}})
      </account-level-card>

      <paid-search-account-level-table
        :tableItems="table.items"
        :totalItems="table.totalItems"
        :pageNumber="page.filter.pageNumber"
        :pageSize="page.filter.pageSize"
        :sortType="page.filter.sortType"
        @pageNumberChanged="onPageNumberChanged"
        @pageSizeChanged="onPageSizeChanged"
        @sortTypeChanged="onSortTypeChanged"
      ></paid-search-account-level-table>
    </template>
    <group-paid-search-by-account-table
      v-else-if="rangeInfo"
      :tableItems="table.items"
      :totalItems="table.totalItems"
      :pageNumber="page.filter.pageNumber"
      :pageSize="page.filter.pageSize"
      :sortType="page.filter.sortType"
      @pageNumberChanged="onPageNumberChanged"
      @pageSizeChanged="onPageSizeChanged"
      @sortTypeChanged="onSortTypeChanged"
      @accountNameClicked="onAccountNameClicked"
    ></group-paid-search-by-account-table>
  </div>
</template>

<style src="@/vendor/styles/pages/analytics.scss" lang="scss"></style>

<script>
import analyticsConstants from './../../../shared/analytics/constants'
import analyticsBuilders from './../../../shared/analytics/builders'
import analyticsHelper from '../helpers.js'
import baseGroupReportPage from './baseGroupReportPage.js'

import PaidSearchSummary from '../../../components/analytics_ga4/summaries/paidSearchSummary'
import AccountLevelCard from '../../../components/analytics_ga4/accountLevelCard'
import PaidSearchAccountLevelTable from '../../../components/analytics_ga4/tables/paidSearchAccountLevelTable'
import GroupPaidSearchByAccountTable from '../../../components/analytics_ga4/tables/groupPaidSearchByAccountTable'

const filterManager = analyticsBuilders.getFilterManager({
  accountId: { type: Number, default: 0 },
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  pageNumber: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  sortType: { type: Number, default: analyticsConstants.websiteEngagementSortTypes.accountNameAsc }
})

const defaultAccountLevelSortType = analyticsConstants.websiteEngagementSortTypes.dateDesc

export default {
  mixins: [baseGroupReportPage],
  name: 'group-paid-search',
  metaInfo: {
    title: 'Analytics - Paid Search'
  },
  components: {
    GroupPaidSearchByAccountTable,
    PaidSearchAccountLevelTable,
    AccountLevelCard,
    PaidSearchSummary
  },
  created () {
    this.$store.commit('analyticsGa4/setAnalyticsName', 'Paid Search')
    this.$store.commit('analyticsGa4/setBlackThemeOn', true)
  },
  data () {
    return {
      page: {
        filter: filterManager.defaultValue
      },
      summary: {
        sessions: 0,
        sessionsDelta: null,
        totalLeads: 0,
        totalLeadsDelta: null,
        conversionRate: 0,
        conversionRateDelta: null,
        spend: 0,
        spendDelta: null,
        costPerLead: 0,
        costPerLeadDelta: null
      }
    }
  },
  computed: {
    filterManager () {
      return filterManager
    },
    defaultAccountLevelSortType () {
      return defaultAccountLevelSortType
    }
  },
  methods: {
    getAccountNameSortTypeCondition () {
      return this.page.filter.sortType === analyticsConstants.paidSearchSortTypes.accountNameAsc ||
        this.page.filter.sortType === analyticsConstants.paidSearchSortTypes.accountNameDesc
    },
    async updateStatistics () {
      try {
        await Promise.all([
          !this.isAccountLevel ? this.updateGroupLevelGraphAndSummary() : null,
          !this.isAccountLevel ? this.updateGroupLevelDetails() : null,
          this.isAccountLevel ? this.updateAccountLevelGraphAndSummary() : null,
          this.isAccountLevel ? this.updateAccountLevelDetails() : null
        ])
      } catch (err) {
        this.$toaster.error('Failed to get data from server', {timeout: 8000})
        this.$logger.handleError(err, 'Can\'t update statistics', {filter: this.page.filter, cache: this.cache})
      }
    },
    async updateGroupLevelGraphAndSummary () {
      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getGroupPaidSearchGraphAndSummary',
        {
          reportGroupId: this.reportGroupId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo
        }
      )

      this.summary = {
        ...this.summary,
        ...store.summary.data
      }
      this.bar.items = store.graph.data.items
    },
    async updateAccountLevelGraphAndSummary () {
      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getPaidSearchGraphAndSummary',
        {
          accountId: this.page.filter.accountId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo
        }
      )

      this.summary = {
        ...this.summary,
        ...store.summary.data
      }
      this.bar.items = store.graph.data.items
    },
    async updateGroupLevelDetails () {
      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getGroupPaidSearchDetailsPage',
        {
          reportGroupId: this.reportGroupId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo,
          pageNumber: this.page.filter.pageNumber,
          pageSize: this.page.filter.pageSize,
          sortType: this.page.filter.sortType
        }
      )

      this.table.items = store.detailedData.data.items.map(x => {
        x.account.isAccessAllowed = this.isAccessToAccountAllowed(x.account.accountId)
        return x
      })
      this.table.totalItems = store.detailedData.data.totalItems
    },
    async updateAccountLevelDetails () {
      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getPaidSearchDetailsPage',
        {
          accountId: this.page.filter.accountId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo,
          pageNumber: this.page.filter.pageNumber,
          pageSize: this.page.filter.pageSize,
          sortType: this.page.filter.sortType
        }
      )

      this.table.items = store.detailedData.data.items
      this.table.totalItems = store.detailedData.data.totalItems
    }
  }
}
</script>
