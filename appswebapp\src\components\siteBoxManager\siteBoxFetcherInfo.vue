<template>
  <div class="mt-3">
    <h4>SiteBox Vehicle Fetcher
      <b-btn @click="refreshVehicleFetcher" class="float-right" variant="primary btn-round" size="sm">
        <span class="ion ion-ios-sync"></span><span class="d-none d-sm-inline">&nbsp; Refresh</span>
      </b-btn>
    </h4>
    <ValidationObserver v-slot="{invalid}">
      <b-row class="mb-3">
        <b-col>
          <ValidationProvider name="AccountId" rules="required|numeric|min_value:1" v-slot="{errors}">
            <b-form-group
              :invalid-feedback="errors[0]"
            >
              <b-form-input v-model="vehicleFetcherFilter.accountId" :state="errors[0] ? false : null" placeholder="Account Id"></b-form-input>
            </b-form-group>
          </ValidationProvider>
        </b-col>
        <b-col>
          <ValidationProvider name="Vin" rules="required" v-slot="{errors}">
           <b-form-group
              :invalid-feedback="errors[0]"
            >
              <b-form-input v-model="vehicleFetcherFilter.vin" :state="errors[0] ? false : null" placeholder="VIN"></b-form-input>
            </b-form-group>
          </ValidationProvider>
        </b-col>
        <b-col>
          <b-btn variant="primary" :disabled="invalid" @click="applyVehicleFetcherFilter"><span class="ion ion-ios-search"></span> Search Vehicle</b-btn>
        </b-col>
      </b-row>
    </ValidationObserver>
    <b-table v-if="!isLoadingVehicleFetcherInfo"
      :items="vehicleFetcherItems"
      :fields="getSiteBoxVehicleFetcherTableFields"
      responsive
      bordered
      striped
      hover
    >
      <template #cell(threads)="data">
        <span class="text-success" v-if="data.item.maxTreadsCount > 0">Max: {{data.item.maxTreadsCount}}, Actual: {{data.item.actualTreadsCount}}</span>
        <span v-else class="text-danger">Unavailable</span>
      </template>
    </b-table>
    <div v-else class="py-3 my-3">
      <loader size="lg"/>
    </div>
    <h4 class="mt-4">SiteBox Settings Fetcher
      <b-btn @click="refreshSettingsFetcher" class="float-right" variant="primary btn-round" size="sm">
        <span class="ion ion-ios-sync"></span><span class="d-none d-sm-inline">&nbsp; Refresh</span>
      </b-btn>
    </h4>
    <b-table v-if="!isLoadingSettingsFetcherInfo"
      :items="settingsFetcherItems"
      :fields="getSiteBoxSettingsFetcherTableFields"
      responsive
      striped
      bordered
      hover
    >
      <template #cell(actions)="data">
        <b-btn size="sm" variant="primary" @click="showSettingsRefineSearchModal(data.item.group)"><span class="ion ion-ios-search"></span> Search</b-btn>
      </template>
    </b-table>
    <div v-else class="py-3 my-3">
      <loader size="lg"/>
    </div>
    <!-- Modals -->
    <ValidationObserver ref="vehicleRefineSearchValidator">
      <b-modal
        title="SiteBox Vehicle Refine Search"
        :visible="isVehicleRefineSearchModalVisible"
        @hide="onHideVehicleRefineSearchModal"
      >
        <ValidationProvider name="AccountId" rules="required|numeric|min_value:1" v-slot="{errors}" immediate>
        <detail-row :title-position="'start'" editMode :error="errors[0]">
          <span slot="title">Account Id:</span>
          <b-form-input slot="payload" name="AccountId" v-model="vehicleFetcherFilter.accountId"></b-form-input>
        </detail-row>
        </ValidationProvider>
        <ValidationProvider name="Vin" rules="required" v-slot="{errors}" immediate>
        <detail-row :title-position="'start'" editMode :error="errors[0]">
          <span slot="title">Vin:</span>
          <b-form-input slot="payload" name="Vin" v-model="vehicleFetcherFilter.vin"></b-form-input>
        </detail-row>
        </ValidationProvider>
        <b-table
          class="mt-2"
          v-if="!isLoadingVehicleRefineSearchItems"
          :items="vehicleRefineSearchItems"
          :fields="getVehicleRefineSearchTableFields"
          responsive
          bordered
          striped
        >
        </b-table>
        <loader v-else class="my-3"/>
        <template #modal-footer>
          <b-btn @click="onHideVehicleRefineSearchModal" size="sm">Cancel</b-btn>
          <b-btn variant="primary" size="sm" @click="applyVehicleFetcherFilter">Submit</b-btn>
        </template>
      </b-modal>
    </ValidationObserver>
    <ValidationObserver ref="settingsRefineSearchValidator">
      <b-modal
        title="SiteBox Settings Refine Search"
        :visible="isSettingsRefineSearchModal"
        @hide="onHideSettingsRefineSearchModal"
      >
        <ValidationProvider name="SiteId" rules="required|numeric|min_value:1" v-slot="{errors}">
        <detail-row :title-position="'start'" editMode :error="errors[0]">
          <span slot="title">Site Id:</span>
          <b-form-input slot="payload" name="SiteId" v-model="settingsFetcherFilter.siteId"></b-form-input>
        </detail-row>
        </ValidationProvider>
        <detail-row :title-position="'start'" editMode>
          <span slot="title">Group Name:</span>
          <b-form-select slot="payload" v-model="settingsFetcherFilter.group" :options="getGroupOptions"></b-form-select>
        </detail-row>
        <b-table
          class="mt-2"
          v-if="!isLoadingSettingRefineSearchItems"
          :items="settingsRefineSearchItems"
          :fields="getSettingsRefineSearchTableFields"
          responsive
          bordered
          striped
          fixed
        >
        </b-table>
        <loader v-else class="my-3"/>
        <template #modal-footer>
          <b-btn size="sm" @click="onHideSettingsRefineSearchModal">Cancel</b-btn>
          <b-btn size="sm" variant="primary" @click="applySettingFetcherFilter">Submit</b-btn>
        </template>
      </b-modal>
    </ValidationObserver>
  </div>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import loader from '@/components/_shared/loader'
import SynchronizationMonitorService from '@/services/siteBoxManager/SynchronizationMonitorService'
import moment from 'moment'

export default {
  name: 'sitebox-fetcher',
  data () {
    return {
      isLoadingVehicleFetcherInfo: true,
      isLoadingSettingsFetcherInfo: true,
      isVehicleRefineSearchModalVisible: false,
      isLoadingVehicleRefineSearchItems: false,
      isLoadingSettingRefineSearchItems: false,
      isSettingsRefineSearchModal: false,
      vehicleFetcherFilter: {
        accountId: null,
        vin: null
      },
      settingsFetcherFilter: {
        siteId: null
      },
      vehicleRefineSearchItems: [],
      settingsRefineSearchItems: [],
      vehicleFetcherItems: [],
      settingsFetcherItems: []
    }
  },
  components: {
    loader,
    detailRow
  },
  computed: {
    getVehicleRefineSearchTableFields () {
      return [
        {
          key: 'queueName',
          label: 'Queue Name',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'addedInQueueDateTime',
          label: 'Added in Queue',
          tdClass: 'py-2 align-middle',
          formatter: value => moment(value).format('MM/DD/YYYY hh:mm A')
        },
        {
          key: 'lastProcessedDateTime',
          label: 'Last Processed',
          tdClass: 'py-2 align-middle',
          formatter: value => moment(value).format('MM/DD/YYYY hh:mm A')
        },
        {
          key: 'status',
          label: 'Current Status',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    getSettingsRefineSearchTableFields () {
      return [
        {
          key: 'addedInQueueDateTime',
          label: 'Added in Queue',
          tdClass: 'py-2 align-middle',
          formatter: value => moment(value).format('MM/DD/YYYY hh:mm A')
        },
        {
          key: 'lastProcessedDateTime',
          label: 'Last Processed',
          tdClass: 'py-2 align-middle',
          formatter: value => moment(value).format('MM/DD/YYYY hh:mm A')
        },
        {
          key: 'status',
          label: 'Current Status',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    getSiteBoxVehicleFetcherTableFields () {
      return [
        {
          key: 'queueName',
          label: 'Queue Name',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'messagesInQueueCount',
          label: 'Messages In Queue',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'threads',
          label: 'Threads',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    getSiteBoxSettingsFetcherTableFields () {
      return [
        {
          key: 'groupName',
          label: 'Group Name',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'messagesInQueueCount',
          label: 'Messages In Queue',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'actions',
          label: 'Actions',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    getGroupOptions () {
      return this.settingsFetcherItems.map(x => { return { value: x.group, text: x.groupName } })
    }
  },
  mounted () {
    this.populateVehicleFetcher()
    this.populateSettingsFetcher()
  },
  methods: {
    refreshSettingsFetcher () {
      this.isLoadingSettingsFetcherInfo = true
      this.populateSettingsFetcher()
    },
    refreshVehicleFetcher () {
      this.isLoadingVehicleFetcherInfo = true
      this.populateVehicleFetcher()
    },
    async applyVehicleFetcherFilter () {
      this.isVehicleRefineSearchModalVisible = true
      const isValid = await this.$refs.vehicleRefineSearchValidator.validate()
      if (isValid) {
        this.isLoadingVehicleRefineSearchItems = true
        SynchronizationMonitorService.getVehicleFetchingStatistic(this.vehicleFetcherFilter).then(res => {
          this.vehicleRefineSearchItems = res.data
        }).catch(ex => {
          this.$toaster.exception(ex, 'Something went wrong!')
        }).finally(() => {
          this.isLoadingVehicleRefineSearchItems = false
        })
      }
    },
    applySettingFetcherFilter () {
      this.$refs.settingsRefineSearchValidator.validate('SiteId').then(isValid => {
        if (isValid) {
          this.isLoadingSettingRefineSearchItems = true
          SynchronizationMonitorService.getGallerySettingsFetchingStatistic(this.settingsFetcherFilter).then(res => {
            this.settingsRefineSearchItems = res.data ? [res.data] : []
          }).catch(ex => {
            this.$toaster.exception(ex, 'Something went wrong!')
          }).finally(() => {
            this.isLoadingSettingRefineSearchItems = false
          })
        }
      })
    },
    populateSettingsFetcher () {
      SynchronizationMonitorService.getSiteBoxSettingsFetcherListingInfo().then(res => {
        this.settingsFetcherItems = res.data
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
      }).finally(() => {
        this.isLoadingSettingsFetcherInfo = false
      })
    },
    populateVehicleFetcher () {
      SynchronizationMonitorService.getSiteBoxVehicleFetcherListingInfo().then(res => {
        this.vehicleFetcherItems = res.data
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
      }).finally(() => {
        this.isLoadingVehicleFetcherInfo = false
      })
    },
    onHideVehicleRefineSearchModal () {
      this.isVehicleRefineSearchModalVisible = false
      this.vehicleFetcherFilter = {
        vin: null,
        accountId: null
      }
    },
    showSettingsRefineSearchModal (group) {
      this.isSettingsRefineSearchModal = true
      this.settingsFetcherFilter.group = group
    },
    onHideSettingsRefineSearchModal () {
      this.isSettingsRefineSearchModal = false
      this.settingsFetcherFilter = {
        siteId: null,
        group: null
      }
    }
  }
}
</script>
