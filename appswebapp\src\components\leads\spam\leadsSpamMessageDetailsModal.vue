<template>
  <b-modal
  size='lg'
  :visible='isShowModal'
  @hide='hide'>
    <b-table
    v-if='item'
    :items='[item]'
    :fields="getTableFields"
    responsive
    stacked
    >
      <template #cell(leadType)>
        {{getLeadTypeDesc}}
      </template>
    </b-table>
    <template #modal-footer>
      <b-btn @click='onResubmit'>Resubmit</b-btn>
      <b-btn variant="primary" @click="onDelete">Delete</b-btn>
      <b-btn variant='light' @click="hide">Close</b-btn>
    </template>
  </b-modal>
</template>

<script>
import { leadType } from '@/shared/leads/common'
import moment from 'moment'

export default {
  name: 'leads-spam-message-details-modal',
  props: {
    isShowModal: { type: Boolean, required: true },
    item: { type: Object, required: true }
  },
  computed: {
    getTableFields () {
      return [{key: 'id', label: 'Id:'},
        {key: 'accountId', label: 'AccountId:'},
        {key: 'firstName', label: 'First Name:'},
        {key: 'lastName', label: 'Last Name:'},
        {key: 'email', label: 'Email:'},
        {key: 'leadType', label: 'Lead Type:'},
        {key: 'leadSource', label: 'Lead Source:'},
        {key: 'sourceAccount', label: 'Source Account:'},
        {key: 'dateTimeCreated', label: 'Create Date:', formatter: value => moment(value).format('MM/DD/YYYY hh:mm A')},
        {key: 'filterName', label: 'Filter Name:'},
        {key: 'leadSourceIp', label: 'Lead Source IP:'},
        {key: 'classCSubnet', label: 'Class C Subnet:'},
        {key: 'vinNumber', label: 'Car VIN:'},
        {key: 'phone', label: 'Phone:'},
        {key: 'contactId', label: 'Contact Id:'},
        {key: 'purchaseTimeFrame', label: 'Purchase Time:'},
        {key: 'timeZone', label: 'Time Zone:'},
        {key: 'make', label: 'Make:'},
        {key: 'model', label: 'Model:'},
        {key: 'year', label: 'Year:'},
        {key: 'specialId', label: 'Special Id:'},
        {key: 'subject', label: 'Subject:'},
        {key: 'message', label: 'Message:'}]
    },
    getLeadTypeDesc () {
      if (this.item) {
        let type = Object.values(leadType).find(x => x.value === this.item.leadType)

        if (type) {
          return type.label
        }
      }

      return ''
    }
  },
  methods: {
    hide () {
      this.$emit('hide')
    },
    onResubmit () {
      this.$store.dispatch('leads/reprocessSpamMessage', this.item.id).then(res => {
        if (res.data) {
          this.$toaster.success('Spam Message Successfully Reprocess')
        } else {
          this.$toaster.error(`Cannot reprocess spam message`)
        }
      }).catch(ex => {
        this.$toaster.error(`Cannot reprocess spam message. Message: ${ex.message}`)
        this.$logger.handleError(ex, `Cannot reprocess spam message with id: ${this.item.id}`)
      }).finally(() => {
        this.hide()
      })
    },
    onDelete () {
      this.$store.dispatch('leads/deleteSpamMessage', this.item.id).then(res => {
        if (res.data) {
          this.$toaster.success('Spam Message Successfully Deleted')
        } else {
          this.$toaster.error(`Cannot delete spam message`)
        }
      }).catch(ex => {
        this.$toaster.error(`Cannot delete spam message. Message: ${ex.message}`)
        this.$logger.handleError(ex, `Cannot delete spam message with id: ${this.item.id}`)
      }).finally(() => {
        this.hide()
      })
    }
  }
}
</script>
