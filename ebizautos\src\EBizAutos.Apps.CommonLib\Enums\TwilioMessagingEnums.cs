﻿using System;
using System.ComponentModel;
using EBizAutos.CommonLib.Extensions;

namespace EBizAutos.Apps.CommonLib.Enums {
	public static class TwilioMessagingEnums {
		public enum MessageStatusEnum : byte {
			[Description("undefined")]
			Undefined = 0,

			[Description("undelivered")]
			Undelivered = 1,

			[Description("sent")]
			Sent = 2,

			[Description("sending")]
			Sending = 3,

			[Description("receiving")]
			Receiving = 4,

			[Description("received")]
			Received = 5,

			[Description("queued")]
			Queued = 6,

			[Description("failed")]
			Failed = 7,

			[Description("delivered")]
			Delivered = 8
		}

		public enum TwilioErrorCodeEnum : int {
			Undefined = 0,
			FromNumberNotAMobileNumber = 21606,
			ToNumberNotAMobileNumber = 21614,
			PhoneNumberAlreadyInTheMessagingService = 21710,
			MessagingServiceNumberPoolSizeLimitReached = 21714,
			TheResourceWasNotFound = 20404
		}

		public static MessageStatusEnum GetMessageStatusByStrValue(string enumStrValue) {
			foreach (MessageStatusEnum enumValue in Enum.GetValues(typeof(MessageStatusEnum))) {
				if (enumValue.GetDescription().ToLower() == enumStrValue) {
					return enumValue;
				}
			}

			return MessageStatusEnum.Undefined;
		}
	}
}