<template>
  <div v-if="!isLoading && menuItems">
    <b-row class="mb-3">
      <b-col>
        <h4>Page Builder</h4>
      </b-col>
      <b-col>
        <b-dropdown class="float-right" boundary='viewport' ref="addPageDropDown" size="md" variant="outline-secondary btn-primary" no-caret>
          <template slot="button-content">
            <span class="ion ion-ios-add"></span><span class="d-none d-md-inline">&nbsp; Add Page</span>
          </template>
          <div class="py-3 d-flex justify-content-center flex-column">
            <b-form-group label="Page Type:" class="mx-2" label-for="dropdown-page-type">
              <b-form-select
                id="dropdown-page-type"
                size="sm"
                v-model="createNewPageEvent.navigationType"
                :options="getNavigationOptions"
                @input="onInputNavigationType"
              ></b-form-select>
            </b-form-group>
            <b-form-group label="Nav Label:" class="mx-2" label-for="dropdown-form-label">
              <b-form-input
                id="dropdown-form-label"
                v-model="createNewPageEvent.navLabel"
                size="sm"
              ></b-form-input>
            </b-form-group>
            <b-button variant="primary" class="mx-4"  size="sm" @click="onCreateNewPage(0, 'addPageDropDown')">Add Page</b-button>
          </div>
        </b-dropdown>
      </b-col>
    </b-row>
    <b-card>
      <draggable
        class="col"
        v-bind="draggableOptions"
        v-model="menuItems"
        @start="isDragging = true"
        @end="isDragging = false"
        @change="changeNavigationMenuItemOrdering"
        :move="checkMove"
        :class="{on: isDragging, off: !isDragging}"
        handle=".menu-item-container"
      >
        <b-card
          @click="menuItemIndex = index"
          v-for="(menuItem, index) in menuItems"
          :key="menuItem.id"
          header-tag="header"
          footer-tag="footer"
          class="bg-light mt-3"
        >
          <template #header>
            <div class="d-flex">
              <div class="menu-item-container w-100 d-flex">
                <div class="bg-light rounded d-none d-sm-flex custom-size">
                  <i v-if="menuItem.siteNavigationType === pageNavigationTypes.home.value"
                    class='ion ion-ios-home m-0 h5 opacity-75'
                  ></i>
                  <i v-else
                    class='ion ion-ios-menu m-0 h5 opacity-100'
                  ></i>
                </div>
                <div class="pt-3 pl-3">
                  <span class="font-weight-bold custom-truncate">{{menuItem.name}}</span>
                </div>
              </div>
              <div class="ml-auto pt-3 pl-3 d-none d-xl-flex border-left">
                <label class="switcher">
                    <input type="checkbox" @change="onVisibleChange(menuItem)"  v-model="menuItem.isVisible" class="switcher-input">
                    <span class="switcher-indicator">
                      <span class="switcher-yes"></span>
                      <span class="switcher-no"></span>
                    </span>
                    <span class="switcher-label">Visible</span>
                </label>
              </div>
              <div class="pt-3 pl-3 d-none d-xl-flex border-left" v-if="menuItem.siteNavigationType !== pageNavigationTypes.customLink.value">
                <label class="switcher">
                    <input type="checkbox" :disabled="menuItem.siteNavigationType === pageNavigationTypes.home.value" @click="onPublishedChanged(menuItem.pageId, !menuItem.isPublished)" v-model="menuItem.isPublished" class="switcher-input">
                    <span class="switcher-indicator">
                      <span class="switcher-yes"></span>
                      <span class="switcher-no"></span>
                    </span>
                    <span class="switcher-label">Publish</span>
                </label>
              </div>
              <div class="pt-3 px-3 d-none d-xl-flex border-left text-nowrap" v-if="getPageUrl(menuItem)">
                <b-link class="text-primary"
                  :href="getPageUrl(menuItem)"
                >
                  <u>View Page</u>
                </b-link>
              </div>
              <div class="pt-3 pb-2 px-3 d-none d-xl-flex border-left" v-if="menuItem.siteNavigationType !== pageNavigationTypes.home.value">
                <b-btn :to="{ path: getEditPageUrl(menuItem.pageId, menuItem.id) }" class="text-nowrap" size="sm"><font-awesome-icon icon="pencil-alt" /> <span>Edit Page</span></b-btn>
                <font-awesome-icon @click="onDeleteNavigation(menuItem.id)" class="ml-4 mt-1" style="cursor: pointer;" icon="trash"/>
              </div>
              <div class="ml-auto pt-3 pl-3 d-block d-xl-none border-left">
                <b-dropdown variant="outline-secondary icon-btn btn-round"  class="mb-2" size="sm" no-caret boundary='viewport'>
                  <template slot="button-content">
                    <i class="ion ion-ios-more m-0"></i><span class="sr-only">Manage</span>
                  </template>
                  <b-dropdown-item @click="menuItem.isVisible = !menuItem.isVisible">{{menuItem.isVisible ? "Hide" : "Show"}} Page</b-dropdown-item>
                    <b-dropdown-item v-if="menuItem.siteNavigationType !== pageNavigationTypes.home.value" :to="{ path: getEditPageUrl(menuItem.pageId, menuItem.id) }">Edit Page</b-dropdown-item>
                    <b-dropdown-item class="text-info" v-if="getPageUrl(menuItem)" :href="getPageUrl(menuItem)">View Page</b-dropdown-item>
                    <b-dropdown-item v-if="menuItem.siteNavigationType !== pageNavigationTypes.home.value" class="text-danger" @click="onDeleteNavigation(menuItem.id)">Delete</b-dropdown-item>
                </b-dropdown>
              </div>
            </div>
          </template>
          <div v-if="menuItem.subMenuItems && menuItem.subMenuItems.length > 0">
            <draggable
              class="col"
              v-bind="draggableOptions"
              v-model="menuItem.subMenuItems"
              @start="isDragging = true"
              @end="isDragging = false"
              @change="changeNavigationMenuItemOrdering"
              :class="{on: isDragging, off: !isDragging}"
              handle=".sub-page-container"
            >
              <div class="d-flex bg-white mt-2" @click="subMenuItemIndex = index" v-for="(subMenuItem, index) in menuItem.subMenuItems" :key="subMenuItem.id">
                <div class="sub-page-container w-100 d-flex">
                  <div class="custom-bg-color d-none d-sm-flex custom-size rounded">
                    <i
                      class='ion ion-ios-menu m-0 h5 opacity-100'
                    ></i>
                  </div>
                  <div class="pt-3 pl-3">
                    <span class="font-weight-bold custom-truncate">{{subMenuItem.name}}</span>
                  </div>
                </div>
                <div class="ml-auto pt-3 pl-3 d-none d-xl-flex border-left">
                  <label class="switcher">
                      <input type="checkbox" @change="onVisibleChange(subMenuItem)" v-model="subMenuItem.isVisible" class="switcher-input">
                      <span class="switcher-indicator">
                        <span class="switcher-yes"></span>
                        <span class="switcher-no"></span>
                      </span>
                      <span class="switcher-label">Visible</span>
                  </label>
                </div>
                <div class="pt-3 pl-3 d-none d-xl-flex border-left" v-if="subMenuItem.siteNavigationType !== pageNavigationTypes.customLink.value">
                  <label class="switcher">
                      <input type="checkbox" @click="onPublishedChanged(subMenuItem.pageId, !subMenuItem.isPublished)" v-model="subMenuItem.isPublished" class="switcher-input">
                      <span class="switcher-indicator">
                        <span class="switcher-yes"></span>
                        <span class="switcher-no"></span>
                      </span>
                      <span class="switcher-label">Publish</span>
                  </label>
                </div>
                <div class="pt-3 px-3 d-none d-xl-flex border-left text-nowrap" v-if="getPageUrl(subMenuItem)">
                  <b-link class="text-primary"
                    :href="getPageUrl(subMenuItem)"
                  >
                    <u>View Page</u>
                  </b-link>
                </div>
                <div class="pt-3 pb-2 px-3 d-none d-xl-flex border-left">
                  <b-btn :to="{ path: getEditPageUrl(subMenuItem.pageId, subMenuItem.id) }" class="text-nowrap" size="sm"><font-awesome-icon icon="pencil-alt" /> <span>Edit Page</span></b-btn>
                  <font-awesome-icon  @click="onDeleteNavigation(subMenuItem.id)" class="ml-3 mt-1 mr-2" style="cursor: pointer;" icon="trash" />
                </div>
                <div class="custom-size ml-auto d-flex d-xl-none border-left">
                  <b-dropdown variant="outline-secondary icon-btn btn-round" size="sm" no-caret boundary='viewport'>
                    <template slot="button-content">
                      <i class="ion ion-ios-more m-0"></i><span class="sr-only">Manage</span>
                    </template>
                    <b-dropdown-item @click="subMenuItem.isVisible = !subMenuItem.isVisible">{{subMenuItem.isVisible ? "Hide" : "Show"}} Page</b-dropdown-item>
                    <b-dropdown-item :to="{ path: getEditPageUrl(subMenuItem.pageId, subMenuItem.id) }">Edit Page</b-dropdown-item>
                    <b-dropdown-item class="text-info" v-if="getPageUrl(subMenuItem)" :href="getPageUrl(subMenuItem)">View Page</b-dropdown-item>
                    <b-dropdown-item class="text-danger" @click="onDeleteNavigation(subMenuItem.id)">Delete</b-dropdown-item>
                  </b-dropdown>
                </div>
                <input type="text" hidden :value="index+1" @change="onSubMenuItemsIndexChanged($event, index + 1, page.id)">
              </div>
            </draggable>
          </div>
          <div class="d-flex mt-3 justify-content-center">
            <b-dropdown boundary='viewport' size="sm" :ref="'dropDown' + menuItem.id" variant="outline-secondary btn-primary btn-round" no-caret>
              <template slot="button-content">
                <span class="ion ion-ios-add"></span><span class="d-none d-md-inline">&nbsp; Add Sub Page</span>
              </template>
              <div class="py-3 d-flex justify-content-center flex-column">
                <b-form-group label="Page Type:" class="mx-2" label-for="dropdown-page-type">
                  <b-form-select
                    id="dropdown-page-type"
                    size="sm"
                    v-model="createNewPageEvent.navigationType"
                    :options="getNavigationOptions"
                    @input="onInputNavigationType"
                  ></b-form-select>
                </b-form-group>
                <b-form-group label="Nav Label:" class="mx-2" label-for="dropdown-form-label">
                  <b-form-input
                    id="dropdown-form-label"
                    v-model="createNewPageEvent.navLabel"
                    size="sm"
                  ></b-form-input>
                </b-form-group>
                <b-button variant="primary" class="mx-4" size="sm" @click="onCreateNewPage(menuItem.id, 'dropDown' + menuItem.id)">Add a Sub Page</b-button>
              </div>
            </b-dropdown>
          </div>
          <input type="text" hidden :value="index+1" @change="onMenuItemsIndexChanged($event, index + 1)">
        </b-card>
      </draggable>
      <div class="d-flex justify-content-center">
        <b-dropdown class="mt-3" boundary='viewport' ref="addTopLevelPageDropDown" size="sm" variant="outline-secondary btn-primary" no-caret>
          <template slot="button-content">
            <span class="ion ion-ios-add"></span><span class="d-none d-md-inline">&nbsp; Add a Top Level Page</span>
          </template>
          <div class="py-3 d-flex justify-content-center flex-column">
            <b-form-group label="Page Type:" class="mx-2" label-for="dropdown-page-type">
              <b-form-select
                id="dropdown-page-type"
                size="sm"
                v-model="createNewPageEvent.navigationType"
                :options="getNavigationOptions"
                @input="onInputNavigationType"
              ></b-form-select>
            </b-form-group>
            <b-form-group label="Nav Label:" class="mx-2" label-for="dropdown-form-label">
              <b-form-input
                id="dropdown-form-label"
                v-model="createNewPageEvent.navLabel"
                size="sm"
              ></b-form-input>
            </b-form-group>
            <b-button variant="primary" class="mx-4" size="sm" @click="onCreateNewPage(0, 'addTopLevelPageDropDown')">Add Top Level Page</b-button>
          </div>
        </b-dropdown>
      </div>
    </b-card>
  </div>
  <div v-else-if="!isErrorOccurred">
    <loader class="mt-5" size="lg"/>
  </div>
  <div v-else class="d-flex justify-content-center">
    <h4 class="text-muted">Something went wrong! Please try again later</h4>
  </div>
</template>

<script>
import draggable from 'vuedraggable'
import loader from '@/components/_shared/loader'
import { pageNavigationTypes } from '@/shared/website/constants'

export default {
  name: 'website-page-builder',
  props: {
    accountId: {type: Number, required: true}
  },
  data () {
    return {
      text: 'text',
      isLoading: true,
      isDragging: true,
      pageNavigationTypes,
      createNewPageEvent: {
        parentId: 0,
        navigationType: pageNavigationTypes.contactUs.value,
        navLabel: pageNavigationTypes.contactUs.label
      },
      menuItems: null,
      draggableOptions: {
        animation: 150
      },
      isErrorOccurred: false
    }
  },
  components: {
    'draggable': draggable,
    'loader': loader
  },
  computed: {
    getNavigationOptions () {
      let options = []
      Object.values(pageNavigationTypes).map(x => {
        if (x.value === pageNavigationTypes.blog.value && !this.isBlogNavigationTypeExist()) {
          options.push({
            value: x.value,
            text: x.label
          })
        }
        if (x.value !== pageNavigationTypes.blog.value && x.isEnabledToManage && this.isNavigationTypeEnabledToCreate(x.value)) {
          options.push({
            value: x.value,
            text: x.label
          })
        }
      })

      return options.sort((a, b) => a.text.localeCompare(b.text))
    }
  },
  created () {
    this.populateData()
  },
  methods: {
    checkMove (event) {
      return (this.menuItems[event.draggedContext.index] || {}).siteNavigationType !== pageNavigationTypes.home.value &&
        (this.menuItems[event.draggedContext.futureIndex] || {}).siteNavigationType !== pageNavigationTypes.home.value
    },
    onMenuItemsIndexChanged (newVal, oldVal) {
      if (!Number.isInteger(+newVal.target.value)) {
        return
      }
      let newIndex = (+newVal.target.value) - 1
      let oldIndex = oldVal - 1
      if (newIndex === 0 || oldIndex === 0) {
        return
      }
      let arrayItem = this.menuItems[oldIndex]
      this.menuItems.splice(oldIndex, 1)
      this.menuItems.splice(newIndex, 0, arrayItem)
    },
    getPageUrl (navItem) {
      return navItem.siteNavigationType !== pageNavigationTypes.customLink.value ? navItem.pageUrl : navItem.externalUrl
    },
    onSubMenuItemsIndexChanged (newVal, oldVal, id) {
      if (!Number.isInteger(+newVal.target.value)) {
        return
      }

      let res = this.menuItems.find(x => x.id === id)
      if (res) {
        let newIndex = (+newVal.target.value) - 1
        let oldIndex = oldVal - 1
        let arrayItem = this.res.subMenuItems[oldIndex]
        this.res.subMenuItems.splice(oldIndex, 1)
        this.res.subMenuItems.splice(newIndex, 0, arrayItem)
      }
    },
    onInputNavigationType (value) {
      let res = this.getNavigationOptions.find(x => x.value === value)
      this.createNewPageEvent.navLabel = (res || {}).text || ''
    },
    getEditPageUrl (pageId, navId) {
      return `/website/${this.accountId}/page/${navId}/edit`
    },
    closeDropDown (ref) {
      if (Array.isArray(this.$refs[ref])) {
        this.$refs[ref][0].hide(true)
      } else if (this.$refs[ref]) {
        this.$refs[ref].hide(true)
      }
    },
    onCreateNewPage (parentId, ref) {
      this.closeDropDown(ref)
      this.createNewPageEvent.parentId = parentId || 0
      this.$store.dispatch('website/createNewPage', { accountId: this.accountId, data: this.createNewPageEvent }).then(res => {
        if (res.data) {
          this.$toaster.success('New Page Added Successfully')
        }
      }).catch(ex => {
        this.$toaster.error('Cannot add new page. Something went wrong!')
        this.$logger.handleError(ex, 'Cannot create new page', this.createNewPageEvent)
      }).finally(() => {
        this.setDefaultCreateNewPageValues()
        this.populateData()
      })
    },
    onDeleteNavigation (navId) {
      this.$store.dispatch('website/deleteNavigation', { accountId: this.accountId, navId: navId }).then(res => {
        this.$toaster.success('Page Deleted Successfully')
      }).catch(ex => {
        this.$toaster.error('Cannot delete page. Something went wrong!')
        this.$logger.handleError(ex, 'Cannot delete page')
      }).finally(() => {
        this.populateData()
      })
    },
    onVisibleChange (navItem) {
      this.$store.dispatch('website/changePageNavigationVisible', { accountId: this.accountId, navId: navItem.id, apiData: { isVisible: navItem.isVisible } }).then(res => {
        this.$toaster.success('Page Visible Changed Successfully')
      }).catch(ex => {
        this.$toaster.error('Cannot change page visible. Something went wrong!')
        this.$logger.handleError(ex, 'Cannot change page visible')
      }).finally(() => {
        this.populateData()
      })
    },
    onPublishedChanged (pageId, isPublished) {
      this.$store.dispatch('website/changePagePublished', { accountId: this.accountId, pageId: pageId, apiData: { published: isPublished } }).then(res => {
        this.$toaster.success('Page Published Changed Successfully')
      }).catch(ex => {
        this.$toaster.error('Cannot change page published. Something went wrong!')
        this.$logger.handleError(ex, 'Cannot change page published')
      }).finally(() => {
        this.populateData()
      })
    },
    changeNavigationMenuItemOrdering (value) {
      this.$store.dispatch('website/changeNavigationMenuItemOrdering', { accountId: this.accountId, navId: value.moved.element.id, data: { siteId: this.accountId, menuItems: this.menuItems } }).then(res => {
        this.$toaster.success('Site Navigation Settings Updated Successfully')
      }).catch(ex => {
        this.$toaster.error('Cannot update site navigation settings. Something went wrong!')
        this.$logger.handleError(ex, 'Cannot update site navigation settings')
      }).finally(() => {
        this.populateData()
      })
    },
    isBlogNavigationTypeExist () {
      let res = this.menuItems.find(x => x.siteNavigationType === pageNavigationTypes.blog.value)
      if (res) {
        return true
      }
      for (let menuItem of this.menuItems) {
        res = menuItem.subMenuItems ? menuItem.subMenuItems.find(x => x.siteNavigationType === pageNavigationTypes.blog.value) : null
        if (res) {
          return true
        }
      }
      return false
    },
    isNavigationTypeEnabledToCreate (type) {
      return type !== pageNavigationTypes.home.value && type !== pageNavigationTypes.siteMap.value
    },
    setDefaultCreateNewPageValues () {
      this.createNewPageEvent.parentId = 0
      this.createNewPageEvent.navigationType = pageNavigationTypes.contactUs.value
      this.createNewPageEvent.navLabel = pageNavigationTypes.contactUs.label
    },
    populateData () {
      this.$store.dispatch('website/getSiteNavigationSettings', { accountId: this.accountId }).then(res => {
        this.menuItems = res.data.menuItems
      }).catch(ex => {
        this.$toaster.error('Something went wrong! Please reload page')
        this.$logger.handleError(ex, `Cannot get site navigation settings for site id: ${this.accountId}`)
        this.isErrorOccurred = true
      }).finally(() => {
        this.isLoading = false
      })
    }
  }
}
</script>

<style scoped>
.custom-bg-color {
  background-color: #E4E6E6;
}

.custom-size {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-truncate {
  max-width: 250px;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@media(max-width: 1400px) {
  .custom-truncate {
    max-width: 150px;
  }
}

@media(max-width: 1185px) {
  .custom-truncate {
    max-width: 300px;
  }
}

@media(max-width: 960px) {
  .custom-truncate {
    max-width: 220px;
  }
}

@media(max-width: 578px) {
  .custom-truncate {
    max-width: 120px;
  }
}

@media(max-width: 360px) {
  .custom-truncate {
    max-width: 75px;
  }
}

@media(max-width: 320px) {
  .custom-truncate {
    max-width: 45px;
  }
}

</style>
