import BaseService from '../BaseService'

class InventoryService extends BaseService {
  fetchInventoryBuilderModel (accountId, inventoryFilters) {
    return this.axios.get(`/api/inventory/${accountId}`, { params: inventoryFilters })
  };
  fetchVehicleConditionsStatistic (accountId, vStatus, search) {
    let params = { vStatus, search }
    return this.axios.get(`/api/inventory/${accountId}/conditions`, { params })
  };
  fetchEmailAdInventoryBuilderModel (accountId, inventoryFilters) {
    return this.axios.get(`/api/inventory/email_ad/${accountId}/vehicles`, { params: inventoryFilters })
  };
  fetchEmailAdVehicleConditionsStatistic (accountId, vStatus, search) {
    let params = { vStatus, search }
    return this.axios.get(`/api/inventory/email_ad/${accountId}/conditions`, { params })
  };
  fetchMakes (accountId, vStatus) {
    return this.axios.get(`/api/inventory/${accountId}/makes?`, { params: { vStatus } })
  };
  fetchVehicleStatuses (accountId) {
    return this.axios.get(`/api/inventory/${accountId}/vehiclestatuses`)
  };
  fetchVehiclesForVStatus (accountId, vStatus) {
    return this.axios.get(`/api/inventory/${accountId}/vehiclestatuses`, {params: { vStatus }})
  };
  getVehicleBaseDetailed (accountId, vin) {
    return this.axios.get(`/api/inventory/${accountId}/${vin}/base`)
  };
  getVehiclePhotoUploadPath (accountId, vin) {
    return `/api/inventory/${accountId}/${vin}/photo`
  };
  getVehicleVideoUploadPath (accountId, vin) {
    return `/api/inventory/${accountId}/${vin}/video`
  };
  getAlertsListing (accountId, alertFilters) {
    return this.axios.get(`/api/inventory/${accountId}/alerts/listing`, { params: alertFilters })
  };
  getAlertsTotals (accountId) {
    return this.axios.get(`/api/inventory/${accountId}/alerts/totals`)
  };
  getVehiclesLostInSync (filter) {
    return this.axios.get('/api/inventory/integrity_report/lost_vehicles', { params: filter })
  };
  deleteVehicleLostInSync (vehicle) {
    return this.axios.post('/api/inventory/integrity_report/lost_vehicles/delete', vehicle)
  };
  deleteAllVehicleLostInSync () {
    return this.axios.post('/api/inventory/integrity_report/lost_vehicles/all/delete')
  };
  getVehicleCarfaxReport (accountId, vin) {
    return this.axios.get(`/api/inventory/${accountId}/icr/${vin}`)
  };
  forceVehicleCarfaxReport (accountId, vin) {
    return this.axios.post(`/api/inventory/${accountId}/icr/${vin}/force`)
  };
  forceAccountVehiclesCarfaxReport (accountId) {
    return this.axios.post(`/api/inventory/${accountId}/icr/force`)
  };
  getCarfaxIntegrityReports (filters) {
    return this.axios.get(`/api/inventory/integrity_report/carfax`, { params: filters })
  };
  getCarfaxIntegrityReportActivity () {
    return this.axios.get('/api/inventory/integrity_report/carfax/activity')
  };
  refreshCarfaxIntegrityReports () {
    return this.axios.get('/api/inventory/integrity_report/carfax/refresh_information')
  };
  getVehiclePostProcessingTaskTypes () {
    return this.axios.get(`/api/inventory/vehicle_post_processing/task_types`)
  };
  getInventoryServiceSettings () {
    return this.axios.get('/api/inventory/services/settings')
  };
  updateInventoryServiceSettings (serviceSettings) {
    return this.axios.post('/api/inventory/services/settings', serviceSettings)
  };
  getUserInventoryGeneralData (accountId) {
    return this.axios.get(`/api/inventory/general_data`, {params: {accountId: accountId}})
  }
  getInventoryAccountsListing (filter) {
    return this.axios.get('/api/inventory/settings', { params: filter })
  };
  updateInventoryEmailPageSettings (accountId, data) {
    return this.axios.post(`/api/inventory/${accountId}/settings/email_page`, data)
  };
  generateEmailAd (accountId, data) {
    return this.axios.post(`/api/inventory/email_ad/${accountId}/generate`, data)
  };
}

export default new InventoryService()
