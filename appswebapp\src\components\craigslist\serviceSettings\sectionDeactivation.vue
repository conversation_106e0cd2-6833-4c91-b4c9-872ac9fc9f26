<template>
<div>
<div v-if='isReady' class='mb-3'>
  <div class="border-bottom">
    <b-row>
      <b-col><h6 class="float-left">Deactivation Settings</h6></b-col>
      <b-col>
      <b-btn v-if="!isViewMode" size="sm" class="float-right d-none d-sm-block fixed-sizes ml-2" @click="cancel()" :disabled='isDisabled'>Cancel</b-btn>
      <b-btn v-if="isViewMode" variant="secondary" size="sm" class="fixed-sizes d-none d-sm-block mb-2 float-right" @click="setEditMode()" :disabled='isDisabled'><font-awesome-icon icon="pencil-alt" size="sm" /> <span class="btn-title">Edit</span></b-btn>
      <b-btn v-else variant="primary" size="sm" class="fixed-sizes d-none d-sm-block mb-2 float-right " @click="saveSettings()" :disabled='isDisabled'><font-awesome-icon icon="cloud-upload-alt" /> <span class="btn-title">Save</span></b-btn>
      </b-col>
    </b-row>
  </div>
  <div>
  <div v-if='isViewMode' class='mt-2'>
    <multiInput
      title="Post presence phrases"
      :data='deactivationSettings.phrasesThatIndicateExistence'
      :viewMode='true'
    />
    <multiInput
      title="Post absence phrases"
      :data='deactivationSettings.phrasesThatIndicateAbsence'
      :viewMode='true'
    />
    <multiInput
      title="Emails to notify if uncertain"
      :data='deactivationSettings.emailsToNotifyWhenNoIndication'
      :viewMode='true'
    />
    <multiInput
      title="Emails to notify if vehicle removed"
      :data='deactivationSettings.emailsToNotifyWhenVehicleDeleted'
      :viewMode='true'
    />
    <b-row class='mt-2'>
        <b-col sm="3">
          <label class="text-muted" size="sm">Deleted vehicles notification frequency:</label>
        </b-col>
        <b-col sm="9">
          <multiselect size="sm" class='custom-servicesettings-craigslist ml-1' :options='hoursOptions' v-model='hourSelected' label='text' :multiple="false" :preselect-first="true" disabled></multiselect>
        </b-col>
      </b-row>
  </div>
  <div v-else class='mt-2'>
    <ValidationObserver ref="validator">
      <multiInput
        title="Post presence phrases"
        :data='deactivationSettingsToUpdate.phrasesThatIndicateExistence'
        :viewMode='false'
        :input='updatePhrasesThatIndicateExistence'
      />
      <multiInput
        title="Post absence phrases"
        :data='deactivationSettingsToUpdate.phrasesThatIndicateAbsence'
        :viewMode='false'
        :input='updatePhrasesThatIndicateAbsence'
      />
      <multiInput
        title="Emails to notify if uncertain"
        :data='deactivationSettingsToUpdate.emailsToNotifyWhenNoIndication'
        :validateRules='emailValidateRules'
        :viewMode='false'
        :input='updateEmailsToNotifyWhenNoIndication'
      />
      <multiInput
        title="Emails to notify if vehicle removed"
        :data='deactivationSettingsToUpdate.emailsToNotifyWhenVehicleDeleted'
        :validateRules='emailValidateRules'
        :viewMode='false'
        :input='updateEmailsToNotifyIfVehicleRemoved'
      />
      <b-row class='mt-2'>
          <b-col sm="3">
            <label class="text-muted" size="sm">Deleted vehicles notification frequency:</label>
          </b-col>
          <b-col sm="9">
            <multiselect size="sm" class='custom-servicesettings-craigslist ml-1' :allowEmpty='false' :options='hoursOptions' v-model='hourSelected' label='text' :multiple="false" :preselect-first="true"></multiselect>
          </b-col>
      </b-row>
    </ValidationObserver>
  </div>
  </div>
  <b-btn v-if="isViewMode" variant="secondary" size="sm" class="float-right w-100 d-block d-sm-none my-2" @click="setEditMode()" :disabled='isDisabled'><font-awesome-icon icon="pencil-alt" size="sm" /> <span class="btn-title">Edit</span></b-btn>
  <b-btn v-else variant="primary" size="sm" class="float-right w-100 d-block d-sm-none my-2" @click="saveSettings()" :disabled='isDisabled'><font-awesome-icon icon="cloud-upload-alt" /> <span class="btn-title">Save</span></b-btn>
  <b-btn v-if="!isViewMode" size="sm" class="w-100 d-block d-sm-none mt-2" @click="cancel()" :disabled='isDisabled'>Cancel</b-btn>
</div>
</div>
</template>

<script>
import { mapGetters } from 'vuex'
import Multiselect from 'vue-multiselect'
import multiInput from './helper/multiInput'
import globals from '../../../globals'

export default {
  name: 'seaction-deactivation',
  props: {
    isDisabled: {
      type: Boolean,
      required: true
    }
  },
  data () {
    return {
      isReady: false,
      isViewMode: true,
      emailValidateRules: 'required|email',
      deactivationSettingsToUpdate: {},
      hourSelected: {
        value: 1,
        text: 'Every 1 hour'
      },
      hoursOptions: [{
        value: 1,
        text: 'Every 1 hour'
      }]
    }
  },
  created () {
    this.populateData()
  },
  components: {
    'multiselect': Multiselect,
    'multiInput': multiInput
  },
  computed: {
    ...mapGetters('craigslistServiceSettings', ['deactivationSettings', 'blackListSettings'])
  },
  methods: {
    setEditMode () {
      this.isViewMode = false
    },
    saveSettings () {
      this.$refs.validator.validate().then(isValid => {
        if (isValid) {
          this.isViewMode = true
          this.deactivationSettingsToUpdate.deletedVehiclesNotificationFrequencyInHours = this.hourSelected.value
          this.$store.commit('craigslistServiceSettings/setDeactivationSettings', this.deactivationSettingsToUpdate)
          this.updateSettingsData()
        }
      })
    },
    cancel () {
      this.populateData()
      this.isViewMode = true
    },
    populateData () {
      this.deactivationSettingsToUpdate = globals().getClonedValue(this.deactivationSettings)
      for (let i = 2; i <= 24; i++) {
        this.hoursOptions.push({
          value: i,
          text: `Every ${i} hours`
        })
      }
      this.setHoursSelected()
      this.isReady = true
    },
    updateSettingsData () {
      const data = {
        craigslistServiceDeactivationSettings: this.deactivationSettingsToUpdate,
        craigslistBlackListSettings: this.blackListSettings
      }

      this.$emit('updateSettings', data)
    },
    setHoursSelected () {
      this.hourSelected = this.hoursOptions.find(x => x.value === this.deactivationSettings.deletedVehiclesNotificationFrequencyInHours)
    },
    updateEmailsToNotifyIfVehicleRemoved (data) {
      this.deactivationSettingsToUpdate.emailsToNotifyWhenVehicleDeleted = data
    },
    updateEmailsToNotifyWhenNoIndication (data) {
      this.deactivationSettingsToUpdate.emailsToNotifyWhenNoIndication = data
    },
    updatePhrasesThatIndicateAbsence (data) {
      this.deactivationSettingsToUpdate.phrasesThatIndicateAbsence = data
    },
    updatePhrasesThatIndicateExistence (data) {
      this.deactivationSettingsToUpdate.phrasesThatIndicateExistence = data
    }
  }
}
</script>

<style scoped>
 @media(min-width: 1600px) {
    .custom-servicesettings-craigslist {
      width: 25%;
    }
 }

 @media(max-width: 1600px) {
    .custom-servicesettings-craigslist {
      width: 30%;
    }
 }

 @media(max-width: 1200px) {
    .custom-servicesettings-craigslist {
      width: 40%;
    }
 }

 @media(max-width: 800px) {
    .custom-servicesettings-craigslist {
      width: 50%;
    }
 }
 @media(max-width: 400px) {
    .custom-servicesettings-craigslist {
      width: 100%;
    }
 }
</style>
