<template>
  <common-analytics-table
    :tableItems="tableItems"
    :totalItems="totalItems"
    :tableFields="tableFields"
    :isPaginated="true"
    :sortType.sync="sortTypeProp"
    :pageNumber.sync="pageNumberProp"
    :pageSize.sync="pageSizeProp"
  ></common-analytics-table>
</template>

<script>
import analyticsConstants from '../../../shared/analytics/constants'

export default {
  name: 'vehicles-by-vehicle-table',
  props: {
    tableItems: { type: Array, required: true },
    totalItems: { type: Number, require: true },
    pageNumber: { type: Number, required: true },
    pageSize: { type: Number, required: true },
    sortType: { type: Number, required: true },
    showAbsoluteUrl: { type: Boolean, default: false }
  },
  components: {
    'common-analytics-table': () => import('./commonAnalyticsTable.vue')
  },
  computed: {
    tableFields () {
      let fields = [
        {
          key: 'vehicleLabel',
          label: 'Vehicle',
          sortable: true,
          sortTypeAsc: analyticsConstants.vehicleSortType.naturalAsc,
          sortTypeDesc: analyticsConstants.vehicleSortType.naturalDesc
        },
        {
          key: 'pageViews',
          label: 'Page Views',
          sortable: true,
          sortTypeAsc: analyticsConstants.vehicleSortType.pageViewsAsc,
          sortTypeDesc: analyticsConstants.vehicleSortType.pageViewsDesc,
          formatter: val => this.$locale.formatNumber(val)
        }, {
          key: 'avgTimeOnPage',
          label: 'Avg. Time on Page',
          sortable: true,
          sortTypeAsc: analyticsConstants.vehicleSortType.avgTimeOnPageAsc,
          sortTypeDesc: analyticsConstants.vehicleSortType.avgTimeOnPageDesc,
          formatter: val => this.$locale.getSecondsDurationFormatted(val)
        }, {
          key: 'timeOnPage',
          label: 'Total Time on Page',
          sortable: true,
          sortTypeAsc: analyticsConstants.vehicleSortType.totalTimeOnPageAsc,
          sortTypeDesc: analyticsConstants.vehicleSortType.totalTimeOnPageDesc,
          formatter: val => this.$locale.getSecondsDurationFormatted(val)
        }, {
          key: 'formLeads',
          label: 'Form Leads',
          sortable: true,
          sortTypeAsc: analyticsConstants.vehicleSortType.fromLeadsAsc,
          sortTypeDesc: analyticsConstants.vehicleSortType.fromLeadsDesc,
          formatter: val => this.$locale.formatNumber(val)
        }
      ]
      if (this.showAbsoluteUrl) {
        fields.push({
          key: 'absoluteUrl',
          label: 'View Vehicle'
        })
      }
      return fields
    },
    sortTypeProp: {
      get () {
        return this.sortType
      },
      set (newVal) {
        this.$emit('sortTypeChanged', newVal)
      }
    },
    pageNumberProp: {
      get () {
        return this.pageNumber
      },
      set (newVal) {
        this.$emit('pageNumberChanged', newVal)
      }
    },
    pageSizeProp: {
      get () {
        return this.pageSize
      },
      set (newVal) {
        this.$emit('pageSizeChanged', newVal)
      }
    }
  }
}
</script>

<style scoped>

</style>
