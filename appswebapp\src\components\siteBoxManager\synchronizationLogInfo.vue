<template>
  <div class="mt-3">
    <h4>
      Vehicle Synchronization Logs
      <b-btn @click="refresh" class="float-right" variant="primary btn-round" size="sm">
        <span class="ion ion-ios-sync"></span><span class="d-none d-sm-inline">&nbsp; Refresh</span>
      </b-btn>
    </h4>
    <div v-if="!isLoading">
      <b-table
        :tbody-class="hasNoNewLogs ? 'text-danger': 'text-success'"
        v-if="vehicleSyncLogItems && vehicleSyncLogItems.length > 0"
        :items="vehicleSyncLogItems"
        :fields="getTableFields"
        striped
        bordered
        fixed
        responsive
      >
      </b-table>
      <span v-else class="text-muted">Not Found</span>
      <div class="mt-2" v-for="(value, key) in siteBoxSyncLogItems" :key="key">
        <h4>
          {{key}}
        </h4>
        <b-table
          v-if="value && value.length > 0"
          :items="value"
          :fields="getTableFields"
          striped
          bordered
          fixed
          responsive
        >
        </b-table>
        <span v-else class="text-muted">Not Found</span>
      </div>
    </div>
    <loader v-else size="lg" class="my-5"/>
  </div>
</template>

<script>
import loader from '@/components/_shared/loader'
import SynchronizationMonitorService from '@/services/siteBoxManager/SynchronizationMonitorService'
import moment from 'moment'

export default {
  data () {
    return {
      isLoading: true,
      vehicleSyncLogItems: [],
      siteBoxSyncLogItems: [],
      hasNoNewLogs: false
    }
  },
  created () {
    this.populateData()
  },
  components: {
    loader
  },
  computed: {
    getTableFields () {
      return [
        {
          key: 'accountId',
          label: 'AccountId',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'machineName',
          label: 'Machine Name',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'startProcessingDateTime',
          label: 'Start Processing',
          tdClass: 'py-2 align-middle',
          formatter: value => moment(value).format('MM/DD/YYYY hh:mm A')
        },
        {
          key: 'endProcessingDateTime',
          label: 'End Processing',
          tdClass: 'py-2 align-middle',
          formatter: value => moment(value).format('MM/DD/YYYY hh:mm A')
        }
      ]
    }
  },
  methods: {
    refresh () {
      this.isLoading = true
      this.populateData()
    },
    populateData () {
      SynchronizationMonitorService.getSynchronizationLogInfo().then(res => {
        this.vehicleSyncLogItems = res.data.vehicleSynchronizationLogs
        this.siteBoxSyncLogItems = res.data.siteVehicleSynchronizationLogsBySiteBox
        this.hasNoNewLogs = res.data.hasNoNewLogs
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
      }).finally(() => {
        this.isLoading = false
      })
    }
  }
}
</script>
