<template>
  <div>
    <div class="d-flex flex-row">
      <h4 class="mt-3">User Activity</h4>
      <!-- Pagination -->
      <paging
        class="d-none d-md-block p-0"
        :pageNumber="filter.page"
        :pageSize="filter.pageSize"
        :totalItems="totalItem"
        @numberChanged="pageChanged"
        @changePageSize="changePageSize"
      />
    </div>
    <b-card>
      <b-form  v-on:submit.prevent="applyFilter">
        <div class="form-row">
          <b-col class="my-1" sm="4" lg="4" xl="2">
            <b-form-input
              max='200'
              v-model="filter.search"
              placeholder="Search..."
              autocomplete="off"
            >
            </b-form-input>
          </b-col>

          <b-col class="my-1" sm="4" lg="4" xl="2">
            <b-input-group class="flex-nowrap">
              <b-input-group-prepend is-text>
                <i class="ion ion-md-calendar" slot="prepend"></i>
              </b-input-group-prepend>
              <date-time-picker
                ref="timeFrom"
                v-model="filter.dateFrom"
                :options="filterTimeOptions"
                format="MM/DD/YYYY HH:mm"
                placeholder="Date From"
                className="form-control"
                @change="onTimeFromInputChange"
              />
              <b-input-group-append
                is-text
                v-show="filter.dateFrom"
                @click="filter.dateFrom = null"
              >
                <i class="ion ion-md-close"></i>
              </b-input-group-append>
            </b-input-group>
          </b-col>

          <b-col class="my-1" sm="4" lg="4" xl="2">
            <b-input-group class="flex-nowrap">
              <b-input-group-prepend is-text>
                <i class="ion ion-md-calendar" slot="prepend"></i>
              </b-input-group-prepend>
              <date-time-picker
                ref="timeTo"
                v-model="filter.dateTo"
                :options="filterTimeOptions"
                format="MM/DD/YYYY HH:mm"
                placeholder="Date To"
                className="form-control"
                @change="onTimeToInputChange"
              />
              <b-input-group-append
                is-text
                v-show="filter.dateTo"
                @click="filter.dateTo = null"
              >
                <i class="ion ion-md-close"></i>
              </b-input-group-append>
            </b-input-group>
          </b-col>
          <b-col class="my-1" sm="4" lg="4" xl="2">
            <b-form-select v-model="filter.action" :options="getActionOptions"></b-form-select>
          </b-col>
          <b-col class="my-1" sm="4" lg="4" xl="2">
            <multiSelectWithCheckboxes
              v-model='selectedUserTypes'
              :options='getUserTypeOptions'
              name="User Types"
              customMessageOfNoneSelectedItems="All User Types"
            ></multiSelectWithCheckboxes>
          </b-col>
          <b-col class="my-1" sm="4" lg="4" xl="2">
            <b-btn block  variant="primary" type="submit">Submit</b-btn>
          </b-col>
        </div>
      </b-form>
    </b-card>

    <b-card v-if="!isLoading">
      <b-table
        :items="items"
        :fields="getTableFields"
        :sort-by="tableSortBy"
        :sort-desc="tableSortDesc"
        @sort-changed="onSortChanged"
        :no-sort-reset="true"
        :no-local-sorting="true"
        striped
        show-empty
        hover
        responsive>
        <template #cell(manage)="data">
          <b-btn size="sm" @click="showDetails(data)">{{ data.detailsShowing ? 'Hide' : 'Show' }} Details</b-btn>
          <a class="btn btn-outline-secondary btn-sm" :href='getLogDetailsPath(data.item.id)' target="_blank">
            <i class="ion ion-md-open"></i>
          </a>
        </template>
        <template #row-details="data">
          <b-card>
            <log-node
              :data="data.item.nodes"
              :isExpandedShallow="true"
              :isExpandedDeep="false"
            />
          </b-card>
        </template>
      </b-table>
      <paging
          :pageNumber="filter.page"
          :pageSize="filter.pageSize"
          :totalItems="totalItem"
          titled
          pageSizeSelector
          @numberChanged="pageChanged"
          @changePageSize="changePageSize"
        />
    </b-card>
    <div v-else>
      <loader size="lg"/>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import analyticsConstants from '@/shared/analytics/constants'
import {userTypes} from '@/shared/users/constants'
import analyticsBuilders from './../../shared/analytics/builders'
import multiSelectWithCheckboxes from '../../components/_shared/multiSelectWithCheckboxes.vue'

const filterManager = analyticsBuilders.getFilterManager({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  search: { type: String, default: '' },
  sort: { type: String, default: 4 },
  action: {type: Number, default: 0},
  userTypes: {type: String, default: `${userTypes.cpUser.value},${userTypes.adminUser.value}`}
})

export default {
  name: 'analytics-user-activity',
  metaInfo: {
    title: 'User Activity'
  },
  data () {
    return {
      filterTimeOptions: {
        autoUpdateInput: false,
        singleDatePicker: true,
        timePicker: true,
        timePicker24Hour: true,
        maxDate: new Date()
      },
      filter: filterManager.defaultValue,
      items: [],
      totalItem: 0,
      isLoading: true
    }
  },
  computed: {
    selectedUserTypes: {
      get () {
        return this.getIntegerArrayFromString(this.filter.userTypes)
      },
      set (value) {
        this.filter.userTypes = (value || []).join()
      }
    },
    getTableFields () {
      return [
        {
          key: 'requestInformation.data.userName',
          label: 'User Name',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: analyticsConstants.userLogSortTypes.userNameAsc,
          sortTypeDesc: analyticsConstants.userLogSortTypes.userNameDesc
        },
        {
          key: 'requestInformation.data.user.userType',
          label: 'User Type',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: analyticsConstants.userLogSortTypes.userTypeAsc,
          sortTypeDesc: analyticsConstants.userLogSortTypes.userTypeDesc,
          formatter: val => (Object.values(userTypes).find(x => x.value === val) || {text: '-'}).text
        },
        {
          key: 'accountId',
          label: 'Account Id',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: analyticsConstants.userLogSortTypes.accountIdAsc,
          sortTypeDesc: analyticsConstants.userLogSortTypes.accountIdDesc,
          formatter: val => val || '-'
        },
        {
          key: 'accountName',
          label: 'Account Name',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: analyticsConstants.userLogSortTypes.accountNameAsc,
          sortTypeDesc: analyticsConstants.userLogSortTypes.accountNameDesc,
          formatter: val => val || '-'
        },
        {
          key: 'groupName',
          label: 'Group Name',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: analyticsConstants.userLogSortTypes.groupNameAsc,
          sortTypeDesc: analyticsConstants.userLogSortTypes.groupNameDesc,
          formatter: val => val || '-'
        },
        {
          key: 'action',
          label: 'Action Name',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: analyticsConstants.userLogSortTypes.actionAsc,
          sortTypeDesc: analyticsConstants.userLogSortTypes.actionDesc,
          formatter: (value, key, item) => this.getActionName(value, key, item)
        },
        {
          key: 'startProcessingDateTime',
          label: 'Date',
          tdClass: 'py-2 align-middle',
          formatter: value => moment(value).format('MM/DD/YYYY HH:mm'),
          sortable: true,
          sortTypeAsc: analyticsConstants.userLogSortTypes.dateAsc,
          sortTypeDesc: analyticsConstants.userLogSortTypes.dateDesc
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    refDateTimeFrom () {
      return (this.$refs.timeFrom || {}).$el || {}
    },
    refDateTimeTo () {
      return (this.$refs.timeTo || {}).$el || {}
    },
    tableSortBy () {
      const sortedColumn = this.getTableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.getTableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sortType
      } else {
        return false
      }
    },
    getUserTypeOptions () {
      return Object.values(userTypes)
    },
    getActionOptions () {
      return Object.values(analyticsConstants.userActivityActions).sort((left, right) => left.text.localeCompare(right.text))
    }
  },
  components: {
    'date-time-picker': () => import('@gravitano/vue-date-range-picker/src/components/DateRangePicker'),
    'paging': () => import('@/components/_shared/paging'),
    'log-node': () => import('@/components/_shared/logItemNode.vue'),
    'loader': () => import('@/components/_shared/loader.vue'),
    multiSelectWithCheckboxes
  },
  created () {
    this.filter = filterManager.urlHelper.parseQueryStringToObject(this.$router)
    this.loadContent()
  },
  methods: {
    onTimeFromInputChange (newVal) {
      this.refDateTimeFrom.value = newVal || this.filter.dateFrom || null
    },
    onTimeToInputChange (newVal) {
      this.refDateTimeTo.value = newVal || this.filter.dateTo || null
    },
    pageChanged (newPage) {
      this.filter.page = newPage
      this.synchronizeUrl()
    },
    changePageSize (newSize) {
      this.filter.pageSize = newSize
      this.filter.page = 1
      this.synchronizeUrl()
    },
    onSortChanged (value) {
      const sortingColumn = this.getTableFields.find(x => x.key === value.sortBy)
      if (value.sortDesc) {
        this.filter.sort = sortingColumn.sortTypeDesc
      } else {
        this.filter.sort = sortingColumn.sortTypeAsc
      }
      this.loadContent()
    },
    applyFilter () {
      this.synchronizeUrl()
    },
    synchronizeUrl () {
      filterManager.urlHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.loadContent()
    },
    showDetails (data) {
      if (data.item.nodes) {
        data.toggleDetails()
      } else {
        this.$store.dispatch('analyticsGa4/getUserActivityLogDetails', { logId: data.item.id }).then(res => {
          data.item.nodes = res.data.model
          data.toggleDetails()
        }).catch(ex => {
          this.$toaster.error('Something went wrong!')
          this.$logger.handleError(ex, 'Cannot get analytics user activity log details')
        })
      }
    },
    loadContent () {
      this.$store.dispatch('analyticsGa4/getUserActivityLogs', { filter: this.filter }).then(res => {
        this.items = res.data.model.logItems
        this.totalItem = res.data.model.totalItemsCount
      }).catch(ex => {
        this.$toaster.error('Something went wrong!')
        this.$logger.handleError(ex, 'Cannot get analytics user activity logs')
      }).finally(() => {
        this.isLoading = false
      })
    },
    getIntegerArrayFromString (str) {
      if (!str || !str.trim()) {
        return []
      }
      let array = []
      for (let numberStr of str.split(',')) {
        let number = parseInt(numberStr)
        if (!isNaN(number)) {
          array.push(number)
        }
      }
      return array
    },
    getLogDetailsPath (id) {
      return `/ebizanalytics/useractivity/${id}/details`
    },
    getActionName (value, key, item) {
      let actionDescription = (Object.values(analyticsConstants.userActivityActions).find(x => x.value === value) || {text: '-'}).text
      if (actionDescription.length > 1 && item.requestInformation &&
        item.requestInformation.data.requestUrl &&
        item.requestInformation.data.requestUrl.indexOf(analyticsConstants.googleAnalytics4ApiEndpointsIdentifier) >= 0) {
        actionDescription += ' - GA4'
      }
      return actionDescription
    }
  }
}
</script>
