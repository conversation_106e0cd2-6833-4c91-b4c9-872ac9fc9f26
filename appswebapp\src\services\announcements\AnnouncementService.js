import BaseService from '@/services/BaseService'

class AnnouncementService extends BaseService {
  getAnnouncements (filters) {
    return this.axios.get('/api/announcement/', {params: filters})
  };
  getAnnouncement (id) {
    return this.axios.get(`/api/announcement/${id}/`)
  };
  createAnnouncement (announcement) {
    return this.axios.post(`/api/announcement/`, announcement)
  };
  updateAnnouncement (announcement) {
    return this.axios.post(`/api/announcement/${announcement.announcementId}/`, announcement)
  };
  deleteAnnouncement (id) {
    return this.axios.post(`/api/announcement/${id}/delete/`)
  };
}

export default new AnnouncementService()
