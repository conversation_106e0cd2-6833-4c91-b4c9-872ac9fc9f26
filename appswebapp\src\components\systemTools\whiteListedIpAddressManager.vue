<template>
  <b-modal
    title="Whitelisted IPs"
    :visible="isVisible"
    @hide="onHide"
  >
    <div class="mb-4">
      <b-input-group>
       <b-form-input v-model="whiteListedIp" placeholder="Enter New IP Address"></b-form-input>
        <b-input-group-append>
          <b-btn variant="primary" @click="addOne" :disabled="!isValidIpAddress">Add</b-btn>
        </b-input-group-append>
      </b-input-group>
    </div>
    <b-table
      v-if="!isLoading"
      :items="items"
      :fields="fields"
      :per-page="pageSize"
      :current-page="currentPage"
      show-empty
      striped
      hover
    >
      <template #cell(ipAddress)="data">
        {{ data.item }}
      </template>
      <template #cell(manage)="data">
        <b-btn size="sm" @click="deleteOne(data.item)" variant="primary">Delete</b-btn>
      </template>
    </b-table>
    <loader v-else/>
    <template #modal-footer>
      <paging
        v-if="!isLoading"
        :pageNumber="currentPage"
        :pageSize="pageSize"
        :totalItems="getTotalItemsCount"
        titled
        @numberChanged="pageChanged"
      />
    </template>
  </b-modal>
</template>

<script>
import SystemToolsService from '@/services/systemTools/SystemToolsService'
import loader from '../_shared/loader.vue'
import globals from '@/globals'

export default {
  name: 'white-listed-ip-manager',
  props: {
    isVisible: Boolean
  },
  components: {
    loader,
    'paging': () => import('@/components/_shared/paging')
  },
  data () {
    return {
      isLoading: true,
      items: [],
      currentPage: 1,
      pageSize: 10,
      whiteListedIp: ''
    }
  },
  computed: {
    fields () {
      return [
        {
          key: 'ipAddress',
          label: 'IP Address',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle',
          thStyle: 'width: 100px;'
        }
      ]
    },
    getTotalItemsCount () {
      return this.items ? this.items.length : 0
    },
    isValidIpAddress () {
      return globals().validateIpAddress(this.whiteListedIp)
    }
  },
  methods: {
    populateData () {
      SystemToolsService.getWhiteListedIpAddresses().then(res => {
        this.items = res.data || []
      }).catch(ex => {
        this.$toaster.error('Something went wrong!')
      }).finally(() => {
        this.isLoading = false
      })
    },
    pageChanged (newPage) {
      this.currentPage = newPage
    },
    onHide () {
      this.resetData()
      this.$emit('hide')
    },
    resetData () {
      this.items = []
      this.whiteListedIp = ''
    },
    deleteOne (ipAddress) {
      SystemToolsService.deleteWhiteListedIpAddress(ipAddress).then(res => {
        this.$toaster.success('Deleted Successfully', {timeout: 5000})
      }).finally(() => {
        this.isLoading = true
        this.populateData()
      })
    },
    addOne () {
      if (this.items.includes(this.whiteListedIp)) {
        this.$toaster.error('This IP address is already present in the whitelist')
        return
      }

      SystemToolsService.addWhiteListedIpAddress(this.whiteListedIp).then(res => {
        this.$toaster.success('Added Successfully', {timeout: 5000})
      }).finally(() => {
        this.isLoading = true
        this.populateData()
      })
    }
  }
}
</script>
