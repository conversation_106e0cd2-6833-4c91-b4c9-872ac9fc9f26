<template>
   <details-section title="Selected OEM Options:" @cancel="onCancel" v-model="mode" class="options" subSection>
    <div class="view" v-if="mode === 'view'">
      <template v-if="hasSelectedOptions">
        <b-list-group>
          <b-list-group-item v-for="i in getOptions" :key="i.optionCodeId">
            <span class="text-capitalize">{{i.name.toLowerCase()}}</span>
            <span class="float-right">${{i.price}}</span>
          </b-list-group-item>
        </b-list-group>
      </template>
      <span v-else>No selected options</span>
    </div>

    <div class="edit" v-else-if="mode === 'edit'">
      <b-list-group>
        <b-list-group-item v-for="i in getAllOptions" :key="i.optionCodeId"
        :active="isActive(i.optionCodeId)"
        @click="toggleOption(i)"
        :class="{ 'bg-secondary-dark': isActive(i.optionCodeId) }">
          <span class="text-capitalize">{{i.name.toLowerCase()}}</span>
          <span class="float-right">${{i.price}}</span>
        </b-list-group-item>
      </b-list-group>
    </div>

  </details-section>
</template>

<script>
import {mapGetters} from 'vuex'
import detailsSection from '@/components/details/detailsSection'

export default {
  name: 'status-section',
  data () {
    return {
      mode: 'view'
    }
  },
  computed: {
    ...mapGetters('details', ['vehicle', 'metadata']),
    getOptions () {
      return this.vehicle.oemOptions
    },
    hasSelectedOptions () {
      return this.vehicle.oemOptions && this.vehicle.oemOptions.length > 0
    },
    getAllOptions () {
      return this.metadata.vehicleOEMOptions
    }
  },
  methods: {
    isActive (id) {
      return this.vehicle.oemOptions && this.vehicle.oemOptions.some(x => x.optionCodeId === id)
    },
    toggleOption (optionItem) {
      let index = (this.vehicle.oemOptions || [])
        .map(x => x.optionCodeId)
        .indexOf(optionItem.optionCodeId)

      if (index === -1) {
        this.vehicle.oemOptions.push(optionItem)
      } else {
        this.vehicle.oemOptions.splice(index, 1)
      }
    },
    getDescription (text) {
      return text.split('- ').filter(x => x.length)
    },
    onCancel () {
      this.$forceUpdate()
    }
  },
  components: {
    'details-section': detailsSection
  }
}
</script>

<style>
.options .edit .list-group .list-group-item:hover {
  cursor: pointer;
}

.options .list-group .bg-secondary-dark.active {
  border: 1px solid #f1f1f2;
}
</style>
