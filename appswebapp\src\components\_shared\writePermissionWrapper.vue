<template>
  <div class="write-p-helper" :class="{ 'write-p-helper--hidden': hasToHide }">
    <slot></slot>
    <div :class="{'write-p-helper--overlay': hasToAddOverlay ,'write-p-helper--disable': hasToDisable}" title="Write access permissions required"></div>
  </div>
</template>

<script>

import {mapGetters} from 'vuex'

export default {
  name: 'WritePermissionWrapper',
  props: {
    variant: {
      type: String,
      validate (val) {
        return ['disable', 'disable-overlay', 'hidden'].includes(val)
      }
    }
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    hasToHide () {
      return this.hasToApply && this.variant === 'hidden'
    },
    hasToDisable () {
      return this.hasToApply && (this.variant === 'disable' || this.variant === 'disable-overlay')
    },
    hasToAddOverlay () {
      return this.hasToApply && this.variant === 'disable-overlay'
    },
    hasToApply () {
      const user = (this.userInfo || {}).user

      if (!user) {
        return true
      }

      if (user.accountId === 0) {
        return false
      }

      const accountId = +this.$route.params.accountId
      const appType = +this.$route.meta.applicationType
      const appFullAccessPermission = this.$route.meta.applicationFullAccess

      if (user.accountId === accountId) {
        return false
      }

      if (appFullAccessPermission && user.hasPermissions(appFullAccessPermission)) {
        return false
      }

      const permissionsByAid = (user.groupPermissionsByAccountId || {})[accountId]

      if (!permissionsByAid) {
        return true
      }

      const appPermission = permissionsByAid.find(x => x.application === appType) || {}

      return !appPermission || appPermission.permissionType !== 2
    }
  }
}
</script>

<style scoped>
  .write-p-helper {
    position: relative;
    width: 100%;
    display: inherit;
    flex: inherit;
    flex-wrap: inherit;
  }
  .write-p-helper--hidden {
    height: 0;
    overflow: hidden;
  }
  .write-p-helper--disable {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
  }
  .write-p-helper--overlay {
    background: rgba(255,255,255,.3);
  }
</style>
