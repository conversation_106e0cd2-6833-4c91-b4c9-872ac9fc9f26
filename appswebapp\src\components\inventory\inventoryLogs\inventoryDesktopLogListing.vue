<template>
  <div class='table-responsive' v-if="totalItems > 0">
    <b-table
      :fields="tableFields"
      :items="items"
      striped
      hover
      class="card-table"
    >
      <template #cell(show_details)="row">
        <l-button :loading="row.item.isLoading" size="sm" @click="showDetails(row)" class="text-center">
          {{ row.detailsShowing ? 'Hide' : 'Show' }} Details
        </l-button>
        <a class="btn btn-outline-secondary btn-sm" :href='getLogDetailsPath(row.item.id)' target="_blank">
          <i class="ion ion-md-open"></i>
        </a>
      </template>
      <template #row-details="row">
          <b-card>
            <log-node
              :data="row.item.details"
              :isExpandedShallow="true"
              :isExpandedDeep="false"
            />
          </b-card>
      </template>
    </b-table>
    <template slot='show_details'>
    </template>
  </div>
</template>

<script>
import InventoryLogService from '@/services/logs/InventoryLogService'
import moment from 'moment'

export default {
  name: 'inventory-desktop-log-listing',
  props: {
    items: { type: Array, required: true },
    totalItems: { type: Number, required: true }
  },
  data () {
    return {
    }
  },
  components: {
    'log-node': () => import('@/components/_shared/logItemNode.vue')
  },
  computed: {
    tableFields () {
      return [
        {
          key: 'vin',
          label: 'Vin',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'userName',
          label: 'UserName',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'startProcessingDateTime',
          label: 'Date',
          tdClass: 'py-2 align-middle',
          formatter: value => {
            return moment(value).format('MM/DD/YYYY hh:mm:ss A')
          }
        },
        {
          key: 'description',
          label: 'Description',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'show_details',
          tdClass: 'py-2 align-middle'
        }
      ]
    }
  },
  methods: {
    async showDetails (row) {
      if (row.item.details) {
        row.toggleDetails()
      } else {
        this.$set(row.item, 'isLoading', true)

        try {
          const apiResult = await InventoryLogService.getInventoryApiLogDetails(row.item.id)

          row.item.details = {
            nodes: apiResult.data.properties
          }

          row.toggleDetails()
        } catch (err) {
          this.$toaster.error('Failed to get data from server')
          this.$logger.handleError(err, 'Can\'t load log details', row.item)
        } finally {
          this.$set(row.item, 'isLoading', false)
        }
      }
    },
    getLogDetailsPath (id) {
      return `/inventory/logs/${id}`
    }
  }
}
</script>
