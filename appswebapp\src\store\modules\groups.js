import axios from 'axios'

const actions = {
  getGroups (_, parameters) {
    return axios.get(`/api/accounts/groups`, {params: parameters.params})
  },
  getGroup (_, parameters) {
    return axios.get(`/api/accounts/groups/${parameters.groupId}`)
  },
  createGroup (_, parameters) {
    return axios.post(`/api/accounts/groups`, parameters.group)
  },
  updateGroup (_, parameters) {
    return axios.post(`/api/accounts/groups/${parameters.group.groupId}`, parameters.group)
  },
  deleteGroup (_, parameters) {
    return axios.post(`/api/accounts/groups/${parameters.groupId}/delete`)
  },
  getAccountUsers (_, parameters) {
    return axios.get(`/api/accounts/groups/permissions/users/${parameters.accountId}`)
  },
  getGroupPemissionRules (_, parameters) {
    return axios.get(`/api/accounts/groups/${parameters.groupId}/permissions`)
  },
  getGroupAccountPermissionRules (_, parameters) {
    return axios.get(`/api/accounts/groups/${parameters.groupId}/accountpermissions/${parameters.accountId}`, { params: parameters.filters })
  },
  getUserGroupPermissionRulesFiltered (_, parameters) {
    return axios.get(`/api/accounts/groups/${parameters.groupId}/accountpermissions/${parameters.accountId}/${parameters.userId}`, { params: parameters.filters })
  },
  getPermissionRule (_, parameters) {
    return axios.get(`/api/accounts/groups/${parameters.groupId}/permissions/${parameters.ruleId}`)
  },
  createPermissionRule (_, parameters) {
    return axios.post(`/api/accounts/groups/${parameters.groupId}/permissions`, parameters.rule)
  },
  createPermissionRules (_, parameters) {
    return axios.post(`/api/accounts/groups/${parameters.groupId}/permissions/create/multiple`, parameters.insertModel)
  },
  updatePermissionRule (_, parameters) {
    return axios.post(`/api/accounts/groups/${parameters.groupId}/permissions/${parameters.rule.ruleId}`, parameters.rule)
  },
  updatePermissionRules (_, parameters) {
    return axios.post(`/api/accounts/groups/${parameters.roupId}/permissions/update/multiple`, parameters.updateModel)
  },
  deletePermissionRule (_, parameters) {
    return axios.post(`/api/accounts/groups/${parameters.groupId}/permissions/${parameters.ruleId}/delete`)
  },
  sendSelectedUsers (_, parameters) {
    return axios.post(`/api/accounts/groups/${parameters.groupId}/permissions/copy`, parameters.selectedUsers)
  }
}

export default {
  namespaced: true,
  actions: actions
}
