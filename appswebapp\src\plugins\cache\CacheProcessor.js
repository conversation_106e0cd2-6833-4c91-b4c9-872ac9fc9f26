import moment from 'moment'
import CacheItem from './CacheItem'

class CacheProcessor {
  constructor () {
    this.cache = {}
    this.process()
    this.shouldClean = false
  };
  add (key, data, expInSeconds) {
    let cacheItem = new CacheItem(key, data, expInSeconds)
    this.cache[key] = cacheItem
  };
  get (key) {
    return this.cache[key]
  };
  clean () {
    this.shouldClean = true
  };
  process () {
    if (this.shouldClean) {
      this.cache = {}
      this.shouldClean = false
    }
    let nowDate = moment()
    for (let cacheItem of Object.values(this.cache)) {
      let duration = moment.duration(nowDate.diff(cacheItem.date))
      let diffInSeconds = duration.asSeconds()
      if (diffInSeconds >= cacheItem.expInSeconds) {
        delete this.cache[cacheItem.key]
      }
    }
    setTimeout(this.process.bind(this), 100)
  }
}

export default CacheProcessor
