<template>
  <div class='d-flex flex-row' v-if="!isLoading">
    <div :class="filterListingCSSClass" class="border-right">
      <div class="float-left border-bottom">
        <messenger-filter :filterOptions="filterOptions" :filter="filter" @hideFilterListingSection="hideFilterListingSection" @filterChange="filterChange" @changeFilterMode="changeFilterMode"/>
      </div>
      <div class='float-left'>
        <b-list-group class="conversations-listing-section" @scroll="onScroll" :class='customMaxHeightCSSClass'>
          <b-list-group-item class="d-flex align-items-center"
            @click="setConversation(conversation)"
            :class="getClass(conversation.conversationId)"
            v-for="conversation in conversations" :key='conversation.conversationId'
            :title="hasAccountsLeadsToDisplay ? getAccountDesc(conversation) : ''">
            <b-avatar :badge="hasNewMessage(conversation.conversationId)" badge-variant="primary" style="background-color: #a3a4a6;" :text="conversation.user.userLetter">
            </b-avatar>
            <div class='d-flex flex-column ml-2'>
              <span class="contact-name" v-html="conversation.user.fullName"></span>
              <span class='text-muted contact-date'>{{getDateShortFormat(conversation.lastActivityDateTime)}}</span>
              <span v-if="hasAccountsLeadsToDisplay" class="text-muted account-description">{{getAccountDesc(conversation)}}</span>
            </div>
          </b-list-group-item>
        </b-list-group>
        <div v-if="isLoadingExtraConversation" class="linePreloader"></div>
      </div>
    </div>
    <div :class="userConversationCSSClass" class="border-left">
      <user-conversation
        @newMessage="newMessage"
        :currentConversation='currentConversation'
        :conversationDetailModel="conversationDetailModel"
        :leadsToggleBtnClass="leadsToggleBtnCSSClass"
        @showFilterListingSection="showFilterListingSection"
        @applyNewConversationDetails="applyNewConversationDetails"
        @loadExtraConversationDetails="loadExtraConversationDetails"
      />
    </div>
  </div>
  <div v-else class="mt-4">
    <loader size="lg"/>
  </div>
</template>

<script>
import userConversation from './userConversation'
import multiSelectWithCheckboxes from '../../_shared/multiSelectWithCheckboxes.vue'
import messengerFilter from '@/components/leads/messenger/messengerFilter'
import loader from '@/components/_shared/loader'
import ConversationService from '@/services/leads/ConversationService'
import moment from 'moment'
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import { mapGetters } from 'vuex'
import permissions from '../../../shared/common/permissions'

const defaultValues = new ObjectSchema({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 15 },
  communicationtypes: { type: String, default: '' },
  search: { type: String, default: '' },
  sort: { type: String, default: 1 },
  conversationid: { type: String, default: '' },
  conversationAccount: { type: Number, default: 0 },
  leadtypes: { type: String, default: '' },
  campaigntypes: { type: String, default: '' },
  dateFrom: { type: String, default: moment().add(-1, 'M').format('MM/DD/YYYY') },
  dateTo: { type: String, default: moment().format('MM/DD/YYYY') },
  includeAllAccounts: { type: Boolean, default: true },
  includedaccounts: { type: String, default: '' },
  excludedaccounts: { type: String, default: '' }
})

const queryHelper = new QueryStringHelper(defaultValues)
const defaultIncrementPageSizeNumber = 10

export default {
  name: 'leads-conversation-listing',
  props: {
    accountId: { type: Number, required: true },
    sitesUrlInfo: { type: Object, required: true }
  },
  data () {
    return {
      isLoading: true,
      total: 0,
      isLoadingExtraConversation: true,
      conversationIdWithNewMessage: '',
      filter: defaultValues.getObject(),
      conversations: [],
      conversationDetailFilters: {
        page: 1,
        pageSize: 25,
        sort: 2
      },
      conversationDetailModel: {
        conversationDetails: [],
        isMessagingAllowed: false,
        isClosed: false,
        userConversation: null,
        total: 0
      },
      filterOptions: {
        leadTypes: [],
        campaignTypes: []
      },
      userConversationCSSClass: 'user-conversation-section-desktop-mode',
      filterListingCSSClass: 'filter-listing-mobile-hide-mode',
      leadsToggleBtnCSSClass: 'leads-toggle-btn-hide-mode',
      customMaxHeightCSSClass: 'custom-max-height-minimized-filter-mode',
      currentConversation: {},
      selectedAccountIds: []
    }
  },
  components: {
    'user-conversation': userConversation,
    'messenger-filter': messengerFilter,
    'loader': loader,
    'multiSelectWithCheckboxes': multiSelectWithCheckboxes
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {getAllowedAccountIdsToDisplayLeads: () => []}
    },
    hasAccountsLeadsToDisplay () {
      return this.user.accountId === this.accountId && this.user.getAllowedAccountIdsToDisplayLeads.length > 0
    }
  },
  created () {
    this.filter = queryHelper.parseQueryStringToObject(this.$router)
    this.populateFilterData()
    this.populateData()
  },
  mounted () {
    this.setSelectedAccountIds()
  },
  methods: {
    setConversation (conversation) {
      this.currentConversation = {
        accountId: conversation.accountId,
        conversationId: conversation.conversationId,
        accountDesc: conversation.dealershipName ? [conversation.dealershipName, conversation.accountId].join(' - ') : conversation.accountId
      }
      if (conversation.conversationId === this.conversationIdWithNewMessage) {
        this.conversationIdWithNewMessage = ''
      }
      this.filter.conversationid = conversation.conversationId
      this.filter.conversationAccount = conversation.accountId
      this.resetConversationDetailsFilters()
      this.populateConversationDetails()
    },
    setSelectedAccountIds () {
      if (this.hasAccountsLeadsToDisplay) {
        let allAccessibleLeadAccounts = this.getAllAccessibleLeadAccounts()
        if (this.filter.includeAllAccounts) {
          this.selectedAccountIds = allAccessibleLeadAccounts
          this.$emit('applySelectedAccountIds', [])
          return
        }
        let excludedAccountIds = this.getIntegerArrayFromString(this.filter.excludedaccounts)
        if (excludedAccountIds.length > 0) {
          this.selectedAccountIds = allAccessibleLeadAccounts.filter(x => !excludedAccountIds.includes(x))
          this.$emit('applySelectedAccountIds', this.selectedAccountIds)
          return
        }
        let includedAccountIds = this.getIntegerArrayFromString(this.filter.includedaccounts)
        if (includedAccountIds.length > 0) {
          this.selectedAccountIds = allAccessibleLeadAccounts.filter(x => includedAccountIds.includes(x))
          this.$emit('applySelectedAccountIds', this.selectedAccountIds)
        }
      }
    },
    getAllAccessibleLeadAccounts () {
      let accountIds = this.user.getAllowedAccountIdsToDisplayLeads || []
      if (this.user.accountId > 0) {
        accountIds.push(this.user.accountId)
      }
      return accountIds
    },
    // CSS Section Start
    getClass (id) {
      if (id === this.currentConversation.conversationId) {
        return 'conversation-item-active'
      }

      return 'conversation-item'
    },
    hasNewMessage (id) {
      return id === this.conversationIdWithNewMessage && id !== this.currentConversation.conversationId
    },
    selectedAccountIdsChange (accountIds) {
      this.selectedAccountIds = accountIds
      this.filter.page = 1
      this.calculateLeadAccountsFilters()
      this.populateCampaignTypes()
      this.synchronizeUrlAndReload()
    },
    calculateLeadAccountsFilters () {
      let allLeadAccounts = this.getAllAccessibleLeadAccounts()
      let countOfAllLeadAccounts = allLeadAccounts.length
      let countOfSelectedAccounts = this.selectedAccountIds.length
      if (countOfAllLeadAccounts === countOfSelectedAccounts || countOfSelectedAccounts === 0) {
        this.filter.includeAllAccounts = true
        this.filter.excludedaccounts = ''
        this.filter.includedaccounts = ''
        return
      }
      this.filter.includeAllAccounts = false
      if (countOfSelectedAccounts > countOfAllLeadAccounts / 2) {
        this.filter.excludedaccounts = allLeadAccounts.filter(x => !this.selectedAccountIds.includes(x)).toString()
        this.filter.includedaccounts = ''
        return
      }
      this.filter.includedaccounts = this.selectedAccountIds.toString()
      this.filter.excludedaccounts = ''
    },
    changeFilterMode (isFullFilterMode) {
      if (isFullFilterMode) {
        this.customMaxHeightCSSClass = 'custom-max-height-full-filter-mode'
      } else {
        this.customMaxHeightCSSClass = 'custom-max-height-minimized-filter-mode'
      }
    },
    hideFilterListingSection () {
      this.filterListingCSSClass = 'filter-listing-mobile-hide-mode'
      this.userConversationCSSClass = 'user-conversation-mobile-hide-mode'
      this.leadsToggleBtnCSSClass = 'leads-toggle-btn-hide-mode'
    },
    showFilterListingSection () {
      this.filterListingCSSClass = 'filter-listing-section-desktop-mode'
      this.userConversationCSSClass = 'user-conversation-section-desktop-mode'
      this.leadsToggleBtnCSSClass = 'leads-toggle-btn'
    },
    // CSS Section End
    filterChange (newFilter) {
      this.filter = newFilter
      queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.populateConversations()
    },
    synchronizeUrlAndReload () {
      queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.populateConversations()
    },
    getDateShortFormat (date) {
      return moment(date).format('MM/DD/YYYY hh:mm:ss A')
    },
    getAccountDesc (conversation) {
      return conversation.dealershipName ? [conversation.dealershipName, conversation.accountId].join(' - ') : conversation.accountId
    },
    onScroll ({ target: { scrollTop, clientHeight, scrollHeight } }) {
      if (this.isLoadingExtraConversation) {
        return
      }
      if (scrollTop + clientHeight >= scrollHeight) {
        this.isLoadingExtraConversation = true
        if (this.filter.pageSize === this.conversations.length) {
          this.filter.pageSize += defaultIncrementPageSizeNumber
        }
        this.populateConversations()
      }
    },
    newMessage (id) {
      if (this.conversations.some(x => x.conversationId === id)) {
        this.conversationIdWithNewMessage = id
        this.populateConversations()
      }
    },
    applyNewConversationDetails (conversationDetails) {
      conversationDetails.vdpLink = this.buildVDPLink(this.sitesUrlInfo[conversationDetails.accountId], conversationDetails)
      if (this.conversationDetailModel.conversationDetails) {
        this.conversationDetailModel.conversationDetails.push(conversationDetails)
        return
      }
      this.conversationDetailModel.conversationDetails = [conversationDetails]
    },
    loadExtraConversationDetails () {
      this.conversationDetailFilters.pageSize += defaultIncrementPageSizeNumber
      this.populateConversationDetails()
    },
    async populateData () {
      let isCurrenConversationInitialized = false
      if (this.filter.conversationid) {
        this.currentConversation.accountId = this.filter.conversationAccount > 0 ? this.filter.conversationAccount : this.accountId
        this.currentConversation.conversationId = this.filter.conversationid
      }

      if (this.currentConversation.accountId > 0 && this.user.hasViewPermissions(this.currentConversation.accountId, permissions.LeadsView, permissions.LeadsFullAccess)) {
        isCurrenConversationInitialized = true
      }

      if (isCurrenConversationInitialized) {
        let keepProcessing
        do {
          await this.populateConversations()
          keepProcessing = this.total !== this.conversations.length && !this.conversations.some(x => x.conversationId === this.filter.conversationid)
          if (keepProcessing) {
            this.total = this.conversations.length
            if (this.filter.pageSize - this.total <= 5) {
              this.filter.pageSize += 10
            }
            this.filter.dateFrom = this.filter.dateFrom ? moment(this.filter.dateFrom).add(-1, 'M').format('MM/DD/YYYY') : moment().add(-1, 'M').format('MM/DD/YYYY')
          }
        } while (keepProcessing)
        await this.populateConversationDetails()
      } else {
        await this.populateConversations()
      }
    },
    async populateFilterData () {
      this.$store.dispatch('leads/getLeadTypes').then(res => {
        this.filterOptions.leadTypes = res.data
      }).catch(ex => {
        this.$toaster.error('Cannot populate lead types')
        this.$logger.handleError(ex, 'Cannot populate lead types')
      })
      this.populateCampaignTypes()
    },
    populateConversationDetails () {
      ConversationService.getAccountConversationsWithDetails(this.currentConversation.accountId, this.currentConversation.conversationId, this.conversationDetailFilters).then(res => {
        this.conversationDetailModel.conversationDetails = (res.data.conversationDetails ? res.data.conversationDetails.reverse() : []).map(x => {
          x.vdpLink = this.buildVDPLink(this.sitesUrlInfo[x.accountId], x)
          return x
        })
        this.conversationDetailModel.isMessagingAllowed = res.data.isMessagingAllowed
        this.conversationDetailModel.isClosed = res.data.isClosed
        this.conversationDetailModel.userConversation = res.data.user
        this.conversationDetailModel.total = this.conversationDetailModel.conversationDetails.length
      }).catch(ex => {
        this.$toaster.error('Somethings went wrong!')
        this.$logger.handleError(ex, `Cannot get conversation with details`, { requestParams: this.currentConversation, filter: this.conversationDetailFilters })
      })
    },
    populateConversations () {
      if (!this.hasAccountsLeadsToDisplay) {
        this.filter.includedaccounts = ''
        this.filter.excludedaccounts = ''
        this.filter.includeAllAccounts = false
      }
      ConversationService.getAccountConversations(this.accountId, this.filter).then(res => {
        this.conversations = res.data.conversations
      }).catch(ex => {
        this.$toaster.error('Cannot get Conversation Listing')
        this.$logger.handleError(ex, 'Cannot get Conversation Listing', this.filter)
      }).finally(() => {
        this.isLoading = false
        this.isLoadingExtraConversation = false
      })
    },
    populateCampaignTypes () {
      let apiCampaignTypeFilters = {
        includeAllAccounts: this.hasAccountsLeadsToDisplay ? this.filter.includeAllAccounts : false,
        includedaccounts: this.hasAccountsLeadsToDisplay ? this.filter.includedaccounts : '',
        excludedaccounts: this.hasAccountsLeadsToDisplay ? this.filter.excludedaccounts : ''
      }
      ConversationService.getCampaignTypes(this.accountId, apiCampaignTypeFilters).then(res => {
        this.filterOptions.campaignTypes = res.data
      }).catch(ex => {
        this.$toaster.error('Cannot populate campaign types')
        this.$logger.handleError(ex, 'Cannot populate campaign types')
      })
    },
    getIntegerArrayFromString (str) {
      if (!str || !str.trim()) {
        return []
      }
      let array = []
      for (let numberStr of str.split(',')) {
        let number = parseInt(numberStr)
        if (!isNaN(number)) {
          array.push(number)
        }
      }
      return array
    },
    resetConversationDetailsFilters () {
      this.conversationDetailFilters.page = 1
      this.conversationDetailFilters.pageSize = 25
    },
    buildVDPLink (siteInfo, conversationDetail) {
      let host = (siteInfo || {}).host || ''
      let vin = conversationDetail.vin || ''
      return vin && host ? `${host}/details.aspx?vin=${vin}` : ''
    }
  }
}
</script>

<style>
.filter-listing-section-desktop-mode, .filter-listing-mobile-hide-mode {
  display: flex;
  flex-shrink: 1;
  flex-direction: column;
  background: #fff;
}
.user-conversation-section-desktop-mode, .user-conversation-mobile-hide-mode {
  width: 100%;
  float: right;
  background: #fff;
}

.conversations-listing-section {
  overflow-y: auto;
  overflow-x: hidden;
  width: 100%;
  display: flex;
  flex-shrink: 1;
  flex-direction: column;
  background: #fff;
}

.conversations-listing-section::-webkit-scrollbar {
  width: 5px;
}
.conversations-listing-section::-webkit-scrollbar-track {
  background: #fdfdfd;
}
.conversations-listing-section::-webkit-scrollbar-thumb {
  background: rgb(207, 207, 207);
}
.conversations-listing-section::-webkit-scrollbar-thumb:hover {
  background: rgb(168, 167, 167);
}

.contact-date {
  font-size: 12px;
}

.contact-name {
  font-size: 14px;
  font-weight: 450;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 150px;
}

.account-description {
  font-size: 12px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 150px;
}

.conversation-item {
  cursor: pointer;
}
.conversation-item:hover {
  background: rgba(150, 148, 148, 0.6);
}
.conversation-item-active {
  cursor: pointer;
  background: rgba(150, 148, 148, 0.6);
}

@media (max-width: 992px) {
  .filter-listing-mobile-hide-mode {
    display: none;
  }
  .filter-listing-section-desktop-mode {
    position: absolute;
    background: white;
    z-index: 100;
  }
  .user-conversation-mobile-hide-mode {
    width: 100%;
  }
}

.custom-max-height-full-filter-mode {
  max-height: calc(100vh - 500px);
}
.custom-max-height-minimized-filter-mode {
  max-height: calc(100vh - 370px);
}

.linePreloader{
  height:4px;
  background:linear-gradient(to right,#af0d14,#af0d14);
  background-color:#ccc;
  border-radius:4px;
  background-size:20%;
  background-repeat:repeat-y;
  background-position:-25% 0;
  animation:scroll 1.2s ease-in-out infinite;
}

@keyframes scroll{
  50%{background-size:80%}
  100%{background-position:125% 0;}
}
</style>
