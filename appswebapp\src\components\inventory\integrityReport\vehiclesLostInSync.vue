<template>
  <div class="m-3">
    <b-row class="my-3">
      <b-col class="py-2">
        <b-input-group>
          <b-form-input v-model='filter.search' placeholder="Search..."></b-form-input>
          <b-input-group-append>
            <b-button variant="primary" @click="applySearch">Submit</b-button>
          </b-input-group-append>
        </b-input-group>
      </b-col>
      <b-col class="py-2"><b-btn class="float-right" variant="primary" @click="onDeleteAll">Delete All</b-btn></b-col>
    </b-row>
    <div v-if="!isLoading && items && items.length > 0">
      <paging
        :pageNumber="filter.page"
        :pageSize="filter.pageSize"
        :totalItems="itemsTotalCount"
        @numberChanged="pageChanged"
        @changePageSize="changePageSize" />
      <b-table
        v-if="!isLoading && items && items.length > 0"
        :items="items"
        :fields="tableFields"
        :sort-by="tableSortBy"
        :sort-desc="tableSortDesc"
        @sort-changed="onSortChanged"
        :striped="true"
        :bordered="false"
        :no-sort-reset="true"
        :no-local-sorting="true"
        responsive
      >
        <template #cell(title)="data">
          {{`${data.item.siteBoxName} / ${data.item.siteBoxId}`}}
        </template>
        <template #cell(manage)="data">
          <b-btn variant="primary" size="sm" @click="onDelete(data.item)">Delete</b-btn>
        </template>
      </b-table>
      <paging
        :pageNumber="filter.page"
        :pageSize="filter.pageSize"
        :totalItems="itemsTotalCount"
        titled
        pageSizeSelector
        @numberChanged="pageChanged"
        @changePageSize="changePageSize" />
    </div>
    <div v-else-if="isLoading" class="py-5">
      <loader size="lg"/>
    </div>
    <div v-else class="p-3">
      <span v-if="!isExceptionOccurred" class="font-weight-bold">No vehicles were lost during sitebox synchronization</span>
    </div>
  </div>
</template>

<script>
import InventoryService from '@/services/inventory/InventoryService'
import loader from '@/components/_shared/loader'
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import paging from '@/components/_shared/paging.vue'
import { vehicleLostInSyncSortTypes, vehicleLostInSyncReasonTypes } from '@/shared/inventory/inventoryTypes'

const defaultValues = new ObjectSchema({
  pageSize: { type: Number, default: 25 },
  search: { type: String, default: '' },
  page: { type: Number, default: 1 },
  sort: { type: Number, default: 6 }
})
const queryHelper = new QueryStringHelper(defaultValues)

export default {
  data () {
    return {
      filter: defaultValues.getObject(),
      itemsTotalCount: 0,
      items: [],
      isLoading: true,
      isExceptionOccurred: false
    }
  },
  created () {
    this.filter = queryHelper.parseQueryStringToObject(this.$router)
    this.populateData()
  },
  components: {
    loader,
    paging
  },
  computed: {
    tableFields () {
      return [
        {
          key: 'title',
          label: 'SiteBox Name / SiteBox Id',
          tdClass: 'py-2 align-middle',
          sortTypeAsc: vehicleLostInSyncSortTypes.siteBoxAsc,
          sortTypeDesc: vehicleLostInSyncSortTypes.siteBoxDesc,
          sortable: true
        },
        {
          key: 'accountId',
          label: 'Account Id',
          tdClass: 'py-2 align-middle',
          sortTypeAsc: vehicleLostInSyncSortTypes.accountIdAsc,
          sortTypeDesc: vehicleLostInSyncSortTypes.accountIdDesc,
          sortable: true
        },
        {
          key: 'siteId',
          label: 'Site Id',
          tdClass: 'py-2 align-middle',
          sortTypeAsc: vehicleLostInSyncSortTypes.siteIdAsc,
          sortTypeDesc: vehicleLostInSyncSortTypes.siteIdDesc,
          formatter: value => value > 0 ? value : '-',
          sortable: true
        },
        {
          key: 'vin',
          label: 'Vin',
          tdClass: 'py-2 align-middle',
          sortTypeAsc: vehicleLostInSyncSortTypes.vinAsc,
          sortTypeDesc: vehicleLostInSyncSortTypes.vinDesc,
          sortable: true
        },
        {
          key: 'reason',
          label: 'Reason',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: vehicleLostInSyncSortTypes.reasonAsc,
          sortTypeDesc: vehicleLostInSyncSortTypes.reasonDesc,
          formatter: value => {
            let res = Object.values(vehicleLostInSyncReasonTypes).find(x => x.value === value)
            return (res || { text: '-' }).text
          }
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    tableSortBy () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sortType
      } else {
        return false
      }
    }
  },
  methods: {
    pageChanged (newPage) {
      this.filter.page = newPage
      this.synchronizeUrlAndReload()
    },
    changePageSize (newSize) {
      this.filter.pageSize = newSize
      this.filter.page = 1
      this.synchronizeUrlAndReload()
    },
    onSortChanged (value) {
      const sortingColumn = this.tableFields.find(x => x.key === value.sortBy)
      if (value.sortDesc) {
        this.filter.sort = sortingColumn.sortTypeDesc
      } else {
        this.filter.sort = sortingColumn.sortTypeAsc
      }
      this.synchronizeUrlAndReload()
    },
    applySearch () {
      this.filter.page = 1
      this.synchronizeUrlAndReload()
    },
    synchronizeUrlAndReload () {
      queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.populateData()
    },
    populateData () {
      InventoryService.getVehiclesLostInSync(this.filter).then(res => {
        this.items = res.data.items
        this.itemsTotalCount = res.data.totalItems
      }).catch(ex => {
        this.$toaster.error('Something went wrong! Please reload page')
        this.$logger.handleError(ex, 'Cannot get vehicles lost in sync')
        this.isExceptionOccurred = true
      }).finally(() => {
        this.isLoading = false
      })
    },
    onDelete (item) {
      InventoryService.deleteVehicleLostInSync(item).then(res => {
        this.$toaster.success('Vehicle Deleted Successfully')
      }).catch(ex => {
        this.$toaster.error('Something went wrong! Please reload page')
        this.$logger.handleError(ex, 'Cannot delete vehicle lost in sync', item)
      }).finally(() => {
        this.isExceptionOccurred = false
        this.isLoading = true
        this.populateData()
      })
    },
    onDeleteAll () {
      InventoryService.deleteAllVehicleLostInSync().then(res => {
        this.$toaster.success('All Vehicles Deleted Successfully')
      }).catch(ex => {
        this.$toaster.error('Something went wrong! Please reload page')
        this.$logger.handleError(ex, 'Cannot delete all vehicle lost in sync')
      }).finally(() => {
        this.isExceptionOccurred = false
        this.isLoading = true
        this.populateData()
      })
    }
  }
}
</script>
