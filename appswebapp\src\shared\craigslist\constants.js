export default {
  inventorySortTypes: Object.freeze({
    undefined: 0,
    daysInStockAsc: 1,
    daysInStockDesc: 2,
    priceAsc: 3,
    priceDesc: 4,
    photosAsc: 5,
    photosDesc: 6,
    stockNumberAsc: 7,
    stockNumberDesc: 8
  }),
  vehiclePostingStatuses: Object.freeze({
    undefined: 0,
    posted: 1,
    neverPosted: 2,
    scheduled: 3
  }),
  vehicleConditions: Object.freeze({
    undefined: 0,
    new: 1,
    used: 2
  }),
  logSortTypes: Object.freeze({
    undefined: 0,
    priceAsc: 1,
    priceDesc: 2,
    areaAsc: 3,
    areaDesc: 4,
    dateTimeAsc: 5,
    dateTimeDesc: 6
  }),
  userActivitySortTypes: Object.freeze({
    undefined: 0,
    accountIdAsc: 1,
    accountIdDesc: 2,
    userNameAsc: 3,
    userNameDesc: 4,
    dateAsc: 5,
    dateDesc: 6,
    accountNameAsc: 7,
    accountNameDesc: 8,
    actionAsc: 9,
    actionDesc: 10,
    userTypeAsc: 11,
    userTypeDesc: 12
  }),
  userActivityActionTypes: Object.freeze({
    undefined: { value: 0, text: 'All Actions' },
    deleteCampaign: { value: 4, text: 'Delete Campaign' },
    deleteScheduledItem: { value: 7, text: 'Delete Scheduled Post' },
    insertCampaign: { value: 2, text: 'Insert Campaign' },
    postVehicle: { value: 5, text: 'Post Vehicle' },
    updateAccountSettings: { value: 1, text: 'Update Account Settings' },
    updateCampaign: { value: 3, text: 'Update Campaign' },
    updateScheduledItem: { value: 6, text: 'Update Scheduled Post' },
    updateServiceSettings: { value: 8, text: 'Update Service Settings' }
  }),
  craigslistCategoryTypes: Object.freeze({
    default: { value: 0, description: 'Default' },
    ctd: { value: 1, description: 'cars & trucks - by dealer' },
    rvd: { value: 2, description: 'rvs - by dealer' },
    hvd: { value: 3, description: 'heavy equipment - by dealer' },
    mcd: { value: 4, description: 'motorcycles/scooters - by dealer' },
    snd: { value: 5, description: 'atvs, utvs, snowmobiles - by dealer' },
    bod: { value: 6, description: 'boats - by dealer' },
    trb: { value: 7, description: 'trailers - by dealer' }
  }),
  craigslistPostingTypes: Object.freeze({
    default: { value: 2, description: 'Post Immediately' },
    schedulePost: { value: 1, description: 'SchedulePost' }
  }),
  craigslistAdsSortType: Object.freeze({
    accountIdAsc: 1,
    accountIdDesc: 2,
    postedPriceAsc: 3,
    postedPriceDesc: 4,
    postingAreaAsc: 5,
    postingAreaDesc: 6,
    postedDateAsc: 7,
    postedDateDesc: 8,
    expDateAsc: 9,
    expDateDesc: 10
  }),
  craigslistPostsSortType: Object.freeze({
    accountIdAsc: 1,
    accountIdDesc: 2,
    accountNameAsc: 3,
    accountNameDesc: 4,
    costAsc: 5,
    costDesc: 6,
    countAsc: 7,
    countDesc: 8,
    campaignNameAsc: 9,
    campaignNameDesc: 10,
    areaAsc: 11,
    areaDesc: 12,
    scheduledAsc: 13,
    scheduledDesc: 14
  }),
  accountPostingStatuses: Object.freeze({
    undefined: { value: 0, text: 'All Accounts' },
    enabledPosting: { value: 1, text: 'Enabled Posting' },
    disabledPosting: { value: 2, text: 'Disabled Posting' }
  })
}
