<template>
  <div>
    <b-row class="mb-2">
      <b-col>
        <h4>Email Ad Generator</h4>
      </b-col>
      <b-col>
        <b-btn v-if="currentMode === modes.emailAdPreview" class="float-right ml-1" variant="secondary" @click="backToemailAdForm">
          <b-icon icon="arrow-left-short" scale="1.2"></b-icon>
        </b-btn>
        <l-button v-if="currentMode === modes.emailAdForm" :loading="isGenerateEmailAdProcessing" class="float-right" variant="primary" @click="generateEmailPage">Generate Email Ad Page</l-button>
        <div v-if="currentMode === modes.emailAdPreview" class="d-flex flex-row align-items-center float-right mr-2">
          <b-btn @click="onCopy" variant="primary">Copy</b-btn>
          <b-checkbox class="ml-1" v-model="isCopyAsText">As text</b-checkbox>
        </div>
      </b-col>
    </b-row>
    <inventoryEmailAdForm v-if="currentMode === modes.emailAdForm" v-model="emailAdData" :accountId="accountId"/>
    <div v-if="currentMode === modes.emailAdPreview" class="inventory-email-page-container">
      <iframe ref="iframeEmailPage" :srcdoc="emailAdHtmlText" width="100%" height="100%">
      </iframe>
    </div>
  </div>
</template>

<script>
import inventoryEmailAdForm from '@/components/inventory/inventoryEmailAdForm'
import InventoryService from '../../services/inventory/InventoryService'
import { mapGetters } from 'vuex'

export default {
  name: 'inventory-email-ad',
  metaInfo: {
    title: 'Inventory Email Ad'
  },
  props: {
    accountId: { type: Number, required: true }
  },
  components: {
    inventoryEmailAdForm
  },
  data: function () {
    return {
      modes: {emailAdForm: 1, emailAdPreview: 2},
      currentMode: 1,
      emailAdHtmlText: '',
      emailAdData: {
        vins: [],
        h1PageTitle: ''
      },
      emailPageUrl: '',
      isGenerateEmailAdProcessing: false,
      isCopyAsText: false
    }
  },
  created () {
    if (this.$cookies.isKey(this.getUserCopyOptionsCookieKey)) {
      let data = this.$cookies.get(this.getUserCopyOptionsCookieKey) || {}
      this.isCopyAsText = data.isCopyAsText
    }
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {}
    },
    getUserCopyOptionsCookieKey () {
      return `email-generator-${this.user.userId}-copy-options`
    }
  },
  methods: {
    generateEmailPage () {
      if (this.emailAdData.vins.length === 0) {
        this.$toaster.error('At least one vehicle required')
        return
      }
      this.isGenerateEmailAdProcessing = true
      InventoryService.generateEmailAd(this.accountId, this.emailAdData).then(res => {
        this.emailAdHtmlText = res.data
        this.currentMode = this.modes.emailAdPreview
      }).catch(ex => {
        this.$toaster.exception(ex, 'Failed to generate email ad', {timeout: 5000})
      }).finally(() => {
        this.isGenerateEmailAdProcessing = false
      })
    },
    onBack () {
      this.emailAdHtmlText = ''
      this.isemailAdHtmlTextLoaded = false
    },
    async copyAsHtml () {
      try {
        const iframeContentWindow = this.$refs.iframeEmailPage.contentWindow
        iframeContentWindow.getSelection().removeAllRanges()
        let range = iframeContentWindow.document.createRange()
        range.selectNode(iframeContentWindow.document.body)
        iframeContentWindow.getSelection().addRange(range)
        iframeContentWindow.document.execCommand('copy')
        this.$toaster.success('Copied Successfully')
      } catch (ex) {
        this.$toaster.error('Failed to copy content')
        this.$logger.handleError(ex, 'Failed to copy inventory email ad as html',
          {accountId: this.selectedAccount.accountId, emailPageRequestData: this.emailAdData, emailPageText: this.emailAdHtmlText}
        )
      }
    },
    copyAsText () {
      this.$copyProvider.copyTextToClipboard(this.emailAdHtmlText).then(() => {
        this.$toaster.success('Copied Successfully')
      }).catch(ex => {
        this.$toaster.error('Failed to copy content')
        this.$logger.handleError(ex, 'Failed to copy inventory email ad as text',
          {accountId: this.selectedAccount.accountId, emailPageRequestData: this.emailAdData, emailPageText: this.emailAdHtmlText}
        )
      })
    },
    onCopy () {
      this.saveToCookies()
      if (this.isCopyAsText) {
        this.copyAsText()
        return
      }
      this.copyAsHtml()
    },
    saveToCookies () {
      this.$cookies.set(this.getUserCopyOptionsCookieKey, {isCopyAsText: this.isCopyAsText}, { expires: '5d' })
    },
    backToemailAdForm () {
      this.emailAdHtmlText = ''
      this.currentMode = this.modes.emailAdForm
    }
  }
}
</script>

<style>
.inventory-email-page-container {
  width: 100%;
  height: 80vh;
}
</style>
