import permissionHelper from './bitFlagHelper'

let DefaultAccess = permissionHelper.shiftLeft('0', 0)
let ManageCraigslist = permissionHelper.shiftLeft('1', 1)
let RepostOnCraigslist = permissionHelper.shiftLeft('1', 2)
let ViewLogs = permissionHelper.shiftLeft('1', 3)
let CraigslistFullAccess = permissionHelper.shiftLeft('1', 4)
let LeadsView = permissionHelper.shiftLeft('1', 6)
let LeadsManageCommunications = permissionHelper.shiftLeft('1', 7)
let LeadsSendMessages = permissionHelper.shiftLeft('1', 8)
let LeadsManageDealerSettings = permissionHelper.shiftLeft('1', 19)
let LeadsLogView = permissionHelper.shiftLeft('1', 14)
let LeadsFullAccess = permissionHelper.concatFlags(LeadsSendMessages, LeadsManageCommunications, LeadsView, LeadsLogView, LeadsManageDealerSettings, permissionHelper.shiftLeft('1', 13))
let ManageLegacyLeads = permissionHelper.shiftLeft('1', 9)
let ManageGoogleAnalytics = permissionHelper.shiftLeft('1', 10)
let AnalyticsFullAccess = permissionHelper.shiftLeft('1', 11)
let GoogleAnalytics4View = permissionHelper.shiftLeft('1', 12)
let SiteBoxManagerChangeSiteHosting = permissionHelper.shiftLeft('1', 15)
let SiteBoxManagerFullAccess = permissionHelper.concatFlags(SiteBoxManagerChangeSiteHosting, permissionHelper.shiftLeft('1', 16))

let ViewErrors = permissionHelper.shiftLeft('1', 17)
let ViewEncryptDecrypt = permissionHelper.shiftLeft('1', 18)

let IMManageVehicles = permissionHelper.shiftLeft('1', 20)
let IMFullAccess = permissionHelper.concatFlags(IMManageVehicles, permissionHelper.shiftLeft('1', 21))

let AMViewSettings = permissionHelper.shiftLeft('1', 23)
let AMManageSettings = permissionHelper.shiftLeft('1', 24)
let AMFullAccess = permissionHelper.concatFlags(AMViewSettings, AMManageSettings, permissionHelper.shiftLeft('1', 25))

let SMViewSettings = permissionHelper.shiftLeft('1', 27)
let SMManageSettings = permissionHelper.shiftLeft('1', 28)
let SMFullAccess = permissionHelper.concatFlags(SMViewSettings, SMManageSettings, permissionHelper.shiftLeft('1', 29))

let ManageUserAccount = permissionHelper.shiftLeft('1', 0)
let ManageMultipleUserAccounts = permissionHelper.concatFlags(ManageUserAccount, permissionHelper.shiftLeft('1', 30))
let UsersFullAccess = permissionHelper.concatFlags(ManageUserAccount, ManageMultipleUserAccounts, permissionHelper.shiftLeft('1', 5))

let EBayViewSettings = permissionHelper.shiftLeft('1', 32)
let EBayManageSettings = permissionHelper.shiftLeft('1', 33)
let EBayFullAccess = permissionHelper.concatFlags(EBayViewSettings, EBayManageSettings, permissionHelper.shiftLeft('1', 34))

let ErrorsViewErrors = permissionHelper.shiftLeft('1', 35)
let ErrorsViewErrorReports = permissionHelper.shiftLeft('1', 36)

let ViewReports = permissionHelper.shiftLeft('1', 37)

let EbizAutosAdmin = '1'.repeat(63)
let FullAccess = '1'.repeat(64)

let ViewAccountListingPermission = [
  CraigslistFullAccess,
  LeadsFullAccess,
  AMFullAccess,
  AnalyticsFullAccess,
  SiteBoxManagerFullAccess,
  IMFullAccess,
  EbizAutosAdmin,
  FullAccess,
  EBayFullAccess,
  SMFullAccess
]

export default {
  DefaultAccess,
  ManageUserAccount,
  ManageCraigslist,
  RepostOnCraigslist,
  ViewLogs,
  CraigslistFullAccess,
  UsersFullAccess,
  LeadsView,
  LeadsManageCommunications,
  LeadsManageDealerSettings,
  LeadsSendMessages,
  LeadsLogView,
  LeadsFullAccess,
  ManageLegacyLeads,
  ManageGoogleAnalytics,
  AnalyticsFullAccess,
  GoogleAnalytics4View,
  SiteBoxManagerChangeSiteHosting,
  SiteBoxManagerFullAccess,
  ViewErrors,
  ViewEncryptDecrypt,
  IMManageVehicles,
  IMFullAccess,
  AMViewSettings,
  AMManageSettings,
  AMFullAccess,
  EbizAutosAdmin,
  FullAccess,
  ViewAccountListingPermission,
  EBayViewSettings,
  EBayManageSettings,
  EBayFullAccess,
  SMViewSettings,
  SMManageSettings,
  SMFullAccess,
  ErrorsViewErrors,
  ErrorsViewErrorReports,
  ViewReports,
  ManageMultipleUserAccounts
}
