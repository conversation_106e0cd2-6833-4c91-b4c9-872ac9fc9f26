<template>
  <editSettingsHelper :readOnlyMode="readOnlyMode" @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="Features" :isLoading="isUpdatingProcessed" :isDisabled="isDisabled" :isViewMode="isViewMode">
    <template slot="settings-content">
      <detail-row v-if="accountLevel" :fixed-payload-width="true" :title-position="'start'">
        <span slot="title">Use Custom Feature Settings:</span>
        <b-form-checkbox v-if="!isViewMode" slot="payload" v-model="updatedSettings.hasToUseCustomFeaturesSettings"></b-form-checkbox>
        <span slot="payload" v-else>
          {{ updatedSettings.hasToUseCustomFeaturesSettings ? 'Yes' : 'No' }}
        </span>
      </detail-row>
      <b-overlay :show="!(!accountLevel || updatedSettings.hasToUseCustomFeaturesSettings)" opacity="0.7" blur="1.2px">
        <template #overlay>
          <span></span>
        </template>
        <ValidationProvider name="Features Intro" :rules="getAccountFeaturesIntroRules" v-slot="{errors}">
          <detail-row :fixed-payload-width="true" :title-position="'start'" :error="errors[0]">
            <span slot="title">Features Intro:</span>
            <span slot="payload" v-if="isViewMode">{{ settings.featuresIntro }}</span>
            <b-form-input slot="payload" v-else v-model="updatedSettings.featuresIntro"></b-form-input>
          </detail-row>
        </ValidationProvider>
        <ValidationProvider name="Feature Limits" :rules="{min_value: 0, max_value: updatedSettings.featureItems.length}" v-slot="{ errors }">
          <detail-row :fixed-payload-width="true" :title-position="'start'" :error="errors[0]">
            <span slot="title">Feature Limits:</span>
            <span slot="payload" v-if="isViewMode">{{ settings.keyFeaturesLimitInDescription }}</span>
            <b-form-input slot="payload" type="number" v-else v-model="updatedSettings.keyFeaturesLimitInDescription"></b-form-input>
          </detail-row>
        </ValidationProvider>
        <ValidationProvider name="Feature List" ref="featureListValidator" :rules="getAccountFeatureListRules" v-slot="{errors}">
          <detail-row :extra-large-payload-width="true" :title-position="'start'" :error="errors[0]">
            <span slot="title">Feature List:</span>
            <div slot="payload" class="w-100">
              <draggable
                name="Feature List"
                class="row p-0"
                v-bind="draggableOptions"
                v-model="updatedSettings.featureItems"
                @start="isFeaturesDragging = true"
                @end="isFeaturesDragging = false"
                :move="isMoveAllowed"
                :class="{on: isFeaturesDragging, off: !isFeaturesDragging}"
                :handle="`.feature-container`"
              >
                <b-col xl="4" lg="4" md="6" sm="12" class="feature-container" @click="featureIndex = index" v-for="(feature, index) in updatedSettings.featureItems" :key="index">
                  <b-form-group :label-for="`${index}-input`">
                    <template #label>
                      <div class="d-flex d-flex-row">
                        <i class="ion ion-ios-menu m-0 mr-2 opacity-100"
                        ></i>
                        <b-form-checkbox v-model="feature.isEnabled" :disabled="isViewMode"></b-form-checkbox>
                        <span>{{ feature.featureName }}</span>
                        <b-btn class="ml-auto p-1" size="sm" variant="light" @click="removeFeatureItemAt(index)" v-if="!isViewMode">
                          <b-icon icon="trash-fill"></b-icon>
                        </b-btn>
                      </div>
                    </template>
                    <span :id="`${index}-input`" v-if="isViewMode">{{ feature.featureCustomText || '-' }}</span>
                    <b-form-input v-else :id="`${index}-input`" v-model="feature.featureCustomText" placeholder="Feature Spelling"></b-form-input>
                  </b-form-group>
                  <input type="text" hidden :value="index+1" @change="onFeatureIndexChanged($event, index + 1)"/>
                </b-col>
              </draggable>
              <b-row v-if="!isViewMode">
                <b-col xl="4" lg="4" md="6" sm="12">
                  <b-form-input class="mb-2" v-model="featureItem.featureName" placeholder="Add New Feature"></b-form-input>
                  <b-input-group>
                    <b-form-input v-model="featureItem.featureCustomText" placeholder="Feature Spelling"></b-form-input>
                    <b-input-group-append @click="addNewFeatureItem()" style="cursor: pointer;">
                      <b-input-group-text class="bg-primary text-white">
                        <b-icon icon="check" aria-hidden="true" scale="1.2"/>
                      </b-input-group-text>
                    </b-input-group-append>
                  </b-input-group>
                </b-col>
              </b-row>
            </div>
          </detail-row>
        </ValidationProvider>
      </b-overlay>
    </template>
  </editSettingsHelper>
</template>

<script>
import editSettingsHelper from '@/components/_shared/editSettingsHelper'
import detailRow from '@/components/details/helpers/detailRow'
import draggable from 'vuedraggable'
import editSettingsMixin from '../editSettingsMixin'
import globals from '../../../../globals'

export default {
  name: 'tts-features-settings-section',
  props: {
    settings: { type: Object, required: true },
    isUpdatingProcessed: { type: Boolean, required: true },
    isDisabled: Boolean,
    accountLevel: Boolean,
    readOnlyMode: Boolean
  },
  data () {
    return {
      updatedSettings: globals().getClonedValue(this.settings),
      isViewMode: true,
      isFeaturesDragging: true,
      draggableOptions: {
        animation: 150
      },
      featureItem: {
        isEnabled: true,
        featureName: '',
        featureCustomText: ''
      }
    }
  },
  mixins: [editSettingsMixin],
  components: {
    detailRow,
    editSettingsHelper,
    draggable
  },
  computed: {
    getAccountFeatureListRules () {
      if (this.accountLevel && this.updatedSettings.hasToUseCustomFeaturesSettings) {
        return 'required'
      }
      return ''
    },
    getAccountFeaturesIntroRules () {
      if (this.accountLevel && this.updatedSettings.hasToUseCustomFeaturesSettings) {
        return 'required'
      }
      return ''
    }
  },
  methods: {
    isMoveAllowed (event) {
      if (this.isDisabled || this.isViewMode) {
        return false
      }
      return true
    },
    updateSettings () {
      this.$emit('saveChanges', this.updatedSettings)
    },
    onFeatureIndexChanged (newVal, oldVal) {
      if (!Number.isInteger(+newVal.target.value)) {
        return
      }

      let newIndex = (+newVal.target.value) - 1
      let oldIndex = oldVal - 1
      let arrayItem = this.updatedSettings.featureItems[oldIndex]
      this.updatedSettings.featureItems.splice(oldIndex, 1)
      this.updatedSettings.featureItems.splice(newIndex, 0, arrayItem)
    },
    removeFeatureItemAt (index) {
      this.updatedSettings.featureItems.splice(index, 1)
      this.revalidateFeatureList()
    },
    addNewFeatureItem () {
      if (!this.featureItem.featureName) {
        this.$toaster.error('Feature Name is required', {timeout: 4000})
        return
      }
      if (this.updatedSettings.featureItems && this.updatedSettings.featureItems.some(x => x.featureName === this.featureItem.featureName)) {
        this.$toaster.error(`The "${this.featureItem.featureName}" feature is already existed in list`, {timeout: 4000})
        return
      }
      if (!this.updatedSettings.featureItems) {
        this.updatedSettings.featureItems = []
      }
      this.updatedSettings.featureItems.push(this.featureItem)
      this.featureItem = {
        isEnabled: true,
        featureName: '',
        featureCustomText: ''
      }
      this.revalidateFeatureList()
    },
    revalidateFeatureList () {
      this.$refs.featureListValidator.validate()
    }
  },
  watch: {
    'updatedSettings': {
      deep: true,
      handler: function () {
        this.$emit('input', this.updatedSettings)
      }
    }
  }
}
</script>
