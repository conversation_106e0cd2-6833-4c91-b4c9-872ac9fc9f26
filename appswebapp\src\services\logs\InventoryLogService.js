import BaseService from './../BaseService'

class InventoryLogService extends BaseService {
  getInventoryApiLogs (filters) {
    return this.axios.get(`/api/inventory/logs`, { params: filters })
  };
  getInventoryApiLogDetails (logId) {
    return this.axios.get(`/api/inventory/logs/${logId}`)
  };
  getSettingsLogsByAccountId (filters) {
    return this.axios.get(`/api/inventory/logs/settings/`, { params: filters })
  };
  getSettingsLogDetails (logId) {
    return this.axios.get(`/api/inventory/logs/settings/${logId}`)
  };
  getSynchronizationLogs (filters) {
    return this.axios.get(`/api/inventory/logs/synchonization`, { params: filters })
  };
  getSynchronizationLogDetails (accountId, vin) {
    return this.axios.get(`/api/inventory/logs/synchonization/${accountId}/${vin}`)
  };
  getVehiclePostProcessingLogs (filters) {
    return this.axios.get(`/api/inventory/logs/vehiclepostprocessing`, { params: filters })
  };
  getVehiclePostProcessingLogDetails (logId) {
    return this.axios.get(`/api/inventory/logs/vehiclepostprocessing/${logId}`)
  };
  getVehicleHistoryReportLogs (filter) {
    return this.axios.get(`/api/inventory/logs/vehiclepostprocessing/vehicle_history`, {params: filter})
  };
}

export default new InventoryLogService()
