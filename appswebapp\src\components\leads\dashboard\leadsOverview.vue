<template>
  <div>
    <b-card no-body class="w-100 mb-3">
      <b-card-header header-tag="h5" class="with-elements border-0">
        <div class="card-header-title">Leads Overview</div>
        <div class="card-header-elements ml-auto">
          <b-dropdown variant="outline-secondary icon-btn btn-round md-btn-flat" size="sm" no-caret>
            <template slot="button-content">
              <i class="ion ion-ios-more m-0"></i><span class="sr-only">Actions</span>
            </template>
            <b-dropdown-item :active='selectedOption === options.all' @click='changeOption(options.all)' class="d-flex">All Leads</b-dropdown-item>
            <b-dropdown-item :active='selectedOption === options.webForm' @click='changeOption(options.webForm)' class="d-flex">Web Form Leads</b-dropdown-item>
            <b-dropdown-item :active='selectedOption === options.sms' @click='changeOption(options.sms)' class="d-flex">SMS Leads</b-dropdown-item>
            <b-dropdown-item :active='selectedOption === options.phone' @click='changeOption(options.phone)' class="d-flex">Phone Leads</b-dropdown-item>
            <b-dropdown-item :active='selectedOption === options.email' @click='changeOption(options.email)' class="d-flex">Proxy Email Leads</b-dropdown-item>
          </b-dropdown>
        </div>
      </b-card-header>
      <div>
        <vue-echart id="echart" :options="getLineOptions" :auto-resize="true"></vue-echart>
      </div>
    </b-card>
    <div class="w-100 custom-summary-card-listing mb-3">
      <div class="custom-summary-card-group">
        <div class="custom-summary-card">
          <summary-card
            label='Web Form Leads'
            :value='model.webFormsCountTotal'
            :delta='model.webFormsCountDelta'
            :rangeLabel='getRangeLabel'
            cardClass='bg-white h-100'
          >
            <i
              class='ion ion-ios-mail h1 m-0 opacity-25'
            ></i>
          </summary-card>
        </div>
        <div class="custom-summary-card">
          <summary-card
            label='SMS Leads'
            :value='model.smsCountTotal'
            :delta='model.smsCountDelta'
            :rangeLabel='getRangeLabel'
            cardClass='bg-white h-100'
          >
            <i class="ion ion-ios-chatboxes h1 m-0 opacity-25"></i>
          </summary-card>
        </div>
      </div>
      <div class="custom-summary-card-group">
        <div class="custom-summary-card">
          <summary-card
            label='Phone Leads'
            :value='model.callsCountTotal'
            :delta='model.callsCountDelta'
            :rangeLabel='getRangeLabel'
            cardClass='bg-white h-100'
          >
            <i
              class='ion ion-ios-call h1 m-0 opacity-25'
            ></i>
          </summary-card>
        </div>
        <div class="custom-summary-card">
          <summary-card
            label='Proxy Email Leads'
            :value='model.emailsCountTotal'
            :delta='model.emailsCountDelta'
            :rangeLabel='getRangeLabel'
            cardClass='bg-white h-100'
          >
            <i
              class='ion ion-ios-mail h1 m-0 opacity-25'
            ></i>
          </summary-card>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import 'echarts/lib/chart/line'
import 'echarts/lib/component/tooltip'
import 'echarts/lib/component/legend'
import VueEchart from 'vue-echarts/components/ECharts.vue'
import summaryCard from '../summaryCard'
import moment from 'moment'

import dateHelper from '@/plugins/locale/date'

const colors = ['#dc3545', '#28a745', '#007bff', '#ffc107', '#7751bd', '#a7b61a', '#f3e562', '#ff9800', '#ff5722', '#ff4514', '#647c8a', '#3f51b5', '#2196f3', '#00b862', '#afdf0a']

export default {
  name: 'leads-overview',
  props: {
    model: { type: Object, required: true }
  },
  components: {
    'vue-echart': VueEchart,
    'summary-card': summaryCard
  },
  data () {
    return {
      selectedOption: 1,
      options: { all: 1, webForm: 2, sms: 3, phone: 4, email: 5 }
    }
  },
  computed: {
    getLineOptions () {
      let options = {
        grid: {
          left: '50px',
          top: '20px',
          right: '50px',
          bottom: '30px',
          containLabel: true
        },
        color: colors,
        tooltip: {
          trigger: 'axis',
          textStyle: {
            fontSize: 13
          }
        },
        xAxis: {
          data: (this.model.intervalTotals || []).map(x => {
            return dateHelper.getDayFormatted(x.dateTimeFrom, 'MMM.D YYYY')
          }),
          show: true,
          boundaryGap: false,
          axisLine: {
            lineStyle: { color: 'rgba(0, 0, 0, .08)' }
          },
          axisLabel: { color: 'rgba(0, 0, 0, .5)' }
        },
        yAxis: {
          show: true,
          boundaryGap: false,
          splitLine: { show: false },
          axisLine: {
            lineStyle: { color: 'rgba(0, 0, 0, .08)' }
          },
          axisLabel: { color: 'rgba(0, 0, 0, .5)' }
        },
        series: this.getSeries()
      }

      return options
    },
    getRangeLabel () {
      return moment(this.model.dateTimeFrom).format('MMMM D, YYYY') + ' - ' + moment(this.model.dateTimeTo).format('MMMM DD, YYYY')
    }
  },
  methods: {
    changeOption (newOption) {
      this.selectedOption = newOption
    },
    getSeries () {
      let series = []
      if (this.selectedOption === this.options.all || this.selectedOption === this.options.webForm) {
        series.push({
          name: 'Web Form',
          type: 'line',
          stack: 'referrals',
          data: (this.model.intervalTotals || []).map(x => x.webFormsCount),
          areaStyle: {},
          smooth: 0.4,
          symbolSize: 7,
          showSymbol: false
        })
      }
      if (this.selectedOption === this.options.all || this.selectedOption === this.options.sms) {
        series.push({
          name: 'SMS',
          type: 'line',
          stack: 'referrals',
          data: (this.model.intervalTotals || []).map(x => x.smsCount),
          areaStyle: {},
          smooth: 0.4,
          symbolSize: 7,
          showSymbol: false
        })
      }
      if (this.selectedOption === this.options.all || this.selectedOption === this.options.phone) {
        series.push({
          name: 'Phone',
          type: 'line',
          stack: 'referrals',
          data: (this.model.intervalTotals || []).map(x => x.callsCount),
          areaStyle: {},
          smooth: 0.4,
          symbolSize: 7,
          showSymbol: false
        })
      }
      if (this.selectedOption === this.options.all || this.selectedOption === this.options.email) {
        series.push({
          name: 'Proxy Email',
          type: 'line',
          stack: 'referrals',
          data: (this.model.intervalTotals || []).map(x => x.emailsCount),
          areaStyle: {},
          smooth: 0.4,
          symbolSize: 7,
          showSymbol: false
        })
      }

      return series
    }
  }
}
</script>

<style>
#echart {
  width: 100%;
  height: 250px;
}
.custom-summary-card {
  width: 100%;
}
.custom-summary-card-listing {
  display: flex;
  flex-direction: row;
  flex-shrink: 1;
}
.custom-summary-card-group {
  display: flex;
  flex-direction: row;
  flex-shrink: 1;
  width: 100%;
}
@media (max-width: 768px) {
  .custom-summary-card-listing {
    flex-direction: column;
  }
  .custom-summary-card {
    width: 100%;
  }
}
@media (max-width: 375px){
  .custom-summary-card-group {
    flex-direction: column;
  }
}
</style>
