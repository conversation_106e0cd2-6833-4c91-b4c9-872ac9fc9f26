<template>
  <div>
    <div>
      <h4>User Activity Details Log</h4>
    </div>
    <b-card>
      <log-node
        v-if="logDetails"
        :data="logDetails"
        :isExpandedShallow="true"
        :isExpandedDeep="false"
      />
    </b-card>
  </div>
</template>

<script>
export default {
  name: 'craigslist-user-activity-log-details',
  metaInfo: {
    title: 'User Activity Details'
  },
  props: {
    logId: { type: String, required: true }
  },
  data () {
    return {
      logDetails: null
    }
  },
  components: {
    'log-node': () => import('@/components/_shared/logItemNode.vue')
  },
  created () {
    this.loadContent()
  },
  methods: {
    loadContent () {
      this.$store.dispatch('craigslist/getUserActivityLogDetails', {id: this.logId}).then(x => {
        this.logDetails = {
          nodes: x.data.model
        }
      }).catch(ex => {
        this.$toaster.error('Something went wrong!')
        this.$logger.handleError(ex, 'Can\'t get craigslist user activity detail log')
      })
    }
  }
}
</script>
