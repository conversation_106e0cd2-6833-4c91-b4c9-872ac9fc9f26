<template>
<ValidationObserver re="validator">
  <b-card>
    <b-row class="border-bottom mb-2 py-2">
      <b-col>
        <strong>{{reviseHeader}}</strong>
      </b-col>
      <c-button v-if="!isDisabled" :message="`Are you sure you want ${btnDesc}?`" variant="primary" size="sm" @confirm="onConfirm">
        {{btnDesc}}
      </c-button>
      <loader class="mr-5" v-else size="sm"/>
    </b-row>
    <detail-row bootstrapMode :fixed-payload-width="true">
      <span slot="title">Listing ID:</span>
      <b-link slot="payload" :href="getListingUrl" class="text-info"><u>{{revise.AuctionId}}</u></b-link>
    </detail-row>
    <detail-row bootstrapMode :fixed-payload-width="true">
      <span slot="title">Bid Status:</span>
      <span slot="payload">
        {{revise.BidsTotal > 0 ? revise.BidsTotal : 'No Bids'}}<span v-if="revise.BidsTotal > 0">(<b-link :href="getViewBidsUrl" class="text-info"><u>View Bid History</u></b-link>)</span>
      </span>
    </detail-row>
    <detail-row bootstrapMode :fixed-payload-width="true">
      <span slot="title">Current Advertised Price:</span>
      <span slot="payload">{{getPriceDesc(revise.StartPrice)}}</span>
    </detail-row>
    <ValidationProvider name="New Advertised Price" :rules="{is_not: `${revise.StartPrice}`, required: true}" v-slot="{errors}">
    <detail-row bootstrapMode title-position="start" :fixed-payload-width="true" :error="errors[0]">
      <span slot="title">New Advertised Price:</span>
      <b-form-input slot="payload" name="New_Advertised_Price" v-model="changeAdvertisedPriceData.NewAdvertisedPrice" type="number"></b-form-input>
    </detail-row>
    </ValidationProvider>
    <ValidationProvider name="Auto DEcline Amount" :rules="`max_value: ${changeAdvertisedPriceData.NewAdvertisedPrice - 1}`" v-slot="{errors}">
    <detail-row bootstrapMode title-position="start" :fixed-payload-width="true" :error="errors[0]">
      <span slot="title">Auto Decline Amount:</span>
      <b-form-input slot="payload" name="Auto_Decline_Amount" v-model="changeAdvertisedPriceData.AutoDeclineAmount" type="number"></b-form-input>
    </detail-row>
    </ValidationProvider>
  </b-card>
</ValidationObserver>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import loader from '@/components/_shared/loader'
import {mapGetters} from 'vuex'
import globals from '../../../globals'
import numeral from 'numeral'
import constants from '@/shared/ebay/constants'

export default {
  props: {
    btnDesc: {
      type: String,
      default: 'Revise'
    },
    reviseHeader: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      changeAdvertisedPriceData: {
        NewAdvertisedPrice: 0,
        AutoDeclineAmount: 0
      },
      isDisabled: false
    }
  },
  components: {
    detailRow,
    loader
  },
  mounted () {
    this.initData()
  },
  computed: {
    ...mapGetters('eBayRevise', ['revise']),
    getViewBidsUrl () {
      return constants.eBayInfoUrls.ebayViewBids(this.revise.AuctionId)
    },
    getListingUrl () {
      return constants.eBayInfoUrls.ebayItemUrl(this.revise.AuctionId)
    }
  },
  methods: {
    onConfirm () {
      this.$refs.validator.validate().then(res => {
        if (res) {
          this.isDisabled = true
          let apiParams = {
            accountId: this.revise.AccountId,
            auctionId: this.revise.AuctionId,
            data: this.changeAdvertisedPriceData
          }

          this.$store.dispatch('eBayRevise/changeAdvertisedPrice', apiParams).then(res => {
            this.$toaster.success('Changed Advertised Price Successfully')
          }).catch(ex => {
            this.$toaster.exception(ex, 'Something went wrong!')
            if (ex.response && ex.response.status !== 400) {
              this.$logger.handleError(ex, `Exception occurred on ${this.reviseHeader}`)
            }
          }).finally(() => {
            this.isDisabled = false
            setTimeout(() => this.$router.go(), 4000)
          })
        }
      })
    },
    getPriceDesc (value) {
      return numeral(value).format('$0,0')
    },
    initData () {
      this.changeAdvertisedPriceData = {
        NewAdvertisedPrice: globals().getClonedValue(this.revise.StartPrice),
        AutoDeclineAmount: globals().getClonedValue(this.revise.BestOfferAutoDeclinePrice)
      }
    }
  }
}
</script>
