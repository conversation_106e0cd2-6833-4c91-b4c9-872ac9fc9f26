const movingSiteHostingsTaskStatusTypes = Object.freeze({
  notFound: {value: 0, text: 'Not Found'},
  inProgress: {value: 1, text: 'In Progress'},
  completed: {value: 3, text: 'Completed'},
  failed: {value: 4, text: 'Failed'}
})

const databaseTypes = Object.freeze({
  mongo: { value: 1, text: 'MongoDb Server/Replica Set' },
  sql: { value: 2, text: 'MsSql Server' }
})

const fetchLevels = Object.freeze({
  settings: 'settings',
  vehicles: 'vehicles',
  categories: 'categories'
})

const syncMonitorTabTypes = Object.freeze({
  siteBoxFetcher: 0,
  foundationFetcher: 1,
  appsVehicleSynchronizer: 2,
  syncLog: 3
})

const userActivitySortTypes = Object.freeze({
  accountIdAsc: 1,
  accountIdDesc: 2,
  accountNameAsc: 3,
  accountNameDesc: 4,
  userNameAsc: 5,
  userNameDesc: 6,
  dateAsc: 7,
  dateDesc: 8,
  actionAsc: 9,
  actionDesc: 10,
  userTypeAsc: 11,
  userTypeDesc: 12
})

const userActivityActionTypes = Object.freeze({
  undefined: {value: 0, text: 'All Actions'},
  createConnection: {value: 1, text: 'Create Connection'},
  updateConnection: {value: 2, text: 'Update Connection'},
  deleteConnection: {value: 3, text: 'Delete Connection'},
  createSiteBox: {value: 4, text: 'Create SiteBox'},
  updateSiteBox: {value: 5, text: 'Update SiteBox'},
  deleteSiteBox: {value: 6, text: 'Delete SiteBox'},
  startSiteHostingMigration: {value: 7, text: 'Start Site Hosting Migration'},
  finishSiteHostingMigration: {value: 8, text: 'Finish Site Hosting Migration'},
  runFetching: {value: 9, text: 'Run Fetching'},
  cancelFetching: {value: 10, text: 'Cancel Fetching'},
  fetchSiteInformation: {value: 11, text: 'Fetch Site Information'},
  cleanupSiteInformation: {value: 12, text: 'Cleanup Site Information'},
  insertSiteHostingSettings: {value: 13, text: 'Insert Site Hosting Settings'},
  deleteSiteHostingSettings: {value: 14, text: 'Delete Site Hosting Settings'}
})

export {
  movingSiteHostingsTaskStatusTypes,
  databaseTypes,
  fetchLevels,
  syncMonitorTabTypes,
  userActivitySortTypes,
  userActivityActionTypes
}
