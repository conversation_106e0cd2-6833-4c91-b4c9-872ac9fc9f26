<template>
  <div>

    <div class="fullwidth-element mb-4" :class="{ 'bg-dark dark text-white' : blackThemeOn }">
      <div class="container-fluid container-p-y analytics-ga4-layout-head-container">
        <div class="title-col align-self-center">
          <h4>{{analyticsName}}</h4>
        </div>

        <div class="analytics-ga4-layout-buttons-container">
          <b-dropdown v-if="reportGroupsArray.length" variant="gold" block :text="selectedReport.name" class="report-selector button-col" menu-class="w-100">
            <template v-if="accountsArray.length">
              <b-dropdown-item
                v-for="account in accountsArray"
                :key="account.id"
                :active="account.id === selectedReport.id"
                :to="{name: accountLinkName, params: { accountId: account.id}, query: query }">
                {{account.name}}
              </b-dropdown-item>
              <b-dropdown-divider></b-dropdown-divider>
            </template>

            <b-dropdown-item
              v-for="group in reportGroupsArray"
              :key="group.id"
              :active="group.id === selectedReport.id"
              :to="{name: groupLinkName, params: {reportGroupId: group.id}, query: query}">
              {{group.name}}
            </b-dropdown-item>
          </b-dropdown>

          <range-selector
            @input="onRangeChanged"
            :value="filterDateRange"
            class="button-col"
          />
        </div>
      </div>
    </div>

    <router-view></router-view>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import analyticsBuilders from './../shared/analytics/builders'
import rangeHelper from '../components/analytics_ga4/rangeSelector/rangeHelper'

const filterManager = analyticsBuilders.getFilterManager({
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' }
})

export default {
  name: 'analytics-ga4-layout',
  props: {
    query: {
      type: Object,
      required: false
    },
    accountId: {
      type: Number,
      required: false
    },
    reportGroupId: {
      type: String,
      required: false
    }
  },
  components: {
    'range-selector': () => import('../components/analytics_ga4/rangeSelector/analyticsRangeSelector')
  },
  created () {
    this.filter = filterManager.urlHelper.parseQueryStringToObject(this.$router)
    this.loadDataFromApi()
  },
  data () {
    return {
      filter: filterManager.defaultValue,
      ownAccountSettings: null,
      foreignAccountSettings: null
    }
  },
  computed: {
    ...mapGetters('analyticsGa4', ['rangeInfo', 'analyticsName', 'blackThemeOn']),
    ...mapGetters('users', ['userInfo', 'analyticsUserInfo']),
    filterDateRange () {
      if ((this.filter || {}).dateFrom && (this.filter || {}).dateTo) {
        return [this.filter.dateFrom, this.filter.dateTo]
      }

      return null
    },

    accountLinkName () {
      if (this.accountId) {
        return this.$route.name
      } else if (this.reportGroupId) {
        return this.$route.meta.relatedPageName
      }
      return ''
    },
    groupLinkName () {
      if (this.accountId) {
        return this.$route.meta.relatedPageName
      } else if (this.reportGroupId) {
        return this.$route.name
      }
      return ''
    },
    accountsArray () {
      const toReturn = []
      if (this.foreignAccountSettings) {
        toReturn.push({ id: this.foreignAccountSettings.accountId, name: this.foreignAccountSettings.dealerInformation.companyName + ' (' + this.foreignAccountSettings.accountId + ')' })
      }
      if (this.ownAccountSettings) {
        toReturn.push({ id: this.ownAccountSettings.accountId, name: this.ownAccountSettings.dealerInformation.companyName + ' (' + this.ownAccountSettings.accountId + ')' })
      }
      return toReturn
    },
    reportGroupsArray () {
      if (this.analyticsUserInfo && this.analyticsUserInfo.accessibleReportGroups) {
        return this.analyticsUserInfo.accessibleReportGroups.map(x => ({ id: x.id, name: x.groupName }))
      }
      return []
    },
    selectedReport () {
      let selectedReport
      if (this.accountId) {
        selectedReport = this.accountsArray.find(x => x.id === this.accountId)
      } else if (this.reportGroupId) {
        selectedReport = this.reportGroupsArray.find(x => x.id === this.reportGroupId)
      }
      if (!selectedReport) {
        selectedReport = { id: '', name: '' }
      }
      return selectedReport
    }

  },
  methods: {
    async onRangeChanged (rangeInfo) {
      try {
        if (this.filter.dateFrom === rangeInfo.range[0] && this.filter.dateTo === rangeInfo.range[1] && this.rangeInfo) {
          return
        }
        this.filter.dateFrom = rangeInfo.range[0]
        this.filter.dateTo = rangeInfo.range[1]
        await this.$store.commit('analyticsGa4/setRangeInfo', rangeInfo)
      } catch (error) {
        this.$toaster.error(error)
        this.$logger.handleError(error, 'Can\'t set rangeInfo to store ', {rangeInfo: rangeInfo})
      }
    },
    async loadDataFromApi () {
      try {
        if (this.userInfo.user.accountId) {
          this.ownAccountSettings = await this.$store.dispatch('accountSettings/getAccountSettings', this.userInfo.user.accountId)
        }
        if (this.accountId && this.accountId !== this.userInfo.user.accountId) {
          this.foreignAccountSettings = await this.$store.dispatch('accountSettings/getAccountSettings', this.accountId)
        }
        await this.$store.dispatch('users/fillAnalyticsInformationForUser')
      } catch (error) {
        this.$toaster.error(error)
        this.$logger.handleError(error, 'Can\'t load data from api ')
      }
    }
  },
  watch: {
    $route (to, from) {
      this.filter = filterManager.urlHelper.parseQueryStringToObject(this.$router)
      if (this.filter.dateFrom === filterManager.defaultValue.dateFrom || this.filter.dateTo === filterManager.defaultValue.dateTo) {
        let defaultRange = rangeHelper.defaultRange.asFormattedStrings()
        this.filter.dateFrom = defaultRange[0]
        this.filter.dateTo = defaultRange[1]
      }
    }
  }
}
</script>

<style >
.report-selector button{
  border-radius: 30px;
  min-width: 270px;
  /*for safari*/
  background-color: #f0f0f0;
}

@media(max-width: 991px) {
  .analytics-ga4-layout-buttons-container .button-col button {
    width: 100%;
  }
}
</style>

<style scoped>
.report-selector .dropdown-menu a{
  text-overflow: ellipsis;
  overflow: hidden;
  color: black;
}

.report-selector .dropdown-menu > a.active{
  background-color: #d0d0d0
}

.report-selector .dropdown-menu > a:active {
  background-color: #dddddd
}

.fullwidth-element > .analytics-ga4-layout-head-container {
  padding-bottom: 0 !important;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.analytics-ga4-layout-head-container .title-col {
  align-self: center;
}

.analytics-ga4-layout-head-container .title-col h4 {
  margin: 0;
}

.analytics-ga4-layout-buttons-container {
  display: flex;
  flex-wrap: wrap;
}

.analytics-ga4-layout-buttons-container .button-col:first-child {
  padding-right: 5px;
}

.analytics-ga4-layout-buttons-container .button-col:last-child {
  padding-left: 5px;
}

@media(max-width: 991px) {
  .analytics-ga4-layout-head-container > div {
    flex-basis: 100%;
    margin-bottom: 15px;
  }
}

@media(max-width: 991px) and (min-width: 768px) {
  .analytics-ga4-layout-buttons-container .button-col {
    flex-grow: 1;
    width: 0px;
  }
}

@media(max-width: 767px) {
  .analytics-ga4-layout-buttons-container > div {
    flex-basis: 100%;
    margin-bottom: 15px;
  }

  .analytics-ga4-layout-buttons-container .button-col{
    padding: 0 !important;
  }
}
</style>
