<template>
  <div>
    <div v-if="isEbizDev && errorIsFetched">
      <div class="d-flex justify-content-end">
        <b-btn size="sm" variant="primary" class="btn-round mb-3" @click="onShowAddNewErrorRuleTool">
          <span class="mr-1">Add New Error Rule</span>
          <span v-if="isAddNewErrorRuleToolOpen"><i class="ion ion-ios-arrow-up opacity-75"></i></span>
          <span v-else><i class="ion ion-ios-arrow-down opacity-75"></i></span>
        </b-btn>
      </div>
      <b-card v-if="isAddNewErrorRuleToolOpen">
        <h5>Add New Error Rule</h5>
        <b-form-radio-group
          v-model="errorRuleModel.errorReportType"
          :options="errorReportTypeOptions"
          name="error-report-types"
        />
        <b-form-group class="mt-2">
          <span class="text-muted">Error Regex:</span>
          <b-form-input w-90 placeholder="ex: first rule item%second rule item%..." v-model="errorRuleModel.errorRuleBody"></b-form-input>
        </b-form-group>
        <b-form-group class="my-2">
          <span class="text-muted">Error comment:</span>
          <b-form-input w-90 v-model="errorRuleModel.errorRuleComment"></b-form-input>
        </b-form-group>
        <b-btn @click="onAddNewErrorRule" size="sm" variant="primary">Submit</b-btn>
      </b-card>
    </div>
    <b-card>
      <loader v-if="!errorIsFetched" size="sm" />
      <div v-if="errorIsFetched">
        <div class="text-center">
          <h2>Error body View for ErrorID {{error.id}}
            <br>Category - {{error.errorCategoryLabel}}
            <br>Error level - {{error.errorLevelLabel}}
          </h2>
        </div>
        <b-table
          id ="ErrorDetails"
          table-variant="secondary"
          :items="[error]"
          :fields="getDetailsErrorField"
          bordered
          responsive
        >
        </b-table>
        <div class="body-scroll-x">
          <div class="error-details-body exception-body" v-html="error.body"/>
        </div>
      </div>
    </b-card>
  </div>
</template>

<script>
import loader from '@/components/_shared/loader'
import moment from 'moment-timezone'
import { errorReportTypes, errorMonitorTimezone } from '@/shared/errorReport/constants'
import {mapGetters} from 'vuex'

export default {
  name: 'error-details',
  props: {
    errorId: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      error: {},
      errorRuleModel: {
        errorReportType: 1,
        errorRuleBody: '',
        errorRuleComment: ''
      },
      isAddNewErrorRuleToolOpen: false
    }
  },
  components: {
    'loader': loader
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {}
    },
    isEbizDev () {
      return this.user.isEbizDev
    },
    errorIsFetched () {
      return this.error.body !== undefined
    },
    errorReportTypeOptions () {
      return Object.values(errorReportTypes)
    },
    getDetailsErrorField () {
      return [
        {
          key: 'applicationName',
          label: 'Application Name'
        },
        {
          key: 'subject',
          label: 'Subject'
        },
        {
          key: 'machineName',
          label: 'Machine Name'
        },
        {
          key: 'clientIpAddress',
          label: 'Client Ip Address'
        },
        {
          key: 'errorLocalDateTime',
          label: 'Error DateTime',
          formatter: value => this.formatDate(value)
        },
        {
          key: 'requestInsertedDateTime',
          label: 'Date Inserted',
          formatter: value => this.formatDate(value)
        }
      ]
    }
  },
  mounted () {
    this.populateError()
  },
  methods: {
    formatDate (str) {
      return moment(str).tz(errorMonitorTimezone).format('MM/DD/YYYY HH:mm:ss')
    },
    populateError () {
      return this.$store.dispatch('systemTools/getErrorDetails', this.errorId)
        .then(x => {
          this.error = x.data.model
        })
        .catch(ex => {
          if (ex.response && ex.response.status && ex.response.status === 400) {
            this.$toaster.error(ex.response.data.executionResultMessage + ' ' + this.errorId, { timeout: 8000 })
          } else {
            this.$toaster.error(`Error Occurred on call api.`)
            this.$logger.handleError(ex, 'Can\'t get error', this.errorId)
          }

          this.$router.push({name: 'error-monitor'})
        })
    },
    onAddNewErrorRule () {
      this.$store.dispatch('systemTools/createErrorRule', this.errorRuleModel).then(res => {
        this.$toaster.success(`New Error Rule Successfully Created`)
      }).catch(ex => {
        if (ex.response && ex.response.status === 400 && ex.response.data && ex.response.data.executionResultMessage) {
          this.$toaster.error(ex.response.data.executionResultMessage)
        } else {
          let message = ex.response ? ex.response.data : ex.message
          this.$toaster.error(`Error Occurred on call api. Message: ${message}`)
          this.$logger.handleError(ex, 'Error Report Exception. Method: onAddNewErrorRule')
        }
      }).finally(() => {
        this.errorRuleModel.errorRuleBody = ''
        this.errorRuleModel.errorRuleComment = ''
      })
    },
    onShowAddNewErrorRuleTool () {
      this.isAddNewErrorRuleToolOpen = !this.isAddNewErrorRuleToolOpen
    }
  }
}
</script>

<style scoped>
h2, .h2 {
  font-size: 25px;
}
@media (max-width: 900px) {
  .exception-body {
    width: 900px;
  }
  .body-scroll-x {
    overflow-x: scroll;
  }
}
</style>

<style>
.error-details-body pre {
  white-space: pre-wrap;
  margin: 0px;
  border: #a0a0c0 1px solid;
  background: #e8faff;
}
</style>
