<template>
  <div>
    <div>
      <h4>Account Settings</h4>
    </div>
    <b-card>
      <defaultViewSettingSection :accountId="accountId" />
      <analyticsAuthorizationSettingSection v-if="hasGoogleAnalyticsFullAccess" :accountId="accountId"/>
    </b-card>
  </div>
</template>

<script>
import defaultViewSettingSection from '../../components/analytics_ga4/accountSettings/defaultViewSettingSection.vue'
import analyticsAuthorizationSettingSection from '../../components/analytics_ga4/accountSettings/analyticsAuthorizationSettingSection.vue'
import { mapGetters } from 'vuex'
import permissions from '../../shared/common/permissions'

export default {
  name: 'settings',
  metaInfo: {
    title: 'Analytics Account Settings'
  },
  props: {
    accountId: {
      type: Number,
      required: false
    }
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {hasPermissions: () => false}
    },
    hasGoogleAnalyticsFullAccess () {
      return this.user.hasPermissions(permissions.AnalyticsFullAccess)
    }
  },
  components: {
    defaultViewSettingSection,
    analyticsAuthorizationSettingSection
  }
}
</script>
