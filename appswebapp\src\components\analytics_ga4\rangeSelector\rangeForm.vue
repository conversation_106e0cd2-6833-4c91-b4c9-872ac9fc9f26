<template>
  <b-form>
    <b-row class="mb-3 align-items-center">
      <b-col cols="3">
        <label for="daterange-year">Year</label>
      </b-col>
      <b-col cols="9">
        <b-form-input id="daterange-year" size="md" type="number" v-model.number="selectedYear"></b-form-input>
      </b-col>
    </b-row>
    <b-row class="mb-3 align-items-center">
      <b-col cols="3">
        <label for="daterange-quarter">Quarter</label>
      </b-col>
      <b-col cols="9">
        <b-form-select id="daterange-quarter" size="md" v-model="selectedQuarter" :options="optionsQuarter" />
      </b-col>
    </b-row>
    <b-row class="mb-3 align-items-center">
      <b-col cols="3">
        <label for="daterange-month">Month</label>
      </b-col>
      <b-col cols="9">
        <b-form-select id="daterange-month" size="md" v-model="selectedMonth" :options="optionsMonth" />
      </b-col>
    </b-row>
    <b-row>
      <b-col>
        <b-button :disabled="disabled" variant="primary btn-round" class="ml-auto d-block" @click="notifySubmitClicked">{{ actionText }}</b-button>
      </b-col>
    </b-row>
  </b-form>
</template>

<script>
import dateHelper from '@/plugins/locale/date'
import analyticsConstants from '@/shared/analytics/constants'

const componentDefaults = {
  minDate: new Date(2000, 1, 1)
}

export default {
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    year: Number,
    quarter: Number,
    month: Number,
    actionText: {
      type: String,
      default: 'Apply'
    }
  },
  data () {
    return {
      selectedYear: 0,
      selectedQuarter: 0,
      selectedMonth: 0,
      optionsQuarter: analyticsConstants.optionsQuarter.map(
        x => Object.assign({ disabled: false }, x)
      ),
      optionsMonth: analyticsConstants.optionsMonth.map(
        x => Object.assign({ disabled: false }, x)
      )
    }
  },
  methods: {
    notifySubmitClicked () {
      this.$emit('submited', {
        year: this.selectedYear,
        quarter: this.selectedQuarter,
        month: this.selectedMonth
      })
    }
  },
  created () {
    this.selectedYear = this.year
    this.selectedQuarter = this.quarter
    this.selectedMonth = this.month
  },
  watch: {
    selectedMonth (newVal) {
      if (newVal > 0) {
        this.selectedQuarter = 0
      }
    },
    selectedQuarter (newVal) {
      if (newVal > 0) {
        this.selectedMonth = 0
      }
    },
    selectedYear (newVal) {
      const nowDate = new Date()
      const nowYear = nowDate.getFullYear()
      const nowQuarter = dateHelper.getQuarterFromDate(nowDate)
      const nowMonth = nowDate.getMonth()
      const isYearValid = newVal > componentDefaults.minDate.getFullYear() && newVal <= nowYear
      const isNowYearSelected = newVal === nowYear

      this.optionsMonth.forEach(x => {
        x.disabled = !isYearValid || (isNowYearSelected && x.value > nowMonth)
      })
      const hasEnabledMonth = !!this.optionsMonth.find(x => x.value > 0 && !x.disabled)
      this.selectedMonth = hasEnabledMonth ? this.selectedMonth : 0

      this.optionsQuarter.forEach(x => {
        x.disabled = !isYearValid || (isNowYearSelected && x.value > nowQuarter)
      })
      const hasEnabledQuarter = !!this.optionsQuarter.find(x => x.value > 0 && !x.disabled)
      this.selectedQuarter = hasEnabledQuarter ? this.selectedQuarter : 0
    },
    year (newVal) {
      this.selectedYear = newVal
    },
    quarter (newVal) {
      this.selectedQuarter = newVal
    },
    month (newVal) {
      this.selectedMonth = newVal
    }
  }
}
</script>
