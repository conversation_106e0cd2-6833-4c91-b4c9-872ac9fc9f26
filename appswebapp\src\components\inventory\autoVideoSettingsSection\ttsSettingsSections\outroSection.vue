<template>
  <editSettingsHelper :readOnlyMode="readOnlyMode" @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="Outro" :isLoading="isUpdatingProcessed" :isDisabled="isDisabled" :isViewMode="isViewMode">
    <template slot="settings-content">
      <detail-row v-if="accountLevel" :fixed-payload-width="true" :title-position="'start'">
        <span slot="title">Use Custom Outro Settings:</span>
        <b-form-checkbox v-if="!isViewMode" slot="payload" v-model="updatedSettings.hasToUseCustomOutroSettings"></b-form-checkbox>
        <span v-else slot="payload">
          {{ updatedSettings.hasToUseCustomOutroSettings ? 'Yes' : 'No' }}
        </span>
      </detail-row>
      <b-overlay :show="!(!accountLevel || updatedSettings.hasToUseCustomOutroSettings)" opacity="0.7" blur="1.2px">
        <template #overlay>
          <span></span>
        </template>
        <ValidationProvider name="Outro Versions" ref="outroVersionsValidator" v-slot="{errors}" :detectInput="false">
          <detail-row :fixed-payload-width="true" :title-position="'start'" :error="errors[0]">
            <span slot="title">Outro Versions</span>
            <div slot="payload" class="w-100">
              <b-form-group v-for="(outro, index) in updatedSettings.outroItems" :key="index"
                :state="outro.outroText ? null : false" :invalid-feedback="outro.outroText ? '' : 'Outro text is required'">
                <template #label>
                  <b-form-checkbox v-model="outro.isEnabled" :disabled="isViewMode">
                    Enabled
                  </b-form-checkbox>
                </template>
                <b-input-group>
                  <b-form-input v-model="outro.outroText" style="border-right: none;" :disabled="isViewMode"></b-form-input>
                  <b-input-group-append v-if="!isViewMode" @click="removeOutro(index)" style="cursor: pointer;" is-text>
                    <b-icon icon="x" aria-hidden="true" scale="1.2"/>
                  </b-input-group-append>
                </b-input-group>
              </b-form-group>
              <b-form-group v-if="!isViewMode">
                <template #label>
                  <b-form-checkbox v-model="outro.isEnabled">
                    Enabled
                  </b-form-checkbox>
                </template>
                <b-input-group>
                  <b-form-input v-model="outro.outroText"></b-form-input>
                  <b-input-group-append @click="addOutro()" style="cursor: pointer;">
                    <b-input-group-text class="bg-primary text-white">
                      <b-icon icon="check" aria-hidden="true" scale="1.2"/>
                    </b-input-group-text>
                  </b-input-group-append>
                </b-input-group>
              </b-form-group>
            </div>
          </detail-row>
        </ValidationProvider>
      </b-overlay>
    </template>
  </editSettingsHelper>
</template>

<script>
import editSettingsHelper from '@/components/_shared/editSettingsHelper'
import detailRow from '@/components/details/helpers/detailRow'
import editSettingsMixin from '../editSettingsMixin'
import globals from '../../../../globals'

export default {
  name: 'tts-outro-settings-section',
  props: {
    settings: { type: Object, required: true },
    isUpdatingProcessed: { type: Boolean, required: true },
    isDisabled: Boolean,
    accountLevel: Boolean,
    readOnlyMode: Boolean
  },
  data () {
    return {
      updatedSettings: globals().getClonedValue(this.settings),
      isViewMode: true,
      outro: {
        outroText: '',
        isEnabled: true
      }
    }
  },
  mixins: [editSettingsMixin],
  components: {
    detailRow,
    editSettingsHelper
  },
  methods: {
    addOutro () {
      if (!this.updatedSettings.outroItems) {
        this.updatedSettings.outroItems = []
      }
      this.updatedSettings.outroItems.push(this.outro)
      this.outro = {
        outroText: '',
        isEnabled: true
      }
      this.validateOutroVersions()
    },
    removeOutro (index) {
      this.updatedSettings.outroItems.splice(index, 1)
      this.validateOutroVersions()
    },
    updateSettings () {
      if (this.validateOutroVersions()) {
        this.$emit('saveChanges', this.updatedSettings)
      }
    },
    validateOutroVersions () {
      this.$refs.outroVersionsValidator.reset() // clean previously validation results
      if (this.accountLevel && !this.updatedSettings.hasToUseCustomOutroSettings) {
        return true
      }
      if (!this.updatedSettings.outroItems || this.updatedSettings.outroItems.length === 0) {
        this.$refs.outroVersionsValidator.applyResult({
          errors: ['The Outro Versions field must have at least one elements'],
          valid: false,
          failedRules: {}
        })
        return false
      }
      if (this.updatedSettings.outroItems && this.updatedSettings.outroItems.length > 0) {
        return !this.updatedSettings.outroItems.some(x => !x.outroText)
      }
      return true
    }
  },
  watch: {
    'updatedSettings': {
      deep: true,
      handler: function () {
        this.$emit('input', this.updatedSettings)
      }
    }
  }
}
</script>
