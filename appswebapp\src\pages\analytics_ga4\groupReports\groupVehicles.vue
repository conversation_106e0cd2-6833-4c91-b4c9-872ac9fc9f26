<template>
  <div>
    <vehicles-summary
      :barItems="bar.items"
      :summary="summary"
      :barTimeFormat="barTimeFormat"
    ></vehicles-summary>

    <div v-if="isAccountLevel">
      <account-level-card v-if="isAccountLevel" @backToGroup="onBackToGroup">
        {{accountName}} ({{page.filter.accountId}})
      </account-level-card>

      <vehicles-by-vehicle-table
        :tableItems="table.items"
        :totalItems="table.totalItems"
        :pageNumber="page.filter.pageNumber"
        :pageSize="page.filter.pageSize"
        :sortType="page.filter.sortType"
        :showAbsoluteUrl="true"
        @pageNumberChanged="onPageNumberChanged"
        @pageSizeChanged="onPageSizeChanged"
        @sortTypeChanged="onSortTypeChanged"
      ></vehicles-by-vehicle-table>
    </div>
    <div v-else>
      <group-report-tables-selector
        defaultSegmentName="Vehicle"
        :isSegmentedByAccountSelected="this.page.filter.segmentedByAccount"
        @reportTablesSelectorChanged="onReportTablesSelectorChanged"
      ></group-report-tables-selector>
      <vehicles-by-vehicle-table
        v-if="!page.filter.segmentedByAccount"
        :tableItems="table.items"
        :totalItems="table.totalItems"
        :pageNumber="page.filter.pageNumber"
        :pageSize="page.filter.pageSize"
        :sortType="page.filter.sortType"
        @pageNumberChanged="onPageNumberChanged"
        @pageSizeChanged="onPageSizeChanged"
        @sortTypeChanged="onSortTypeChanged"
      ></vehicles-by-vehicle-table>
      <group-vehicles-by-account-table
        v-else-if="page.filter.segmentedByAccount"
        :tableItems="table.items"
        :totalItems="table.totalItems"
        :pageNumber="page.filter.pageNumber"
        :pageSize="page.filter.pageSize"
        :sortType="page.filter.sortType"
        @pageNumberChanged="onPageNumberChanged"
        @pageSizeChanged="onPageSizeChanged"
        @sortTypeChanged="onSortTypeChanged"
        @accountNameClicked="onAccountNameClicked"
      ></group-vehicles-by-account-table>
    </div>
  </div>
</template>

<script>
import analyticsConstants from './../../../shared/analytics/constants'
import analyticsBuilders from './../../../shared/analytics/builders'
import analyticsHelper from '../helpers.js'
import baseGroupReportPage from './baseGroupReportPage.js'

import VehiclesSummary from '../../../components/analytics_ga4/summaries/vehiclesSummary'
import AccountLevelCard from '../../../components/analytics_ga4/accountLevelCard'
import VehiclesByVehicleTable from '../../../components/analytics_ga4/tables/vehiclesByVehicleTable'
import GroupReportTablesSelector from '../../../components/analytics_ga4/groupReportTablesSelector'
import GroupVehiclesByAccountTable from '../../../components/analytics_ga4/tables/groupVehiclesByAccountTable'

const filterManager = analyticsBuilders.getFilterManager({
  accountId: { type: Number, default: 0 },
  segmentedByAccount: { type: Boolean, default: false },
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  pageNumber: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  sortType: { type: Number, default: analyticsConstants.vehicleSortType.pageViewsDesc }
})

export default {
  mixins: [baseGroupReportPage],
  name: 'group-vehicles',
  metaInfo: {
    title: 'Analytics - Vehicles'
  },
  components: {
    GroupVehiclesByAccountTable,
    GroupReportTablesSelector,
    VehiclesByVehicleTable,
    AccountLevelCard,
    VehiclesSummary
  },
  created () {
    this.$store.commit('analyticsGa4/setAnalyticsName', 'Vehicles')
    this.$store.commit('analyticsGa4/setBlackThemeOn', true)
  },
  data () {
    return {
      page: {
        filter: filterManager.defaultValue
      },
      summary: {
        pageViews: 0,
        pageViewsDelta: null,
        formLeads: 0,
        formLeadsDelta: null
      }
    }
  },
  computed: {
    filterManager () {
      return filterManager
    },
    defaultAccountLevelSortType () {
      return filterManager.defaultValue.sortType
    }
  },
  methods: {
    getAccountNameSortTypeCondition () {
      return this.page.filter.sortType === analyticsConstants.vehicleSortType.accountNameAsc ||
        this.page.filter.sortType === analyticsConstants.vehicleSortType.accountNameDesc
    },
    getReportTablesSelectorSortTypeCondition () {
      return this.page.filter.sortType === analyticsConstants.vehicleSortType.accountNameAsc ||
        this.page.filter.sortType === analyticsConstants.vehicleSortType.accountNameDesc ||
        this.page.filter.sortType === analyticsConstants.vehicleSortType.naturalAsc ||
        this.page.filter.sortType === analyticsConstants.vehicleSortType.naturalDesc
    },
    async updateStatistics () {
      try {
        await Promise.all([
          !this.isAccountLevel ? this.updateGroupLevelGraphAndSummary() : null,
          !this.isAccountLevel && this.page.filter.segmentedByAccount ? this.updateGroupTableByAccounts() : null,
          !this.isAccountLevel && !this.page.filter.segmentedByAccount ? this.updateGroupTableByVehicles() : null,
          this.isAccountLevel ? this.updateAccountLevelGraphAndSummary() : null,
          this.isAccountLevel ? this.updateAccountTableByVehicles() : null
        ])
      } catch (err) {
        this.$toaster.error('Failed to get data from server', { timeout: 8000 })
        this.$logger.handleError(err, 'Can\'t update statistics', { filter: this.page.filter, cache: this.cache })
      }
    },
    async updateGroupLevelGraphAndSummary () {
      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getGroupVehiclesGraphAndSummary',
        {
          reportGroupId: this.reportGroupId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo
        }
      )

      this.summary = {
        ...this.summary,
        ...store.summary.data
      }
      this.bar.items = store.graph.data
    },
    async updateAccountLevelGraphAndSummary () {
      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getVehiclesGraphAndSummary',
        {
          accountId: this.page.filter.accountId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo
        }
      )

      this.summary = {
        ...this.summary,
        ...store.summary.data
      }
      this.bar.items = store.graph.data
    },
    async updateGroupTableByVehicles () {
      const pageData = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getGroupVehiclesDetailsPageByVehicle',
        {
          reportGroupId: this.reportGroupId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo,
          pageNumber: this.page.filter.pageNumber,
          pageSize: this.page.filter.pageSize,
          sortType: this.page.filter.sortType
        }
      )

      this.table.items = pageData.detailedData.data.items.map(x => {
        x.vehicleLabel = `${x.year} ${x.make} ${x.model} - ${x.vin}`
        return x
      })
      this.table.totalItems = pageData.detailedData.data.totalItems
    },
    async updateGroupTableByAccounts () {
      const pageData = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getGroupVehiclesDetailsPageByAccount',
        {
          reportGroupId: this.reportGroupId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo,
          pageNumber: this.page.filter.pageNumber,
          pageSize: this.page.filter.pageSize,
          sortType: this.page.filter.sortType
        }
      )

      this.table.items = pageData.detailedData.data.items.map(x => {
        x.account.isAccessAllowed = this.isAccessToAccountAllowed(x.account.accountId)
        return x
      })
      this.table.totalItems = pageData.detailedData.data.totalItems
    },
    async updateAccountTableByVehicles () {
      const siteSettings = await this.$store.dispatch('siteSettings/getSiteSettings', this.page.filter.accountId)
      const pageData = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getVehiclesDetailsPage',
        {
          accountId: this.page.filter.accountId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo,
          pageNumber: this.page.filter.pageNumber,
          pageSize: this.page.filter.pageSize,
          sortType: this.page.filter.sortType
        }
      )
      const siteDefaultUrl = siteSettings && siteSettings.urlWithProtocol
        ? siteSettings.urlWithProtocol
        : ''

      this.table.items = pageData.detailedData.data.items.map(x => {
        x.vehicleLabel = `${x.year} ${x.make} ${x.model} - ${x.vin}`
        x.absoluteUrl = siteDefaultUrl + x.pagePath

        return x
      })
      this.table.totalItems = pageData.detailedData.data.totalItems
    }
  }
}
</script>

<style src="@/vendor/styles/pages/analytics.scss" lang="scss"></style>
