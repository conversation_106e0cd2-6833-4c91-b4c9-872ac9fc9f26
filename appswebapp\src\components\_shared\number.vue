<template>
  <span class="tweened-number">{{ tweeningValue }}</span>
</template>

<script>

export default {
  props: {
    value: {
      type: Number,
      required: true
    },
    tweenDuration: {
      type: Number,
      default: 500
    }
  },

  watch: {
    value (newVal, oldVal) {
      this.tween(oldVal, newVal)
    }
  },
  data () {
    return {
      tweeningValue: 0
    }
  },
  mounted () {
    this.tween(0, this.value)
  },
  methods: {
    tween (start, end) {
      this.$tweening({
        tween: 'tweeningValue',
        from: start,
        to: end,
        within: this.tweenDuration,
        via: this.$tweening.Easing.Quadratic.Out,
        rounded: true
      })
    }
  }
}
</script>
