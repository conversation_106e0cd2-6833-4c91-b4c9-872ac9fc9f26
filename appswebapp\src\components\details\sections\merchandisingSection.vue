<template>
  <details-section title="Merchandising" v-model="mode" @cancel="onCancel">
    <div class="view" v-if="mode === 'view'">

      <auto-detail-row title="eBiz Keywords" :text="vehicle.marketingInformation.eBizKeywords"/>

      <auto-detail-row title="Homepage Featured" :text="getBooleanDescription(vehicle.marketingInformation.isHomepageFeatured)"/>

      <auto-detail-row title="Internet Special" :text="getBooleanDescription(vehicle.marketingInformation.hasToIncludePromotionalFlag)"/>

    </div>

    <div class="edit" v-else-if="mode === 'edit'">

      <auto-detail-row title="eBiz Keywords" v-model="vehicle.marketingInformation.eBizKeywords" validationRule="min:0|xml"/>

      <detail-row fixedPayloadWidth editMode>
        <span slot="title">Homepage Featured:</span>
        <b-form-checkbox v-model="vehicle.marketingInformation.isHomepageFeatured" slot="payload">{{getBooleanDescription(vehicle.marketingInformation.isHomepageFeatured)}}</b-form-checkbox>
      </detail-row>

      <detail-row fixedPayloadWidth editMode>
        <span slot="title">Internet Special:</span>
        <b-form-checkbox v-model="vehicle.marketingInformation.hasToIncludePromotionalFlag" slot="payload">{{getBooleanDescription(vehicle.marketingInformation.hasToIncludePromotionalFlag)}}</b-form-checkbox>
      </detail-row>

    </div>

  </details-section>
</template>

<script>
import { mapGetters } from 'vuex'
import detailRow from '../helpers/detailRow'
import detailsSection from '@/components/details/detailsSection'
import autoDetailRow from '../helpers/autoDetailRow'

export default {
  name: 'vehicle-history-section',
  data () {
    return {
      mode: 'view'
    }
  },
  computed: {
    ...mapGetters('details', ['vehicle'])
  },
  components: {
    'details-section': detailsSection,
    'detail-row': detailRow,
    'auto-detail-row': autoDetailRow
  },
  methods: {
    getBooleanDescription (boolValue) {
      return boolValue ? 'Yes' : 'No'
    },
    onCancel () {
      this.$forceUpdate()
    }
  }
}
</script>

<style scoped lang="scss">
</style>
