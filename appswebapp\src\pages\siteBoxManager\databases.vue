<template>
  <div>
    <b-row>
      <b-col>
        <h4>Databases</h4>
      </b-col>
      <b-col>
        <b-btn variant="primary btn-round" class="float-right" size="sm" @click="onAddDatabase"><span class="ion ion-ios-add"></span><span class="d-none d-sm-inline">&nbsp; Add Databases</span></b-btn>
      </b-col>
    </b-row>
    <b-card v-if="!isLoading && items && items.length > 0">
      <b-table
        :items="items"
        :fields="getTableFields"
        hover
        striped
        responsive
      >
        <template #cell(type)="data">
          {{getTypeDesc(data.item)}}
        </template>
        <template #cell(manage)="data">
          <b-btn size="sm" @click="onEdit(data.item)">Edit</b-btn>
          <c-button size="sm" variant="primary" :message="'Are you sure want to delete database?'" @confirm="onDelete(data.item.id)">Delete</c-button>
        </template>
      </b-table>
    </b-card>
    <div v-else-if="isLoading" class="py-3 my-3">
      <loader size="lg"/>
    </div>
    <div v-else>
      <span class="text-muted">Not Found</span>
    </div>

    <b-modal
      :title="isEditMode ? 'Edit Databases' : 'Add Databases'"
      :visible="isVisibleModal"
      size="lg"
      @hide="onHideModal"
    >
      <ValidationObserver ref="validator">
        <ValidationProvider name="Name" rules="required" v-slot="{errors}">
        <detail-row title-position="start" :fixed-payload-width="true" :error="errors[0]">
          <span slot="title">Name:</span>
          <b-form-input slot="payload" name="Name" v-model="itemModal.name"></b-form-input>
        </detail-row>
        </ValidationProvider>
        <detail-row title-position="start" :fixed-payload-width="true">
          <span slot="title">Type:</span>
          <b-form-select slot="payload" v-model="itemModal.type" :options="getDatabaseTypeOptions"></b-form-select>
        </detail-row>
        <detail-row title-position="start" :large-payload-width="true" v-if="databaseTypes.sql.value === itemModal.type">
          <span slot="title">Connection Strings:</span>
          <multiInput slot="payload" v-model="itemModal.connectionStrings" :values="itemModal.connectionStrings"/>
        </detail-row>
        <ValidationProvider v-else name="Connection string" rules="required" v-slot="{errors}">
        <detail-row title-position="start" :large-payload-width="true" :error="errors[0]">
          <span slot="title">Connection String:</span>
          <b-form-input slot="payload" name="Connection-String" v-model="itemModal.connectionString"></b-form-input>
        </detail-row>
        </ValidationProvider>
      </ValidationObserver>
      <template #modal-footer>
        <b-btn size="sm" @click="onHideModal">Cancel</b-btn>
        <b-btn size="sm" variant="primary" @click="onSubmit">Submit</b-btn>
      </template>
    </b-modal>
  </div>
</template>

<script>
import globals from '@/globals'
import loader from '@/components/_shared/loader'
import { databaseTypes } from '@/shared/siteBoxManager/common/constants'
import detailRow from '@/components/details/helpers/detailRow'
import multiInput from '@/components/_shared/multiInput'

export default {
  metaInfo: {
    title: 'Databases'
  },
  data () {
    return {
      items: [],
      itemModal: {},
      isVisibleModal: false,
      isEditMode: false,
      isLoading: true,
      databaseTypes
    }
  },
  components: {
    loader,
    detailRow,
    multiInput
  },
  computed: {
    getTableFields () {
      return [
        {
          key: 'id',
          label: 'Id',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'name',
          label: 'Name',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'type',
          label: 'Type',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    getDatabaseTypeOptions () {
      return Object.values(databaseTypes)
    }
  },
  created () {
    this.populateData()
  },
  methods: {
    onHideModal () {
      this.isVisibleModal = false
      this.itemModal = {}
    },
    onAddDatabase () {
      this.isEditMode = false
      this.itemModal = {
        name: '',
        type: 1,
        connectionStrings: [],
        connectionString: ''
      }
      this.isVisibleModal = true
    },
    onEdit (item) {
      this.itemModal = globals().getClonedValue(item)
      if (item.type !== databaseTypes.sql.value) {
        this.itemModal.connectionString = globals().getClonedValue(item.connectionStrings[0])
        this.itemModal.connectionStrings = []
      }
      this.isEditMode = true
      this.isVisibleModal = true
    },
    onSubmit () {
      this.$refs.validator.validate().then(isValid => {
        if (isValid) {
          if (this.itemModal.type === databaseTypes.sql.value && this.itemModal.connectionStrings.length === 0) {
            this.$toaster.error('Connection Strings cannot be empty')
          } else {
            this.doAction()
          }
        }
      })
    },
    doAction () {
      if (this.itemModal.type !== databaseTypes.sql.value) {
        this.itemModal.connectionStrings = [this.itemModal.connectionString]
      }
      if (!this.isEditMode) {
        this.$store.dispatch('siteBoxManager/createConnection', {data: this.itemModal}).then(res => {
          this.$toaster.success('Added Database Successfully')
          this.onHideModal()
          this.populateData()
        }).catch(ex => {
          this.$toaster.exception(ex, 'Something went wrong!')
        })
      } else {
        this.$store.dispatch('siteBoxManager/updateConnection', {id: this.itemModal.id, data: this.itemModal}).then(res => {
          this.$toaster.success('Updated Database Successfully')
          this.onHideModal()
          this.populateData()
        }).catch(ex => {
          this.$toaster.exception(ex, 'Something went wrong!')
        })
      }
    },
    onDelete (id) {
      this.$store.dispatch('siteBoxManager/deleteConnection', {id: id}).then(res => {
        this.$toaster.success('Deleted Database Successfully')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
      }).finally(() => {
        this.populateData()
      })
    },
    populateData () {
      this.$store.dispatch('siteBoxManager/getConnections').then(res => {
        this.items = res.data.items
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
      }).finally(() => {
        this.isLoading = false
      })
    },
    getTypeDesc (item) {
      let res = Object.values(databaseTypes).find(x => x.value === item.type)
      return (res || {text: '-'}).text
    }
  }
}
</script>
