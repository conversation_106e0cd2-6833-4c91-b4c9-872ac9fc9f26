<template>
  <div>
    <paid-search-summary
      :barItems="bar.items"
      :summary="summary"
      :barTimeFormat="barTimeFormat"
    ></paid-search-summary>

    <paid-search-account-level-table
      :tableItems="table.items"
      :totalItems="table.totalItems"
      :pageNumber="page.filter.pageNumber"
      :pageSize="page.filter.pageSize"
      :sortType="page.filter.sortType"
      @pageNumberChanged="onPageNumberChanged"
      @pageSizeChanged="onPageSizeChanged"
      @sortTypeChanged="onSortTypeChanged"
    ></paid-search-account-level-table>
  </div>
</template>

<style src="@/vendor/styles/pages/analytics.scss" lang="scss"></style>

<script>
import analyticsConstants from './../../shared/analytics/constants'
import analyticsBuilders from './../../shared/analytics/builders'
import analyticsHelper from './helpers.js'
import baseReportPage from './baseReportPage.js'

import PaidSearchSummary from '../../components/analytics_ga4/summaries/paidSearchSummary'
import PaidSearchAccountLevelTable from '../../components/analytics_ga4/tables/paidSearchAccountLevelTable'

const filterManager = analyticsBuilders.getFilterManager({
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  pageNumber: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  sortType: { type: Number, default: analyticsConstants.paidSearchSortTypes.dateDesc }
})

export default {
  mixins: [baseReportPage],
  name: 'paid-search',
  metaInfo: {
    title: 'Analytics - Paid Search Report'
  },
  components: {
    PaidSearchAccountLevelTable,
    PaidSearchSummary
  },
  created () {
    this.$store.commit('analyticsGa4/setAnalyticsName', 'Paid Search Report')
    this.$store.commit('analyticsGa4/setBlackThemeOn', true)
  },
  data () {
    return {
      page: {
        filter: filterManager.defaultValue
      },
      summary: {
        sessions: 0,
        sessionsDelta: null,
        totalLeads: 0,
        totalLeadsDelta: null,
        conversionRate: 0,
        conversionRateDelta: null,
        spend: 0,
        spendDelta: null,
        costPerLead: 0,
        costPerLeadDelta: null
      }
    }
  },
  computed: {
    filterManager () {
      return filterManager
    }
  },
  methods: {
    async updateStatistics () {
      try {
        await Promise.all([
          this.updateAccountLevelGraphAndSummary(),
          this.updateAccountLevelDetails()
        ])
      } catch (err) {
        this.$toaster.error('Failed to get data from server', { timeout: 8000 })
        this.$logger.handleError(err, 'Can\'t update statistics', { filter: this.page.filter, cache: this.cache })
      }
    },
    async updateAccountLevelGraphAndSummary () {
      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getPaidSearchGraphAndSummary',
        {
          accountId: this.accountId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo
        }
      )

      this.summary = {
        ...this.summary,
        ...store.summary.data
      }
      this.bar.items = store.graph.data.items
    },
    async updateAccountLevelDetails () {
      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getPaidSearchDetailsPage',
        {
          accountId: this.accountId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo,
          pageNumber: this.page.filter.pageNumber,
          pageSize: this.page.filter.pageSize,
          sortType: this.page.filter.sortType
        }
      )

      this.table.items = store.detailedData.data.items
      this.table.totalItems = store.detailedData.data.totalItems
    }
  }
}
</script>
