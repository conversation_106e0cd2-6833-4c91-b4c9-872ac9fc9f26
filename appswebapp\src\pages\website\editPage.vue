<template>
<ValidationObserver ref="validator">
  <div v-if="!isLoading">
    <div class="d-flex justify-content-between">
      <h4>Edit Page</h4>
      <b-button variant="primary" @click="saveChanges" :disabled="isDisabledBtn">Save Changes</b-button>
    </div>
    <b-tabs class="nav-tabs-top nav-responsive-sm" v-model="selectedTab" no-fade>
      <b-tab v-for="tab in getTabOptions" :key="tab.value" :title="tab.title">
      </b-tab>
      <div class="p-3">
        <page-type-filter @refreshData="onRefreshData" :pageEditModel="pageEditModel" v-if="selectedTab === 0"></page-type-filter>
        <seo-settings :customSeoSettings="customSeoSettings" @customSeoSettingsChanged="customSeoSettingsChanged" :seoSettings="pageEditModel.pageSettings.metaData" v-if="selectedTab === 1 && pageEditModel.pageSettings"></seo-settings>
      </div>
    </b-tabs>
    <b-button variant="primary" class="float-right mt-3" @click="saveChanges" :disabled="isDisabledBtn">Save Changes</b-button>
  </div>
  <div v-else-if="!isErrorOccurred">
    <loader class="mt-5" size="lg"/>
  </div>
  <div v-else class="d-flex justify-content-center">
    <h4 class="text-muted">Something went wrong! Please try again later</h4>
  </div>
</ValidationObserver>
</template>

<script>
import globals from '@/globals'
import pageTypeFilter from '@/components/website/editPage/pageTypeFilter'
import seoSettings from '@/components/website/editPage/seoSettings'
import loader from '@/components/_shared/loader'
import { pageNavigationTypes } from '@/shared/website/constants'

export default {
  props: {
    accountId: { type: Number, required: true },
    navId: { type: Number, required: true }
  },
  data () {
    return {
      filter: {
        tab: 0
      },
      pageEditModel: {
        pageSettings: null,
        navigationSettings: null
      },
      pageEditModelOriginal: {
        pageSettings: null,
        navigationSettings: null
      },
      customSeoSettings: false,
      isLoading: true,
      isErrorOccurred: false,
      isValid: true,
      isDisabledBtn: false
    }
  },
  components: {
    pageTypeFilter,
    seoSettings,
    loader
  },
  created () {
    this.populateData()
  },
  computed: {
    getTabOptions () {
      let res = [{value: 0, title: 'Page Settings'}]
      if (this.pageEditModel.navigationSettings && this.pageEditModel.navigationSettings.pageId && this.pageEditModel.navigationSettings.pageId > 0) {
        res.push({value: 1, title: 'SEO'})
      }
      return res
    },
    selectedTab: {
      get () {
        return this.filter.tab
      },
      set (index) {
        this.filter.tab = index
      }
    }
  },
  methods: {
    async populateData () {
      try {
        let res = await this.$store.dispatch('website/getPageNavigationSettings', { accountId: this.accountId, navId: this.navId })
        this.pageEditModel.navigationSettings = res.data

        if (res.data.pageId && res.data.pageId > 0) {
          let pageRes = await this.$store.dispatch('website/getPageSettings', { accountId: this.accountId, pageId: res.data.pageId })
          this.pageEditModel.pageSettings = pageRes.data
          this.customSeoSettings = pageRes.data.metaData !== null
          this.pageEditModel.pageSettings.metaData = pageRes.data.metaData || { title: '', description: '', keywords: '' }
        }
        this.pageEditModelOriginal = globals().getClonedValue(this.pageEditModel)
        this.isLoading = false
      } catch (ex) {
        this.$toaster.exception(ex, 'Something went wrong!')
        if (ex.response && ex.response.status !== 400) {
          this.$logger.handleError(ex, 'Cannot populate page edit model')
        } else {
          this.$router.push({name: 'website-page-builder', params: { accountId: this.accountId }})
        }
        this.isErrorOccurred = true
      }
    },
    onRefreshData () {
      this.isLoading = true
      this.populateData()
    },
    async saveChanges () {
      this.isDisabledBtn = true
      if (await this.validate()) {
        try {
          if (JSON.stringify(this.pageEditModelOriginal.navigationSettings) !== JSON.stringify(this.pageEditModel.navigationSettings)) {
            await this.$store.dispatch('website/updatePageNavigationSettings', { accountId: this.accountId, navId: this.navId, data: this.pageEditModel.navigationSettings })
          }
          if (this.pageEditModel.navigationSettings.pageId && this.pageEditModel.navigationSettings.pageId > 0) {
            let pageSettingsToUpdated = globals().getClonedValue(this.pageEditModel.pageSettings)
            if (!this.customSeoSettings) {
              pageSettingsToUpdated.metaData = null
            }
            if (JSON.stringify(this.pageEditModelOriginal.pageSettings) !== JSON.stringify(pageSettingsToUpdated)) {
              await this.$store.dispatch('website/updatePage', { accountId: this.accountId, pageId: pageSettingsToUpdated.id, data: pageSettingsToUpdated })
            }
          }
          this.$toaster.success('Changes Successfully Saved')
        } catch (ex) {
          this.$toaster.exception(ex, 'Something went wrong!')
          if (ex.response && ex.response.status !== 400) {
            this.$logger.handleError(ex, 'Cannot populate page edit model')
          } else {
            this.$router.push({name: 'website-page-builder', params: { accountId: this.accountId }})
          }
          this.isErrorOccurred = true
        }
      }
      this.populateData()
      this.isDisabledBtn = false
    },
    async validate () {
      if (!this.pageEditModel.navigationSettings.name.trim()) {
        this.$toaster.error('Nav Bar Label is Required')
        return false
      }

      if (this.pageEditModel.navigationSettings.siteNavigationType !== pageNavigationTypes.customLink.value &&
          this.pageEditModel.navigationSettings.siteNavigationType !== pageNavigationTypes.blog.value &&
          this.pageEditModel.navigationSettings.siteNavigationType !== pageNavigationTypes.newVehicle.value) {
        let isValidPageName = await this.pageNameValidate()
        if (!isValidPageName) {
          return false
        }
      }

      let isValid = await this.$refs.validator.validate()
      return isValid
    },
    async pageNameValidate () {
      if (!this.pageEditModel.pageSettings.pageName) {
        this.$toaster.error('Page Url is Required')
        return false
      }

      let pageName = this.pageEditModel.pageSettings.pageName.trim().replaceAll(' ', '-').replaceAll('&', '').replaceAll('!', '').toLowerCase()
      try {
        let res = await this.$store.dispatch('website/isPageNameAvailable', {accountId: this.accountId, pageId: this.pageEditModel.pageSettings.id, apiData: {pageName: pageName}})
        if (!res.data) {
          this.$toaster.error('Page Name Is Unavailable')
          return res.data
        }
        this.pageEditModel.pageSettings.pageName = pageName
      } catch (ex) {
        this.$logger.handleError(ex, 'Error occurred on api method isPageNameEnabled')
      }

      return true
    },
    customSeoSettingsChanged (value) {
      this.customSeoSettings = value
    }
  }
}
</script>
