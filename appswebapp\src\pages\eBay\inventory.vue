<template>
  <div class="position-relative">
    <h4>eBay Inventory</h4>
    <template v-if="isInventoryLoaded && !isException">
      <inventory-filters
        :filtersOptions="inventory.filtersOptions"
        :filters="inventory.filters"
        :totalItems="inventory.vehiclesTotal"
        :pageNumber.sync="inventory.filters.page"
        :pageSize.sync="inventory.filters.pageSize"
        :sortType.sync="inventory.filters.sort"
        @filtersChanged="onFiltersChanged"
      />

      <inventory-listing-table
        v-if="!isRequestProcessing"
        :tableItems="inventory.vehicles"
        :totalItems="inventory.vehiclesTotal"
        :pageNumber.sync="inventory.filters.page"
        :pageSize.sync="inventory.filters.pageSize"
        :sortType.sync="inventory.filters.sort"
        :isPostingAllowed="isPostingAllowed"
        :status="inventory.filters.status"
        :sitesInfo="sitesInfo"
      />
      <div v-else class="pt-4">
        <loader size="lg"/>
      </div>
    </template>
    <div v-else-if="!isInventoryLoaded && !isException">
      <loader size="lg"/>
    </div>
    <div v-else>
      <error-alert/>
    </div>
  </div>
</template>

<script>
import inventoryListingTable from './../../components/eBay/inventory/inventoryListing'
import inventoryFilters from './../../components/eBay/inventory/inventoryFilters'
import QueryStringHelper from './../../shared/common/queryStringHelper'
import defaultEBayInventoryFilters from './../../shared/ebay/inventoryFilters'
import globals from './../../globals'
import lodash from 'lodash'
import loader from '@/components/_shared/loader'

const queryStringHelper = new QueryStringHelper(defaultEBayInventoryFilters)

export default {
  components: {
    inventoryListingTable,
    inventoryFilters,
    loader
  },
  metaInfo: {
    title: 'eBay Inventory'
  },
  data: function () {
    return {
      sitesInfo: {},
      accountId: +this.$route.params.accountId,
      isPostingAllowed: false,
      isInventoryLoaded: false,
      isRequestProcessing: false,
      isException: false,
      inventory: {
        vehicles: [],
        vehiclesTotal: 0,
        filters: this.convertQueryToInventoryFilters(),
        filtersOptions: {}
      }
    }
  },
  mounted: async function () {
    await this.fetchEBayInventory()
    await this.populateSiteInfo()
  },
  methods: {
    convertQueryToInventoryFilters () {
      const queryFilters = queryStringHelper.parseQueryStringToObject(this.$router)

      return globals().getClonedValue(queryFilters)
    },
    async fetchEBayInventory () {
      this.$store.dispatch('eBay/getInventoryData', { accountId: this.accountId, filters: this.inventory.filters }).then(res => {
        this.inventory.vehicles = res.data.Vehicles
        this.inventory.filtersOptions = res.data.FiltersOptions
        this.inventory.vehiclesTotal = res.data.TotalItems
      }).catch(ex => {
        this.$toaster.error('Something went wrong! Please try again later')
        this.$logger.handleError(ex, 'Cannot fetch eBay inventory data', this.inventory.filters)
        this.isException = true
      }).finally(() => {
        this.isRequestProcessing = false
        this.isInventoryLoaded = true
      })
    },
    onFiltersChanged (filters) {
      lodash.assign(this.inventory.filters, filters)
    },
    async populateSiteInfo () {
      let siteIds = [this.accountId]
      this.$store.dispatch('website/getSitesBasicInfo', { data: { siteIds: siteIds } }).then(res => {
        this.sitesInfo = res.data.sites
      }).catch(ex => {
        this.$toaster.error('Something went wrong!')
        this.$logger.handleError(ex, 'Cannot populate sites basic info')
      })
    }
  },
  watch: {
    'inventory.filters': {
      deep: true,
      async handler () {
        queryStringHelper.rebuildParamsInQueryString(this.$router, this.inventory.filters)
        this.inventory.vehicles = []
        this.inventory.vehiclesTotal = 0
        this.isException = false
        this.isRequestProcessing = true
        await this.fetchEBayInventory()
      }
    }
  }
}
</script>
