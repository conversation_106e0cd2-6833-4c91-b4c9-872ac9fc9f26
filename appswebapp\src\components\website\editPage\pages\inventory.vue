<template>
  <div>
    <page-status-section :page-edit-model="pageEditModel"/>
    <text-html-content-section v-model="pageEditModel.pageSettings.customText"/>
    <inventory-filter-section :page-settings="pageEditModel.pageSettings"/>
  </div>
</template>

<script>
import pageStatusSection from '../sections/pageStatusSection'
import textHtmlContentSection from '../sections/textHtmlContentSection'
import inventoryFilterSection from '../sections/inventoryFilterSection'

export default {
  props: {
    pageEditModel: {type: Object, required: true}
  },
  components: {
    pageStatusSection,
    textHtmlContentSection,
    inventoryFilterSection
  }
}
</script>
