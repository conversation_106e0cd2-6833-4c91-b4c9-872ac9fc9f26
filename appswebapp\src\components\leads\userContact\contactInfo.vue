<template>
<ValidationObserver ref="validator">
  <div class="mb-3 mt-3 mx-3">
    <div class="border-bottom">
      <b-row class="mr-2">
        <b-col><h6 class="float-left">Contact Information</h6></b-col>
        <b-btn v-if="isViewMode" variant="secondary" size="sm" class="fixed-sizes mb-2" @click="setEditMode()"><font-awesome-icon icon="pencil-alt" size="sm" /> <span class="btn-title">Edit</span></b-btn>
        <b-btn v-else variant="primary" size="sm" class="fixed-sizes mb-2" @click="saveSettings()"><font-awesome-icon icon="cloud-upload-alt" /> <span class="btn-title">Save</span></b-btn>
      </b-row>
    </div>
    <div>
      <detail-row fixedPayloadWidth>
        <span slot="title">First Name:</span>
        <span slot="payload" v-if="isViewMode" v-html="model.firstName"></span>
        <b-form-input v-else v-model="model.firstName" type="text" slot="payload"/>
      </detail-row>
      <detail-row fixedPayloadWidth>
        <span slot="title">Last Name:</span>
        <span slot="payload" v-if="isViewMode" v-html="model.lastName"></span>
        <b-form-input v-else v-model="model.lastName" type="text" slot="payload"/>
      </detail-row>
      <detail-row fixedPayloadWidth>
        <span slot="title">Phone:</span>
        <phone-input v-model="model.phone" type="text" format="$1-$2-$3" slot="payload" :disabled='isViewMode'/>
      </detail-row>
      <ValidationProvider name="Email" rules="email" v-slot="{errors}">
      <detail-row fixedPayloadWidth :error="errors[0]">
        <span slot="title">Email:</span>
        <b-form-input slot="payload" name="email" v-model="model.email" type="email" :disabled='isViewMode'/>
      </detail-row>
      </ValidationProvider>
      <detail-row fixedPayloadWidth>
        <span slot="title">Address:</span>
        <b-form-input v-model="model.address" type="text" slot="payload" :disabled='isViewMode'/>
      </detail-row>
      <detail-row fixedPayloadWidth>
        <span slot="title">City, State, Postal Code:</span>
        <b-form-input v-model="addressExtraInfo" type="text" slot="payload" :disabled='isViewMode'/>
      </detail-row>
      <detail-row fixedPayloadWidth>
        <span slot="title">Preferred Contact Method:</span>
        <b-form-select v-model="model.preferredContactType" slot="payload" :options="preferredContactTypes" :disabled='isViewMode'/>
      </detail-row>
      <b-btn class="leads-contact-delete-btn" @click="onDelete">Delete Contact</b-btn>
    </div>
  </div>
</ValidationObserver>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import phoneInput from '@/components/_shared/phoneInput'
import { preferredContactTypes } from '@/shared/leads/common'

export default {
  name: 'leads-contact-info',
  props: {
    model: { type: Object, required: true }
  },
  data () {
    return {
      isViewMode: true,
      preferredContactTypes
    }
  },
  components: {
    'detail-row': detailRow,
    'phone-input': phoneInput
  },
  computed: {
    addressExtraInfo: {
      get () {
        let text = ''
        text = [text, this.model.city, this.model.state, this.model.zip].filter(v => v && v !== '').join(', ')

        return text
      },
      set (text) {
        let res = text.split(', |,', 3)
        this.model.city = res[0]
        this.model.state = res[1]
        this.model.zip = res[2]
      }
    }
  },
  methods: {
    saveSettings () {
      if (this.validate()) {
        if (this.model.email) {
          this.$refs.validator.validate().then(valid => {
            if (valid) {
              this.isViewMode = true
              this.$emit('updateUser', this.model)
            }
          })
        } else {
          this.isViewMode = true
          this.$emit('updateUser', this.model)
        }
      }
    },
    setEditMode () {
      this.isViewMode = false
    },
    onDelete () {
      this.$emit('deleteUser')
    },
    validate () {
      if (!this.model.phone && !this.model.email) {
        this.$toaster.error('One of those fields: "Phone", "Email" must not be empty', { timedOut: 10000 })
        return false
      }
      return true
    }
  }
}
</script>

<style>
.leads-contact-delete-btn {
  position: absolute;
  right: 1%;
  bottom: 28px;
}
@media(max-width: 1200px) {
  .leads-contact-delete-btn {
    position: relative;
    right: 0;
    bottom: 0;
  }
}
</style>
