using System;
using System.Threading.Tasks;
using EBizAutos.Apps.Authentication.CommonLib.Abstract.Repositories;
using EBizAutos.Apps.Authentication.CommonLib.Models;
using EBizAutos.CommonLib.RepositoryClasses;
using MongoDB.Driver;

namespace EBizAutos.Apps.Authentication.MongoDbRepository {
	public class MfaChallengeMongoDbRepository : BaseMongoDbRepository, IMfaChallengeRepository {
		#region Fields
		private readonly IMongoCollection<MfaChallengeModel> _collection;
		#endregion

		#region Constructor
		public MfaChallengeMongoDbRepository(string connectionString) : base(connectionString ?? throw new ArgumentNullException(nameof(connectionString))) {
			_collection = GetCollection<MfaChallengeModel>(InternalConstants.CollectionNames.ConstMfaChallengesCollection);
			EnsureIndexes();
		}
		#endregion

		#region Public Methods
		public async Task InsertAsync(MfaChallengeModel challenge) {
			await _collection.InsertOneAsync(challenge);
		}

		public async Task<MfaChallengeModel> GetAsync(string id) {
			return await _collection.Find(x => x.Id == id).SingleOrDefaultAsync();
		}

		public async Task UpdateAsync(MfaChallengeModel challenge) {
			await _collection.ReplaceOneAsync(x => x.Id == challenge.Id, challenge);
		}

		public async Task DeleteAsync(string id) {
			await _collection.DeleteOneAsync(x => x.Id == id);
		}
		#endregion

		#region Private Methods
		private void EnsureIndexes() {
			// TTL index on CreatedAtUtc - cleanup after 7 days from challenge creation
			var indexKeys = Builders<MfaChallengeModel>.IndexKeys.Ascending(x => x.CreatedAtUtc);
			var indexOptions = new CreateIndexOptions {
				ExpireAfter = TimeSpan.FromDays(7),
				Name = "CreatedAtUtc_TTL"
			};
			_collection.Indexes.CreateOne(new CreateIndexModel<MfaChallengeModel>(indexKeys, indexOptions));
		}
		#endregion
	}
}