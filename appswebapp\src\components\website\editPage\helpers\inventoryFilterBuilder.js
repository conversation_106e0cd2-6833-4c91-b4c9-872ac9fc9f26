import { inventoryTypeFilters } from '@/shared/website/constants'
import inventoryPageConstants from '@/shared/website/inventoryPageConstants'

class InventoryFilterBuilder {
  BuildInventoryFiltersModel (filters, inventoryFilters, accountRelations) {
    if (!filters || filters.length === 0) {
      return []
    }
    filters.map(x => {
      this.BuildInventoryFilterItem(x, inventoryFilters, accountRelations)
    })
  };
  BuildApiInventoryFiltersModel (inventoryFilters) {
    let filters = []
    let res = this.BuildApiConditionsFilters(inventoryFilters.conditions)
    filters = filters.concat(res)
    res = this.BuildApiMakeModelTrimsFilters(inventoryFilters.makes, inventoryFilters.models, inventoryFilters.trims, inventoryFilters.makeModelFilterType)
    filters = filters.concat(res)
    res = this.BuildApiRangeFilter(inventoryFilters.yearFrom, inventoryFilters.yearTo, inventoryTypeFilters.yearFrom, inventoryTypeFilters.yearTo)
    filters = filters.concat(res)
    res = this.BuildApiRangeFilter(inventoryFilters.mileageFrom, inventoryFilters.mileageTo, inventoryTypeFilters.milesFrom, inventoryTypeFilters.milesTo)
    filters = filters.concat(res)
    res = this.BuildApiRangeFilter(inventoryFilters.priceFrom, inventoryFilters.priceTo, inventoryTypeFilters.priceFrom, inventoryTypeFilters.priceTo)
    filters = filters.concat(res)
    res = this.BuildApiRangeFilter(inventoryFilters.mpgHighwayFrom, inventoryFilters.mpgHighwayTo, inventoryTypeFilters.highwayMilesPerGallonFrom, inventoryTypeFilters.highwayMilesPerGallonTo)
    filters = filters.concat(res)
    res = this.BuildApiRangeFilter(inventoryFilters.mpgCityFrom, inventoryFilters.mpgCityTo, inventoryTypeFilters.cityMilesPerGallonFrom, inventoryTypeFilters.cityMilesPerGallonTo)
    filters = filters.concat(res)
    res = this.BuildApiRangeFilter(inventoryFilters.daysInStockFrom, inventoryFilters.daysInStockTo, inventoryTypeFilters.daysInStockFrom, inventoryTypeFilters.daysInStockTo)
    filters = filters.concat(res)
    res = this.BuildApiCarfaxReportFilter(inventoryFilters.carfaxReport)
    filters = filters.concat(res)
    res = this.BuildApiSingleFilter(inventoryFilters.ebizKeywords, inventoryTypeFilters.promoText)
    filters = filters.concat(res)
    res = this.BuildApiIncludeOrExcludeFilter(inventoryFilters.featuredVehicle, inventoryPageConstants.featuredVehicleFilterTypes, inventoryTypeFilters.featured)
    filters = filters.concat(res)
    res = this.BuildApiIncludeOrExcludeFilter(inventoryFilters.promotionFlag, inventoryPageConstants.promotionalFlagFilterTypes, inventoryTypeFilters.specialList)
    filters = filters.concat(res)
    res = this.BuildApiIncludeOrExcludeFilter(inventoryFilters.ebayListing, inventoryPageConstants.ebayListingsTypes, inventoryTypeFilters.ebayList)
    filters = filters.concat(res)
    res = this.BuildApiMultiplyFilters(inventoryFilters.fuelTypes, inventoryTypeFilters.fuelType)
    filters = filters.concat(res)
    res = this.BuildApiMultiplyFilters(inventoryFilters.drivetrainTypes, inventoryTypeFilters.drivetrain, true)
    filters = filters.concat(res)
    res = this.BuildApiMultiplyFilters(inventoryFilters.locations, inventoryTypeFilters.location, true)
    filters = filters.concat(res)
    res = this.BuildApiMultiplyFilters(inventoryFilters.vehicleTypes, inventoryTypeFilters.vehicleType)
    filters = filters.concat(res)
    res = this.BuildApiMultiplyFilters(inventoryFilters.bodyStyles, inventoryTypeFilters.bodyStyle)
    filters = filters.concat(res)
    res = this.BuildApiMultiplyFilters(inventoryFilters.truckCabTypes, inventoryTypeFilters.cabStyle)
    filters = filters.concat(res)
    res = this.BuildApiMultiplyFilters(inventoryFilters.rvTypes, inventoryTypeFilters.rvClass)
    filters = filters.concat(res)
    res = this.BuildApiMultiplyFilters(inventoryFilters.transmissionTypes, inventoryTypeFilters.transmissionClass)
    filters = filters.concat(res)
    res = this.BuildApiMultiplyFilters(inventoryFilters.vehicleStatus, inventoryTypeFilters.vehicleStatus)
    filters = filters.concat(res)
    res = this.BuildApiStatusCodeFilter(inventoryFilters.statusCodeFilterType, inventoryFilters.statusCode)
    filters = filters.concat(res)

    return filters
  };
  BuildInventoryFilterItem (filterItem, inventoryFilters, accountRelations) {
    switch (filterItem.filterType) {
      case inventoryTypeFilters.vehicleStatus:
        let vehicleStatus = Object.values(inventoryPageConstants.vehicleStatusTypes).find(x => x.value === +filterItem.filterValue)
        if (vehicleStatus && !inventoryFilters.vehicleStatus.some(x => x.value === vehicleStatus.value)) {
          inventoryFilters.vehicleStatus.push(vehicleStatus)
        }
        return
      case inventoryTypeFilters.newUsedCarList:
        if (filterItem.filterValue === 'true') {
          inventoryFilters.conditions.push(inventoryPageConstants.conditionTypes.new)
        } else {
          inventoryFilters.conditions.push(inventoryPageConstants.conditionTypes.used)
        }
        return
      case inventoryTypeFilters.certifiedPreOwned:
        inventoryFilters.conditions.push(inventoryPageConstants.conditionTypes.cpo)
        return
      case inventoryTypeFilters.make:
        if (!filterItem.isStraightOperation) {
          inventoryFilters.makeModelFilterType = inventoryPageConstants.makeModelFilterTypes.notShowMake.value
        }
        inventoryFilters.makes.push(filterItem.filterValue)
        return
      case inventoryTypeFilters.model:
        if (!filterItem.isStraightOperation && inventoryFilters.makeModelFilterType === 1) {
          inventoryFilters.makeModelFilterType = inventoryPageConstants.makeModelFilterTypes.notShowModel.value
        }
        inventoryFilters.models.push(filterItem.filterValue)
        return
      case inventoryTypeFilters.ebayList:
        if (filterItem.filterValue === 'true') {
          inventoryFilters.ebayListing = inventoryPageConstants.ebayListingsTypes.include.value
        } else {
          inventoryFilters.ebayListing = inventoryPageConstants.ebayListingsTypes.exclude.value
        }
        return
      case inventoryTypeFilters.yearFrom:
        inventoryFilters.yearFrom = filterItem.filterValue
        return
      case inventoryTypeFilters.yearTo:
        inventoryFilters.yearTo = filterItem.filterValue
        return
      case inventoryTypeFilters.bodyStyle:
        let bodyStyle = Object.values(inventoryPageConstants.bodyStyleTypes).find(x => x.value === +filterItem.filterValue)
        if (bodyStyle) {
          inventoryFilters.bodyStyles.push(bodyStyle)
        }
        return
      case inventoryTypeFilters.priceFrom:
        inventoryFilters.priceFrom = filterItem.filterValue
        return
      case inventoryTypeFilters.priceTo:
        inventoryFilters.priceTo = filterItem.filterValue
        return
      case inventoryTypeFilters.milesFrom:
        inventoryFilters.mileageFrom = filterItem.filterValue
        return
      case inventoryTypeFilters.milesTo:
        inventoryFilters.mileageTo = filterItem.filterValue
        return
      case inventoryTypeFilters.vehicleType:
        let vehicleType = Object.values(inventoryPageConstants.vehicleTypeTypes).find(x => x.value === +filterItem.filterValue)
        if (vehicleType) {
          inventoryFilters.vehicleTypes.push(vehicleType)
        }
        return
      case inventoryTypeFilters.daysInStockFrom:
        inventoryFilters.daysInStockFrom = filterItem.filterValue
        return
      case inventoryTypeFilters.daysInStockTo:
        inventoryFilters.daysInStockTo = filterItem.filterValue
        return
      case inventoryTypeFilters.featured:
        if (filterItem.filterValue === 'true') {
          inventoryFilters.featuredVehicle = inventoryPageConstants.featuredVehicleFilterTypes.include.value
        } else {
          inventoryFilters.featuredVehicle = inventoryPageConstants.featuredVehicleFilterTypes.exclude.value
        }
        return
      case inventoryTypeFilters.historyCarfax:
        inventoryFilters.carfaxReport = inventoryPageConstants.carfaxReportFilterTypes.activeVehiclesCarfaxReport.value
        return
      case inventoryTypeFilters.historyCarfaxOneOwner:
        inventoryFilters.carfaxReport = inventoryPageConstants.carfaxReportFilterTypes.activeVehiclesOneOwnerCarfaxReport.value
        return
      case inventoryTypeFilters.fuelType:
        let fuelType = Object.values(inventoryPageConstants.fuelTypeTypes).find(x => x.value === +filterItem.filterValue)
        if (fuelType) {
          inventoryFilters.fuelTypes.push(fuelType)
        }
        return
      case inventoryTypeFilters.cityMilesPerGallonFrom:
        inventoryFilters.mpgCityFrom = filterItem.filterValue
        return
      case inventoryTypeFilters.cityMilesPerGallonTo:
        inventoryFilters.mpgCityTo = filterItem.filterValue
        return
      case inventoryTypeFilters.highwayMilesPerGallonFrom:
        inventoryFilters.mpgHighwayFrom = filterItem.filterValue
        return
      case inventoryTypeFilters.highwayMilesPerGallonTo:
        inventoryFilters.mpgHighwayTo = filterItem.filterValue
        return
      case inventoryTypeFilters.drivetrain:
        let drivetrainValues = (filterItem.filterValue || '').split(',') || []
        drivetrainValues.map(x => {
          let drivetrain = Object.values(inventoryPageConstants.drivetrainTypes).find(dr => dr.value === +x)
          if (drivetrain) {
            inventoryFilters.drivetrainTypes.push(drivetrain)
          }
        })
        return
      case inventoryTypeFilters.rvClass:
        let rvClass = Object.values(inventoryPageConstants.rvClassTypes).find(x => x.value === +filterItem.filterValue)
        if (rvClass) {
          inventoryFilters.rvTypes.push(rvClass)
        }
        return
      case inventoryTypeFilters.vehicleTrim:
        inventoryFilters.trims.push(filterItem.filterValue)
        return
      case inventoryTypeFilters.dealerGroupCPO:
        inventoryFilters.conditions.push(inventoryPageConstants.conditionTypes.dealerGroupCpo)
        return
      case inventoryTypeFilters.cabStyle:
        let cabStyle = Object.values(inventoryPageConstants.cabStyleTypes).find(x => x.value === +filterItem.filterValue)
        if (cabStyle) {
          inventoryFilters.truckCabTypes.push(cabStyle)
        }
        return
      case inventoryTypeFilters.transmissionClass:
        let transmissionClass = Object.values(inventoryPageConstants.transmissionTypeTypes).find(x => x.value === +filterItem.filterValue)
        if (transmissionClass) {
          inventoryFilters.transmissionTypes.push(transmissionClass)
        }
        return
      case inventoryTypeFilters.dmsStatus:
        if (filterItem.isStraightOperation) {
          inventoryFilters.statusCodeFilterType = inventoryPageConstants.statusCodeFilterTypes.displayOnlyStatusCode.value
        } else {
          inventoryFilters.statusCodeFilterType = inventoryPageConstants.statusCodeFilterTypes.excludeStatusCode.value
        }
        inventoryFilters.statusCode = filterItem.filterValue
        return
      case inventoryTypeFilters.promoText:
        inventoryFilters.ebizKeywords = filterItem.filterValue
        return
      case inventoryTypeFilters.specialList:
        if (filterItem.filterValue === 'true') {
          inventoryFilters.promotionFlag = inventoryPageConstants.promotionalFlagFilterTypes.include.value
        } else {
          inventoryFilters.promotionFlag = inventoryPageConstants.promotionalFlagFilterTypes.exclude.value
        }
        return
      case inventoryTypeFilters.cpoDealer:
        inventoryFilters.conditions.push(inventoryPageConstants.conditionTypes.dealerCpo)
        return
      case inventoryTypeFilters.location:
        let locations = (filterItem.filterValue || '').split(',') || []
        locations.map(x => {
          let res = accountRelations.find(rel => rel.accountId === +x)
          if (res) {
            inventoryFilters.locations.push({
              value: +x,
              text: `${x} - ${res.accountName}`
            })
          }
        })
    }
  };
  BuildApiConditionsFilters (conditions) {
    if (!conditions || conditions.length === 0) {
      return []
    }
    let res = []
    conditions.map(x => {
      switch (x.value) {
        case inventoryPageConstants.conditionTypes.new.value:
          res.push({
            filterType: inventoryTypeFilters.newUsedCarList,
            isStraightOperation: true,
            filterValue: 'true'
          })
          break
        case inventoryPageConstants.conditionTypes.used.value:
          res.push({
            filterType: inventoryTypeFilters.newUsedCarList,
            isStraightOperation: true,
            filterValue: 'false'
          })
          break
        case inventoryPageConstants.conditionTypes.cpo.value:
          res.push({
            filterType: inventoryTypeFilters.certifiedPreOwned,
            isStraightOperation: true,
            filterValue: 'true'
          })
          break
        case inventoryPageConstants.conditionTypes.dealerCpo.value:
          res.push({
            filterType: inventoryTypeFilters.cpoDealer,
            isStraightOperation: true,
            filterValue: 'true'
          })
          break
        case inventoryPageConstants.conditionTypes.dealerGroupCpo.value:
          res.push({
            filterType: inventoryTypeFilters.dealerGroupCPO,
            isStraightOperation: true,
            filterValue: 'true'
          })
          break
      }
    })
    return res
  };
  BuildApiMakeModelTrimsFilters (makes, models, trims, makeModelFilterType) {
    let res = []
    let makeStraitOperation = true
    let modelStraitOperation = true
    let trimStraitOperation = true
    if (makeModelFilterType === inventoryPageConstants.makeModelFilterTypes.notShowMake.value) {
      makeStraitOperation = false
      modelStraitOperation = false
      trimStraitOperation = false
    } else if (makeModelFilterType === inventoryPageConstants.makeModelFilterTypes.notShowModel.value) {
      modelStraitOperation = false
      trimStraitOperation = false
    }
    if (makes && makes.length > 0) {
      res.push({
        filterType: inventoryTypeFilters.make,
        isStraightOperation: makeStraitOperation,
        filterValue: makes[0]
      })
    }
    if (models && models.length > 0) {
      res = res.concat(models.map(x => {
        return {
          filterType: inventoryTypeFilters.model,
          isStraightOperation: modelStraitOperation,
          filterValue: x
        }
      }))
    }
    if (trims && trims.length > 0) {
      res = res.concat(trims.map(x => {
        return {
          filterType: inventoryTypeFilters.vehicleTrim,
          isStraightOperation: trimStraitOperation,
          filterValue: x
        }
      }))
    }

    return res
  };
  BuildApiRangeFilter (from, to, fromType, toType) {
    let res = []
    if (from) {
      res.push({
        filterType: fromType,
        isStraightOperation: true,
        filterValue: from
      })
    }
    if (to) {
      res.push({
        filterType: toType,
        isStraightOperation: true,
        filterValue: to
      })
    }

    return res
  };
  BuildApiCarfaxReportFilter (carfaxReport) {
    let res = []
    switch (carfaxReport) {
      case inventoryPageConstants.carfaxReportFilterTypes.activeVehiclesCarfaxReport.value:
        res.push({
          filterType: inventoryTypeFilters.historyCarfax,
          isStraightOperation: true,
          filterValue: true
        })
        break
      case inventoryPageConstants.carfaxReportFilterTypes.activeVehiclesOneOwnerCarfaxReport.value:
        res.push({
          filterType: inventoryTypeFilters.historyCarfaxOneOwner,
          isStraightOperation: true,
          filterValue: true
        })
        break
    }

    return res
  };
  BuildApiStatusCodeFilter (statusCodeFilterType, statusCode) {
    let res = []
    switch (statusCodeFilterType) {
      case inventoryPageConstants.statusCodeFilterTypes.displayOnlyStatusCode.value:
        res.push({
          filterType: inventoryTypeFilters.dmsStatus,
          isStraightOperation: true,
          filterValue: statusCode
        })
        break
      case inventoryPageConstants.statusCodeFilterTypes.excludeStatusCode.value:
        res.push({
          filterType: inventoryTypeFilters.dmsStatus,
          isStraightOperation: false,
          filterValue: statusCode
        })
        break
    }

    return res
  };
  BuildApiMultiplyFilters (range, type, isSplitByComma) {
    if (!range || range.length === 0) {
      return []
    }
    let res = []
    if (isSplitByComma) {
      res.push({
        filterType: type,
        isStraightOperation: true,
        filterValue: range.map(x => x.value).join()
      })
    } else {
      range.map(x => {
        res.push({
          filterType: type,
          isStraightOperation: true,
          filterValue: x.value
        })
      })
    }

    return res
  };
  BuildApiSingleFilter (value, type) {
    if (!value) {
      return []
    } else {
      return [
        {
          filterType: type,
          isStraightOperation: true,
          filterValue: value
        }
      ]
    }
  };
  BuildApiIncludeOrExcludeFilter (value, option, type) {
    let res = []
    switch (value) {
      case option.include.value:
        res.push({
          filterType: type,
          isStraightOperation: true,
          filterValue: 'true'
        })
        break
      case option.exclude.value:
        res.push({
          filterType: type,
          isStraightOperation: true,
          filterValue: 'false'
        })
        break
    }
    return res
  }
}

export default new InventoryFilterBuilder()
