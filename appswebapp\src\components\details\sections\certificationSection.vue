<template>
  <details-section title="Certification" v-model="mode" @cancel="onCancel" :visible="isDisplayed" @visibilityChange='onVisibilityChange' v-if="isCertificationExist">
    <div class="view" v-if="mode === 'view'">

      <auto-detail-row title="Certification Type" :text="getDealerCertificationObject.text"/>

      <detail-row>
        <span slot="title">Dealer Certified?</span>
        <span slot="payload">{{isDealerCertified}}</span>
      </detail-row>

    </div>
    <div class="edit" v-else-if="mode === 'edit'">

      <auto-detail-row title="Certification Type" :value="getDealerCertificationObject.value" :options="getCertificationOptions" @input="onCertificationChange"/>

      <detail-row>
        <span slot="title">Dealer Certified?</span>
        <b-form-checkbox slot="payload" v-model="vehicle.certificationInformation.isDealerCpo">{{isDealerCertified}}</b-form-checkbox>
      </detail-row>
    </div>
  </details-section>
</template>

<script>
import { mapGetters } from 'vuex'
import detailRow from '../helpers/detailRow'
import detailsSection from '@/components/details/detailsSection'
import autoDetailRow from '../helpers/autoDetailRow'

export default {
  name: 'certification-section',
  data () {
    return {
      mode: 'view',
      isDisplayed: true
    }
  },
  computed: {
    ...mapGetters('details', ['vehicle', 'metadata']),
    isCertificationExist () {
      return Array.isArray(this.metadata.certificationOptions) && this.metadata.certificationOptions.length > 0
    },
    isDealerCertified () {
      return this.vehicle.certificationInformation.isDealerCpo ? 'Yes' : 'No'
    },
    getCertificationOptions () {
      return this.metadata.certificationOptions.map(x => ({
        value: {
          dealerGroupCertificationId: x.dealerGroupCertificationId,
          isOEMCpo: x.isOEMCpo
        },
        text: x.label
      }))
    },
    getDealerCertificationObject () {
      let res = this.getCertificationOptions.find(x =>
        x.value.dealerGroupCertificationId === this.vehicle.certificationInformation.dealerGroupCpoId &&
        x.value.isOEMCpo === this.vehicle.certificationInformation.isOEMCpo
      )
      return res || { value: 0, text: 'Not Selected' }
    }
  },
  methods: {
    onVisibilityChange (val) {
      this.isDisplayed = val
    },
    onCertificationChange (newCer) {
      this.vehicle.certificationInformation.dealerGroupCpoId = newCer.dealerGroupCertificationId
      this.vehicle.certificationInformation.isOEMCpo = newCer.isOEMCpo
    },
    onCancel () {
      this.$forceUpdate()
    }
  },
  components: {
    'details-section': detailsSection,
    'detail-row': detailRow,
    'auto-detail-row': autoDetailRow
  }
}
</script>
