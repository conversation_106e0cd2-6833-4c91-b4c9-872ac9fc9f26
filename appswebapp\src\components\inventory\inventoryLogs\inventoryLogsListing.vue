<template>
  <inventory-mobile-log-listing v-if='logType === logTypes.mobile.value' :items='items' :totalItems='totalItems'/>
  <inventory-desktop-log-listing v-else-if='logType === logTypes.desktop.value' :items='items' :totalItems='totalItems'/>
  <inventory-settings-log-listing v-else-if='logType === logTypes.settings.value' :items='items' :totalItems='totalItems'/>
  <inventory-vehicle-post-processing-log-listing v-else-if='logType === logTypes.vehiclePostProcessing.value' :items='items' :totalItems='totalItems'/>
  <vehicle-history-reports-logs-listing v-else-if='logType === logTypes.vehicleHistoryReport.value' :items='items' :totalItems='totalItems' />
</template>

<script>
import inventoryDesktopLogListing from './inventoryDesktopLogListing'
import inventoryMobileLogListing from './inventoryMobileLogListing'
import inventorySettingsLogListing from './inventorySettingsLogListing'
import inventoryVehiclePostProcessingLogsListing from './inventoryVehiclePostProcessingLogsListing'
import vehicleHistoryReportLogsListing from './vehicleHistoryReports/historyReportsLogsListing.vue'
import { logTypes } from '@/shared/inventory/inventoryTypes'

export default {
  name: 'inventory-logs-listing',
  props: {
    logType: { type: Number, required: true },
    items: { type: Array, required: true },
    totalItems: { type: Number, required: true }
  },
  components: {
    'inventory-desktop-log-listing': inventoryDesktopLogListing,
    'inventory-mobile-log-listing': inventoryMobileLogListing,
    'inventory-settings-log-listing': inventorySettingsLogListing,
    'inventory-vehicle-post-processing-log-listing': inventoryVehiclePostProcessingLogsListing,
    'vehicle-history-reports-logs-listing': vehicleHistoryReportLogsListing
  },
  data () {
    return {
      logTypes
    }
  },
  methods: {
    onSortChanged (sortType) {
      this.$emit('onSortChanged', sortType)
    }
  }
}
</script>
