const objectKeysToLowerCase = function (origObj) {
  return Object.keys(origObj).reduce(function (newObj, key) {
    let val = origObj[key]
    newObj[key.toLowerCase()] = (typeof val === 'object' && val !== null) ? objectKeysToLowerCase(val) : val
    return newObj
  }, {})
}

class ObjectSchema {
  constructor (object) {
    this.prepareObject(object)
  };

  reset (object) {
    this.prepareObject(object)
  };

  getObject () {
    return this.objKeyValues
  };

  getObjectTypeSchema () {
    return this.objKeyTypes
  };

  prepareObject (obj) {
    this.objKeyValues = Object.create(null)
    this.objKeyTypes = Object.create(null)

    for (let key in obj) {
      this.objKeyValues[key] = obj[key].default
      this.objKeyTypes[key] = obj[key].type
    }
  }
}

export {
  objectKeysToLowerCase,
  ObjectSchema
}
