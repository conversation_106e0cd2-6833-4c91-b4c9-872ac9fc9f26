const errorsAndScheduledOptions = Object.freeze({
  errors: { key: 0, title: 'Errors' },
  scheduled: { key: 1, title: 'Scheduled' }
})

const postsOptions = Object.freeze({
  active: { key: 0, title: 'Active' },
  scheduled: { key: 1, title: 'Scheduled' },
  ended: { key: 2, title: 'Ended' }
})

const listingTypes = [{value: 1, text: 'All Listings'}, {value: 2, text: 'National Listings'}, {value: 3, text: 'Local Listings'}]

const accountListingSortTypes = Object.freeze({
  errorsAsc: 1,
  errorsDesc: 2,
  scheduledAsc: 3,
  scheduledDesc: 4,
  activeListingsAsc: 5,
  activeListingsDesc: 6
})

const inventorySortTypes = Object.freeze({
  undefined: 0,
  daysInStockAsc: 1,
  daysInStockDesc: 2,
  priceAsc: 3,
  priceDesc: 4,
  photosAsc: 5,
  photosDesc: 6,
  stockNumberAsc: 7,
  stockNumberDesc: 8
})

const reviseOptions = Object.freeze({
  allOptions: { key: 0 },
  modifyTitlePrivateCategory: { key: 1, title: 'Modify Title/Private/Category', buttonDescription: 'Modify Title/Private/Category' },
  addEBayUpgrades: { key: 2, title: 'Add Listing Upgrades', buttonDescription: 'Add eBay Upgrades' },
  addToItemDescription: { key: 3, title: 'Add to Item Description', buttonDescription: 'Add to Item Description' },
  addReservePrice: { key: 4, title: 'Add Reserve Price', buttonDescription: 'Add Reserve Price' },
  changeReservePrice: { key: 5, title: 'Change Reserve Price', buttonDescription: 'Change Reserve Price' },
  lowerReservePrice: { key: 6, title: 'Lower Reserve Price', buttonDescription: 'Lower Reserve Price' },
  removeReservePrice: { key: 7, title: 'Remove Reserve Price', buttonDescription: 'Remover Reserve Price' },
  addBinPrice: { key: 8, title: 'Add BIN Price', buttonDescription: 'Add BIN Price' },
  lowerBinPrice: { key: 9, title: 'Lower BIN Price', buttonDescription: 'Lower BIN Price' },
  raiseBinPrice: { key: 10, title: 'Raise BIN Price', buttonDescription: 'Raise BIN Price' },
  removeBinPrice: { key: 11, title: 'Remove BIN Price', buttonDescription: 'Remove BIN Price' },
  changeFixedPrice: { key: 12, title: 'Change Fixed Price', buttonDescription: 'Change Fixed Price' },
  changeStartingBid: { key: 13, title: 'Change Starting Bid', buttonDescription: 'Change Starting Bid' },
  changeAdvertisedPrice: { key: 14, title: 'Change Advertised Price', buttonDescription: 'Change Advertised Price' },
  modifyVehicleDescription: { key: 15, title: 'Modify the Vehicle Description', buttonDescription: 'Modify Vehicle Description' }
})

const listingFormatTypes = Object.freeze({
  auction: { value: 0, text: 'Auction' },
  fixedPriceListing: { value: 1, text: 'Fixed Price Listing' },
  local: { value: 3, text: 'eBay Local' }
})

const durationOptions = Object.freeze({
  auctionDurationOptions: {value: 0, options: [{value: 3, text: '3 Days'}, {value: 5, text: '5 Days'}, {value: 7, text: '7 Days'}, {value: 10, text: '10 Days ($18 eBay Fee)'}]},
  fixedPriceListingDurationOptions: {value: 1, options: [{value: 3, text: '3 Days'}, {value: 5, text: '5 Days'}, {value: 7, text: '7 Days'}, {value: 10, text: '10 Days ($18 eBay Fee)'}, {value: 30, text: '30 Days ($50 eBay Fee)'}]},
  localDurationOptions: {value: 3, options: [{value: 30, text: '30 Days'}, {value: 250, text: 'Good Till Canceled'}]}
})

const eBayInventoryStatusOptions = [
  { value: 0, text: 'All' },
  { value: 1, text: 'Posted' },
  { value: 2, text: 'Never Posted' },
  { value: 3, text: 'Scheduled' }
]

const eBayInventoryVehicleStatus = {
  active: { value: 1, text: 'Active' },
  scheduled: { value: 2, text: 'Scheduled' },
  ended: { value: 3, text: 'Ended' },
  neverPosted: { value: 4, text: 'Never Posted' }
}

const ebayCategoriesByVehicleType = Object.freeze({
  Default: { value: 0, ebayCategoryId: 6038, ebayCategoryName: 'Other Vehicles & Trailers' },
  Atv: { value: 1, ebayCategoryId: 6723, ebayCategoryName: 'ATVs' },
  Boat: { value: 2, ebayCategoryId: 26429, ebayCategoryName: 'Boats' },
  Bus: { value: 3, ebayCategoryId: 6728, ebayCategoryName: 'Buses' },
  Motorcycle: { value: 4, ebayCategoryId: 6024, ebayCategoryName: '' },
  Passenger: { value: 5, ebayCategoryId: 6001, ebayCategoryName: 'Cars & Trucks' },
  Plane: { value: 6, ebayCategoryId: 63676, ebayCategoryName: 'Aircraft' },
  Rv: { value: 7, ebayCategoryId: 50054, ebayCategoryName: 'RVs & Campers' },
  Scooter: { value: 8, ebayCategoryId: 6720, ebayCategoryName: 'Scooters & Mopeds' },
  Snow: { value: 9, ebayCategoryId: 42595, ebayCategoryName: 'Snowmobiles' },
  Truck: { value: 10, ebayCategoryId: 6038, ebayCategoryName: 'Other Vehicles & Trailers' },
  Misc: { value: 11, ebayCategoryId: 6737, ebayCategoryName: 'Other' }
})

const eBayInfoUrls = Object.freeze({
  endEarlyInfo: 'https://pages.ebay.com/help/sell/end_early.html',
  feesCreditsInvoices: 'https://www.ebay.com/help/selling/fees-credits-invoices/motors-fees?id=4127',
  userFeedBackUrl: (userId) => `https://feedback.${process.env.NODE_ENV !== 'production' ? 'sandbox.' : ''}ebay.com/ws/eBayISAPI.dll?ViewFeedback&userid=${userId}`,
  ebayItemUrl: (iid) => `https://www.${process.env.NODE_ENV !== 'production' ? 'sandbox.' : ''}ebay.com/itm/${iid}`,
  ebayViewBids: (iid) => `https://www.${process.env.NODE_ENV !== 'production' ? 'sandbox.' : ''}ebay.com/bfl/viewbids/${iid}`
})

const eBayAuctionEndReasonOptions = [
  {value: 2, text: 'This item is no longer available for sale'},
  {value: 4, text: 'This listing contained an error'}
]

const hourOptions = ['12', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11']

const postSortTypes = Object.freeze({
  eBayIdAsc: 1,
  eBayIdDesc: 2,
  highBidAsc: 3,
  highBidDesc: 4,
  totalBidsAsc: 5,
  totalBidsDesc: 6,
  totalViewsAsc: 7,
  totalViewsDesc: 8,
  totalWatchersAsc: 9,
  totalWatchersDesc: 10,
  totalLeadsAsc: 11,
  totalLeadsDesc: 12,
  timeLeftAsc: 13,
  timeLeftDesc: 14,
  pricingAsc: 15,
  pricingDesc: 16,
  startTimeAsc: 17,
  startTimeDesc: 18
})

const eBayVehicleSynchronizationStatuses = Object.freeze({
  completed: 'Completed',
  failed: 'Failed',
  expired: 'Expired'
})

export default {
  errorsAndScheduledOptions,
  postsOptions,
  listingTypes,
  accountListingSortTypes,
  inventorySortTypes,
  reviseOptions,
  listingFormatTypes,
  durationOptions,
  hourOptions,
  eBayInventoryStatusOptions,
  ebayCategoriesByVehicleType,
  eBayInfoUrls,
  postSortTypes,
  eBayInventoryVehicleStatus,
  eBayAuctionEndReasonOptions,
  eBayVehicleSynchronizationStatuses
}
