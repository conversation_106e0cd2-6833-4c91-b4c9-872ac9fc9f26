<template>
  <div>
    <website-engagement-summary
      :barItems="bar.items"
      :summary="summary"
      :barTimeFormat="barTimeFormat"
      :deviceFilter="page.deviceFilter"
      @deviceFilterChanged="onDeviceFilterChanged"
    ></website-engagement-summary>

    <website-engagement-account-level-table
      :tableItems="table.items"
      :totalItems="table.totalItems"
      :pageNumber="page.filter.pageNumber"
      :pageSize="page.filter.pageSize"
      :sortType="page.filter.sortType"
      @pageNumberChanged="onPageNumberChanged"
      @pageSizeChanged="onPageSizeChanged"
      @sortTypeChanged="onSortTypeChanged"
    ></website-engagement-account-level-table>
  </div>
</template>

<style src="@/vendor/styles/pages/analytics.scss" lang="scss"></style>

<script>
import analyticsConstants from './../../shared/analytics/constants'
import analyticsBuilders from './../../shared/analytics/builders'
import analyticsHelper from './helpers.js'
import baseReportPage from './baseReportPage.js'

import WebsiteEngagementSummary from '../../components/analytics_ga4/summaries/websiteEngagementSummary'
import WebsiteEngagementAccountLevelTable from '../../components/analytics_ga4/tables/websiteEngagementAccountLevelTable'

const filterManager = analyticsBuilders.getFilterManager({
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  pageNumber: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  sortType: { type: Number, default: analyticsConstants.websiteEngagementSortTypes.dateDesc }
})

export default {
  mixins: [baseReportPage],
  name: 'website-engagement',
  metaInfo: {
    title: 'Analytics - Website Engagement'
  },
  components: {
    WebsiteEngagementAccountLevelTable,
    WebsiteEngagementSummary
  },
  created () {
    this.$store.commit('analyticsGa4/setAnalyticsName', 'Website Engagement')
    this.$store.commit('analyticsGa4/setBlackThemeOn', true)
  },
  data () {
    return {
      page: {
        filter: filterManager.defaultValue
      },
      summary: {
        pageViewsPerSession: 0,
        pageViewsPerSessionDelta: null,
        avgSessionDuration: 0,
        avgSessionDurationDelta: null,
        bounceRate: 0,
        bounceRateDelta: null
      }
    }
  },
  computed: {
    filterManager () {
      return filterManager
    }
  },
  methods: {
    async updateStatistics () {
      try {
        await Promise.all([
          this.updateAccountLevelGraphAndSummary(),
          this.updateAccountLevelDetails()
        ])
      } catch (err) {
        this.$toaster.error('Failed to get data from server', { timeout: 8000 })
        this.$logger.handleError(err, 'Can\'t update statistics', { filter: this.page.filter, cache: this.cache })
      }
    },
    async updateAccountLevelGraphAndSummary () {
      let storePath = 'analyticsGa4/'
      switch (this.page.deviceFilter) {
        case 'mobile':
          storePath += 'getMobileEngagementGraphAndSummary'
          break
        case 'desktop':
          storePath += 'getDesktopEngagementGraphAndSummary'
          break
        default:
          storePath += 'getWebsiteEngagementGraphAndSummary'
          break
      }

      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        storePath,
        {
          accountId: this.accountId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo
        }
      )

      this.summary = {
        ...this.summary,
        ...store.summary.data
      }
      this.bar.items = store.graph.data.items
    },
    async updateAccountLevelDetails () {
      let storePath = ''
      switch (this.page.deviceFilter) {
        case 'mobile':
          storePath = 'analyticsGa4/getMobileEngagementDetailsPage'
          break
        case 'desktop':
          storePath = 'analyticsGa4/getDesktopEngagementDetailsPage'
          break
        default:
          storePath = 'analyticsGa4/getWebsiteEngagementDetailsPage'
          break
      }

      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        storePath,
        {
          accountId: this.accountId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo,
          pageNumber: this.page.filter.pageNumber,
          pageSize: this.page.filter.pageSize,
          sortType: this.page.filter.sortType
        }
      )

      this.table.items = store.detailedData.data.items
      this.table.totalItems = store.detailedData.data.totalItems
    }
  }
}
</script>
