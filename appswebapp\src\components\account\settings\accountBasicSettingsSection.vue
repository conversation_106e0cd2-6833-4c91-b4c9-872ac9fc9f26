<template>
  <div class="mb-5">
    <div class="border-bottom">
      <b-row class="mr-1 ml-1 title-section">
        <h5 class="font-weight-bold">Account Basic Settings</h5>
      </b-row>
    </div>

    <div class="mt-3">
      <detail-row :fixed-payload-width="true">
        <span slot="title">Account ID:</span>
        <span slot="payload">{{ settings.accountId }}</span>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Account Name:</span>
        <span slot="payload">{{ settings.dealershipName }}</span>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Account Status:</span>
        <div slot="payload" class="d-flex align-items-center">
          <span class="mr-3">{{ accountStatusText }}</span>
          <account-status-action-buttons :account="settings" />
        </div>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Website Status:</span>
        <span slot="payload">{{ websiteStatusText }}</span>
      </detail-row>
    </div>
  </div>
</template>

<script>
import accountStatuses from '@/shared/accounts/accountStatuses'
import detailRow from '@/components/details/helpers/detailRow'
import AccountStatusActionButtons from './accountStatusActionButtons.vue'

export default {
  name: 'AccountBasicSettingsSection',
  components: {
    detailRow,
    AccountStatusActionButtons
  },
  props: {
    settings: {
      type: Object,
      required: true
    }
  },
  computed: {
    accountStatusOptions () {
      return [
        { value: accountStatuses.Active.Value, text: accountStatuses.Active.Label },
        { value: accountStatuses.Pending.Value, text: accountStatuses.Pending.Label },
        { value: accountStatuses.OnHold.Value, text: accountStatuses.OnHold.Label },
        { value: accountStatuses.Closed.Value, text: accountStatuses.Closed.Label }
      ]
    },
    websiteStatusOptions () {
      return [
        { value: 1, text: 'Process Disabling' },
        { value: 2, text: 'Disabled' },
        { value: 3, text: 'Process Enabling' },
        { value: 4, text: 'Enabled' }
      ]
    },
    accountStatusText () {
      const status = this.accountStatusOptions.find(s => s.value === this.settings.accountStatus)
      return status ? status.text : 'Unknown'
    },
    websiteStatusText () {
      const status = this.websiteStatusOptions.find(s => s.value === this.settings.accountWebsiteStatus)
      return status ? status.text : 'Unknown'
    }
  }
}
</script>
