<template>
  <div>
    <div class="border-bottom">
      <b-row>
        <b-col class="m-0"><h6>Inventory Type Filters</h6></b-col>
      </b-row>
    </div>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Conditions:</span>
      <multiselect
        :closeOnSelect="false"
        selectLabel=""
        deselectLabel=""
        selectedLabel=""
        size="sm"
        multiple
        placeholder="Selected All Conditions" slot="payload" trackBy="value" v-model="inventoryFilters.conditions" :options="getConditionOptions" label='text'></multiselect>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Year Range:</span>
      <div class="d-flex flex-row" slot="payload">
        <span class="d-flex align-items-center mr-2">From</span>
        <b-form-select v-model="inventoryFilters.yearFrom" :options="getYearFromOptions"></b-form-select>
        <span class="d-flex align-items-center mx-2">to</span>
        <b-form-select v-model="inventoryFilters.yearTo" :options="getYearToOptions"></b-form-select>
      </div>
    </detail-row>
    <detail-row titlePosition="start" :extra-large-payload-width="true">
      <span slot="title">Make/Model:</span>
      <div slot="payload" class="w-100">
        <b-row>
          <b-col xl="6" lg="6" md="10" sm="12">
            <b-form-select v-model="inventoryFilters.makeModelFilterType" :options="getMakeModelFilterOptions"></b-form-select>
          </b-col>
        </b-row>
        <b-row v-for="(make, index) in inventoryFilters.makes" :key="index">
          <b-col xl="3" lg="3" md="3" sm="12 mt-1"><b-form-select :value="make" :options="[make]" disabled></b-form-select></b-col>
          <b-col xl="3" lg="3" md="3" sm="12 mt-1"><b-form-select :value="inventoryFilters.models[index] || 'Select Model'" :options="[inventoryFilters.models[index] || 'Select Model']" disabled></b-form-select></b-col>
          <b-col xl="3" lg="3" md="3" sm="12 mt-1"><b-form-select :value="inventoryFilters.trims[index] || 'Select Trim'" :options="[inventoryFilters.trims[index] || 'Select Trim']" disabled></b-form-select></b-col>
          <b-col xl="3" lg="3" md="3" sm="12 mt-1" class="d-flex align-items-center"><b-btn @click="onRemoveMakeModelTrim(index)" size="sm">Remove</b-btn></b-col>
        </b-row>
        <b-row class="mt-2">
          <b-col xl="3" lg="3" md="3" sm="12 mt-1"><b-form-select v-model="selectedMake" @input="onChangeMake" :options="makeOptions" :disabled="inventoryFilters.makes.length > 0"></b-form-select></b-col>
          <b-col xl="3" lg="3" md="3" sm="12 mt-1"><b-form-select v-model="selectedModel" @input="onChangeModel" :options="modelOptions"></b-form-select></b-col>
          <b-col v-if="inventoryFilters.makeModelFilterType !== makeModelFilterTypes.notShowModel.value" xl="3" lg="3" md="3" sm="12 mt-1"><b-form-select v-model="selectedTrim" :options="trimOptions" :disabled="inventoryFilters.makeModelFilterType === makeModelFilterTypes.notShowMake.value"></b-form-select></b-col>
          <b-col xl="3" lg="3" md="3" sm="12 mt-1" class="d-flex align-items-center"><b-btn @click="onAddMakeModelTrim()" size="sm" :disabled="!selectedMake">Add</b-btn></b-col>
        </b-row>
      </div>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Fuel Type:</span>
      <multiselect
        :closeOnSelect="false"
        selectLabel=""
        deselectLabel=""
        selectedLabel=""
        size="sm"
        placeholder="Selected All Fuel Types" slot="payload" multiple v-model="inventoryFilters.fuelTypes" :options="getFuelTypeOptions" trackBy="value" label='text'></multiselect>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Drivetrain:</span>
      <multiselect
        :closeOnSelect="false"
        selectLabel=""
        deselectLabel=""
        selectedLabel=""
        size="sm"
        slot="payload" placeholder="Selected All Drivetrain" multiple v-model="inventoryFilters.drivetrainTypes" :options="getDrivetrainOptions" trackBy="value" label='text'></multiselect>=>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Vehicle Type:</span>
      <multiselect
        :closeOnSelect="false"
        selectLabel=""
        deselectLabel=""
        selectedLabel=""
        size="sm"
        slot="payload" placeholder="Selected All Vehicle Types" multiple v-model="inventoryFilters.vehicleTypes" :options="getVehicleTypeOptions" trackBy="value" label='text'></multiselect>=>
    </detail-row>
    <detail-row title-position="start" :fixed-payload-width="true">
      <span slot="title">Body Style:</span>
      <b-form-group
        slot="payload"
        class="w-100"
        description="Applies only to the Passenger Vehicle Type"
      >
        <multiselect
          :closeOnSelect="false"
          selectLabel=""
          deselectLabel=""
          selectedLabel=""
          size="sm" multiple placeholder="Selected All Body Styles" v-model="inventoryFilters.bodyStyles"  trackBy="value" :options="getBodyStyleOptions" label='text'></multiselect>
      </b-form-group>
    </detail-row>
    <detail-row title-position="start" v-if="isEnabledSelectTruckCabTypes" :fixed-payload-width="true">
      <span slot="title">Truck Cab Type:</span>
      <b-form-group
        slot="payload"
        class="w-100"
        description="Applies only to the Truck Body Type"
      >
        <multiselect
          :closeOnSelect="false"
          selectLabel=""
          deselectLabel=""
          selectedLabel=""
          size="sm"
          multiple
          placeholder="Selected All Cab Types" v-model="inventoryFilters.truckCabTypes" :options="getTruckCabTypeOptions" trackBy="value" label='text'></multiselect>
      </b-form-group>
    </detail-row>
    <detail-row title-position="start" v-if="isEnabledSelectRvTypes" :fixed-payload-width="true">
      <span slot="title">RV Type:</span>
      <b-form-group
        slot="payload"
        class="w-100"
        description="Applies only to the RV Vehicle Type"
      >
        <multiselect
          :closeOnSelect="false"
          selectLabel=""
          deselectLabel=""
          selectedLabel=""
          size="sm"
          placeholder="Selected All RV Types"
          multiple v-model="inventoryFilters.rvTypes" :options="getRvTypeOptions" trackBy="value" label='text'></multiselect>
      </b-form-group>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Mileage:</span>
      <div class="d-flex flex-row" slot="payload">
        <span class="d-flex align-items-center mr-2">From</span>
        <b-form-input type="number" min="0" @blur.native="onMileageFromBlur" v-model="inventoryFilters.mileageFrom"></b-form-input>
        <span class="d-flex align-items-center mx-2">to</span>
        <b-form-input type="number" min="0" @blur.native="onMileageToBlur" v-model="inventoryFilters.mileageTo"></b-form-input>
      </div>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">MPG City:</span>
      <div class="d-flex flex-row" slot="payload">
        <span class="d-flex align-items-center mr-2">From</span>
        <b-form-input type="number" min="0" @blur.native="onMpgCityFromBlur" v-model="inventoryFilters.mpgCityFrom"></b-form-input>
        <span class="d-flex align-items-center mx-2">to</span>
        <b-form-input type="number" min="0" @blur.native="onMpgCityToBlur" v-model="inventoryFilters.mpgCityTo"></b-form-input>
        <span class="d-flex align-items-center ml-2">MPG.</span>
      </div>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">MPG Highway:</span>
      <div class="d-flex flex-row" slot="payload">
        <span class="d-flex align-items-center mr-2">From</span>
        <b-form-input type="number" min="0" @blur.native="onMpgHighwayFromBlur" v-model="inventoryFilters.mpgHighwayFrom"></b-form-input>
        <span class="d-flex align-items-center mx-2">to</span>
        <b-form-input type="number" min="0" @blur.native="onMpgHighwayToBlur" v-model="inventoryFilters.mpgHighwayTo"></b-form-input>
        <span class="d-flex align-items-center ml-2">MPG.</span>
      </div>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Transmission:</span>
        <multiselect
          :closeOnSelect="false"
          selectLabel=""
          deselectLabel=""
          selectedLabel=""
          size="sm"
          slot="payload"
          placeholder="Selected All Transmission Types"
          multiple
          v-model="inventoryFilters.transmissionTypes"
          :options="getTransmissionOptions"
          trackBy="value" label='text'></multiselect>
    </detail-row>
    <div class="mt-3 border-bottom">
      <b-row>
        <b-col class="m-0"><h6>Other Filters</h6></b-col>
      </b-row>
    </div>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Price Range:</span>
      <div class="d-flex flex-row" slot="payload">
        <span class="d-flex align-items-center mr-2">From</span>
        <b-form-input type="number" min="0" @blur.native="onPriceFromBlur" v-model="inventoryFilters.priceFrom"></b-form-input>
        <span class="d-flex align-items-center mx-2">to</span>
        <b-form-input type="number" min="0" @blur.native="onPriceToBlur" v-model="inventoryFilters.priceTo"></b-form-input>
      </div>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">CARFAX Reports:</span>
        <b-form-select slot="payload" v-model="inventoryFilters.carfaxReport" :options="getCarfaxReportOptions"></b-form-select>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Days in Stock:</span>
      <div class="d-flex flex-row" slot="payload">
        <span class="d-flex align-items-center mr-2">From</span>
        <b-form-input type="number" min="0" @blur.native="onDaysInStockFromBlur" v-model="inventoryFilters.daysInStockFrom"></b-form-input>
        <span class="d-flex align-items-center mx-2">to</span>
        <b-form-input type="number" min="0" @blur.native="onDaysInStockToBlur" v-model="inventoryFilters.daysInStockTo"></b-form-input>
        <span class="d-flex align-items-center ml-2">days.</span>
      </div>
    </detail-row>
    <detail-row titlePosition="start" :fixed-payload-width="true">
      <span slot="title">Status Code:</span>
      <div slot="payload" class="w-100">
        <b-form-select class="w-100" v-model="inventoryFilters.statusCodeFilterType" :options="getStatusCodeOptions"></b-form-select>
        <div class="w-100">
          <b-form-group
            label-cols-sm="4"
            label-cols-lg="5"
            label="Status Code:"
            label-class="text-muted"
            label-for="status-code"
          >
            <b-form-input v-model="inventoryFilters.statusCode" id="status-code"></b-form-input>
          </b-form-group>
        </div>
      </div>
    </detail-row>
    <detail-row titlePosition="start" :fixed-payload-width="true">
      <span slot="title">eBiz Keywords:</span>
      <b-form-group
        class="w-100"
        slot="payload"
        description="Leave blank to skip this filter."
      >
        <b-form-input v-model="inventoryFilters.ebizKeywords"></b-form-input>
      </b-form-group>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Featured Vehicles:</span>
      <b-form-select slot="payload" v-model="inventoryFilters.featuredVehicle" :options="getFeaturedVehicleOptions"></b-form-select>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Promotion Flag:</span>
      <b-form-select slot="payload" v-model="inventoryFilters.promotionFlag" :options="getPromotionFlagOptions"></b-form-select>
    </detail-row>
    <detail-row v-if="isLocationsFilterTypeEnabled" :fixed-payload-width="true">
      <span slot="title">Vehicle Location:</span>
      <multiselect
        :closeOnSelect="false"
        size="sm"
        slot="payload"
        placeholder=""
        multiple
        v-model="inventoryFilters.locations"
        :options="getLocationOptions"
        trackBy="value"
        label='text'
        selectLabel=""
        deselectLabel=""
        selectedLabel="">
      </multiselect>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Vehicle Status:</span>
      <multiselect
        :closeOnSelect="false"
        selectLabel=""
        deselectLabel=""
        selectedLabel=""
        size="sm"
        slot="payload"
        preselectFirst
        :allowEmpty="false"
        multiple trackBy="value" v-model="inventoryFilters.vehicleStatus" :options="getVehicleStatusOptions" label='text'></multiselect>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">eBay Listings:</span>
      <b-form-select slot="payload" v-model="inventoryFilters.ebayListing" :options="getEBayListingsOptions"></b-form-select>
    </detail-row>
  </div>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import inventoryPageConstants from '@/shared/website/inventoryPageConstants'
import inventoryFilterBuilder from '@/components/website/editPage/helpers/inventoryFilterBuilder'
import Multiselect from 'vue-multiselect'
import globals from '../../../../globals'

export default {
  props: {
    pageSettings: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      originalPageFilters: {},
      accountId: +this.$route.params.accountId,
      makeOptions: [{ value: null, text: 'Select Make' }],
      modelOptions: [{ value: null, text: 'Select Model' }],
      trimOptions: [{ value: null, text: 'Select Trim or Style' }],
      accountRelations: [],
      availableYears: [],
      selectedMake: null,
      selectedModel: null,
      selectedTrim: null,
      makeModelFilterTypes: inventoryPageConstants.makeModelFilterTypes,
      inventoryFilters: {
        makes: [],
        models: [],
        trims: [],
        conditions: [],
        yearFrom: null,
        yearTo: null,
        mileageFrom: '',
        mileageTo: '',
        mpgCityFrom: '',
        mpgCityTo: '',
        mpgHighwayFrom: '',
        mpgHighwayTo: '',
        priceFrom: '',
        priceTo: '',
        carfaxReport: null,
        daysInStockFrom: '',
        daysInStockTo: '',
        statusCodeFilterType: null,
        statusCode: '',
        ebizKeywords: '',
        featuredVehicle: null,
        promotionFlag: null,
        ebayListing: null,
        fuelTypes: [],
        drivetrainTypes: [],
        vehicleTypes: [],
        bodyStyles: [],
        truckCabTypes: [],
        rvTypes: [],
        transmissionTypes: [],
        vehicleStatus: [],
        locations: [],
        makeModelFilterType: 1
      }
    }
  },
  created () {
    this.originalPageFilters = globals().getClonedValue(this.pageSettings.pageFilters)
    this.populateMakes()
    this.populateYears()
    this.populateAccountRelations().then(() => {
      this.initInventoryFilterData()
    })
  },
  components: {
    detailRow,
    Multiselect
  },
  computed: {
    isLocationsFilterTypeEnabled () {
      return this.accountRelations.length > 0
    },
    isEnabledSelectRvTypes () {
      return this.inventoryFilters.vehicleTypes.find(x => x.value === inventoryPageConstants.vehicleTypeTypes.rv.value)
    },
    isEnabledSelectTruckCabTypes () {
      return this.inventoryFilters.bodyStyles.find(x => x.value === inventoryPageConstants.bodyStyleTypes.truck.value)
    },
    getYearFromOptions () {
      let years = [...this.availableYears]
      if (this.inventoryFilters.yearTo) {
        years = years.splice(0, years.findIndex(x => x === this.inventoryFilters.yearTo))
      }
      let options = [{value: null, text: 'Any Year'}]
      return options.concat(years)
    },
    getYearToOptions () {
      let years = [...this.availableYears]
      years.reverse()
      if (this.inventoryFilters.yearFrom) {
        years = years.splice(0, years.findIndex(x => x === this.inventoryFilters.yearFrom))
      }
      let options = [{value: null, text: 'Any Year'}]

      return options.concat(years)
    },
    getConditionOptions () {
      return Object.values(inventoryPageConstants.conditionTypes)
    },
    getFuelTypeOptions () {
      return Object.values(inventoryPageConstants.fuelTypeTypes)
    },
    getDrivetrainOptions () {
      return Object.values(inventoryPageConstants.drivetrainTypes)
    },
    getVehicleTypeOptions () {
      return Object.values(inventoryPageConstants.vehicleTypeTypes)
    },
    getBodyStyleOptions () {
      return Object.values(inventoryPageConstants.bodyStyleTypes)
    },
    getTruckCabTypeOptions () {
      return Object.values(inventoryPageConstants.cabStyleTypes)
    },
    getRvTypeOptions () {
      return Object.values(inventoryPageConstants.rvClassTypes)
    },
    getTransmissionOptions () {
      return Object.values(inventoryPageConstants.transmissionTypeTypes)
    },
    getCarfaxReportOptions () {
      return Object.values(inventoryPageConstants.carfaxReportFilterTypes)
    },
    getStatusCodeOptions () {
      return Object.values(inventoryPageConstants.statusCodeFilterTypes)
    },
    getFeaturedVehicleOptions () {
      return Object.values(inventoryPageConstants.featuredVehicleFilterTypes)
    },
    getPromotionFlagOptions () {
      return Object.values(inventoryPageConstants.promotionalFlagFilterTypes)
    },
    getVehicleStatusOptions () {
      return Object.values(inventoryPageConstants.vehicleStatusTypes)
    },
    getEBayListingsOptions () {
      return Object.values(inventoryPageConstants.ebayListingsTypes)
    },
    getMakeModelFilterOptions () {
      return Object.values(inventoryPageConstants.makeModelFilterTypes)
    },
    getLocationOptions () {
      return this.accountRelations.map(x => {
        return {
          value: x.accountId,
          text: `${x.accountId} - ${x.accountName}`
        }
      })
    }
  },
  methods: {
    populateMakes () {
      let apiFilter = {
        accountId: this.accountId,
        hasToIncludeRelatedAccounts: true
      }
      this.$store.dispatch('inventoryCategoryData/getMakes', { filter: apiFilter }).then(res => {
        this.makeOptions = [{value: null, text: 'Select Makes'}].concat(res.data || [])
      }).catch(ex => {
        this.$toaster.error('Something went wrong! Please reload page')
        this.$logger.handleError(ex, 'Cannot get makes')
      })
    },
    populateYears () {
      const currentYear = (new Date()).getFullYear() + 2
      this.availableYears = Array.from(Array(currentYear - 1900), (_, i) => (i + 1900).toString())
    },
    async populateAccountRelations () {
      try {
        let res = await this.$store.dispatch('accountManagement/getRelatedAccounts', { accountId: this.accountId })
        this.accountRelations = res.data || []
      } catch (ex) {
        this.$toaster.error('Something went wrong! Please reload page')
        this.$logger.handleError(ex, 'Cannot get account relations')
      }
    },
    onChangeMake () {
      this.selectedModel = null
      this.selectedTrim = null
      this.modelOptions = [{ value: null, text: 'Select Model' }]
      this.trimOptions = [{ value: null, text: 'Select Trim or Style' }]
      if (!this.selectedMake) {
        return
      }
      let apiFilter = {
        make: this.selectedMake,
        accountId: this.accountId,
        hasToIncludeRelatedAccounts: true
      }
      this.$store.dispatch('inventoryCategoryData/getModels', { filter: apiFilter }).then(res => {
        this.modelOptions = [{value: null, text: 'Select Models'}].concat(res.data || [])
      }).catch(ex => {
        this.$toaster.error('Something went wrong! Please reload page')
        this.$logger.handleError(ex, 'Cannot get models')
      })
    },
    onChangeModel () {
      this.selectedTrim = null
      this.trimOptions = [{ value: null, text: 'Select Trim or Style' }]
      if (!this.selectedModel || !this.selectedMake) {
        return
      }
      let apiFilter = {
        make: this.selectedMake,
        model: this.selectedModel,
        accountId: this.accountId,
        hasToIncludeRelatedAccounts: true
      }
      this.$store.dispatch('inventoryCategoryData/getTrims', { filter: apiFilter }).then(res => {
        this.trimOptions = [{value: null, text: 'Select Trims'}].concat(res.data || [])
      }).catch(ex => {
        this.$toaster.error('Something went wrong! Please reload page')
        this.$logger.handleError(ex, 'Cannot get models')
      })
    },
    onAddMakeModelTrim () {
      if (this.selectedModel && this.inventoryFilters.models.includes(this.selectedModel)) {
        this.$toaster.error(`This model: ${this.selectedModel} is already exist in filter`)
        return
      }
      if (this.selectedTrim && this.inventoryFilters.trims.includes(this.selectedTrim)) {
        this.$toaster.error(`This trim: ${this.selectedTrim} is already exist in filter`)
        return
      }
      this.inventoryFilters.makes.push(this.selectedMake)
      if (this.selectedModel) {
        this.inventoryFilters.models.push(this.selectedModel)
      }
      if (this.selectedTrim) {
        this.inventoryFilters.trims.push(this.selectedTrim)
      }
    },
    onRemoveMakeModelTrim (index) {
      this.inventoryFilters.makes.splice(index, 1)
      this.inventoryFilters.models.splice(index, 1)
      this.inventoryFilters.trims.splice(index, 1)
    },
    initInventoryFilterData () {
      inventoryFilterBuilder.BuildInventoryFiltersModel(this.originalPageFilters, this.inventoryFilters, this.accountRelations)
      this.prepareMakesModelsTrimsData()
    },
    buildApiInventoryFilters () {
      this.pageSettings.pageFilters = inventoryFilterBuilder.BuildApiInventoryFiltersModel(this.inventoryFilters)
    },
    onMileageFromBlur () {
      if (this.inventoryFilters.mileageTo && +this.inventoryFilters.mileageFrom > +this.inventoryFilters.mileageTo) {
        this.inventoryFilters.mileageFrom = this.inventoryFilters.mileageTo
      }
    },
    onMileageToBlur () {
      if (this.inventoryFilters.mileageFrom && this.inventoryFilters.mileageTo && +this.inventoryFilters.mileageFrom > +this.inventoryFilters.mileageTo) {
        this.inventoryFilters.mileageTo = this.inventoryFilters.mileageFrom
      }
    },
    onMpgCityFromBlur () {
      if (this.inventoryFilters.mpgCityTo && +this.inventoryFilters.mpgCityFrom > +this.inventoryFilters.mpgCityTo) {
        this.inventoryFilters.mpgCityFrom = this.inventoryFilters.mpgCityTo
      }
    },
    onMpgCityToBlur () {
      if (this.inventoryFilters.mpgCityFrom && this.inventoryFilters.mpgCityTo && +this.inventoryFilters.mpgCityFrom > +this.inventoryFilters.mpgCityTo) {
        this.inventoryFilters.mpgCityTo = this.inventoryFilters.mpgCityFrom
      }
    },
    onMpgHighwayFromBlur () {
      if (this.inventoryFilters.mpgHighwayTo && +this.inventoryFilters.mpgHighwayFrom > +this.inventoryFilters.mpgHighwayTo) {
        this.inventoryFilters.mpgHighwayFrom = this.inventoryFilters.mpgHighwayTo
      }
    },
    onMpgHighwayToBlur () {
      if (this.inventoryFilters.mpgHighwayFrom && this.inventoryFilters.mpgHighwayTo && +this.inventoryFilters.mpgHighwayFrom > +this.inventoryFilters.mpgHighwayTo) {
        this.inventoryFilters.mpgHighwayTo = this.inventoryFilters.mpgHighwayFrom
      }
    },
    onPriceFromBlur () {
      if (this.inventoryFilters.priceTo && +this.inventoryFilters.priceFrom > +this.inventoryFilters.priceTo) {
        this.inventoryFilters.priceFrom = this.inventoryFilters.priceTo
      }
    },
    onPriceToBlur () {
      if (this.inventoryFilters.priceFrom && this.inventoryFilters.priceTo && +this.inventoryFilters.priceFrom > +this.inventoryFilters.priceTo) {
        this.inventoryFilters.priceTo = this.inventoryFilters.priceFrom
      }
    },
    onDaysInStockFromBlur () {
      if (this.inventoryFilters.daysInStockTo && +this.inventoryFilters.daysInStockFrom > +this.inventoryFilters.daysInStockTo) {
        this.inventoryFilters.daysInStockFrom = this.inventoryFilters.daysInStockTo
      }
    },
    onDaysInStockToBlur () {
      if (this.inventoryFilters.daysInStockFrom && this.inventoryFilters.daysInStockTo && +this.inventoryFilters.daysInStockFrom > +this.inventoryFilters.daysInStockTo) {
        this.inventoryFilters.daysInStockTo = this.inventoryFilters.daysInStockFrom
      }
    },
    prepareMakesModelsTrimsData () {
      this.selectedMake = this.inventoryFilters.makes[0] || null
      if (this.selectedMake) {
        let maxLength = Math.max(this.inventoryFilters.models.length, this.inventoryFilters.trims.length)
        if (maxLength > 0) {
          for (var i = 0; i < maxLength - 1; i++) {
            this.inventoryFilters.makes.push(this.selectedMake)
          }
        }
      }
      this.onChangeMake()
    }
  },
  watch: {
    'inventoryFilters': {
      deep: true,
      handler () {
        this.buildApiInventoryFilters()
      }
    }
  }
}
</script>
