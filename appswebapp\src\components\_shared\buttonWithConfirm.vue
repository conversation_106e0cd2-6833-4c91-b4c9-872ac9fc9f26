<template>
  <div class="inline" :class="className">
    <l-button :class="className" :loading="loading" :disabled="disabled" :size="size" :variant="variant" @click='show=true'>
      <slot></slot>
    </l-button>
    <b-modal v-model="show" title="Confirm" lazy @ok="handleOk">
      <p class="confirmation-message">
       {{message}}
      </p>
    </b-modal>
  </div>
</template>

<script>
export default {
  name: 'button-with-confirm',
  props: {
    variant: String,
    size: {
      type: String,
      required: false,
      validator: function (value) {
        return ['sm', 'md', '', 'lg'].indexOf(value) !== -1
      }
    },
    message: {
      type: String,
      default: 'Are you sure?'
    },
    className: {
      type: String,
      default: ''
    },
    loading: Boolean,
    disabled: Boolean
  },
  data () {
    return {
      show: false
    }
  },
  methods: {
    handleOk () {
      this.$emit('confirm')
      this.show = false
    }
  }
}
</script>

<style>
  .inline{
    display: inline-block
  }

  .confirmation-message{
    font-weight: 600;
    margin: 0;
  }
</style>
