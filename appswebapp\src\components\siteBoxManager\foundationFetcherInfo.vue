<template>
  <div class="mt-3">
    <h4>
      Foundation Settings Fetcher
      <b-btn @click="refresh" class="float-right" variant="primary btn-round" size="sm">
        <span class="ion ion-ios-sync"></span><span class="d-none d-sm-inline">&nbsp; Refresh</span>
      </b-btn>
    </h4>
    <b-table
      v-if="!isLoading && items && items.length > 0"
      :items="items"
      :fields="getTableFields"
      responsive
      bordered
      striped
    >
      <template #cell(actions)="data">
        <b-btn size="sm" variant="primary" @click="showRefineSearchModal(data.item.group)"><span class="ion ion-ios-search"></span> Search</b-btn>
      </template>
    </b-table>
    <loader v-else-if="isLoading" size="lg" class="my-5"/>
    <span v-else class="text-muted">Not Found</span>
    <b-modal
      title="Foundation Settings Refine Search"
      :visible="isRefineSearchModalVisible"
      @hide="onHideRefineSearchModal"
    >
      <ValidationObserver ref="validator">
        <ValidationProvider name="SiteId" rules="required|numeric|min_value:1" v-slot="{errors}">
        <detail-row :title-position="'start'" editMode :error="errors[0]">
          <span slot="title">Site Id:</span>
          <b-form-input slot="payload" name="SiteId" v-model="refineSearchFilter.siteId"></b-form-input>
        </detail-row>
        </ValidationProvider>
        <ValidationProvider v-if="isTemplateGroupSelected" name="PageTypeId" rules="numeric|min_value:0" v-slot="{errors}">
        <detail-row :title-position="'start'" editMode :error="errors[0]">
          <span slot="title">Page Type Id:</span>
          <b-form-input slot="payload" name="PageTypeId" v-model="refineSearchFilter.pageTypeId"></b-form-input>
        </detail-row>
        </ValidationProvider>
        <ValidationProvider v-if="isTemplateGroupSelected" name="PageId" rules="numeric|min_value:0" v-slot="{errors}">
        <detail-row :title-position="'start'" editMode :error="errors[0]">
          <span slot="title">Page Id:</span>
          <b-form-input slot="payload" name="PageId" v-model="refineSearchFilter.pageId"></b-form-input>
        </detail-row>
        </ValidationProvider>
        <detail-row :title-position="'start'" editMode>
          <span slot="title">Group Name:</span>
          <b-form-select slot="payload" v-model="refineSearchFilter.groupName" :options="getGroupOptions"></b-form-select>
        </detail-row>
      </ValidationObserver>
      <b-table
        class="mt-2"
        v-if="!isRefineSearchItemsLoading"
        :items="refineSearchItems"
        :fields="getRefineSearchTableFields"
        striped
        fixed
        bordered
        responsive
      >
      </b-table>
      <loader v-else/>
      <template #modal-footer>
        <b-btn size="sm" @click="onHideRefineSearchModal">Cancel</b-btn>
        <b-btn size="sm" variant="primary" @click="applyRefineSearchFilter">Submit</b-btn>
      </template>
    </b-modal>
  </div>
</template>

<script>
import SynchronizationMonitorService from '@/services/siteBoxManager/SynchronizationMonitorService'
import loader from '@/components/_shared/loader'
import detailRow from '@/components/details/helpers/detailRow'
import moment from 'moment'

export default {
  data () {
    return {
      isLoading: true,
      refineSearchFilter: {
        siteId: null,
        groupName: null,
        pageId: 0,
        pageTypeId: 0
      },
      isRefineSearchItemsLoading: false,
      isRefineSearchModalVisible: false,
      items: [],
      refineSearchItems: []
    }
  },
  computed: {
    getRefineSearchTableFields () {
      return [
        {
          key: 'addedInQueueDateTime',
          label: 'Added in Queue',
          tdClass: 'py-2 align-middle',
          formatter: value => moment(value).format('MM/DD/YYYY hh:mm A')
        },
        {
          key: 'lastProcessedDateTime',
          label: 'Last Processed',
          tdClass: 'py-2 align-middle',
          formatter: value => moment(value).format('MM/DD/YYYY hh:mm A')
        },
        {
          key: 'status',
          label: 'Current Status',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    getTableFields () {
      return [
        {
          key: 'groupName',
          label: 'Group Name',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'messagesInQueueCount',
          label: 'Messages In Queue',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'actions',
          label: 'Actions',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    getGroupOptions () {
      return this.items.map(x => { return { value: x.group, text: x.groupName } })
    },
    isTemplateGroupSelected () {
      return this.refineSearchFilter.groupName === 'templates'
    }
  },
  components: {
    loader,
    detailRow
  },
  mounted () {
    this.populateData()
  },
  methods: {
    populateData () {
      SynchronizationMonitorService.getFoundationSettingsFetcherListingInfo().then(res => {
        this.items = res.data
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
      }).finally(() => {
        this.isLoading = false
      })
    },
    showRefineSearchModal (group) {
      this.refineSearchFilter.groupName = group
      this.isRefineSearchModalVisible = true
    },
    onHideRefineSearchModal () {
      this.isRefineSearchModalVisible = false
      this.refineSearchItems = []
    },
    refresh () {
      this.isLoading = true
      this.populateData()
    },
    applyRefineSearchFilter () {
      this.$refs.validator.validate().then(isValid => {
        if (isValid) {
          this.isRefineSearchItemsLoading = true
          SynchronizationMonitorService.getFoundationSettingsFetchingStatistic(this.refineSearchFilter).then(res => {
            this.refineSearchItems = res.data ? [res.data] : []
          }).catch(ex => {
            this.exception(ex, 'Something went wrong!')
          }).finally(() => {
            this.isRefineSearchItemsLoading = false
          })
        }
      })
    }
  }
}
</script>
