<template>
  <b-card v-if='!model.isAutoNotification'>
    <div class="border-bottom">
      <span class="h4" v-html="model.webFormTitle"></span>
    </div>
    <detail-row fixedPayloadWidth v-if="model.webFormComments">
      <span slot="title">User Message:</span>
      <div slot="payload" v-html="model.webFormComments" class="leads-webform-comments"></div>
    </detail-row>
    <div v-if="model.vehicleTitle" class="border-bottom mb-2">
      <detail-row fixedPayloadWidth>
        <span slot="title">Vehicle:</span>
        <b-link slot="payload" class="d-flex flex-column vdp-link" target="_blank" rel="noopener noreferrer" :href="model.vdpLink || '#'">
          <b-img class="leads-vehicle-img" v-if="model.vehiclePresentationPhotoUrl" :src="model.vehiclePresentationPhotoUrl"/>
          <span>{{model.vehicleTitle}}</span>
          <span v-if="model.vehicleStockNumber">Stock #: {{model.vehicleStockNumber}}</span>
          <span v-if="model.vehiclePrice">Price: {{model.vehiclePrice}}</span>
        </b-link>
      </detail-row>
    </div>
    <div v-if='model.conversationWebFormProperties'>
      <div class="border-bottom mb-1"><strong>Web Form Details</strong></div>
      <log-node
        :data="getLogNode"
        :isExpandedShallow="false"
        :isExpandedDeep="false"
      >
      </log-node>
    </div>
  </b-card>
  <div v-else>
    {{model.autoNotificationMessage}}
  </div>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import logItemNodeVue from '../../../_shared/logItemNode.vue'

export default {
  name: 'leads-webform-type-message',
  props: {
    model: { type: Object, required: true }
  },
  components: {
    'detail-row': detailRow,
    'log-node': logItemNodeVue
  },
  computed: {
    getLogNode () {
      return {
        name: '',
        nodes: this.model.conversationWebFormProperties
      }
    }
  }
}
</script>

<style scoped>
  .leads-vehicle-img {
    width: 107px;
    height: auto;
  }

  .leads-webform-comments {
    max-height: 200px;
    max-width: 300px;
    overflow: auto;
  }

  .leads-webform-comments::-webkit-scrollbar {
    width: 2px;
  }

  .leads-webform-comments::-webkit-scrollbar-track {
    background: #fdfdfd;
  }

  .leads-webform-comments::-webkit-scrollbar-thumb {
    background: rgb(207, 207, 207);
  }

  .leads-webform-comments::-webkit-scrollbar-thumb:hover {
    background: rgb(168, 167, 167);
  }

  .vdp-link {
    color: rgba(24, 28, 33, 0.9);
  }
  .vdp-link:hover {
    color: #bf0e16;
  }
</style>
