<template>
  <b-table
    :items="items"
    :fields="getTableFields"
    responsive
    striped
  >
    <template #cell(details)="data">
      <b-btn size="sm" @click="data.toggleDetails">{{ data.detailsShowing ? 'Hide' : 'Show' }} Details</b-btn>
    </template>
    <template #row-details="data">
      <b-card>
        <detail-row :large-payload-width="true">
          <span slot="title">From:</span>
          <span slot="payload">{{(data.item.email || {}).from || '-'}}</span>
        </detail-row>
        <detail-row :large-payload-width="true">
          <span slot="title">From Name:</span>
          <span slot="payload">{{(data.item.email || {}).fromName || '-'}}</span>
        </detail-row>
        <detail-row :large-payload-width="true">
          <span slot="title">To:</span>
          <span slot="payload">{{getStringFromArray((data.item.email || {}).to)}}</span>
        </detail-row>
        <detail-row :fixed-payload-width="true">
          <span slot="title">CC:</span>
          <span slot="payload">{{getStringFromArray((data.item.email || {}).cc)}}</span>
        </detail-row>
        <detail-row :fixed-payload-width="true">
          <span slot="title">BCC:</span>
          <span slot="payload">{{getStringFromArray((data.item.email || {}).bcc)}}</span>
        </detail-row>
        <detail-row :large-payload-width="true">
          <span slot="title">Reply To:</span>
          <span slot="payload">{{(data.item.email || {}).replyTo || '-'}}</span>
        </detail-row>
        <detail-row :large-payload-width="true">
          <span slot="title">Subject:</span>
          <span slot="payload">{{(data.item.email || {}).subject || '-'}}</span>
        </detail-row>
        <detail-row :fixed-payload-width="true">
          <span slot="title">Body:</span>
          <span slot="payload" v-if="!data.item.email || !data.item.email.body">-</span>
          <span v-else slot="payload">
            <span v-if="data.item.email.isHtml" v-html="data.item.email.body"></span>
            <span v-else>{{data.item.email.body}}</span>
          </span>
        </detail-row>
      </b-card>
    </template>
    <template #cell(manage)="data">
      <b-btn size="sm" variant="primary" @click="reprocess(data.item)">Reprocess</b-btn>
      <b-btn size="sm" @click="deleteItem(data.item)">Delete</b-btn>
    </template>
  </b-table>
</template>

<script>
import moment from 'moment'
import detailRow from '../../details/helpers/detailRow'
import IntegrityReportService from '../../../services/leads/IntegrityReportService'

export default {
  props: {
    items: { type: Array, required: true }
  },
  components: {
    detailRow
  },
  computed: {
    getTableFields () {
      return [
        {
          key: 'proxyEmail',
          label: 'Proxy Email',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'details',
          label: '',
          tdClass: 'py-2 algn-middle'
        },
        {
          key: 'itemDescription',
          label: 'Description',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'dateTimeInserted',
          label: 'DateTime',
          tdClass: 'py-2 align-middle',
          formatter: value => moment(value).format('MM/DD/YYYY hh:mm:ss A')
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle'
        }
      ]
    }
  },
  methods: {
    getStringFromArray (items) {
      if (!items || items.length === 0 || !Array.isArray(items)) {
        return '-'
      }

      return items.join(', ')
    },
    deleteItem (item) {
      this.$emit('delete', item.id)
    },
    reprocess (item) {
      IntegrityReportService.sendToReprocessNotMatchedEmail(item.id).then(res => {
        this.$toaster.success('Sent to Reprocess Successfully')
        this.items.splice(this.items.findIndex(x => x.id === item.id), 1)
      }).catch(ex => {
        this.$toaster.exception(ex, 'Exception occurred on send to reprocess')
        this.$logger.handleError(ex, 'Exception occurred on send to reprocess')
      })
    }
  }
}
</script>
