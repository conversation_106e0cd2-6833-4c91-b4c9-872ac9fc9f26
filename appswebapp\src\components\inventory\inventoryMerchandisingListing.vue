<template>
  <div>
    <b-table
    class="products-table card-table"
    :items="vehicles"
    :current-page="inventoryData.pageNumber"
    :per-page="inventoryData.pageSize"
    :sort-by.sync="sortBy"
    :sort-desc.sync="sortDesc"
    @sort-changed="onSortChanged"
    :fields="fields"
    :striped="true"
    :bordered="false"
    :no-sort-reset="true"
    :no-local-sorting="true"
    responsive
    >
      <template #cell(title)="data">
        <div class="media align-items-center">
          <router-link class="media-body text-dark d-flex align-items-center" :to="{ path: getDetailsPath(data.item) }">
            <img class="d-block float-left ui-w-50 mr-3" style="min-height: 1px" :src="getVehiclePhotoSrc(data.item)">
            <span>{{data.item | getVehicleTitle}}</span>
          </router-link>
        </div>
      </template>

      <template #cell(hasKeywords)="data">
        <i class="ion ion-ios-checkmark text-success zoomed" v-if="data.item.hasKeywords"></i>
        <i class="ion ion-ios-close text-danger zoomed" v-else></i>
      </template>

      <template #cell(galleryDescription)="data">
        <i class="ion ion-ios-checkmark text-success zoomed" v-if="hasGalleryDescription(data.item)"></i>
        <i class="ion ion-ios-close text-danger zoomed" v-else></i>
      </template>

      <template #cell(isHomepageFeatured)="data">
        <write-permission-wrapper variant="disable-overlay">
          <b-form-checkbox v-model="data.item.isHomepageFeatured"></b-form-checkbox>
        </write-permission-wrapper>
      </template>

      <template #cell(hasToIncludePromotionalFlag)="data">
        <write-permission-wrapper variant="disable-overlay">
          <b-form-checkbox v-model="data.item.hasToIncludePromotionalFlag"></b-form-checkbox>
        </write-permission-wrapper>
      </template>

      <template #cell(actions)="data">
       <router-link :to="{ path: getDetailsPath(data.item, true) }" v-if="isOriginalModel(data.item)">
          <b-btn variant="secondary" size="sm" class="m-width-75"><font-awesome-icon icon="pencil-alt" /> <span class="btn-title">Edit</span></b-btn>
        </router-link>
        <b-btn v-else variant="primary" size="sm" class="m-width-75" @click="saveItem(data.item)"><font-awesome-icon icon="cloud-upload-alt" /> <span class="btn-title">Save</span></b-btn>
      </template>
    </b-table>
  </div>
</template>

<script>
import permission from '@/shared/common/permissions'
import globals from '@/globals'
import vehicleService from '@/services/inventory/VehicleManagementService'
import writePermissionWrapper from '../_shared/writePermissionWrapper'

export default {
  props: {
    inventoryData: {
      type: Object,
      default: function () {
        return {
          vehicles: [],
          pageNumber: 1,
          pageSize: 25
        }
      }
    },
    siteSettings: {
      type: Object,
      required: true
    },
    sortField: String,
    sortOrderDesc: Boolean
  },
  data () {
    return {
      permission,
      vehicles: globals().getClonedValue(this.inventoryData.vehicles),
      originalVehicles: globals().getClonedValue(this.inventoryData.vehicles),
      fields: [ { key: 'title',
        label: 'Vehicle',
        sortable: true,
        tdClass: 'py-2 align-middle',
        thStyle: 'min-width: 300px'
      }, {
        key: 'stockNumber',
        label: 'Stock #',
        sortable: true,
        tdClass: 'py-2 align-middle'
      }, {
        key: 'age',
        label: 'Age',
        sortable: true,
        tdClass: 'py-2 align-middle'
      }, {
        key: 'hasKeywords',
        label: 'Keywords',
        sortable: true,
        tdClass: 'py-2 align-middle zoomed'
      }, {
        key: 'galleryDescription',
        label: 'Description',
        sortable: false,
        tdClass: 'py-2 align-middle zoomed'
      }, {
        key: 'isHomepageFeatured',
        label: 'Featured',
        sortable: true,
        tdClass: 'py-2 align-middle'
      }, {
        key: 'hasToIncludePromotionalFlag',
        label: 'Special',
        sortable: true,
        tdClass: 'py-2 align-middle'
      }, {
        key: 'actions',
        label: 'Actions',
        sortable: false,
        tdClass: 'py-2 align-middle text-nowrap'
      }]
    }
  },
  methods: {
    onSortChanged (value) {
      this.$emit('sortChanged', value)
    },
    isOriginalModel (item) {
      let originalItem = this.originalVehicles.find(x => x.vin === item.vin)

      if (!originalItem) {
        return false
      }

      return item.isHomepageFeatured === originalItem.isHomepageFeatured && item.hasToIncludePromotionalFlag === originalItem.hasToIncludePromotionalFlag
    },
    saveItem (item) {
      this.$set(item, '_rowVariant', 'info')

      let marketingInfo = {
        isHomepageFeatured: item.isHomepageFeatured,
        hasToIncludePromotionalFlag: item.hasToIncludePromotionalFlag
      }

      const updateData = {
        marketingInformation: marketingInfo,
        vin: item.vin
      }

      return vehicleService.updateVehicle(item.accountId, updateData, 1).then(result => {
        let vehicle = this.originalVehicles.find(x => x.vin === result.data.vin)
        vehicle.isHomepageFeatured = result.data.marketingInformation.isHomepageFeatured
        vehicle.hasToIncludePromotionalFlag = result.data.marketingInformation.hasToIncludePromotionalFlag
        this.$set(item, '_rowVariant', 'success')
      }).catch(reason => {
        this.$set(item, '_rowVariant', 'danger')
        this.$logger.handleError(reason, 'Can\'t update merchandising information', updateData)
      })
    },
    hasGalleryDescription (item) {
      let div = document.createElement('div')
      div.innerHTML = item.galleryDescription
      return div.textContent.trim() !== ''
    },
    getVehiclePhotoSrc (item) {
      return (item.presentationPhoto || {}).photo107 + '?q=' + item.actualPhotosDateTimeEdited
    },
    getDetailsPath (item, hasToAppendTab) {
      return `/inventory/${item.accountId}/edit/${item.vin}` + (
        hasToAppendTab
          ? `/?tabindex=3`
          : ''
      )
    }
  },
  filters: {
    getVehicleTitle: function (value) {
      if (!value) return ''
      let title = ''
      if (value.year > 0) {
        title = value.year.toString()
      }

      title = [title, value.make, value.model, value.trim].filter(v => v && v !== '').join(' ')

      return title.trim()
    }
  },
  computed: {
    sortBy: {
      get () {
        return this.sortField
      },
      set () {}
    },
    sortDesc: {
      get () {
        return this.sortOrderDesc
      },
      set () {}
    }
  },
  components: {
    'write-permission-wrapper': writePermissionWrapper
  },
  watch: {
    'inventoryData.vehicles': {
      handler: function (newValue) {
        this.vehicles = globals().getClonedValue(newValue)
        this.originalVehicles = globals().getClonedValue(newValue)
      },
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped>
  .m-width-75 {
    min-width: 75px;
  }
  .zoomed {
    font-size: 2.2rem;
  }

  .custom-control.custom-checkbox.custom-control-inline {
    transform: translate3d(0, 0, 0);
    -webkit-overflow-scrolling: touch; // ios safari fix
  }
</style>
