import Vue from 'vue'

class MotorcycleModifier {
  modifyFeature (feature) {
    this.updateExteriorSection(feature)
    this.markFeatureAsProperty(feature)
  }

  updateExteriorSection (feature) {
    // https://ebizautos.atlassian.net/browse/APPS-299
    // Motorcycle:
    // We have to remove question marks from 'Accidents?' and 'Body work?' checkboxes and add hint '(Describe in Notes)'
    //  -  MotorcycleExteriorHasAccidents = -4000,
    //  -  MotorcycleExteriorHasBodywork = -4001,

    if ([-4000, -4001].includes(feature.id)) {
      feature.name = feature.name.trimChar('?')
      Vue.set(feature, 'hint', '(Describe in Notes)')
    }
  }

  markFeatureAsProperty (feature) {
    // Motorcycle type is a feature but we want to show it like a field.
    //  -  MotorcycleType = -4053

    if ([-4053].includes(feature.id)) {
      Vue.set(feature, 'displaySection', 'Vehicle Overview')
    }
  }
}

export default MotorcycleModifier
