<template>
  <details-section :title="conditionMetadata.name" @cancel="onCancel" v-model="mode" v-if="conditionMetadata" canBeHidden :visible="conditionMetadata.featureCategory.isDisplayed" @visibilityChange='onVisibilityChange'>
    <div class="view" v-if="mode === 'view'">
      <b-row>
        <b-col md="6">
          <detail-row v-for="i in getSelectsCols.left" :key="i.id">
            <span slot="title">{{i.name}}</span>
            <span slot="payload">{{getDescForSelect(i)}}</span>
          </detail-row>
        </b-col>

        <b-col md="6">
           <detail-row v-for="i in getSelectsCols.right" :key="i.id">
            <span slot="title">{{i.name}}</span>
            <span slot="payload">{{getDescForSelect(i)}}</span>
          </detail-row>

          <detail-row class="pb-0" titlePosition="start">
            <span slot="title">Additional Conditions:</span>
            <b-form-group slot="payload" class="mb-0">
              <detail-row v-for="(i, index) in getCheckboxes" :key="i.id" :class="{'pt-0' : index === 0}">
                <b-form-checkbox slot="payload" :checked="i.value" disabled>
                  <span style="color:rgb(78, 81, 85);">{{i.name}}</span>
                </b-form-checkbox>
              </detail-row>
            </b-form-group>
          </detail-row>
        </b-col>

      </b-row>
      <detail-row titlePosition="start">
        <span slot="title">{{getNotes.name}}:</span>
        <span slot="payload">{{getNotes.value}}</span>
      </detail-row>
    </div>

    <div class="edit" v-else-if="mode === 'edit'">
      <b-row>
        <b-col md="6">
          <detail-row fixedPayloadWidth editMode v-for="i in getSelectsCols.left" :key="i.id" wideMode noFixedSize>
            <span slot="title">{{i.name}}</span>
            <b-form-select slot="payload" v-model="i.value" :options="getNameValueOptions(i.nameValueOptions)"></b-form-select>
          </detail-row>
        </b-col>

        <b-col md="6">
          <detail-row fixedPayloadWidth editMode v-for="i in getSelectsCols.right" :key="i.id" wideMode noFixedSize>
            <span slot="title">{{i.name}}</span>
            <b-form-select slot="payload" v-model="i.value" :options="getNameValueOptions(i.nameValueOptions)"></b-form-select>
          </detail-row>

          <detail-row fixedPayloadWidth editMode titlePosition="start" class="pt-3">
            <span slot="title">Additional Conditions:</span>
            <b-form-group slot="payload" class="mb-0">
              <detail-row  v-for="(i, index) in getCheckboxes" :key="i.id" :class="{'pt-0' : index === 0}">
                <b-form-checkbox slot="payload" v-model="i.value" class="pb-1">
                  {{i.name}}
                </b-form-checkbox>
              </detail-row>
            </b-form-group>
          </detail-row>
        </b-col>

        <b-col md="6">
          <ValidationProvider rules="min:0|xml" :name="getNotes.name" v-slot="{errors}">
           <detail-row fixedPayloadWidth editMode titlePosition="start" :error="errors[0]">
              <span slot="title">{{getNotes.name}}:</span>
              <b-form-textarea slot="payload" v-model="getNotes.value"
                :name ="getNotes.name"
                :rows="3"
                :max-rows="6">
              </b-form-textarea>
            </detail-row>
          </ValidationProvider>
        </b-col>
      </b-row>
    </div>
  </details-section>
</template>

<script>
import { mapGetters } from 'vuex'
import detailRow from '../helpers/detailRow'
import detailsSection from '@/components/details/detailsSection'

export default {
  name: 'vehicle-condition-section',
  data () {
    return {
      mode: 'view'
    }
  },
  computed: {
    ...mapGetters('details', ['vehicle', 'conditionMetadata']),
    getSelectsCols () {
      let cols = this.findFeatures(2, 3)
      let mid = Math.ceil(this.findFeatures(1, 2, 3).length / 2)
      return {
        left: cols.slice(0, mid),
        right: cols.slice(mid)
      }
    },
    getCheckboxes () {
      return this.findFeatures(1)
    },
    getNotes () {
      return this.findFeatures(6)[0]
    }
  },
  methods: {
    findFeatures (...valueType) {
      return this.conditionMetadata.features
        .filter(x => Array.prototype.concat(valueType).includes(x.valueType))
    },
    getDescForSelect (select) {
      return select.nameValueOptions.find(x => x.value === select.value).key
    },
    onVisibilityChange (val) {
      this.conditionMetadata.featureCategory.isDisplayed = val
    },
    getNameValueOptions (nameValueOptions) {
      return nameValueOptions.map(x => ({
        value: x.value,
        text: x.key
      }))
    },
    onCancel () {
      this.$forceUpdate()
    }
  },
  components: {
    'details-section': detailsSection,
    'detail-row': detailRow
  }
}
</script>
