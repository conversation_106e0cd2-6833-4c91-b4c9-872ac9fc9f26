<template>
  <div>
    <h4>AutoVideo Settings</h4>
    <div v-if="isLoaded && !isErrorOccurred">
      <b-alert v-if="!isVideoMarketingEnabled || isInactiveAutoVideStatus" show variant="warning" class="text-center">
        <span v-if="!isVideoMarketingEnabled">Video Marketing is not yet activated for your account</span>
        <span v-else>AutoVideo is disabled for your account</span>
        <br/>
        <span>
          Please <a :href="getSupportLink" class="text-primary"><u>contact</u></a> your support team for additional information or to activate this feature.
        </span>
      </b-alert>
      <b-tabs class="nav-tabs-top nav-responsive-sm" v-model="selectedTab" no-fade>
        <b-tab class="p-3" v-for="tab in tabsOptions" :title="tab.title" :key="tab.value">
          <component :is="tab.componentName" :settings="settings[tab.key]" @refresh="initData" :readOnlyMode="!isVideoMarketingEnabled"/>
        </b-tab>
      </b-tabs>
    </div>
    <loader v-else-if="!isErrorOccurred" size="lg"/>
  </div>
</template>

<script>
import musicSettingsSection from '@/components/inventory/autoVideoSettingsSection/musicSettingsSection'
import voiceSettingsSection from '@/components/inventory/autoVideoSettingsSection/voiceSettingsSection'
import activationSettingsSection from '@/components/inventory/autoVideoSettingsSection/activationSettingsSection'
import ttsSettingsSection from '@/components/inventory/autoVideoSettingsSection/ttsSettingsSection'
import {ObjectSchema} from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import loader from '../../components/_shared/loader.vue'
import VideoEncoderService from '../../services/inventory/VideoEncoderService'
import { mapGetters } from 'vuex'
import permissions from '@/shared/common/permissions'
import videoEncoderTypes from '../../shared/inventory/videoEncoderTypes'

const tabs = {
  activationSettings: {value: 'activation', title: 'Activation Settings', componentName: 'activationSettingsSection', key: 'activationSettings'},
  ttsSettings: {value: 'tts', title: 'TTS Settings', componentName: 'ttsSettingsSection', key: 'descriptionTextSettings'},
  musicSettings: {value: 'music', title: 'Music Settings', componentName: 'musicSettingsSection', key: 'musicSettings'},
  voiceSettings: {value: 'voice', title: 'Voice Settings', componentName: 'voiceSettingsSection', key: 'voiceSettings'}
}

const defaultValues = new ObjectSchema({
  tab: { type: String, default: tabs.activationSettings.value }
})
let queryHelper = new QueryStringHelper(defaultValues)

export default {
  name: 'inventory-account-autovideo-settings',
  metaInfo: {
    title: 'AutoVideo Settings'
  },
  props: {
    accountId: {
      type: Number,
      required: true
    }
  },
  data () {
    return {
      isLoaded: false,
      isErrorOccurred: false,
      filter: defaultValues.getObject(),
      tabs: tabs,
      settings: {},
      isVideoMarketingEnabled: false
    }
  },
  created () {
    this.filter = queryHelper.parseQueryStringToObject(this.$router)
    this.initData()
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {hasPermissions: () => false}
    },
    isInactiveAutoVideStatus () {
      return this.settings.activationSettings.activationStatus === videoEncoderTypes.autoVideoStatusTypes.inactive.value
    },
    hasAccessToTTSSettingsTab () {
      return this.user.hasPermissions(permissions.IMFullAccess)
    },
    tabsOptions () {
      let options = []
      options.push(tabs.activationSettings)
      if (this.hasAccessToTTSSettingsTab) {
        options.push(tabs.ttsSettings)
      }
      options.push(tabs.musicSettings)
      options.push(tabs.voiceSettings)
      return options
    },
    selectedTab: {
      get () {
        let index = this.tabsOptions.findIndex(x => x.value === this.filter.tab)
        if (index < 0) {
          return 0
        }
        return index
      },
      set (index) {
        let tab = this.tabsOptions[index]
        this.filter.tab = tab.value
        queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
      }
    },
    isProduction () {
      return process.env.NODE_ENV === 'production'
    },
    getSupportLink () {
      return this.isProduction ? 'https://cp.ebizautos.com/index.cfm?navid=249' : 'https://cp.sandbox.ebizautos.com/index.cfm?navid=249'
    }
  },
  components: {
    loader,
    musicSettingsSection,
    voiceSettingsSection,
    activationSettingsSection,
    ttsSettingsSection
  },
  methods: {
    initData () {
      VideoEncoderService.getAccountPhotoToVideoSettings(this.accountId).then(res => {
        this.settings = res.data.photoToVideoSettings
        this.isVideoMarketingEnabled = res.data.isVideoMarketingEnabled
      }).catch(ex => {
        this.isErrorOccurred = true
        this.$toaster.error('Something went wrong!')
      }).finally(() => {
        this.isLoaded = true
      })
    }
  }
}
</script>
