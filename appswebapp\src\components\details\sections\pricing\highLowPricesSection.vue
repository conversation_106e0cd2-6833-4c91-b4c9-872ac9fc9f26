<template>
  <details-section title="Pricing" @cancel="onCancel" v-model="mode" noBorder>
    <div class="view" v-if="mode === 'view'">
      <b-table
        class="price-table card-table"
        tbody-tr-class="border border-top-0"
        thead-class="border"
        :items="getPrices"
        :fields="fields"
        :striped="true"
        responsive
      >
        <template #cell(amount)="data">
          <span>{{formatPrice(data.item.price)}}</span>
        </template>
        <template #cell(dlabel)="data">
          <span>{{data.item.displayLabel}}</span>
        </template>
        <template #cell(dstatus)="data">
          <span :class="{striked : data.item.isStriked }">{{data.item.displayStatusLabel}}</span>
        </template>
        <template #cell(lock)="data">
          <span
            :class="{'locked': data.item.importLock}"
            v-if="data.item.canBeLocked"
          >
            {{data.item.importLock ? "Locked" : "Unlocked"}}
          </span>
        </template>
      </b-table>
    </div>

    <div class="edit" v-else-if="mode === 'edit'">
      <b-table
        class="price-table card-table"
        tbody-tr-class="border border-top-0"
        thead-class="border"
        :items="getPrices"
        :fields="fields"
        :striped="true"
        :bordered="false"
        responsive
      >

        <template #cell(amount)="data">
          <price-input
            v-if="data.item.isEditable"
            :value="data.item.price"
            @input="onPriceInput($event, data.item.isHighPrice)"
            active size="sm"
            class="edit-width"
          />
          <span class="ml-1" v-else>{{formatPrice(data.item.price)}}</span>
        </template>

        <template #cell(dlabel)="data">
          <ValidationProvider immediate :name="data.item.title" rules="min:0|xml" v-slot="{errors}">
          <b-form-input
            v-if="(data.item.displayLabel !== undefined) && data.item.isEditable"
            :value="data.item.displayLabel"
            @input="onLabelInput($event, data.item.isHighPrice)"
            :name ="data.item.title"
            size="sm"
            class="edit-width"
          />
          <span v-else class="ml-1">{{data.item.displayLabel}}</span>
          <span class="error_text">{{errors[0]}}</span>
          </ValidationProvider>
        </template>

        <template #cell(dstatus)="data">
          <span :class="{striked : data.item.isStriked }">{{data.item.displayStatusLabel}}</span>
        </template>

        <template #cell(lock)="data">
          <b-form-checkbox
            v-if="data.item.canBeLocked"
            :checked="data.item.importLock"
            @change="onLockChange($event, data.item.isHighPrice)"
          >
            Lock Price
          </b-form-checkbox>
        </template>

      </b-table>
    </div>

    <div class="disclaimer-block" v-if="hasSystemCalculatedLowPrice">
      <p class="text-muted">System-Calculated Low is only generated and displayed when no low Price is entered and Discounts are applied.</p>
      <p class="text-muted">System-Calculated Low Price is used only in eBizAutos and NOT exported to Third Parties.</p>
      <p class="text-muted">System-Calculated Low Price displays in Gallery with the label (Low Price Label).</p>
    </div>
  </details-section>
</template>

<script>
import {mapGetters} from 'vuex'
import {priceDisplayTypes, pricingTemplateTypes} from '@/shared/details/detailsTypes'
import detailsSection from '@/components/details/detailsSection'
import priceInput from '@/components/_shared/priceInput'
import detailRow from '@/components/details/helpers/detailRow'

export default {
  name: 'vehicle-history-section',
  data () {
    return {
      mode: 'view',
      fields: [{ key: 'title',
        label: 'Price / Discount Type',
        tdClass: 'py-3 align-middle',
        thStyle: 'min-width: 300px'
      }, {
        key: 'amount',
        label: 'Amount',
        tdClass: 'py-3 align-middle'
      }, {
        key: 'dlabel',
        label: 'Display Label',
        tdClass: 'py-3 align-middle'
      }, {
        key: 'dstatus',
        label: 'Display Status',
        tdClass: 'py-3 align-middle'
      }, {
        key: 'lock',
        label: 'Import Lock',
        tdClass: 'py-3 align-middle'
      }],
      hasSystemCalculatedLowPrice: false
    }
  },
  created () {
    this.init()
  },
  computed: {
    ...mapGetters('details', ['vehicle', 'siteSettings']),
    getPrices () {
      let pricingNode = this.vehicle.pricing

      let prices = [{
        isEditable: true,
        canBeLocked: true,
        isHighPrice: true,
        title: 'High Price',
        price: pricingNode.highPrice,
        displayLabel: pricingNode.highPriceLabel,
        importLock: pricingNode.highPriceLockedBy !== 0,
        displayStatusLabel: this.getPriceDisplaySettings.highPrice.message,
        isStriked: this.getPriceDisplaySettings.highPrice.isStriked
      }]

      pricingNode.discounts.forEach(x => prices.push({
        price: x.price,
        title: x.label
      }))

      prices.push({
        isEditable: true,
        canBeLocked: true,
        isHighPrice: false,
        title: 'Low Price',
        price: pricingNode.lowPrice,
        displayLabel: pricingNode.lowPriceLabel,
        importLock: pricingNode.lowPriceLockedBy !== 0,
        displayStatusLabel: this.getPriceDisplaySettings.lowPrice.message,
        isStriked: this.getPriceDisplaySettings.lowPrice.isStriked
      })

      if (this.hasSystemCalculatedLowPrice) {
        prices.push({
          title: 'System Calculated Low Price *',
          price: pricingNode.actualLowPrice,
          displayLabel: pricingNode.lowPriceLabel
        })
      }

      return prices
    },
    getPriceDisplaySettings () {
      let isNew = this.vehicle.isNew

      let highPriceDisplayType = isNew
        ? this.siteSettings.priceSettings.newHighPriceDisplayType
        : this.siteSettings.priceSettings.usedHighPriceDisplayType

      let lowPriceDisplayType = isNew
        ? this.siteSettings.priceSettings.newLowPriceDisplayType
        : this.siteSettings.priceSettings.usedLowPriceDisplayType

      let pricingTemplateType = isNew
        ? this.siteSettings.priceSettings.newPricingTemplateType
        : this.siteSettings.priceSettings.usedPricingTemplateType

      let isHighPriceHidden =
          (highPriceDisplayType === priceDisplayTypes.no) ||
          (pricingTemplateType === pricingTemplateTypes.paymentOnly) ||
          (pricingTemplateType === pricingTemplateTypes.displayLabelAndContact)

      let isLowPriceHidden =
          (lowPriceDisplayType === priceDisplayTypes.no) ||
          (pricingTemplateType === pricingTemplateTypes.paymentOnly) ||
          (pricingTemplateType === pricingTemplateTypes.displayLabelAndContact)

      let highPriceMessage = isHighPriceHidden
        ? 'Not Displayed'
        : 'Displayed'

      let lowPriceMessage = isLowPriceHidden
        ? 'Not Displayed'
        : 'Displayed'

      return {
        highPrice: {
          message: highPriceMessage,
          isStriked: !isHighPriceHidden && highPriceDisplayType === priceDisplayTypes.yesStriked
        },
        lowPrice: {
          message: lowPriceMessage,
          isStriked: !isLowPriceHidden && lowPriceDisplayType === priceDisplayTypes.yesStriked
        }
      }
    }
  },
  methods: {
    init () {
      let pricingNode = this.vehicle.pricing
      this.hasSystemCalculatedLowPrice = pricingNode.highPrice && !pricingNode.lowPrice && (pricingNode.actualLowPrice !== pricingNode.lowPrice)
    },
    onPriceInput (val, isHighPrice) {
      if (isHighPrice) {
        this.vehicle.pricing.highPrice = val
      } else {
        this.vehicle.pricing.lowPrice = val
      }
    },
    onLabelInput (val, isHighPrice) {
      if (isHighPrice) {
        this.vehicle.pricing.highPriceLabel = val
      } else {
        this.vehicle.pricing.lowPriceLabel = val
      }
    },
    onLockChange (val, isHighPrice) {
      let lockId = val ? 27 : 0 // 27 - cp lock id

      if (isHighPrice) {
        this.vehicle.pricing.highPriceLockedBy = lockId
      } else {
        this.vehicle.pricing.lowPriceLockedBy = lockId
      }
    },
    formatPrice (p) {
      return this.$locale.formatCurrency(p)
    },
    onCancel () {
      this.$forceUpdate()
    }
  },
  components: {
    'details-section': detailsSection,
    'detail-row': detailRow,
    'price-input': priceInput
  },
  watch: {
    vehicle () {
      this.init()
    }
  }
}
</script>
<style>
  .error_text {
    color: red;
  }
</style>

<style scoped lang="scss">
  .disclaimer-block {
    padding: 20px;
  }
  .disclaimer-block > p {
    font-size: 0.8rem;
    margin-bottom: 0.2rem;
  }
  .disclaimer-block > p:before {
    content: '*';
    margin-right: 0.3rem;
  }
  .edit-width {
    width: 6rem;
  }
  .striked::after {
    content: 'w/Strikethrough';
    text-decoration: line-through;
    margin-left: 0.3rem;
  }
  .locked {
    text-decoration: underline;
    color: #ca1713;
  }
  .custom-control.custom-checkbox.custom-control-inline {
    transform: translate3d(0, 0, 0);
    -webkit-overflow-scrolling: touch; // ios safari fix
  }
</style>
