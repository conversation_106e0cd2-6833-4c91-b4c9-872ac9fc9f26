<template>
  <editSettingsHelper v-if='isReady' title="Admin Craigslist Settings" @save="saveSettings" @cancel="cancel" @changeMode="changeMode" :isDisabled="isDisabled" :isLoading="isUpdatingProcessing" :isViewMode="isViewMode">
    <div slot="settings-content">
      <b-form-checkbox class="flex-inline-sized status-incoming-inv my-2" v-model="isPostingAllowed" :disabled="isViewMode">
        Enable Posting
      </b-form-checkbox>
    </div>
  </editSettingsHelper>
</template>

<script>
import { mapGetters } from 'vuex'
import globals from '../../../globals'
import editSettingsHelper from '../../_shared/editSettingsHelper.vue'

export default {
  name: 'section-admin-settings',
  props: {
    isDisabled: {
      type: Boolean,
      required: true
    },
    isUpdatingProcessing: {
      type: Boolean,
      required: true
    }
  },
  data () {
    return {
      isReady: false,
      isViewMode: true,
      isPostingAllowed: true
    }
  },
  created () {
    this.populateData()
  },
  components: {
    editSettingsHelper
  },
  computed: {
    ...mapGetters('craigslistSettings', ['settingsPutData'])
  },
  methods: {
    changeMode (newMode) {
      this.isViewMode = newMode
    },
    saveSettings () {
      this.isViewMode = true
      this.$store.commit('craigslistSettings/setAdminSettings', this.isPostingAllowed)
      this.putSettingsData()
    },
    cancel () {
      this.isPostingAllowed = globals().getClonedValue(this.settingsPutData.isPostingAllowed)
      this.isViewMode = true
    },
    populateData () {
      this.isPostingAllowed = globals().getClonedValue(this.settingsPutData.isPostingAllowed)
      this.isReady = true
    },
    putSettingsData () {
      this.$emit('putSettingsData', this.settingsPutData)
    }
  }
}
</script>
