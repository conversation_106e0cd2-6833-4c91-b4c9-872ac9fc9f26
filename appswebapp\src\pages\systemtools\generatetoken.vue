<template>
  <div>
    <h4>eBizAutos GenerateToken Service</h4>
       <div>
         <b-row class="mb-2">
            <b-col cols="12" sm="6" md="4" lg="3" xl="2">
               <label for="UserName">UserName</label>
               <b-input id="UserName" required v-model="loginRequest.UserName"></b-input>
               <br>
             </b-col>
            <b-col cols="12" sm="6" md="4" lg="3" xl="2">
               <label for="Password">Password</label>
               <b-input id="Password" required v-model="loginRequest.Password"></b-input>
               <br>
             </b-col>
            <b-col cols="12" sm="6" md="4" lg="3" xl="2">
               <label for="DeviceId">DeviceId</label>
               <b-input id="DeviceId" required v-model="loginRequest.DeviceId"></b-input>
               <br>
             </b-col>
            <b-col cols="12" sm="6" md="4" lg="3" xl="2">
               <label for="DeviceModel">DeviceModel</label>
               <b-input id="DeviceModel" required v-model="loginRequest.DeviceModel"></b-input>
               <br>
             </b-col>
            <b-col cols="12" sm="6" md="4" lg="3" xl="2">
               <label for="ApplicationName">ApplicationName</label>
               <b-input id="ApplicationName" required v-model="loginRequest.ApplicationName"></b-input>
               <br>
             </b-col>
           </b-row>
          <b-button variant="outline-primary" @click="GenerateToken">Generate Token</b-button>
         </div>
        <br>
      <label for="generatedToken">Generated Token</label>
      <b-input id="generatedToken" readonly rows="4" v-model="generatedToken"></b-input>
      <br>
      <b-button variant="outline-primary" @click="CopyToBoofer">Copy</b-button>
   </div>
</template>

<script>
export default {
  name: 'generatetoken',
  data () {
    return {
      loginRequest: {
        UserName: '',
        Password: '',
        DeviceId: '',
        DeviceModel: '',
        ApplicationName: ''
      },
      generatedToken: ''
    }
  },
  methods: {
    async GenerateToken () {
      const params = this.loginRequest
      try {
        const result = await this.$store.dispatch('systemTools/generateToken', params)
        this.generatedToken = result.data.authenticationToken
      } catch (err) {
        if (err.response && err.response.data) {
          this.$toaster.error(err.response.data, { timeout: 8000 })
        } else {
          this.$toaster.error('An error occured. Please try again later.', { timeout: 8000 })
        }
        console.error(err, 'Error occurred while generating token', params)
      }
    },
    CopyToBoofer () {
      let tokenToCopy = document.querySelector('#generatedToken')
      tokenToCopy.select()
      if (document.execCommand('copy')) {
        this.$toaster.success('Token copied successfully', { timeout: 2000 })
      } else {
        this.$toaster.error('Token copied successfully', { timeout: 5000 })
      }
      tokenToCopy = document.getSelection().removeAllRanges()
    }
  }
}
</script>

<style scoped>

</style>
