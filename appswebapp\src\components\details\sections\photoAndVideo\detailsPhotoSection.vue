<template>
  <b-card-body v-if="id !== null">
    <header>
      <span class="section-title">{{title}}</span>
      <div>
        <show-hide-helper v-if="isViewMode" class="d-sm-none" :visible="isSectionVisible" @visibilityChange="onCollapse" variant="arrow" />

        <span class="ml-4 d-none d-sm-inline-block">
          <write-permission-wrapper variant="hidden">
            <b-btn
              v-if="isViewMode"
              variant="primary"
              size="sm"
              class="fixed-sizes"
              :disabled="!isEditAvailable || isEditSaveButtonDisabled || disableAddButton"
              @click="setAddPhotosMode()">
              <font-awesome-icon icon="plus-circle" size="sm" /> <span class="btn-title">Add Photos</span>
            </b-btn>

            <b-btn
              v-if="isViewMode"
              variant="secondary"
              size="sm"
              class="fixed-sizes ml-2"
              :disabled="!isEditAvailable || isEditSaveButtonDisabled || disableDeleteButton"
              @click="setDeletePhotosMode()">
              <font-awesome-icon icon="trash" size="sm" /> <span class="btn-title">Delete Photos</span>
            </b-btn>

            <b-btn
              v-if="isViewMode"
              variant="secondary"
              size="sm"
              class="fixed-sizes ml-2"
              :disabled="!isEditAvailable || isEditSaveButtonDisabled || disableRearrangeButton"
              @click="setRearrangePhotosMode()">
              <font-awesome-icon icon="pen" size="sm" /> <span class="btn-title">Rearrange Photos</span>
            </b-btn>

            <b-btn
              v-if="isRearrangeMode"
              variant="primary"
              size="sm"
              class="fixed-sizes"
              @click="saveVehicle()"
              :disabled="isEditSaveButtonDisabled">
              <font-awesome-icon icon="cloud-upload-alt" /> <span class="btn-title">Save & Close</span>
            </b-btn>

            <b-form-checkbox v-if="isDeleteMode" :checked="deleteAll" @change="onToggleDeleteAll">
              Select All
            </b-form-checkbox>

            <b-btn
              v-if="isDeleteMode"
              variant="primary"
              size="sm"
              class="fixed-sizes"
              @click="saveVehicle()"
              :disabled="isEditSaveButtonDisabled || !hasSelectedToDeletePhotos">
              <font-awesome-icon icon="trash" size="sm" /> <span class="btn-title">Delete Selected</span>
            </b-btn>

            <b-btn
              v-if="isDeleteMode"
              variant="secondary"
              size="sm"
              class="fixed-sizes"
              @click="setViewMode"
              :disabled="isEditSaveButtonDisabled">
              <font-awesome-icon icon="times-circle" size="sm" /> <span class="btn-title">Cancel</span>
            </b-btn>

            <b-btn
              v-if="isAddMode"
              variant="primary"
              size="sm"
              class="fixed-sizes"
              @click="saveAddedFiles()"
              :disabled="isEditSaveButtonDisabled">
              <font-awesome-icon icon="cloud-upload-alt" /> <span class="btn-title">Save & Close</span>
            </b-btn>
          </write-permission-wrapper>
        </span>
      </div>
    </header>
    <b-collapse :visible="isSectionVisible" :id="getId">
      <main class="w-100">
        <slot></slot>
      </main>
      <footer class="d-flex d-sm-none footer">
        <write-permission-wrapper variant="hidden">
          <b-btn
            v-if="isViewMode"
            variant="primary"
            class="footer__mobile-control"
            :disabled="!isEditAvailable || isEditSaveButtonDisabled || disableAddButton"
            @click="setAddPhotosMode()">
            <font-awesome-icon icon="plus-circle" size="sm" /> <span class="btn-title">Add Photos</span>
          </b-btn>

          <b-btn
            v-if="isViewMode"
            variant="secondary"
            class="footer__mobile-control"
            :disabled="!isEditAvailable || isEditSaveButtonDisabled || disableDeleteButton"
            @click="setDeletePhotosMode()">
            <font-awesome-icon icon="trash" size="sm" /> <span class="btn-title">Delete Photos</span>
          </b-btn>

          <b-btn
            v-if="isViewMode"
            variant="secondary"
            class="footer__mobile-control"
            :disabled="!isEditAvailable || isEditSaveButtonDisabled || disableRearrangeButton"
            @click="setRearrangePhotosMode()">
            <font-awesome-icon icon="pen" size="sm" /> <span class="btn-title">Rearrange Photos</span>
          </b-btn>

          <b-btn
            v-if="isRearrangeMode"
            variant="primary"
            class="footer__mobile-control"
            @click="saveVehicle()"
            :disabled="isEditSaveButtonDisabled">
            <font-awesome-icon icon="cloud-upload-alt" /> <span class="btn-title">Save & Close</span>
          </b-btn>
          <div class="footer__mobile-control group-control">
            <b-form-checkbox v-if="isDeleteMode" :checked="deleteAll" @change="onToggleDeleteAll">
              Select All
            </b-form-checkbox>

            <b-btn
              v-if="isDeleteMode"
              variant="primary"
              class="group-control__mobile-control"
              @click="saveVehicle()"
              :disabled="isEditSaveButtonDisabled || !hasSelectedToDeletePhotos">
              <font-awesome-icon icon="trash" size="sm" /> <span class="btn-title">Delete Selected</span>
            </b-btn>
          </div>

          <b-btn
            v-if="isDeleteMode"
            variant="secondary"
            class="footer__mobile-control"
            @click="setViewMode"
            :disabled="isEditSaveButtonDisabled">
            <font-awesome-icon icon="times-circle" size="sm" /> <span class="btn-title">Cancel</span>
          </b-btn>

          <b-btn
            v-if="isAddMode"
            variant="primary"
            class="footer__mobile-control"
            @click="saveAddedFiles()"
            :disabled="isEditSaveButtonDisabled">
            <font-awesome-icon icon="cloud-upload-alt" /> <span class="btn-title">Save & Close</span>
          </b-btn>
        </write-permission-wrapper>
      </footer>
    </b-collapse>
  </b-card-body>
</template>

<script>
import { mapGetters } from 'vuex'
import vehicleSaveMixin from '@/mixins/vehicle/vehicleSaveMixin'
import showHideHelper from '../../helpers/showHideHelper'
import writePermissionWrapper from '../../../_shared/writePermissionWrapper'

export default {
  name: 'details-photo-section',
  components: {
    'show-hide-helper': showHideHelper,
    'write-permission-wrapper': writePermissionWrapper
  },
  mixins: [vehicleSaveMixin],
  created () {
    this.$store.dispatch('editSectionController/registerSectionId', {
      onReset: () => {
        this.$emit('input', 'view')
        this.photoMode = 'view'
      },
      onEdit: () => {
        this.$emit('input', this.photoMode)
      }
    }).then(x => {
      this.id = x
    })
  },
  mounted () {
    window.addEventListener('resize', this.recalculateMobileMode)
    this.recalculateMobileMode()
  },
  destroyed () {
    window.removeEventListener('resize', this.recalculateMobileMode)
  },
  props: {
    title: String,
    deleteAll: Boolean,
    hasSelectedToDeletePhotos: Boolean,
    disableAddButton: Boolean,
    disableDeleteButton: Boolean,
    disableRearrangeButton: Boolean
  },
  data () {
    return {
      id: null,
      isMobile: false,
      isVisibleMobile: false,
      isVisible: true,
      isEditSaveButtonDisabled: false,
      isVisibleDisabled: false,
      photoMode: 'view'
    }
  },
  computed: {
    ...mapGetters('editSectionController', ['isEditAvailable', 'isEditMode']),
    ...mapGetters('details', ['vehicle', 'vehicleOriginal']),
    isViewMode () {
      return this.currentMode === 'view'
    },
    isRearrangeMode () {
      return this.photoMode === 'rearrange'
    },
    isDeleteMode () {
      return this.photoMode === 'delete'
    },
    isAddMode () {
      return this.photoMode === 'add'
    },
    getId () {
      return this.title.replace(/ /g, '_')
    },
    currentMode () {
      return this.isEditMode(this.id) ? 'edit' : 'view'
    },
    isSectionVisible () {
      if (this.isMobile) {
        return this.isVisibleMobile || !this.isViewMode
      }

      return this.isVisible || !this.isViewMode
    }
  },
  methods: {
    onCollapse (newVal) {
      this.isVisibleMobile = newVal
    },
    recalculateMobileMode () {
      this.isMobile = window.innerWidth < 576
    },
    setViewMode () {
      this.changeMode(false, 'view')
    },
    setAddPhotosMode () {
      this.changeMode(true, 'add')
    },
    setDeletePhotosMode () {
      this.changeMode(true, 'delete')
    },
    setRearrangePhotosMode () {
      this.changeMode(true, 'rearrange')
    },
    saveVehicle () {
      this.$emit('modifyPhotos')
    },
    saveAddedFiles () {
      this.$emit('saveAddedFiles')
    },
    changeMode (isEditMode, modeName) {
      this.photoMode = modeName

      this.$store.commit('editSectionController/setMode', {
        id: this.id,
        isEditMode: isEditMode
      })
    },
    disableSaveButton () {
      this.isEditSaveButtonDisabled = true
    },
    enableSaveButton () {
      this.isEditSaveButtonDisabled = false
    },
    disableVisibleButton () {
      this.isVisibleDisabled = true
    },
    enableVisibleButton () {
      this.isVisibleDisabled = false
    },
    onToggleDeleteAll (val) {
      this.$emit('deleteAll', val)
    }
  }
}
</script>

<style scoped lang="scss">
  header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding-bottom: 10px;
    margin-bottom: 5px;
    border-bottom: 1px solid #eee;

    transition: border-width 0.6s linear;

    .section-title {
      font-size: 0.894rem;
      font-weight: bold;
    }

    &.subSection {
      border-bottom: none;
      margin-bottom: 0;
      .section-title {
        font-weight: normal;
      }
    }

    .btn-title {
      margin-left: 3px;
    }
  }

  footer {
    margin-top: 1rem;
    .fixed-sizes {
      min-width: 66px;
      padding: 0.188rem 0.4875rem;
    }

    .footer-buttons {
      flex: 425
    }

    flex-wrap: wrap;

    .footer__mobile-control {
      width: 100%;
      margin-bottom: 1rem;
    }

    .group-control {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .group-control__mobile-control {
      width: 50%;
    }

  }
</style>
