<template>
  <div>
    <h4>Account Listing</h4>
    <b-card>
      <b-form @submit.prevent="applyFilter">
        <div class="form-row align-items-center">
          <b-col class="my-1" sm="6" lg="3" xl="3">
            <b-form-input
              v-model="filters.search"
              placeholder="Search..."
              autocomplete="off"
            >
            </b-form-input>
          </b-col>
          <b-col class="my-1" sm="6" lg="2" xl="2">
            <b-form-select
              v-model="filters.accountSetup"
              :options="accountSetupOptions"
            >
            </b-form-select>
          </b-col>
          <b-col class="py-2 d-flex flex-row" sm="6" lg="2" xl="2">
            <b-form-checkbox @change="synchronizeUrlAndReload" v-model="filters.excludeWithGoogleAnalytics4">Exclude With GA4</b-form-checkbox>
          </b-col>
          <b-col class="my-1" sm="6" lg="3" xl="3">
            <b-form-select
              v-model="filters.authMechanism"
              :options="analyticsAuthMechanismOptions"
            >
            </b-form-select>
          </b-col>
          <b-col class="my-1" sm="6" lg="2" xl="2">
            <b-btn block  variant="primary" type="submit">Submit</b-btn>
          </b-col>
        </div>
      </b-form>
    </b-card>
    <b-card v-if="!isLoading">
      <b-table
        :items="items"
        :fields="tableFields"
        :sort-by="tableSortBy"
        :sort-desc="tableSortDesc"
        @sort-changed="onSortChanged"
        :striped="true"
        :bordered="false"
        :no-sort-reset="true"
        :no-local-sorting="true"
        show-empty
        responsive
        class="products-table card-table"
      >
        <template #cell(accountId)="data">
          <router-link :to="{name: 'analyticsGa4Dashboard', params: { accountId: data.item.accountId }} ">{{data.item.accountId}}</router-link>
        </template>
        <template #cell(accountName)="data">
          <router-link :to="{name: 'analyticsGa4Dashboard', params: { accountId: data.item.accountId }} ">{{data.item.accountName}}</router-link>
        </template>
        <template #cell(hasGoogleAnalyticsUniversal)="data">
          <i class="ion ion-ios-checkmark text-success zoomeds" style="font-size:2rem" v-if="data.item.hasGoogleAnalyticsUniversal"></i>
          <i class="ion ion-ios-close text-danger zoomeds" style="font-size:2rem" v-else></i>
        </template>
        <template #cell(hasGoogleAnalytics4)="data">
          <i class="ion ion-ios-checkmark text-success zoomeds" style="font-size:2rem" v-if="data.item.hasGoogleAnalytics4"></i>
          <i class="ion ion-ios-close text-danger zoomeds" style="font-size:2rem" v-else></i>
        </template>
      </b-table>
      <paging
        class="p-0"
        :pageNumber="filters.page"
        :pageSize="filters.pageSize"
        :totalItems="totalItems"
        titled
        pageSizeSelector
        @numberChanged="onPageChanged"
        @changePageSize="onChangePageSize"
      />
    </b-card>
    <div v-else class="mt-3 pt-3">
      <loader size="lg"/>
    </div>
  </div>
</template>

<script>
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import paging from '@/components/_shared/paging'
import loader from '@/components/_shared/loader'
import analyticsConstants from './../../shared/analytics/constants'

const defaultValues = new ObjectSchema({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  search: { type: String, default: '' },
  authMechanism: { type: Number, default: null },
  accountSetup: { type: Number, default: null },
  excludeWithGoogleAnalytics4: { type: Boolean, default: false },
  sort: { type: Number, default: 0 }
})

const queryHelper = new QueryStringHelper(defaultValues)

export default {
  name: 'analytics-accounts',
  metaInfo: {
    title: 'Analytics Accounts'
  },
  data () {
    return {
      isLoading: true,
      items: [],
      totalItems: 0,
      filters: defaultValues.getObject()
    }
  },
  created () {
    this.filters = queryHelper.parseQueryStringToObject(this.$router)
    this.populateData()
  },
  components: {
    paging,
    loader
  },
  computed: {
    tableFields () {
      return [
        {
          key: 'accountId',
          label: 'Account Id',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: analyticsConstants.accountsListSortTypes.accountIdAsc,
          sortTypeDesc: analyticsConstants.accountsListSortTypes.accountIdDesc
        },
        {
          key: 'accountName',
          label: 'Account Name',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: analyticsConstants.accountsListSortTypes.accountNameAsc,
          sortTypeDesc: analyticsConstants.accountsListSortTypes.accountNameDesc
        },
        {
          key: 'accountSetup',
          label: 'Setup Status',
          tdClass: 'py-2 align-middle',
          formatter: val => {
            return (Object.values(analyticsConstants.analyticsAccountSetupTypes).find(x => x.value === val) || {text: '-'}).text
          }
        },
        {
          key: 'hasGoogleAnalyticsUniversal',
          label: 'Is GA Universal Enabled',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'hasGoogleAnalytics4',
          label: 'Is GA4 Enabled',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    tableSortBy () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sortType
      } else {
        return false
      }
    },
    accountSetupOptions () {
      return [{value: null, text: 'All Setup Statuses'}].concat(Object.values(analyticsConstants.analyticsAccountSetupTypes))
    },
    analyticsAuthMechanismOptions () {
      return [{value: null, text: 'All Accounts'}].concat(Object.values(analyticsConstants.analyticsAuthMechanismTypes))
    }
  },
  methods: {
    applyFilter () {
      this.filters.page = 1
      this.synchronizeUrlAndReload()
    },
    onPageChanged (newPage) {
      this.filters.page = newPage
      this.synchronizeUrlAndReload()
    },
    onSortChanged (value) {
      const sortingColumn = this.tableFields.find(x => x.key === value.sortBy)
      if (value.sortDesc) {
        this.filters.sort = sortingColumn.sortTypeDesc
      } else {
        this.filters.sort = sortingColumn.sortTypeAsc
      }
      this.synchronizeUrlAndReload()
    },
    onChangePageSize (newSize) {
      this.filters.pageSize = newSize
      this.filters.page = 1
      this.synchronizeUrlAndReload()
    },
    synchronizeUrlAndReload () {
      queryHelper.rebuildParamsInQueryString(this.$router, this.filters)
      this.populateData()
    },
    populateData () {
      this.$store.dispatch('analyticsGa4/getAccountsList', { filters: this.filters }).then(res => {
        this.items = res.data.model.items
        this.totalItems = res.data.model.totalItemsCount
      }).catch(ex => {
        this.$toaster.error('Something went wrong. Please try again later')
        this.$logger.handleError(ex, 'Cannot populate analytics account listing')
      }).finally(() => {
        this.isLoading = false
      })
    }
  }
}
</script>
