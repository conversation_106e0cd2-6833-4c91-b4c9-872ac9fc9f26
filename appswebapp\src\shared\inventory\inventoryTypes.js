const sortTypes = Object.freeze({
  yearAsc: 0,
  yearDesc: 1,
  makeAsc: 2,
  makeDesc: 3,
  stockAsc: 4,
  stockDesc: 5,
  photosCountAsc: 6,
  photosCountDesc: 7,
  inStockAsc: 8,
  inStockDesc: 9,
  lowPriceAsc: 10,
  lowPriceDesc: 11,
  highPriceAsc: 12,
  highPriceDesc: 13,
  yearDescMakeAsc: 14,
  yearAscMakeAsc: 15,
  keywordsAsc: 16,
  keywordsDesc: 17,
  galleryDescriptionAsc: 18,
  galleryDescriptionDesc: 19,
  homepageFeaturedAsc: 20,
  homepageFeaturedDesc: 21,
  promotionalFlagAsc: 22,
  promotionalFlagDesc: 23,
  accountIdAsc: 24,
  accountIdDesc: 25
})

const alertSortTypes = Object.freeze({
  stockAsc: 0,
  stockDesc: 1,
  photosCountAsc: 2,
  photosCountDesc: 3,
  videosCountAsc: 4,
  videosCountDesc: 5,
  inStockAsc: 6,
  inStockDesc: 7,
  lowPriceAsc: 8,
  lowPriceDesc: 9,
  highPriceAsc: 10,
  highPriceDesc: 11,
  yearDescMakeAsc: 12,
  yearAscMakeAsc: 13,
  totalAlertsAsc: 14,
  totalAlertsDesc: 15
})

const displayTypes = Object.freeze({
  default: 0,
  pricing: 1,
  merchandising: 2,
  settings: 3
})

const priceTypes = Object.freeze({
  all: 0,
  highPrice: 1,
  lowPrice: 2
})

const logTypes = Object.freeze({
  mobile: {value: 0, text: 'Mobile Logs'},
  desktop: {value: 1, text: 'Desktop Logs'},
  settings: {value: 2, text: 'Settings Logs'},
  vehiclePostProcessing: {value: 3, text: 'Vehicle Post Processing Logs'},
  vehicleHistoryReport: {value: 4, text: 'Vehicle History Report Logs'}
})

const vehicleStateSortTypes = Object.freeze({
  vinAsc: 1,
  vinDesc: 2,
  lastProcessedAsc: 3,
  lastProcessedDesc: 4,
  historyReportCheckedAsc: 5,
  historyReportCheckedDesc: 6
})

const vehicleModificationSourceTypes = Object.freeze({
  undefined: { value: 0, text: 'Undefined' },
  webApp: { value: 1, text: 'from Apps main web application' },
  iOSApp: { value: 2, text: 'from mobile (iOS)' },
  androidApp: { value: 3, text: 'from mobile (Android)' },
  vehicleSynchronizer: { value: 4, text: '' },
  historyReportChecker: { value: 5, text: '' }
})

const vehicleProcessingStepStatusTypes = Object.freeze({
  undefined: { value: 0, text: 'Undefined' },
  success: { value: 1, text: 'Success' },
  failed: { value: 2, text: 'Failed' },
  notNeeded: { value: 3, text: 'Not needed' },
  skipped: { value: 4, text: 'Skipped' },
  carfaxRequested: { value: 5, text: 'Carfax requested' },
  autocheckRequested: { value: 6, text: 'Autocheck requested' },
  notExist: { value: 7, text: 'Not exist' },
  validForGallery: { value: 8, text: 'Valid for gallery' },
  validForApps: { value: 9, text: 'Valid for apps' },
  notValid: { value: 10, text: 'Not valid' }
})

const carfaxIntegrationType = [
  { value: 0, text: 'Not Active' },
  { value: 1, text: 'Standard' },
  { value: 2, text: 'Advanced' }
]

const carfaxAutoPurchaseReports = [
  { value: 0, text: 'Do Not Auto Purchase Reports' },
  { value: 1, text: 'Live & In Progress only' },
  { value: 2, text: 'Live Vehicles Only' },
  { value: 3, text: 'In Progress Vehicles Only' }
]

const priceAlertType = [
  { value: 2, text: 'Do Not Include In Tasks' },
  { value: 0, text: 'Has At Least One Price' },
  { value: 1, text: 'Has High And Low Price' }
]

const integrityReportTypes = Object.freeze({
  vehiclesLostInSync: { key: 0, title: 'Vehicles Lost in Synchronization' },
  carfaxReport: { key: 1, title: 'Carfax Report' }
})

const vehicleLostInSyncSortTypes = Object.freeze({
  accountIdAsc: 1,
  accountIdDesc: 2,
  siteIdAsc: 3,
  siteIdDesc: 4,
  vinAsc: 5,
  vinDesc: 6,
  siteBoxAsc: 7,
  siteBoxDesc: 8,
  reasonAsc: 9,
  reasonDesc: 10
})

const vehicleLostInSyncReasonTypes = Object.freeze({
  vehicleNotPresentInApps: { value: 0, text: 'Vehicle is not present in Apps' },
  vehicleNotRelatedToSiteBox: { value: 1, text: 'Vehicle is not related to SiteBox' }
})

const vehiclePostProcessingModificationTypes = Object.freeze({
  created: { value: 1, text: 'Create Vehicle' },
  updated: { value: 2, text: 'Update Vehicle' },
  deleted: { value: 3, text: 'Delete Vehicle' },
  updatedVehicleHistoryReport: { value: 4, text: 'Update Vehicle History Report' }
})

const vehicleHistoryReportTypes = Object.freeze({
  none: {value: 0, text: 'None'},
  carfax: {value: 1, text: 'Carfax'},
  autoCheck: {value: 2, text: 'AutoCheck'}
})

const carfaxApiCallTypes = Object.freeze({
  undefined: {value: 0, text: 'All Api Call Types'},
  icr: {value: 1, text: 'ICR'},
  dealer: {value: 2, text: 'Dealer'}
})

const carfaxIntegrityReportSortTypes = Object.freeze({
  isAppsAccountWithCarfaxAsc: 1,
  isAppsAccountWithCarfaxDesc: 2,
  isAccountIncludedInCarfaxExportAsc: 3,
  isAccountIncludedInCarfaxExportDesc: 4,
  createdDateTimeAsc: 5,
  createdDateTimeDesc: 6,
  updatedDateTimeAsc: 7,
  updatedDateTimeDesc: 8,
  isDemoAccountAsc: 9,
  isDemoAccountDesc: 10,
  accountIdAsc: 11,
  accountIdDesc: 12
})

const accountListingSortTypes = Object.freeze({
  undefined: 0,
  accountIdAsc: 1,
  accountIdDesc: 2,
  accountNameAsc: 3,
  accountNameDesc: 4
})

const photoSizeTypes = Object.freeze({
  photo1024: {value: 1024, text: '1024'},
  photo1600: {value: 1600, text: '1600'},
  photo1920: {value: 1920, text: '1920'},
  photo2048: {value: 2048, text: '2048'}
})

const vehicleSearchTypes = Object.freeze({
  categorizedSearch: {value: 0, text: 'Categorized Search'},
  mongoDbAtlasSearch: {value: 1, text: 'MongoDB Atlas Search'}
})

export {
  sortTypes,
  displayTypes,
  priceTypes,
  carfaxIntegrationType,
  carfaxAutoPurchaseReports,
  priceAlertType,
  alertSortTypes,
  logTypes,
  vehicleStateSortTypes,
  vehicleModificationSourceTypes,
  vehicleProcessingStepStatusTypes,
  integrityReportTypes,
  vehicleLostInSyncSortTypes,
  vehicleLostInSyncReasonTypes,
  vehiclePostProcessingModificationTypes,
  vehicleHistoryReportTypes,
  carfaxIntegrityReportSortTypes,
  carfaxApiCallTypes,
  accountListingSortTypes,
  photoSizeTypes,
  vehicleSearchTypes
}
