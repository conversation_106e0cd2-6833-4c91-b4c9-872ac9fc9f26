<template>
  <editSettingsHelper @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="Music Settings" :isLoading="isUpdatingProcessed" :isViewMode="isViewMode" :readOnlyMode="readOnlyMode">
    <template slot="settings-content">
      <detail-row :fixed-payload-width="true" :title-position="'start'">
        <span slot="title">Choose Music:</span>
        <div slot="payload">
          <b-form-radio-group stacked name="autovideo-music-option" v-model="musicSelectedOption" :options="getMusicSelectOptions" :disabled="isViewMode"></b-form-radio-group>
        </div>
      </detail-row>
      <b-overlay :show="musicSelectedOption !== musicSelectOptions.genresAndTracks.value" opacity="0.7" blur="1.2px">
        <template #overlay>
          <span></span>
        </template>
        <detail-row :fixed-payload-width="true" :title-position="'start'">
          <span slot="title">Genres And Tracks:</span>
          <div slot="payload" class="d-flex flex-column w-100">
            <div v-for="(genreOrTrack, genreType) in genresAndTracksOptions" :key="genreType">
              <span style="cursor: pointer;" @click="genreOrTrack.toggle = !genreOrTrack.toggle">
                <b-icon v-if="genreOrTrack.toggle" scale="1.2" variant="primary" icon="arrow-up-square-fill">
                </b-icon>
                <b-icon v-else scale="1.2" variant="primary" icon="arrow-down-square-fill">
                </b-icon>
                {{ getGenreTypeDesc(genreOrTrack.genreType) }}
              </span>
              <b-collapse :visible="genreOrTrack.toggle" class="pl-2">
                <div class="d-flex flex-column mt-2">
                  <b-form-radio
                    name="autovideo-track-radio"
                    @click="onRandomGenreClick()"
                    class="mb-1"
                    v-model="updatedSettings.genreId"
                    :value="genreOrTrack.genreType"
                    :disabled="isViewMode">
                      Randomly
                  </b-form-radio>
                  <div v-for="track in genreOrTrack.tracks" :key="track.value" class="mb-1 d-flex flex-row">
                    <b-form-radio
                      name="autovideo-track-radio"
                      @click="onSpecificTrackClick()"
                      class="mr-1"
                      v-model="updatedSettings.soundtrackId"
                      :value="track.value"
                      :disabled="isViewMode">
                        {{ track.text }}
                    </b-form-radio>
                    <audioPlayer :loadSourceOnPlay="true" :loadSourceFunc="gedLoadTrackSourceFunc(track.value)" type="mp3"/>
                  </div>
                </div>
              </b-collapse>
            </div>
          </div>
        </detail-row>
      </b-overlay>
      <b-overlay :show="musicSelectedOption !== musicSelectOptions.custom.value" opacity="0.7" blur="1.2px">
        <template #overlay>
          <span></span>
        </template>
        <detail-row :fixed-payload-width="true" :title-position="'start'">
          <span slot="title">Custom Soundtrack:</span>
          <div slot="payload">
            <div class="d-flex flex-row">
              <span class="float-left mr-2">Soundtrack:</span>
              <audioPlayer :loadSourceOnPlay="true" :loadSourceFunc="gedLoadCustomSoundtrackSourceFunc()"  type="mp3"/>
            </div>
            <div class="mt-2" v-if="!isViewMode">
              <b-input-group>
                <b-form-file class="ebiz-custom-file-selector" placeholder="Select your soundtrack" v-model="customSoundtrackFile" accept=".mp3"/>
                <b-input-group-append>
                  <l-button @click="uploadSoundtrack" :loading="isUploading" :disabled="!customSoundtrackFile" variant="primary">Upload</l-button>
                </b-input-group-append>
              </b-input-group>
            </div>
          </div>
        </detail-row>
      </b-overlay>
    </template>
  </editSettingsHelper>
</template>

<script>
import editSettingsHelper from '@/components/_shared/editSettingsHelper'
import editSettingsMixin from './editSettingsMixin'
import detailRow from '@/components/details/helpers/detailRow'
import globals from '@/globals'
import VideoEncoderService from '../../../services/inventory/VideoEncoderService'
import videoEncoderTypes from '../../../shared/inventory/videoEncoderTypes'
import audioPlayer from '../../_shared/audioPlayer.vue'

const musicSelectOptions = {
  genresAndTracks: { value: 0, text: 'Select from featured Genres and Tracks' },
  custom: { value: 1, text: 'Custom Soundtrack' },
  random: { value: 2, text: 'Randomly' },
  none: { value: 3, text: 'Do not play music track' }
}

export default {
  name: 'inventory-account-autovideo-music-settings-section',
  props: {
    settings: {
      type: Object,
      required: true
    },
    readOnlyMode: {
      type: Boolean
    }
  },
  data () {
    return {
      accountId: +this.$route.params.accountId,
      musicSelectOptions,
      updatedSettings: globals().getClonedValue(this.settings),
      genresAndTracks: [],
      isViewMode: true,
      isUpdatingProcessed: false,
      genresAndTracksOptions: {},
      customSoundtrackFile: null,
      isUploading: false
    }
  },
  mixins: [editSettingsMixin],
  components: {
    editSettingsHelper,
    detailRow,
    audioPlayer
  },
  created () {
    this.initGenresAndTracks()
    this.$watch('updatedSettings', function () {
      this.buildGenresAndTracksOptions()
    })
  },
  computed: {
    musicSelectedOption: {
      get () {
        if (this.updatedSettings.audioTrackType === videoEncoderTypes.musicTypes.random) {
          return musicSelectOptions.random.value
        }
        if (this.updatedSettings.audioTrackType === videoEncoderTypes.musicTypes.none) {
          return musicSelectOptions.none.value
        }
        if ([videoEncoderTypes.musicTypes.genreSpecific, videoEncoderTypes.musicTypes.genreRandom].includes(this.updatedSettings.audioTrackType)) {
          return musicSelectOptions.genresAndTracks.value
        }
        return musicSelectOptions.custom.value
      },
      set (value) {
        if (value === musicSelectOptions.random.value) {
          this.updatedSettings.audioTrackType = videoEncoderTypes.musicTypes.random
          return
        }
        if (value === musicSelectOptions.none.value) {
          this.updatedSettings.audioTrackType = videoEncoderTypes.musicTypes.none
          return
        }
        if (value === musicSelectOptions.genresAndTracks.value) {
          this.updatedSettings.audioTrackType = this.updatedSettings.soundtrackId > 0 ? videoEncoderTypes.musicTypes.genreSpecific : videoEncoderTypes.musicTypes.genreRandom
          return
        }
        this.updatedSettings.audioTrackType = videoEncoderTypes.musicTypes.custom
      }
    },
    getMusicSelectOptions () {
      return Object.values(musicSelectOptions)
    }
  },
  methods: {
    updateSettings () {
      this.isUpdatingProcessed = true
      VideoEncoderService.updateAccountPhotoToVideoMusicSettings(this.accountId, this.updatedSettings).then(res => {
        this.$toaster.success('Updated Music Settings Successfully')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Failed on Updating Music Settings', {timeout: 5000})
      }).finally(() => {
        this.isUpdatingProcessed = false
        this.isViewMode = true
        this.$emit('refresh')
      })
    },
    getGenreTypeDesc (type) {
      return (Object.values(videoEncoderTypes.genreTypes).find(x => x.value === type) || {text: '-'}).text
    },
    initGenresAndTracks () {
      VideoEncoderService.getTracks().then(res => {
        this.genresAndTracks = (res.data || { items: [] }).items
        this.buildGenresAndTracksOptions()
      }).catch(ex => {
        console.error(ex)
      })
    },
    buildGenresAndTracksOptions () {
      let options = {}
      this.genresAndTracks.forEach(x => {
        if (!options[x.genreType]) {
          options[x.genreType] = {}
          options[x.genreType].tracks = []
          options[x.genreType].genreType = x.genreType
        }
        options[x.genreType].tracks.push({
          value: x.id,
          text: x.name
        })
      })
      Object.values(options).forEach(opt => {
        opt.toggle = this.updatedSettings.genreId === opt.genreType || opt.tracks.some(x => x.value === this.updatedSettings.soundtrackId)
      })
      this.genresAndTracksOptions = options
    },
    onSpecificTrackClick () {
      this.$set(this.updatedSettings, 'genreId', 0)
      this.$set(this.updatedSettings, 'audioTrackType', videoEncoderTypes.musicTypes.genreSpecific)
    },
    onRandomGenreClick () {
      this.$set(this.updatedSettings, 'soundtrackId', 0)
      this.$set(this.updatedSettings, 'audioTrackType', videoEncoderTypes.musicTypes.genreRandom)
    },
    gedLoadTrackSourceFunc (trackId) {
      return async () => {
        let url = ''
        try {
          let response = await VideoEncoderService.downloadTrack(trackId)
          if (response.status === 200) {
            let blob = this.$locale.b64toBlob(response.data.base64String, 'audio/mp3')
            url = URL.createObjectURL(blob)
          } else {
            console.log(response)
            this.$toaster.error('Failed on loading track')
          }
        } catch (ex) {
          console.error(ex)
          this.$toaster.error('Failed on loading track')
        }

        return url
      }
    },
    gedLoadCustomSoundtrackSourceFunc () {
      return async () => {
        let url = ''
        try {
          let response = await VideoEncoderService.downloadAccountSoundtrack(this.accountId)
          if (response.status === 200) {
            let blob = this.$locale.b64toBlob(response.data.base64String, 'audio/mp3')
            url = URL.createObjectURL(blob)
          } else {
            console.log(response)
            this.$toaster.error('Failed on loading custom soundtrack')
          }
        } catch (ex) {
          console.error(ex)
          this.$toaster.error('Failed on loading custom soundtrack')
        }

        return url
      }
    },
    uploadSoundtrack () {
      this.isUploading = true
      let formData = new FormData()
      formData.append('file', this.customSoundtrackFile)
      VideoEncoderService.uploadAccountSoundtrack(this.accountId, formData).then(res => {
        this.$toaster.success('Soundtrack Uploaded Successfully', {timeout: 5000})
      }).catch(ex => {
        console.error(ex)
        this.$toaster.error('Failed on uploading soundtrack', {timeout: 4000})
      }).finally(() => {
        this.isUploading = false
      })
    }
  }
}
</script>

<style>
.ebiz-custom-file-selector .custom-file-label::after {
  display: none;
}
</style>
