<template>
  <div>
    <h4>Account Settings</h4>
    <b-card v-if='isReady'>
      <section-contact-settings
        @putSettingsData='putContactSettingsData'
        :isUpdatingProcessing="sections.contact.isUpdatingProcessing"
        :isDisabled='sections.contact.isDisabled'
      />
      <section-post-title-settings
        @putSettingsData='putPostTitleSettingsData'
        :isUpdatingProcessing="sections.postTitle.isUpdatingProcessing"
        :isDisabled='sections.postTitle.isDisabled'
      />
      <section-other-settings
        @putSettingsData='putOtherSettingsData'
        :isUpdatingProcessing="sections.other.isUpdatingProcessing"
        :isDisabled='sections.other.isDisabled'
      />

      <section-posting-limits-settings v-if='hasPermissions'
        :isDisabled='sections.postingLimits.isDisabled'
      />

      <section-admin-settings v-if='hasPermissions'
        @putSettingsData='putAdminSettingsData'
        :isUpdatingProcessing="sections.admin.isUpdatingProcessing"
        :isDisabled='sections.admin.isDisabled'/>
    </b-card>
  </div>
</template>

<script>
import sectionAdminSettings from '@/components/craigslist/settings/sectionAdminSettings'
import sectionContactSettings from '@/components/craigslist/settings/sectionContactSettings'
import sectionPostTitleSettings from '@/components/craigslist/settings/sectionPostTitleSettings'
import sectionOtherSettings from '@/components/craigslist/settings/sectionOtherSettings'
import sectionPostingLimitsSettings from '@/components/craigslist/settings/sectionPostLimitsSettings'
import { mapGetters } from 'vuex'
import permissions from '@/shared/common/permissions'

export default {
  name: 'craigslist-settings-for-account',
  metaInfo: {
    title: 'Craigslist Settings'
  },
  props: {
    accountId: {
      type: Number,
      required: true
    }
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user
    },
    hasPermissions () {
      if (this.user) {
        return this.user.hasPermissions(permissions.EbizAutosAdmin)
      }
      return false
    }
  },
  components: {
    'section-admin-settings': sectionAdminSettings,
    'section-contact-settings': sectionContactSettings,
    'section-post-title-settings': sectionPostTitleSettings,
    'section-other-settings': sectionOtherSettings,
    'section-posting-limits-settings': sectionPostingLimitsSettings
  },
  created () {
    if (this.accountId) {
      this.populateData().then(() => { this.isReady = true })
    }
  },
  data () {
    return {
      isReady: false,
      isDisabled: false,
      sections: {
        contact: {
          isDisabled: false,
          isUpdatingProcessing: false
        },
        postTitle: {
          isDisabled: false,
          isUpdatingProcessing: false
        },
        other: {
          isDisabled: false,
          isUpdatingProcessing: false
        },
        postingLimits: {
          isDisabled: false,
          isUpdatingProcessing: false
        },
        admin: {
          isDisabled: false,
          isUpdatingProcessing: false
        }
      }
    }
  },
  methods: {
    async populateData () {
      await this.$store.dispatch('craigslistSettings/populateAccountSettings', { accountId: this.accountId })
      await this.$store.dispatch('craigslistSettings/populateCraigslistAreas', { accountId: this.accountId })
    },
    refresh () {
      this.isReady = false
      this.populateData().then(() => { this.isReady = true })
    },
    putContactSettingsData (putSettingsData) {
      this.sections.contact.isUpdatingProcessing = true
      this.makeOtherSectionsDisabled('contact')
      this.putSettingsData(putSettingsData)
    },
    putPostTitleSettingsData (putSettingsData) {
      this.sections.postTitle.isUpdatingProcessing = true
      this.makeOtherSectionsDisabled('postTitle')
      this.putSettingsData(putSettingsData)
    },
    putOtherSettingsData (putSettingsData) {
      this.sections.other.isUpdatingProcessing = true
      this.makeOtherSectionsDisabled('other')
      this.putSettingsData(putSettingsData)
    },
    putAdminSettingsData (putSettingsData) {
      this.sections.admin.isUpdatingProcessing = true
      this.makeOtherSectionsDisabled('admin')
      this.putSettingsData(putSettingsData)
    },
    async putSettingsData (putSettingsData) {
      await this.$store.dispatch('craigslistSettings/putSettingsData', { accountId: this.accountId, data: putSettingsData }).then(x => {
        this.$toaster.success('Craigslist Account Settings Successfully Updated')
      })
        .catch(ex => {
          this.$toaster.exception(ex, `Can't update settings for accountId:${this.accountId}`)
          this.$logger.handleError(ex, `Can't update settings for accountId:${this.accountId}`)
        })
        .finally(() => {
          this.refresh()
          this.setSectionsToDefault()
        })
    },
    makeOtherSectionsDisabled (excludeSectionKey) {
      Object.keys(this.sections).filter(key => key !== excludeSectionKey).forEach(key => {
        this.sections[key].isDisabled = true
      })
    },
    setSectionsToDefault () {
      Object.values(this.sections).forEach(section => {
        section.isDisabled = false
        section.isUpdatingProcessing = false
      })
    }
  }
}
</script>
