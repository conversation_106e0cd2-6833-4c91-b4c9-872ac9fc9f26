<template>
  <div class="position-relative">
    <h4> Craigslist Posts - {{accountName}} ({{accountId}}) </h4>
    <paging
      class="custom-posts-paging d-none d-md-block"
      :pageNumber="filter.page"
      :pageSize="filter.pageSize"
      :totalItems="itemsTotalCount"
      @numberChanged="onPageNumberChanged"
      @changePageSize="onPageSizeChanged"
    />
    <b-tabs class="nav-tabs-top nav-responsive-sm" no-fade>
      <b-tab @click="changedStatus(1)" title="Active">
        <posts-filters @filterChanged="applyFilters" :filter='filter'/>
      </b-tab>
      <b-tab @click="changedStatus(2)" title="Inactive">
        <posts-filters @filterChanged="applyFilters" :filter='filter'/>
      </b-tab>
    </b-tabs>
    <posts-listing
      :tableItems="items"
      :totalItems="itemsTotalCount"
      :pageNumber.sync="filter.page"
      :pageSize.sync="filter.pageSize"
      :sortType.sync="filter.sort"
      :status="status"
      :isPostingAllowed="isPostingAllowed"
      :isLoaded='isLoaded'
      @refreshData="synchronizeUrlAndReload"
    />
  </div>
</template>

<script>
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import lodash from 'lodash'

const defaultValues = new ObjectSchema({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  search: { type: String, default: '' },
  area: { type: String, default: '' },
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  sort: { type: Number, default: 8 }
})

const queryHelper = new QueryStringHelper(defaultValues)

export default {
  name: 'craigslist-posts-for-account',
  metaInfo: {
    title: 'Craigslist Posts'
  },
  props: {
    accountId: {
      type: Number,
      required: true
    }
  },
  components: {
    'paging': () => import('@/components/_shared/paging'),
    'posts-filters': () => import('@/components/craigslist/posts/postsFilters'),
    'posts-listing': () => import('@/components/craigslist/posts/postsListing')
  },
  data () {
    return {
      accountName: '',
      items: [],
      itemsTotalCount: 0,
      filter: defaultValues.getObject(),
      status: 1,
      isPostingAllowed: false,
      isLoaded: false
    }
  },
  mounted () {
    this.filter = queryHelper.parseQueryStringToObject(this.$router)
    if (this.accountId) {
      this.loadContent()
    } else {
      this.$toaster.error('AccountId cannot be empty!!!')
    }
  },
  methods: {
    loadContent () {
      this.isLoaded = false
      if (this.status === 1) {
        this.$store.dispatch('craigslist/getAccountsActive', { accountId: this.accountId, filter: this.filter }).then(response => {
          if (response.data.model) {
            this.items = response.data.model.craigslistHistoryEntries
            this.accountName = response.data.model.accountName
            this.itemsTotalCount = response.data.model.pageInfo.totalEntries
            this.isPostingAllowed = response.data.model.isPostingAllowed
          } else {
            this.$toaster.error('Something went wrong! Please reload page')
            this.$logger.handleError(new Error('Response is invalid'), 'Can\'t get accounts active posts', response)
          }
        })
          .catch(ex => {
            this.$toaster.error('Can\'t get accounts active posts')
            this.$logger.handleError(ex, 'Can\'t get accounts active posts')
          })
          .finally(() => {
            this.isLoaded = true
          })
      } else {
        this.$store.dispatch('craigslist/getAccountsInactive', { accountId: this.accountId, filter: this.filter }).then(response => {
          if (response.data.model) {
            this.items = response.data.model.craigslistHistoryEntries
            this.accountName = response.data.model.accountName
            this.itemsTotalCount = response.data.model.pageInfo.totalEntries
            this.isPostingAllowed = response.data.model.isPostingAllowed
          } else {
            this.$toaster.error('Something went wrong! Please reload page')
            this.$logger.handleError(new Error('Response is invalid'), 'Can\'t get accounts inactive posts', response)
          }
        })
          .catch(ex => {
            this.$toaster.error('Can\'t get accounts inactive posts')
            this.$logger.handleError(ex, 'Can\'t get accounts inactive posts')
          })
          .finally(() => {
            this.isLoaded = true
          })
      }
    },
    changedStatus (val) {
      this.status = val
      this.filter.page = 1
      this.synchronizeUrlAndReload()
    },
    synchronizeUrlAndReload () {
      queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.loadContent()
    },
    applyFilters (newFilter) {
      this.filter.page = 1
      lodash.assign(this.filter, newFilter)
      this.synchronizeUrlAndReload()
    },
    onPageNumberChanged (newPage) {
      this.filter.page = newPage
      this.synchronizeUrlAndReload()
    },
    onPageSizeChanged (newSize) {
      this.filter.pageSize = newSize
      this.synchronizeUrlAndReload()
    }
  }
}
</script>

<style lang="scss">
.custom-posts-paging {
position: absolute;
right: -10px;
top: 30px;
z-index: 2;
}
</style>
