<template>
  <editSettingsHelper @cancel="cancel" id="deposit-settings" @save="saveSettings" @changeMode="changeMode" title="Deposit Settings" :isLoading="isUpdatingProcessed" :isViewMode="isViewMode">
    <template slot="settings-content">
      <detail-row titlePosition="start" :large-payload-width="true">
        <span slot="title">Require a Deposit:</span>
        <b-form-group
          slot="payload"
        >
          <b-form-select v-if="!isViewMode" v-model="settingsToUpdate.IsRequiredDeposit" :options="getYesNoOptions"></b-form-select>
          <span v-else>{{settings.IsRequiredDeposit ? 'Yes' : 'No'}}</span>
          <b-form-text class="text-dark custom-text-nowrap">
           Deposits are paid via PayPal (Business or Premier PayPal account required)
          </b-form-text>
        </b-form-group>
      </detail-row>
      <detail-row v-if="settingsToUpdate.IsRequiredDeposit" :large-payload-width="true">
        <span slot="title"></span>
        <b-form-group
          slot="payload"
        >
          <b-form-checkbox v-model="settingsToUpdate.IsRequiredImmediateDeposit" class="custom-text-nowrap" :disabled="isViewMode">Require Immediate Deposit (max of $500) for Buy It Now Items.</b-form-checkbox>
          <b-form-text class="text-dark custom-text-nowrap">
            Using Immediate Deposit will set your Sell To Locations to United States Only.
          </b-form-text>
        </b-form-group>
      </detail-row>
      <ValidationProvider name="Deposit Amount" rules="min_value:0" v-slot="{errors}">
      <detail-row v-if="settingsToUpdate.IsRequiredDeposit" :large-payload-width="true" :error="errors[0]">
        <span slot="title">Deposit Amount:</span>
        <b-form-input slot="payload" name="Deposit_Amount" v-if="!isViewMode" v-model="settingsToUpdate.DepositAmount" type="number"></b-form-input>
        <span slot="payload" v-else>{{settings.DepositAmount || '-'}}</span>
      </detail-row>
      </ValidationProvider>
      <detail-row v-if="settingsToUpdate.IsRequiredDeposit" :large-payload-width="true">
        <span slot="title">Deposit Required Within:</span>
        <div class="d-flex flex-row w-100" slot="payload">
          <b-form-select v-if="!isViewMode" v-model="settingsToUpdate.HoursToDeposit" :options="getHourOptions" class="mr-2"></b-form-select>
          <span v-else>{{settings.HoursToDeposit || '-'}} hours</span>
          <span class="align-self-center custom-text-nowrap ml-1">of auction / listing close</span>
        </div>
      </detail-row>
      <detail-row v-if="settingsToUpdate.IsRequiredDeposit" :large-payload-width="true">
        <span slot="title">Change Deposit Per Listing:</span>
        <b-form-select v-if="!isViewMode" slot="payload" v-model="settingsToUpdate.IsAllowedToChangeDepositPerListing" :options="getAllowNotAllowOptions"></b-form-select>
        <span slot="payload" v-else>{{settings.IsAllowedToChangeDepositPerListing ? 'Allow' : 'Do Not Allow'}}</span>
      </detail-row>
    </template>
  </editSettingsHelper>
</template>

<script>
import editSettingsHelper from '../../_shared/editSettingsHelper.vue'
import detailRow from '@/components/details/helpers/detailRow'
import ebayOptions from '@/shared/ebay/ebayOptions'
import globals from '../../../globals'
import editSettingsMixin from '@/mixins/eBay/editSettingsMixin'

export default {
  props: {
    settings: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      isUpdatingProcessed: false,
      isViewMode: true,
      settingsToUpdate: globals().getClonedValue(this.settings)
    }
  },
  mounted () {
    // set focus on Deposit Settings Section
    this.$nextTick(() => {
      if (this.$route.hash && this.$route.hash === '#deposit-settings') {
        document.querySelector(this.$route.hash).scrollIntoView()
      }
    })
  },
  components: {
    editSettingsHelper,
    detailRow
  },
  mixins: [editSettingsMixin],
  computed: {
    getYesNoOptions () {
      return ebayOptions.yesNoOptions
    },
    getAllowNotAllowOptions () {
      return ebayOptions.allowNotAllowOptions
    },
    getHourOptions () {
      return ebayOptions.depositHoursOptions
    }
  },
  methods: {
    updateSettings () {
      this.isUpdatingProcessed = true
      this.$store.dispatch('eBay/updateDepositSettings', { accountId: this.$route.params.accountId, data: this.settingsToUpdate }).then(res => {
        this.$toaster.success('Deposit Settings Updated Successfully')
      }).catch(ex => {
        this.$toaster.error('Something went wrong! Please try again later')
        this.$logger.handleError(ex, 'Cannot update eBay account deposit setting')
      }).finally(() => {
        this.isUpdatingProcessed = false
        this.isViewMode = true
        this.$emit('refresh')
      })
    }
  }
}
</script>

<style scoped>
@media (min-width: 1320px) {
  .custom-text-nowrap {
    white-space: nowrap;
  }
}
.custom-input-width {
  width: 5rem;
}
</style>
