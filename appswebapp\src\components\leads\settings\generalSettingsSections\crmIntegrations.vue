<template>
  <div class='mb-4'>
    <editSettingsHelper @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="CRM Integrations" :isLoading="isUpdatingProcessed" :isDisabled="isDisabled" :isViewMode="isViewMode">
      <div slot="settings-content">
        <detail-row fixedPayloadWidth>
          <span slot="title">ADF Lead Source:</span>
          <b-form-input type="text" v-model='adminAccountSettingsToUpdate.adfLeadSource' slot="payload" :disabled='isViewMode'/>
        </detail-row>
        <detail-row fixedPayloadWidth>
          <span slot="title">DealerSocket APi ID:</span>
          <b-form-input type="text" v-model='adminAccountSettingsToUpdate.dealershipId' slot="payload" :disabled='isViewMode'/>
        </detail-row>
        <detail-row fixedPayloadWidth>
          <span slot="title">Shift Digital APi ID:</span>
          <b-form-input type="text" v-model='adminAccountSettingsToUpdate.shiftDigitalId' slot="payload" :disabled='isViewMode'/>
        </detail-row>
        <detail-row fixedPayloadWidth>
          <span slot="title">Shift Digital Program</span>
          <b-form-select type="text" v-model='adminAccountSettingsToUpdate.shiftDigitalType' :options='shiftDigitalTypes' slot="payload" :disabled='isViewMode'/>
        </detail-row>
        <detail-row fixedPayloadWidth>
          <span slot="title">Shift Digital Post URL:</span>
          <b-form-input type="text" v-model='adminAccountSettingsToUpdate.shiftDigitalApiUrl' slot="payload" :disabled='isViewMode'/>
        </detail-row>
        <detail-row fixedPayloadWidth>
          <span slot="title">Premier Truck DB Name:</span>
          <b-form-input type="text" v-model='adminAccountSettingsToUpdate.premierTruckDbName' slot="payload" :disabled='isViewMode'/>
        </detail-row>
        <detail-row fixedPayloadWidth>
          <span slot="title">Premier Truck Post URL:</span>
          <b-form-input type="text" v-model='adminAccountSettingsToUpdate.premierTruckApiUrl' slot="payload" :disabled='isViewMode'/>
        </detail-row>
        <detail-row fixedPayloadWidth>
          <span slot="title">Show Provider ID:</span>
          <b-form-checkbox slot="payload" v-model='adminAccountSettingsToUpdate.hasToShowProviderIdInADF' :disabled='isViewMode'/>
        </detail-row>
        <detail-row fixedPayloadWidth>
          <span slot="title">Add &lt;LeadType&gt; Service &lt;/LeadType&gt; to ADF email (VinSolutions):</span>
          <b-form-checkbox slot="payload" v-model='adminAccountSettingsToUpdate.hasToAppendLeadTypeToADF' :disabled='isViewMode'/>
        </detail-row>
        <detail-row fixedPayloadWidth>
          <span slot="title">Show ADF eBay Source:</span>
          <b-form-checkbox slot="payload" v-model='adminAccountSettingsToUpdate.hasToShowEBaySourceInADF' :disabled='isViewMode'/>
        </detail-row>
      </div>
    </editSettingsHelper>
  </div>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import { shiftDigitalTypes } from '@/shared/leads/leadsAccountSettings'
import { mapGetters } from 'vuex'
import globals from '../../../../globals'

export default {
  name: 'leads-crm-settings',
  props: {
    isUpdatingProcessed: {
      type: Boolean,
      required: true
    },
    isDisabled: Boolean
  },
  data () {
    return {
      shiftDigitalTypes,
      isViewMode: true,
      adminAccountSettingsToUpdate: {}
    }
  },
  created () {
    this.initData()
  },
  components: {
    'detail-row': detailRow,
    editSettingsHelper: () => import('@/components/_shared/editSettingsHelper')
  },
  computed: {
    ...mapGetters('leadsAccountSettings', ['adminAccountSettings'])
  },
  methods: {
    initData () {
      if (this.adminAccountSettings) {
        this.adminAccountSettingsToUpdate = globals().getClonedValue(this.adminAccountSettings)
      }
    },
    saveSettings () {
      this.$emit('save', this.adminAccountSettingsToUpdate)
      this.isViewMode = true
    },
    changeMode (mode) {
      this.isViewMode = mode
    },
    cancel () {
      this.initData()
      this.changeMode(true)
    }
  }
}
</script>
