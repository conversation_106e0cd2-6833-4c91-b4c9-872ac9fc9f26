{"AppSettings": {"ApplicationName": "Apps System Tools Log Processor (sandbox)", "HasToSendSlackNotification": true}, "DbSettings": {"AppsServiceBusLogsMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/appsservicebuslogs?retryWrites=true&w=majority", "SystemToolsLogsMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/appssystemtoolslogs?retryWrites=true&w=majority", "AppsAccountSettingsMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/accounts?retryWrites=true&w=majority", "WhitelistedIpAddressesMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/cp?retryWrites=true&w=majority"}, "ServiceBusSettings": {"Host": "b-14ab2686-6644-4584-b08a-5a4d86f5fad1-1.mq.us-east-1.amazonaws.com", "Port": 61617, "Username": "ebizeventbus", "Password": "z$32s0d-nd8G62!js83aPuhpUh", "FailoverHosts": ["b-14ab2686-6644-4584-b08a-5a4d86f5fad1-1.mq.us-east-1.amazonaws.com"], "HasToUseSsl": true, "IsLoggingOn": true, "PublishSettings": {"RetryAttempts": 3, "MinRetryDelayInMs": 5000, "MaxRetryDelayInMs": 10000}, "ReceiveEndpointsSettings": {"SystemToolLogProcessorReceiveEndpointSettings": {"IsEnabled": true, "ConsumersCount": 3, "RetriesCount": 2, "RetriesIntervalInSec": 3, "IsRedeliveryEnabled": true, "RedeliveryCount": 2, "RedeliveryTimeoutInMinutes": 2}}}, "MailConfiguration": {"MailServer": "email-01.internal.aws.ebizautos.com", "MailPort": 25, "MailUserName": "", "MailPassword": "", "MailEnableSsl": false}, "SlackApiConfiguration": {"EmailFrom": "<EMAIL>", "EmailsTo": ["<EMAIL>"]}, "UserAuthReportConfiguration": {"MaxIncorrectPasswordAttemptsForOneUserPerIp": 3, "MaxIncorrectUsernameAttemptsPerIp": 10}, "ExceptionSettings": {"ErrorWebServiceLocation": "http://errors.internal.aws.ebizautos.com/queuedservice.asmx", "ApplicationCategoryId": 1, "MailServer": "email-01.internal.aws.ebizautos.com", "MailPort": 25, "MailUserName": "", "MailPassword": "", "MailEnableSsl": false, "ErrorEmailFrom": "<EMAIL>", "ErrorEmailTo": ["alex.i<PERSON><PERSON><PERSON>@ebizautos.com"], "EmailCc": [], "HasToUseServiceBus": true}}