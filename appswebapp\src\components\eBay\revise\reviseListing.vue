<template>
  <div>
    <h6 class="border-bottom py-2">Revise eBay Listing Options</h6>
    <span>Based on the current listing status, you have the following options for revising this listing:</span>
    <div class="d-flex flex-column my-2">
      <span class="my-2 link text-info cursor-pointer" v-for="revise in getReviseOptions" :key="revise.title" @click="selectRevise(revise.type)">
        <u>{{revise.title}}</u>
      </span>
    </div>
  </div>
</template>

<script>
import { SidenavRouterLink } from '@/vendor/libs/sidenav'
import {mapGetters} from 'vuex'

export default {
  components: {
    SidenavRouterLink
  },
  computed: {
    ...mapGetters('eBayRevise', ['reviseOptions', 'revise']),
    getReviseOptions () {
      let options = []
      this.reviseOptions.map(x => {
        if (x.isEnabled) {
          options.push(
            {
              title: x.title,
              type: x.key
            }
          )
        }
      })
      return options
    }
  },
  methods: {
    selectRevise (type) {
      this.$emit('selectRevise', type)
    }
  }
}
</script>
