<template>
  <div>
    <craigslist-log-listing :accountId="accountId"/>
  </div>
</template>

<script>
import craigslistLogListing from '@/components/craigslist/logs/craigslistLogListing.vue'

export default {
  name: 'craislist-log',
  metaInfo: {
    title: 'Craislist Log'
  },
  props: {
    accountId: {
      type: Number,
      required: true
    }
  },
  components: {
    'craigslist-log-listing': craigslistLogListing
  },
  data () {
    return {
    }
  }
}
</script>>
