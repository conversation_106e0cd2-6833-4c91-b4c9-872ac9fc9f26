<template>
  <div class="v-toaster">
    <transition-group name="v-toast">
      <div class="v-toast" :class="{[t.theme]: t.theme}" v-for="t in items" :key="t.key">
        <a class="v-toast-btn-clear" @click="remove(t)">
        </a>{{t.message}}
      </div>
    </transition-group>
  </div>
</template>

<script>
export default {
  props: {
    timeout: {
      type: Number,
      default: 7000
    }
  },
  methods: {
    success (message, option = {}) { this.add(message, {theme: 'v-toast-success', timeout: option.timeout}) },
    info    (message, option = {}) { this.add(message, {theme: 'v-toast-info', timeout: option.timeout}) },
    warning (message, option = {}) { this.add(message, {theme: 'v-toast-warning', timeout: option.timeout}) },
    error   (message, option = {}) { this.add(message, {theme: 'v-toast-error', timeout: option.timeout}) },
    exception (exception, message = '', option = {}) { this.add(this.parseException(exception, message), {theme: 'v-toast-error', timeout: option.timeout}) },
    add (message, {theme, timeout}) {
      if (!this.$parent) {
        this.$mount()
        document.body.appendChild(this.$el)
      }
      let item = {message, theme, key: `${Date.now()}-${Math.random()}`}
      this.items.push(item)
      setTimeout(() => this.remove(item), timeout || this.timeout)
    },
    remove (item) {
      let i = this.items.indexOf(item)
      if (i >= 0) {
        this.items.splice(i, 1)
      }
    },
    parseException (ex, message) {
      if (!ex || !ex.response || ex.response.status !== 400) {
        return message
      }
      let data = ex.response.data
      if (!data) {
        return message
      }

      if (typeof data === 'string') {
        return `${message}. Reason: ${data}`
      }

      if (typeof data !== 'object') {
        return message
      }

      if (data.executionResultMessage) {
        return `${message}. Reason: ${data.executionResultMessage}`
      }

      return message
    }
  },
  data () {
    return {
      items: []
    }
  }
}
</script>

<style lang="scss">
.v-toaster {
  position: fixed;
  bottom: 2%;
  left: 50%;
  transform: translate(-50%, 0%);
  z-index: 10000;
  width: 300px;
  padding-left: 10px;
  padding-right: 10px;
}

.v-toaster .v-toast {
  margin-top: 10px;
  transition: all 0.3s ease;
  border-radius: 8px;
  color: #fff;
  display: block;
  padding: 1rem;
  background: rgba(69,77,93,0.9);
  border: 1px solid #454d5d;
}

.v-toaster .v-toast.v-toast-enter,
.v-toaster .v-toast.v-toast-leave-to {
  -webkit-transform: translateY(100%);
  transform: translateY(100%);
}

.v-toaster .v-toast.v-toast-success {
  background: rgba(2, 188, 119, 0.95);
  border-color: #32b643;
}

.v-toaster .v-toast.v-toast-warning {
  background: rgba(255, 217, 80, 0.95);
  border-color: #ffb700;
}

.v-toaster .v-toast.v-toast-info {
  background: rgba(23, 162, 184,0.95);
  border-color: #5bc0de;
}

.v-toaster .v-toast.v-toast-error {
  background: rgba(217, 83, 79,0.95);
  border-color: #bc4a4a;
}

.v-toaster .v-toast .v-toast-btn-clear {
  background: transparent;
  border: 0;
  color: currentColor;
  opacity: 0.45;
  text-decoration: none;
  float: right;
  cursor: pointer;
}

.v-toaster .v-toast .v-toast-btn-clear:hover {
  opacity: 0.85;
}

.v-toaster .v-toast .v-toast-btn-clear::before {
  content: "\2715";
}

@media (max-width: 300px) {
  .v-toaster {
     width: 100%;
  }
}
</style>
