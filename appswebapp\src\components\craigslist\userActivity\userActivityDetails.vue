<template>
  <b-card>
    <log-node
      v-if="logDetails"
      :data="logDetails"
      :isExpandedShallow="true"
      :isExpandedDeep="false"
    />
  </b-card>
</template>

<script>

export default {
  name: 'user-activity-modal',
  props: {
    item: { type: Object, required: true }
  },
  data () {
    return {
      logDetails: null
    }
  },
  components: {
    'log-node': () => import('@/components/_shared/logItemNode.vue')
  },
  created () {
    this.initData()
  },
  methods: {
    initUserActivityLogDetails () {
      this.$store.dispatch('craigslist/getUserActivityLogDetails', {id: this.item.id}).then(x => {
        this.logDetails = {
          nodes: x.data.model
        }
      })
        .catch(ex => this.$logger.handleError(ex, 'Can\'t get craigslist user activity detail log'))
    },
    initData () {
      this.initUserActivityLogDetails()
    }
  },
  watch: {
    item: {
      deep: true,
      handler: function () {
        this.initData()
      }
    }
  }
}
</script>
