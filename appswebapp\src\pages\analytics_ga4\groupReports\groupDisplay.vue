<template>
  <div>
    <display-summary
      :barItems="bar.items"
      :summary="summary"
      :barTimeFormat="barTimeFormat"
    ></display-summary>

    <template v-if="isAccountLevel">
      <account-level-card v-if="isAccountLevel" @backToGroup="onBackToGroup">
        {{accountName}} ({{page.filter.accountId}})
      </account-level-card>

      <display-account-level-table
        :tableItems="table.items"
        :totalItems="table.totalItems"
        :pageNumber="page.filter.pageNumber"
        :pageSize="page.filter.pageSize"
        :sortType="page.filter.sortType"
        @pageNumberChanged="onPageNumberChanged"
        @pageSizeChanged="onPageSizeChanged"
        @sortTypeChanged="onSortTypeChanged"
      ></display-account-level-table>
    </template>
    <group-display-by-account-table
      v-else-if="rangeInfo"
      :tableItems="table.items"
      :totalItems="table.totalItems"
      :pageNumber="page.filter.pageNumber"
      :pageSize="page.filter.pageSize"
      :sortType="page.filter.sortType"
      @pageNumberChanged="onPageNumberChanged"
      @pageSizeChanged="onPageSizeChanged"
      @sortTypeChanged="onSortTypeChanged"
      @accountNameClicked="onAccountNameClicked"
    ></group-display-by-account-table>
  </div>
</template>

<style src="@/vendor/styles/pages/analytics.scss" lang="scss"></style>

<script>
import analyticsConstants from './../../../shared/analytics/constants'
import analyticsBuilders from './../../../shared/analytics/builders'
import analyticsHelper from '../helpers.js'
import baseGroupReportPage from './baseGroupReportPage.js'

import DisplaySummary from '../../../components/analytics_ga4/summaries/displaySummary'
import AccountLevelCard from '../../../components/analytics_ga4/accountLevelCard'
import DisplayAccountLevelTable from '../../../components/analytics_ga4/tables/displayAccountLevelTable'
import GroupDisplayByAccountTable from '../../../components/analytics_ga4/tables/groupDisplayByAccountTable'

const filterManager = analyticsBuilders.getFilterManager({
  accountId: { type: Number, default: 0 },
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  pageNumber: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  sortType: { type: Number, default: analyticsConstants.displaySortTypes.accountNameAsc }
})

const defaultAccountLevelSortType = analyticsConstants.displaySortTypes.dateDesc

export default {
  mixins: [baseGroupReportPage],
  name: 'group-display',
  metaInfo: {
    title: 'Analytics - Display / YouTube Report'
  },
  components: {
    GroupDisplayByAccountTable,
    DisplayAccountLevelTable,
    AccountLevelCard,
    DisplaySummary
  },
  created () {
    this.$store.commit('analyticsGa4/setAnalyticsName', 'Display / YouTube Report')
    this.$store.commit('analyticsGa4/setBlackThemeOn', true)
  },
  data () {
    return {
      page: {
        filter: filterManager.defaultValue
      },
      summary: {
        sessions: 0,
        sessionsDelta: null,
        impressions: 0,
        impressionsDelta: null,
        clicks: 0,
        clicksDelta: null,
        spend: 0,
        spendDelta: null,
        costPerClick: 0,
        costPerClickDelta: null
      }
    }
  },
  computed: {
    filterManager () {
      return filterManager
    },
    defaultAccountLevelSortType () {
      return defaultAccountLevelSortType
    }
  },
  methods: {
    getAccountNameSortTypeCondition () {
      return this.page.filter.sortType === analyticsConstants.displaySortTypes.accountNameAsc ||
        this.page.filter.sortType === analyticsConstants.displaySortTypes.accountNameDesc
    },
    async updateStatistics () {
      try {
        await Promise.all([
          !this.isAccountLevel ? this.updateGroupLevelGraphAndSummary() : null,
          !this.isAccountLevel ? this.updateGroupLevelDetails() : null,
          this.isAccountLevel ? this.updateAccountLevelGraphAndSummary() : null,
          this.isAccountLevel ? this.updateAccountLevelDetails() : null
        ])
      } catch (err) {
        this.$toaster.error('Failed to get data from server', {timeout: 8000})
        this.$logger.handleError(err, 'Can\'t update statistics', {filter: this.page.filter, cache: this.cache})
      }
    },
    async updateGroupLevelGraphAndSummary () {
      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getGroupDisplayGraphAndSummary',
        {
          reportGroupId: this.reportGroupId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo
        }
      )

      this.summary = {
        ...this.summary,
        ...store.summary.data
      }
      this.bar.items = store.graph.data.items
    },
    async updateAccountLevelGraphAndSummary () {
      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getDisplayGraphAndSummary',
        {
          accountId: this.page.filter.accountId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo
        }
      )

      this.summary = {
        ...this.summary,
        ...store.summary.data
      }
      this.bar.items = store.graph.data.items
    },
    async updateGroupLevelDetails () {
      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getGroupDisplayDetailsPage',
        {
          reportGroupId: this.reportGroupId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo,
          pageNumber: this.page.filter.pageNumber,
          pageSize: this.page.filter.pageSize,
          sortType: this.page.filter.sortType
        }
      )

      this.table.items = store.detailedData.data.items.map(x => {
        x.account.isAccessAllowed = this.isAccessToAccountAllowed(x.account.accountId)
        return x
      })
      this.table.totalItems = store.detailedData.data.totalItems
    },
    async updateAccountLevelDetails () {
      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getDisplayDetailsPage',
        {
          accountId: this.page.filter.accountId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo,
          pageNumber: this.page.filter.pageNumber,
          pageSize: this.page.filter.pageSize,
          sortType: this.page.filter.sortType
        }
      )

      this.table.items = store.detailedData.data.items
      this.table.totalItems = store.detailedData.data.totalItems
    }
  }
}
</script>
