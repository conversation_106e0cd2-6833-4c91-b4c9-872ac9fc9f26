<template>
  <div>
    <b-table
    class="products-table card-table"
    :items="vehicles"
    :current-page="inventoryData.pageNumber"
    :per-page="inventoryData.pageSize"
    :sort-by.sync="sortBy"
    :sort-desc.sync="sortDesc"
    @sort-changed="onSortChanged"
    :fields="fields"
    :striped="true"
    :bordered="false"
    :no-sort-reset="true"
    :no-local-sorting="true"
    responsive
    >
      <template #cell(title)="data">
        <div class="media align-items-center">
          <router-link class="media-body text-dark d-flex align-items-center" :to="{ path: getDetailsPath(data.item) }">
            <img class="d-block float-left ui-w-50 mr-3" style="min-height: 1px" :src="getVehiclePhotoSrc(data.item)">
            <span>{{data.item | getVehicleTitle}}</span>
          </router-link>
        </div>
      </template>

      <template #cell(highPrice)="data">
        <write-permission-wrapper variant="disable">
          <price-input size="sm" v-model="data.item.highPrice" placeholder="Enter Price" active></price-input>
        </write-permission-wrapper>
      </template>

      <template #cell(lowPrice)="data">
        <write-permission-wrapper variant="disable">
          <price-input size="sm" v-model="data.item.lowPrice" placeholder="Enter Price" active></price-input>
        </write-permission-wrapper>
      </template>

      <template #cell(actions)="data">
        <router-link :to="{ path: getDetailsPath(data.item, true) }" v-if="isOriginalModel(data.item)">
          <b-btn variant="secondary" size="sm" class="inv-btn-fixed-sizes"><font-awesome-icon icon="pencil-alt" /> <span class="inv-btn-title">Edit</span></b-btn>
        </router-link>
        <b-btn v-else variant="primary" size="sm" class="inv-btn-fixed-sizes" @click="saveItem(data.item)"><font-awesome-icon icon="cloud-upload-alt" /> <span class="inv-btn-title">Save</span></b-btn>
      </template>

    </b-table>
  </div>
</template>

<script>
import globals from '@/globals'
import permission from '@/shared/common/permissions'
import priceInput from '@/components/_shared/priceInput'
import vehicleService from '@/services/inventory/VehicleManagementService'
import writePermissionWrapper from '../_shared/writePermissionWrapper'

export default {
  components: {
    'price-input': priceInput,
    'write-permission-wrapper': writePermissionWrapper
  },
  props: {
    inventoryData: {
      type: Object,
      default: function () {
        return {
          vehicles: [],
          pageNumber: 1,
          pageSize: 25
        }
      }
    },
    siteSettings: {
      type: Object,
      required: true
    },
    sortField: String,
    sortOrderDesc: Boolean
  },
  data () {
    return {
      vehicles: globals().getClonedValue(this.inventoryData.vehicles),
      originalVehicles: globals().getClonedValue(this.inventoryData.vehicles),
      permission,
      fields: [{ key: 'title',
        label: 'Vehicle',
        sortable: true,
        tdClass: 'py-2 align-middle',
        thStyle: 'min-width: 300px'
      }, {
        key: 'stockNumber',
        label: 'Stock #',
        sortable: true,
        tdClass: 'py-2 align-middle'
      }, {
        key: 'age',
        label: 'Age',
        sortable: true,
        tdClass: 'py-2 align-middle'
      }, {
        key: 'highPrice',
        label: 'High Price',
        sortable: true,
        tdClass: 'py-2 align-middle inv-price-width'
      }, {
        key: 'lowPrice',
        label: 'Low Price',
        sortable: true,
        tdClass: 'py-2 align-middle inv-price-width'
      }, {
        key: 'actions',
        label: 'Actions',
        sortable: false,
        tdClass: 'py-2 align-middle text-nowrap'
      }]
    }
  },
  methods: {
    onSortChanged (value) {
      this.$emit('sortChanged', value)
    },
    isOriginalModel (item) {
      let originalItem = this.originalVehicles.find(x => x.vin === item.vin)

      if (!originalItem) {
        return false
      }
      return +item.highPrice === +originalItem.highPrice && +item.lowPrice === +originalItem.lowPrice
    },
    saveItem (item) {
      this.$set(item, '_rowVariant', 'info')

      let prices = {
        lowPrice: +item.lowPrice,
        highPrice: +item.highPrice
      }

      const updateData = {
        pricing: prices,
        vin: item.vin
      }

      return vehicleService.updateVehicle(item.accountId, updateData, 1).then(result => {
        let vehicle = this.originalVehicles.find(x => x.vin === result.data.vin)
        vehicle.highPrice = result.data.pricing.highPrice
        vehicle.lowPrice = result.data.pricing.lowPrice

        this.$set(item, '_rowVariant', 'success')
      }).catch(reason => {
        this.$set(item, '_rowVariant', 'danger')
        this.$logger.handleError(reason, 'Can\'t update vehicle price', updateData)
      })
    },
    getVehiclePhotoSrc (item) {
      return (item.presentationPhoto || {}).photo107 + '?q=' + item.actualPhotosDateTimeEdited
    },
    getDetailsPath (item, hasToAppendTab) {
      return `/inventory/${item.accountId}/edit/${item.vin}` + (
        hasToAppendTab
          ? `/?tabindex=1`
          : ''
      )
    }
  },
  filters: {
    getVehicleTitle: function (value) {
      if (!value) return ''
      let title = ''
      if (value.year > 0) {
        title = value.year.toString()
      }

      title = [title, value.make, value.model, value.trim].filter(v => v && v !== '').join(' ')

      return title.trim()
    }
  },
  computed: {
    sortBy: {
      get () {
        return this.sortField
      },
      set () {}
    },
    sortDesc: {
      get () {
        return this.sortOrderDesc
      },
      set () {}
    }
  },
  watch: {
    'inventoryData.vehicles': {
      handler: function (newValue) {
        this.vehicles = globals().getClonedValue(newValue)
        this.originalVehicles = globals().getClonedValue(newValue)
      },
      deep: true
    }
  }
}
</script>

<style>
  .inv-btn-title {
    margin: 0 2px;
  }

  .inv-price-width {
    max-width: 100px;
  }

  .inv-btn-fixed-sizes {
    min-width: 66px;
    padding: 0.188rem 0.4875rem;
  }
</style>
