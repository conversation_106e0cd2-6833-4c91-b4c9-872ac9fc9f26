<template>
<ValidationObserver ref="validator">
<b-card>
  <b-row class="border-bottom mb-2 py-2">
    <b-col>
      <strong>{{reviseHeader}}</strong>
    </b-col>
    <c-button v-if="!isDisabled" :message="`Are you sure you want ${btnDesc}?`" variant="primary" size="sm" @confirm="onConfirm">
        {{btnDesc}}
    </c-button>
    <loader class="mr-5" v-else size="sm"/>
  </b-row>
  <detail-row bootstrapMode :fixed-payload-width="true">
    <span slot="title">Listing ID:</span>
    <b-link slot="payload" :href="getListingUrl" class="text-info"><u>{{revise.AuctionId}}</u></b-link>
  </detail-row>
  <detail-row bootstrapMode :fixed-payload-width="true">
    <span slot="title">Boldface Title:</span>
    <b-form-checkbox v-model="addEBayUpgradesData.HasBoldfaceTitle" slot="payload">Activate for $4</b-form-checkbox>
  </detail-row>
  <detail-row bootstrapMode :fixed-payload-width="true">
    <span slot="title">Sub Title:</span>
    <b-form-checkbox @input="onInput" v-model="addEBayUpgradesData.HasSubTitle" slot="payload">Activate fot $5</b-form-checkbox>
  </detail-row>
  <ValidationProvider name="Sub Title" rules="required|max:55" v-slot="{errors}">
  <detail-row bootstrapMode titlePosition="start" v-if="addEBayUpgradesData.HasSubTitle" :fixed-payload-width="true"
    :error="errors[0]">
    <span slot="title">Sub Title Text:</span>
    <b-form-group
      slot="payload"
      class="w-100"
    >
      <b-form-input name="Sub_Title_Text" v-model="addEBayUpgradesData.SubTitleText"></b-form-input>
      <b-form-text class="text-dark float-right">
        55 Characters Remaining
      </b-form-text>
    </b-form-group>
  </detail-row>
  </ValidationProvider>
</b-card>
</ValidationObserver>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import loader from '@/components/_shared/loader'
import constants from '@/shared/ebay/constants'
import {mapGetters} from 'vuex'
import globals from '@/globals'

export default {
  props: {
    btnDesc: {
      type: String,
      default: 'Revise'
    },
    reviseHeader: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      addEBayUpgradesData: {
        SubTitleText: '',
        HasBoldfaceTitle: false,
        HasSubTitle: false
      },
      isDisabled: false
    }
  },
  created () {
    this.init()
  },
  components: {
    detailRow,
    loader
  },
  computed: {
    ...mapGetters('eBayRevise', ['revise']),
    getListingUrl () {
      return constants.eBayInfoUrls.ebayItemUrl(this.revise.AuctionId)
    }
  },
  methods: {
    init () {
      this.addEBayUpgradesData.SubTitleText = globals().getClonedValue(this.revise.SubTitle)
      this.addEBayUpgradesData.HasBoldfaceTitle = globals().getClonedValue(this.revise.IsBold)
      this.addEBayUpgradesData.HasSubTitle = this.revise.SubTitle && this.revise.SubTitle.trim().length > 0
    },
    onInput () {
      if (!this.addEBayUpgradesData.HasSubTitle) {
        this.addEBayUpgradesData.SubTitleText = ''
      }
    },
    async onConfirm () {
      let validateRes = await this.validate()
      if (!validateRes) {
        return
      }
      let apiParams = {
        accountId: this.revise.AccountId,
        auctionId: this.revise.AuctionId,
        data: this.addEBayUpgradesData
      }
      this.isDisabled = true
      this.$store.dispatch('eBayRevise/addEBayUpgrades', apiParams).then(res => {
        this.$toaster.success('Operation Successfully')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
        if (ex.response && ex.response.status !== 400) {
          this.$logger.handleError(ex, `Exception occurred on ${this.reviseHeader}`)
        }
      }).finally(() => {
        this.isDisabled = false
        setTimeout(() => this.$router.go(), 4000)
      })
    },
    async validate () {
      if (this.revise.SubTitle === this.addEBayUpgradesData.SubTitleText.trim() && this.addEBayUpgradesData.HasBoldfaceTitle === this.revise.IsBold) {
        return false
      }

      let res = await this.$refs.validator.validate()

      return res
    }
  }
}
</script>
