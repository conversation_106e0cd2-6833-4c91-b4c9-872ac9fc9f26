﻿using MongoDB.Bson.Serialization.Attributes;
using System.Collections.Generic;
using System.ComponentModel;
using static EBizAutos.Apps.CommonLib.Enums.AuthenticationEnums;

namespace EBizAutos.Apps.CommonLib.Models.Logs.SystemTools {
	public class UserAuthLogItem : BaseLogItem {
		public enum AuthStatusEnum : byte {
			Undefined = 0,
			Success = 1,
			Rejected = 2,
			ErrorOccurred = 3,
			InvalidCredentials = 4
		}

		[Description("IP Address")]
		public string IpAddress { get; set; }
		[Description("User Name")]
		public string UserName { get; set; }
		[BsonIgnoreIfDefault]
		public string Password { get; set; }
		[Description("User Type")]
		public UserTypeEnum UserType { get; set; }
		[Description("Device Id")]
		public string DeviceId { get; set; }
		[Description("Device Model")]
		public string DeviceModel { get; set; }
		[Description("Application Name")]
		public string ApplicationName { get; set; }
		[Description("First Name")]
		[BsonIgnoreIfDefault]
		public string FirstName { get; set; }
		[Description("Last Name")]
		[BsonIgnoreIfDefault]
		public string LastName { get; set; }
		[Description("Account Id")]
		public int AccountId { get; set; }
		[Description("Account Name")]
		[BsonIgnoreIfDefault]
		public string AccountName { get; set; }
		[Description("User Agent")]
		[BsonIgnoreIfDefault]
		public string UserAgent { get; set; }
		[Description("CloudFront Viewer City")]
		[BsonIgnoreIfDefault]
		public string CloudFrontViewerCity { get; set; }
		[Description("CloudFront Viewer Country Region Name")]
		[BsonIgnoreIfDefault]
		public string CloudFrontViewerCountryRegionName { get; set; }
		[Description("CloudFront Viewer Country Name")]
		[BsonIgnoreIfDefault]
		public string CloudFrontViewerCountryName { get; set; }
		[Description("CloudFront Viewer Address")]
		[BsonIgnoreIfDefault]
		public string CloudFrontViewerAddress { get; set; }
		[Description("CloudFront Viewer ASN")]
		[BsonIgnoreIfDefault]
		public string CloudFrontViewerAsn { get; set; }
		[Description("Rejection Message")]
		[BsonIgnoreIfDefault]
		public string RejectionMessage { get; set; }
		public AuthStatusEnum Status { get; set; }
	}

	public class UserAuthLogGroupByIpAddress {
		public string IpAddress { get; set; }
		public IEnumerable<UserAuthLogItem> Items { get; set; }
	}
}