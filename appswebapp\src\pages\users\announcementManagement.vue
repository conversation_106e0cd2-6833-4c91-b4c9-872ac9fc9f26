<template>
  <div v-if="isLoaded">
    <b-row>
      <b-col><h4>{{ getTitle }}</h4></b-col>
      <b-col>
        <b-btn class="float-right ml-2" v-if="isEditMode" variant="dark" @click="deleteAnnouncement">Delete</b-btn>
        <b-btn class="float-right" variant="primary" @click="addOrEditAnnouncement">{{ isEditMode ? 'Save changes' : 'Create' }}</b-btn>
      </b-col>
    </b-row>
    <b-card class="mt-2">
      <detail-row :fixed-payload-width="true">
        <span slot="title">Title:</span>
        <b-form-input slot="payload" v-model="announcement.title"></b-form-input>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Type:</span>
        <b-form-select slot="payload" @input="onInputAnnouncementType" v-model="announcement.announcementType" :options="getAnnouncementTypesOptions" :disabled="isEditMode"></b-form-select>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Start Date:</span>
        <b-input-group slot="payload" class="flex-nowrap mt-2">
          <b-input-group-prepend is-text>
            <i class="ion ion-md-calendar" slot="prepend"></i>
          </b-input-group-prepend>
          <date-time-picker
            ref="startDateTime"
            v-model="announcement.startDateTime"
            :options="filterTimeOptions"
            format="MM/DD/YYYY HH:mm"
            className="form-control"
            @change="onAnnouncementStartDateTimeChange"
          />
          <b-input-group-append
            is-text
            v-show="announcement.startDateTime"
            @click="announcement.startDateTime = null"
          >
            <i class="ion ion-md-close"></i>
          </b-input-group-append>
        </b-input-group>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">End Date:</span>
        <b-input-group slot="payload" class="flex-nowrap mt-2">
          <b-input-group-prepend is-text>
            <i class="ion ion-md-calendar" slot="prepend"></i>
          </b-input-group-prepend>
          <date-time-picker
            ref="endDateTime"
            v-model="announcement.endDateTime"
            :options="filterTimeOptions"
            format="MM/DD/YYYY HH:mm"
            className="form-control"
            @change="onAnnouncementEndDateTimeChange"
          />
          <b-input-group-append
            is-text
            v-show="announcement.endDateTime"
            @click="announcement.endDateTime = null"
          >
            <i class="ion ion-md-close"></i>
          </b-input-group-append>
        </b-input-group>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Affected Accounts:</span>
        <b-form-select slot="payload" v-model="announcement.filters.announcementAccountFilterType" @input="onInputAccountFilterType" :options="getAffectedAccountsOptions"></b-form-select>
      </detail-row>
      <detail-row :fixed-payload-width="true" :title-position="'start'" v-if="hasToDisplayAccountsSelector">
        <span slot="title">Account Ids:</span>
        <div class="w-100 d-flex flex-row" slot="payload">
          <span class="text-truncate mr-2" style="max-width: 200px;">{{ getAccountsDesc }}</span>
          <b-btn variant="secondary" size="sm" @click="showAccountsSelector">Edit</b-btn>
        </div>
      </detail-row>
      <div class="my-2">
        <b-row class="border-bottom pb-2">
          <b-col>
            <h5 class="p-0 m-0">Boxes</h5>
          </b-col>
          <b-col>
            <b-btn v-if="isAddNewBoxEnabled" class="float-right" size="sm" variant="primary" @click="addNewBox">Add New Box</b-btn>
          </b-col>
        </b-row>
        <draggable
            class="col"
            v-bind="draggableOptions"
            v-model="announcement.boxes"
            @start="isDragging = true"
            @end="isDragging = false"
            :class="{on: isDragging, off: !isDragging}"
            :handle="`.boxes-container`"
          >
            <b-row class="mt-2" @click="boxIndex = index" v-for="(box, index) in announcement.boxes" :key="index">
              <b-card class="w-100">
                <ValidationObserver v-slot="{ invalid }">
                  <b-row class="boxes-container border-bottom">
                    <b-col>
                      <h6>Box Number {{ index + 1 }}</h6>
                    </b-col>
                    <b-col>
                      <b-btn v-if="isRemoveBoxEnabled" class="float-right ml-1" size="sm" variant="dark" @click="onRemoveBox(index)">Remove</b-btn>
                      <b-btn v-if="!box.isEdit" class="float-right" size="sm" @click="onEditBox(box, index)">Edit</b-btn>
                      <b-btn v-else-if="box.copy" class="float-right ml-1"  size="sm"  @click="onCancelBox(box, index)">Cancel</b-btn>
                      <b-btn v-if="box.isEdit" class="float-right" size="sm" variant="primary" @click="onSaveBoxChanges(box, index)" :disabled="invalid">Save</b-btn>
                    </b-col>
                  </b-row>
                  <b-col xl="6" lg="12" sm="12">
                    <ValidationProvider immediate name="Title" rules="required" v-slot="{errors}">
                      <detail-row :title-position="'start'" :large-payload-width="true" :error="errors[0]">
                        <span slot="title">Title:</span>
                        <div slot="payload" class="w-100">
                          <div v-if="!box.isEdit" v-html="box.title"></div>
                          <ckeditor v-else v-model="box.title"></ckeditor>
                        </div>
                      </detail-row>
                    </ValidationProvider>
                  </b-col>
                  <b-col xl="6" lg="12" sm="12">
                    <ValidationProvider immediate name="Message" rules="required" v-slot="{errors}">
                      <detail-row :title-position="'start'" :large-payload-width="true" :error="errors[0]">
                        <span slot="title">Message:</span>
                        <div slot="payload" class="w-100">
                          <div v-if="!box.isEdit" v-html="box.message"></div>
                          <ckeditor v-else v-model="box.message"></ckeditor>
                        </div>
                      </detail-row>
                    </ValidationProvider>
                  </b-col>
                  <b-col xl="6" lg="12" sm="12">
                    <ValidationProvider immediate name="Path&Query" rules="required" v-slot="{errors}">
                      <detail-row :title-position="'start'" :large-payload-width="true" :error="errors[0]">
                        <span slot="title">{{  isAnnouncementDynamic ? 'Path&Query' : 'Path Regex' }}:</span>
                        <div slot="payload" class="w-100">
                          <span v-if="!box.isEdit">{{ box.pathAndQuery }}</span>
                          <b-form-input v-else v-model="box.pathAndQuery"></b-form-input>
                        </div>
                      </detail-row>
                    </ValidationProvider>
                  </b-col>
                  <b-col v-if="isAnnouncementDynamic">
                    <detail-row :title-position="'start'" :large-payload-width="true">
                      <span slot="title">Position:</span>
                      <b-form-select slot="payload" v-model="box.hasToIncludeElement" @input="onBoxHasToIncludeElementChange($event, box, index)" :options="getBoxPositionOptions" :disabled="!box.isEdit"></b-form-select>
                    </detail-row>
                  </b-col>
                  <b-col xl="6" lg="12" sm="12" v-if="box.hasToIncludeElement && isAnnouncementDynamic">
                    <ValidationProvider immediate name="Element" rules="required" v-slot="{errors}">
                      <detail-row :title-position="'start'" :large-payload-width="true" :error="errors[0]">
                        <span slot="title">Element:</span>
                        <div slot="payload" class="w-100">
                          <b-input-group>
                            <b-form-input v-model="box.element.elementId" :disabled="!box.isEdit"></b-form-input>
                            <b-input-group-append>
                              <b-form-select v-model="box.element.tooltipRelativePosition" :options="getTooltipRelativePositionOptions" :disabled="!box.isEdit"></b-form-select>
                            </b-input-group-append>
                          </b-input-group>
                        </div>
                      </detail-row>
                    </ValidationProvider>
                  </b-col>
                </ValidationObserver>
              </b-card>
              <input type="text" hidden :value="index+1" @change="onAnnouncementBoxesIndexChanged($event, index + 1)"/>
            </b-row>
          </draggable>
      </div>
    </b-card>
    <b-row class="mt-2">
      <b-col cols="12">
        <b-btn class="float-right" variant="primary" @click="addOrEditAnnouncement">{{ isEditMode ? 'Save changes' : 'Create' }}</b-btn>
      </b-col>
    </b-row>
    <accountsselector
      :selectedIds="announcement.filters.accountIds"
      @onAccountSelected="onAccountSelected"
      @onAccountUnselected="onAccountUnselected"
      :visible="isEditingAccountListing"
      @hide="onAccountsSelectorHide"
    />
  </div>
  <div v-else>
    <loader size="lg"/>
  </div>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import draggable from 'vuedraggable'
import loader from '@/components/_shared/loader'
import { tooltipRelativePositionOptions, announcementTypes, announcementAccountFilterTypes } from '@/shared/announcements/constants'
import globals from '../../globals'
import AnnouncementService from '@/services/announcements/AnnouncementService'
import moment from 'moment'
import ckeditor from '@/components/_shared/ckeditor/ckeditor'
import accountsselector from '@/components/annnouncements/accountsselector'

export default {
  name: 'announcement-management',
  metaInfo: {
    title: 'Announcements'
  },
  props: {
    announcementId: String
  },
  data () {
    return {
      isEditingAccountListing: false,
      isLoaded: false,
      announcement: {
        boxes: [],
        announcementType: announcementTypes.dynamic.value,
        filters: {
          announcementAccountFilterType: announcementAccountFilterTypes.none.value,
          accountIds: []
        }
      },
      isDragging: true,
      draggableOptions: {
        animation: 150
      },
      filterTimeOptions: {
        autoUpdateInput: false,
        singleDatePicker: true,
        timePicker: true,
        filterTimeOptions: {
          autoUpdateInput: false,
          singleDatePicker: true,
          timePicker: true,
          timePicker24Hour: true,
          maxDate: new Date()
        }
      }
    }
  },
  components: {
    'date-time-picker': () => import('@gravitano/vue-date-range-picker/src/components/DateRangePicker'),
    draggable,
    detailRow,
    ckeditor,
    accountsselector,
    loader
  },
  created () {
    this.initData()
  },
  computed: {
    isEditMode () {
      return !!this.announcementId
    },
    getTitle () {
      return this.isEditMode ? 'Edit Announcement' : 'Create New Announcement'
    },
    refAnnouncementStartDate () {
      return (this.$refs.startDateTime || {}).$el || {}
    },
    refAnnouncementEndDate () {
      return (this.$refs.endDateTime || {}).$el || {}
    },
    getTooltipRelativePositionOptions () {
      return tooltipRelativePositionOptions
    },
    getBoxPositionOptions () {
      return [
        {
          value: false,
          text: 'Display it in the center of the window'
        },
        {
          value: true,
          text: 'Attach to element'
        }
      ]
    },
    getAnnouncementTypesOptions () {
      return Object.values(announcementTypes)
    },
    getAffectedAccountsOptions () {
      return Object.values(announcementAccountFilterTypes)
    },
    getAccountsDesc () {
      if (!this.announcement.filters.accountIds || this.announcement.filters.accountIds.length === 0) {
        return '-'
      }
      return this.announcement.filters.accountIds.join(', ')
    },
    hasToDisplayAccountsSelector () {
      return this.announcement.filters.announcementAccountFilterType !== announcementAccountFilterTypes.none.value
    },
    isAnnouncementDynamic () {
      return this.announcement.announcementType === announcementTypes.dynamic.value
    },
    isAddNewBoxEnabled () {
      return this.isAnnouncementDynamic || !this.announcement.boxes || this.announcement.boxes.length === 0
    },
    isRemoveBoxEnabled () {
      return this.isAnnouncementDynamic
    }
  },
  methods: {
    onAccountSelected (accountId) {
      if (!this.announcement.filters.accountIds) {
        this.$set(this.announcement.filters, 'accountIds', [])
      }
      if (!this.announcement.filters.accountIds.includes(accountId)) {
        this.announcement.filters.accountIds.push(accountId)
      }
    },
    onAccountUnselected (accountId) {
      var rmIndex = this.announcement.filters.accountIds.indexOf(accountId)
      if (rmIndex !== -1) {
        this.announcement.filters.accountIds.splice(rmIndex, 1)
      }
    },
    showAccountsSelector () {
      this.isEditingAccountListing = true
    },
    onAccountsSelectorHide () {
      this.isEditingAccountListing = false
    },
    onInputAccountFilterType (val) {
      if (+val === announcementAccountFilterTypes.none.value) {
        this.$set(this.announcement.filters, 'accountIds', [])
      }
      this.$set(this.announcement.filters, 'announcementAccountFilterType', val)
    },
    onInputAnnouncementType (val) {
      if (val === announcementTypes.static.value) {
        this.$set(this.announcement, 'boxes', [])
      }
      this.$set(this.announcement, 'announcementType', val)
    },
    initData () {
      if (this.isEditMode) {
        AnnouncementService.getAnnouncement(this.announcementId).then(res => {
          this.announcement = res.data
          this.announcement.startDateTime = moment(res.data.startDateTime).format('MM/DD/YYYY HH:mm')
          this.announcement.endDateTime = moment(res.data.endDateTime).format('MM/DD/YYYY HH:mm')
          this.announcement.boxes.forEach(x => {
            x.hasToIncludeElement = !!x.element
          })
        }).catch(ex => {
          this.$toaster.exception(ex, 'Something went wrong on getting announcement from server', {timeout: 5000})
          this.$logger.handleError(ex, 'Exception occurred on getting announcement from server')
          this.$router.push({name: 'announcements-tool'})
        }).finally(() => {
          this.isLoaded = true
        })
      } else {
        this.isLoaded = true
      }
    },
    onAnnouncementBoxesIndexChanged (newVal, oldVal) {
      if (!Number.isInteger(+newVal.target.value)) {
        return
      }

      let newIndex = (+newVal.target.value) - 1
      let oldIndex = oldVal - 1
      let arrayItem = this.announcement.boxes[oldIndex]
      this.announcement.boxes.splice(oldIndex, 1)
      this.announcement.boxes.splice(newIndex, 0, arrayItem)
    },
    onAnnouncementStartDateTimeChange (newVal) {
      let value = newVal || this.announcement.startDateTime || null
      this.$set(this.refAnnouncementStartDate, 'value', value)
    },
    onAnnouncementEndDateTimeChange (newVal) {
      let value = newVal || this.announcement.endDateTime || null
      this.$set(this.refAnnouncementEndDate, 'value', value)
    },
    addNewBox () {
      this.announcement.boxes.push({
        hasToIncludeElement: true,
        element: {
          tooltipRelativePosition: this.getTooltipRelativePositionOptions[0].value
        },
        isEdit: true
      })
    },
    onRemoveBox (index) {
      this.announcement.boxes.splice(index, 1)
    },
    onEditBox (box, index) {
      box.copy = globals().getClonedValue(box)
      box.isEdit = true
      this.announcement.boxes.splice(index, 1, box)
    },
    onCancelBox (box, index) {
      let original = globals().getClonedValue(box.copy)
      box.title = original.title
      box.message = original.message
      box.pathAndQuery = original.pathAndQuery
      box.element = original.element
      box.isEdit = false
      this.announcement.boxes.splice(index, 1, box)
    },
    onSaveBoxChanges (box, index) {
      box.isEdit = false
      this.announcement.boxes.splice(index, 1, box)
    },
    onBoxHasToIncludeElementChange (value, box, index) {
      this.$set(box, 'hasToIncludeElement', value)
      box.element = value ? {tooltipRelativePosition: this.getTooltipRelativePositionOptions[0].value} : null
      this.announcement.boxes.splice(index, 1, box)
    },
    addOrEditAnnouncement () {
      if (this.validation()) {
        if (this.isEditMode) {
          this.editAnnouncement()
        } else {
          this.addAnnouncement()
        }
      }
    },
    editAnnouncement () {
      AnnouncementService.updateAnnouncement(this.announcement).then(res => {
        this.$toaster.success('Announcement Updated Successfully!', {timeout: 5000})
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong on creating announcement', {timeout: 5000})
      })
    },
    addAnnouncement () {
      if (this.announcement.announcementType === announcementTypes.static.value) {
        this.announcement.boxes.forEach(x => { x.element = null })
      }
      AnnouncementService.createAnnouncement(this.announcement).then(res => {
        this.$toaster.success('Announcement Created Successfully!', {timeout: 5000})
        this.$router.push({name: 'announcements-tool'})
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong on creating announcement', {timeout: 5000})
      })
    },
    deleteAnnouncement () {
      AnnouncementService.deleteAnnouncement(this.announcementId).then(res => {
        this.$toaster.success('Announcement Deleted Successfully', {timeout: 5000})
        this.$router.push({name: 'announcements-tool'})
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong on creating announcement', {timeout: 5000})
      })
    },
    validation () {
      if (this.announcement.filters.announcementAccountFilterType !== announcementAccountFilterTypes.none.value) {
        if (!this.announcement.filters.accountIds || this.announcement.filters.accountIds.length === 0) {
          this.showErrorMessage('Account Ids is required')
          return false
        }
      }
      if (!this.announcement.startDateTime) {
        this.showErrorMessage('Start date is required')
        return false
      }
      if (!this.announcement.endDateTime) {
        this.showErrorMessage('End date is required')
        return false
      }
      if (this.announcement.startDateTime > this.announcement.endDateTime) {
        this.showErrorMessage('Start date must be less than end date')
        return false
      }
      if (!this.announcement.boxes || this.announcement.boxes.length === 0) {
        this.showErrorMessage('Boxes is required')
        return false
      }
      for (let index in this.announcement.boxes) {
        let box = this.announcement.boxes[index]
        if (box.isEdit) {
          this.showErrorMessage(`Please save or cancel your changes of ${+index + 1} box`)
          return false
        }
      }
      return true
    },
    showErrorMessage (errorMessage) {
      this.$toaster.error(errorMessage, {timeout: 7000})
    }
  }
}
</script>
