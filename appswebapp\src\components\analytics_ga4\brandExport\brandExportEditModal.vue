<template>
  <b-modal
    v-if="model"
    :title="getTitle"
    :visible="isShowModal"
    size="lg"
    @hide="hide"
    no-close-on-backdrop>
      <h6>Not for edit*</h6>
      <hr>
      <detail-row :big-payload-width="true">
        <span slot="title">Export Brand Type:</span>
        <span slot="payload">{{getBrandType}}</span>
      </detail-row>
      <detail-row :big-payload-width="true">
        <span slot="title">Make ID:</span>
        <span slot="payload">{{model.makeId}}</span>
      </detail-row>
      <span class="text-muted">*If you need to change these settings please contact Administrator</span>
      <detail-row :big-payload-width="true" class="mt-5">
        <span slot="title">Is Active</span>
        <b-form-checkbox slot="payload" v-model="model.isActive"></b-form-checkbox>
      </detail-row>
      <detail-row :big-payload-width="true">
        <span slot="title">Export Name:</span>
        <b-form-input slot="payload" v-model="model.name"></b-form-input>
      </detail-row>

      <h6 class="mt-5">Ftp Settings</h6>
      <hr>
      <detail-row :big-payload-width="true" class="mt-4">
        <span slot="title">Has to send to FTP</span>
        <b-form-checkbox slot="payload" v-model="model.ftpInformation.hasToSendToFtp"></b-form-checkbox>
      </detail-row>
      <detail-row :big-payload-width="true" class="mt-4">
        <span slot="title">FTP Type</span>
        <b-form-select slot="payload" @input="onInputFtpType" v-model="model.ftpInformation.ftpType" :options="getFtpTypeOptions"></b-form-select>
      </detail-row>
      <detail-row :big-payload-width="true">
        <span slot="title">Host:</span>
        <b-form-input slot="payload" v-model="model.ftpInformation.host"></b-form-input>
      </detail-row>
      <detail-row :big-payload-width="true">
        <span slot="title">Login:</span>
        <b-form-input slot="payload" v-model="model.ftpInformation.login"></b-form-input>
      </detail-row>
      <detail-row :big-payload-width="true">
        <span slot="title">Password:</span>
        <b-form-input slot="payload" v-model="model.ftpInformation.password"></b-form-input>
      </detail-row>
      <detail-row v-if="model.ftpInformation.ftpType !== ftpTypes.sftp.value" :big-payload-width="true">
        <span slot="title">IsPassiveConnection</span>
        <b-form-checkbox slot="payload" v-model="model.ftpInformation.isPassiveConnection"></b-form-checkbox>
      </detail-row>
      <detail-row :big-payload-width="true">
        <span slot="title">Port:</span>
        <b-form-input slot="payload" type="number" v-model="model.ftpInformation.port"></b-form-input>
      </detail-row>
      <template #modal-footer>
        <b-btn @click="hide">Close</b-btn>
        <b-btn @click="save" variant="primary">Save</b-btn>
      </template>
  </b-modal>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import constants from '@/shared/analytics/constants'

export default {
  name: 'analytic-brand-export-edit-modal',
  props: {
    model: { type: Object },
    isShowModal: { type: Boolean, required: true }
  },
  data () {
    return {
      ftpTypes: constants.ftpTypes
    }
  },
  components: {
    'detail-row': detailRow
  },
  computed: {
    getBrandType () {
      let res = constants.brandTypes.find(x => x.value === this.model.brandType)
      if (res) {
        return res.text
      }

      return ''
    },
    getTitle () {
      return `Edit Export Settings for ${this.model.name}`
    },
    getFtpTypeOptions () {
      return Object.values(constants.ftpTypes)
    }
  },
  methods: {
    hide () {
      this.$emit('hide')
    },
    save () {
      if (this.validate()) {
        this.$emit('save', this.model)
      }
    },
    validate () {
      if (this.model.ftpInformation.port < 0) {
        this.$toaster.error('Port field must be > 0')
        return false
      }

      return true
    },
    onInputFtpType (value) {
      if (value === constants.ftpTypes.sftp.value) {
        this.$nextTick(() => {
          this.$set(this.model.ftpInformation, 'isPassiveConnection', false)
        })
      }
    }
  }
}
</script>
