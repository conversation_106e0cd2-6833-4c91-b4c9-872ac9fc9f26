<template>
  <div class="date-picker">
    <datepicker ref="dateSelector"
                :minimumView="minView"
                :maximumView="maxView"
                v-model="currentDate"
                class="date-picker_component"
                wrapper-class="wrapper-class"
                :id="id"
                :disabled-dates="disabledDates"
    />
    <button class="date-picker-input-arrow btn btn-primary btn-round btn-md" @click="openRangePicker">{{label}}</button>
  </div>
</template>

<script>
export default {
  name: 'date-selector',
  props: {
    minView: String,
    maxView: String,
    value: String,
    labelFormat: String,
    maxDate: Date
  },
  mounted () {
    this.updateLabel()
  },
  data () {
    return {
      date: this.value,
      label: '',
      id: this.$uuid.v4(),
      disabledDates: {
        customPredictor: (data) => {
          return this.maxDate < data
        }
      }
    }
  },
  computed: {
    currentDate: {
      get () {
        return this.date
      },
      set (value, value2) {
        this.date = value
        this.updateLabel()
        this.$emit('input', value)
      }
    }
  },
  methods: {
    updateLabel () {
      this.$nextTick(() => {
        this.label = this.$locale.getMonthYearFormatted(this.date)
      })
    },
    openRangePicker () {
      document.getElementById(this.id).focus()
      document.getElementById(this.id).click()
    }
  },
  watch: {
    value (newVal) {
      this.date = newVal
    }
  },
  components: {
    'datepicker': () => import('vuejs-datepicker')
  }
}
</script>

<style scoped>
  .date-picker {
    position: relative !important;
    display: flex;
    justify-content: flex-end;
  }

  .date-picker-input-arrow:after {
    display: inline-block;
    margin-left: 0.457em;
    content: "";
    margin-top: -0.28em;
    width: 0.42em;
    height: 0.42em;
    border: 1px solid;
    border-top: 0;
    border-left: 0;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
    vertical-align: middle;
  }
</style>

<style>
  .date-picker_component div input {
    height: 0;
    width: 0;
    padding: 0;
    border: 0;
  }

  .date-picker_component {
    position: absolute !important;
    bottom: 0;
    right: 300px;
  }
</style>
