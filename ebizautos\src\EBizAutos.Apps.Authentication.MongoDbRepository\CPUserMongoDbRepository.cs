﻿using EBizAutos.Apps.Authentication.CommonLib.Models;
using EBizAutos.Apps.CommonLib.Abstract.Repositories.AppsUser;
using EBizAutos.Apps.CommonLib.Enums;
using EBizAutos.Apps.CommonLib.Models.AppsUser;
using EBizAutos.CommonLib.Encryption;
using EBizAutos.CommonLib.RepositoryClasses;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace EBizAutos.Apps.Authentication.MongoDbRepository {
	public class CPUserMongoDbRepository : BaseMongoDbRepository, IUserRepository, IUserSecondaryRepository {
		#region Additional Classes
		public class UnwindedUserSessions {
			[BsonId]
			[BsonRepresentation(BsonType.ObjectId)]
			public string Id { get; set; }

			public UserSession Sessions { get; set; }
		}
		#endregion

		private readonly IEncryptionProvider _encryptionProvider = null;

		#region Constructor
		public CPUserMongoDbRepository(string connectionString, IEncryptionProvider encryptionProvider) : base(connectionString) {
			_encryptionProvider = encryptionProvider;
		}
		#endregion

		#region IUserRepository
		public User GetUser(string id) {
			FilterDefinition<User> filter = Builders<User>.Filter.Eq(u => u.Id, id);

			return GetUserByFilter(filter);
		}

		public User GetUser(string userName, string password, string email = "") {
			string encryptedPassword = _encryptionProvider.Encrypt(password);

			FilterDefinition<User> filter = null;
			if (email.Length > 0) {
				filter = Builders<User>.Filter.And(
					Builders<User>.Filter.Eq(u => u.PersonalInformation.UserName, userName.ToLower()),
					Builders<User>.Filter.Eq(u => u.PersonalInformation.Email, email.ToLower()),
					Builders<User>.Filter.Eq(u => u.PersonalInformation.Password, encryptedPassword)
				);
			} else {
				filter = Builders<User>.Filter.And(
					Builders<User>.Filter.Eq(u => u.PersonalInformation.UserName, userName.ToLower()),
					Builders<User>.Filter.Eq(u => u.PersonalInformation.Password, encryptedPassword)
				);
			}

			return GetUserByFilter(filter);
		}

		public User GetUserByContactId(int contactId) {
			IMongoCollection<User> collection = GetCollection<User>(InternalConstants.CollectionNames.ConstUsersCollection);
			var filter = Builders<User>.Filter.Eq(u => u.ContactId, contactId);

			return collection.Find(filter)
				.Project<User>(GetUserProjection())
				.FirstOrDefault();
		}

		public List<User> GetUsersByIds(List<string> ids) {
			IMongoCollection<User> collection = GetCollection<User>(InternalConstants.CollectionNames.ConstUsersCollection);
			FilterDefinition<User> filter = Builders<User>.Filter.In(u => u.Id, ids);

			return collection.Find(filter).ToList();
		}

		public async Task<bool> IsUserNameReservedAsync(string userName) {
			IMongoCollection<User> collection = GetCollection<User>(InternalConstants.CollectionNames.ConstUsersCollection);
			FilterDefinition<User> filter = Builders<User>.Filter.Eq(u => u.PersonalInformation.UserName, userName);

			return await collection.Find(filter).AnyAsync();
		}

		public async Task<User> GetUserAsync(string id) {
			FilterDefinition<User> filter = Builders<User>.Filter.Eq(u => u.Id, id);

			return await GetUserByFilterAsync(filter);
		}

		public async Task<User> GetUserAsync(string userName, string password, string email = "") {
			string encryptedPassword = _encryptionProvider.Encrypt(password);

			FilterDefinition<User> filter = null;
			if (email.Length > 0) {
				filter = Builders<User>.Filter.And(
					Builders<User>.Filter.Eq(u => u.PersonalInformation.UserName, userName.ToLower()),
					Builders<User>.Filter.Eq(u => u.PersonalInformation.Email, email.ToLower()),
					Builders<User>.Filter.Eq(u => u.PersonalInformation.Password, encryptedPassword)
				);
			} else {
				filter = Builders<User>.Filter.And(
					Builders<User>.Filter.Eq(u => u.PersonalInformation.UserName, userName.ToLower()),
					Builders<User>.Filter.Eq(u => u.PersonalInformation.Password, encryptedPassword)
				);
			}

			return await GetUserByFilterAsync(filter).ConfigureAwait(false);
		}

		public List<User> GetUsersByUserName(string userName) {
			IMongoCollection<User> collection = GetCollection<User>(InternalConstants.CollectionNames.ConstUsersCollection);
			FilterDefinition<User> filter = Builders<User>.Filter.Eq(u => u.PersonalInformation.UserName, userName);
			SortDefinition<User> sort = Builders<User>.Sort.Ascending(x => x.PersonalInformation.UserName);

			return collection
				.Find(filter)
				.Sort(sort)
				.Project(x => new User() {
					Id = x.Id,
					AccountId = x.AccountId,
					UserType = x.UserType,
					PersonalInformation = new UserPersonalInformation() {
						UserName = x.PersonalInformation.UserName,
						FirstName = x.PersonalInformation.FirstName,
						LastName = x.PersonalInformation.LastName,
						Email = x.PersonalInformation.Email
					}
				}
				)
				.ToList();
		}

		public Task<List<User>> GetUsersBySearchPhraseAsync(string searchPhrase) {
			List<FilterDefinition<User>> searchFilters = new List<FilterDefinition<User>>();
			if (!string.IsNullOrEmpty(searchPhrase)) {
				if (int.TryParse(searchPhrase, out int number)) {
					searchFilters.Add(Builders<User>.Filter.Eq(x => x.AccountId, number));
				}

				BsonRegularExpression regularExpression = new BsonRegularExpression(searchPhrase, ConstRegexCaseInsensetiveOption);
				searchFilters.Add(Builders<User>.Filter.Regex(x => x.PersonalInformation.UserName, regularExpression));
				searchFilters.Add(Builders<User>.Filter.Regex(x => x.PersonalInformation.FirstName, regularExpression));
				searchFilters.Add(Builders<User>.Filter.Regex(x => x.PersonalInformation.LastName, regularExpression));
			}

			IMongoCollection<User> collection = GetCollection<User>(InternalConstants.CollectionNames.ConstUsersCollection);

			return collection.Find(Builders<User>.Filter.Or(searchFilters)).ToListAsync();
		}

		public User GetUserBySession(string authToken) {
			FilterDefinition<UserSession> sessionFilter = Builders<UserSession>.Filter.Where(
				s => s.AuthToken == authToken && s.ClosedDateTime == null
			);
			FilterDefinition<User> filter = Builders<User>.Filter.ElemMatch(u => u.Sessions, sessionFilter);

			return GetUserByFilter(filter);
		}

		public async Task<User> GetUserBySessionAsync(string authToken) {
			FilterDefinition<UserSession> sessionFilter = Builders<UserSession>.Filter.Where(
				s => s.AuthToken == authToken && s.ClosedDateTime == null
			);
			FilterDefinition<User> filter = Builders<User>.Filter.ElemMatch(u => u.Sessions, sessionFilter);

			return await GetUserByFilterAsync(filter).ConfigureAwait(false);
		}

		public User GetUserByUserIdAndSessionId(string userId, string authToken) {
			FilterDefinition<UserSession> sessionFilter = Builders<UserSession>.Filter.Where(
				s => s.AuthToken == authToken && s.ClosedDateTime == null
			);
			FilterDefinition<User> filter = Builders<User>.Filter.Eq(u => u.Id, userId) &
											Builders<User>.Filter.ElemMatch(u => u.Sessions, sessionFilter);

			return GetUserByFilter(filter);
		}

		public async Task<User> GetUserWithSessionByUserIdAndSessionIdAsync(string authToken, string userId) {
			IMongoCollection<User> users = GetCollection<User>(InternalConstants.CollectionNames.ConstUsersCollection);

			IMongoQueryable<User> query = users.AsQueryable()
				.Where(u => u.Id == userId)
				.SelectMany(u => u.Sessions, (u, s) => new User {
					Session = s,
					Id = u.Id,
					AccountId = u.AccountId,
					ContactId = u.ContactId,
					PersonalInformation = u.PersonalInformation,
					Roles = u.Roles,
					SessionUUID = u.SessionUUID,
					UserId = u.UserId,
					UserType = u.UserType,
					IsInactive = u.IsInactive,
					DisplaySettings = u.DisplaySettings
				}
				)
				.Where(u => u.Session.IsUserOnline && u.Session.AuthToken == authToken);

			User user = await query.SingleOrDefaultAsync().ConfigureAwait(false);

			if (user != null) {
				SetUserPermissions(user);
			}

			return user;
		}

		public async Task<User> GetDealerUserForCreditApplicationByContactAsync(int dealerContactId, string creditApplicationPassword) {
			FilterDefinition<User> filter = Builders<User>.Filter.And(
				Builders<User>.Filter.Eq(u => u.ContactId, dealerContactId),
				Builders<User>.Filter.Eq(u => u.PersonalInformation.CreditAppPassword, _encryptionProvider.Encrypt(creditApplicationPassword))
			);

			return await GetUserByFilterAsync(filter);
		}

		public async Task<User> GetDealerUserForCreditApplicationByAccountAsync(int accountId, string creditApplicationPassword) {
			FilterDefinition<User> filter = Builders<User>.Filter.And(
				Builders<User>.Filter.Eq(u => u.AccountId, accountId),
				Builders<User>.Filter.Eq(u => u.PersonalInformation.CreditAppPassword, _encryptionProvider.Encrypt(creditApplicationPassword))
			);

			return await GetUserByFilterAsync(filter);
		}

		public void AddUserSession(string userId, UserSession userSessionInfo) {
			FilterDefinition<User> filter = Builders<User>.Filter.Eq(u => u.Id, userId);

			UpdateDefinition<User> update = Builders<User>.Update.Push(u => u.Sessions, userSessionInfo);
			UpdateElement(InternalConstants.CollectionNames.ConstUsersCollection, filter, update);
		}

		public void CleanupUserSessions(string userId, int allowedInactivityInDays) {
			FilterDefinition<User> filter = Builders<User>.Filter.Eq(u => u.Id, userId);

			DateTime dateConsideredAsOld = DateTime.Now.AddDays(-allowedInactivityInDays);
			UpdateDefinition<User> update = Builders<User>.Update.PullFilter(
				user => user.Sessions,
				session => session.CreatedDateTime < dateConsideredAsOld && (
					session.IsUserOnline == false ||
					session.LastActivitiDateTime < dateConsideredAsOld
				)
			);
			UpdateElement(InternalConstants.CollectionNames.ConstUsersCollection, filter, update);
		}

		public void UpdateUserSession(string authToken, bool isUserOnline, DateTime? lastActivityDate, DateTime? closedDateTime) {
			FilterDefinition<User> filter = Builders<User>.Filter.Where(u => u.Sessions.Any(el => el.AuthToken == authToken));

			UpdateDefinition<User> updateDefinition = Builders<User>.Update.Set(u => u.Sessions[-1].IsUserOnline, isUserOnline);

			if (lastActivityDate.HasValue) {
				updateDefinition = Builders<User>.Update.Combine(updateDefinition, Builders<User>.Update.Set(u => u.Sessions[-1].LastActivitiDateTime, lastActivityDate.Value));
			}

			if (closedDateTime.HasValue) {
				updateDefinition = Builders<User>.Update.Combine(updateDefinition, Builders<User>.Update.Set(u => u.Sessions[-1].ClosedDateTime, closedDateTime.Value));
			}

			UpdateElement(InternalConstants.CollectionNames.ConstUsersCollection, filter, updateDefinition);
		}

		public async Task UpdateUserSessionAsync(string authToken, bool isUserOnline, DateTime? lastActivityDate, DateTime? closedDateTime) {
			FilterDefinition<User> filter = Builders<User>.Filter.Where(u => u.Sessions.Any(el => el.AuthToken == authToken));

			UpdateDefinition<User> updateDefinition = Builders<User>.Update.Set(u => u.Sessions[-1].IsUserOnline, isUserOnline);

			if (lastActivityDate.HasValue) {
				updateDefinition = Builders<User>.Update.Combine(updateDefinition, Builders<User>.Update.Set(u => u.Sessions[-1].LastActivitiDateTime, lastActivityDate.Value));
			}

			if (closedDateTime.HasValue) {
				updateDefinition = Builders<User>.Update.Combine(updateDefinition, Builders<User>.Update.Set(u => u.Sessions[-1].ClosedDateTime, closedDateTime.Value));
			}

			await UpdateElementAsync(InternalConstants.CollectionNames.ConstUsersCollection, filter, updateDefinition).ConfigureAwait(false);
		}

		public async Task InsertUserAsync(User user) {
			await InsertElementAsync<User>(InternalConstants.CollectionNames.ConstUsersCollection, user);
		}

		public void InsertUser(User user) {
			InsertElement<User>(InternalConstants.CollectionNames.ConstUsersCollection, user);
		}

		public async Task UpdateUserAsync(User user) {
			FilterDefinition<User> filter = Builders<User>.Filter.Eq(u => u.Id, user.Id);

			UpdateDefinition<User> updateDefinition = Builders<User>.Update.Combine(
				Builders<User>.Update.Set(u => u.PersonalInformation, user.PersonalInformation),
				Builders<User>.Update.Set(u => u.Roles, user.Roles),
				Builders<User>.Update.Set(u => u.DefaultApp, user.DefaultApp)
			);

			await UpsertElementAsync(InternalConstants.CollectionNames.ConstUsersCollection, filter, updateDefinition);
		}

		public async Task UpdateUserDisplaySettingsAsync(string userId, UserDisplaySettings displaySettings) {
			FilterDefinition<User> filter = Builders<User>.Filter.Eq(u => u.Id, userId);

			UpdateDefinition<User> updateDefinition = Builders<User>.Update.Set(u => u.DisplaySettings, displaySettings);

			await UpdateElementAsync(InternalConstants.CollectionNames.ConstUsersCollection, filter, updateDefinition);
		}

		public void UpdateUser(User user) {
			FilterDefinition<User> filter = Builders<User>.Filter.Eq(u => u.Id, user.Id);

			UpdateDefinition<User> updateDefinition = Builders<User>.Update.Combine(
				Builders<User>.Update.Set(u => u.PersonalInformation, user.PersonalInformation),
				Builders<User>.Update.Set(u => u.Roles, user.Roles),
				Builders<User>.Update.Set(u => u.DefaultApp, user.DefaultApp),
				Builders<User>.Update.Set(u => u.Storage, user.Storage)
			);

			UpsertElement(InternalConstants.CollectionNames.ConstUsersCollection, filter, updateDefinition);
		}

		public async Task DeleteUserAsync(string id) {
			await DeleteElementAsync(InternalConstants.CollectionNames.ConstUsersCollection, Builders<User>.Filter.Eq(u => u.Id, id));
		}

		public async Task CleanupUserRoleAsync(int userRole) {
			FilterDefinition<User> filter = Builders<User>.Filter.Empty;
			UpdateDefinition<User> updateDefinition = Builders<User>.Update.Pull(x => x.Roles, userRole);

			await UpdateManyElementsAsync(InternalConstants.CollectionNames.ConstUsersCollection, filter, updateDefinition);
		}

		public void DeleteUser(string id) {
			DeleteElement(InternalConstants.CollectionNames.ConstUsersCollection, Builders<User>.Filter.Eq(u => u.Id, id));
		}

		public async Task<(List<User>, long)> GetUserListingAsync(int pageSize, int pageNumber, string searchPhrase, AuthenticationEnums.UserTypeEnum userType) {
			IMongoCollection<User> collection = GetCollection<User>(InternalConstants.CollectionNames.ConstUsersCollection);
			FilterDefinition<User> filter = BuildUsersFilter(searchPhrase, userType, true);
			SortDefinition<User> sort = Builders<User>.Sort.Ascending(x => x.PersonalInformation.UserName);

			IFindFluent<User, User> findFluent = collection.Find(filter);

			long totalCount = await findFluent.CountDocumentsAsync();

			List<User> users = await findFluent.Sort(sort).Skip(pageSize * (pageNumber - 1)).Limit(pageSize).ToListAsync();

			return (users, totalCount);
		}

		public List<User> GetUsersListing(int pageSize, int pageNumber, string searchPhrase, AuthenticationEnums.UserTypeEnum userType) {
			IMongoCollection<User> collection = GetCollection<User>(InternalConstants.CollectionNames.ConstUsersCollection);
			FilterDefinition<User> filter = BuildUsersFilter(searchPhrase, userType, true);
			SortDefinition<User> sort = Builders<User>.Sort.Ascending(x => x.PersonalInformation.UserName);

			List<User> users = collection
				.Find(filter)
				.Sort(sort)
				.Skip(pageSize * (pageNumber - 1))
				.Limit(pageSize)
				.Project(x => new User() {
					Id = x.Id,
					UserType = x.UserType,
					PersonalInformation = new UserPersonalInformation() {
						UserName = x.PersonalInformation.UserName,
						FirstName = x.PersonalInformation.FirstName,
						LastName = x.PersonalInformation.LastName
					}
				}
				)
				.ToList();

			return users;
		}

		public async Task<List<User>> GetUsersByAccountIdsAsync(int accountId) {
			IMongoCollection<User> collection = GetCollection<User>(InternalConstants.CollectionNames.ConstUsersCollection);
			FilterDefinition<User> filter = Builders<User>.Filter.Eq(x => x.AccountId, accountId);

			return await collection.Find(filter).ToListAsync();
		}

		public async Task<List<User>> GetUsersByIdsAsync(List<string> userIds) {
			IMongoCollection<User> collection = GetCollection<User>(InternalConstants.CollectionNames.ConstUsersCollection);
			FilterDefinition<User> filter = Builders<User>.Filter.In(x => x.Id, userIds);

			return await collection.Find(filter).ToListAsync();
		}

		public long GetAllUsersCount(string searchPhrase, AuthenticationEnums.UserTypeEnum userType) {
			IMongoCollection<User> collection = GetCollection<User>(InternalConstants.CollectionNames.ConstUsersCollection);
			FilterDefinition<User> filter = BuildUsersFilter(searchPhrase, userType, true);

			return collection.Find(filter).CountDocuments();
		}

		public async Task<List<User>> GetUserSuggestionsAsync(string searchPhrase, AuthenticationEnums.UserTypeEnum userType) {
			IMongoCollection<User> collection = GetCollection<User>(InternalConstants.CollectionNames.ConstUsersCollection);
			FilterDefinition<User> filter = BuildUsersFilter(searchPhrase, userType, false);
			SortDefinition<User> sort = Builders<User>.Sort.Ascending(x => x.PersonalInformation.UserName);

			return await collection
				.Find(filter)
				.Sort(sort)
				.Project(x => new User() {
					Id = x.Id,
					PersonalInformation = new UserPersonalInformation() {
						UserName = x.PersonalInformation.UserName,
						FirstName = x.PersonalInformation.FirstName,
						LastName = x.PersonalInformation.LastName
					}
				}
				)
				.ToListAsync();
		}

		public List<User> GetUserSuggestions(string searchPhrase, AuthenticationEnums.UserTypeEnum userType) {
			IMongoCollection<User> collection = GetCollection<User>(InternalConstants.CollectionNames.ConstUsersCollection);
			FilterDefinition<User> filter = BuildUsersFilter(searchPhrase, userType, false);
			SortDefinition<User> sort = Builders<User>.Sort.Ascending(x => x.PersonalInformation.UserName);

			return collection
				.Find(filter)
				.Sort(sort)
				.Project(x => new User() {
					Id = x.Id,
					PersonalInformation = new UserPersonalInformation() {
						UserName = x.PersonalInformation.UserName,
						FirstName = x.PersonalInformation.FirstName,
						LastName = x.PersonalInformation.LastName
					}
				}
				)
				.ToList();
		}

		public async Task UpdateUsersInactiveStatusAsync(int accountId, bool isInactive) {
			IMongoCollection<User> collection = GetCollection<User>(InternalConstants.CollectionNames.ConstUsersCollection);
			List<string> userIds = collection.Find(x => x.AccountId == accountId && x.UserType == AuthenticationEnums.UserTypeEnum.CPUser).Project(x => x.Id).ToList();

			if (userIds.Count > 0) {
				UpdateDefinition<User> updateDefinition = Builders<User>.Update.Set(x => x.IsInactive, isInactive);
				collection.UpdateMany(x => userIds.Contains(x.Id), updateDefinition);

				//find all active sessions
				List<UnwindedUserSessions> activeSessions = collection
					.Aggregate()
					.Match(x => userIds.Contains(x.Id))
					.Unwind<User, UnwindedUserSessions>(x => x.Sessions)
					.Match(x => x.Sessions.ClosedDateTime == null)
					.ToList();

				if (activeSessions.Count > 0) {
					List<WriteModel<User>> bulkUpdate = new List<WriteModel<User>>();
					foreach (UnwindedUserSessions session in activeSessions) {
						bulkUpdate.Add(
							new UpdateOneModel<User>(
								Builders<User>.Filter.Where(x => x.Id == session.Id && x.Sessions.Any(s => s.AuthToken == session.Sessions.AuthToken)),
								Builders<User>
									.Update
									.Set(x => x.Sessions[-1].ClosedDateTime, DateTime.Now)
							)
						);
					}

					await collection.BulkWriteAsync(bulkUpdate);
				}
			}
		}

		public void UpdateUsersInactiveStatus(int accountId, bool isInactive) {
			IMongoCollection<User> collection = GetCollection<User>(InternalConstants.CollectionNames.ConstUsersCollection);
			List<string> userIds = collection.Find(x => x.AccountId == accountId && x.UserType == AuthenticationEnums.UserTypeEnum.CPUser).Project(x => x.Id).ToList();

			if (userIds.Count > 0) {
				UpdateDefinition<User> updateDefinition = Builders<User>.Update.Set(x => x.IsInactive, isInactive);
				collection.UpdateMany(x => userIds.Contains(x.Id), updateDefinition);

				//find all active sessions
				List<UnwindedUserSessions> activeSessions = collection
					.Aggregate()
					.Match(x => userIds.Contains(x.Id))
					.Unwind<User, UnwindedUserSessions>(x => x.Sessions)
					.Match(x => x.Sessions.ClosedDateTime == null)
					.ToList();

				if (activeSessions.Count > 0) {
					List<WriteModel<User>> bulkUpdate = new List<WriteModel<User>>();
					foreach (UnwindedUserSessions session in activeSessions) {
						bulkUpdate.Add(
							new UpdateOneModel<User>(
								Builders<User>.Filter.Where(x => x.Id == session.Id && x.Sessions.Any(s => s.AuthToken == session.Sessions.AuthToken)),
								Builders<User>
									.Update
									.Set(x => x.Sessions[-1].ClosedDateTime, DateTime.Now)
							)
						);
					}

					collection.BulkWrite(bulkUpdate);
				}
			}
		}

		public async Task DeleteUsersByAccountIdAsync(int accountId) {
			FilterDefinition<User> filter = Builders<User>.Filter.Where(x => x.AccountId == accountId && x.UserType == AuthenticationEnums.UserTypeEnum.CPUser);

			await DeleteManyElementsAsync(InternalConstants.CollectionNames.ConstUsersCollection, filter);
		}

		public void DeleteUsers(int accountId) {
			FilterDefinition<User> filter = Builders<User>.Filter.Where(x => x.AccountId == accountId && x.UserType == AuthenticationEnums.UserTypeEnum.CPUser);

			DeleteManyElements(InternalConstants.CollectionNames.ConstUsersCollection, filter);
		}

		#region Private helpers
		private User GetUserByFilter(FilterDefinition<User> filter) {
			IMongoCollection<User> collection = GetCollection<User>(InternalConstants.CollectionNames.ConstUsersCollection);

			User user = collection.Find(filter).Project<User>(GetUserProjection()).FirstOrDefault();

			if (user != null) {
				SetUserPermissions(user);
			}

			return user;
		}

		private async Task<User> GetUserByFilterAsync(FilterDefinition<User> filter) {
			IMongoCollection<User> collection = GetCollection<User>(InternalConstants.CollectionNames.ConstUsersCollection);

			User user = await collection.Find(filter).Project<User>(GetUserProjection()).FirstOrDefaultAsync().ConfigureAwait(false);

			if (user != null) {
				SetUserPermissions(user);
			}

			return user;
		}

		private ProjectionDefinition<User> GetUserProjection() {
			return Builders<User>.Projection.Exclude(x => x.Sessions);
		}

		private void SetUserPermissions(User user) {
			IMongoCollection<Role> roleCollection = GetCollection<Role>(InternalConstants.CollectionNames.ConstRolesCollection);
			FilterDefinition<Role> rolesFilter = Builders<Role>.Filter.In(f => f.RoleType, user.Roles);
			IEnumerable<AuthenticationEnums.UserPermissionEnum> permissions = roleCollection.Find(rolesFilter).Project(x => x.Permissions).ToList().SelectMany(d => d);
			AuthenticationEnums.UserPermissionEnum userPermissons = 0;
			foreach (AuthenticationEnums.UserPermissionEnum permission in permissions) {
				userPermissons = userPermissons | permission;
			}

			user.Permissions = userPermissons;
		}

		private FilterDefinition<User> BuildUsersFilter(string searchPhrase, AuthenticationEnums.UserTypeEnum userType, bool isExactMatchRequired) {
			FilterDefinition<User> filter = Builders<User>.Filter.Where(x => x.UserType == userType);

			if (!string.IsNullOrEmpty(searchPhrase)) {
				BsonRegularExpression filterRegex = isExactMatchRequired
					? new BsonRegularExpression(ConstRegexStart + Regex.Escape(searchPhrase) + ConstRegexEnd, ConstRegexCaseInsensetiveOption)
					: new BsonRegularExpression(ConstRegexStart + Regex.Escape(searchPhrase), ConstRegexCaseInsensetiveOption);

				filter &= Builders<User>.Filter.Or(
					Builders<User>.Filter.Regex(
						x => x.PersonalInformation.UserName,
						filterRegex
					),
					Builders<User>.Filter.Regex(
						x => x.PersonalInformation.FirstName,
						filterRegex
					),
					Builders<User>.Filter.Regex(
						x => x.PersonalInformation.LastName,
						filterRegex
					)
				);
			}

			return filter;
		}
		#endregion
		#endregion
	}
}