<template>
  <b-modal
    size="lg"
    title="End eBay Listing Early"
    :visible="isVisible"
    @hide="onHide"
  >
    <div v-if="item && isEnabledToEnd">
      <vehicleDescription class="mb-2" :vehicle="item"/>
      <span>Please confirm that you would like to end this eBay listing early.</span>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Listing ID:</span>
        <b-link slot="payload" :href="getAuctionUrl(item.AuctionId)"><u>{{item.AuctionId}}</u></b-link>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Start Time / Duration:</span>
        <span slot="payload">{{getStartTimeAndDurationDesc()}}</span>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Time Remaining:</span>
        <span slot="payload">{{getTimeRemainingDesc()}}</span>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Bid Status:</span>
        <span slot="payload">{{item.TotalBids > 0 ? item.TotalBids : 'No Bids'}}<span v-if="item.TotalBids > 0">(<b-link :href="getViewBidsUrl(item)"><u>View Bid History</u></b-link>)</span></span>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Current High Bid:</span>
        <span slot="payload">{{getHighBidDesc(item)}}</span>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Current Reserve:</span>
        <span slot="payload">{{getPriceText(item.ReservePrice)}}</span>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Reason:</span>
        <b-form-select slot="payload" v-model="endAuctionReason" :options="getReasonOptions"></b-form-select>
      </detail-row>
      <span>This process will end your live listing on eBay Motors.</span>
    </div>
    <div v-else-if="item && item.TotalOffers > 0">
      <b-link :href="getOfferUrl"><u>You have {{item.TotalOffers}} {{item.TotalOffers === 1 ? 'Offer' : 'Offers'}} Pending on this listing.</u></b-link>
      <br/>
      <span>Based on eBay's Rules for Ending Listings, you must first decline any pending offers on this listing.</span>
      <br/>
      <b-btn variant="primary" @click="declineAllPendingOffers">Decline All Pending Offers</b-btn>
    </div>
    <div v-else-if="item && item.TotalBids > 0 && isUnderTwentyHours">
      <b-alert show variant="info">eBay does not allow a listing to be ended early if the listing has less than 12 hours remaining and has bids.</b-alert>
    </div>
    <div v-else-if="item && item.TotalBids > 0 && item.HighBid >= item.ReservePrice && !isUnderTwentyHours">
      <span>You have <b>{{item.TotalBids === 1 ? `${item.TotalBids} Bid` : `${item.TotalBids} Bids`}}</b> on this Listing</span><br/>
      <span>The Highest Bid of {{getPriceText(item.HighBid)}}, has met reserve price.</span><br/>
      <span>Based on eBay's Rules for Ending Listings, You May Sell the Vehicle to the High Bidder using This Process or may cancel bids and end the listing.</span><br/>
      <b-btn variant="primary" @click="sellVehicleToHighBidder">Sell Vehicle to The High Bidder</b-btn>
    </div>
    <div v-else class="py-4">
      <loader size="lg"/>
    </div>
    <div v-if="item" class="mt-2">
      <span>
        Please click here for more information on
        <b-link :href="endEarlyInfoUrl" class="text-primary" target="_blank"><u>eBay's policy for ending listings early.</u></b-link>
      </span>
    </div>
    <template #modal-footer>
      <b-btn variant="primary" @click="endAuction" :disabled="!item">End eBay Listing Early</b-btn>
    </template>
  </b-modal>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import vehicleDescription from '@/components/eBay/helpers/vehicleDescription'
import loader from '@/components/_shared/loader'
import constants from '@/shared/ebay/constants'
import numeral from 'numeral'
import moment from 'moment'

export default {
  name: 'ebay-end-auction-modal',
  props: {
    isVisible: Boolean,
    auctionId: { type: String, required: true },
    accountId: { type: Number, required: true }
  },
  components: {
    detailRow,
    vehicleDescription,
    loader
  },
  data () {
    return {
      endEarlyInfoUrl: constants.eBayInfoUrls.endEarlyInfo,
      endAuctionReason: null,
      item: null
    }
  },
  mounted () {
    this.populateData()
  },
  computed: {
    getReasonOptions () {
      return Array.concat([{value: null, text: 'Select a reason'}], constants.eBayAuctionEndReasonOptions)
    },
    isReservePriceMet () {
      return this.item && this.item.HighBid > this.item.ReservePrice
    },
    isEnabledToEnd () {
      return this.item && ((this.item.TotalOffers === 0 && !this.isUnderTwentyHours) || this.item.IsFixedPrice) && this.item.HighBid <= this.item.ReservePrice
    },
    isUnderTwentyHours () {
      return this.item && moment(this.item.EndDateTime).diff(moment(), 'minutes') < 721
    },
    getOfferUrl () {
      return `/ebay/${this.accountId}/inventory/${this.auctionId}/offers`
    }
  },
  methods: {
    getAuctionUrl (value) {
      return constants.eBayInfoUrls.ebayItemUrl(value)
    },
    getStartTimeAndDurationDesc () {
      let date = moment(this.item.StartDateTime).format('MM/DD/YYYY')
      let time = moment(this.item.StartDateTime).format('hh:mm A')
      let duration = this.item.AuctionDuration
      return `${date} | ${time} | ${duration} Days`
    },
    getTimeRemainingDesc () {
      let duration = moment.duration(moment(this.item.EndDateTime).diff(moment()))
      let days = duration.asDays()
      let hours = duration.asHours() - parseInt(duration.asHours() / 24) * 24
      return `${parseInt(days)}d ${parseInt(hours)}h`
    },
    getHighBidDesc (item) {
      if (item.HighBid) {
        let percent = (item.HighBid / item.ReservePrice) * 100

        return `${numeral(item.HighBid).format('$0,0')} (${parseInt(percent)}%)`
      }

      return 'N/A'
    },
    getPriceText (price) {
      if (price && price > 0) {
        return numeral(price).format('$0,0')
      }
      return 'N/A'
    },
    getViewBidsUrl (item) {
      return constants.eBayInfoUrls.ebayViewBids(item.AuctionId)
    },
    endAuction () {
      let apiData = {
        auctionId: this.item.AuctionId,
        EndReasonCode: this.endAuctionReason
      }

      this.$store.dispatch('eBay/endAuction', { accountId: this.accountId, auctionId: this.item.AuctionId, data: apiData }).then(res => {
        this.$toaster.success('Ended Auction Successfully')
      }).catch(ex => {
        this.$toaster.error('Something went wrong!')
        this.$logger.handleError(ex, 'Cannot end auction')
      }).finally(() => {
        this.onHide()
        this.$router.go()
      })
    },
    populateData () {
      this.$store.dispatch('eBay/getAuctionToEnd', { accountId: this.accountId, auctionId: this.auctionId }).then(res => {
        this.item = res.data
      }).catch(ex => {
        this.$toaster.error('Something went wrong!')
        this.$logger.handleError(ex, 'Cannot get auction to end')
        this.onHide()
      })
    },
    declineAllPendingOffers () {
      let apiData = {
        AccountId: this.accountId,
        AuctionId: this.auctionId
      }
      this.$store.dispatch('eBay/declineBestOffers', { accountId: this.accountId, auctionId: this.auctionId, data: apiData }).then(res => {
        this.$toaster.success('Declined All Pending Best Offers Successfully')
      }).catch(ex => {
        this.$toaster.error('Something went wrong!')
        this.$logger.handleError(ex, 'Exception occurred on decline all pending best offers')
      }).finally(() => {
        this.onHide()
        this.$router.go()
      })
    },
    sellVehicleToHighBidder () {
      let apiData = {
        AccountId: this.accountId,
        AuctionId: this.auctionId
      }
      this.$store.dispatch('eBay/sellVehicleToHighBidder', { accountId: this.accountId, auctionId: this.auctionId, data: apiData }).then(res => {
        this.$toaster.success('Sold Vehicle to the High Bidder Successfully')
      }).catch(ex => {
        this.$toaster.error('Something went wrong!')
        this.$logger.handleError(ex, 'Exception occurred on sell vehicle to the high bidder')
      }).finally(() => {
        this.onHide()
        this.$router.go()
      })
    },
    onHide () {
      this.item = null
      this.$emit('hide')
    }
  },
  watch: {
    auctionId: {
      deep: true,
      handler: function () {
        this.populateData()
      }
    },
    isVisible: {
      deep: true,
      handler: function () {
        if (!this.item && this.isVisible) {
          this.populateData()
        }
      }
    }
  }
}
</script>
