<template>
<ValidationObserver ref="validator">
  <b-card>
    <b-row class="border-bottom mb-2 py-2">
      <b-col>
        <strong>{{reviseHeader}}</strong>
      </b-col>
      <c-button v-if="!isDisabled" :message="`Are you sure you want ${btnDesc}?`" variant="primary" size="sm" @confirm="onConfirm">
        {{btnDesc}}
      </c-button>
      <loader class="mr-5" v-else size="sm"/>
    </b-row>
    <detail-row bootstrapMode :fixed-payload-width="true">
      <span slot="title">Listing ID:</span>
      <b-link slot="payload" :href="getListingUrl" class="text-info"><u>{{revise.AuctionId}}</u></b-link>
    </detail-row>
    <detail-row bootstrapMode :fixed-payload-width="true">
      <span slot="title">Current Starting Bid:</span>
      <span slot="payload">{{getPriceDesc(revise.StartPrice)}}</span>
    </detail-row>
    <ValidationProvider name="New Starting Bid" :rules="{is_not: `${revise.StartPrice}`, required: true}" v-slot="{errors}">
    <detail-row bootstrapMode title-position="start" :fixed-payload-width="true" :error="errors[0]">
      <span slot="title">New Starting Bid:</span>
      <b-form-input slot="payload" name="New_Starting_Bid" v-model="changeStartingBid.NewStartingBid" type="number"></b-form-input>
    </detail-row>
    </ValidationProvider>
  </b-card>
</ValidationObserver>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import constants from '@/shared/ebay/constants'
import loader from '@/components/_shared/loader'
import {mapGetters} from 'vuex'
import numeral from 'numeral'

export default {
  props: {
    btnDesc: {
      type: String,
      default: 'Revise'
    },
    reviseHeader: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      changeStartingBid: {
        NewStartingBid: null
      },
      isDisabled: false
    }
  },
  components: {
    detailRow,
    loader
  },
  computed: {
    ...mapGetters('eBayRevise', ['revise']),
    getListingUrl () {
      return constants.eBayInfoUrls.ebayItemUrl(this.revise.AuctionId)
    }
  },
  methods: {
    onConfirm () {
      this.$refs.validator.validate().then(res => {
        if (res) {
          this.isDisabled = true
          let apiParams = {
            accountId: this.revise.AccountId,
            auctionId: this.revise.AuctionId,
            data: this.changeStartingBid
          }

          this.$store.dispatch('eBayRevise/changeStartingBid', apiParams).then(res => {
            this.$toaster.success('Changed Starting Bid Successfully')
          }).catch(ex => {
            this.$toaster.exception(ex, 'Something went wrong!')
            if (ex.response && ex.response.status !== 400) {
              this.$logger.handleError(ex, `Exception occurred on ${this.reviseHeader}`)
            }
          }).finally(() => {
            this.isDisabled = false
            setTimeout(() => this.$router.go(), 4000)
          })
        }
      })
    },
    getPriceDesc (value) {
      return numeral(value).format('$0,0')
    }
  }
}
</script>
