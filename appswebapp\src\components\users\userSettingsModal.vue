<template>
  <b-modal
    title="User Display Settings"
    :visible="visible"
    @hide="hide"
  >
    <detail-row :fixed-payload-width="true">
      <b-form-checkbox slot="payload" v-model="displaySettings.hasToDisplayDemoAccounts" class="text-muted">Has to display demo accounts</b-form-checkbox>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <b-form-checkbox slot="payload" v-model="displaySettings.hasToDisplayUnfinishedLogs" class="text-muted">Has to display unfinished logs</b-form-checkbox>
    </detail-row>
    <template #modal-footer>
      <b-btn size="sm" @click="hide">Close</b-btn>
      <b-btn variant="primary" size="sm" @click="save">Save</b-btn>
    </template>
  </b-modal>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import { mapGetters } from 'vuex'
import UserManagementService from '@/services/users/UserManagementService'

export default {
  props: {
    visible: {type: Boolean, required: true}
  },
  data () {
    return {
      displaySettings: {}
    }
  },
  components: {
    detailRow
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {
        displaySettings: {}
      }
    }
  },
  mounted () {
    this.initData()
  },
  methods: {
    hide () {
      this.$emit('hide')
    },
    save () {
      UserManagementService.updateUserDisplaySettings(this.displaySettings).then(res => {
        this.$toaster.success('User Display Settings Updated Successfully', {timeout: 5000})
      }).catch(ex => {
        this.$toaster.exception(ex, 'Failed to Update User Display Settings', {timeout: 7000})
      })
      this.hide()
    },
    initData () {
      this.displaySettings = this.user.displaySettings || {}
    }
  }
}
</script>
