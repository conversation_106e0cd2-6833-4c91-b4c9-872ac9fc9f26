import axios from 'axios'

export default function setup () {
  axios.interceptors.request.use(function (config) {
    const getTrimmedData = obj => {
      if (obj && typeof obj === 'object') {
        Object.keys(obj).map(key => {
          if (typeof obj[key] === 'object') {
            getTrimmedData(obj[key])
          } else if (typeof obj[key] === 'string') {
            obj[key] = obj[key].trim()
          }
        })
      }
      return obj
    }

    config.headers = {
      ...config.headers
    }
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        timeStamp: new Date().getTime()
      }
    }

    if (config.method === 'post' || config.method === 'put') {
      if (config.data) {
        config.data = getTrimmedData(config.data)
      }
    }

    return config
  }, function (err) {
    return Promise.reject(err)
  })

  axios.interceptors.response.use(function (response) {
    if (response && (response.status === 401 || response.status === 302)) {
      window.location.href = window.location.origin
    } else {
      return response
    }
  }, function (err) {
    return Promise.reject(err)
  })
}
