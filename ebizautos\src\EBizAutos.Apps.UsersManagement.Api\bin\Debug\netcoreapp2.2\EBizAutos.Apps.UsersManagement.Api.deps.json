{"runtimeTarget": {"name": ".NETCoreApp,Version=v2.2", "signature": ""}, "compilationOptions": {"defines": ["TRACE", "DEBUG", "NETCOREAPP", "NETCOREAPP2_2", "NETCOREAPP1_0_OR_GREATER", "NETCOREAPP1_1_OR_GREATER", "NETCOREAPP2_0_OR_GREATER", "NETCOREAPP2_1_OR_GREATER", "NETCOREAPP2_2_OR_GREATER"], "languageVersion": "7.3", "platform": "", "allowUnsafe": false, "warningsAsErrors": false, "optimize": false, "keyFile": "", "emitEntryPoint": true, "xmlDoc": false, "debugType": "portable"}, "targets": {".NETCoreApp,Version=v2.2": {"EBizAutos.Apps.UsersManagement.Api/1.0.0": {"dependencies": {"Coravel": "3.6.1", "EBizAutos.Apps.Authentication.CommonLib": "1.0.0", "EBizAutos.Apps.Authentication.MongoDbRepository": "1.0.0", "EBizAutos.Apps.Common.MongoDbRepository": "1.0.0", "EBizAutos.Apps.CommonLib": "1.0.0", "EBizAutos.Apps.MongoDbRepository": "1.0.0", "EBizAutos.Apps.ServiceBus": "1.0.0", "EBizAutos.Apps.Users.MongoDbRepository": "1.0.0", "EBizAutos.Apps.Users.MsSqlRepository": "1.0.0", "Microsoft.AspNetCore.App": "2.2.0", "Microsoft.AspNetCore.Hosting.WindowsServices": "2.2.0", "Microsoft.AspNetCore.Razor.Design": "2.2.0", "Microsoft.NETCore.App": "2.2.0", "CommonLibCore.Reference": "*******", "FoundationCommonLib.Reference": "*******"}, "runtime": {"EBizAutos.Apps.UsersManagement.Api.dll": {}}, "compile": {"EBizAutos.Apps.UsersManagement.Api.dll": {}}}, "Amazon.AspNetCore.DataProtection.SSM/1.1.0": {"dependencies": {"AWSSDK.Extensions.NETCore.Setup": "3.7.1", "AWSSDK.SimpleSystemsManagement": "********", "Microsoft.AspNetCore.DataProtection.Extensions": "2.2.0"}, "runtime": {"lib/netstandard2.0/Amazon.AspNetCore.DataProtection.SSM.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Amazon.AspNetCore.DataProtection.SSM.dll": {}}}, "Amazon.Extensions.Configuration.SystemsManager/2.1.1": {"dependencies": {"AWSSDK.Extensions.NETCore.Setup": "3.7.1", "AWSSDK.SimpleSystemsManagement": "********", "Microsoft.Extensions.Configuration": "2.2.0", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Amazon.Extensions.Configuration.SystemsManager.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Amazon.Extensions.Configuration.SystemsManager.dll": {}}}, "Apache.NMS/2.0.0": {"runtime": {"lib/netstandard2.0/Apache.NMS.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Apache.NMS.dll": {}}}, "Apache.NMS.ActiveMQ/2.0.0": {"dependencies": {"Apache.NMS": "2.0.0", "SharpZipLib": "1.3.3", "System.Runtime": "4.3.1"}, "runtime": {"lib/netstandard2.0/Apache.NMS.ActiveMQ.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Apache.NMS.ActiveMQ.dll": {}}}, "Automatonymous/5.1.3": {"dependencies": {"GreenPipes": "4.0.1"}, "runtime": {"lib/netstandard2.0/Automatonymous.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Automatonymous.dll": {}}}, "AWSSDK.Core/**********": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0"}, "runtime": {"lib/netstandard2.0/AWSSDK.Core.dll": {"assemblyVersion": "*******", "fileVersion": "**********"}}, "compile": {"lib/netstandard2.0/AWSSDK.Core.dll": {}}}, "AWSSDK.Extensions.NETCore.Setup/3.7.1": {"dependencies": {"AWSSDK.Core": "**********", "Microsoft.Extensions.Configuration.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0"}, "runtime": {"lib/netstandard2.0/AWSSDK.Extensions.NETCore.Setup.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/AWSSDK.Extensions.NETCore.Setup.dll": {}}}, "AWSSDK.S3/**********": {"dependencies": {"AWSSDK.Core": "**********"}, "runtime": {"lib/netstandard2.0/AWSSDK.S3.dll": {"assemblyVersion": "*******", "fileVersion": "**********"}}, "compile": {"lib/netstandard2.0/AWSSDK.S3.dll": {}}}, "AWSSDK.SecurityToken/3.7.100.14": {"dependencies": {"AWSSDK.Core": "**********"}, "runtime": {"lib/netstandard2.0/AWSSDK.SecurityToken.dll": {"assemblyVersion": "*******", "fileVersion": "3.7.100.14"}}, "compile": {"lib/netstandard2.0/AWSSDK.SecurityToken.dll": {}}}, "AWSSDK.SimpleSystemsManagement/********": {"dependencies": {"AWSSDK.Core": "**********"}, "runtime": {"lib/netstandard2.0/AWSSDK.SimpleSystemsManagement.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}, "compile": {"lib/netstandard2.0/AWSSDK.SimpleSystemsManagement.dll": {}}}, "Castle.Core/4.4.1": {"dependencies": {"NETStandard.Library": "2.0.3", "System.Collections.Specialized": "4.3.0", "System.ComponentModel": "4.3.0", "System.ComponentModel.TypeConverter": "4.3.0", "System.Diagnostics.TraceSource": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.TypeExtensions": "4.4.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard1.5/Castle.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard1.5/Castle.Core.dll": {}}}, "CompareNETObjects/4.57.0": {"dependencies": {"Microsoft.CSharp": "4.5.0"}, "runtime": {"lib/netstandard2.0/KellermanSoftware.Compare-NET-Objects.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "compile": {"lib/netstandard2.0/KellermanSoftware.Compare-NET-Objects.dll": {}}}, "Coravel/3.6.1": {"dependencies": {"Microsoft.Extensions.Caching.Memory": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0"}, "runtime": {"lib/netstandard2.0/Coravel.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Coravel.dll": {}}}, "Dapper/1.50.5": {"dependencies": {"System.Data.SqlClient": "4.6.0", "System.Reflection.Emit.Lightweight": "4.7.0", "System.Reflection.TypeExtensions": "4.4.0"}, "runtime": {"lib/netstandard2.0/Dapper.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "compile": {"lib/netstandard2.0/Dapper.dll": {}}}, "DeepCloner/0.10.2": {"dependencies": {"NETStandard.Library": "2.0.3"}, "runtime": {"lib/netstandard1.3/DeepCloner.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "compile": {"lib/netstandard1.3/DeepCloner.dll": {}}}, "DnsClient/1.6.1": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0", "System.Buffers": "4.5.1"}, "runtime": {"lib/netstandard2.0/DnsClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/DnsClient.dll": {}}}, "Elasticsearch.Net/7.1.0": {"dependencies": {"System.Buffers": "4.5.1", "System.Diagnostics.DiagnosticSource": "4.7.1", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.Lightweight": "4.7.0"}, "runtime": {"lib/netstandard2.0/Elasticsearch.Net.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.1.0.0"}}, "compile": {"lib/netstandard2.0/Elasticsearch.Net.dll": {}}}, "EnyimMemcachedCore/2.1.8": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Caching.Abstractions": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "2.2.0", "Newtonsoft.Json": "13.0.1", "Newtonsoft.Json.Bson": "1.0.2"}, "runtime": {"lib/netstandard2.0/EnyimMemcachedCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/EnyimMemcachedCore.dll": {}}}, "Experimental.System.Messaging/1.0.0": {"runtime": {"lib/netstandard2.0/Experimental.System.Messaging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Experimental.System.Messaging.dll": {}}}, "GreenPipes/4.0.1": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "System.Reflection.Emit": "4.7.0"}, "runtime": {"lib/netstandard2.0/GreenPipes.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/GreenPipes.dll": {}}}, "Magick.NET-Q16-AnyCPU/7.11.0": {"runtime": {"lib/netstandard20/Magick.NET-Q16-AnyCPU.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "runtimeTargets": {"runtimes/linux-x64/native/Magick.NET-Q16-x64.Native.dll.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/Magick.NET-Q16-x64.Native.dll.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/Magick.NET-Q16-x64.Native.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "********"}, "runtimes/win-x86/native/Magick.NET-Q16-x86.Native.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "********"}}, "compile": {"lib/netstandard20/Magick.NET-Q16-AnyCPU.dll": {}}}, "Mapster/4.1.1": {"dependencies": {"Microsoft.CSharp": "4.5.0"}, "runtime": {"lib/netstandard2.0/Mapster.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Mapster.dll": {}}}, "MassTransit/7.3.1": {"dependencies": {"Automatonymous": "5.1.3", "Microsoft.Bcl.AsyncInterfaces": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "NewId": "3.0.3", "Newtonsoft.Json": "13.0.1", "Newtonsoft.Json.Bson": "1.0.2", "System.Diagnostics.DiagnosticSource": "4.7.1", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.Lightweight": "4.7.0", "System.Text.Json": "5.0.2", "System.Threading.Channels": "4.7.1", "System.ValueTuple": "4.5.0"}, "runtime": {"lib/netstandard2.0/MassTransit.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/MassTransit.dll": {}}}, "MassTransit.ActiveMQ/7.3.3": {"dependencies": {"Apache.NMS.ActiveMQ": "2.0.0", "MassTransit": "7.3.1"}, "runtime": {"lib/netstandard2.0/MassTransit.ActiveMqTransport.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/MassTransit.ActiveMqTransport.dll": {}}}, "MassTransit.Extensions.DependencyInjection/7.3.1": {"dependencies": {"MassTransit": "7.3.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0"}, "runtime": {"lib/netstandard2.0/MassTransit.ExtensionsDependencyInjectionIntegration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/MassTransit.ExtensionsDependencyInjectionIntegration.dll": {}}}, "MassTransit.MongoDb/7.3.1": {"dependencies": {"MassTransit": "7.3.1", "MongoDB.Driver": "2.21.0", "MongoDB.Driver.GridFS": "2.21.0"}, "runtime": {"lib/netstandard2.0/MassTransit.MongoDbIntegration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/MassTransit.MongoDbIntegration.dll": {}}}, "Microsoft.AspNetCore.Hosting.WindowsServices/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting": "2.2.0", "System.ServiceProcess.ServiceController": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.WindowsServices.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.WindowsServices.dll": {}}}, "Microsoft.Bcl.AsyncInterfaces/5.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "compile": {"lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll": {}}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "compile": {"ref/netstandard2.0/Microsoft.Win32.Registry.dll": {}}}, "Microsoft.Win32.SystemEvents/4.5.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "MongoDB.Bson/2.21.0": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "runtime": {"lib/netstandard2.0/MongoDB.Bson.dll": {"assemblyVersion": "2.21.0.0", "fileVersion": "2.21.0.0"}}, "compile": {"lib/netstandard2.0/MongoDB.Bson.dll": {}}}, "MongoDB.Driver/2.21.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "2.2.0", "MongoDB.Bson": "2.21.0", "MongoDB.Driver.Core": "2.21.0", "MongoDB.Libmongocrypt": "1.8.0"}, "runtime": {"lib/netstandard2.0/MongoDB.Driver.dll": {"assemblyVersion": "2.21.0.0", "fileVersion": "2.21.0.0"}}, "compile": {"lib/netstandard2.0/MongoDB.Driver.dll": {}}}, "MongoDB.Driver.Core/2.21.0": {"dependencies": {"AWSSDK.SecurityToken": "3.7.100.14", "DnsClient": "1.6.1", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "MongoDB.Bson": "2.21.0", "MongoDB.Libmongocrypt": "1.8.0", "SharpCompress": "0.30.1", "Snappier": "1.0.0", "System.Buffers": "4.5.1", "ZstdSharp.Port": "0.6.2"}, "runtime": {"lib/netstandard2.0/MongoDB.Driver.Core.dll": {"assemblyVersion": "2.21.0.0", "fileVersion": "2.21.0.0"}}, "compile": {"lib/netstandard2.0/MongoDB.Driver.Core.dll": {}}}, "MongoDB.Driver.GridFS/2.21.0": {"dependencies": {"MongoDB.Bson": "2.21.0", "MongoDB.Driver": "2.21.0", "MongoDB.Driver.Core": "2.21.0"}, "runtime": {"lib/netstandard2.0/MongoDB.Driver.GridFS.dll": {"assemblyVersion": "2.21.0.0", "fileVersion": "2.21.0.0"}}, "compile": {"lib/netstandard2.0/MongoDB.Driver.GridFS.dll": {}}}, "MongoDB.Libmongocrypt/1.8.0": {"runtime": {"lib/netstandard2.0/MongoDB.Libmongocrypt.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}}, "runtimeTargets": {"runtimes/linux/native/libmongocrypt.so": {"rid": "linux", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx/native/libmongocrypt.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win/native/mongocrypt.dll": {"rid": "win", "assetType": "native", "fileVersion": "0.0.0.0"}}, "compile": {"lib/netstandard2.0/MongoDB.Libmongocrypt.dll": {}}}, "MongolianBarbecue/1.0.0": {"dependencies": {"MongoDB.Driver": "2.21.0"}, "runtime": {"lib/netstandard2.0/MongolianBarbecue.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/MongolianBarbecue.dll": {}}}, "NEST/7.1.0": {"dependencies": {"Elasticsearch.Net": "7.1.0"}, "runtime": {"lib/netstandard2.0/Nest.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.1.0.0"}}, "compile": {"lib/netstandard2.0/Nest.dll": {}}}, "NewId/3.0.3": {"runtime": {"lib/netstandard2.0/NewId.dll": {"assemblyVersion": "3.0.3.0", "fileVersion": "3.0.3.0"}}, "compile": {"lib/netstandard2.0/NewId.dll": {}}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.1.25517"}}, "compile": {"lib/netstandard2.0/Newtonsoft.Json.dll": {}}}, "Newtonsoft.Json.Bson/1.0.2": {"dependencies": {"Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.2.22727"}}, "compile": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {}}}, "Scrutor/3.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyModel": "2.1.0"}, "runtime": {"lib/netstandard2.0/Scrutor.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Scrutor.dll": {}}}, "SharpCompress/0.30.1": {"dependencies": {"System.Memory": "4.5.5", "System.Text.Encoding.CodePages": "5.0.0"}, "runtime": {"lib/netstandard2.0/SharpCompress.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "compile": {"lib/netstandard2.0/SharpCompress.dll": {}}}, "SharpZipLib/1.3.3": {"runtime": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "compile": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {}}}, "Snappier/1.0.0": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Snappier.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Snappier.dll": {}}}, "Swashbuckle.AspNetCore/4.0.1": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "4.0.1", "Swashbuckle.AspNetCore.SwaggerGen": "4.0.1", "Swashbuckle.AspNetCore.SwaggerUI": "4.0.1"}, "runtime": {"lib/netstandard2.0/Swashbuckle.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Swashbuckle.AspNetCore.dll": {}}}, "Swashbuckle.AspNetCore.Annotations/4.0.1": {"dependencies": {"Swashbuckle.AspNetCore.SwaggerGen": "4.0.1"}, "runtime": {"lib/netstandard2.0/Swashbuckle.AspNetCore.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Swashbuckle.AspNetCore.Annotations.dll": {}}}, "Swashbuckle.AspNetCore.Filters/4.5.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "2.2.0", "Scrutor": "3.0.2", "Swashbuckle.AspNetCore.Annotations": "4.0.1", "Swashbuckle.AspNetCore.SwaggerGen": "4.0.1"}, "runtime": {"lib/netstandard2.0/Swashbuckle.AspNetCore.Filters.dll": {"assemblyVersion": "4.5.5.0", "fileVersion": "4.5.5.0"}}, "compile": {"lib/netstandard2.0/Swashbuckle.AspNetCore.Filters.dll": {}}}, "Swashbuckle.AspNetCore.Swagger/4.0.1": {"dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.2.0", "Microsoft.AspNetCore.Mvc.Formatters.Json": "2.2.0"}, "runtime": {"lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.dll": {}}}, "Swashbuckle.AspNetCore.SwaggerGen/4.0.1": {"dependencies": {"Microsoft.AspNetCore.Mvc.ApiExplorer": "2.2.0", "Microsoft.AspNetCore.Mvc.Core": "2.2.0", "Microsoft.AspNetCore.Mvc.DataAnnotations": "2.2.0", "Swashbuckle.AspNetCore.Swagger": "4.0.1"}, "runtime": {"lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {}}}, "Swashbuckle.AspNetCore.SwaggerUI/4.0.1": {"dependencies": {"Microsoft.AspNetCore.Routing": "2.2.0", "Microsoft.AspNetCore.StaticFiles": "2.2.0", "Microsoft.Extensions.FileProviders.Embedded": "2.2.0", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {}}}, "System.Buffers/4.5.1": {}, "System.ComponentModel/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.ComponentModel.Primitives/4.3.0": {"dependencies": {"System.ComponentModel": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1"}}, "System.ComponentModel.TypeConverter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Collections.NonGeneric": "4.3.0", "System.Collections.Specialized": "4.3.0", "System.ComponentModel": "4.3.0", "System.ComponentModel.Primitives": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.4.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Configuration.ConfigurationManager/4.5.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "4.5.0", "System.Security.Permissions": "4.5.0"}, "runtime": {"lib/netstandard2.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}, "compile": {"ref/netstandard2.0/System.Configuration.ConfigurationManager.dll": {}}}, "System.Diagnostics.DiagnosticSource/4.7.1": {"runtime": {"lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.21406"}}, "compile": {"lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll": {}}}, "System.Diagnostics.EventLog/4.5.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.Win32.Registry": "5.0.0", "System.Security.Permissions": "4.5.0", "System.Security.Principal.Windows": "5.0.0", "System.Threading.AccessControl": "4.5.0"}, "runtime": {"lib/netstandard2.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}, "compile": {"ref/netstandard2.0/System.Diagnostics.EventLog.dll": {}}}, "System.Diagnostics.TraceSource/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Drawing.Common/4.5.1": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.Win32.SystemEvents": "4.5.0"}, "runtime": {"lib/netstandard2.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26919.2"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26919.2"}, "runtimes/win/lib/netcoreapp2.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26919.2"}}, "compile": {"ref/netstandard2.0/System.Drawing.Common.dll": {}}}, "System.Memory/4.5.5": {}, "System.Private.ServiceModel/4.5.3": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Reflection.DispatchProxy": "4.5.0", "System.Security.Principal.Windows": "5.0.0"}, "runtimeTargets": {"runtimes/unix/lib/netstandard2.0/System.Private.ServiceModel.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}, "runtimes/win/lib/netstandard2.0/System.Private.ServiceModel.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}}}, "System.Reflection.DispatchProxy/4.5.0": {}, "System.Reflection.Emit/4.7.0": {}, "System.Reflection.Emit.Lightweight/4.7.0": {}, "System.Reflection.TypeExtensions/4.4.0": {}, "System.Runtime/4.3.1": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.0.0"}}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"runtime": {"lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "compile": {"ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {}}}, "System.Security.AccessControl/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "compile": {"ref/netstandard2.0/System.Security.AccessControl.dll": {}}}, "System.Security.Cryptography.ProtectedData/4.5.0": {"runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.Security.Principal.Windows/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "compile": {"ref/netstandard2.0/System.Security.Principal.Windows.dll": {}}}, "System.ServiceModel.Duplex/4.5.3": {"dependencies": {"System.Private.ServiceModel": "4.5.3", "System.ServiceModel.Primitives": "4.5.3"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.Duplex.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}}, "compile": {"ref/netstandard2.0/System.ServiceModel.Duplex.dll": {}}}, "System.ServiceModel.Http/4.5.3": {"dependencies": {"System.Private.ServiceModel": "4.5.3", "System.ServiceModel.Primitives": "4.5.3"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.Http.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}}, "compile": {"ref/netstandard2.0/System.ServiceModel.Http.dll": {}}}, "System.ServiceModel.NetTcp/4.5.3": {"dependencies": {"System.Private.ServiceModel": "4.5.3", "System.ServiceModel.Primitives": "4.5.3"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.NetTcp.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}}, "compile": {"ref/netstandard2.0/System.ServiceModel.NetTcp.dll": {}}}, "System.ServiceModel.Primitives/4.5.3": {"dependencies": {"System.Private.ServiceModel": "4.5.3"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}, "lib/netstandard2.0/System.ServiceModel.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}}, "compile": {"ref/netstandard2.0/System.ServiceModel.Primitives.dll": {}, "ref/netstandard2.0/System.ServiceModel.dll": {}}}, "System.ServiceModel.Security/4.5.3": {"dependencies": {"System.Private.ServiceModel": "4.5.3", "System.ServiceModel.Primitives": "4.5.3"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.Security.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}}, "compile": {"ref/netstandard2.0/System.ServiceModel.Security.dll": {}}}, "System.ServiceProcess.ServiceController/4.5.0": {"dependencies": {"System.Diagnostics.EventLog": "4.5.0"}, "runtime": {"lib/netstandard2.0/System.ServiceProcess.ServiceController.dll": {"assemblyVersion": "4.2.1.0", "fileVersion": "4.6.26515.6"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.ServiceProcess.ServiceController.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.2.1.0", "fileVersion": "4.6.26515.6"}}, "compile": {"ref/netstandard2.0/System.ServiceProcess.ServiceController.dll": {}}}, "System.Text.Encoding.CodePages/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Text.Encoding.CodePages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "compile": {"lib/netstandard2.0/System.Text.Encoding.CodePages.dll": {}}}, "System.Text.Encodings.Web/5.0.1": {"runtime": {"lib/netstandard2.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "5.0.0.1", "fileVersion": "5.0.421.11614"}}, "compile": {"lib/netstandard2.0/System.Text.Encodings.Web.dll": {}}}, "System.Text.Json/5.0.2": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Text.Encodings.Web": "5.0.1"}, "runtime": {"lib/netstandard2.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.521.16609"}}, "compile": {"lib/netstandard2.0/System.Text.Json.dll": {}}}, "System.Threading.AccessControl/4.5.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Threading.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Threading.AccessControl.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.Threading.Channels/4.7.1": {"runtime": {"lib/netstandard2.0/System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.21406"}}, "compile": {"lib/netstandard2.0/System.Threading.Channels.dll": {}}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.ValueTuple/4.5.0": {}, "TimeZoneConverter/5.0.0": {"runtime": {"lib/netstandard2.0/TimeZoneConverter.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/TimeZoneConverter.dll": {}}}, "Twilio/5.20.1": {"dependencies": {"Microsoft.IdentityModel.Logging": "5.3.0", "Microsoft.IdentityModel.Tokens": "5.3.0", "NETStandard.Library": "2.0.3", "Newtonsoft.Json": "13.0.1", "System.Collections.Specialized": "4.3.0", "System.IdentityModel.Tokens.Jwt": "5.3.0"}, "runtime": {"lib/netstandard1.4/Twilio.dll": {"assemblyVersion": "5.20.1.0", "fileVersion": "5.20.1.0"}}, "compile": {"lib/netstandard1.4/Twilio.dll": {}}}, "Twilio.AspNet.Common/5.20.1": {"dependencies": {"NETStandard.Library": "2.0.3"}, "runtime": {"lib/netstandard1.0/Twilio.AspNet.Common.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard1.0/Twilio.AspNet.Common.dll": {}}}, "Twilio.AspNet.Core/5.20.1": {"dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.2.0", "NETStandard.Library": "2.0.3", "Twilio": "5.20.1", "Twilio.AspNet.Common": "5.20.1"}, "runtime": {"lib/netstandard1.6/Twilio.AspNet.Core.dll": {"assemblyVersion": "5.20.1.0", "fileVersion": "5.20.1.0"}}, "compile": {"lib/netstandard1.6/Twilio.AspNet.Core.dll": {}}}, "UAParser/3.0.0": {"dependencies": {"NETStandard.Library": "2.0.3"}, "runtime": {"lib/netstandard1.3/UAParser.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.0.0"}}, "compile": {"lib/netstandard1.3/UAParser.dll": {}}}, "ZstdSharp.Port/0.6.2": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "System.Memory": "4.5.5"}, "runtime": {"lib/netstandard2.0/ZstdSharp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/ZstdSharp.dll": {}}}, "CommonLibCore/1.0.0": {"dependencies": {"Castle.Core": "4.4.1", "CompareNETObjects": "4.57.0", "Dapper": "1.50.5", "DeepCloner": "0.10.2", "EnyimMemcachedCore": "2.1.8", "Experimental.System.Messaging": "1.0.0", "GreenPipes": "4.0.1", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.Primitives": "2.2.0", "MongoDB.Driver": "2.21.0", "MongoDB.Driver.GridFS": "2.21.0", "MongolianBarbecue": "1.0.0", "NEST": "7.1.0", "Newtonsoft.Json": "13.0.1", "System.Configuration.ConfigurationManager": "4.5.0", "System.Data.SqlClient": "4.6.0", "System.Private.ServiceModel": "4.5.3", "System.ServiceModel.Duplex": "4.5.3", "System.ServiceModel.Http": "4.5.3", "System.ServiceModel.NetTcp": "4.5.3", "System.ServiceModel.Security": "4.5.3", "System.ServiceProcess.ServiceController": "4.5.0", "TimeZoneConverter": "5.0.0", "UAParser": "3.0.0"}, "runtime": {"CommonLibCore.dll": {"assemblyVersion": "1.0.0", "fileVersion": ""}}, "compile": {"CommonLibCore.dll": {}}}, "EBizAutos.ApplicationCommonLib/1.0.0": {"dependencies": {"AWSSDK.S3": "**********", "Amazon.Extensions.Configuration.SystemsManager": "2.1.1", "CommonLibCore": "1.0.0", "Magick.NET-Q16-AnyCPU": "7.11.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Configuration": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "2.2.0", "Microsoft.Extensions.Configuration.Binder": "2.2.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "2.2.0", "Microsoft.Extensions.Configuration.FileExtensions": "2.2.0", "Microsoft.Extensions.Configuration.Json": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "System.Drawing.Common": "4.5.1"}, "runtime": {"ApplicationCommonLibCore.dll": {"assemblyVersion": "1.0.0", "fileVersion": ""}}, "compile": {"ApplicationCommonLibCore.dll": {}}}, "EBizAutos.Apps.Authentication.CommonLib/1.0.0": {"dependencies": {"Amazon.AspNetCore.DataProtection.SSM": "1.1.0", "CommonLibCore": "1.0.0", "EBizAutos.ApplicationCommonLib": "1.0.0", "EBizAutos.Apps.CommonLib": "1.0.0", "Microsoft.AspNetCore.Authentication.Cookies": "2.2.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "2.2.0", "Microsoft.AspNetCore.Authorization": "2.2.0", "Microsoft.AspNetCore.DataProtection.Extensions": "2.2.0", "Microsoft.AspNetCore.Mvc": "2.2.0", "Microsoft.AspNetCore.Mvc.Core": "2.2.0", "Microsoft.IdentityModel.Tokens": "5.3.0", "System.Security.Claims": "4.3.0"}, "runtime": {"EBizAutos.Apps.Authentication.CommonLib.dll": {"assemblyVersion": "1.0.0", "fileVersion": "0.0.0.0"}}, "compile": {"EBizAutos.Apps.Authentication.CommonLib.dll": {}}}, "EBizAutos.Apps.Authentication.MongoDbRepository/1.0.0": {"dependencies": {"EBizAutos.Apps.Authentication.CommonLib": "1.0.0", "EBizAutos.Apps.CommonLib": "1.0.0"}, "runtime": {"EBizAutos.Apps.Authentication.MongoDbRepository.dll": {"assemblyVersion": "1.0.0", "fileVersion": "0.0.0.0"}}, "compile": {"EBizAutos.Apps.Authentication.MongoDbRepository.dll": {}}}, "EBizAutos.Apps.Common.MongoDbRepository/1.0.0": {"dependencies": {"EBizAutos.Apps.CommonLib": "1.0.0", "Mapster": "4.1.1"}, "runtime": {"EBizAutos.Apps.Common.MongoDbRepository.dll": {"assemblyVersion": "1.0.0", "fileVersion": "0.0.0.0"}}, "compile": {"EBizAutos.Apps.Common.MongoDbRepository.dll": {}}}, "EBizAutos.Apps.CommonLib/1.0.0": {"dependencies": {"CommonLibCore": "1.0.0", "EBizAutos.ApplicationCommonLib": "1.0.0", "FoundationCommonLib": "1.0.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection": "2.2.0", "Newtonsoft.Json": "13.0.1", "Swashbuckle.AspNetCore": "4.0.1", "Swashbuckle.AspNetCore.Filters": "4.5.5", "System.ComponentModel.Annotations": "4.5.0", "Twilio.AspNet.Core": "5.20.1"}, "runtime": {"EBizAutos.Apps.CommonLib.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}, "compile": {"EBizAutos.Apps.CommonLib.dll": {}}}, "EBizAutos.Apps.MongoDbRepository/1.0.0": {"dependencies": {"EBizAutos.Apps.CommonLib": "1.0.0", "FoundationCommonLib": "1.0.0"}, "runtime": {"EBizAutos.Apps.MongoDbRepository.dll": {"assemblyVersion": "1.0.0", "fileVersion": "0.0.0.0"}}, "compile": {"EBizAutos.Apps.MongoDbRepository.dll": {}}}, "EBizAutos.Apps.ServiceBus/1.0.0": {"dependencies": {"CommonLibCore": "1.0.0", "EBizAutos.Apps.CommonLib": "1.0.0", "MassTransit.ActiveMQ": "7.3.3", "MassTransit.Extensions.DependencyInjection": "7.3.1", "MassTransit.MongoDb": "7.3.1", "Scrutor": "3.0.2"}, "runtime": {"EBizAutos.Apps.ServiceBus.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}, "compile": {"EBizAutos.Apps.ServiceBus.dll": {}}}, "EBizAutos.Apps.Users.MongoDbRepository/1.0.0": {"dependencies": {"EBizAutos.Apps.CommonLib": "1.0.0"}, "runtime": {"EBizAutos.Apps.Users.MongoDbRepository.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}, "compile": {"EBizAutos.Apps.Users.MongoDbRepository.dll": {}}}, "EBizAutos.Apps.Users.MsSqlRepository/1.0.0": {"dependencies": {"EBizAutos.Apps.CommonLib": "1.0.0"}, "runtime": {"EBizAutos.Apps.Users.MsSqlRepository.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}, "compile": {"EBizAutos.Apps.Users.MsSqlRepository.dll": {}}}, "FoundationCommonLib/1.0.0": {"dependencies": {"CommonLibCore": "1.0.0", "EBizAutos.ApplicationCommonLib": "1.0.0", "Microsoft.Extensions.Caching.Abstractions": "2.2.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "2.2.0"}, "runtime": {"FoundationCommonLib.dll": {"assemblyVersion": "1.0.0", "fileVersion": ""}}, "compile": {"FoundationCommonLib.dll": {}}}, "CommonLibCore.Reference/*******": {"runtime": {"CommonLibCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"CommonLibCore.dll": {}}}, "FoundationCommonLib.Reference/*******": {"runtime": {"FoundationCommonLib.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"FoundationCommonLib.dll": {}}}, "Microsoft.AspNet.WebApi.Client/5.2.6": {"dependencies": {"Newtonsoft.Json": "13.0.1", "Newtonsoft.Json.Bson": "1.0.2"}, "compile": {"lib/netstandard2.0/System.Net.Http.Formatting.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Diagnostics": "2.2.0", "Microsoft.AspNetCore.HostFiltering": "2.2.0", "Microsoft.AspNetCore.Hosting": "2.2.0", "Microsoft.AspNetCore.Routing": "2.2.0", "Microsoft.AspNetCore.Server.IIS": "2.2.0", "Microsoft.AspNetCore.Server.IISIntegration": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel.Https": "2.2.0", "Microsoft.Extensions.Configuration.CommandLine": "2.2.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "2.2.0", "Microsoft.Extensions.Configuration.FileExtensions": "2.2.0", "Microsoft.Extensions.Configuration.Json": "2.2.0", "Microsoft.Extensions.Configuration.UserSecrets": "2.2.0", "Microsoft.Extensions.Logging": "2.2.0", "Microsoft.Extensions.Logging.Configuration": "2.2.0", "Microsoft.Extensions.Logging.Console": "2.2.0", "Microsoft.Extensions.Logging.Debug": "2.2.0", "Microsoft.Extensions.Logging.EventSource": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Antiforgery/2.2.0": {"dependencies": {"Microsoft.AspNetCore.DataProtection": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Antiforgery.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.App/2.2.0": {"dependencies": {"Microsoft.AspNet.WebApi.Client": "5.2.6", "Microsoft.AspNetCore": "2.2.0", "Microsoft.AspNetCore.Antiforgery": "2.2.0", "Microsoft.AspNetCore.Authentication": "2.2.0", "Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Authentication.Cookies": "2.2.0", "Microsoft.AspNetCore.Authentication.Core": "2.2.0", "Microsoft.AspNetCore.Authentication.Facebook": "2.2.0", "Microsoft.AspNetCore.Authentication.Google": "2.2.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "2.2.0", "Microsoft.AspNetCore.Authentication.MicrosoftAccount": "2.2.0", "Microsoft.AspNetCore.Authentication.OAuth": "2.2.0", "Microsoft.AspNetCore.Authentication.OpenIdConnect": "2.2.0", "Microsoft.AspNetCore.Authentication.Twitter": "2.2.0", "Microsoft.AspNetCore.Authentication.WsFederation": "2.2.0", "Microsoft.AspNetCore.Authorization": "2.2.0", "Microsoft.AspNetCore.Authorization.Policy": "2.2.0", "Microsoft.AspNetCore.Connections.Abstractions": "2.2.0", "Microsoft.AspNetCore.CookiePolicy": "2.2.0", "Microsoft.AspNetCore.Cors": "2.2.0", "Microsoft.AspNetCore.Cryptography.Internal": "2.2.0", "Microsoft.AspNetCore.Cryptography.KeyDerivation": "2.2.0", "Microsoft.AspNetCore.DataProtection": "2.2.0", "Microsoft.AspNetCore.DataProtection.Abstractions": "2.2.0", "Microsoft.AspNetCore.DataProtection.Extensions": "2.2.0", "Microsoft.AspNetCore.Diagnostics": "2.2.0", "Microsoft.AspNetCore.Diagnostics.Abstractions": "2.2.0", "Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore": "2.2.0", "Microsoft.AspNetCore.Diagnostics.HealthChecks": "2.2.0", "Microsoft.AspNetCore.HostFiltering": "2.2.0", "Microsoft.AspNetCore.Hosting": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.2.0", "Microsoft.AspNetCore.Html.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Connections": "1.1.0", "Microsoft.AspNetCore.Http.Connections.Common": "1.1.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.AspNetCore.HttpOverrides": "2.2.0", "Microsoft.AspNetCore.HttpsPolicy": "2.2.0", "Microsoft.AspNetCore.Identity": "2.2.0", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "2.2.0", "Microsoft.AspNetCore.Identity.UI": "2.2.0", "Microsoft.AspNetCore.JsonPatch": "2.2.0", "Microsoft.AspNetCore.Localization": "2.2.0", "Microsoft.AspNetCore.Localization.Routing": "2.2.0", "Microsoft.AspNetCore.MiddlewareAnalysis": "2.2.0", "Microsoft.AspNetCore.Mvc": "2.2.0", "Microsoft.AspNetCore.Mvc.Abstractions": "2.2.0", "Microsoft.AspNetCore.Mvc.Analyzers": "2.2.0", "Microsoft.AspNetCore.Mvc.ApiExplorer": "2.2.0", "Microsoft.AspNetCore.Mvc.Core": "2.2.0", "Microsoft.AspNetCore.Mvc.Cors": "2.2.0", "Microsoft.AspNetCore.Mvc.DataAnnotations": "2.2.0", "Microsoft.AspNetCore.Mvc.Formatters.Json": "2.2.0", "Microsoft.AspNetCore.Mvc.Formatters.Xml": "2.2.0", "Microsoft.AspNetCore.Mvc.Localization": "2.2.0", "Microsoft.AspNetCore.Mvc.Razor": "2.2.0", "Microsoft.AspNetCore.Mvc.Razor.Extensions": "2.2.0", "Microsoft.AspNetCore.Mvc.Razor.ViewCompilation": "2.2.0", "Microsoft.AspNetCore.Mvc.RazorPages": "2.2.0", "Microsoft.AspNetCore.Mvc.TagHelpers": "2.2.0", "Microsoft.AspNetCore.Mvc.ViewFeatures": "2.2.0", "Microsoft.AspNetCore.NodeServices": "2.2.0", "Microsoft.AspNetCore.Owin": "2.2.0", "Microsoft.AspNetCore.Razor": "2.2.0", "Microsoft.AspNetCore.Razor.Design": "2.2.0", "Microsoft.AspNetCore.Razor.Language": "2.2.0", "Microsoft.AspNetCore.Razor.Runtime": "2.2.0", "Microsoft.AspNetCore.ResponseCaching": "2.2.0", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "2.2.0", "Microsoft.AspNetCore.ResponseCompression": "2.2.0", "Microsoft.AspNetCore.Rewrite": "2.2.0", "Microsoft.AspNetCore.Routing": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.AspNetCore.Server.HttpSys": "2.2.0", "Microsoft.AspNetCore.Server.IIS": "2.2.0", "Microsoft.AspNetCore.Server.IISIntegration": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel.Core": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel.Https": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets": "2.2.0", "Microsoft.AspNetCore.Session": "2.2.0", "Microsoft.AspNetCore.SignalR": "1.1.0", "Microsoft.AspNetCore.SignalR.Common": "1.1.0", "Microsoft.AspNetCore.SignalR.Core": "1.1.0", "Microsoft.AspNetCore.SignalR.Protocols.Json": "1.1.0", "Microsoft.AspNetCore.SpaServices": "2.2.0", "Microsoft.AspNetCore.SpaServices.Extensions": "2.2.0", "Microsoft.AspNetCore.StaticFiles": "2.2.0", "Microsoft.AspNetCore.WebSockets": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.CodeAnalysis.Razor": "2.2.0", "Microsoft.EntityFrameworkCore": "2.2.0", "Microsoft.EntityFrameworkCore.Abstractions": "2.2.0", "Microsoft.EntityFrameworkCore.Analyzers": "2.2.0", "Microsoft.EntityFrameworkCore.Design": "2.2.0", "Microsoft.EntityFrameworkCore.InMemory": "2.2.0", "Microsoft.EntityFrameworkCore.Relational": "2.2.0", "Microsoft.EntityFrameworkCore.SqlServer": "2.2.0", "Microsoft.EntityFrameworkCore.Tools": "2.2.0", "Microsoft.Extensions.Caching.Abstractions": "2.2.0", "Microsoft.Extensions.Caching.Memory": "2.2.0", "Microsoft.Extensions.Caching.SqlServer": "2.2.0", "Microsoft.Extensions.Configuration": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "2.2.0", "Microsoft.Extensions.Configuration.Binder": "2.2.0", "Microsoft.Extensions.Configuration.CommandLine": "2.2.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "2.2.0", "Microsoft.Extensions.Configuration.FileExtensions": "2.2.0", "Microsoft.Extensions.Configuration.Ini": "2.2.0", "Microsoft.Extensions.Configuration.Json": "2.2.0", "Microsoft.Extensions.Configuration.KeyPerFile": "2.2.0", "Microsoft.Extensions.Configuration.UserSecrets": "2.2.0", "Microsoft.Extensions.Configuration.Xml": "2.2.0", "Microsoft.Extensions.DependencyInjection": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.DiagnosticAdapter": "2.2.0", "Microsoft.Extensions.Diagnostics.HealthChecks": "2.2.0", "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Composite": "2.2.0", "Microsoft.Extensions.FileProviders.Embedded": "2.2.0", "Microsoft.Extensions.FileProviders.Physical": "2.2.0", "Microsoft.Extensions.FileSystemGlobbing": "2.2.0", "Microsoft.Extensions.Hosting": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "2.2.0", "Microsoft.Extensions.Http": "2.2.0", "Microsoft.Extensions.Identity.Core": "2.2.0", "Microsoft.Extensions.Identity.Stores": "2.2.0", "Microsoft.Extensions.Localization": "2.2.0", "Microsoft.Extensions.Localization.Abstractions": "2.2.0", "Microsoft.Extensions.Logging": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Configuration": "2.2.0", "Microsoft.Extensions.Logging.Console": "2.2.0", "Microsoft.Extensions.Logging.Debug": "2.2.0", "Microsoft.Extensions.Logging.EventSource": "2.2.0", "Microsoft.Extensions.Logging.TraceSource": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "2.2.0", "Microsoft.Extensions.Options.DataAnnotations": "2.2.0", "Microsoft.Extensions.Primitives": "2.2.0", "Microsoft.Extensions.WebEncoders": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0", "System.IO.Pipelines": "4.5.2"}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.2.0", "Microsoft.AspNetCore.DataProtection": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "Microsoft.Extensions.WebEncoders": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Cookies/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Cookies.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Facebook/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.OAuth": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Facebook.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Google/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.OAuth": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Google.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.JwtBearer/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication": "2.2.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "5.3.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.MicrosoftAccount/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.OAuth": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.MicrosoftAccount.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.OAuth/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication": "2.2.0", "Newtonsoft.Json": "13.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.OAuth.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.OpenIdConnect/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.OAuth": "2.2.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "5.3.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.OpenIdConnect.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Twitter/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.OAuth": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Twitter.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.WsFederation/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication": "2.2.0", "Microsoft.IdentityModel.Protocols.WsFederation": "5.3.0", "System.IdentityModel.Tokens.Jwt": "5.3.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.WsFederation.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authorization/2.2.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authorization.Policy/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Authorization": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.Policy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Connections.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.IO.Pipelines": "4.5.2"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Connections.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.CookiePolicy/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.CookiePolicy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Cors/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Cors.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Cryptography.Internal/2.2.0": {"compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "2.2.0"}, "compile": {"lib/netcoreapp2.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "2.2.0", "Microsoft.AspNetCore.DataProtection.Abstractions": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "Microsoft.Win32.Registry": "5.0.0", "System.Security.Cryptography.Xml": "4.5.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection.Abstractions/2.2.0": {"compile": {"lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection.Extensions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.DataProtection": "2.2.0", "Microsoft.Extensions.DependencyInjection": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.Extensions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Diagnostics.Abstractions": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.FileProviders.Physical": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "System.Diagnostics.DiagnosticSource": "4.7.1", "System.Reflection.Metadata": "1.6.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Diagnostics.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics.Abstractions/2.2.0": {"compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Diagnostics.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.EntityFrameworkCore.Relational": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics.HealthChecks/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Diagnostics.HealthChecks": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Diagnostics.HealthChecks.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HostFiltering/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.HostFiltering.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Configuration": "2.2.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "2.2.0", "Microsoft.Extensions.Configuration.FileExtensions": "2.2.0", "Microsoft.Extensions.DependencyInjection": "2.2.0", "Microsoft.Extensions.FileProviders.Physical": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "2.2.0", "Microsoft.Extensions.Logging": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "System.Diagnostics.DiagnosticSource": "4.7.1", "System.Reflection.Metadata": "1.6.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Html.Abstractions/2.2.0": {"dependencies": {"System.Text.Encodings.Web": "5.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Html.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "5.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Connections/1.1.0": {"dependencies": {"Microsoft.AspNetCore.Authorization.Policy": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Connections.Common": "1.1.0", "Microsoft.AspNetCore.Routing": "2.2.0", "Microsoft.AspNetCore.WebSockets": "2.2.0", "Newtonsoft.Json": "13.0.1", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"lib/netcoreapp2.2/Microsoft.AspNetCore.Http.Connections.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Connections.Common/1.1.0": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "2.2.0", "Newtonsoft.Json": "13.0.1", "System.Buffers": "4.5.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Connections.Common.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0", "System.Buffers": "4.5.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HttpOverrides/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.HttpOverrides.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HttpsPolicy/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Configuration.Binder": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.HttpsPolicy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Identity/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Cookies": "2.2.0", "Microsoft.AspNetCore.Cryptography.KeyDerivation": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.Extensions.Identity.Core": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Identity.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Identity": "2.2.0", "Microsoft.EntityFrameworkCore.Relational": "2.2.0", "Microsoft.Extensions.Identity.Stores": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Identity.EntityFrameworkCore.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Identity.UI/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Identity": "2.2.0", "Microsoft.AspNetCore.Mvc": "2.2.0", "Microsoft.AspNetCore.StaticFiles": "2.2.0", "Microsoft.Extensions.FileProviders.Embedded": "2.2.0", "Microsoft.Extensions.Identity.Stores": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Identity.UI.Views.V3.dll": {}, "lib/netstandard2.0/Microsoft.AspNetCore.Identity.UI.Views.V4.dll": {}, "lib/netstandard2.0/Microsoft.AspNetCore.Identity.UI.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.JsonPatch/2.2.0": {"dependencies": {"Microsoft.CSharp": "4.5.0", "Newtonsoft.Json": "13.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.JsonPatch.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Localization/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Localization.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Localization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Localization.Routing/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Localization": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Localization.Routing.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.MiddlewareAnalysis/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "System.Diagnostics.DiagnosticSource": "4.7.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.MiddlewareAnalysis.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Analyzers": "2.2.0", "Microsoft.AspNetCore.Mvc.ApiExplorer": "2.2.0", "Microsoft.AspNetCore.Mvc.Cors": "2.2.0", "Microsoft.AspNetCore.Mvc.DataAnnotations": "2.2.0", "Microsoft.AspNetCore.Mvc.Formatters.Json": "2.2.0", "Microsoft.AspNetCore.Mvc.Localization": "2.2.0", "Microsoft.AspNetCore.Mvc.Razor.Extensions": "2.2.0", "Microsoft.AspNetCore.Mvc.RazorPages": "2.2.0", "Microsoft.AspNetCore.Mvc.TagHelpers": "2.2.0", "Microsoft.AspNetCore.Mvc.ViewFeatures": "2.2.0", "Microsoft.AspNetCore.Razor.Design": "2.2.0", "Microsoft.Extensions.Caching.Memory": "2.2.0", "Microsoft.Extensions.DependencyInjection": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Analyzers/2.2.0": {"compileOnly": true}, "Microsoft.AspNetCore.Mvc.ApiExplorer/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Core/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.2.0", "Microsoft.AspNetCore.Authorization.Policy": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Mvc.Abstractions": "2.2.0", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "2.2.0", "Microsoft.AspNetCore.Routing": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection": "2.2.0", "Microsoft.Extensions.DependencyModel": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "System.Diagnostics.DiagnosticSource": "4.7.1", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Cors/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Cors": "2.2.0", "Microsoft.AspNetCore.Mvc.Core": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Cors.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.DataAnnotations/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.2.0", "Microsoft.Extensions.Localization": "2.2.0", "System.ComponentModel.Annotations": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Formatters.Json/2.2.0": {"dependencies": {"Microsoft.AspNetCore.JsonPatch": "2.2.0", "Microsoft.AspNetCore.Mvc.Core": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Formatters.Json.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Formatters.Xml/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Formatters.Xml.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Localization/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Localization": "2.2.0", "Microsoft.AspNetCore.Mvc.Razor": "2.2.0", "Microsoft.Extensions.DependencyInjection": "2.2.0", "Microsoft.Extensions.Localization": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Localization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Razor/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Razor.Extensions": "2.2.0", "Microsoft.AspNetCore.Mvc.ViewFeatures": "2.2.0", "Microsoft.AspNetCore.Razor.Runtime": "2.2.0", "Microsoft.CodeAnalysis.CSharp": "2.8.0", "Microsoft.CodeAnalysis.Razor": "2.2.0", "Microsoft.Extensions.Caching.Memory": "2.2.0", "Microsoft.Extensions.FileProviders.Composite": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Razor.Extensions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "2.2.0", "Microsoft.CodeAnalysis.Razor": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.Extensions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Razor.ViewCompilation/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting": "2.2.0", "Microsoft.AspNetCore.Mvc.RazorPages": "2.2.0"}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.RazorPages/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Razor": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.RazorPages.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.TagHelpers/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Razor": "2.2.0", "Microsoft.AspNetCore.Razor.Runtime": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.Caching.Memory": "2.2.0", "Microsoft.Extensions.FileSystemGlobbing": "2.2.0", "Microsoft.Extensions.Primitives": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.TagHelpers.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.ViewFeatures/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Antiforgery": "2.2.0", "Microsoft.AspNetCore.Diagnostics.Abstractions": "2.2.0", "Microsoft.AspNetCore.Html.Abstractions": "2.2.0", "Microsoft.AspNetCore.Mvc.Core": "2.2.0", "Microsoft.AspNetCore.Mvc.DataAnnotations": "2.2.0", "Microsoft.AspNetCore.Mvc.Formatters.Json": "2.2.0", "Microsoft.Extensions.WebEncoders": "2.2.0", "Newtonsoft.Json.Bson": "1.0.2"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.ViewFeatures.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.NodeServices/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Console": "2.2.0", "Newtonsoft.Json": "13.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.NodeServices.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Owin/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Owin.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Razor/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Html.Abstractions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Razor.Design/2.2.0": {"compileOnly": true}, "Microsoft.AspNetCore.Razor.Language/2.2.0": {"compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Razor.Runtime/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Html.Abstractions": "2.2.0", "Microsoft.AspNetCore.Razor": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Runtime.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCaching/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "2.2.0", "Microsoft.Extensions.Caching.Memory": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.ResponseCaching.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCompression/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netcoreapp2.1/Microsoft.AspNetCore.ResponseCompression.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Rewrite/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Rewrite.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Routing/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netcoreapp2.2/Microsoft.AspNetCore.Routing.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Routing.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.HttpSys/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.2.0", "Microsoft.AspNetCore.Hosting": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0", "Microsoft.Win32.Registry": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.HttpSys.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.IIS/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.2.0", "Microsoft.AspNetCore.Connections.Abstractions": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "System.IO.Pipelines": "4.5.2", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.IIS.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.IISIntegration/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.HttpOverrides": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "System.Buffers": "4.5.1", "System.IO.Pipelines": "4.5.2", "System.Memory": "4.5.5", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.IISIntegration.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel.Core": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel.Https": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.Kestrel.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Core/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.Configuration.Binder": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0", "System.Memory": "4.5.5", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Security.Cryptography.Cng": "4.5.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/netcoreapp2.1/Microsoft.AspNetCore.Server.Kestrel.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Https/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel.Core": "2.2.0"}, "compile": {"lib/netcoreapp2.1/Microsoft.AspNetCore.Server.Kestrel.Https.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netcoreapp2.1/Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Session/2.2.0": {"dependencies": {"Microsoft.AspNetCore.DataProtection": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Caching.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Session.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR/1.1.0": {"dependencies": {"Microsoft.AspNetCore.Http.Connections": "1.1.0", "Microsoft.AspNetCore.SignalR.Core": "1.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.SignalR.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Common/1.1.0": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "Newtonsoft.Json": "13.0.1", "System.Buffers": "4.5.1"}, "compile": {"lib/netcoreapp2.2/Microsoft.AspNetCore.SignalR.Common.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Core/1.1.0": {"dependencies": {"Microsoft.AspNetCore.Authorization": "2.2.0", "Microsoft.AspNetCore.SignalR.Common": "1.1.0", "Microsoft.AspNetCore.SignalR.Protocols.Json": "1.1.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "System.Reflection.Emit": "4.7.0", "System.Threading.Channels": "4.7.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.SignalR.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Protocols.Json/1.1.0": {"dependencies": {"Microsoft.AspNetCore.SignalR.Common": "1.1.0", "Newtonsoft.Json": "13.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.SignalR.Protocols.Json.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SpaServices/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.TagHelpers": "2.2.0", "Microsoft.AspNetCore.Mvc.ViewFeatures": "2.2.0", "Microsoft.AspNetCore.NodeServices": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.SpaServices.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SpaServices.Extensions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.SpaServices": "2.2.0", "Microsoft.AspNetCore.StaticFiles": "2.2.0", "Microsoft.AspNetCore.WebSockets": "2.2.0", "Microsoft.Extensions.FileProviders.Physical": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.SpaServices.Extensions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.StaticFiles/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.WebEncoders": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.StaticFiles.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.WebSockets/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "System.Net.WebSockets.WebSocketProtocol": "4.5.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.WebSockets.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"dependencies": {"Microsoft.Net.Http.Headers": "2.2.0", "System.Text.Encodings.Web": "5.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll": {}}, "compileOnly": true}, "Microsoft.CodeAnalysis.Analyzers/1.1.0": {"compileOnly": true}, "Microsoft.CodeAnalysis.Common/2.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "1.1.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Collections.Immutable": "1.5.0", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.FileVersionInfo": "4.3.0", "System.Diagnostics.StackTrace": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Globalization": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Metadata": "1.6.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.CodePages": "5.0.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Parallel": "4.3.0", "System.Threading.Thread": "4.3.0", "System.ValueTuple": "4.5.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0", "System.Xml.XPath.XDocument": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "compile": {"lib/netstandard1.3/Microsoft.CodeAnalysis.dll": {}}, "compileOnly": true}, "Microsoft.CodeAnalysis.CSharp/2.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "2.8.0"}, "compile": {"lib/netstandard1.3/Microsoft.CodeAnalysis.CSharp.dll": {}}, "compileOnly": true}, "Microsoft.CodeAnalysis.Razor/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "2.2.0", "Microsoft.CodeAnalysis.CSharp": "2.8.0", "Microsoft.CodeAnalysis.Common": "2.8.0"}, "compile": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll": {}}, "compileOnly": true}, "Microsoft.CSharp/4.5.0": {"compileOnly": true}, "Microsoft.DotNet.PlatformAbstractions/2.1.0": {"dependencies": {"System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Reflection.TypeExtensions": "4.4.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0"}, "compile": {"lib/netstandard1.3/Microsoft.DotNet.PlatformAbstractions.dll": {}}, "compileOnly": true}, "Microsoft.EntityFrameworkCore/2.2.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "2.2.0", "Microsoft.EntityFrameworkCore.Analyzers": "2.2.0", "Microsoft.Extensions.Caching.Memory": "2.2.0", "Microsoft.Extensions.DependencyInjection": "2.2.0", "Microsoft.Extensions.Logging": "2.2.0", "Remotion.Linq": "2.2.0", "System.Collections.Immutable": "1.5.0", "System.ComponentModel.Annotations": "4.5.0", "System.Diagnostics.DiagnosticSource": "4.7.1", "System.Interactive.Async": "3.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.dll": {}}, "compileOnly": true}, "Microsoft.EntityFrameworkCore.Abstractions/2.2.0": {"compile": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.EntityFrameworkCore.Analyzers/2.2.0": {"compileOnly": true}, "Microsoft.EntityFrameworkCore.Design/2.2.0": {"dependencies": {"Microsoft.CSharp": "4.5.0", "Microsoft.EntityFrameworkCore.Relational": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.Design.dll": {}}, "compileOnly": true}, "Microsoft.EntityFrameworkCore.InMemory/2.2.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.InMemory.dll": {}}, "compileOnly": true}, "Microsoft.EntityFrameworkCore.Relational/2.2.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.Relational.dll": {}}, "compileOnly": true}, "Microsoft.EntityFrameworkCore.SqlServer/2.2.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "2.2.0", "System.Data.SqlClient": "4.6.0"}, "compile": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {}}, "compileOnly": true}, "Microsoft.EntityFrameworkCore.Tools/2.2.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "2.2.0"}, "compileOnly": true}, "Microsoft.Extensions.Caching.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Caching.Memory/2.2.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Caching.SqlServer/2.2.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "System.Data.SqlClient": "4.6.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Caching.SqlServer.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Binder/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.CommandLine/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.EnvironmentVariables/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.FileExtensions/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration": "2.2.0", "Microsoft.Extensions.FileProviders.Physical": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Ini/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration": "2.2.0", "Microsoft.Extensions.Configuration.FileExtensions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Ini.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Json/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration": "2.2.0", "Microsoft.Extensions.Configuration.FileExtensions": "2.2.0", "Newtonsoft.Json": "13.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.KeyPerFile/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration": "2.2.0", "Microsoft.Extensions.FileProviders.Physical": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.KeyPerFile.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.UserSecrets/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Json": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Xml/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration": "2.2.0", "Microsoft.Extensions.Configuration.FileExtensions": "2.2.0", "System.Security.Cryptography.Xml": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Xml.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.DependencyInjection/2.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0"}, "compile": {"lib/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.DependencyInjection.Abstractions/2.2.0": {"compile": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.DependencyModel/2.1.0": {"dependencies": {"Microsoft.DotNet.PlatformAbstractions": "2.1.0", "Newtonsoft.Json": "13.0.1", "System.Diagnostics.Debug": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Linq": "4.3.0"}, "compile": {"lib/netstandard1.6/Microsoft.Extensions.DependencyModel.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.DiagnosticAdapter/2.2.0": {"dependencies": {"System.Diagnostics.DiagnosticSource": "4.7.1"}, "compile": {"lib/netcoreapp2.0/Microsoft.Extensions.DiagnosticAdapter.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.HealthChecks/2.2.0": {"dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Diagnostics.HealthChecks.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/2.2.0": {"compile": {"lib/netstandard2.0/Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Composite/2.2.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Composite.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Embedded/2.2.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Physical/2.2.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Extensions.FileSystemGlobbing": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileSystemGlobbing/2.2.0": {"compile": {"lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Hosting/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration": "2.2.0", "Microsoft.Extensions.DependencyInjection": "2.2.0", "Microsoft.Extensions.FileProviders.Physical": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "2.2.0", "Microsoft.Extensions.Logging": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Hosting.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Hosting.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Http/2.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Logging": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Http.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Identity.Core/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Cryptography.KeyDerivation": "2.2.0", "Microsoft.Extensions.Logging": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "System.ComponentModel.Annotations": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Identity.Core.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Identity.Stores/2.2.0": {"dependencies": {"Microsoft.Extensions.Identity.Core": "2.2.0", "Microsoft.Extensions.Logging": "2.2.0", "System.ComponentModel.Annotations": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Identity.Stores.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Localization/2.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Localization.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Localization.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Localization.Abstractions/2.2.0": {"compile": {"lib/netstandard2.0/Microsoft.Extensions.Localization.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Abstractions/2.2.0": {"compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Configuration/2.2.0": {"dependencies": {"Microsoft.Extensions.Logging": "2.2.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Console/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "2.2.0", "Microsoft.Extensions.Logging": "2.2.0", "Microsoft.Extensions.Logging.Configuration": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Console.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Debug/2.2.0": {"dependencies": {"Microsoft.Extensions.Logging": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.EventSource/2.2.0": {"dependencies": {"Microsoft.Extensions.Logging": "2.2.0", "Newtonsoft.Json": "13.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.EventSource.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.TraceSource/2.2.0": {"dependencies": {"Microsoft.Extensions.Logging": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.TraceSource.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.ObjectPool/2.2.0": {"compile": {"lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Options/2.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Primitives": "2.2.0", "System.ComponentModel.Annotations": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Options.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Options.ConfigurationExtensions/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "2.2.0", "Microsoft.Extensions.Configuration.Binder": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Options.DataAnnotations/2.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "System.ComponentModel.Annotations": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Options.DataAnnotations.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Primitives/2.2.0": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Primitives.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.WebEncoders/2.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "System.Text.Encodings.Web": "5.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.WebEncoders.dll": {}}, "compileOnly": true}, "Microsoft.IdentityModel.JsonWebTokens/5.3.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "5.3.0", "Newtonsoft.Json": "13.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll": {}}, "compileOnly": true}, "Microsoft.IdentityModel.Logging/5.3.0": {"dependencies": {"System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0"}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll": {}}, "compileOnly": true}, "Microsoft.IdentityModel.Protocols/5.3.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "5.3.0", "Microsoft.IdentityModel.Tokens": "5.3.0", "System.Collections.Specialized": "4.3.0", "System.Diagnostics.Contracts": "4.3.0", "System.Net.Http": "4.3.0"}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll": {}}, "compileOnly": true}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/5.3.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "5.3.0", "Newtonsoft.Json": "13.0.1", "System.Dynamic.Runtime": "4.3.0", "System.IdentityModel.Tokens.Jwt": "5.3.0"}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {}}, "compileOnly": true}, "Microsoft.IdentityModel.Protocols.WsFederation/5.3.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "5.3.0", "Microsoft.IdentityModel.Tokens.Saml": "5.3.0", "Microsoft.IdentityModel.Xml": "5.3.0", "System.Xml.XmlDocument": "4.3.0"}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.WsFederation.dll": {}}, "compileOnly": true}, "Microsoft.IdentityModel.Tokens/5.3.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "5.3.0", "Newtonsoft.Json": "13.0.1", "System.Collections": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Runtime.Serialization.Xml": "4.3.0", "System.Security.Claims": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll": {}}, "compileOnly": true}, "Microsoft.IdentityModel.Tokens.Saml/5.3.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "5.3.0", "Microsoft.IdentityModel.Xml": "5.3.0"}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.Tokens.Saml.dll": {}}, "compileOnly": true}, "Microsoft.IdentityModel.Xml/5.3.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "5.3.0"}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.Xml.dll": {}}, "compileOnly": true}, "Microsoft.Net.Http.Headers/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "2.2.0", "System.Buffers": "4.5.1"}, "compile": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {}}, "compileOnly": true}, "Microsoft.NETCore.App/2.2.0": {"dependencies": {"Microsoft.NETCore.DotNetHostPolicy": "2.2.0", "Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.0.0", "NETStandard.Library": "2.0.3"}, "compile": {"ref/netcoreapp2.2/Microsoft.CSharp.dll": {}, "ref/netcoreapp2.2/Microsoft.VisualBasic.dll": {}, "ref/netcoreapp2.2/Microsoft.Win32.Primitives.dll": {}, "ref/netcoreapp2.2/System.AppContext.dll": {}, "ref/netcoreapp2.2/System.Buffers.dll": {}, "ref/netcoreapp2.2/System.Collections.Concurrent.dll": {}, "ref/netcoreapp2.2/System.Collections.Immutable.dll": {}, "ref/netcoreapp2.2/System.Collections.NonGeneric.dll": {}, "ref/netcoreapp2.2/System.Collections.Specialized.dll": {}, "ref/netcoreapp2.2/System.Collections.dll": {}, "ref/netcoreapp2.2/System.ComponentModel.Annotations.dll": {}, "ref/netcoreapp2.2/System.ComponentModel.DataAnnotations.dll": {}, "ref/netcoreapp2.2/System.ComponentModel.EventBasedAsync.dll": {}, "ref/netcoreapp2.2/System.ComponentModel.Primitives.dll": {}, "ref/netcoreapp2.2/System.ComponentModel.TypeConverter.dll": {}, "ref/netcoreapp2.2/System.ComponentModel.dll": {}, "ref/netcoreapp2.2/System.Configuration.dll": {}, "ref/netcoreapp2.2/System.Console.dll": {}, "ref/netcoreapp2.2/System.Core.dll": {}, "ref/netcoreapp2.2/System.Data.Common.dll": {}, "ref/netcoreapp2.2/System.Data.dll": {}, "ref/netcoreapp2.2/System.Diagnostics.Contracts.dll": {}, "ref/netcoreapp2.2/System.Diagnostics.Debug.dll": {}, "ref/netcoreapp2.2/System.Diagnostics.FileVersionInfo.dll": {}, "ref/netcoreapp2.2/System.Diagnostics.Process.dll": {}, "ref/netcoreapp2.2/System.Diagnostics.StackTrace.dll": {}, "ref/netcoreapp2.2/System.Diagnostics.TextWriterTraceListener.dll": {}, "ref/netcoreapp2.2/System.Diagnostics.Tools.dll": {}, "ref/netcoreapp2.2/System.Diagnostics.TraceSource.dll": {}, "ref/netcoreapp2.2/System.Diagnostics.Tracing.dll": {}, "ref/netcoreapp2.2/System.Drawing.Primitives.dll": {}, "ref/netcoreapp2.2/System.Drawing.dll": {}, "ref/netcoreapp2.2/System.Dynamic.Runtime.dll": {}, "ref/netcoreapp2.2/System.Globalization.Calendars.dll": {}, "ref/netcoreapp2.2/System.Globalization.Extensions.dll": {}, "ref/netcoreapp2.2/System.Globalization.dll": {}, "ref/netcoreapp2.2/System.IO.Compression.Brotli.dll": {}, "ref/netcoreapp2.2/System.IO.Compression.FileSystem.dll": {}, "ref/netcoreapp2.2/System.IO.Compression.ZipFile.dll": {}, "ref/netcoreapp2.2/System.IO.Compression.dll": {}, "ref/netcoreapp2.2/System.IO.FileSystem.DriveInfo.dll": {}, "ref/netcoreapp2.2/System.IO.FileSystem.Primitives.dll": {}, "ref/netcoreapp2.2/System.IO.FileSystem.Watcher.dll": {}, "ref/netcoreapp2.2/System.IO.FileSystem.dll": {}, "ref/netcoreapp2.2/System.IO.IsolatedStorage.dll": {}, "ref/netcoreapp2.2/System.IO.MemoryMappedFiles.dll": {}, "ref/netcoreapp2.2/System.IO.Pipes.dll": {}, "ref/netcoreapp2.2/System.IO.UnmanagedMemoryStream.dll": {}, "ref/netcoreapp2.2/System.IO.dll": {}, "ref/netcoreapp2.2/System.Linq.Expressions.dll": {}, "ref/netcoreapp2.2/System.Linq.Parallel.dll": {}, "ref/netcoreapp2.2/System.Linq.Queryable.dll": {}, "ref/netcoreapp2.2/System.Linq.dll": {}, "ref/netcoreapp2.2/System.Memory.dll": {}, "ref/netcoreapp2.2/System.Net.Http.dll": {}, "ref/netcoreapp2.2/System.Net.HttpListener.dll": {}, "ref/netcoreapp2.2/System.Net.Mail.dll": {}, "ref/netcoreapp2.2/System.Net.NameResolution.dll": {}, "ref/netcoreapp2.2/System.Net.NetworkInformation.dll": {}, "ref/netcoreapp2.2/System.Net.Ping.dll": {}, "ref/netcoreapp2.2/System.Net.Primitives.dll": {}, "ref/netcoreapp2.2/System.Net.Requests.dll": {}, "ref/netcoreapp2.2/System.Net.Security.dll": {}, "ref/netcoreapp2.2/System.Net.ServicePoint.dll": {}, "ref/netcoreapp2.2/System.Net.Sockets.dll": {}, "ref/netcoreapp2.2/System.Net.WebClient.dll": {}, "ref/netcoreapp2.2/System.Net.WebHeaderCollection.dll": {}, "ref/netcoreapp2.2/System.Net.WebProxy.dll": {}, "ref/netcoreapp2.2/System.Net.WebSockets.Client.dll": {}, "ref/netcoreapp2.2/System.Net.WebSockets.dll": {}, "ref/netcoreapp2.2/System.Net.dll": {}, "ref/netcoreapp2.2/System.Numerics.Vectors.dll": {}, "ref/netcoreapp2.2/System.Numerics.dll": {}, "ref/netcoreapp2.2/System.ObjectModel.dll": {}, "ref/netcoreapp2.2/System.Reflection.DispatchProxy.dll": {}, "ref/netcoreapp2.2/System.Reflection.Emit.ILGeneration.dll": {}, "ref/netcoreapp2.2/System.Reflection.Emit.Lightweight.dll": {}, "ref/netcoreapp2.2/System.Reflection.Emit.dll": {}, "ref/netcoreapp2.2/System.Reflection.Extensions.dll": {}, "ref/netcoreapp2.2/System.Reflection.Metadata.dll": {}, "ref/netcoreapp2.2/System.Reflection.Primitives.dll": {}, "ref/netcoreapp2.2/System.Reflection.TypeExtensions.dll": {}, "ref/netcoreapp2.2/System.Reflection.dll": {}, "ref/netcoreapp2.2/System.Resources.Reader.dll": {}, "ref/netcoreapp2.2/System.Resources.ResourceManager.dll": {}, "ref/netcoreapp2.2/System.Resources.Writer.dll": {}, "ref/netcoreapp2.2/System.Runtime.CompilerServices.VisualC.dll": {}, "ref/netcoreapp2.2/System.Runtime.Extensions.dll": {}, "ref/netcoreapp2.2/System.Runtime.Handles.dll": {}, "ref/netcoreapp2.2/System.Runtime.InteropServices.RuntimeInformation.dll": {}, "ref/netcoreapp2.2/System.Runtime.InteropServices.WindowsRuntime.dll": {}, "ref/netcoreapp2.2/System.Runtime.InteropServices.dll": {}, "ref/netcoreapp2.2/System.Runtime.Loader.dll": {}, "ref/netcoreapp2.2/System.Runtime.Numerics.dll": {}, "ref/netcoreapp2.2/System.Runtime.Serialization.Formatters.dll": {}, "ref/netcoreapp2.2/System.Runtime.Serialization.Json.dll": {}, "ref/netcoreapp2.2/System.Runtime.Serialization.Primitives.dll": {}, "ref/netcoreapp2.2/System.Runtime.Serialization.Xml.dll": {}, "ref/netcoreapp2.2/System.Runtime.Serialization.dll": {}, "ref/netcoreapp2.2/System.Runtime.dll": {}, "ref/netcoreapp2.2/System.Security.Claims.dll": {}, "ref/netcoreapp2.2/System.Security.Cryptography.Algorithms.dll": {}, "ref/netcoreapp2.2/System.Security.Cryptography.Csp.dll": {}, "ref/netcoreapp2.2/System.Security.Cryptography.Encoding.dll": {}, "ref/netcoreapp2.2/System.Security.Cryptography.Primitives.dll": {}, "ref/netcoreapp2.2/System.Security.Cryptography.X509Certificates.dll": {}, "ref/netcoreapp2.2/System.Security.Principal.dll": {}, "ref/netcoreapp2.2/System.Security.SecureString.dll": {}, "ref/netcoreapp2.2/System.Security.dll": {}, "ref/netcoreapp2.2/System.ServiceModel.Web.dll": {}, "ref/netcoreapp2.2/System.ServiceProcess.dll": {}, "ref/netcoreapp2.2/System.Text.Encoding.Extensions.dll": {}, "ref/netcoreapp2.2/System.Text.Encoding.dll": {}, "ref/netcoreapp2.2/System.Text.RegularExpressions.dll": {}, "ref/netcoreapp2.2/System.Threading.Overlapped.dll": {}, "ref/netcoreapp2.2/System.Threading.Tasks.Dataflow.dll": {}, "ref/netcoreapp2.2/System.Threading.Tasks.Extensions.dll": {}, "ref/netcoreapp2.2/System.Threading.Tasks.Parallel.dll": {}, "ref/netcoreapp2.2/System.Threading.Tasks.dll": {}, "ref/netcoreapp2.2/System.Threading.Thread.dll": {}, "ref/netcoreapp2.2/System.Threading.ThreadPool.dll": {}, "ref/netcoreapp2.2/System.Threading.Timer.dll": {}, "ref/netcoreapp2.2/System.Threading.dll": {}, "ref/netcoreapp2.2/System.Transactions.Local.dll": {}, "ref/netcoreapp2.2/System.Transactions.dll": {}, "ref/netcoreapp2.2/System.ValueTuple.dll": {}, "ref/netcoreapp2.2/System.Web.HttpUtility.dll": {}, "ref/netcoreapp2.2/System.Web.dll": {}, "ref/netcoreapp2.2/System.Windows.dll": {}, "ref/netcoreapp2.2/System.Xml.Linq.dll": {}, "ref/netcoreapp2.2/System.Xml.ReaderWriter.dll": {}, "ref/netcoreapp2.2/System.Xml.Serialization.dll": {}, "ref/netcoreapp2.2/System.Xml.XDocument.dll": {}, "ref/netcoreapp2.2/System.Xml.XPath.XDocument.dll": {}, "ref/netcoreapp2.2/System.Xml.XPath.dll": {}, "ref/netcoreapp2.2/System.Xml.XmlDocument.dll": {}, "ref/netcoreapp2.2/System.Xml.XmlSerializer.dll": {}, "ref/netcoreapp2.2/System.Xml.dll": {}, "ref/netcoreapp2.2/System.dll": {}, "ref/netcoreapp2.2/WindowsBase.dll": {}, "ref/netcoreapp2.2/mscorlib.dll": {}, "ref/netcoreapp2.2/netstandard.dll": {}}, "compileOnly": true}, "Microsoft.NETCore.DotNetAppHost/2.2.0": {"compileOnly": true}, "Microsoft.NETCore.DotNetHostPolicy/2.2.0": {"dependencies": {"Microsoft.NETCore.DotNetHostResolver": "2.2.0"}, "compileOnly": true}, "Microsoft.NETCore.DotNetHostResolver/2.2.0": {"dependencies": {"Microsoft.NETCore.DotNetAppHost": "2.2.0"}, "compileOnly": true}, "Microsoft.NETCore.Targets/2.0.0": {"compileOnly": true}, "NETStandard.Library/2.0.3": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0"}, "compileOnly": true}, "Remotion.Linq/2.2.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Linq.Queryable": "4.0.1", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"lib/netstandard1.0/Remotion.Linq.dll": {}}, "compileOnly": true}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"compileOnly": true}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"compileOnly": true}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"compileOnly": true}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.0.0"}, "compileOnly": true}, "runtime.native.System.Data.SqlClient.sni/4.5.0": {"dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}, "compileOnly": true}, "runtime.native.System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.0.0"}, "compileOnly": true}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.0.0"}, "compileOnly": true}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}, "compileOnly": true}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}, "compileOnly": true}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"compileOnly": true}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"compileOnly": true}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"compileOnly": true}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"compileOnly": true}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"compileOnly": true}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"compileOnly": true}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"compileOnly": true}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"compileOnly": true}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"compileOnly": true}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"compileOnly": true}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"compileOnly": true}, "System.AppContext/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.0.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compileOnly": true}, "System.Collections.Immutable/1.5.0": {"compileOnly": true}, "System.Collections.NonGeneric/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compileOnly": true}, "System.Collections.Specialized/4.3.0": {"dependencies": {"System.Collections.NonGeneric": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compileOnly": true}, "System.ComponentModel.Annotations/4.5.0": {"compileOnly": true}, "System.Console/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.0.0", "System.IO": "4.3.0", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0"}, "compileOnly": true}, "System.Data.SqlClient/4.6.0": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0", "System.Security.Principal.Windows": "5.0.0", "System.Text.Encoding.CodePages": "5.0.0", "runtime.native.System.Data.SqlClient.sni": "4.5.0"}, "compile": {"ref/netcoreapp2.1/System.Data.SqlClient.dll": {}}, "compileOnly": true}, "System.Diagnostics.Contracts/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.0.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Diagnostics.FileVersionInfo/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Reflection.Metadata": "1.6.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}, "compileOnly": true}, "System.Diagnostics.StackTrace/4.3.0": {"dependencies": {"System.IO.FileSystem": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Metadata": "1.6.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Diagnostics.Tools/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.0.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.0.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Dynamic.Runtime/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.4.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compileOnly": true}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.0.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.0.0", "System.Globalization": "4.3.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}, "compileOnly": true}, "System.IdentityModel.Tokens.Jwt/5.3.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "5.3.0", "Microsoft.IdentityModel.Tokens": "5.3.0", "Newtonsoft.Json": "13.0.1"}, "compile": {"lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll": {}}, "compileOnly": true}, "System.Interactive.Async/3.2.0": {"compile": {"lib/netstandard2.0/System.Interactive.Async.dll": {}}, "compileOnly": true}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.0.0", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compileOnly": true}, "System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Buffers": "4.5.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}, "compileOnly": true}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.0.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compileOnly": true}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}, "compileOnly": true}, "System.IO.Pipelines/4.5.2": {"compile": {"ref/netstandard1.3/System.IO.Pipelines.dll": {}}, "compileOnly": true}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}, "compileOnly": true}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.7.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.4.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compileOnly": true}, "System.Linq.Queryable/4.0.1": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "4.7.1", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}, "compileOnly": true}, "System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.0.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}, "compileOnly": true}, "System.Net.WebSockets.WebSocketProtocol/4.5.1": {"compile": {"ref/netstandard2.0/System.Net.WebSockets.WebSocketProtocol.dll": {}}, "compileOnly": true}, "System.Numerics.Vectors/4.5.0": {"compileOnly": true}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Threading": "4.3.0"}, "compileOnly": true}, "System.Private.DataContractSerialization/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.7.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.4.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0", "System.Xml.XmlDocument": "4.3.0", "System.Xml.XmlSerializer": "4.3.0"}, "compileOnly": true}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.0.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.0.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Reflection.Metadata/1.6.0": {"compileOnly": true}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.0.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.0.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.0.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.0.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.0.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}, "compileOnly": true}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}, "compileOnly": true}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}, "compileOnly": true}, "System.Runtime.Serialization.Primitives/4.3.0": {"dependencies": {"System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Runtime.Serialization.Xml/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Private.DataContractSerialization": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}, "compileOnly": true}, "System.Security.Claims/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Security.Principal": "4.3.0"}, "compileOnly": true}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}, "compileOnly": true}, "System.Security.Cryptography.Cng/4.5.0": {"compile": {"ref/netcoreapp2.1/System.Security.Cryptography.Cng.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}, "compileOnly": true}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}, "compileOnly": true}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}, "compileOnly": true}, "System.Security.Cryptography.Pkcs/4.5.0": {"dependencies": {"System.Security.Cryptography.Cng": "4.5.0"}, "compileOnly": true}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compileOnly": true}, "System.Security.Cryptography.X509Certificates/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.5.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}, "compileOnly": true}, "System.Security.Cryptography.Xml/4.5.0": {"dependencies": {"System.Security.Cryptography.Pkcs": "4.5.0", "System.Security.Permissions": "4.5.0"}, "compile": {"ref/netstandard2.0/System.Security.Cryptography.Xml.dll": {}}, "compileOnly": true}, "System.Security.Permissions/4.5.0": {"dependencies": {"System.Security.AccessControl": "5.0.0"}, "compile": {"ref/netstandard2.0/System.Security.Permissions.dll": {}}, "compileOnly": true}, "System.Security.Principal/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.0.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.0.0", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0"}, "compileOnly": true}, "System.Text.RegularExpressions/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.1", "System.Threading.Tasks": "4.3.0"}, "compileOnly": true}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.0.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Threading.Tasks.Parallel/4.3.0": {"dependencies": {"System.Collections.Concurrent": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compileOnly": true}, "System.Threading.Thread/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "compileOnly": true}, "System.Xml.XDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}, "compileOnly": true}, "System.Xml.XmlDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}, "compileOnly": true}, "System.Xml.XmlSerializer/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.4.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "compileOnly": true}, "System.Xml.XPath/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}, "compileOnly": true}, "System.Xml.XPath.XDocument/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0", "System.Xml.XPath": "4.3.0"}, "compileOnly": true}}}, "libraries": {"EBizAutos.Apps.UsersManagement.Api/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Amazon.AspNetCore.DataProtection.SSM/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-2OG9McdQ0zheFSp0bVpHRzYik40W6rgvLZS94A/BCXhuxD8m9GaKuVoFQUZc43egJUSQli/J0gesxaBpeUmNmw==", "path": "amazon.aspnetcore.dataprotection.ssm/1.1.0", "hashPath": "amazon.aspnetcore.dataprotection.ssm.1.1.0.nupkg.sha512"}, "Amazon.Extensions.Configuration.SystemsManager/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-ejZvpCO9fifoM+14uy+BKKazW+9My61IpVuYp+b0MfaPJoPkz6LNKTgRXjNiZWO/9mRr3Zi/fEKsSDwEtMnvaA==", "path": "amazon.extensions.configuration.systemsmanager/2.1.1", "hashPath": "amazon.extensions.configuration.systemsmanager.2.1.1.nupkg.sha512"}, "Apache.NMS/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uWsheNh+Bg1rGegOlsWuyrs5dO8xTd+7x4ZsCkrh3vnLBjQ1lsG1kloYBvMj5BBNn2Gcd6jP8YKIgp1Re0oiBA==", "path": "apache.nms/2.0.0", "hashPath": "apache.nms.2.0.0.nupkg.sha512"}, "Apache.NMS.ActiveMQ/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-pJWWXS8SCWKGMoTzZB5LxuMhzGaXh1eg9yDMiTvmkGtaDfLRJI2n1yN/Wpiy1e62acCMiBxvnH07U8JwX08KjQ==", "path": "apache.nms.activemq/2.0.0", "hashPath": "apache.nms.activemq.2.0.0.nupkg.sha512"}, "Automatonymous/5.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-7B0upgJUTJ7fIwFjGEIxOMVJTxqBPyjtnua5i+VyClvHJd31t64SPIm4nhYxI7PF4afmvv5a3dtFCz1W1tQc0A==", "path": "automatonymous/5.1.3", "hashPath": "automatonymous.5.1.3.nupkg.sha512"}, "AWSSDK.Core/**********": {"type": "package", "serviceable": true, "sha512": "sha512-akydySw5e74IM9Y95X8XQ21e4b9oCZmz756ZsLW4eSt/ZI988zdoq8NObVr6nbLNlG7DHNM53d6AAAIx7q/0tQ==", "path": "awssdk.core/**********", "hashPath": "awssdk.core.**********.nupkg.sha512"}, "AWSSDK.Extensions.NETCore.Setup/3.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-cm7D75uispA3h/WwMEQVBMmFPPFwRvFQz8fyLwJEB9uqWYfTOMrRTcynMxHnrdoMyQnfCjByRTzHuFgWELmB6Q==", "path": "awssdk.extensions.netcore.setup/3.7.1", "hashPath": "awssdk.extensions.netcore.setup.3.7.1.nupkg.sha512"}, "AWSSDK.S3/**********": {"type": "package", "serviceable": true, "sha512": "sha512-rAath6wuVmv8kaeXJK2vUzjvEpe2qMt7dkL5wWgQ11juqh3wyizWzytlsaBKyAmXs1EKqc9gF5GVW5PbjbWnaA==", "path": "awssdk.s3/**********", "hashPath": "awssdk.s3.**********.nupkg.sha512"}, "AWSSDK.SecurityToken/3.7.100.14": {"type": "package", "serviceable": true, "sha512": "sha512-dGCVuVo0CFUKWW85W8YENO+aREf8sCBDjvGbnNvxJuNW4Ss+brEU9ltHhq2KfZze2VUNK1/wygbPG1bmbpyXEw==", "path": "awssdk.securitytoken/3.7.100.14", "hashPath": "awssdk.securitytoken.3.7.100.14.nupkg.sha512"}, "AWSSDK.SimpleSystemsManagement/********": {"type": "package", "serviceable": true, "sha512": "sha512-fyGmcOFFitWMm+smKJj6MvNR/xU+jJZheP7K4hId33PQjokJVsNPLfRKUyvyPe7iUGdSMPWuU923lc1bbr+7eA==", "path": "awssdk.simplesystemsmanagement/********", "hashPath": "awssdk.simplesystemsmanagement.********.nupkg.sha512"}, "Castle.Core/4.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-zanbjWC0Y05gbx4eGXkzVycOQqVOFVeCjVsDSyuao9P4mtN1w3WxxTo193NGC7j3o2u3AJRswaoC6hEbnGACnQ==", "path": "castle.core/4.4.1", "hashPath": "castle.core.4.4.1.nupkg.sha512"}, "CompareNETObjects/4.57.0": {"type": "package", "serviceable": true, "sha512": "sha512-0uCvlZwCtYAjEvMwRZAqFnXtkGiXZCCgW1Y/fzH6k/yIVlN4pqaRwSfxRSKezFlg6UG1XXoSZtvmAEt7X2AKhg==", "path": "comparenetobjects/4.57.0", "hashPath": "comparenetobjects.4.57.0.nupkg.sha512"}, "Coravel/3.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-RPQk+QMkSkwXAnCyfQRNqAOAcLTRC3UGpMsHIdxw5sCgLCDsonCjsYGbY1SsjR6agndN5K3bDcRB4DjXIvHCUg==", "path": "coravel/3.6.1", "hashPath": "coravel.3.6.1.nupkg.sha512"}, "Dapper/1.50.5": {"type": "package", "serviceable": true, "sha512": "sha512-1vPpX7WQmQCIb7rwlGOUoVs/yWZhVKvdhuG7WrJV+V+qsP8btnrrCqVWHENAlJxBAnUw5rhWfmuba9/Egei9MA==", "path": "dapper/1.50.5", "hashPath": "dapper.1.50.5.nupkg.sha512"}, "DeepCloner/0.10.2": {"type": "package", "serviceable": true, "sha512": "sha512-QJTEz5Y9m74S0gzarkIdljlbyx3gNOKM/UT9WZR5bS/pYZb6UX59QQWzLnu8KZc5jajQXtr/rHJcAGB39Ne6CA==", "path": "deepcloner/0.10.2", "hashPath": "deepcloner.0.10.2.nupkg.sha512"}, "DnsClient/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-4H/f2uYJOZ+YObZjpY9ABrKZI+JNw3uizp6oMzTXwDw6F+2qIPhpRl/1t68O/6e98+vqNiYGu+lswmwdYUy3gg==", "path": "dnsclient/1.6.1", "hashPath": "dnsclient.1.6.1.nupkg.sha512"}, "Elasticsearch.Net/7.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-MFjokKp8YrdnB1CR+LSveVa6j+pKWrHJtGV/b9A300eOyq+RWacJfIa0Qv/SsDqaUErga63J6RmqMM/ICi4vHA==", "path": "elasticsearch.net/7.1.0", "hashPath": "elasticsearch.net.7.1.0.nupkg.sha512"}, "EnyimMemcachedCore/2.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-BjyIhnS1/NVJ2WIxNaFQMFqsAIFgz9AUTOdvDkdRqJn09zb7QnW5hvWH9OJi/RLGQvI52I+hn6OFTUJRU/EAXw==", "path": "enyimmemcachedcore/2.1.8", "hashPath": "enyimmemcachedcore.2.1.8.nupkg.sha512"}, "Experimental.System.Messaging/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3iV3yEL3cq5UL0onKmdyF8Z180uRF4bQzOcKwS798Tx8L792aNXTLWiYuViKOT2J6tNqVpJh9agfPa2GnIWNsQ==", "path": "experimental.system.messaging/1.0.0", "hashPath": "experimental.system.messaging.1.0.0.nupkg.sha512"}, "GreenPipes/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-nc90y7DAhj8isRbqioVQF3/ExztBZSXRrRoplZvEjckNFC5wP1r+ssfsgl8BptWdQrnMdgkOYhQ6EnHetyFW1Q==", "path": "greenpipes/4.0.1", "hashPath": "greenpipes.4.0.1.nupkg.sha512"}, "Magick.NET-Q16-AnyCPU/7.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-F6usuiffkAblVeAJ2Yq1InH0XW6XhNi0PzYLjQoVEszOy2jRJLzKWleOR+tIs0H4sDqCpbtXD/MZ5sLMMSK2Og==", "path": "magick.net-q16-anycpu/7.11.0", "hashPath": "magick.net-q16-anycpu.7.11.0.nupkg.sha512"}, "Mapster/4.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-mx4e5faOzMZTeP0zXQJ8m7p1Z4Myxh430nEgWqHAhsPdPCh/Y1S5CZvnk9MI4iBTgqrcWAztUS57DEfzJlG2Rw==", "path": "mapster/4.1.1", "hashPath": "mapster.4.1.1.nupkg.sha512"}, "MassTransit/7.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-dMOmC1ucSpgpW1M7ewQIw2Jf/d0lC4sx5LdUU5CSuFhlw4L7hssAB/glrbwvYZB9B90ZYvLJB8JLV1K/xhHBMA==", "path": "masstransit/7.3.1", "hashPath": "masstransit.7.3.1.nupkg.sha512"}, "MassTransit.ActiveMQ/7.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-nPoa3bKRI/e0ESeQ4cJdyXpOEFmicgof+yrsveB3pOPLM5KFjMIJItEHXRNCwzpefU7SZk5sssffOxZyuhntrw==", "path": "masstransit.activemq/7.3.3", "hashPath": "masstransit.activemq.7.3.3.nupkg.sha512"}, "MassTransit.Extensions.DependencyInjection/7.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-s1gnkYUngHNP9m2+Jx1vuUAvoVm4KB3oVbtFJUlXNtBBzhRFq1N3Y0w5iiew60ZBLm0j0RQcSRbI0ahMOkL+og==", "path": "masstransit.extensions.dependencyinjection/7.3.1", "hashPath": "masstransit.extensions.dependencyinjection.7.3.1.nupkg.sha512"}, "MassTransit.MongoDb/7.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-OpEmL39lEBeK5ySDB8zkskHdFoV6XEMDJ+1chgSVJKaIqvDla/B4prbJFQo78xYYBeslvBjLkk2xpWLXcthxtg==", "path": "masstransit.mongodb/7.3.1", "hashPath": "masstransit.mongodb.7.3.1.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.WindowsServices/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-614AnpWOeIx/cCRihy30H1QagwnvH9utCtUP2db2XGXMz2PHcbQf9Qp1S0KKF9wWJ+p7+LuVi+/99m9XJayqMg==", "path": "microsoft.aspnetcore.hosting.windowsservices/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.windowsservices.2.2.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-W8<PERSON>QjkMScOMTtJbPwmPyj9c3zYSFGawDW3jwlBOOsnY+EzZFLgNQ/UMkK35JmkNOVPdCyPr2Tw7Vv9N+KA3ZQ==", "path": "microsoft.bcl.asyncinterfaces/5.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.5.0.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-LuI1oG+24TUj1ZRQQjM5Ew73BKnZE5NZ/7eAdh1o8ST5dPhUnJvIkiIn2re3MwnkRy6ELRnvEbBxHP8uALKhJw==", "path": "microsoft.win32.systemevents/4.5.0", "hashPath": "microsoft.win32.systemevents.4.5.0.nupkg.sha512"}, "MongoDB.Bson/2.21.0": {"type": "package", "serviceable": true, "sha512": "sha512-QT+D1I3Jz6r6S6kCgJD1L9dRCLVJCKlkGRkA+tJ7uLpHRmjDNcNKy4D1T+L9gQrjl95lDN9PHdwEytdvCW/jzA==", "path": "mongodb.bson/2.21.0", "hashPath": "mongodb.bson.2.21.0.nupkg.sha512"}, "MongoDB.Driver/2.21.0": {"type": "package", "serviceable": true, "sha512": "sha512-VxKj1wuhadiXhaXkykCWRgsYOysdaOYJ202hJFz25UjkrqC/tHA8RS4hdS5HYfGWoI//fypBXnxZCkEjXLXdfw==", "path": "mongodb.driver/2.21.0", "hashPath": "mongodb.driver.2.21.0.nupkg.sha512"}, "MongoDB.Driver.Core/2.21.0": {"type": "package", "serviceable": true, "sha512": "sha512-Ac44U3bQfinmdH5KNFjTidJe9LKW87SxkXJ3YuIUJQMITEc4083YF1yvjJxaSeYF9er0YgHSmwhHpsZv0Fwplg==", "path": "mongodb.driver.core/2.21.0", "hashPath": "mongodb.driver.core.2.21.0.nupkg.sha512"}, "MongoDB.Driver.GridFS/2.21.0": {"type": "package", "serviceable": true, "sha512": "sha512-8a6n05xXWGh/CsY/egbZi8NF5S14c7IO05/eqi6cEsInlu4Dd1ZofFd3e4v0vRYOjtjUXImQ4xFTgOFvFTIAgg==", "path": "mongodb.driver.gridfs/2.21.0", "hashPath": "mongodb.driver.gridfs.2.21.0.nupkg.sha512"}, "MongoDB.Libmongocrypt/1.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-fgNw8Dxpkq7mpoaAYes8cfnPRzvFIoB8oL9GPXwi3op/rONftl0WAeg4akRLcxfoVuUvuUO2wGoVBr3JzJ7Svw==", "path": "mongodb.libmongocrypt/1.8.0", "hashPath": "mongodb.libmongocrypt.1.8.0.nupkg.sha512"}, "MongolianBarbecue/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Qeg8JGFCOGh8CmBMVJiE2ABujgFXtX1lDQtXJ+3cUfp2Ut55EO0Da9sMxKUBSx4Lzpivzy27TONGUMbgoAAiEQ==", "path": "mongolianbarbecue/1.0.0", "hashPath": "mongolianbarbecue.1.0.0.nupkg.sha512"}, "NEST/7.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-/Ij6XS5SnTeeZudTHypO8IlopiMZmyeljim7fz02UxwQPZ7duSmuw/bZu+ixKgXSWXGDH0thkdawv/81RZZKdA==", "path": "nest/7.1.0", "hashPath": "nest.7.1.0.nupkg.sha512"}, "NewId/3.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-72cBqYQ+pZpsfuTerhi5Q/ZkfK81s/Dkz2u0WNytirWVLktUm6sy4mRNepM+DL1AB0V61QlYesZHDf0wlmMU5A==", "path": "newid/3.0.3", "hashPath": "newid.3.0.3.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-QYFyxhaABwmq3p/21VrZNYvCg3DaEoN/wUuw5nmfAf0X3HLjgupwhkEWdgfb9nvGAUIv3osmZoD3kKl4jxEmYQ==", "path": "newtonsoft.json.bson/1.0.2", "hashPath": "newtonsoft.json.bson.1.0.2.nupkg.sha512"}, "Scrutor/3.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-eevFT6R8vDIB4bVDxFeEGQQJX3o7xUYcKMluf6XnnyPJEV3P4OP2oQmKXxgcy6qEkJXgEMddkVvgPl9J0dx09A==", "path": "scrutor/3.0.2", "hashPath": "scrutor.3.0.2.nupkg.sha512"}, "SharpCompress/0.30.1": {"type": "package", "serviceable": true, "sha512": "sha512-XqD4TpfyYGa7QTPzaGlMVbcecKnXy4YmYLDWrU+JIj7IuRNl7DH2END+Ll7ekWIY8o3dAMWLFDE1xdhfIWD1nw==", "path": "sharpcompress/0.30.1", "hashPath": "sharpcompress.0.30.1.nupkg.sha512"}, "SharpZipLib/1.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-N8+hwhsKZm25tDJfWpBSW7EGhH/R7EMuiX+KJ4C4u+fCWVc1lJ5zg1u3S1RPPVYgTqhx/C3hxrqUpi6RwK5+Tg==", "path": "sharpziplib/1.3.3", "hashPath": "sharpziplib.1.3.3.nupkg.sha512"}, "Snappier/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rFtK2KEI9hIe8gtx3a0YDXdHOpedIf9wYCEYtBEmtlyiWVX3XlCNV03JrmmAi/Cdfn7dxK+k0sjjcLv4fpHnqA==", "path": "snappier/1.0.0", "hashPath": "snappier.1.0.0.nupkg.sha512"}, "Swashbuckle.AspNetCore/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Zq6lkFieuFPNDwXwQ+e8i5zy2VMrexcRFU8mQORxqIc8r7Y+qKX63vg57yL1HeGCINHQGGzxGfw2rP63IeEqhg==", "path": "swashbuckle.aspnetcore/4.0.1", "hashPath": "swashbuckle.aspnetcore.4.0.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.Annotations/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Eke+OE1T+ogLsSmVFdhFPaVlsCBvGfkO/qpOgmD8pBns5vUMaI3pHjKq4sg9FfK8lLK/cxFbLx8Q+/iGogJ+Xw==", "path": "swashbuckle.aspnetcore.annotations/4.0.1", "hashPath": "swashbuckle.aspnetcore.annotations.4.0.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.Filters/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-/nLPAqxdWu9KbpXSNJn3uchv/Ct8gNrNF3zaAiTGG2KdWC8DodgX+1kIKoHO/yGoHM5cHKS/z2233wFL3dGMvg==", "path": "swashbuckle.aspnetcore.filters/4.5.5", "hashPath": "swashbuckle.aspnetcore.filters.4.5.5.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-rqzS3vHrjz+tR5j0nZOKZyaMTDfLGbVYkwMq205aYuGbsiGwbOlNU0Q8lq4Q0ptQPMKVkUf8XouCIdJ3qpK17w==", "path": "swashbuckle.aspnetcore.swagger/4.0.1", "hashPath": "swashbuckle.aspnetcore.swagger.4.0.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ztAj0T1U+2AqQNA8b+nr8yVkDW9XzNaAfez6d1jO13sdn2A/JW5Syn9TThsakrHxYNLt6y6aQCXbyBfQXpcQwA==", "path": "swashbuckle.aspnetcore.swaggergen/4.0.1", "hashPath": "swashbuckle.aspnetcore.swaggergen.4.0.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-d2U7NyV0e4UhyCzAVK9QHm0iz2QoVPUa9XzJ/Gr0rn/jBZWFpVLvigKv0vxFzO2E793sY605+4h885gvCdKSxQ==", "path": "swashbuckle.aspnetcore.swaggerui/4.0.1", "hashPath": "swashbuckle.aspnetcore.swaggerui.4.0.1.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.ComponentModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyGn1jGRZVfxnh8EdvDCi71v3bMXrsu8aYJOwoV7SNDLVhiEqwP86pPMyRGsDsxhXAm2b3o9OIqeETfN5qfezw==", "path": "system.componentmodel/4.3.0", "hashPath": "system.componentmodel.4.3.0.nupkg.sha512"}, "System.ComponentModel.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-j8GUkCpM8V4d4vhLIIoBLGey2Z5bCkMVNjEZseyAlm4n5arcsJOeI3zkUP+zvZgzsbLTYh4lYeP/ZD/gdIAPrw==", "path": "system.componentmodel.primitives/4.3.0", "hashPath": "system.componentmodel.primitives.4.3.0.nupkg.sha512"}, "System.ComponentModel.TypeConverter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-16pQ6P+EdhcXzPiEK4kbA953Fu0MNG2ovxTZU81/qsCd1zPRsKc3uif5NgvllCY598k6bI0KUyKW8fanlfaDQg==", "path": "system.componentmodel.typeconverter/4.3.0", "hashPath": "system.componentmodel.typeconverter.4.3.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-UIFvaFfuKhLr9u5tWMxmVoDPkFeD+Qv8gUuap4aZgVGYSYMdERck4OhLN/2gulAc0nYTEigWXSJNNWshrmxnng==", "path": "system.configuration.configurationmanager/4.5.0", "hashPath": "system.configuration.configurationmanager.4.5.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/4.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-j81Lovt90PDAq8kLpaJfJKV/rWdWuEk6jfV+MBkee33vzYLEUsy4gXK8laa9V2nZlLM9VM9yA/OOQxxPEJKAMw==", "path": "system.diagnostics.diagnosticsource/4.7.1", "hashPath": "system.diagnostics.diagnosticsource.4.7.1.nupkg.sha512"}, "System.Diagnostics.EventLog/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QaQAhEk18QSBPSu4VjXcznvjlg45IoXcJJNS5hcoqyyLj58g/SzQwpYXUrdzo+UtHV0grmOzFwABxhCYSTTp5Q==", "path": "system.diagnostics.eventlog/4.5.0", "hashPath": "system.diagnostics.eventlog.4.5.0.nupkg.sha512"}, "System.Diagnostics.TraceSource/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VnYp1NxGx8Ww731y2LJ1vpfb/DKVNKEZ8Jsh5SgQTZREL/YpWRArgh9pI8CDLmgHspZmLL697CaLvH85qQpRiw==", "path": "system.diagnostics.tracesource/4.3.0", "hashPath": "system.diagnostics.tracesource.4.3.0.nupkg.sha512"}, "System.Drawing.Common/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-GiyeGi/v4xYDz1vCNFwFvhz9k1XddOG7VD3jxRqzRBCbTHji+s3HxxbxtoymuK4OadEpgotI8zQ5+GEEH9sUEQ==", "path": "system.drawing.common/4.5.1", "hashPath": "system.drawing.common.4.5.1.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Private.ServiceModel/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-ancrQgJagx+yC4SZbuE+eShiEAUIF0E1d21TRSoy1C/rTwafAVcBr/fKibkq5TQzyy9uNil2tx2/iaUxsy0S9g==", "path": "system.private.servicemodel/4.5.3", "hashPath": "system.private.servicemodel.4.5.3.nupkg.sha512"}, "System.Reflection.DispatchProxy/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-+UW1hq11TNSeb+16rIk8hRQ02o339NFyzMc4ma/FqmxBzM30l1c2IherBB4ld1MNcenS48fz8tbt50OW4rVULA==", "path": "system.reflection.dispatchproxy/4.5.0", "hashPath": "system.reflection.dispatchproxy.4.5.0.nupkg.sha512"}, "System.Reflection.Emit/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-VR4kk8XLKebQ4MZuKuIni/7oh+QGFmZW3qORd1GvBq/8026OpW501SzT/oypwiQl4TvT8ErnReh/NzY9u+C6wQ==", "path": "system.reflection.emit/4.7.0", "hashPath": "system.reflection.emit.4.7.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-a4OLB4IITxAXJeV74MDx49Oq2+PsF6Sml54XAFv+2RyWwtDBcabzoxiiJRhdhx+gaohLh4hEGCLQyBozXoQPqA==", "path": "system.reflection.emit.lightweight/4.7.0", "hashPath": "system.reflection.emit.lightweight.4.7.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-dkmh/ySlwnXJp/1qYP9uyKkCK1CXR/REFzl7abHcArxBcV91mY2CgrrzSRA5Z/X4MevJWwXsklGRdR3A7K9zbg==", "path": "system.reflection.typeextensions/4.4.0", "hashPath": "system.reflection.typeextensions.4.4.0.nupkg.sha512"}, "System.Runtime/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-abhfv1dTK6NXOmu4bgHIONxHyEqFjW8HwXPmpY9gmll+ix9UNo4XDcmzJn6oLooftxNssVHdJC1pGT9jkSynQg==", "path": "system.runtime/4.3.1", "hashPath": "system.runtime.4.3.1.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZD9TMpsmYJLrxbbmdvhwt9YEgG5WntEnZ/d1eH8JBX9LBp+Ju8BSBhUGbZMNVHHomWo2KVImJhTDl2hIgw/6MA==", "path": "system.runtime.compilerservices.unsafe/5.0.0", "hashPath": "system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512"}, "System.Security.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "path": "system.security.accesscontrol/5.0.0", "hashPath": "system.security.accesscontrol.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-wLBKzFnDCxP12VL9ANydSYhk59fC4cvOr9ypYQLPnAj48NQIhqnjdD2yhP8yEKyBJEjERWS9DisKL7rX5eU25Q==", "path": "system.security.cryptography.protecteddata/4.5.0", "hashPath": "system.security.cryptography.protecteddata.4.5.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.ServiceModel.Duplex/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-cXn6zfl2od9Au3sDpArjUXo7zmNPLw77sjOrAUqjrh3TsImy8SPMSC4/F58jJGJrxUiyPo0DDwalRaF5JXZqsQ==", "path": "system.servicemodel.duplex/4.5.3", "hashPath": "system.servicemodel.duplex.4.5.3.nupkg.sha512"}, "System.ServiceModel.Http/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-zxR4z6G/FFK/uAUbo7+3IJOqm0w4/lyfHSQDf+hhUHRTc7XSeReGS5iKQq95gyl1ighHEuayqOiB7iacrB6ZUg==", "path": "system.servicemodel.http/4.5.3", "hashPath": "system.servicemodel.http.4.5.3.nupkg.sha512"}, "System.ServiceModel.NetTcp/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-Bx4oVK4ApBvZ0C3J62A8p3j6U0XK54JjN0byK52Qw4EgK89Uc48XzbF+0m1Oysc2bnnbrur+SwFWw7J8co3jTQ==", "path": "system.servicemodel.nettcp/4.5.3", "hashPath": "system.servicemodel.nettcp.4.5.3.nupkg.sha512"}, "System.ServiceModel.Primitives/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-Wc9Hgg4Cmqi416zvEgq2sW1YYCGuhwWzspDclJWlFZqY6EGhFUPZU+kVpl5z9kAgrSOQP7/Uiik+PtSQtmq+5A==", "path": "system.servicemodel.primitives/4.5.3", "hashPath": "system.servicemodel.primitives.4.5.3.nupkg.sha512"}, "System.ServiceModel.Security/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-e0c5f536zJ2qEp8sDbI88Tm+NLkx9eqGiXQbQx5fQEtCfQ/dqPOwluu/3aAj/9Bc5XdBAaQcElmr1kyjr2j3EA==", "path": "system.servicemodel.security/4.5.3", "hashPath": "system.servicemodel.security.4.5.3.nupkg.sha512"}, "System.ServiceProcess.ServiceController/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-8DGUtcNHf9TlvSVemKMFiqcOWJ4OdGBgvpcGL/cYossGf5ApMQdPUQS8vXHTBmlbYAcG+JXsjMFGAHp2oJrr+Q==", "path": "system.serviceprocess.servicecontroller/4.5.0", "hashPath": "system.serviceprocess.servicecontroller.4.5.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NyscU59xX6Uo91qvhOs2Ccho3AR2TnZPomo1Z0K6YpyztBPM/A5VbkzOO19sy3A3i1TtEnTxA7bCe3Us+r5MWg==", "path": "system.text.encoding.codepages/5.0.0", "hashPath": "system.text.encoding.codepages.5.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KmJ+CJXizDofbq6mpqDoRRLcxgOd2z9X3XoFNULSbvbqVRZkFX3istvr+MUjL6Zw1RT+RNdoI4GYidIINtgvqQ==", "path": "system.text.encodings.web/5.0.1", "hashPath": "system.text.encodings.web.5.0.1.nupkg.sha512"}, "System.Text.Json/5.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-I47dVIGiV6SfAyppphxqupertT/5oZkYLDCX6vC3HpOI4ZLjyoKAreUoem2ie6G0RbRuFrlqz/PcTQjfb2DOfQ==", "path": "system.text.json/5.0.2", "hashPath": "system.text.json.5.0.2.nupkg.sha512"}, "System.Threading.AccessControl/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZU4JNV9eHPw3TAdIJCDH07u9EfGFGgNJnaga8aFjcdvIIZKq4A+ZqaQNvUMFIbdCMPceYzt8JT5MdYIXAOlJ9A==", "path": "system.threading.accesscontrol/4.5.0", "hashPath": "system.threading.accesscontrol.4.5.0.nupkg.sha512"}, "System.Threading.Channels/4.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-6akRtHK/wab3246t4p5v3HQrtQk8LboOt5T4dtpNgsp3zvDeM4/Gx8V12t0h+c/W9/enUrilk8n6EQqdQorZAA==", "path": "system.threading.channels/4.7.1", "hashPath": "system.threading.channels.4.7.1.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.ValueTuple/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "path": "system.valuetuple/4.5.0", "hashPath": "system.valuetuple.4.5.0.nupkg.sha512"}, "TimeZoneConverter/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-U7Oilf3Ya6Rmu6gOaBfWyT3q0kwy2av6a5PfTn05CF54C+7DvuLsE3ljASvYmCpsSQeJvpnqU5Uzag6+ysWUeA==", "path": "timezoneconverter/5.0.0", "hashPath": "timezoneconverter.5.0.0.nupkg.sha512"}, "Twilio/5.20.1": {"type": "package", "serviceable": true, "sha512": "sha512-UKfpDK6032qFzcSAFZbSUlvphr/XZcOQ2epWXaV3w6O3d6DAUWA+pKtIxIBlocbCRJqVxvIJeKzF9DFgrX0Atw==", "path": "twilio/5.20.1", "hashPath": "twilio.5.20.1.nupkg.sha512"}, "Twilio.AspNet.Common/5.20.1": {"type": "package", "serviceable": true, "sha512": "sha512-Bexs6ktjjteOZWFWOSp6JHynA9WW2SeOZxjzQTBGBwlNrKvp+d0neRSpbbPGwakHd7BMCWA/SiKR6GC/Hc4eHA==", "path": "twilio.aspnet.common/5.20.1", "hashPath": "twilio.aspnet.common.5.20.1.nupkg.sha512"}, "Twilio.AspNet.Core/5.20.1": {"type": "package", "serviceable": true, "sha512": "sha512-1s/8rqdfyR5pUaoYDdzm3XADTePiJ1qcp2mQsUOYeAWSuKeJ5eDjsPRDydx8BJdQUPVSJVbBi3WdOOFFoh3MYQ==", "path": "twilio.aspnet.core/5.20.1", "hashPath": "twilio.aspnet.core.5.20.1.nupkg.sha512"}, "UAParser/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-q3HQawDOf6+8lzYyc+WDSPO2Sz0F5SpD7jaBm22bBy1i1/DtHTw7rPfuX5J7IRDVXxkJXp5QTL1NIvXCaKTkvQ==", "path": "uaparser/3.0.0", "hashPath": "uaparser.3.0.0.nupkg.sha512"}, "ZstdSharp.Port/0.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-jPao/LdUNLUz8rn3H1D8W7wQbZsRZM0iayvWI4xGejJg3XJHT56gcmYdgmCGPdJF1UEBqUjucCRrFB+4HbJsbw==", "path": "zstdsharp.port/0.6.2", "hashPath": "zstdsharp.port.0.6.2.nupkg.sha512"}, "CommonLibCore/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "EBizAutos.ApplicationCommonLib/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "EBizAutos.Apps.Authentication.CommonLib/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "EBizAutos.Apps.Authentication.MongoDbRepository/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "EBizAutos.Apps.Common.MongoDbRepository/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "EBizAutos.Apps.CommonLib/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "EBizAutos.Apps.MongoDbRepository/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "EBizAutos.Apps.ServiceBus/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "EBizAutos.Apps.Users.MongoDbRepository/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "EBizAutos.Apps.Users.MsSqlRepository/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "FoundationCommonLib/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "CommonLibCore.Reference/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "FoundationCommonLib.Reference/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.AspNet.WebApi.Client/5.2.6": {"type": "package", "serviceable": true, "sha512": "sha512-owAlEIUZXWSnkK8Z1c+zR47A0X6ykF4XjbPok4lQKNuciUfHLGPd6QnI+rt/8KlQ17PmF+I4S3f+m+Qe4IvViw==", "path": "microsoft.aspnet.webapi.client/5.2.6", "hashPath": "microsoft.aspnet.webapi.client.5.2.6.nupkg.sha512"}, "Microsoft.AspNetCore/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Bs75iht4lXS8uVWy/Cbsr9i0m2jRtnrfPEWU+6t0dQTZcJEfF9b7G2F7XvstLFWkAKSgYRzFkAwi/KypY0Qtew==", "path": "microsoft.aspnetcore/2.2.0", "hashPath": "microsoft.aspnetcore.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Antiforgery/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-fVQsSXNZz38Ysx8iKwwqfOLHhLrAeKEMBS5Ia3Lh7BJjOC2vPV28/yk08AovOMsB3SNQPGnE7bv+lsIBTmAkvw==", "path": "microsoft.aspnetcore.antiforgery/2.2.0", "hashPath": "microsoft.aspnetcore.antiforgery.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.App/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-L3W3kgOOU5+2Tdtnzywcs4/a3XFbwcM7Ghvr2uWnhLUvBithluWlGI+0/lXFrDysXaRMLSRJdExSLuSJJQYuTg==", "path": "microsoft.aspnetcore.app/2.2.0", "hashPath": "microsoft.aspnetcore.app.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-b0R9X7L6zMqNsssKDvhYHuNi5x0s4DyHTeXybIAyGaitKiW1Q5aAGKdV2codHPiePv9yHfC9hAMyScXQ/xXhPw==", "path": "microsoft.aspnetcore.authentication/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-VloMLDJMf3n/9ic5lCBOa42IBYJgyB1JhzLsL68Zqg+2bEPWfGBj/xCJy/LrKTArN0coOcZp3wyVTZlx0y9pHQ==", "path": "microsoft.aspnetcore.authentication.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Cookies/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Iar9VFlBHkZGdSG9ZUTmn6Q8Qg+6CtW5G/TyJI2F8B432TOH+nZlkU7O0W0byow6xsxqOYeTviSHz4cCJ3amfQ==", "path": "microsoft.aspnetcore.authentication.cookies/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.cookies.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-XlVJzJ5wPOYW+Y0J6Q/LVTEyfS4ssLXmt60T0SPP+D8abVhBTl+cgw2gDHlyKYIkcJg7btMVh383NDkMVqD/fg==", "path": "microsoft.aspnetcore.authentication.core/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.core.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Facebook/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-SOc/wjoBntSWVZ6uG0R/TqQ0xmxu2H1PhkuYxINYpkUB7s3cQQuRDyZtJIdQonzpWVwBRj0ImwktiMaBF/7ihQ==", "path": "microsoft.aspnetcore.authentication.facebook/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.facebook.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Google/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-norGVE0KRIT0pdNKhlLlsMi/7O69ACpx2RSj8rMHCoMRETCYH4PTqUbHI1kkfAGNUtcuQ8VIGIXSa1ZdGKWcdA==", "path": "microsoft.aspnetcore.authentication.google/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.google.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-FnyoLdiIo8KDobVcDuUYYFSbQYp1OR8vSMIOcW6M5+dtF9TC6XvCCS8Ook+DSbqUj6HPxwOIKa5BeIZm1/EpMw==", "path": "microsoft.aspnetcore.authentication.jwtbearer/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.MicrosoftAccount/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-troBjvJAMK7P2Vb5sDOzCztq9vR8BJtajDznam2XuQai7kLh5z7cmkB+2zMin+K/HzNjqItJSuSyuaK2PoZ8nA==", "path": "microsoft.aspnetcore.authentication.microsoftaccount/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.microsoftaccount.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.OAuth/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-i33SSdJd0g3ENRnHczgzrOlru3ciPsyYHMgAh90sbURS8wuBx0Y4xXfRQcYfu1W0/uiHQO832KNb/ICINWqLzA==", "path": "microsoft.aspnetcore.authentication.oauth/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.oauth.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.OpenIdConnect/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-y4iu3vMFnkMTaqT9mCJhD3XUMfavNP0CoOeNOHd7ArqZfgzs3GqAPcBc8Ld6mK2u5OOva8C6bhnQfRu9z0qJKQ==", "path": "microsoft.aspnetcore.authentication.openidconnect/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.openidconnect.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Twitter/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-wKfJeBL+13duv0o4q9zp4pW7UopBHaLafnq2GiIJTcu1x3RR/1N4sRIIppLSIJdulgM1XfNOivlIE2FEfZpmog==", "path": "microsoft.aspnetcore.authentication.twitter/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.twitter.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.WsFederation/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-TIBkO7Tx8uWXNL5Z7/6+iKdhTS+D9dpJMNcmiVxrAJUqxL4EWGHNqJyUp5yqI76GmbrT4GD23T3cUsSuCi7E0A==", "path": "microsoft.aspnetcore.authentication.wsfederation/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.wsfederation.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-/L0W8H3jMYWyaeA9gBJqS/tSWBegP9aaTM0mjRhxTttBY9z4RVDRYJ2CwPAmAXIuPr3r1sOw+CS8jFVRGHRezQ==", "path": "microsoft.aspnetcore.authorization/2.2.0", "hashPath": "microsoft.aspnetcore.authorization.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization.Policy/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-aJCo6niDRKuNg2uS2WMEmhJTooQUGARhV2ENQ2tO5443zVHUo19MSgrgGo9FIrfD+4yKPF8Q+FF33WkWfPbyKw==", "path": "microsoft.aspnetcore.authorization.policy/2.2.0", "hashPath": "microsoft.aspnetcore.authorization.policy.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Connections.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Aqr/16Cu5XmGv7mLKJvXRxhhd05UJ7cTTSaUV4MZ3ynAzfgWjsAdpIU8FWuxwAjmVdmI8oOWuVDrbs+sRkhKnA==", "path": "microsoft.aspnetcore.connections.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.connections.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.CookiePolicy/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Kn9CvhNsxRup/5SJfD4/YP3AbFwLJX8u3tKKyQszjUIvjE7M6lU93W44zlqBxltS94gTdLmo2ixPWDNeZthi1w==", "path": "microsoft.aspnetcore.cookiepolicy/2.2.0", "hashPath": "microsoft.aspnetcore.cookiepolicy.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Cors/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-LFlTM3ThS3ZCILuKnjy8HyK9/IlDh3opogdbCVx6tMGyDzTQBgMPXLjGDLtMk5QmLDCcP3l1TO3z/+1viA8GUg==", "path": "microsoft.aspnetcore.cors/2.2.0", "hashPath": "microsoft.aspnetcore.cors.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.Internal/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-GXmMD8/vuTLPLvKzKEPz/4vapC5e0cwx1tUVd83ePRyWF9CCrn/pg4/1I+tGkQqFLPvi3nlI2QtPtC6MQN8Nww==", "path": "microsoft.aspnetcore.cryptography.internal/2.2.0", "hashPath": "microsoft.aspnetcore.cryptography.internal.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-NCY0PH3nrFYbhqiq72rwWsUXlV4OAE0MOukvGvIBOTnEPMC1yVL42k1DXLnaIu+c0yfMAxIIG9Iuaykp9BQQQw==", "path": "microsoft.aspnetcore.cryptography.keyderivation/2.2.0", "hashPath": "microsoft.aspnetcore.cryptography.keyderivation.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-G6dvu5Nd2vjpYbzazZ//qBFbSEf2wmBUbyAR7E4AwO3gWjhoJD5YxpThcGJb7oE3VUcW65SVMXT+cPCiiBg8Sg==", "path": "microsoft.aspnetcore.dataprotection/2.2.0", "hashPath": "microsoft.aspnetcore.dataprotection.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-seANFXmp8mb5Y12m1ShiElJ3ZdOT3mBN3wA1GPhHJIvZ/BxOCPyqEOR+810OWsxEZwA5r5fDRNpG/CqiJmQnJg==", "path": "microsoft.aspnetcore.dataprotection.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.dataprotection.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection.Extensions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Goo1xU9WJnEJ0dKDgYFF+hFQqRMLKjf9zc8Bu3PaBdGncR7QwDMeFIkO7FEM6izaC38QjYrs1Q5AsmljkPyOrw==", "path": "microsoft.aspnetcore.dataprotection.extensions/2.2.0", "hashPath": "microsoft.aspnetcore.dataprotection.extensions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Diagnostics/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-RobNuZecn/eefWVApOE+OWAZXCdgfzm8pB7tBvJkahsjWfn1a+bLM9I2cuKlp/9aFBok1O/oDXlgYSvaQYu/yg==", "path": "microsoft.aspnetcore.diagnostics/2.2.0", "hashPath": "microsoft.aspnetcore.diagnostics.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Diagnostics.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-pva9ggfUDtnJIKzv0+wxwTX7LduDx6xLSpMqWwdOJkW52L0t31PI78+v+WqqMpUtMzcKug24jGs3nTFpAmA/2g==", "path": "microsoft.aspnetcore.diagnostics.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.diagnostics.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-xAIXyVmrTcVIJ38/j0TVMRlChC25k+cEAeSYotWhAnho3urzf1EfhoyyNdVytZbbBskue5i6XBL8gA1vlp5KGg==", "path": "microsoft.aspnetcore.diagnostics.entityframeworkcore/2.2.0", "hashPath": "microsoft.aspnetcore.diagnostics.entityframeworkcore.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Diagnostics.HealthChecks/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-RNmdLy9yncTprony49cuwhyTKoROpVflGM+pKlHA1671F00QUsjoY1Oi6xoa9XsUrfRDRYlxbt2CHYCMLzMh7Q==", "path": "microsoft.aspnetcore.diagnostics.healthchecks/2.2.0", "hashPath": "microsoft.aspnetcore.diagnostics.healthchecks.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.HostFiltering/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-JSX6ZlVWDkokZ+xCKDhUVQNqbmFn1lHQNzJc8K4Y/uTUocZS83+b/8Q7y/yx3oJ362etGMVy0keAvmCdqbP8nA==", "path": "microsoft.aspnetcore.hostfiltering/2.2.0", "hashPath": "microsoft.aspnetcore.hostfiltering.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-7t4RbUGugpHtQmzAkc9fpDdYJg6t/jcB2VVnjensVYbZFnLDU8pNrG0hrekk1DQG7P2UzpSqKLzDsFF0/lkkbw==", "path": "microsoft.aspnetcore.hosting/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ubycklv+ZY7Kutdwuy1W4upWcZ6VFR8WUXU7l7B2+mvbDBBPAcfpi+E+Y5GFe+Q157YfA3C49D2GCjAZc7Mobw==", "path": "microsoft.aspnetcore.hosting.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-1PMijw8RMtuQF60SsD/JlKtVfvh4NORAhF4wjysdABhlhTrYmtgssqyncR0Stq5vqtjplZcj6kbT4LRTglt9IQ==", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Html.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y4rs5aMEXY8G7wJo5S3EEt6ltqyOTr/qOeZzfn+hw/fuQj5GppGckMY5psGLETo1U9hcT5MmAhaT5xtusM1b5g==", "path": "microsoft.aspnetcore.html.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.html.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-YogBSMotWPAS/X5967pZ+yyWPQkThxhmzAwyCHCSSldzYBkW5W5d6oPfBaPqQOnSHYTpSOSOkpZoAce0vwb6+A==", "path": "microsoft.aspnetcore.http/2.2.0", "hashPath": "microsoft.aspnetcore.http.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Connections/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZcwAM9rE5yjGC+vtiNAK0INybpKIqnvB+/rntZn2/CPtyiBAtovVrEp4UZOoC31zH5t0P78ix9gLNJzII/ODsA==", "path": "microsoft.aspnetcore.http.connections/1.1.0", "hashPath": "microsoft.aspnetcore.http.connections.1.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Connections.Common/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-mYk5QUUjyXQmlyDHWDjkLYDArt97plwe6KsDsNVhDEQ+HgZMKGjISyM6YSA7BERQNR25kXBTbIYfSy1vePGQgg==", "path": "microsoft.aspnetcore.http.connections.common/1.1.0", "hashPath": "microsoft.aspnetcore.http.connections.common.1.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-2DgZ9rWrJtuR7RYiew01nGRzuQBDaGHGmK56Rk54vsLLsCdzuFUPqbDTJCS1qJQWTbmbIQ9wGIOjpxA1t0l7/w==", "path": "microsoft.aspnetcore.http.extensions/2.2.0", "hashPath": "microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "path": "microsoft.aspnetcore.http.features/2.2.0", "hashPath": "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.HttpOverrides/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-pOlLQyNKQduGbtbgB55RyTHFeshSfKi3DmofrVjk+UBQjyp+Tm0RNNJFQf+sv34hlFsel+VnD79QyO9Zk/c3oA==", "path": "microsoft.aspnetcore.httpoverrides/2.2.0", "hashPath": "microsoft.aspnetcore.httpoverrides.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.HttpsPolicy/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-0EmmwzAkWEPCC8rpg9nGfcOiitIOYkZ13f+b5ED7AAZvz/ZwkdWbeMarGf77lSyA+Mb9O/iAt4LWup0RRMVOJw==", "path": "microsoft.aspnetcore.httpspolicy/2.2.0", "hashPath": "microsoft.aspnetcore.httpspolicy.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Identity/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-F16BKeS96wKhyIyhaFR7m8kRIwIvPUW9Dx7IlGWmu2IIwnUDCdo+2z7IrWKA8r77pZQ1UE9kYcBPg5456YdAIA==", "path": "microsoft.aspnetcore.identity/2.2.0", "hashPath": "microsoft.aspnetcore.identity.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGJ8f8sE9vbnyPJpSCMYAjh1itkM8uL9QnkO5lQSSJGeyG4b1+zNoLS+leJgjGnlkTzgWPffc4OuqH7wsYahWw==", "path": "microsoft.aspnetcore.identity.entityframeworkcore/2.2.0", "hashPath": "microsoft.aspnetcore.identity.entityframeworkcore.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Identity.UI/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-T4B/Uaqd4u7jN6XDHbEBTZO002HquQKU49V+PvWEGKoiJBgZ96JskDr/NsfgVin8n8/bRSx+4A1WwlkMDKcNBg==", "path": "microsoft.aspnetcore.identity.ui/2.2.0", "hashPath": "microsoft.aspnetcore.identity.ui.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.JsonPatch/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-o9BB9hftnCsyJalz9IT0DUFxz8Xvgh3TOfGWolpuf19duxB4FySq7c25XDYBmBMS+sun5/PsEUAi58ra4iJAoA==", "path": "microsoft.aspnetcore.jsonpatch/2.2.0", "hashPath": "microsoft.aspnetcore.jsonpatch.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Localization/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-+PGX1mEfq19EVvskBBb9XBQrXZpZrh6hYhX0x3FkPTEqr+rDM2ZmsEwAAMRmzcidmlDM1/7cyDSU/WhkecU8tA==", "path": "microsoft.aspnetcore.localization/2.2.0", "hashPath": "microsoft.aspnetcore.localization.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Localization.Routing/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-kjheDUpXWaGOH8bUQafFAkUvw74xoe0Y2hojgeYaAg5LKvaFUwupkz8wgyhfSbLdejxEQJ6PsA7Zq/AcdPoIUQ==", "path": "microsoft.aspnetcore.localization.routing/2.2.0", "hashPath": "microsoft.aspnetcore.localization.routing.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.MiddlewareAnalysis/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-GISp0KoVyJ4llqkmUOWFbOb7g/rOABlsf0Nt8a4eanY71XfUCM0dqBaMct3IUE3KWUvjhKPACQimxgMjPcF7pA==", "path": "microsoft.aspnetcore.middlewareanalysis/2.2.0", "hashPath": "microsoft.aspnetcore.middlewareanalysis.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-noun9xcrEvOs/ubczt2OluY9/bOOM2erv1D/gyyYtfS2sfyx2uGknUIAWoqmqc401TvQDysyx8S4M9j5zPIVBw==", "path": "microsoft.aspnetcore.mvc/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ET6uZpfVbGR1NjCuLaLy197cQ3qZUjzl7EG5SL4GfJH/c9KRE89MMBrQegqWsh0w1iRUB/zQaK0anAjxa/pz4g==", "path": "microsoft.aspnetcore.mvc.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Analyzers/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wxxt1rFVHITp4MDaGQP/wyl+ROVVVeQCTWI6C8hxI8X66C4u6gcxvelqgnmsn+dISMCdE/7FQOwgiMx1HxuZqA==", "path": "microsoft.aspnetcore.mvc.analyzers/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.analyzers.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.ApiExplorer/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-iSREQct43Xg2t3KiQ2648e064al/HSLPXpI5yO9VPeTGDspWKHW23XFHRKPN1YjIQHHfBj8ytXbiF0XcSxp5pg==", "path": "microsoft.aspnetcore.mvc.apiexplorer/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.apiexplorer.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Core/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ALiY4a6BYsghw8PT5+VU593Kqp911U3w9f/dH9/ZoI3ezDsDAGiObqPu/HP1oXK80Ceu0XdQ3F0bx5AXBeuN/Q==", "path": "microsoft.aspnetcore.mvc.core/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.core.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Cors/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-oINjMqhU7yzT2T9AMuvktlWlMd40i0do8E1aYslJS+c5fof+EMhjnwTh6cHN1dfrgjkoXJ/gutxn5Qaqf/81Kg==", "path": "microsoft.aspnetcore.mvc.cors/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.cors.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.DataAnnotations/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-WOw4SA3oT47aiU7ZjN/88j+b79YU6VftmHmxK29Km3PTI7WZdmw675QTcgWfsjEX4joCB82v7TvarO3D0oqOyw==", "path": "microsoft.aspnetcore.mvc.dataannotations/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.dataannotations.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Formatters.Json/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ScWwXrkAvw6PekWUFkIr5qa9NKn4uZGRvxtt3DvtUrBYW5Iu2y4SS/vx79JN0XDHNYgAJ81nVs+4M7UE1Y/O+g==", "path": "microsoft.aspnetcore.mvc.formatters.json/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.formatters.json.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Formatters.Xml/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-4pUhKtqhaNqSeMRRyEw1kGjg/pNLczzd4VAsanMGI539sCdkl1JBaoFojZb1helVdUvX9a1Jo+lYXq0lnwB/GQ==", "path": "microsoft.aspnetcore.mvc.formatters.xml/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.formatters.xml.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Localization/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-H1L4pP124mrN6duwOtNVIJUqy4CczC2/ah4MXarRt9ZRpJd2zNp1j3tJCgyEQpqai6zNVP6Vp2ZRMQcNDcNAKA==", "path": "microsoft.aspnetcore.mvc.localization/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.localization.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Razor/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-TXvEOjp3r6qDEjmDtv3pXjQr/Zia9PpoGkl1MyTEqKqrUehBTpAdCjA8APXFwun19lH20OuyU+e4zDYv9g134w==", "path": "microsoft.aspnetcore.mvc.razor/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.razor.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Razor.Extensions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Sei/0moqBDQKaAYT9PtOeRtvYgHQQLyw/jm3exHw2w9VdzejiMEqCQrN2d63Dk4y7IY0Irr/P9JUFkoVURRcNw==", "path": "microsoft.aspnetcore.mvc.razor.extensions/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.razor.extensions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Razor.ViewCompilation/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-dys8ggIDth3g5GBpCfeayU9sNg6Z9IbKFKOuaXbVaAiZQUd+Egk9op4NLHpqfR9Ey2HGw+u87LYC55bhEeOpag==", "path": "microsoft.aspnetcore.mvc.razor.viewcompilation/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.razor.viewcompilation.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.RazorPages/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-GsMs4QKCf5VgdGZq9/nfAVkMJ/8uE4ie0Iugv4FtxbHBmMdpPQQBfTFKoUpwMbgIRw7hzV8xy2HPPU5o58PsdQ==", "path": "microsoft.aspnetcore.mvc.razorpages/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.razorpages.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.TagHelpers/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-hsrm/dLx7ztfWV+WEE7O8YqEePW7TmUwFwR7JsOUSTKaV9uSeghdmoOsYuk0HeoTiMhRxH8InQVE9/BgBj+jog==", "path": "microsoft.aspnetcore.mvc.taghelpers/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.taghelpers.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.ViewFeatures/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-dt7MGkzCFVTAD5oesI8UeVVeiSgaZ0tPdFstQjG6YLJSCiq1koOUSHMpf0PASGdOW/H9hxXkolIBhT5dWqJi7g==", "path": "microsoft.aspnetcore.mvc.viewfeatures/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.viewfeatures.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.NodeServices/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ML+s+nv/ri3MxM4vXjTK3S4K925TGklSKH74VOkCqWQF9ki5yuYcyxaWTUsCyAXliw+N8HMNmW++uU81JngDDg==", "path": "microsoft.aspnetcore.nodeservices/2.2.0", "hashPath": "microsoft.aspnetcore.nodeservices.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Owin/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-h9QIdnrH7fOTQgUwjz/v0fDk8j8JNtUB233gYFtngt7jLoVc7vfMEGs9rnOWh8ubz+JdrMt7UBrva07af4Smxw==", "path": "microsoft.aspnetcore.owin/2.2.0", "hashPath": "microsoft.aspnetcore.owin.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Razor/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-V54PIyDCFl8COnTp9gezNHpUNHk7F9UnerGeZy3UfbnwYvfzbo+ipqQmSgeoESH8e0JvKhRTyQyZquW2EPtCmg==", "path": "microsoft.aspnetcore.razor/2.2.0", "hashPath": "microsoft.aspnetcore.razor.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Design/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-VLWK+ZtMMNukY6XjxYHc7mz33vkquoEzQJHm/LCF5REVxIaexLr+UTImljRRJBdUDJluDAQwU+59IX0rFDfURA==", "path": "microsoft.aspnetcore.razor.design/2.2.0", "hashPath": "microsoft.aspnetcore.razor.design.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Language/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-IeyzVFXZdpUAnWKWoNYE0SsP1Eu7JLjZaC94jaI1VfGtK57QykROz/iGMc8D0VcqC8i02qYTPQN/wPKm6PfidA==", "path": "microsoft.aspnetcore.razor.language/2.2.0", "hashPath": "microsoft.aspnetcore.razor.language.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Runtime/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-7YqK+H61lN6yj9RiQUko7oaOhKtRR9Q/kBcoWNRemhJdTIWOh1OmdvJKzZrMWOlff3BAjejkPQm+0V0qXk+B1w==", "path": "microsoft.aspnetcore.razor.runtime/2.2.0", "hashPath": "microsoft.aspnetcore.razor.runtime.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.ResponseCaching/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-MEBP1UwGD7X1vhO43LN5KhZDt4HMTX7u1YA0nq7HR6IDRhWczHczJPDu3GbL01IMdb03hyT/glJIv8PI5zKtnA==", "path": "microsoft.aspnetcore.responsecaching/2.2.0", "hashPath": "microsoft.aspnetcore.responsecaching.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-CIHWEKrHzZfFp7t57UXsueiSA/raku56TgRYauV/W1+KAQq6vevz60zjEKaazt3BI76zwMz3B4jGWnCwd8kwQw==", "path": "microsoft.aspnetcore.responsecaching.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.responsecaching.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.ResponseCompression/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-RvSstOhebIMDdRLd4iWjA6z2o2kGGwEYGPajvTXwndOA3TZpWH3FOIV4L7mehN/HoKrbTbX5vZ54ZFDwWoAFKA==", "path": "microsoft.aspnetcore.responsecompression/2.2.0", "hashPath": "microsoft.aspnetcore.responsecompression.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Rewrite/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-jztwQxyn4CcWZj/1mQtFiZ5+pIWYltHIXk5ykyrXMjO6qaKVvc+mlffSUCQ0AOl3vH7vxsZnda8poHwVaT0QIA==", "path": "microsoft.aspnetcore.rewrite/2.2.0", "hashPath": "microsoft.aspnetcore.rewrite.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Routing/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-jAhDBy0wryOnMhhZTtT9z63gJbvCzFuLm8yC6pHzuVu9ZD1dzg0ltxIwT4cfwuNkIL/TixdKsm3vpVOpG8euWQ==", "path": "microsoft.aspnetcore.routing/2.2.0", "hashPath": "microsoft.aspnetcore.routing.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Routing.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-lRRaPN7jDlUCVCp9i0W+PB0trFaKB0bgMJD7hEJS9Uo4R9MXaMC8X2tJhPLmeVE3SGDdYI4QNKdVmhNvMJGgPQ==", "path": "microsoft.aspnetcore.routing.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.routing.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Server.HttpSys/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-tei37PK4c6CKd7tGgAOkpbePwu8WLjqsEfiAfLbaMXnmp7o30bzcIxtraTrjvq2SpRAFA9p6WwUbmyqQxXPcfQ==", "path": "microsoft.aspnetcore.server.httpsys/2.2.0", "hashPath": "microsoft.aspnetcore.server.httpsys.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Server.IIS/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-6NEwFAJFrnZ0f5eJB1ReIpgPM1ZRDj3IE3Rda01nD3vJANCyJFjZ4SGW3Ckn1AmMi225fGflWzpCKLb7/l43jw==", "path": "microsoft.aspnetcore.server.iis/2.2.0", "hashPath": "microsoft.aspnetcore.server.iis.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Server.IISIntegration/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-iVjgAg+doTTrTFCOq6kZRpebXq94YGCx9efMIwO5QhwdY/sHAjfrVz2lXzji63G96YjJVK3ZRrlpgS2fd49ABw==", "path": "microsoft.aspnetcore.server.iisintegration/2.2.0", "hashPath": "microsoft.aspnetcore.server.iisintegration.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Server.Kestrel/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-D0vGB8Tp0UNMiAhT+pwAVeqDDx2OFrfpu/plwm0WhA+1DZvTLc99eDwGISL6LAY8x7a12lhl9w7/m+VdoyDu8Q==", "path": "microsoft.aspnetcore.server.kestrel/2.2.0", "hashPath": "microsoft.aspnetcore.server.kestrel.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Server.Kestrel.Core/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-F6/Vesd3ODq/ISbHfcvfRf7IzRtTvrNX8VA36Knm5e7bteJhoRA2GKQUVQ+neoO1njLvaQKnjcA3rdCZ6AF6cg==", "path": "microsoft.aspnetcore.server.kestrel.core/2.2.0", "hashPath": "microsoft.aspnetcore.server.kestrel.core.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Server.Kestrel.Https/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-nEH5mU6idUYS3/+9BKw2stMOM25ZdGwIH4P4kyj6PVkMPgQUTkBQ7l/ScPkepdhejcOlPa+g3+M4dYsSYPUJ8g==", "path": "microsoft.aspnetcore.server.kestrel.https/2.2.0", "hashPath": "microsoft.aspnetcore.server.kestrel.https.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-j1ai2CG8BGp4mYf2TWSFjjy1pRgW9XbqhdR4EOVvrlFVbcpEPfXNIPEdjkcgK+txWCupGzkFnFF8oZsASMtmyw==", "path": "microsoft.aspnetcore.server.kestrel.transport.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.server.kestrel.transport.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-qTACI0wePgAKCH+YKrMgChyfqJpjwgGZEtSuwBw6TjWLQ66THGasleia/7EZz2t2eAjwWxw8RA/D8ODrBqpj9A==", "path": "microsoft.aspnetcore.server.kestrel.transport.sockets/2.2.0", "hashPath": "microsoft.aspnetcore.server.kestrel.transport.sockets.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Session/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-lOjJVh293AKaOEPi1MIC1/G9gOVZMrve2a05o56oslK6bo0PMgMB17rmPomvqrJAjMdlWZ/MGdN2y78Z9wzWTw==", "path": "microsoft.aspnetcore.session/2.2.0", "hashPath": "microsoft.aspnetcore.session.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-V5X5XkeAHaFyyBOGPrddVeqTNo6zRPJNS5PRhlzEyBXiNG9AtqUbMyWFdZahQyMiIWJau550z59A4kdC9g5I9A==", "path": "microsoft.aspnetcore.signalr/1.1.0", "hashPath": "microsoft.aspnetcore.signalr.1.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Common/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-TyLgQ4y4RVUIxiYFnHT181/rJ33/tL/NcBWC9BwLpulDt5/yGCG4EvsToZ49EBQ7256zj+R6OGw6JF+jj6MdPQ==", "path": "microsoft.aspnetcore.signalr.common/1.1.0", "hashPath": "microsoft.aspnetcore.signalr.common.1.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Core/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-mk69z50oFk2e89d3F/AfKeAvP3kvGG7MHG4ErydZiUd3ncSRq0kl0czq/COn/QVKYua9yGr2LIDwuR1C6/pu8Q==", "path": "microsoft.aspnetcore.signalr.core/1.1.0", "hashPath": "microsoft.aspnetcore.signalr.core.1.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Protocols.Json/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-BOsjatDJnvnnXCMajOlC0ISmiFnJi/EyJzMo0i//5fZJVCLrQ4fyV/HzrhhAhSJuwJOQDdDozKQ9MB9jHq84pg==", "path": "microsoft.aspnetcore.signalr.protocols.json/1.1.0", "hashPath": "microsoft.aspnetcore.signalr.protocols.json.1.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.SpaServices/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-hUAsOd45CQbUV47b/c5wp6uKM0Fa6MXekFHbRb+jEPjzmrxLPn9nAKK1dYmyMAqSBRL8c6zVCWQk+TOP7eGs/A==", "path": "microsoft.aspnetcore.spaservices/2.2.0", "hashPath": "microsoft.aspnetcore.spaservices.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.SpaServices.Extensions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-RvzzubzGPD+dGCfKVVtAvyIsnWpAWOA/x1n6fGLwICPER7Ze6budQGFPdZ7yuXTwtTMRvHa4O4AaGLG1XmoXGw==", "path": "microsoft.aspnetcore.spaservices.extensions/2.2.0", "hashPath": "microsoft.aspnetcore.spaservices.extensions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.StaticFiles/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-byZDrjir6Co5EoWbraQyG0qbPCUG6XgGYQstipMF9lucOAjq/mqnIyt8B8iMWnin/ghZoOln9Y01af4rUAwOhA==", "path": "microsoft.aspnetcore.staticfiles/2.2.0", "hashPath": "microsoft.aspnetcore.staticfiles.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.WebSockets/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZpOcg2V0rCwU9ErfDb9y3Hcjoe7rU42XlmUS0mO4pVZQSgJVqR+DfyZtYd5LDa11F7bFNS2eezI9cBM3CmfGhw==", "path": "microsoft.aspnetcore.websockets/2.2.0", "hashPath": "microsoft.aspnetcore.websockets.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ErxAAKaDzxXASB/b5uLEkLgUWv1QbeVxyJYEHQwMaxXOeFFVkQxiq8RyfVcifLU7NR0QY0p3acqx4ZpYfhHDg==", "path": "microsoft.aspnetcore.webutilities/2.2.0", "hashPath": "microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-HS3iRWZKcUw/8eZ/08GXKY2Bn7xNzQPzf8gRPHGSowX7u7XXu9i9YEaBeBNKUXWfI7qjvT2zXtLUvbN0hds8vg==", "path": "microsoft.codeanalysis.analyzers/1.1.0", "hashPath": "microsoft.codeanalysis.analyzers.1.1.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/2.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-06AzG7oOLKTCN1EnoVYL1bQz+Zwa10LMpUn7Kc+PdpN8CQXRqXTyhfxuKIz6t0qWfoatBNXdHD0OLcEYp5pOvQ==", "path": "microsoft.codeanalysis.common/2.8.0", "hashPath": "microsoft.codeanalysis.common.2.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/2.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-RizcFXuHgGmeuZhxxE1qQdhFA9lGOHlk0MJlCUt6LOnYsevo72gNikPcbANFHY02YK8L/buNrihchY0TroGvXQ==", "path": "microsoft.codeanalysis.csharp/2.8.0", "hashPath": "microsoft.codeanalysis.csharp.2.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Razor/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-2qL0Qyu5qHzg6/JzF80mLgsqn9NP/Q0mQwjH+Z+DiqcuODJx8segjN4un2Tnz6bEAWv8FCRFNXR/s5wzlxqA8A==", "path": "microsoft.codeanalysis.razor/2.2.0", "hashPath": "microsoft.codeanalysis.razor.2.2.0.nupkg.sha512"}, "Microsoft.CSharp/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-kaj6Wb4qoMuH3HySFJhxwQfe8R/sJsNJnANrvv8WdFPMoNbKY5htfNscv+LHCu5ipz+49m2e+WQXpLXr9XYemQ==", "path": "microsoft.csharp/4.5.0", "hashPath": "microsoft.csharp.4.5.0.nupkg.sha512"}, "Microsoft.DotNet.PlatformAbstractions/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-9KPDwvb/hLEVXYruVHVZ8BkebC8j17DmPb56LnqRF74HqSPLjCkrlFUjOtFpQPA2DeADBRTI/e69aCfRBfrhxw==", "path": "microsoft.dotnet.platformabstractions/2.1.0", "hashPath": "microsoft.dotnet.platformabstractions.2.1.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-xfDHe+J94oz2d+ESDU8u+96iSfsiPwpgYGPRSp/bato0Ekjz5kYs61u9mS0GN5t8n/wxc5P3uEJm1x7TfROxhQ==", "path": "microsoft.entityframeworkcore/2.2.0", "hashPath": "microsoft.entityframeworkcore.2.2.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-/js/H09B8MQGoPDFzJoaAealyL66g4vKf7DVcdBYcxgKztkttjZbRzSWKF9PZZFyfBl9Ia/BiStM70t7kjgMpg==", "path": "microsoft.entityframeworkcore.abstractions/2.2.0", "hashPath": "microsoft.entityframeworkcore.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-/1rw3toRCal80Ch51lIjuf/0WfW9ZGB4eRY1y6GOcOoOvnMXXXt+4xMRw/0k0kIwHsWUNXUpw73jf40/Pe+ZYA==", "path": "microsoft.entityframeworkcore.analyzers/2.2.0", "hashPath": "microsoft.entityframeworkcore.analyzers.2.2.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-mcsUEzg1bWvPgj/isz7aabDy41x+x8WBTmSF+JFsDGe3K5ZElWT2FSr3LLmkXk/5BLWJ3f9SDe0YR55u3ZgHrw==", "path": "microsoft.entityframeworkcore.design/2.2.0", "hashPath": "microsoft.entityframeworkcore.design.2.2.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.InMemory/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-WxvRXUwCGdY1Ze8GfZteWadsxrxpFRfZN8WJ1jcXZKp5eYo5WwmiBq5e3xIZR8cHxznqlqczJ3NpXjqyYpNK9w==", "path": "microsoft.entityframeworkcore.inmemory/2.2.0", "hashPath": "microsoft.entityframeworkcore.inmemory.2.2.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-CRJHHp/GqmXByeeODgbDgMDWId9kKT9TDRzHF8voWHFNTajBQl0U4jaMrHxjIP6hJ2olF8n+5GYmQv+v3bUVKQ==", "path": "microsoft.entityframeworkcore.relational/2.2.0", "hashPath": "microsoft.entityframeworkcore.relational.2.2.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.SqlServer/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-rCGBF6Hab9633Dh2xtiAcJnDxf2CjaBrGyoagoCoYHH5Ftbdw5zX/e9ABgif5ngzh7DsrBcgxK/3gHBZ2n+TGA==", "path": "microsoft.entityframeworkcore.sqlserver/2.2.0", "hashPath": "microsoft.entityframeworkcore.sqlserver.2.2.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-F253CmzpL7eXFKpK++/GIVbyVMZyXYq388osdkggsA1eL7c8ZGwHho0jE3LGA+L6WuXm6KbwQMtnt15zZAqzzA==", "path": "microsoft.entityframeworkcore.tools/2.2.0", "hashPath": "microsoft.entityframeworkcore.tools.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-spsJkYo8gGJapaxTSQFN/wqA+ghpJMLwB4ZyTB+fSdpd7AmMFP/YSpIcGmczcw4KggpxLGhLk7lCkSIlgvHaqQ==", "path": "microsoft.extensions.caching.abstractions/2.2.0", "hashPath": "microsoft.extensions.caching.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-yFs44RzB2Pzfoj4uk+mEz3MTTQKyeWb8gDhv5GyVPfHnLv0eQhGwzbw/5WpxAcVyOgG/H3/0ULY6g0/7/B+r7w==", "path": "microsoft.extensions.caching.memory/2.2.0", "hashPath": "microsoft.extensions.caching.memory.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.SqlServer/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-hDAunudTCNyVb22W+ctToi9T3mcrix2L+GfnuhbIcbzgXVyUGMULUJmb2D5ElIJKkcGxkC/lM1aBMgHsSFFZcA==", "path": "microsoft.extensions.caching.sqlserver/2.2.0", "hashPath": "microsoft.extensions.caching.sqlserver.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-nOP8R1mVb/6mZtm2qgAJXn/LFm/2kMjHDAg/QJLFG6CuWYJtaD3p1BwQhufBVvRzL9ceJ/xF0SQ0qsI2GkDQAA==", "path": "microsoft.extensions.configuration/2.2.0", "hashPath": "microsoft.extensions.configuration.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-65MrmXCziWaQFrI0UHkQbesrX5wTwf9XPjY5yFm/VkgJKFJ5gqvXRoXjIZcf2wLi5ZlwGz/oMYfyURVCWbM5iw==", "path": "microsoft.extensions.configuration.abstractions/2.2.0", "hashPath": "microsoft.extensions.configuration.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-vJ9xvOZCnUAIHcGC3SU35r3HKmHTVIeHzo6u/qzlHAqD8m6xv92MLin4oJntTvkpKxVX3vI1GFFkIQtU3AdlsQ==", "path": "microsoft.extensions.configuration.binder/2.2.0", "hashPath": "microsoft.extensions.configuration.binder.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-4kJIGOSRqD1Ccqerst4t/zsNs51plR7BIxbdKO1J/9rL+2DuNT+ieAuEv+HROelqTam3yOpKFR7TtHBt3oLpOA==", "path": "microsoft.extensions.configuration.commandline/2.2.0", "hashPath": "microsoft.extensions.configuration.commandline.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gIqt9PkKO01hZ0zmHnWrZ1E45MDreZTVoyDbL1kMWKtDgxxWTJpYtESTEcgpvR1uB1iex1zKGYzJpOMgmuP5TQ==", "path": "microsoft.extensions.configuration.environmentvariables/2.2.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-H1qCpWBC8Ed4tguTR/qYkbb3F6DI5Su3t8xyFo3/5MzAd8PwPpHzgX8X04KbBxKmk173Pb64x7xMHarczVFQUA==", "path": "microsoft.extensions.configuration.fileextensions/2.2.0", "hashPath": "microsoft.extensions.configuration.fileextensions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Ini/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-uEDasBxY7m0GJseqHD8QhfiznxDMhxN9YE3j01Es6eks42rRm3yL8ZMbRxuEjyKqGZqjjt+Vr297/nKcg0eOow==", "path": "microsoft.extensions.configuration.ini/2.2.0", "hashPath": "microsoft.extensions.configuration.ini.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-jUDdmLyFmLf9V3mqnMzSAzAv4QigJ67tZh5Q7HBXeBnESL2UyeesNG6jSBti+b63JpxZf+EDyn+anx3gyrNxug==", "path": "microsoft.extensions.configuration.json/2.2.0", "hashPath": "microsoft.extensions.configuration.json.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.KeyPerFile/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-qK7vVxtUrpxdQPhvjF3RVYkcV86q/QfMBWqvvXAKYYkQ+H/4GXxk5cbPaSWdMZB5YU1GBEFBuZg9MZxDRvPJkg==", "path": "microsoft.extensions.configuration.keyperfile/2.2.0", "hashPath": "microsoft.extensions.configuration.keyperfile.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-2/N2xo6/sNbVshnKktmq5lwaQbsAR2SrzCVrJEeMP8OKZVI7SzT8P6/WXZF8/YC7dTYsMe3nrHzgl1cF9i5ZKQ==", "path": "microsoft.extensions.configuration.usersecrets/2.2.0", "hashPath": "microsoft.extensions.configuration.usersecrets.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Xml/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-toCFesKf2KZgRtb6T7tulnJv3IBVL+Gqd4KE3ebQZ20wA2Z5Rp6A44MsRGZ1ollmihzkxxBDavVfgufFeji3Sw==", "path": "microsoft.extensions.configuration.xml/2.2.0", "hashPath": "microsoft.extensions.configuration.xml.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-MZtBIwfDFork5vfjpJdG5g8wuJFt7d/y3LOSVVtDK/76wlbtz6cjltfKHqLx2TKVqTj5/c41t77m1+h20zqtPA==", "path": "microsoft.extensions.dependencyinjection/2.2.0", "hashPath": "microsoft.extensions.dependencyinjection.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-f9hstgjVmr6rmrfGSpfsVOl2irKAgr1QjrSi3FgnS7kulxband50f2brRLwySAQTADPZeTdow0mpSMcoAdadCw==", "path": "microsoft.extensions.dependencyinjection.abstractions/2.2.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-nS2XKqi+1A1umnYNLX2Fbm/XnzCxs5i+zXVJ3VC6r9t2z0NZr9FLnJN4VQpKigdcWH/iFTbMuX6M6WQJcTjVIg==", "path": "microsoft.extensions.dependencymodel/2.1.0", "hashPath": "microsoft.extensions.dependencymodel.2.1.0.nupkg.sha512"}, "Microsoft.Extensions.DiagnosticAdapter/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Av0QGyboU9hYcprPduZg8Ny4mtp1Z0xOfZGCiBhYMh6a0loNomZ74U1P9EJUBksT2ZJd0+hh/pOQIVdAJ8+AbA==", "path": "microsoft.extensions.diagnosticadapter/2.2.0", "hashPath": "microsoft.extensions.diagnosticadapter.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.HealthChecks/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-p9njfetdebuplBCkIJPqyxsUIOBf/7B/RhPXZnFjh+/wqWNRqhP/1s18q1me9XP0l8uCD8TqJRPC+L0MCoUGRA==", "path": "microsoft.extensions.diagnostics.healthchecks/2.2.0", "hashPath": "microsoft.extensions.diagnostics.healthchecks.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-cO6f4csTakJXuLWnU/p5mfQInyNq5sSi4mS2YtQZcGoHynU6P/TD6gjqt1TRnVfwuZLw3tmmw2ipFrHbBUqWew==", "path": "microsoft.extensions.diagnostics.healthchecks.abstractions/2.2.0", "hashPath": "microsoft.extensions.diagnostics.healthchecks.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-EcnaSsPTqx2MGnHrmWOD0ugbuuqVT8iICqSqPzi45V5/MA1LjUNb0kwgcxBGqizV1R+WeBK7/Gw25Jzkyk9bIw==", "path": "microsoft.extensions.fileproviders.abstractions/2.2.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Composite/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Az/RxWB+UlyVN/TvQFaGXx8XAXVZN5WQnnuJOsjwBzghSJc1i8zqNjIypPHOedcuIXs2XSWgOSL6YQ3BlCnoJA==", "path": "microsoft.extensions.fileproviders.composite/2.2.0", "hashPath": "microsoft.extensions.fileproviders.composite.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Embedded/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-6e22jnVntG9JLLowjY40UBPLXkKTRlDpFHmo2evN8lwZIpO89ZRGz6JRdqhnVYCaavq5KeFU2W5VKPA5y5farA==", "path": "microsoft.extensions.fileproviders.embedded/2.2.0", "hashPath": "microsoft.extensions.fileproviders.embedded.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-tbDHZnBJkjYd9NjlRZ9ondDiv1Te3KYCTW2RWpR1B0e1Z8+EnFRo7qNnHkkSCixLdlPZzhjlX24d/PixQ7w2dA==", "path": "microsoft.extensions.fileproviders.physical/2.2.0", "hashPath": "microsoft.extensions.fileproviders.physical.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZSsHZp3PyW6vk37tDEdypjgGlNtpJ0EixBMOfUod2Thx7GtwfFSAQXUQx8a8BN8vfWKGGMbp7jPWdoHx/At4wQ==", "path": "microsoft.extensions.filesystemglobbing/2.2.0", "hashPath": "microsoft.extensions.filesystemglobbing.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-PxZPd5QbWr8+3JN2segEaD7IAYI+mR8ZmMqgo6GOk+E+UKnRcbC3RSQgJrZYuWVQwJCvdxesO5e64LSHC1zC8g==", "path": "microsoft.extensions.hosting/2.2.0", "hashPath": "microsoft.extensions.hosting.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-+k4AEn68HOJat5gj1TWa6X28WlirNQO9sPIIeQbia+91n03esEtMSSoekSTpMjUzjqtJWQN3McVx0GvSPFHF/Q==", "path": "microsoft.extensions.hosting.abstractions/2.2.0", "hashPath": "microsoft.extensions.hosting.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Http/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-hZ8mz6FgxSeFtkHzw+Ad0QOt2yjjpq4WaG9itnkyChtXYTrDlbkw3af2WJ9wdEAAyYqOlQaVDB6MJSEo8dd/vw==", "path": "microsoft.extensions.http/2.2.0", "hashPath": "microsoft.extensions.http.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Identity.Core/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-/C+Valwg8IeUwDIunusittHivA9iyf82Jr1yeUFWO2zH2mDMMeYgjRyDLZqfL/7Vq94PEQsgv1XAaDfAX8msMw==", "path": "microsoft.extensions.identity.core/2.2.0", "hashPath": "microsoft.extensions.identity.core.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Identity.Stores/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-WY6os4m5LcG7XXHQK1vQggjdNFs7h6CsidVLOzPjG7Cb1zwRYKzfRT/pSUD40JNGvVp4oNENjLPvu/30ufIGNw==", "path": "microsoft.extensions.identity.stores/2.2.0", "hashPath": "microsoft.extensions.identity.stores.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Localization/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-3nBQLeBrcd4Rgd9vQi4gF5NgAWxnQrHekjjwlgww4wyLNfJDizjiex2resOLoAuAgy3y2IIAWjOpbr0UKR2ykw==", "path": "microsoft.extensions.localization/2.2.0", "hashPath": "microsoft.extensions.localization.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Localization.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-FQzXG/lYR9UOM2zHpqsjTRpp3EghIYo3FCsQpfmtbp+glPaU0WXZfNmMjyqBRmMj1Sq93fPnC+G9zzYRauuRQA==", "path": "microsoft.extensions.localization.abstractions/2.2.0", "hashPath": "microsoft.extensions.localization.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxqhadc9FCmFHzU+fz3oc8sFlE6IadViYg8dfUdGzJZ2JUxnCsRghBhhOWdM4B2zSZqEc+0BjliBh/oNdRZuig==", "path": "microsoft.extensions.logging/2.2.0", "hashPath": "microsoft.extensions.logging.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-B2WqEox8o+4KUOpL7rZPyh6qYjik8tHi2tN8Z9jZkHzED8ElYgZa/h6K+xliB435SqUcWT290Fr2aa8BtZjn8A==", "path": "microsoft.extensions.logging.abstractions/2.2.0", "hashPath": "microsoft.extensions.logging.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ukU1mQGX9+xBsEzpNd13yl4deFVYI+fxxnmKpOhvNZsF+/trCrAUQh+9QM5pPGHbfYkz3lLQ4BXfKCP0502dLw==", "path": "microsoft.extensions.logging.configuration/2.2.0", "hashPath": "microsoft.extensions.logging.configuration.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-1eGgcOJ++PMxW6sn++j6U7wsWvhEBm/5ScqBUUBGLRE8M7AHahi9tsxivDMqEXVM3F0/pshHl3kEpMXtw4BeFg==", "path": "microsoft.extensions.logging.console/2.2.0", "hashPath": "microsoft.extensions.logging.console.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-JjqWtshxUujSnxslFccCRAaH8uFOciqXkYdRw+h5MwpC4sUc+ju9yZzvVi6PA5vW09ckv26EkasEvXrofGiaJg==", "path": "microsoft.extensions.logging.debug/2.2.0", "hashPath": "microsoft.extensions.logging.debug.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-oOa5H+vdNgpsxE6vgtX4U/godKtX2edVi+QjlWb2PBQfavGIQ3WxtjxN+B0DQAjwBNdV4mW8cgOiDEZ8KdR7Ig==", "path": "microsoft.extensions.logging.eventsource/2.2.0", "hashPath": "microsoft.extensions.logging.eventsource.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.TraceSource/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-2laIg/Mf1OhhduUKVN3//j+sYceyUocgGC/ySx6cnZFeNf2mezs32TmRZyzfkQAZQ6azlo/0wTxi8BgIVUyRYA==", "path": "microsoft.extensions.logging.tracesource/2.2.0", "hashPath": "microsoft.extensions.logging.tracesource.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gA8H7uQOnM5gb+L0uTNjViHYr+hRDqCdfugheGo/MxQnuHzmhhzCBTIPm19qL1z1Xe0NEMabfcOBGv9QghlZ8g==", "path": "microsoft.extensions.objectpool/2.2.0", "hashPath": "microsoft.extensions.objectpool.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Options/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-UpZLNLBpIZ0GTebShui7xXYh6DmBHjWM8NxGxZbdQh/bPZ5e6YswqI+bru6BnEL5eWiOdodsXtEz3FROcgi/qg==", "path": "microsoft.extensions.options/2.2.0", "hashPath": "microsoft.extensions.options.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-d4WS6yVXaw43ffiUnHj8oG1t2B6RbDDiQcgdA+Eq//NlPa3Wd+GTJFKj4OM4eDF3GjVumGr/CEVRS/jcYoF5LA==", "path": "microsoft.extensions.options.configurationextensions/2.2.0", "hashPath": "microsoft.extensions.options.configurationextensions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Options.DataAnnotations/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Xk7okx/u+ZQb8xvz71FwVmfZjwDh2DWrovhtQXprWE16KqaP8bs6A8wb0h9nTSFh9rcFDVeo42d47iduu01XvQ==", "path": "microsoft.extensions.options.dataannotations/2.2.0", "hashPath": "microsoft.extensions.options.dataannotations.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-azyQtqbm4fSaDzZHD/J+V6oWMFaf2tWP4WEGIYePLCMw3+b2RQdj9ybgbQyjCshcitQKQ4lEDOZjmSlTTrHxUg==", "path": "microsoft.extensions.primitives/2.2.0", "hashPath": "microsoft.extensions.primitives.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.WebEncoders/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8XcqYcpcdBAxUhLeyYcuKmxu4CtNQA9IphTnARpQGhkop4A93v2XgM3AtaVVJo3H2cDWxWM6aeO8HxkifREqw==", "path": "microsoft.extensions.webencoders/2.2.0", "hashPath": "microsoft.extensions.webencoders.2.2.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/5.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5LW5VYvGZLvrbEGxyaE6dSQhT1B5frnpwX/c4/PWrNXeuJ6GkYmiOPf2u5Iwk1qQXPTvDedwEfnBg+i/0cFAyA==", "path": "microsoft.identitymodel.jsonwebtokens/5.3.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.5.3.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/5.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-o+bBauEMOi6ZI0MlJEC69Sw9UPwKLFmN+lD942g9UCx5pfiLFvJBKp8OPmxtGFL02ZxzXCIUyhyKn85izBDsnQ==", "path": "microsoft.identitymodel.logging/5.3.0", "hashPath": "microsoft.identitymodel.logging.5.3.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/5.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-o2Fx9cYQHKtOyVrCXB41kEmny1Zvm+fqXNTD5heB9yPY0C+qYm7fo1yCvtHaH2JPEersGW0iS2dE0s65kWkVEw==", "path": "microsoft.identitymodel.protocols/5.3.0", "hashPath": "microsoft.identitymodel.protocols.5.3.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/5.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-NihXp2JT3fRbTq6AOQhEQT8TuJzhUNg9TOeK+TxlkkvanllWFF0gfXH5hTRn9Qn68HJQXtp/mtLbCWzi+4bCSg==", "path": "microsoft.identitymodel.protocols.openidconnect/5.3.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.5.3.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.WsFederation/5.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6nGUoC+foCQ2UTsRD/Z6TLgsghuX10tunLXxuLE+LljW9H1oANqAQWrP8DNP++nfXke+qu1zVi6yBl6MMK/Dfg==", "path": "microsoft.identitymodel.protocols.wsfederation/5.3.0", "hashPath": "microsoft.identitymodel.protocols.wsfederation.5.3.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/5.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/piauST4FL0qzVI6oqLWxqhFReg12KwVGy0jRlnVOpGMeOVSKdtNVtHsN/hARc25hOOPEp9WKMce5ILzyMx/tQ==", "path": "microsoft.identitymodel.tokens/5.3.0", "hashPath": "microsoft.identitymodel.tokens.5.3.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens.Saml/5.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-XS6zgN7jKG7QDqG3fV9BRADs8HmRJ6vJDKVBPFFly9MCkS6KMFps4hBdBJ5ycPrXtPBfnISCLiGLHP54blCvWw==", "path": "microsoft.identitymodel.tokens.saml/5.3.0", "hashPath": "microsoft.identitymodel.tokens.saml.5.3.0.nupkg.sha512"}, "Microsoft.IdentityModel.Xml/5.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-i4uFRjipeRXGhyfHmJaZ3PkOQIWhwxBJABNDWNaxcwUvramMCWYRLE1P3g4sLjiw8zXehH6eZwxww8F+dB7/+g==", "path": "microsoft.identitymodel.xml/5.3.0", "hashPath": "microsoft.identitymodel.xml.5.3.0.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-iZNkjYqlo8sIOI0bQfpsSoMTmB/kyvmV2h225ihyZT33aTp48ZpF6qYnXxzSXmHt8DpBAwBTX+1s1UFLbYfZKg==", "path": "microsoft.net.http.headers/2.2.0", "hashPath": "microsoft.net.http.headers.2.2.0.nupkg.sha512"}, "Microsoft.NETCore.App/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-7z5l8Jp324S8bU8+yyWeYHXUFYvKyiI5lqS1dXgTzOx1H69Qbf6df12kCKlNX45LpMfCMd4U3M6p7Rl5Zk7SLA==", "path": "microsoft.netcore.app/2.2.0", "hashPath": "microsoft.netcore.app.2.2.0.nupkg.sha512"}, "Microsoft.NETCore.DotNetAppHost/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-DrhaKInRKKvN6Ns2VNIlC7ZffLOp9THf8cO6X4fytPRJovJUbF49/zzx4WfgX9E44FMsw9hT8hrKiIqDSHvGvA==", "path": "microsoft.netcore.dotnetapphost/2.2.0", "hashPath": "microsoft.netcore.dotnetapphost.2.2.0.nupkg.sha512"}, "Microsoft.NETCore.DotNetHostPolicy/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-FJie7IoPZFaPgNDxhZGmDBQP/Bs5vPdfca/G2Wf9gd6LIvMYkZcibtmJwB4tcf4KXkaOYfIOo4Cl9sEPMsSzkw==", "path": "microsoft.netcore.dotnethostpolicy/2.2.0", "hashPath": "microsoft.netcore.dotnethostpolicy.2.2.0.nupkg.sha512"}, "Microsoft.NETCore.DotNetHostResolver/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-spDm3AJYmebthDNhzY17YLPtvbc+Y1lCLVeiIH1uLJ/hZaM+40pBiPefFR8J1u66Ndkqi8ipR2tEbqPnYnjRhw==", "path": "microsoft.netcore.dotnethostresolver/2.2.0", "hashPath": "microsoft.netcore.dotnethostresolver.2.2.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-odP/tJj1z6GylFpNo7pMtbd/xQgTC3Ex2If63dRTL38bBNMwsBnJ+RceUIyHdRBC0oik/3NehYT+oECwBhIM3Q==", "path": "microsoft.netcore.targets/2.0.0", "hashPath": "microsoft.netcore.targets.2.0.0.nupkg.sha512"}, "NETStandard.Library/2.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-st47PosZSHrjECdjeIzZQbzivYBJFv6P2nv4cj2ypdI204DO+vZ7l5raGMiX4eXMJ53RfOIg+/s4DHVZ54Nu2A==", "path": "netstandard.library/2.0.3", "hashPath": "netstandard.library.2.0.3.nupkg.sha512"}, "Remotion.Linq/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-fK/76UmpC0FXBlGDFVPLJHQlDLYnGC+XY3eoDgCgbtrhi0vzbXDQ3n/IYHhqSKqXQfGw/u04A1drWs7rFVkRjw==", "path": "remotion.linq/2.2.0", "hashPath": "remotion.linq.2.2.0.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-HdSSp5MnJSsg08KMfZThpuLPJpPwE5hBXvHwoKWosyHHfe8Mh5WKT0ylEOf6yNzX6Ngjxe4Whkafh5q7Ymac4Q==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+yH1a49wJMy8Zt4yx5RhJrxO/DBDByAiCzNwiETI+1S4mPdCu0OY4djdciC7Vssk0l22wQaDLrXxXkp+3+7bVA==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c3YNH1GQJbfIPJeCnr4avseugSqPrxwIqzthYyZDN6EuOyNOzq+y2KSUfRcXauya1sF4foESTgwM5e1A8arAKw==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.Data.SqlClient.sni/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-AJfX7owAAkMjWQYhoml5IBfXh8UyYPjktn8pK0BFGAdKgBS7HqMz1fw5vdzfZUWfhtTPDGCjgNttt46ZyEmSjg==", "path": "runtime.native.system.data.sqlclient.sni/4.5.0", "hashPath": "runtime.native.system.data.sqlclient.sni.4.5.0.nupkg.sha512"}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "path": "runtime.native.system.io.compression/4.3.0", "hashPath": "runtime.native.system.io.compression.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-NS1U+700m4KFRHR5o4vo9DSlTmlCKu/u7dtE5sUHVIPB+xpXxYQvgBgA6wEIeCz6Yfn0Z52/72WYsToCEPJnrw==", "path": "runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-b3pthNgxxFcD+Pc0WSEoC0+md3MyhRS6aCEeenvNE3Fdw1HyJ18ZhRFVJJzIeR/O/jpxPboB805Ho0T3Ul7w8A==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KeLz4HClKf+nFS7p/6Fi/CqyLXh81FpiGzcmuS8DGi9lUqSnZ6Es23/gv2O+1XVGfrbNmviF7CckBpavkBoIFQ==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X7IdhILzr4ROXd8mI1BUCQMSHSQwelUlBjF1JyTKCjXaOGn2fB4EKBxQbCK2VjO3WaWIdlXZL3W6TiIVnrhX4g==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-nyFNiCk/r+VOiIqreLix8yN+q3Wga9+SE8BCgkf+2BwEKiNx6DyvFjCgkfV743/grxv8jHJ8gUK4XEQw7yzRYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ytoewC6wGorL7KoCAvRfsgoJPJbNq+64k2SqW6JcOAebWsFUvCCYgfzQMrnpvPiEl4OrblUlhF2ji+Q1+SVLrQ==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I8bKw2I8k58Wx7fMKQJn2R8lamboCAiHfHeV/pS65ScKWMMI0+wJkLYlEKvgW1D/XvSl/221clBoR2q9QNNM7A==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VB5cn/7OzUfzdnC8tqAIMQciVLiq2epm2NrAm1E9OjNRyG4lVhfR61SMcLizejzQP8R8Uf/0l5qOIbUEi+RdEg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "System.AppContext/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fKC+rmaLfeIzUhagxY17Q9siv/sPrjjKcfNg1Ic8IlQkZLipo8ljcaZQu4VtI4Jqbzjc2VTjzGLF6WmsRXAEgA==", "path": "system.appcontext/4.3.0", "hashPath": "system.appcontext.4.3.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/1.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-EXKiDFsChZW0RjrZ4FYHu9aW6+P4MCgEDCklsVseRfhoO0F+dXeMSsMRAlVXIo06kGJ/zv+2w1a2uc2+kxxSaQ==", "path": "system.collections.immutable/1.5.0", "hashPath": "system.collections.immutable.1.5.0.nupkg.sha512"}, "System.Collections.NonGeneric/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-prtjIEMhGUnQq6RnPEYLpFt8AtLbp9yq2zxOSrY7KJJZrw25Fi97IzBqY7iqssbM61Ek5b8f3MG/sG1N2sN5KA==", "path": "system.collections.nongeneric/4.3.0", "hashPath": "system.collections.nongeneric.4.3.0.nupkg.sha512"}, "System.Collections.Specialized/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Epx8PoVZR0iuOnJJDzp7pWvdfMMOAvpUo95pC4ScH2mJuXkKA2Y4aR3cG9qt2klHgSons1WFh4kcGW7cSXvrxg==", "path": "system.collections.specialized/4.3.0", "hashPath": "system.collections.specialized.4.3.0.nupkg.sha512"}, "System.ComponentModel.Annotations/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-UxYQ3FGUOtzJ7LfSdnYSFd7+oEv6M8NgUatatIN2HxNtDdlcvFAf+VIq4Of9cDMJEJC0aSRv/x898RYhB4Yppg==", "path": "system.componentmodel.annotations/4.5.0", "hashPath": "system.componentmodel.annotations.4.5.0.nupkg.sha512"}, "System.Console/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHDrIxiqk1h03m6khKWV2X8p/uvN79rgSqpilL6uzpmSfxfU5ng8VcPtW4qsDsQDHiTv6IPV9TmD5M/vElPNLg==", "path": "system.console/4.3.0", "hashPath": "system.console.4.3.0.nupkg.sha512"}, "System.Data.SqlClient/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-gwItUWW1BMCckicFO85c8frFaMK8SGqYn5IeA3GSX4Lmid+CjXETfoHz7Uv+Vx6L0No7iRc/7cBL8gd6o9k9/g==", "path": "system.data.sqlclient/4.6.0", "hashPath": "system.data.sqlclient.4.6.0.nupkg.sha512"}, "System.Diagnostics.Contracts/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-eelRRbnm+OloiQvp9CXS0ixjNQldjjkHO4iIkR5XH2VIP8sUB/SIpa1TdUW6/+HDcQ+MlhP3pNa1u5SbzYuWGA==", "path": "system.diagnostics.contracts/4.3.0", "hashPath": "system.diagnostics.contracts.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.FileVersionInfo/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-omCF64wzQ3Q2CeIqkD6lmmxeMZtGHUmzgFMPjfVaOsyqpR66p/JaZzManMw1s33osoAb5gqpncsjie67+yUPHQ==", "path": "system.diagnostics.fileversioninfo/4.3.0", "hashPath": "system.diagnostics.fileversioninfo.4.3.0.nupkg.sha512"}, "System.Diagnostics.StackTrace/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiHg0vgtd35/DM9jvtaC1eKRpWZxr0gcQd643ABG7GnvSlf5pOkY2uyd42mMOJoOmKvnpNj0F4tuoS1pacTwYw==", "path": "system.diagnostics.stacktrace/4.3.0", "hashPath": "system.diagnostics.stacktrace.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "path": "system.diagnostics.tools/4.3.0", "hashPath": "system.diagnostics.tools.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.Dynamic.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SNVi1E/vfWUAs/WYKhE9+qlS6KqK0YVhnlT0HQtr8pMIA8YX3lwy3uPMownDwdYISBdmAF/2holEIldVp85Wag==", "path": "system.dynamic.runtime/4.3.0", "hashPath": "system.dynamic.runtime.4.3.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "path": "system.globalization.calendars/4.3.0", "hashPath": "system.globalization.calendars.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/5.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-EdcMk+36u9gQtbwTiPQ7ckIfiADBwOmCZ6rGD2rfkaozIdW1t7vbXk/FPVAu2r9KgCQZ5245Z+P0YMM/0Q0G2g==", "path": "system.identitymodel.tokens.jwt/5.3.0", "hashPath": "system.identitymodel.tokens.jwt.5.3.0.nupkg.sha512"}, "System.Interactive.Async/3.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-C07p0dAA5lGqYUPiPCK3paR709gqS4aMDDsje0v0pvffwzLaxmsn5YQTfZbyNG5qrudPx+BCxTqISnncQ3wIoQ==", "path": "system.interactive.async/3.2.0", "hashPath": "system.interactive.async.3.2.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "path": "system.io.compression/4.3.0", "hashPath": "system.io.compression.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.IO.Pipelines/4.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-NOC/SO4gSX6t0tB25xxDPqPEzkksuzW7NVFBTQGAkjXXUPQl7ZtyE83T7tUCP2huFBbPombfCKvq1Ox1aG8D9w==", "path": "system.io.pipelines/4.5.2", "hashPath": "system.io.pipelines.4.5.2.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Linq.Queryable/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Yn/WfYe9RoRfmSLvUt2JerP0BTGGykCZkQPgojaxgzF2N0oPo+/AhB8TXOpdCcNlrG3VRtsamtK2uzsp3cqRVw==", "path": "system.linq.queryable/4.0.1", "hashPath": "system.linq.queryable.4.0.1.nupkg.sha512"}, "System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-sYg+FtILtRQuYWSIAuNOELwVuVsxVyJGWQyOnlAzhV4xvhyFnON1bAzYYC+jjRW8JREM45R0R5Dgi8MTC5sEwA==", "path": "system.net.http/4.3.0", "hashPath": "system.net.http.4.3.0.nupkg.sha512"}, "System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "path": "system.net.primitives/4.3.0", "hashPath": "system.net.primitives.4.3.0.nupkg.sha512"}, "System.Net.WebSockets.WebSocketProtocol/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-FquLjdb/0CeMqb15u9Px6TwnyFl306WztKWu6sKKc5kWPYMdpi5BFEkdxzGoieYFp9UksyGwJnCw4KKAUfJjrw==", "path": "system.net.websockets.websocketprotocol/4.5.1", "hashPath": "system.net.websockets.websocketprotocol.4.5.1.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Private.DataContractSerialization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yDaJ2x3mMmjdZEDB4IbezSnCsnjQ4BxinKhRAaP6kEgL6Bb6jANWphs5SzyD8imqeC/3FxgsuXT6ykkiH1uUmA==", "path": "system.private.datacontractserialization/4.3.0", "hashPath": "system.private.datacontractserialization.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Metadata/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-COC1aiAJjCoA5GBF+QKL2uLqEBew4JsCkQmoHKbN3TlOZKa2fKLz5CpiRQKDz0RsAOEGsVKqOD5bomsXq/4STQ==", "path": "system.reflection.metadata/1.6.0", "hashPath": "system.reflection.metadata.1.6.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wz+0KOukJGAlXjtKr+5Xpuxf8+c8739RI1C+A2BoQZT+wMCCoMDDdO8/4IRHfaVINqL78GO8dW8G2lW/e45Mcw==", "path": "system.runtime.serialization.primitives/4.3.0", "hashPath": "system.runtime.serialization.primitives.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Xml/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-nUQx/5OVgrqEba3+j7OdiofvVq9koWZAC7Z3xGI8IIViZqApWnZ5+lLcwYgTlbkobrl/Rat+Jb8GeD4WQESD2A==", "path": "system.runtime.serialization.xml/4.3.0", "hashPath": "system.runtime.serialization.xml.4.3.0.nupkg.sha512"}, "System.Security.Claims/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-P/+BR/2lnc4PNDHt/TPBAWHVMLMRHsyYZbU1NphW4HIWzCggz8mJbTQQ3MKljFE7LS3WagmVFuBgoLcFzYXlkA==", "path": "system.security.claims/4.3.0", "hashPath": "system.security.claims.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-WG3r7EyjUe9CMPFSs6bty5doUqT+q9pbI80hlNzo2SkPkZ4VTuZkGWjpp77JB8+uaL4DFPRdBsAY+DX3dBK92A==", "path": "system.security.cryptography.cng/4.5.0", "hashPath": "system.security.cryptography.cng.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "path": "system.security.cryptography.openssl/4.3.0", "hashPath": "system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-TGQX51gxpY3K3I6LJlE2LAftVlIMqJf0cBGhz68Y89jjk3LJCB6SrwiD+YN1fkqemBvWGs+GjyMJukl6d6goyQ==", "path": "system.security.cryptography.pkcs/4.5.0", "hashPath": "system.security.cryptography.pkcs.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "path": "system.security.cryptography.x509certificates/4.3.0", "hashPath": "system.security.cryptography.x509certificates.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Xml/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-i2Jn6rGXR63J0zIklImGRkDIJL4b1NfPSEbIVHBlqoIb12lfXIigCbDRpDmIEzwSo/v1U5y/rYJdzZYSyCWxvg==", "path": "system.security.cryptography.xml/4.5.0", "hashPath": "system.security.cryptography.xml.4.5.0.nupkg.sha512"}, "System.Security.Permissions/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-9gdyuARhUR7H+p5CjyUB/zPk7/Xut3wUSP8NJQB6iZr8L3XUXTMdoLeVAg9N4rqF8oIpE7MpdqHdDHQ7XgJe0g==", "path": "system.security.permissions/4.5.0", "hashPath": "system.security.permissions.4.5.0.nupkg.sha512"}, "System.Security.Principal/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I1tkfQlAoMM2URscUtpcRo/hX0jinXx6a/KUtEQoz3owaYwl3qwsO8cbzYVVnjxrzxjHo3nJC+62uolgeGIS9A==", "path": "system.security.principal/4.3.0", "hashPath": "system.security.principal.4.3.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RpT2DA+L660cBt1FssIE9CAGpLFdFPuheB7pLpKpn6ZXNby7jDERe8Ua/Ne2xGiwLVG2JOqziiaVCGDon5sKFA==", "path": "system.text.regularexpressions/4.3.0", "hashPath": "system.text.regularexpressions.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Parallel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbjBNZHf/vQCfcdhzx7knsiygoCKgxL8mZOeocXZn5gWhCdzHIq6bYNKWX0LAJCWYP7bds4yBK8p06YkP0oa0g==", "path": "system.threading.tasks.parallel/4.3.0", "hashPath": "system.threading.tasks.parallel.4.3.0.nupkg.sha512"}, "System.Threading.Thread/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OHmbT+Zz065NKII/ZHcH9XO1dEuLGI1L2k7uYss+9C1jLxTC9kTZZuzUOyXHayRk+dft9CiDf3I/QZ0t8JKyBQ==", "path": "system.threading.thread/4.3.0", "hashPath": "system.threading.thread.4.3.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5zJ0XDxAIg8iy+t4aMnQAu0MqVbqyvfoUVl1yDV61xdo3Vth45oA2FoY4pPkxYAH5f8ixpmTqXeEIya95x0aCQ==", "path": "system.xml.xdocument/4.3.0", "hashPath": "system.xml.xdocument.4.3.0.nupkg.sha512"}, "System.Xml.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lJ8AxvkX7GQxpC6GFCeBj8ThYVyQczx2+f/cWHJU8tjS7YfI6Cv6bon70jVEgs2CiFbmmM8b9j1oZVx0dSI2Ww==", "path": "system.xml.xmldocument/4.3.0", "hashPath": "system.xml.xmldocument.4.3.0.nupkg.sha512"}, "System.Xml.XmlSerializer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-MYoTCP7EZ98RrANESW05J5ZwskKDoN0AuZ06ZflnowE50LTpbR5yRg3tHckTVm5j/m47stuGgCrCHWePyHS70Q==", "path": "system.xml.xmlserializer/4.3.0", "hashPath": "system.xml.xmlserializer.4.3.0.nupkg.sha512"}, "System.Xml.XPath/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-v1JQ5SETnQusqmS3RwStF7vwQ3L02imIzl++sewmt23VGygix04pEH+FCj1yWb+z4GDzKiljr1W7Wfvrx0YwgA==", "path": "system.xml.xpath/4.3.0", "hashPath": "system.xml.xpath.4.3.0.nupkg.sha512"}, "System.Xml.XPath.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-jw9oHHEIVW53mHY9PgrQa98Xo2IZ0ZjrpdOTmtvk+Rvg4tq7dydmxdNqUvJ5YwjDqhn75mBXWttWjiKhWP53LQ==", "path": "system.xml.xpath.xdocument/4.3.0", "hashPath": "system.xml.xpath.xdocument.4.3.0.nupkg.sha512"}}}