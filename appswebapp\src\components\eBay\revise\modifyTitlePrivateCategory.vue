<template>
  <b-card>
    <b-row class="border-bottom mb-2 py-2">
      <b-col>
        <strong>{{getHeader}}</strong>
      </b-col>
      <c-button v-if="!isDisabled" :message="`Are you sure you want ${btnDesc}?`" variant="primary" size="sm" @confirm="onConfirm">
        {{btnDesc}}
      </c-button>
      <loader class="mr-5" v-else size="sm"/>
    </b-row>
    <detail-row bootstrapMode :fixed-payload-width="true">
      <span slot="title">Listing ID:</span>
      <b-link slot="payload" :href="getListingUrl" class="text-info"><u>{{revise.AuctionId}}</u></b-link>
    </detail-row>
    <detail-row bootstrapMode :fixed-payload-width="true">
      <span slot="title">Listing Title:</span>
      <b-form-input slot="payload" v-model="modifyTitlePrivateCategoryData.AuctionTitle"></b-form-input>
    </detail-row>
    <detail-row bootstrapMode :fixed-payload-width="true">
      <span slot="title">Private Listing:</span>
      <b-form-checkbox slot="payload" v-model="modifyTitlePrivateCategoryData.IsPrivateListing">Make this a Private Listing</b-form-checkbox>
    </detail-row>
    <detail-row bootstrapMode :fixed-payload-width="true" v-if="isEnabledReviseEBayCategory">
      <span slot="title">eBay Category Number:</span>
      <b-form-input type="number" min="0" slot="payload" v-model="modifyTitlePrivateCategoryData.EBayCategoryNumber"></b-form-input>
    </detail-row>
    <detail-row bootstrapMode :fixed-payload-width="true">
      <span slot="title">eBay Sub-Model:</span>
      <b-form-input slot="payload" v-model="modifyTitlePrivateCategoryData.EBaySubModel"></b-form-input>
    </detail-row>
  </b-card>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import constants from '@/shared/ebay/constants'
import {mapGetters} from 'vuex'
import globals from '../../../globals'
import loader from '@/components/_shared/loader'

export default {
  props: {
    btnDesc: {
      type: String,
      default: 'Revise'
    },
    reviseHeader: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      modifyTitlePrivateCategoryData: {
        AuctionTitle: '',
        IsPrivateListing: false,
        EBayCategoryNumber: 0,
        EBaySubModel: ''
      },
      isDisabled: false
    }
  },
  mounted () {
    this.initData()
  },
  computed: {
    ...mapGetters('eBayRevise', ['revise']),
    isEnabledReviseEBayCategory () {
      return !this.revise.IsLocal
    },
    getHeader () {
      if (!this.isEnabledReviseEBayCategory) {
        return this.reviseHeader.replace('/Category', '')
      }
      return this.reviseHeader
    },
    getListingUrl () {
      return constants.eBayInfoUrls.ebayItemUrl(this.revise.AuctionId)
    }
  },
  components: {
    detailRow,
    loader
  },
  methods: {
    onConfirm () {
      this.isDisabled = true
      let apiParams = {
        accountId: this.revise.AccountId,
        auctionId: this.revise.AuctionId,
        data: this.modifyTitlePrivateCategoryData
      }

      this.$store.dispatch('eBayRevise/modifyTitlePrivateCategory', apiParams).then(res => {
        this.$toaster.success(`${this.getHeader} Successfully`)
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
        if (ex.response && ex.response.status !== 400) {
          this.$logger.handleError(ex, `Exception occurred on ${this.reviseHeader}`)
        }
      }).finally(() => {
        this.isDisabled = false
        setTimeout(() => this.$router.go(), 4000)
      })
    },
    initData () {
      this.modifyTitlePrivateCategoryData = {
        AuctionTitle: globals().getClonedValue(this.revise.AuctionTitle),
        IsPrivateListing: globals().getClonedValue(this.revise.IsPrivate),
        EBayCategoryNumber: globals().getClonedValue(this.revise.EBayCategoryId),
        EBaySubModel: globals().getClonedValue(this.revise.EBaySubModel)
      }
    }
  }
}
</script>
