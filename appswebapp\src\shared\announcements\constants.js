const tooltipRelativePositionOptions = [
  {
    value: 1,
    text: 'Top',
    shepherdValue: 'top'
  },
  {
    value: 2,
    text: 'Top Start',
    shepherdValue: 'top-start'
  },
  {
    value: 3,
    text: 'Top End',
    shepherdValue: 'top-end'
  },
  {
    value: 4,
    text: 'Bottom',
    shepherdValue: 'bottom'
  },
  {
    value: 5,
    text: 'Bottom Start',
    shepherdValue: 'bottom-start'
  },
  {
    value: 6,
    text: 'Bottom End',
    shepherdValue: 'bottom-end'
  },
  {
    value: 7,
    text: 'Right',
    shepherdValue: 'right'
  },
  {
    value: 8,
    text: 'Right Start',
    shepherdValue: 'right-start'
  },
  {
    value: 9,
    text: 'Right End',
    shepherdValue: 'right-end'
  },
  {
    value: 10,
    text: 'Left',
    shepherdValue: 'left'
  },
  {
    value: 11,
    text: 'Left Start',
    shepherdValue: 'left-start'
  },
  {
    value: 12,
    text: 'Left End',
    shepherdValue: 'left-end'
  }
]

const announcementTypes = Object.freeze({
  dynamic: {value: 1, text: 'Dynamic'},
  static: {value: 2, text: 'Static'}
})

const announcementAccountFilterTypes = Object.freeze({
  none: {value: 0, text: 'All Accounts'},
  accountsInclude: {value: 1, text: 'Include Only Selected Account IDs'},
  accountsExclude: {value: 2, text: 'Exclude Selected Account IDs'}
})

export {
  tooltipRelativePositionOptions,
  announcementTypes,
  announcementAccountFilterTypes
}
