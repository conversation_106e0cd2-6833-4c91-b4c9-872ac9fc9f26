<template>
  <div class="fullwidth-element bg-dark dark text-white">
    <div class="container-fluid container-p-y">
      <div class="chart-filters text-center">
        <graph-tabs
          :tabsConfiguration="barTabs"
          @tabSelect="onGraphTypeChanged"
          @subTabSelect="onDeviceFilterChanged"
        />
      </div>

      <div>
        <vue-echart
          :options="barOptions"
          :auto-resize="true"
        ></vue-echart>
      </div>

      <div class="row mt-3 widget-metric-higlights">

        <div class="col-6 col-sm-6 col-xl-3">
          <summary-card
            label="Sessions"
            :value="summary.sessions"
            :delta="summary.sessionsDelta"
            :rangeLabel="summary.label">
            <i class="ion ion-md-people h1 m-0 opacity-25 d-none d-sm-inline"></i>
          </summary-card>
        </div>

        <div class="col-6 col-sm-6 col-xl-3">
          <summary-card
            label="Views"
            :value="summary.pageViews"
            :delta="summary.pageViewsDelta"
            :rangeLabel="summary.label">
            <i class="ion ion-md-eye h1 m-0 opacity-25 d-none d-sm-inline"></i>
          </summary-card>
        </div>

        <div class="col-6 col-sm-6 col-xl-3">
          <summary-card
            label="Leads"
            :value="summary.totalLeads"
            :delta="summary.totalLeadsDelta"
            :rangeLabel="summary.label">
            <i class="ion ion-md-chatboxes h1 m-0 opacity-25 d-none d-sm-inline"></i>
          </summary-card>
        </div>

        <div class="col-6 col-sm-6 col-xl-3">
          <summary-card
            label="Conv. Rate per Session"
            :value="summary.convRatePerSession"
            :delta="summary.convRatePerSessionDelta"
            :rangeLabel="summary.label">
            <template slot="value" slot-scope="valueScope">
              {{valueScope.localizedValue}}%
            </template>
            <i class="ion ion-md-pie h1 m-0 opacity-25 d-none d-sm-inline"></i>
          </summary-card>
        </div>

      </div>
    </div>
  </div>
</template>

<script>
import dateHelper from '../../../plugins/locale/date'

import 'echarts/lib/chart/bar'
import 'echarts/lib/component/tooltip'
import 'echarts/lib/component/legend'

export default {
  name: 'website-overview-summary',
  props: {
    barItems: { type: Array, required: true },
    summary: { type: Object, required: true },
    barTimeFormat: { type: String, required: true },
    deviceFilter: { type: String, required: true }
  },
  components: {
    'vue-echart': () => import('vue-echarts/components/ECharts.vue'),
    'summary-card': () => import('../summaryCard.vue'),
    'graph-tabs': () => import('../graphTabs.vue')
  },
  data () {
    return {
      bar: {
        activeTab: 'sessions'
      }
    }
  },
  computed: {
    barTabs () {
      return {
        defaultTabKey: this.bar.activeTab,
        tabs: [
          {
            key: 'sessions',
            label: 'Sessions',
            iconClass: 'ion-md-people'
          }, {
            key: 'pageViews',
            label: 'Views',
            iconClass: 'ion-md-eye'
          }, {
            key: 'totalLeads',
            label: 'Leads',
            iconClass: 'ion-md-chatboxes'
          }, {
            key: 'convRatePerSession',
            label: 'Conv. <span class="d-none d-inline-md">Rate per</span> Session',
            iconClass: 'ion-md-pie'
          }, {
            key: 'devices',
            label: 'Devices',
            iconClass: 'ion-md-laptop',
            defaultTabKey: 'all',
            tabs: [
              {
                key: 'all',
                label: 'All',
                iconClass: 'ion-ios-apps'
              }, {
                key: 'desktop',
                label: 'Desktop',
                iconClass: 'ion-ios-laptop'
              }, {
                key: 'mobile',
                label: 'Mobile',
                iconClass: 'ion-ios-phone-portrait'
              }
            ]
          }
        ]
      }
    },
    barOptions () {
      return {
        color: '#6b0001',
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            return `${params[0].name}<br />${this.$locale.formatNumber(params[0].value)}`
          },
          axisPointer: {
            type: 'line',
            lineStyle: {
              color: '#444'
            }
          },
          textStyle: {
            fontSize: 13
          }
        },
        grid: {
          left: '0',
          right: '0',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: this.barItems.map(x => {
              return dateHelper.getDayFormatted(x.dateFrom, this.barTimeFormat)
            }),
            axisTick: {
              show: true,
              alignWithLabel: true
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: '#444'
              }
            },
            axisLabel: {
              color: 'rgba(255, 255, 255, .9)'
            }
          }
        ],
        yAxis: [
          {
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#444'
              }
            },
            axisLabel: {
              color: 'rgba(255, 255, 255, .9)'
            },
            type: 'value'
          }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 50,
            shadowColor: 'rgba(0, 0, 0, 0.3)'
          }
        },
        series: [
          {
            barWidth: '4',
            type: 'bar',
            data: this.barItems.map(x => x[this.bar.activeTab]),
            itemStyle: {
              normal: {
                color: '#dc3545'
              },
              emphasis: {
                shadowBlur: 50,
                shadowColor: '#dc3545',
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0.5,
                      color: '#dc3545'
                    },
                    {
                      offset: 1,
                      color: '#ff5a44'
                    }
                  ],
                  globalCoord: false // false by default
                }
              }
            }
          }
        ],
        animationDuration: 2000
      }
    }
  },
  methods: {
    onGraphTypeChanged (newTab) {
      this.bar.activeTab = newTab.key
    },
    async onDeviceFilterChanged (tab) {
      this.$emit('deviceFilterChanged', tab)
    }
  }
}
</script>

<style scoped>

</style>
