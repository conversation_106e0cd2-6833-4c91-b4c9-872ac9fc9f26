import axios from 'axios'
import { announcementTypes } from '@/shared/announcements/constants'

export default {
  namespaced: true,
  state: {
    dynamicAnnouncements: [],
    staticAnnouncements: []
  },
  getters: {
    dynamicAnnouncements: state => state.dynamicAnnouncements,
    staticAnnouncements: state => state.staticAnnouncements
  },
  mutations: {
    setDynamicAnnouncements (state, announcements) {
      state.dynamicAnnouncements = announcements
    },
    setStaticAnnouncements (state, announcements) {
      state.staticAnnouncements = announcements
    },
    setEmptyAnnouncements (state) {
      state.dynamicAnnouncements = []
      state.staticAnnouncements = []
    }
  },
  actions: {
    async populateUserAnnouncements ({commit}) {
      let response = await axios.get('/api/user_announcement/')
      if (response && response.status === 200 && Array.isArray(response.data) && response.data.length > 0) {
        commit('setDynamicAnnouncements', response.data.filter(x => x.announcementType === announcementTypes.dynamic.value))
        commit('setStaticAnnouncements', response.data.filter(x => x.announcementType === announcementTypes.static.value))
      }
    },
    exitAnnouncement ({commit}, params) {
      axios.post(`/api/user_announcement/${params.id}/exit/`)
    },
    reviewAnnouncement ({commit}, params) {
      axios.post(`/api/user_announcement/${params.id}/review/`)
    },
    cleanUp ({commit}) {
      commit('setEmptyAnnouncements')
    }
  }
}
