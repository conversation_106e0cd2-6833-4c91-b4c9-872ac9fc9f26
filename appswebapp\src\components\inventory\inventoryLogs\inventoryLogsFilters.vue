<template>
  <div class="position-relative">
    <h4>Logs</h4>
    <paging
      class="custom-logs-paging d-none d-xl-block"
      :totalItems="totalItems"
      :pageNumber="filter.page"
      :pageSize="filter.pageSize"
      @numberChanged="onPageChanged"
      @changePageSize="onPageSizeChanged" />

    <b-tabs v-model='selectedTabIndex' class="nav-tabs-top nav-responsive-sm">
      <b-tab v-for="(tab, key) in logTabs" :key="key" :title='tab.text'>
        <inventory-log-filter-form :filter='filter' :logType='tab.value' @filterApply='onFilterApply' />
      </b-tab>
    </b-tabs>

    <b-card v-if="!isLoading">
      <inventory-logs-listing v-if="totalItems > 0" :logType='filter.logType' :items='items' :totalItems='totalItems' @onSortChanged='onSortChanged'/>
      <span v-else class="text-muted">Not Found</span>
      <paging
        :totalItems="totalItems"
        :pageNumber="filter.page"
        :pageSize="filter.pageSize"
        titled
        pageSizeSelector
        @numberChanged="onPageChanged"
        @changePageSize="onPageSizeChanged" />
    </b-card>
    <div v-else class="py-5">
      <loader size="lg"/>
    </div>
  </div>
</template>

<script>
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import inventoryLogsListing from './inventoryLogsListing'
import inventoryLogFiltersForm from './inventoryLogFiltersForm'
import InventoryLogService from '@/services/logs/InventoryLogService'
import paging from '@/components/_shared/paging.vue'
import commonConstants from '@/shared/common/constants'
import { logTypes } from '@/shared/inventory/inventoryTypes'
import loader from '../../_shared/loader'

const defaultValues = new ObjectSchema({
  pageSize: { type: Number, default: 25 },
  page: { type: Number, default: 1 },
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  sortType: { type: Number, default: 4 },
  search: { type: String, default: '' },
  vin: { type: String, default: '' },
  logType: { type: Number, default: 0 },
  task_type: { type: String, default: null },
  type: { type: Number, default: 0 },
  historyType: { type: Number, default: 0 }
})
const queryHelper = new QueryStringHelper(defaultValues)

export default {
  name: 'inventory-logs-filters',
  components: {
    'inventory-logs-listing': inventoryLogsListing,
    'paging': paging,
    'inventory-log-filter-form': inventoryLogFiltersForm,
    'loader': loader
  },
  data () {
    return {
      logTabs: Object.values(logTypes),
      accountId: +this.$route.params.accountId,
      items: [],
      totalItems: 0,
      isLoading: true,
      filter: defaultValues.getObject()
    }
  },
  computed: {
    selectedTabIndex: {
      get: function () {
        if (this.filter.logType >= this.logTabs.length || this.filter.logType < 0) {
          return 0
        }
        let res = this.logTabs.find(x => x.value === this.filter.logType)
        if (res) {
          return res.value
        }

        return 0
      },
      set: function (index) {
        this.setDefaultFilters()
        this.filter.logType = index
        this.items = []
        this.totalItems = 0
        this.isLoading = true
        this.synchronizeUrlAndReload()
      }
    }
  },
  async created () {
    this.filter = queryHelper.parseQueryStringToObject(this.$router)
    if (this.filter.logType >= this.logTabs.length || this.filter.logType < 0) {
      this.filter.logType = 0
    }
    await this.synchronizeUrlAndReload()
  },
  methods: {
    async onPageChanged (newPage) {
      this.filter.page = newPage
      this.isLoading = true
      await this.synchronizeUrlAndReload()
    },
    async onPageSizeChanged (newSize) {
      this.filter.pageSize = newSize
      this.filter.page = 1
      this.isLoading = true
      await this.synchronizeUrlAndReload()
    },
    async onSortChanged (sortType) {
      this.filter.sortType = sortType
      await this.synchronizeUrlAndReload()
    },
    async onFilterApply () {
      this.isLoading = true
      await this.synchronizeUrlAndReload()
    },
    async synchronizeUrlAndReload () {
      queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
      await this.loadContent()
    },
    setDefaultFilters () {
      this.filter.search = ''
      this.filter.vin = ''
      this.filter.page = 1
      this.filter.sortType = 0
    },
    async loadContent () {
      switch (this.filter.logType) {
        case logTypes.mobile.value:
          await this.getMobileLogs()
          break
        case logTypes.desktop.value:
          await this.getDesktopLogs()
          break
        case logTypes.settings.value:
          await this.getSettingsLogs()
          break
        case logTypes.vehiclePostProcessing.value:
          await this.getVehiclePostProcessingLogs()
          break
        case logTypes.vehicleHistoryReport.value:
          await this.getVehicleHistoryReportLogs()
          break
      }
    },
    async getVehicleHistoryReportLogs () {
      const apiFilter = {
        ...this.filter,
        accountId: this.accountId
      }
      try {
        const apiResult = await InventoryLogService.getVehicleHistoryReportLogs(apiFilter)

        this.items = apiResult.data.items
        this.totalItems = apiResult.data.itemsTotalCount
      } catch (err) {
        if (err.response && err.response.data) {
          this.$toaster.error(err.response.data)
        } else {
          this.$toaster.error('Failed to get data from server')
          this.$logger.handleError(err, 'Can\'t reset UI', this.filter)
        }
      } finally {
        this.isLoading = false
      }
    },
    async getVehiclePostProcessingLogs () {
      const apiFilter = {
        ...this.filter,
        accountId: this.accountId
      }
      try {
        const apiResult = await InventoryLogService.getVehiclePostProcessingLogs(apiFilter)

        this.items = apiResult.data.items
        this.totalItems = apiResult.data.itemsTotalCount
      } catch (err) {
        if (err.response && err.response.data) {
          this.$toaster.error(err.response.data)
        } else {
          this.$toaster.error('Failed to get data from server')
          this.$logger.handleError(err, 'Can\'t reset UI', this.filter)
        }
      } finally {
        this.isLoading = false
      }
    },
    async getDesktopLogs () {
      const apiFilter = {
        ...this.filter,
        type: commonConstants.clientTypes.desktop,
        accountId: this.accountId
      }

      try {
        const apiResult = await InventoryLogService.getInventoryApiLogs(apiFilter)

        this.items = apiResult.data.items
        this.totalItems = apiResult.data.itemsTotalCount
      } catch (err) {
        if (err.response && err.response.data) {
          this.$toaster.error(err.response.data)
        } else {
          this.$toaster.error('Failed to get data from server')
          this.$logger.handleError(err, 'Can\'t reset UI', this.filter)
        }
      } finally {
        this.isLoading = false
      }
    },
    async getMobileLogs () {
      const apiFilter = {
        ...this.filter,
        type: commonConstants.clientTypes.mobile,
        accountId: this.accountId
      }

      try {
        const apiResult = await InventoryLogService.getInventoryApiLogs(apiFilter)

        this.items = apiResult.data.items
        this.totalItems = apiResult.data.itemsTotalCount
      } catch (err) {
        if (err.response && err.response.data) {
          this.$toaster.error(err.response.data)
        } else {
          this.$toaster.error('Failed to get data from server')
          this.$logger.handleError(err, 'Can\'t reset UI', this.filter)
        }
      } finally {
        this.isLoading = false
      }
    },
    async getSettingsLogs () {
      let apiFilter = {
        ...this.filter,
        accountId: this.accountId
      }

      try {
        const apiResult = await InventoryLogService.getSettingsLogsByAccountId(apiFilter)

        this.items = apiResult.data.items
        this.totalItems = apiResult.data.itemsTotalCount
      } catch (err) {
        if (err.response && err.response.data) {
          this.$toaster.error(err.response.data)
        } else {
          this.$toaster.error('Failed to get data from server')
          this.$logger.handleError(err, 'Can\'t reset UI', this.filter)
        }
      } finally {
        this.isLoading = false
      }
    }
  }
}
</script>

<style lang="scss">
.custom-logs-paging {
  position: absolute;
  right: 0px;
  top: -20px;
  z-index: 2;
}
@media (max-width: 575px) {
  .nav-responsive-sm > .nav, .nav-responsive-sm > div > .nav {
      flex-wrap: nowrap!important;
      -webkit-box-orient: horizontal;
      -webkit-box-direction: normal;
      -ms-flex-direction: row;
      flex-direction: row;
      border: 0;
      overflow-x: scroll;
      white-space: nowrap;
      -webkit-overflow-scrolling: touch;
  }
  .nav-responsive-sm > div > .nav-tabs .nav-item {
    -webkit-box-flex: 1 !important;
    -ms-flex: 1 1 auto !important;
    flex: 1 1 auto !important;
  }
}
</style>
