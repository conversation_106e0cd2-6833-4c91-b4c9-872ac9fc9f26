<template>
  <div>
    <b-table
    class="products-table card-table"
    :fields="tableFields"
    :items="items"
    :striped="true"
    :bordered="false"
    :no-sort-reset="true"
    :no-local-sorting="true"
    :sort-by.sync="sortBy"
    :sort-desc.sync="sortDesc"
    @sort-changed="onSortChanged"
    responsive
    >
      <template #cell(title)="data">
        <div class="media align-items-center">
          <router-link class="media-body text-dark d-flex align-items-center" :to="{ path: getDetailsPath(data.item) }">
            <img class="d-block float-left ui-w-50 mr-3" style="min-height: 1px" :src="getVehiclePhotoSrc(data.item)">
            <span>{{data.item | getVehicleTitle}}</span>
          </router-link>
        </div>
      </template>
      <template #cell(actions)="data">
        <b-button size="sm" variant="secondary" :to="getDetailsPath(data.item)">Add Photos</b-button>
      </template>
    </b-table>
  </div>
</template>

<script>

export default {
  name: 'inventory-task-photo-listing',
  props: {
    sortField: String,
    sortOrderDesc: Boolean,
    items: Array
  },
  data () {
    return {

    }
  },
  filters: {
    getVehicleTitle: function (value) {
      if (!value) return ''
      let title = ''
      if (value.year > 0) {
        title = value.year.toString()
      }

      title = [title, value.make, value.model, value.trim].filter(v => v && v !== '').join(' ')

      return title.trim()
    }
  },
  computed: {
    tableFields () {
      return [
        {
          key: 'title',
          label: 'Vehicle',
          tdClass: 'py-2 align-middle',
          sortable: true
        },
        {
          key: 'stockNumber',
          label: 'Stock #',
          tdClass: 'py-2 align-middle',
          sortable: true
        },
        {
          key: 'age',
          label: 'Age',
          tdClass: 'py-2 align-middle',
          sortable: true
        },
        {
          key: 'actualPhotosCount',
          label: 'Actual Photos Count',
          tdClass: 'py-2 align-middle',
          sortable: true
        },
        {
          key: 'actions',
          label: 'Actions',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    sortBy: {
      get () {
        return this.sortField
      },
      set () {}
    },
    sortDesc: {
      get () {
        return this.sortOrderDesc
      },
      set () {}
    }
  },
  methods: {
    onSortChanged (value) {
      this.$emit('sortChanged', value)
    },
    getVehiclePhotoSrc (item) {
      return (item.presentationPhoto || {}).photo107 + '?q=' + item.actualPhotosDateTimeEdited
    },
    getDetailsPath (item, hasToAppendTab) {
      return `/inventory/${item.accountId}/edit/${item.vin}?tabindex=2`
    }
  }
}
</script>
