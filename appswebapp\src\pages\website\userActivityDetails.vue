<template>
  <div>
    <h4>User Activity Log Details</h4>
    <b-card>
      <log-node
        v-if='logDetails'
        :data="logDetails"
        :isExpandedShallow="true"
        :isExpandedDeep="false"
      />
    </b-card>
  </div>
</template>

<script>

import logNode from '@/components/_shared/logItemNode.vue'
import UserActivityService from '@/services/website/UserActivityService'

export default {
  name: 'website-user-activity-details',
  metaInfo: {
    title: 'User Activity Details'
  },
  props: {
    id: String
  },
  data () {
    return {
      logDetails: null
    }
  },
  created () {
    UserActivityService.getUserActivityDetails(this.id).then(res => {
      this.logDetails = { nodes: res.data.details }
    }).catch(ex => {
      this.$toaster.exception(ex, 'Failed on getting User Activity details')
      this.$logger.handleError(ex, 'Exception occurred on api calling')
    })
  },
  components: {
    logNode
  }
}
</script>
