const clientTypes = Object.freeze({
  undefined: 0,
  desktop: 1,
  mobile: 2
})

const paginationDirectionTypes = Object.freeze({
  undefined: 0,
  toFuture: 1,
  toPast: 2
})

const dayOfWeek = Object.freeze({
  monday: 1,
  tuesday: 2,
  wednesday: 3,
  thursday: 4,
  friday: 5,
  saturday: 6,
  sunday: 0
})

const hoursOptions = ['12 AM', '1 AM', '2 AM', '3 AM', '4 AM', '5 AM', '6 AM',
  '7 AM', '8 AM', '9 AM', '10 AM', '11 AM', '12 PM', '1 PM', '2 PM', '3 PM',
  '4 PM', '5 PM', '6 PM', '7 PM', '8 PM', '9 PM', '10 PM', '11 PM']

export default {
  clientTypes,
  paginationDirectionTypes,
  hoursOptions,
  dayOfWeek
}
