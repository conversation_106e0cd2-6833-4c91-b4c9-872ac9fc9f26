<template>
  <div v-if='!isLoading && !isException'>
    <div class="py-2">
      <b-row>
        <b-col class="d-flex align-items-center">
          <h4 class="m-0">{{isEditMode ? 'Edit' : 'Add'}} Phone Campaign</h4>
        </b-col>
        <b-col class="d-flex justify-content-end">
          <b-btn variant="primary" size="sm" @click="onSubmit" class="mr-2 custom-btn-height" :disabled="isSubmitButtonDisabled">Submit</b-btn>
          <b-btn variant="secondary" size="sm" class="custom-btn-height" @click="$router.go(-1)">Close</b-btn>
        </b-col>
      </b-row>
    </div>
    <b-card>
      <!-- Proxy Phone Settings Section -->
      <div class="mt-1">
        <div class="border-bottom">
          <b-row>
            <b-col><h6 class="float-left">Proxy Phone Settings</h6></b-col>
          </b-row>
        </div>
        <div>
          <div class="d-flex flex-column mt-4">
            <b-form-checkbox v-model='phoneCommunication.isCustomerCallProxyEnabled'>
              <div class="d-flex flex-column">
                <span>Use a Proxy Phone for each new Contact/Caller</span>
                <span class="text-muted">Enable this setting to be able to receive SMS messages on this campaign</span>
              </div>
            </b-form-checkbox>
            <b-form-checkbox class="mt-3" v-model='phoneCommunication.isImportEnabled' v-if='!isEditMode'>
              <div class="d-flex flex-column">
                <span>Import an Existing Twilio Number</span>
                <span class="text-muted">Select this option if a phone number has already been posted into our Twilio Account</span>
                <div class="d-flex flex-row mt-2" v-if='phoneCommunication.isImportEnabled'>
                  <phone-input v-model='phoneNumber'></phone-input>
                  <b-btn size="sm" :disabled='!isPhone' @click='onCheckPhone' variant="outline-success">
                    <i class="ion ion-md-search h1 m-0 mb-1 opacity-50 d-none d-sm-inline" style="font-size: 24px;"></i>
                  </b-btn>
                </div>
              </div>
            </b-form-checkbox>
            <div v-else>
              <detail-row fixedPayloadWidth>
                <span slot="title">Proxy Phone Number:</span>
                <b-form-input v-model='phoneCommunication.proxyPhone' slot="payload" disabled />
              </detail-row>
            </div>
          </div>
          <div v-if='phone'>
            <detail-row fixedPayloadWidth v-if='!isEditMode'>
              <span slot="title">Number SID:</span>
              <b-form-input v-model='phone.phoneNumberSid' slot="payload" disabled />
            </detail-row>
            <detail-row fixedPayloadWidth v-if='!isEditMode'>
              <span slot="title">Friendly Name:</span>
              <b-form-input v-model='phone.friendlyName' slot="payload" disabled />
            </detail-row>
            <detail-row fixedPayloadWidth v-if='!isEditMode'>
              <span slot="title">Is Toll Free:</span>
              <b-form-checkbox v-model='phone.isTollFree' slot="payload" disabled />
            </detail-row>
          </div>
        </div>
      </div>
      <!-- Proxy Phone Settings Section End -->
      <!-- Campaign Settings Section -->
      <div class="mt-4">
        <div class="border-bottom">
          <b-row>
            <b-col><h6 class="float-left">Campaign Settings</h6></b-col>
          </b-row>
        </div>
         <div>
          <detail-row fixedPayloadWidth v-if='!isEditMode'>
            <span slot="title">Location Type:</span>
            <b-form-select v-model='selectedCampaignsType' :value='getLocationOptions[0].value' :options='getLocationOptions' slot="payload"/>
          </detail-row>
          <detail-row fixedPayloadWidth>
            <span slot="title">Campaign(s):</span>
            <multiselect slot="payload"
              v-model='selectedCampaigns'
              track-by="campaignTypeId"
              :options='availableCampaigns'
              label='campaignName'
              :multiple="true"
              :disabled='isEditMode'
              :closeOnSelect='false'
              :showLabels='false'
              :showPointer='false'
              :limit='3'
            />
          </detail-row>
          <detail-row fixedPayloadWidth>
            <span slot="title">Contact(s):</span>
            <multiselect slot="payload"
              v-model='selectedContacts'
              :options='availableContacts'
              group-values="data"
              group-label="groupName"
              track-by="trackId"
              label="title"
              :closeOnSelect='false'
              :group-select="false"
              :multiple="true"
              :showLabels='false'
              :showPointer='false'
              @select='onSelectContact'
              :limit='3'
            />
          </detail-row>
          <detail-row fixedPayloadWidth>
            <span slot="title">Alternate Contact(s):</span>
            <b-form-checkbox v-model='hasToShowAlternativeContacts' slot="payload"/>
          </detail-row>
          <detail-row fixedPayloadWidth v-if='hasToShowAlternativeContacts'>
            <span slot="title"></span>
            <multiselect slot="payload"
              v-model='selectedAlternativeContacts'
              :options='alternativeContacts'
              group-values="data"
              group-label="groupName"
              track-by="trackId"
              label="title"
              :closeOnSelect='false'
              :group-select="false"
              :multiple="true"
              :showLabels='false'
              :showPointer='false'
              :limit='3'
              @select="onSelectAlternativeContact"
            />
          </detail-row>
          <detail-row fixedPayloadWidth>
            <span slot="title">Legacy Campaign Id:</span>
            <b-form-select v-model='phoneCommunication.legacyCampaignId' slot="payload" :options='legacyCampaignIds' />
          </detail-row>
          <detail-row fixedPayloadWidth v-if='phoneCommunication.campaignsType === campaignTypes.offSiteParent.value'>
            <span slot="title">Departments:</span>
            <b-form-select v-model='phoneCommunication.departmentType' text-field='name' value-field='id' slot="payload" :options='availableDepartmentTypes' />
          </detail-row>
        </div>
      </div>
      <!-- Campaign Settings Section End -->
      <!-- Disclaimer Settings Section -->
      <div class="mt-4">
        <div class="border-bottom">
          <b-row>
            <b-col><h6 class="float-left">Disclaimer Settings</h6></b-col>
          </b-row>
        </div>
        <div>
          <detail-row fixedPayloadWidth>
            <span slot="title">Initial SMS Confirmation:</span>
            <b-form-checkbox v-model='phoneCommunication.smsSettings.hasToUseCustomMessages' slot="payload"/>
          </detail-row>
          <detail-row fixedPayloadWidth>
            <span slot="title"></span>
            <b-form-textarea v-model='phoneCommunication.smsSettings.initialConfirmation' :disabled='!phoneCommunication.smsSettings.hasToUseCustomMessages' rows="5" slot="payload"/>
          </detail-row>
          <detail-row fixedPayloadWidth>
            <span slot="title">Phone Whisper/Disclaimer:</span>
            <b-form-checkbox v-model='phoneCommunication.hasToEnableDisclaimer' slot="payload"/>
          </detail-row>
          <detail-row fixedPayloadWidth>
            <span slot="title"></span>
            <b-form-textarea v-model='phoneCommunication.disclaimerText' :disabled='!phoneCommunication.hasToEnableDisclaimer' rows="5" slot="payload"/>
          </detail-row>
        </div>
      </div>
      <!-- Disclaimer Settings Section End -->
      <!-- Tracking & Notification Settings Section -->
      <div class="mt-4">
        <div class="border-bottom">
          <b-row>
            <b-col><h6 class="float-left">Tracking & Notification Settings</h6></b-col>
          </b-row>
        </div>
        <div>
          <div class="d-flex flex-column mt-2">
            <b-form-checkbox v-model='phoneCommunication.hasToRecordCall'>Enable Call Recording</b-form-checkbox>
            <b-form-checkbox v-model='phoneCommunication.notificationSettings.hasToSendUserEmail' class="mt-3">Send Auto Response Email to Lead</b-form-checkbox>
            <b-form-checkbox v-if="phoneCommunication.notificationSettings.hasToShowDealerSocketSettings" v-model='phoneCommunication.notificationSettings.hasToSendDealerSocketLead' class="mt-3">Send Dealer Socket Lead</b-form-checkbox>
            <b-form-checkbox v-if="phoneCommunication.notificationSettings.hasToShowShiftDigitalSettings" v-model='phoneCommunication.notificationSettings.hasToSendShiftDigitalLead' class="mt-3">Send Shift Digital Lead</b-form-checkbox>
            <b-form-checkbox v-model='phoneCommunication.notificationSettings.hasToSendDealerEmail' class="mt-3">Send Notification Email to Dealership</b-form-checkbox>
            <div class="mt-3 d-flex flex-row ml-4" v-if='phoneCommunication.notificationSettings.hasToSendDealerEmail'>
              <div>
                <label class="text-muted" for="DealershipEmailTo">Dealership Emails (To):</label>
                <multi-input
                  type="email"
                  validateRules='email'
                  :values='phoneCommunication.notificationSettings.dealerEmailAddresses'
                  placeholder="<EMAIL>"
                  v-model='phoneCommunication.notificationSettings.dealerEmailAddresses'
                />
              </div>
              <div class="ml-4">
                <label class="text-muted" for="DealershipEmailCC">Dealership Emails (CC):</label>
                <multi-input
                  type="email"
                  validateRules='email'
                  :values='phoneCommunication.notificationSettings.dealerEmailAddressesCc'
                  placeholder="<EMAIL>"
                  v-model="phoneCommunication.notificationSettings.dealerEmailAddressesCc"
                />
              </div>
            </div>
            <b-form-checkbox v-model='phoneCommunication.notificationSettings.hasToSendAdfEmail' class="mt-3">Send ADF Email to CRM</b-form-checkbox>
            <div v-if='phoneCommunication.notificationSettings.hasToSendAdfEmail'>
              <div class="mt-3 d-flex flex-row ml-4">
                <div>
                  <label class="text-muted" for="ADFEmailTo">ADF Emails (To):</label>
                  <multi-input
                    type="email"
                    validateRules='email'
                    :values='phoneCommunication.notificationSettings.adfEmailAddresses'
                    placeholder="<EMAIL>"
                    v-model="phoneCommunication.notificationSettings.adfEmailAddresses"
                  />
                </div>
                <div class="ml-4">
                  <label class="text-muted" for="ADFEmailCC">ADF Emails (CC):</label>
                  <multi-input
                    type="email"
                    validateRules='email'
                    :values='phoneCommunication.notificationSettings.adfEmailAddressesCc'
                    placeholder="<EMAIL>"
                    v-model="phoneCommunication.notificationSettings.adfEmailAddressesCc"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- Tracking & Notification Settings Section End -->
    </b-card>
  </div>
  <div v-else-if='isLoading && !isException' class="mt-5">
    <loader size="lg"/>
  </div>
  <div v-else class="mt-5">
    <h4 class="text-center text-muted">Something went wrong! Please reload page</h4>
  </div>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import CommunicationService from '@/services/leads/CommunicationService'
import { communicationTypes } from '@/shared/leads/common'
import { campaignTypes } from '@/shared/leads/campaignTypes'
import Multiselect from 'vue-multiselect'
import multiInput from '@/components/_shared/multiInput'
import loader from '@/components/_shared/loader'
import phoneInput from '@/components/_shared/phoneInput'

export default {
  name: 'leads-add-edit-phone-campaign',
  props: {
    accountId: { type: Number, required: true },
    communicationId: { type: String }
  },
  data () {
    return {
      campaignTypes,
      hasToShowAlternativeContacts: false,
      isLoading: true,
      isSubmitButtonDisabled: false,
      isException: false,
      phoneCommunication: null,
      legacyCampaignIds: [],
      selectedCampaigns: [],
      availableCampaigns: [],
      availableContacts: [],
      availableDepartmentTypes: [],
      alternativeContacts: [],
      selectedContacts: [],
      selectedAlternativeContacts: [],
      phone: null,
      phoneNumber: ''
    }
  },
  created () {
    if (this.isEditMode) {
      this.populateCommunicationModelById(this.communicationId)
    } else {
      this.populateCommunicationPrototypeModel()
    }
  },
  computed: {
    isPhone () {
      if (this.phoneNumber && this.phoneNumber.toString().length === 14) {
        return true
      }

      return false
    },
    isEditMode () {
      if (this.communicationId) {
        return true
      }

      return false
    },
    getLocationOptions () {
      return Object.values(campaignTypes)
    },
    selectedCampaignsType: {
      get () {
        if (this.phoneCommunication && this.phoneCommunication.campaignsType !== 0) {
          return this.phoneCommunication.campaignsType
        }

        return 1
      },
      set (value) {
        this.phoneCommunication.campaignsType = value
        this.selectedCampaigns = []
        this.populateAvailableCampaigns()
        this.populateAvailableDepartmentTypes()
      }
    }
  },
  components: {
    'detail-row': detailRow,
    'multiselect': Multiselect,
    'multi-input': multiInput,
    'phone-input': phoneInput,
    'loader': loader
  },
  methods: {
    finalize () {
      this.$router.push({name: 'leads-dashboard', params: {accountId: this.accountId}})
    },
    populateCommunicationModelById () {
      CommunicationService.getCommunicationSettings(this.accountId, this.communicationId).then(res => {
        this.availableDepartmentTypes = res.data.availableDepartmentTypes
        this.legacyCampaignIds = res.data.availableLegacyCampaignIds
        this.phoneCommunication = res.data.communication
        this.availableCampaigns = res.data.communication.campaignTypes
        this.selectedCampaigns = res.data.communication.campaignTypes
        this.hasToShowAlternativeContacts = res.data.communication.alternativeContacts && res.data.communication.alternativeContacts.length > 0
        this.setAlternativeContacts(res.data.availableAlternativeContacts)
        this.setAvailableContacts(res.data.availableContacts)
        this.prepareEditModeData()
      }).catch(ex => {
        this.isException = true
        this.$toaster.exception(ex, `Cannot get Communication Details Model`, {timeout: 5000})
        this.$logger.handleError(ex, `Cannot get Communication Details Model with id:${this.communicationId} for accountId:${this.accountId}`)
      }).finally(() => {
        this.isLoading = false
      })
    },
    populateCommunicationPrototypeModel () {
      CommunicationService.getCommunicationPrototype(this.accountId, communicationTypes.voice.value).then(res => {
        this.phoneCommunication = res.data.communication
        this.availableCampaigns = res.data.availableCampaigns
        this.phoneCommunication.importPhoneSettings = {}
      }).catch(ex => {
        this.isException = true
        this.$toaster.error(`Cannot get Communication Details Model`)
        this.$logger.handleError(ex, `Cannot get Communication Details Model with id:${this.communicationId} for accountId:${this.accountId}`)
      }).finally(() => {
        this.isLoading = false
      })
    },
    prepareEditModeData () {
      if (this.phoneCommunication.contacts && this.phoneCommunication.contacts.length > 0) {
        this.selectedContacts = this.phoneCommunication.contacts.map(x => {
          return {
            contactId: x.contactId,
            trackId: `${x.contactId}_${x.selectedPhone.phoneType}`,
            phoneType: x.selectedPhone.phoneType,
            title: x.selectedPhone.title
          }
        })
      }
      if (this.phoneCommunication.alternativeContacts && this.phoneCommunication.alternativeContacts.length > 0) {
        this.selectedAlternativeContacts = this.phoneCommunication.alternativeContacts.map(x => {
          return {
            contactId: x.contactId,
            trackId: `${x.contactId}_${x.selectedPhone.phoneType}`,
            phoneType: x.selectedPhone.phoneType,
            title: x.selectedPhone.title
          }
        })
      }
    },
    setAvailableLegacyCampaignIds () {
      if (!this.isEditMode) {
        let campaigns = this.selectedCampaigns.map(x => x.campaignTypeId).toString()
        if (campaigns && campaigns.length > 0) {
          CommunicationService.getLegacyCampaignIds(campaigns).then(res => {
            this.legacyCampaignIds = res.data
          }).catch(ex => {
            this.legacyCampaignIds = []
            this.$toaster.error('Cannot get available legacy campaign ids')
            this.$logger.handleError(ex, 'Cannot get available legacy campaign ids')
          })
        } else {
          this.legacyCampaignIds = []
          this.phoneCommunication.legacyCampaignId = null
        }
      }
    },
    populateAvailableCampaigns () {
      CommunicationService.getAvailableCampaigns(this.phoneCommunication.campaignsType, null).then(res => {
        this.availableCampaigns = res.data
      }).catch(ex => {
        this.$toaster.error('Cannot get available campaigns')
        this.$logger.handleError(ex, `cannot get available campaigns for campaign type: ${this.phoneCommunication.campaignsType}`)
      })
    },
    populateAvailableDepartmentTypes () {
      if (this.phoneCommunication.campaignsType === this.campaignTypes.offSiteParent.value && !this.isEditMode) {
        let campaigns = this.selectedCampaigns.map(x => x.campaignTypeId).toString()
        if (campaigns && campaigns.length > 0) {
          let filter = {
            communicationType: communicationTypes.voice.value,
            campaignTypeIds: campaigns
          }
          CommunicationService.getAvailableDepartmentTypes(this.accountId, filter).then(res => {
            this.availableDepartmentTypes = res.data
          }).catch(ex => {
            this.availableDepartmentTypes = []
            this.$toaster.error('Cannot get available department types')
            this.$logger.handleError(ex, 'Cannot get available department types')
          })
        } else {
          this.availableDepartmentTypes = []
          this.phoneCommunication.departmentType = null
        }
      }
    },
    populateAvailableContacts () {
      if (!this.isEditMode) {
        let campaigns = this.selectedCampaigns.map(x => x.campaignTypeId).toString()
        if (campaigns && campaigns.length > 0) {
          let filter = {
            communicationType: communicationTypes.voice.value,
            campaignTypeIds: campaigns,
            includeExistingContacts: this.selectedCampaignsType === campaignTypes.offSiteParent.value
          }
          CommunicationService.getAvailableContacts(this.accountId, filter).then(res => {
            this.setAvailableContacts(res.data)
            this.setAlternativeContacts(res.data)
          }).catch(ex => {
            this.availableContacts = []
            this.availableAlternativeContacts = []
            this.$toaster.error('Cannot get available contacts')
            this.$logger.handleError(ex, 'Cannot get available legacy campaign ids')
          })
        } else {
          this.legacyCampaignIds = []
          this.phoneCommunication.legacyCampaignId = null
        }
      }
    },
    setAvailableContacts (newContacts) {
      this.availableContacts = []
      if (newContacts && newContacts.length > 0) {
        newContacts.forEach(contact => {
          this.availableContacts.push({
            groupName: contact.contactNameWithDepartments,
            data: this.getPhoneData(contact)
          })
        })
      }
    },
    setAlternativeContacts (contacts) {
      this.alternativeContacts = []
      if (contacts && contacts.length > 0) {
        contacts.forEach(contact => {
          this.alternativeContacts.push({
            groupName: contact.contactNameWithDepartments,
            data: this.getPhoneData(contact)
          })
        })
      }
    },
    getPhoneData (contact) {
      let data = []
      contact.phones.forEach(phone => {
        data.push({
          trackId: `${contact.contactId}_${phone.phoneType}`,
          contactId: contact.contactId,
          phoneType: phone.phoneType,
          title: phone.title
        })
      })

      return data
    },
    onSubmit () {
      this.isSubmitButtonDisabled = true
      if (this.validate()) {
        if (this.isEditMode) {
          this.updateCommunication()
        } else {
          this.createNewCommunication()
        }
      } else {
        this.isSubmitButtonDisabled = false
      }
    },
    onCheckPhone () {
      this.phone = null
      CommunicationService.getPhoneInfo(this.phoneNumber).then(res => {
        this.phone = res.data
      }).catch(ex => {
        this.$toaster.error(`${ex.response.data}`)
      })
    },
    updateCommunication () {
      this.phoneCommunication.contacts = this.selectedContacts.map(x => { return { contactId: x.contactId, phoneType: x.phoneType } })
      this.phoneCommunication.alternativeContacts = this.selectedAlternativeContacts.map(x => { return { contactId: x.contactId, phoneType: x.phoneType } })
      CommunicationService.updateCommunication(this.accountId, this.phoneCommunication).then(res => {
        this.$toaster.success('Phone Campaign Successfully Updated')
      }).catch(ex => {
        this.$toaster.exception(ex, `Cannot updated Phone Campaign.`, {timeout: 5000})
        this.$logger.handleError(ex, 'Cannot updated Phone Campaign', this.phoneCommunicationModel)
      }).finally(() => {
        this.finalize()
      })
    },
    createNewCommunication () {
      this.phoneCommunication.importPhoneSettings.phoneNumber = this.phoneNumber
      this.phoneCommunication.campaignTypes = this.selectedCampaigns
      this.phoneCommunication.contacts = this.selectedContacts.map(x => { return { contactId: x.contactId, phoneType: x.phoneType } })
      this.phoneCommunication.alternativeContacts = this.selectedAlternativeContacts.map(x => { return { contactId: x.contactId, phoneType: x.phoneType } })
      CommunicationService.createNewCommunication(this.accountId, this.phoneCommunication).then(res => {
        this.$toaster.success('New Phone Campaign Successfully Created')
      }).catch(ex => {
        this.$toaster.exception(ex, `Cannot create Phone Campaign.`, {timeout: 5000})
        this.$logger.handleError(ex, 'Cannot create Phone Campaign', this.phoneCommunicationModel)
      }).finally(() => {
        this.finalize()
      })
    },
    validate () {
      if (this.phoneCommunication.isImportEnabled && !this.isPhone) {
        this.$toaster.error('Phone number should be in format: (xxx) xxx-xxxx')
        return false
      }
      if (this.selectedCampaigns.length === 0) {
        this.$toaster.error('Campaigns field cannot be empty')
        return false
      }
      if (this.selectedContacts.length === 0) {
        this.$toaster.error('Contact (s) field cannot be empty')
        return false
      }
      if (this.hasToShowAlternativeContacts && this.selectedAlternativeContacts.length === 0) {
        this.$toaster.error('Alternate Contact(s) field cannot be empty')
        return false
      }
      if (!this.phoneCommunication.legacyCampaignId || this.phoneCommunication.legacyCampaignId === '') {
        this.$toaster.error('Legacy Campaign Id field cannot be empty')
        return false
      }
      if (this.phoneCommunication.notificationSettings.hasToSendDealerEmail &&
          (!this.phoneCommunication.notificationSettings.dealerEmailAddresses ||
          this.phoneCommunication.notificationSettings.dealerEmailAddresses.length === 0)) {
        this.$toaster.error('Dealership Emails (To) field cannot be empty')
        return false
      }
      if (this.phoneCommunication.notificationSettings.hasToSendAdfEmail &&
          (!this.phoneCommunication.notificationSettings.adfEmailAddresses ||
          this.phoneCommunication.notificationSettings.adfEmailAddresses.length === 0)) {
        this.$toaster.error('ADF Emails (To) field cannot be empty')
        return false
      }
      if (this.phoneCommunication.campaignsType === this.campaignTypes.offSiteParent.value && !this.phoneCommunication.departmentType) {
        this.$toaster.error('Departments: field cannot be empty')
        return false
      }

      return true
    },
    onSelectContact (value) {
      if (this.selectedContacts.length === 0) {
        return
      }
      let res = this.selectedContacts.find(x => x.contactId === value.contactId)
      if (res) {
        this.selectedContacts.splice(this.selectedContacts.indexOf(res), 1)
      }
    },
    onSelectAlternativeContact (value) {
      if (this.selectedAlternativeContacts.length === 0) {
        return
      }
      let res = this.selectedAlternativeContacts.find(x => x.contactId === value.contactId)
      if (res) {
        this.selectedAlternativeContacts.splice(this.selectedAlternativeContacts.indexOf(res), 1)
      }
    }
  },
  watch: {
    'selectedCampaigns': {
      deep: true,
      handler: function () {
        if (!this.phoneCommunication) {
          return
        }
        this.setAvailableLegacyCampaignIds()
        this.populateAvailableContacts()
        this.populateAvailableDepartmentTypes()
      }
    }
  }
}
</script>

<style>
.custom-btn-height {
  height: 35px;
}

.multiselect__option--group {
  font-size: 12px !important;
  padding-top: 12px !important;
  padding-left: 4px !important;
}
</style>
