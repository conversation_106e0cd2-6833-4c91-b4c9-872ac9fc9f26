import moment from 'moment/moment'
import { LogsService } from '@/services/analytics/LogsService'

const googleAnalytics4ApiEndpointsBasePath = '/api/ebizanalytics/ga4/'
const ga4LogsService = new LogsService(googleAnalytics4ApiEndpointsBasePath)
const accountLogsSortTypes = Object.freeze({
  createdAtAsc: 1,
  createdAtDesc: 2,
  processedAtAsc: 3,
  processedAtDesc: 4
})
const groupLogsSortTypes = Object.freeze({
  createdAtAsc: 1,
  createdAtDesc: 2,
  processedAtAsc: 3,
  processedAtDesc: 4
})

export default {
  accountsListSortTypes: Object.freeze({
    accountIdAsc: 1,
    accountIdDesc: 2,
    accountNameAsc: 3,
    accountNameDesc: 4
  }),
  websiteOverviewSortTypes: Object.freeze({
    undefined: 0,
    sessionsAsc: 1,
    sessionsDesc: 2,
    usersAsc: 3,
    usersDesc: 4,
    pageViewsAsc: 5,
    pageViewsDesc: 6,
    uniquePageViewvsAsc: 7,
    uniquePageViewvsDesc: 8,
    fromLeadsAsc: 9,
    fromLeadsDesc: 10,
    phoneLeadsAsc: 11,
    phoneLeadsDesc: 12,
    totalLeadsAsc: 13,
    totalLeadsDesc: 14,
    convRatePerSessionAsc: 15,
    convRatePerSessionDesc: 16,
    convRatePerUserAsc: 17,
    convRatePerUserDesc: 18,
    dateAsc: 19,
    dateDesc: 20,
    smsLeadsAsc: 21,
    smsLeadsDesc: 22,
    accountNameAsc: 23,
    accountNameDesc: 24
  }),
  websiteEngagementSortTypes: Object.freeze({
    undefined: 0,
    pageViewsPerSessionAsc: 1,
    pageViewsPerSessionDesc: 2,
    avgTimeOnSiteAsc: 3,
    avgTimeOnSiteDesc: 4,
    bounceRateAsc: 5,
    bounceRateDesc: 6,
    webActionsAsc: 7,
    webActionsDesc: 8,
    dateAsc: 9,
    dateDesc: 10,
    accountNameAsc: 11,
    accountNameDesc: 12
  }),
  vehicleSortType: Object.freeze({
    undefined: 0,
    pageViewsAsc: 1,
    pageViewsDesc: 2,
    avgTimeOnPageAsc: 3,
    avgTimeOnPageDesc: 4,
    totalTimeOnPageAsc: 5,
    totalTimeOnPageDesc: 6,
    webActionsAsc: 7,
    webActionsDesc: 8,
    fromLeadsAsc: 9,
    fromLeadsDesc: 10,
    pagePathAsc: 11,
    pagePathDesc: 12,
    dateAsc: 13,
    dateDesc: 14,
    naturalAsc: 15,
    naturalDesc: 16,
    accountNameAsc: 17,
    accountNameDesc: 18
  }),
  channelSegmentSortTypes: Object.freeze({
    undefined: 0,
    sessionsAsc: 1,
    sessionsDesc: 2,
    usersAsc: 3,
    usersDesc: 4,
    percentNewSessionsAsc: 5,
    percentNewSessionsDesc: 6,
    pageViewsAsc: 7,
    pageViewsDesc: 8,
    pageViewsPerSessionAsc: 9,
    pageViewsPerSessionDesc: 10,
    avgTimeOnSiteAsc: 11,
    avgTimeOnSiteDesc: 12,
    totalTimeOnSiteAsc: 13,
    totalTimeOnSiteDesc: 14,
    webActionsAsc: 15,
    webActionsDesc: 16,
    fromLeadsAsc: 17,
    fromLeadsDesc: 18,
    phoneLeadsAsc: 19,
    phoneLeadsDesc: 20,
    totalLeadsAsc: 21,
    totalLeadsDesc: 22,
    dateAsc: 23,
    dateDesc: 24,
    channelGroupingAsc: 25,
    channelGroupingDesc: 26,
    smsLeadsAsc: 27,
    smsLeadsDesc: 28,
    accountNameAsc: 29,
    accountNameDesc: 30
  }),
  trafficSourceSortTypes: Object.freeze({
    undefined: 0,
    sessionsAsc: 1,
    sessionsDesc: 2,
    usersAsc: 3,
    usersDesc: 4,
    percentNewSessionsAsc: 5,
    percentNewSessionsDesc: 6,
    pageViewsAsc: 7,
    pageViewsDesc: 8,
    pageViewsPerSessionAsc: 9,
    pageViewsPerSessionDesc: 10,
    avgTimeOnSiteAsc: 11,
    avgTimeOnSiteDesc: 12,
    totalTimeOnSiteAsc: 13,
    totalTimeOnSiteDesc: 14,
    webActionsAsc: 15,
    webActionsDesc: 16,
    fromLeadsAsc: 17,
    fromLeadsDesc: 18,
    phoneLeadsAsc: 19,
    phoneLeadsDesc: 20,
    totalLeadsAsc: 21,
    totalLeadsDesc: 22,
    dateAsc: 23,
    dateDesc: 24,
    channelGroupingAsc: 25,
    channelGroupingDesc: 26,
    sourceAsc: 27,
    sourceDesc: 28,
    smsLeadsAsc: 29,
    smsLeadsDesc: 30,
    accountNameAsc: 31,
    accountNameDesc: 32
  }),
  paidSearchSortTypes: Object.freeze({
    undefined: 0,
    spendAsc: 1,
    spendDesc: 2,
    impressionsAsc: 3,
    impressionsDesc: 4,
    clicksAsc: 5,
    clicksDesc: 6,
    CPCAsc: 7,
    CPCDesc: 8,
    CTRAsc: 9,
    CTRDesc: 10,
    sessionsAsc: 11,
    sessionsDesc: 12,
    totalEventsAsc: 13,
    totalEventsDesc: 14,
    phoneLeadsAsc: 15,
    phoneLeadsDecs: 16,
    emailLeadsAsc: 17,
    emailLeadsDesc: 18,
    totalLeadsAsc: 19,
    totalLeadsDesc: 20,
    convRateAsc: 21,
    convRateDesc: 22,
    CPLAsc: 23,
    CPLDesc: 24,
    mgmtFeeAsc: 25,
    mgmtFeeDesc: 26,
    budgetAsc: 27,
    budgetDesc: 28,
    dateAsc: 29,
    dateDesc: 30,
    smsLeadsAsc: 31,
    smsLeadsDecs: 32,
    accountNameAsc: 33,
    accountNameDesc: 34
  }),
  displaySortTypes: Object.freeze({
    undefined: 0,
    spendAsc: 1,
    spendDesc: 2,
    impressionsAsc: 3,
    impressionsDesc: 4,
    clicksAsc: 5,
    clicksDesc: 6,
    CPCAsc: 7,
    CPCDesc: 8,
    CTRAsc: 9,
    CTRDesc: 10,
    sessionsAsc: 11,
    sessionsDesc: 12,
    totalEventsAsc: 13,
    totalEventsDesc: 14,
    phoneLeadsAsc: 15,
    phoneLeadsDecs: 16,
    emailLeadsAsc: 17,
    emailLeadsDesc: 18,
    totalLeadsAsc: 19,
    totalLeadsDesc: 20,
    convRateAsc: 21,
    convRateDesc: 22,
    CPLAsc: 23,
    CPLDesc: 24,
    mgmtFeeAsc: 25,
    mgmtFeeDesc: 26,
    budgetAsc: 27,
    budgetDesc: 28,
    dateAsc: 29,
    dateDesc: 30,
    smsLeadsAsc: 31,
    smsLeadsDecs: 32,
    accountNameAsc: 33,
    accountNameDesc: 34
  }),
  remarketingSortTypes: Object.freeze({
    undefined: 0,
    spendAsc: 1,
    spendDesc: 2,
    impressionsAsc: 3,
    impressionsDesc: 4,
    clicksAsc: 5,
    clicksDesc: 6,
    CPCAsc: 7,
    CPCDesc: 8,
    CTRAsc: 9,
    CTRDesc: 10,
    sessionsAsc: 11,
    sessionsDesc: 12,
    totalEventsAsc: 13,
    totalEventsDesc: 14,
    phoneLeadsAsc: 15,
    phoneLeadsDecs: 16,
    emailLeadsAsc: 17,
    emailLeadsDesc: 18,
    totalLeadsAsc: 19,
    totalLeadsDesc: 20,
    convRateAsc: 21,
    convRateDesc: 22,
    CPLAsc: 23,
    CPLDesc: 24,
    mgmtFeeAsc: 25,
    mgmtFeeDesc: 26,
    budgetAsc: 27,
    budgetDesc: 28,
    dateAsc: 29,
    dateDesc: 30,
    smsLeadsAsc: 31,
    smsLeadsDecs: 32,
    accountNameAsc: 33,
    accountNameDesc: 34
  }),
  userLogSortTypes: Object.freeze({
    undefined: 0,
    userNameAsc: 1,
    userNameDesc: 2,
    dateAsc: 3,
    dateDesc: 4,
    accountIdAsc: 5,
    accountIdDesc: 6,
    accountNameAsc: 7,
    accountNameDesc: 8,
    groupNameAsc: 9,
    groupNameDesc: 10,
    actionAsc: 11,
    actionDesc: 12,
    userTypeAsc: 13,
    userTypeDesc: 14
  }),
  brandExportLogsSortTypes: Object.freeze({
    undefined: 0,
    exportNameAsc: 1,
    exportNameDesc: 2,
    processingDateTimeAsc: 3,
    processingDateTimeDesc: 4,
    reportDateFromAsc: 5,
    reportDateFromDesc: 6,
    reportDateToAsc: 7,
    reportDateToDesc: 8
  }),
  reportTypes: [
    { value: 0, text: 'Undefined' },
    { value: 1, text: 'Website Activity' },
    { value: 2, text: 'Digital Advertising' },
    { value: 3, text: 'Dealer File' },
    { value: 4, text: 'Monthly Dealer Budget' },
    { value: 5, text: 'Monthly Digital Advertising' }
  ],
  brandTypes: [
    { value: 0, text: 'Undefined' },
    { value: 1, text: 'VW' },
    { value: 2, text: 'BMW' },
    { value: 3, text: 'MINI' },
    { value: 4, text: 'Porsche' }
  ],
  exportTaskTypes: [
    { value: 0, text: 'Waiting' },
    { value: 1, text: 'In Queue' },
    { value: 2, text: 'In Process' },
    { value: 3, text: 'Completed' },
    { value: 4, text: 'Error' },
    { value: 5, text: 'Killed' },
    { value: 6, text: 'FtpError' }
  ],
  brandExportTaskStatusContextTypes: [
    { value: 0, text: 'Undefined' },
    { value: 1, text: 'Waiting' },
    { value: 2, text: 'In Queue' },
    { value: 3, text: 'In Process' },
    { value: 4, text: 'Completed' },
    { value: 5, text: 'Error' },
    { value: 6, text: 'Killed' },
    { value: 7, text: 'FtpError' }
  ],
  ftpTypes: Object.freeze({
    ftp: { value: 0, text: 'FTP' },
    ftps: { value: 1, text: 'FTPS' },
    sftp: { value: 2, text: 'SFTP' }
  }),
  reportRangeTypes: Object.freeze({
    undefined: 0,
    day: 1,
    month: 2
  }),
  rangeTypes: Object.freeze({
    undefined: 0,
    day: 1,
    month: 2,
    quarter: 3,
    year: 4,
    yearToDate: 5
  }),
  optionsQuarter: [
    { value: 0, text: 'Not Selected' },
    { value: 1, text: 'Quarter #1 (Jan, Feb, Mar)' },
    { value: 2, text: 'Quarter #2 (Apr, May, Jun)' },
    { value: 3, text: 'Quarter #3 (Jul, Aug, Sep)' },
    { value: 4, text: 'Quarter #4 (Oct, Nov, Dec)' }
  ],
  optionsMonth: [
    { value: 0, text: 'Not Selected' },
    { value: 1, text: 'January' },
    { value: 2, text: 'February' },
    { value: 3, text: 'March' },
    { value: 4, text: 'April' },
    { value: 5, text: 'May' },
    { value: 6, text: 'June' },
    { value: 7, text: 'July' },
    { value: 8, text: 'August' },
    { value: 9, text: 'September' },
    { value: 10, text: 'October' },
    { value: 11, text: 'November' },
    { value: 12, text: 'December' }
  ],
  beginOfTime: moment().subtract(5, 'year'),
  userActivityActions: Object.freeze({
    undefined: {value: 0, text: 'All Actions'},
    insertAnalyticsReportGroup: {value: 17, text: 'Insert Apps Analytics Report Group'},
    insertAdsAccountSettings: {value: 13, text: 'Insert Google Ads Account Settings'},
    insertAdsCampaignUrlSettings: {value: 15, text: 'Insert Google Ads Campaign URL Settings'},
    updateAnalyticsAccountSettings: {value: 6, text: 'Update Google Analytics Account Settings'},
    updateAdsAccountSettings: {value: 7, text: 'Update Google Ads Account Settings'},
    updateAdsCampaignUrlSettings: {value: 8, text: 'Update Google Ads Campaign URL Settings'},
    updateAnalyticsReportGroup: {value: 9, text: 'Update Apps Analytics Report Group'},
    updateBrandExportServiceSettings: {value: 1, text: 'Update Brand Export Service Settings'},
    updateBrandExportReportSettings: {value: 19, text: 'Update Brand Export Report Settings'},
    updateBrandExportSettings: {value: 20, text: 'Update Brand Export Settings'},
    deleteAdsAccountSettings: {value: 14, text: 'Delete Google Ads Account Settings'},
    deleteAdsCampaignUrlSettings: {value: 16, text: 'Delete Google Ads Campaign URL Settings'},
    deleteAnalyticsReportGroup: {value: 18, text: 'Delete Apps Analytics Report Group'},
    rebuildAnalyticsStatistic: {value: 3, text: 'Rebuild Apps Analytics Statistic'},
    rebuildAnalyticsReportGroupStatistic: {value: 10, text: 'Rebuild Apps Analytics Report Group Statistic'},
    rebuildAdsStatistic: {value: 5, text: 'Rebuild Google Ads Statistic'},
    rebuildBrandExportReport: {value: 2, text: 'Rebuild Brand Export Report'},
    retrieveGoogleRefreshTokenForAnalytics: {value: 11, text: 'Retrieve Google Analytics Dealer Account Refresh Token'},
    revokeGoogleRefreshTokenForAnalytics: {value: 12, text: 'Revoke Google Analytics Dealer Account Refresh Token'},
    retrieveOAuth2RefreshToken: {value: 21, text: 'Retrieve OAuth2 Refresh Token'},
    revokeOAuth2RefreshToken: {value: 22, text: 'Revoke OAuth2 Refresh Token'}
  }),
  analyticsAuthMechanismTypes: Object.freeze({
    none: {value: 0, text: 'No Analytics'},
    shapedStrata: {value: 1, text: 'UA - Google Analytics (shaped-strata-607)'},
    freshBloom: {value: 2, text: 'GA4 - Google Analytics (fresh-bloom-366121)'}
  }),
  analyticsAccountSetupTypes: Object.freeze({
    none: {value: 0, text: 'None'},
    googleAnalyticsOnly: {value: 1, text: 'Google Analytics Only'},
    googleAdsOnly: {value: 2, text: 'Google Ads Only'},
    completely: {value: 3, text: 'Both Google Analytics & Ads'}
  }),
  googleAnalytics4ApiEndpointsIdentifier: '/ga4/',
  googleAnalytics4ApiEndpointsBasePath: googleAnalytics4ApiEndpointsBasePath,
  googleAnalyticsDefaultApiEndpointsBasePath: '/api/ebizanalytics/',
  analyticsLogsTabTypes: Object.freeze({
    tabTypes: [
      {
        index: 0,
        value: 'account',
        label: 'Account',
        defaultSort: accountLogsSortTypes.createdAtDesc,
        async populateListing (filters) {
          if (!Object.values(accountLogsSortTypes).includes(filters.sort)) {
            filters.sort = accountLogsSortTypes.createdAtDesc
          }

          return ga4LogsService.getAccountLogs(filters)
        },
        async deleteTask (id) {
          return ga4LogsService.deleteAccountTask(id)
        }
      },
      {
        index: 1,
        value: 'group',
        label: 'Group',
        defaultSort: groupLogsSortTypes.createdAtDesc,
        async populateListing (filters) {
          if (!Object.values(groupLogsSortTypes).includes(filters.sort)) {
            filters.sort = groupLogsSortTypes.createdAtDesc
          }

          return ga4LogsService.getGroupLogs(filters)
        },
        async deleteTask (id) {
          return ga4LogsService.deleteGroupTask(id)
        }
      }
    ],
    getTab (tabIndex) {
      return this.tabTypes.find(x => x.index === tabIndex)
    },
    getDefaultTabType () {
      return this.tabTypes[0]
    }
  }),
  accountLogsSortTypes: accountLogsSortTypes,
  groupLogsSortTypes: groupLogsSortTypes,
  googleAnalyticsServiceId: 1
}
