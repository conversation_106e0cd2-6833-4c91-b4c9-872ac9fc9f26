<template>
  <div class="card-component">
    <section class="card-component__header">
      <slot name="header">
      </slot>
    </section>
    <section class="card-component__body">
      <slot name="body">
      </slot>
    </section>
    <section class="card-component__footer" v-if="hasFooter">
      <slot name="footer">
      </slot>
    </section>
  </div>
</template>p0

<script>
export default {
  name: 'card',
  computed: {
    hasFooter () {
      return !!this.$slots.footer
    }
  }
}
</script>

<style scoped>
  .card-component__header {
    border: 1px solid #eee;
  }

  .card-component__body {
    border: 1px solid #eee;
    padding: 10px;
    font-size: 0.8rem;
    margin-top: -1px;
  }

  .card-component__footer {
    border: 1px solid #eee;
    padding: 4px 10px;
    margin-top: -1px;
    text-align: right;
    font-size: 0.8rem;
  }
</style>
