﻿using EBizAutos.CommonLib.Encryption;
using System;
using System.Xml.Serialization;
using static EBizAutos.Apps.CommonLib.Enums.TwilioMessagingEnums;

namespace EBizAutos.Apps.CommonLib.Models.TwilioSms {
	[Serializable]
	public class TwilioMessage {
		[XmlIgnore]
		public string MessageSid { get; set; }

		[Encrypt]
		[XmlElement("messagefrom")]
		public string MessageFrom { get; set; }

		[Encrypt]
		[XmlElement("messageto")]
		public string MessageTo { get; set; }

		[Encrypt]
		[XmlElement("body")]
		public string Body { get; set; }

		[XmlIgnore]
		public DateTime DateCreated { get; set; }

		[XmlIgnore]
		public MessageStatusEnum MessageStatus { get; set; }

		[XmlIgnore]
		public TwilioErrorCodeEnum ErrorCode { get; set; }

		public string GetTwilioErrorDescription() {
			switch (ErrorCode) {
				case TwilioErrorCodeEnum.FromNumberNotAMobileNumber:
					return $"Can't send a message. '{MessageFrom}' is not a mobile number.";
				case TwilioErrorCodeEnum.ToNumberNotAMobileNumber:
					return $"Can't send a message. '{MessageTo}' is not a mobile number.";
				default:
					return null;
			}
		}
	}
}