const siteSortTypes = Object.freeze({
  siteIdAsc: 1,
  siteIdDesc: 2,
  siteHostAsc: 3,
  siteHostDesc: 4
})

const siteLeadSettingSortTypes = Object.freeze({
  siteIdAsc: 1,
  siteIdDesc: 2,
  dealershipNameAsc: 3,
  dealershipNameDesc: 4,
  dealerLeadHostAsc: 5,
  dealerLeadHostDesc: 6
})

const siteLeadHostTypes = Object.freeze({
  all: { value: 0, text: 'All' },
  secureLeadHost: { value: 1, text: 'Secure Lead Host' },
  dealerSecureSubfolder: { value: 2, text: 'Dealership Secure Subfolder' }
})

export {
  siteSortTypes,
  siteLeadHostTypes,
  siteLeadSettingSortTypes
}
