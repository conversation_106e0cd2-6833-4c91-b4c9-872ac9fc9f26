<template>
  <ValidationProvider name="Phone" v-slot="{errors}" rules="phone">
    <b-form-input
      :size="size"
      v-model="phoneValue"
      type="text"

      :state='errors[0] ? false : null'
      aria-describedby="input-live-feedback"
      name='CustomPhone'

      :placeholder="placeholder"
      @keydown.native="handleKeyDown"
      :disabled='disabled'>
    </b-form-input>

    <b-form-invalid-feedback id="input-live-feedback">
      {{errors[0]}}
    </b-form-invalid-feedback>
  </ValidationProvider>
</template>

<script>
export default {
  name: 'phone-input',
  props: {
    placeholder: String,
    size: String,
    disabled: { type: Boolean, default: false },
    value: { type: String, default: '' },
    customvalidate: { type: Boolean, default: false },
    format: { type: String, default: '($1) $2-$3' }
  },
  computed: {
    phoneValue: {
      get () {
        if (!this.value) {
          return ''
        }
        return `${this.value.toString().replace(/(\d{3})(\d{3})(\d{4})/, this.format)}` // ********** -> (*************
      },
      set (value) {
        this.$emit('input', value)
      }
    }
  },
  methods: {
    handleKeyDown (e) {
      // key.length = 1 mean that it's a letter not a control button
      if (e.key && e.key.length === 1 && !Number.isInteger(+e.key) && !(e.key === '(') && !(e.key === ')') && !(e.key === '-')) { //
        e.preventDefault()
      }
    }
  }
}
</script>
