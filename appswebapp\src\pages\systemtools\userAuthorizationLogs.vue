<template>
  <div>
    <b-row>
      <b-col>
        <h4>User Authorizations Logs</h4>
      </b-col>
      <b-col>
        <b-dropdown class="float-right" variant="primary" size="sm" >
          <template slot="button-content">
            <span class="ion ion-ios-settings"></span> Configuration
          </template>
          <b-dropdown-item @click="showWhiteUserIpAddressManager">
            Whitelisted IPs
          </b-dropdown-item>
          <b-dropdown-item @click="showUserAuthWhitelistRuleManager">
            Whitelist Rules
          </b-dropdown-item>
        </b-dropdown>
      </b-col>
    </b-row>
    <b-tabs class="nav-tabs-top nav-responsive-sm" v-model="currentTab" no-fade>
      <b-tab class="p-3" :title="tabs.logs.title">
        <b-form v-on:submit.prevent="applyFilter">
          <div class="form-row">
            <b-col class="my-1" xs="12" sm="12" lg="4" xl="2">
              <b-form-input
                max='200'
                v-model="filter.search"
                placeholder="Search..."
                autocomplete="off"
              >
              </b-form-input>
            </b-col>

            <b-col class="my-1" xs="12" sm="6" lg="4" xl="2">
              <b-input-group class="flex-nowrap">
                <b-input-group-prepend is-text>
                  <i class="ion ion-md-calendar" slot="prepend"></i>
                </b-input-group-prepend>
                <date-time-picker
                  ref="timeFrom"
                  v-model="filter.dateFrom"
                  :options="filterTimeOptions"
                  format="MM/DD/YYYY HH:mm"
                  placeholder="Date From"
                  className="form-control"
                  @change="onTimeFromInputChange"
                />
                <b-input-group-append
                  is-text
                  v-show="filter.dateFrom"
                  @click="filter.dateFrom = null"
                >
                  <i class="ion ion-md-close"></i>
                </b-input-group-append>
              </b-input-group>
            </b-col>

            <b-col class="my-1" xs="12" sm="6" lg="4" xl="2">
              <b-input-group class="flex-nowrap">
                <b-input-group-prepend is-text>
                  <i class="ion ion-md-calendar" slot="prepend"></i>
                </b-input-group-prepend>
                <date-time-picker
                  ref="timeTo"
                  v-model="filter.dateTo"
                  :options="filterTimeOptions"
                  format="MM/DD/YYYY HH:mm"
                  placeholder="Date To"
                  className="form-control"
                  @change="onTimeToInputChange"
                />
                <b-input-group-append
                  is-text
                  v-show="filter.dateTo"
                  @click="filter.dateTo = null"
                >
                  <i class="ion ion-md-close"></i>
                </b-input-group-append>
              </b-input-group>
            </b-col>

            <b-col class="my-1" xs="12" sm="4" lg="4" xl="2">
              <b-form-select v-model="filter.status" :options="getStatusOptions">
              </b-form-select>
            </b-col>

            <b-col class="my-1" xs="12" sm="4" lg="4" xl="2">
              <b-form-select v-model="filter.userType" :options="getUserTypeOptions">
              </b-form-select>
            </b-col>

            <b-col class="my-1" xs="12" sm="4" lg="4" xl="2">
              <b-btn block  variant="primary" type="submit">Submit</b-btn>
            </b-col>
          </div>
        </b-form>
      </b-tab>
      <b-tab :title="tabs.report.title">
        <user-auth-log-report v-if="filter.tab === tabs.report.key"/>
      </b-tab>
    </b-tabs>

    <b-card v-if="filter.tab === tabs.logs.key && !isLoading">
      <b-table
        :items="items"
        :fields="tableFields"
        :sort-by="tableSortBy"
        :sort-desc="tableSortDesc"
        @sort-changed="onSortChanged"
        :no-sort-reset="true"
        :no-local-sorting="true"
        striped
        show-empty
        hover
        responsive>
        <template #cell(user)="data">
          {{ data.item.firstName }} {{ data.item.lastName }} ({{ data.item.userName }})
        </template>
        <template #cell(account)="data">
          {{ getAccountTitle(data.item) }}
        </template>
        <template #cell(manage)="data">
          <b-btn size="sm" @click="showDetails(data)">{{ data.detailsShowing ? 'Hide' : 'Show' }} Details</b-btn>
          <a class="btn btn-outline-secondary btn-sm" :href='getLogDetailsPath(data.item.id)' target="_blank">
            <i class="ion ion-md-open"></i>
          </a>
        </template>
        <template #row-details="data">
          <b-card>
            <log-node
              :data="data.item.nodes"
              :isExpandedShallow="true"
              :isExpandedDeep="false"
            />
          </b-card>
        </template>
      </b-table>
      <paging
          :pageNumber="filter.page"
          :pageSize="filter.pageSize"
          :totalItems="itemsTotalCount"
          titled
          pageSizeSelector
          @numberChanged="pageChanged"
          @changePageSize="changePageSize"
        />
    </b-card>
    <div v-else-if="filter.tab === tabs.logs.key">
      <loader size="lg"/>
    </div>
    <white-listed-ip-manager ref="whiteListedIpManager" :isVisible="isWhiteListedIpManagerVisible" @hide="hideWhiteListedIpManager"/>
    <user-auth-whitelist-rule-manager ref="userAuthWhitelistRuleManager" :isVisible="isUserAuthWhitelistRuleManagerVisible" @hide="hideUserAuthWhitelistRuleManager"/>
  </div>
</template>

<script>
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import {userTypes} from '@/shared/users/constants'
import Constants from '@/shared/systemTools/constants'
import moment from 'moment'
import SystemToolsService from '@/services/systemTools/SystemToolsService'

const tabs = Object.freeze({
  logs: {key: 'logs', title: 'Logs'},
  report: {key: 'report', title: 'Report'}
})

const defaultFilters = new ObjectSchema({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  search: { type: String, default: '' },
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  sort: { type: Number, default: Constants.userAuthLogSortTypes.dateDesc },
  userType: { type: Number, default: 0 },
  status: { type: Number, default: 0 },
  tab: { type: String, default: tabs.logs.key }
})
const queryHelper = new QueryStringHelper(defaultFilters)

export default {
  name: 'system-tools-user-auth-logs',
  metaInfo: {
    title: 'User Authorization Logs'
  },
  components: {
    'paging': () => import('@/components/_shared/paging'),
    'date-time-picker': () => import('@gravitano/vue-date-range-picker/src/components/DateRangePicker'),
    'log-node': () => import('@/components/_shared/logItemNode.vue'),
    'loader': () => import('@/components/_shared/loader.vue'),
    'white-listed-ip-manager': () => import('@/components/systemTools/whiteListedIpAddressManager'),
    'user-auth-whitelist-rule-manager': () => import('@/components/systemTools/userAuthWhitelistRuleManager'),
    'user-auth-log-report': () => import('@/components/systemTools/userAuthLogReport')
  },
  data () {
    return {
      tabs,
      items: [],
      itemsTotalCount: 0,
      isLoading: true,
      filter: defaultFilters.getObject(),
      filterTimeOptions: {
        autoUpdateInput: false,
        singleDatePicker: true,
        timePicker: true,
        timePicker24Hour: true,
        maxDate: new Date()
      },
      isWhiteListedIpManagerVisible: false,
      isUserAuthWhitelistRuleManagerVisible: false
    }
  },
  created () {
    this.filter = queryHelper.parseQueryStringToObject(this.$router)
    this.loadContent()
  },
  computed: {
    currentTab: {
      get () {
        let index = Object.values(tabs).findIndex(x => x.key === this.filter.tab)
        if (index < 0) {
          return 0
        }
        return index
      },
      set (value) {
        let tab = Object.values(tabs)[value]
        this.filter.tab = tab.key
        this.synchronizeUrlAndReload()
      }
    },
    getUserTypeOptions () {
      return [{ value: 0, text: 'All User Types' }, userTypes.cpUser, userTypes.adminUser]
    },
    getStatusOptions () {
      return Object.values(Constants.userAuthStatus).map(x => {
        if (x.value === 0) {
          return {value: 0, text: 'All Statuses'}
        }
        return x
      })
    },
    tableFields () {
      return [
        {
          key: 'ipAddress',
          sortable: true,
          sortTypeAsc: Constants.userAuthLogSortTypes.ipAddressAsc,
          sortTypeDesc: Constants.userAuthLogSortTypes.ipAddressDesc,
          label: 'IP Address',
          tdClass: 'py-2 align-middle',
          thStyle: 'min-width: 125px;'
        },
        {
          key: 'user',
          sortable: true,
          sortTypeAsc: Constants.userAuthLogSortTypes.userNameAsc,
          sortTypeDesc: Constants.userAuthLogSortTypes.userNameDesc,
          label: 'User',
          tdClass: 'py-2 align-middle',
          thStyle: 'min-width: 200px;'
        },
        {
          key: 'account',
          sortable: true,
          sortTypeAsc: Constants.userAuthLogSortTypes.accountNameAsc,
          sortTypeDesc: Constants.userAuthLogSortTypes.accountNameDesc,
          label: 'Account',
          tdClass: 'py-2 align-middle',
          thStyle: 'min-width: 200px;'
        },
        {
          key: 'applicationName',
          sortable: true,
          sortTypeAsc: Constants.userAuthLogSortTypes.applicationAsc,
          sortTypeDesc: Constants.userAuthLogSortTypes.applicationDesc,
          label: 'Application',
          tdClass: 'py-2 align-middle',
          thStyle: 'min-width: 125px;'
        },
        {
          key: 'status',
          sortable: true,
          sortTypeAsc: Constants.userAuthLogSortTypes.statusAsc,
          sortTypeDesc: Constants.userAuthLogSortTypes.statusDesc,
          label: 'Status',
          tdClass: 'py-2 align-middle',
          formatter: val => (Object.values(Constants.userAuthStatus).find(x => x.value === val) || {text: '-'}).text
        },
        {
          key: 'startProcessingDateTime',
          sortable: true,
          sortTypeAsc: Constants.userAuthLogSortTypes.dateAsc,
          sortTypeDesc: Constants.userAuthLogSortTypes.dateDesc,
          label: 'Date',
          tdClass: 'py-2 align-middle',
          thStyle: 'width: 200px;',
          formatter: value => {
            return moment(value).format('MM/DD/YYYY hh:mm:ss A')
          }
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle',
          thStyle: 'width: 175px;'
        }
      ]
    },
    refDateTimeFrom () {
      return (this.$refs.timeFrom || {}).$el || {}
    },
    refDateTimeTo () {
      return (this.$refs.timeTo || {}).$el || {}
    },
    sortType () {
      return this.filter.sort
    },
    tableSortBy () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sortType
      } else {
        return false
      }
    }
  },
  methods: {
    loadContent () {
      SystemToolsService.getUserAuthorizationLogs(this.filter).then(res => {
        this.items = res.data.items
        this.totalItemsCount = res.data.total
      }).catch(ex => {
        this.$toaster.error('Something went wrong!', {timeout: 5000})
        this.$logger.handleError(ex, 'Can\'t get user auth logs', {filter: this.filter})
      }).finally(() => {
        this.isLoading = false
      })
    },
    showDetails (data) {
      if (data.item.nodes) {
        data.toggleDetails()
      } else {
        SystemToolsService.getUserAuthorizationLogDetails(data.item.id).then(res => {
          data.item.nodes = { nodes: res.data.details }
          data.toggleDetails()
        }).catch(ex => {
          this.$toaster.error('Something went wrong!')
          this.$logger.handleError(ex, 'Cannot get user auth log details')
        })
      }
    },
    pageChanged (newPage) {
      this.filter.page = newPage
      this.isLoading = true
      this.synchronizeUrlAndReload()
    },
    changePageSize (newSize) {
      this.filter.pageSize = newSize
      this.filter.page = 1
      this.isLoading = true
      this.synchronizeUrlAndReload()
    },
    onSortChanged (value) {
      const sortingColumn = this.tableFields.find(x => x.key === value.sortBy)
      if (value.sortDesc) {
        this.filter.sort = sortingColumn.sortTypeDesc
      } else {
        this.filter.sort = sortingColumn.sortTypeAsc
      }
      this.synchronizeUrlAndReload()
    },
    onTimeFromInputChange (newVal) {
      this.refDateTimeFrom.value = newVal || this.filter.dateFrom || null
    },
    onTimeToInputChange (newVal) {
      this.refDateTimeTo.value = newVal || this.filter.dateTo || null
    },
    applyFilter () {
      this.filter.search = (this.filter.search || '').trim()
      this.filter.page = 1
      this.isLoading = true
      this.synchronizeUrlAndReload()
    },
    synchronizeUrlAndReload () {
      queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.loadContent()
    },
    getAccountTitle (item) {
      if (item.accountName) {
        return `${item.accountName} (${item.accountId})`
      }
      if (item.accountId > 0) {
        return item.accountId
      }
      return '-'
    },
    hideWhiteListedIpManager () {
      this.isWhiteListedIpManagerVisible = false
    },
    showWhiteUserIpAddressManager () {
      this.isWhiteListedIpManagerVisible = true
      this.$refs.whiteListedIpManager.populateData()
    },
    hideUserAuthWhitelistRuleManager () {
      this.isUserAuthWhitelistRuleManagerVisible = false
    },
    showUserAuthWhitelistRuleManager () {
      this.isUserAuthWhitelistRuleManagerVisible = true
      this.$refs.userAuthWhitelistRuleManager.populateData()
    },
    getLogDetailsPath (id) {
      return `/system/userauthlogs/${id}/details`
    }
  }
}
</script>
