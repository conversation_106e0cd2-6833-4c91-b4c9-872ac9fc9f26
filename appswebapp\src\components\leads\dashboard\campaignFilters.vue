<template>
<div class="mt-2">
    <div>
      <b-row >
        <b-col>
          <b-dropdown variant="primary btn-round" class="float-right add-tracking-campaign-b-dropdown" size="sm">
            <template slot="button-content">
              <span class="ion ion-ios-add"></span><span class="d-none d-md-inline">&nbsp; Add Tracking Campaign</span>
            </template>
            <b-dropdown-item :to='{ path: "campaign/webform" }'>Add Web Form Campaign</b-dropdown-item>
            <div v-if="hasLeadsManageCommunications">
              <b-dropdown-item :to='{ path: "campaign/sms" }'>Add SMS Campaign</b-dropdown-item>
              <b-dropdown-item :to='{ path: "campaign/phone" }'>Add Phone Campaign</b-dropdown-item>
              <b-dropdown-item :to='{ path: "campaign/emailproxy" }'>Add Email Proxy Campaign</b-dropdown-item>
            </div>
          </b-dropdown>
        </b-col>
      </b-row>
    </div>
  <b-tabs class="nav-tabs-top nav-responsive-sm" v-model="selectedTab" no-fade>
    <b-tab v-for='tab in tabOptions' :key='tab.key' :title='tab.label'>
      <campaign-listing @refresh="refresh" :type='tab.key' :items='getItems(tab.key)' />
    </b-tab>
  </b-tabs>
</div>
</template>

<script>
import permission from '@/shared/common/permissions'
import campaignListing from './campaignListing'
import { campaignTabTypes, getDefaultTabType } from '@/shared/leads/campaign'
import { mapGetters } from 'vuex'

export default {
  name: 'leads-campaign-filters',
  props: {
    model: { type: Object, required: true }
  },
  data () {
    return {
      selectedTab: getDefaultTabType(this.model).key
    }
  },
  components: {
    'campaign-listing': campaignListing
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {
        canManageAccountApplicationType: () => false
      }
    },
    tabOptions () {
      let options = []
      Object.values(campaignTabTypes).map(x => {
        if (x.isEnabled(this.model)) {
          options.push(x)
        }
      })

      return options
    },
    hasLeadsManageCommunications () {
      if (this.user && this.user.hasPermissions && this.user.hasPermissions(permission.LeadsManageCommunications)) {
        return true
      }

      return false
    }
  },
  methods: {
    getItems (tab) {
      switch (tab) {
        case campaignTabTypes.tracking.key:
          let items = []
          if (this.model.phoneCommunicationSummaries) {
            Array.prototype.push.apply(items, this.model.phoneCommunicationSummaries)
          }
          if (this.model.emailCommunicationSummaries) {
            Array.prototype.push.apply(items, this.model.emailCommunicationSummaries)
          }
          return items
        case campaignTabTypes.webForm.key:
          return this.model.webFormCommunicationSummaries
        case campaignTabTypes.ebayLeads.key:
          return this.model.eBayCommunicationSummaries
        default:
          return []
      }
    },
    refresh () {
      this.$emit('refreshCommunication')
    }
  }
}
</script>

<style>
.add-tracking-campaign-b-dropdown {
  bottom: -35px;
  z-index: 10;
}
@media (max-width: 580px) {
  .add-tracking-campaign-b-dropdown {
    bottom: 5px;
  }
}
</style>
