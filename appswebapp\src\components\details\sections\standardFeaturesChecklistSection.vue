<template>
  <details-section title="Features Checklist" v-if="featureChecklistMetadata" v-model="mode" @cancel="onCancel" canBeHidden :visible="isVisible" @visibilityChange='onVisibilityChange'>
    <div class="view mt-3" v-if="mode === 'view'">
      <b-tabs class="nav-tabs-top nav-responsive-sm" lazy>
        <b-tab v-for="item in getTabs" :key="item.featuresCategory" :title="item.name" class="position-relative">

          <write-permission-wrapper variant="hidden">
            <show-hide-helper class="features-checklist-toggle d-none d-sm-block"
                              v-if="canSubsectionBeHidden(item.featureCategory)"
                              :visible="isSubsectionVisible(item.featureCategory)"
                              @visibilityChange='changeFeatureCategoryVisibility($event, item.featuresCategory)' />
          </write-permission-wrapper>

          <b-card-body class="pb-0">
            <detail-row bootstrapMode v-for="i in itemFeatures(item)" :key="i.id" titlePosition="start">
              <span slot="title">{{i.name}}</span>
              <span slot="payload">{{i.value}}</span>
            </detail-row>

            <b-row class="pt-2">
              <b-col cols="12" class="d-sm-none mb-3 text-muted">Select Features:</b-col>
              <b-col v-for="i in item.features.filter(x => x.valueType === 1)" :key="i.id" sm="6" md="4" xl='2'>
                <b-form-group>
                  <b-form-checkbox disabled :checked="i.value">
                    <span style="color:rgb(78, 81, 85);">
                      {{i.name}}
                      <br v-if="i.hint" v-once>
                      {{i.hint}}
                    </span>
                  </b-form-checkbox>
                </b-form-group>
              </b-col>
            </b-row>

            <b-row>
              <write-permission-wrapper variant="hidden">
                <show-hide-helper class="d-sm-none features-checklist-toggle-mobile"
                                  variant="button"
                                  v-if="canSubsectionBeHidden(item.featureCategory)"
                                  :visible="isSubsectionVisible(item.featureCategory)"
                                  @visibilityChange='changeFeatureCategoryVisibility($event, item.featuresCategory)' />
              </write-permission-wrapper>
            </b-row>

          </b-card-body>

        </b-tab>
      </b-tabs>

      <b-row v-if="getNotes">
        <b-col sm="11">
          <detail-row bootstrapMode titlePosition="start">
            <span slot="title">{{getNotes.name}}:</span>
            <div slot="payload" class="w-100">
              <b-collapse id="features-notes" :visible="getNotes.featureCategory.isDisplayed">
                <div class="mb-3 mb-sm-0">{{getNotes.features[0].value}}</div>
              </b-collapse>
              <write-permission-wrapper hidden>
                <show-hide-helper variant="button" class="d-sm-none w-100"
                                  :visible="getNotes.featureCategory.isDisplayed"
                                  @visibilityChange='changeFeatureCategoryVisibility($event, getNotes.featuresCategory)' />
              </write-permission-wrapper>
            </div>

          </detail-row>
        </b-col>
        <b-col sm="1" class="pt-2 pl-0 d-none d-sm-block">
          <write-permission-wrapper variant="hidden">
            <show-hide-helper
              :visible="getNotes.featureCategory.isDisplayed"
              @visibilityChange='changeFeatureCategoryVisibility($event, getNotes.featuresCategory)' />
          </write-permission-wrapper>

        </b-col>
      </b-row>
    </div>

    <div class="edit mt-3" v-else-if="mode === 'edit'">

      <b-tabs class="nav-tabs-top nav-responsive-sm">
        <b-tab v-for="item in getTabs" :key="item.featuresCategory" :title="item.name" class="position-relative">
          <write-permission-wrapper variant="hidden">
            <show-hide-helper class="features-checklist-toggle d-none d-sm-block"
                              v-if="canSubsectionBeHidden(item.featureCategory)"
                              :visible="isSubsectionVisible(item.featureCategory)"
                              @visibilityChange='changeFeatureCategoryVisibility($event, item.featuresCategory)' />
          </write-permission-wrapper>

          <b-card-body class="pb-0">

            <ValidationProvider v-for="i in itemFeatures(item)" :key="i.id" :titlePosition="i.valueType === 6 ? 'start' : ''" immediate rules="min:0|xml" v-slot="{errors}">
            <detail-row :title-position="'start'" bootstrapMode editMode :error="errors[0]">
              <span slot="title">{{i.name}}</span>
              <b-form-select slot="payload" v-if="i.nameValueOptions" v-model="i.value" :options="getNameValueOptions(i.nameValueOptions)"></b-form-select>
              <b-form-textarea slot="payload" v-else-if="i.valueType === 6"
                :name ="i.name"
                v-model="i.value"
                :rows="3"
                :max-rows="6">
              </b-form-textarea>
              <b-form-input slot="payload" v-else v-model="i.value"
                :name ="i.name"
              >
              </b-form-input>
            </detail-row>
            </ValidationProvider>

            <b-row class="pt-2">
              <b-col v-for="i in item.features.filter(x => x.valueType === 1)" :key="i.id" sm="6" md="4" xl='2' class="pb-3">
                <b-form-checkbox v-model="i.value">
                  {{i.name}}
                  <br v-if="i.hint" v-once>
                  {{i.hint}}
                </b-form-checkbox>
              </b-col>
            </b-row>

          </b-card-body>

        </b-tab>
      </b-tabs>

      <b-row class="mt-2" v-if="getNotes">

        <b-col sm="10">
            <detail-row bootstrapMode editMode mobileWrap titlePosition="start">
              <span slot="title">{{getNotes.name}}:</span>
              <div slot="payload" class="w-100">
                <b-collapse id="features-notes-edit" class="w-100 mb-3" slot="payload" :visible="getNotes.featureCategory.isDisplayed">

                <ValidationProvider immediate rules="min:0|xml" :name ="getNotes.features[0].name" v-slot="{errors}">
                  <b-form-textarea
                    v-model="getNotes.features[0].value"
                    :name ="getNotes.features[0].name"
                    :rows="6">
                  </b-form-textarea>
                  <span class="text-danger">{{errors[0]}}</span>
                </ValidationProvider>
                </b-collapse>
                <write-permission-wrapper variant="hidden">
                  <show-hide-helper variant="button" class="d-sm-none w-100"
                                    :visible="getNotes.featureCategory.isDisplayed"
                                    @visibilityChange='changeFeatureCategoryVisibility($event, getNotes.featuresCategory)' />
                </write-permission-wrapper>
              </div>
            </detail-row>

        </b-col>

        <b-col class="pt-2 pl-0 d-none d-sm-flex justify-content-end">
          <write-permission-wrapper variant="hidden">
            <show-hide-helper :visible="getNotes.featureCategory.isDisplayed" @visibilityChange='changeFeatureCategoryVisibility($event, getNotes.featuresCategory)' />
          </write-permission-wrapper>
        </b-col>

      </b-row>
    </div>
  </details-section>
</template>

<script>
import {mapGetters} from 'vuex'
import detailRow from '../helpers/detailRow'
import detailsSection from '@/components/details/detailsSection'
import showHideHelper from '../helpers/showHideHelper'
import vehicleSaveMixin from '@/mixins/vehicle/vehicleSaveMixin'
import writePermissionWrapper from '../../_shared/writePermissionWrapper'

export default {
  name: 'warranty-section',
  data () {
    return {
      mode: 'view',
      notesFeatureCategories: [509, 701]
    }
  },
  mixins: [vehicleSaveMixin],
  computed: {
    ...mapGetters('details', ['vehicle', 'vehicleOriginal', 'featureChecklistMetadata']),
    getTabs () {
      return this.featureChecklistMetadata.subsections
        .filter(x => !(this.notesFeatureCategories.includes(+x.featuresCategory)))
        .map(x => ({
          ...x,
          name: x.name.replace(/features/i, '')
        }))
    },
    getNotes () {
      return this.featureChecklistMetadata.subsections
        .find(x => this.notesFeatureCategories.includes(+x.featuresCategory))
    },
    isVisible () {
      return this.vehicle.isStandardFeaturesTurnedOn
    }
  },
  methods: {
    canSubsectionBeHidden (featureCategory) {
      return !!featureCategory
    },
    isSubsectionVisible (featureCategory) {
      return !featureCategory || featureCategory.isDisplayed
    },
    onVisibilityChange (val) {
      this.vehicle.isStandardFeaturesTurnedOn = val
    },
    itemFeatures (featureCategory) {
      return featureCategory.features.filter(x => x.valueType !== 1 && !x.displaySection)
    },
    async changeFeatureCategoryVisibility (val, id) {
      try {
        this.featureChecklistMetadata.subsections.find(x => x.featuresCategory === id).featureCategory.isDisplayed = val
        await this.updateDisplayedSections(this.vehicleOriginal, this.vehicle)
      } catch (e) {
        this.$toaster.error("An error occurred. Can't update the visibility option.")
      }
    },
    getNameValueOptions (nameValueOptions) {
      return nameValueOptions.map(x => ({
        value: x.value,
        text: x.key
      }))
    },
    onCancel () {
      this.$forceUpdate()
    }
  },
  components: {
    'details-section': detailsSection,
    'show-hide-helper': showHideHelper,
    'detail-row': detailRow,
    'write-permission-wrapper': writePermissionWrapper
  }
}
</script>

<style>
  .features-checklist-toggle{
    position: absolute;
    right: 25px;
    top: 15px;
    z-index: 1
  }

  .features-checklist-toggle-mobile {
    margin: 0.8rem 0.8rem 1.5rem;
    width: 100%;
  }
</style>
