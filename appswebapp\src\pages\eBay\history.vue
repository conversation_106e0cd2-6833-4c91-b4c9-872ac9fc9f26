<template>
  <div v-if="!isLoading && !isExceptionOccurred">
    <h4>eBay History</h4>
    <vehicleDescription :vehicle="vehicle"/>
    <b-card no-body>
      <b-table
        :fields="tableFields"
        :items="historyItems"
        striped
      >
        <template #cell(auctionId)="row">
          <b-link :href="getAuctionUrl(row.item.AuctionId)"><u>{{row.item.AuctionId}}</u></b-link>
        </template>
        <template #cell(highBid)="row">
          {{getHighBidDesc(row.item)}}
        </template>
        <template #cell(type)="row">
          {{getTypeDesc(row.item)}}
        </template>
        <template #cell(timeLeftOrEnded)="row">
          {{getDateTimeDesc(row.item.EndDateTime)}}
        </template>
        <template #cell(LeadCount)="row">
          {{getLeadsCount(row.item)}}
        </template>
      </b-table>
    </b-card>
  </div>
  <div v-else-if="isLoading && !isExceptionOccurred">
    <loader size="lg"/>
  </div>
  <div v-else>
    <error-alert/>
  </div>
</template>

<script>
import vehicleDescription from '@/components/eBay/helpers/vehicleDescription'
import loader from '@/components/_shared/loader'
import moment from 'moment'
import numeral from 'numeral'
import constants from '@/shared/ebay/constants'

export default {
  name: 'ebay-history',
  props: {
    accountId: { type: Number, required: true },
    vin: { type: String, required: true }
  },
  metaInfo: {
    title: 'eBay History'
  },
  data () {
    return {
      historyItems: [],
      vehicle: {},
      isLoading: true,
      auctionsLeadsInfo: {},
      isExceptionOccurred: false
    }
  },
  async created () {
    await this.populateData()
  },
  components: {
    vehicleDescription,
    loader
  },
  computed: {
    tableFields () {
      return [
        {
          key: 'auctionId',
          label: 'eBay ID',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'highBid',
          label: 'High Bid ($)',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'TotalBids',
          label: 'Bids',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'HitCount',
          label: 'Views',
          tdClass: 'Views'
        },
        {
          key: 'WatchCount',
          label: 'Watchers',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'LeadCount',
          label: 'Leads',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'timeLeftOrEnded',
          label: 'Time Left / Ended',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'type',
          label: 'Type',
          tdClass: 'py-2 align-middle'
        }
      ]
    }
  },
  methods: {
    getTypeDesc (item) {
      if (item.IsLocal) {
        return 'eBay Local'
      }
      if (item.IsPriceFixed) {
        return 'Fixed Price Listing'
      }

      return `Auction (${item.AuctionDuration}d)`
    },
    getDateTimeDesc (value) {
      if (moment(value) > moment()) {
        let duration = moment.duration(moment(value).diff(moment()))
        let days = duration.asDays()
        let hours = duration.asHours() - parseInt(duration.asHours() / 24) * 24
        return `${parseInt(days)}d ${parseInt(hours)}h`
      }

      return moment(value).format('MM/DD/YYYY hh:mm A')
    },
    getAuctionUrl (value) {
      return constants.eBayInfoUrls.ebayItemUrl(value)
    },
    getHighBidDesc (item) {
      if (item.HighBid) {
        let percent = (item.HighBid / item.ReservePrice) * 100

        return `${numeral(item.HighBid).format('$0,0')} (${parseInt(percent)}%)`
      }

      return 'N/A'
    },
    async populateData () {
      try {
        let res = await this.$store.dispatch('eBay/getEBayHistory', { accountId: this.accountId, vin: this.vin })
        this.historyItems = res.data.Items
        this.vehicle = res.data.Vehicle
        await this.populateLeadsInfo(res.data.Items)
      } catch (ex) {
        this.isExceptionOccurred = true
        this.$toaster.exception(ex, 'Something went wrong!')
        this.$logger.handleError(ex, 'Cannot get eBay history')
      } finally {
        this.isLoading = false
      }
    },
    async populateLeadsInfo (items) {
      if (!items || items.length === 0) {
        return
      }
      let auctionIds = items.map(x => { return x.AuctionId })
      this.$store.dispatch('leads/getEBayAuctionsLeadsInfo', { data: { auctionIds: auctionIds } }).then(res => {
        this.auctionsLeadsInfo = res.data.items
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
        this.$logger.handleError(ex, 'Cannot get auctions leads info', auctionIds)
      })
    },
    getLeadsCount (item) {
      let res = this.auctionsLeadsInfo[item.AuctionId]
      if (res) {
        return res.leadCount
      }

      return 0
    }
  }
}
</script>
