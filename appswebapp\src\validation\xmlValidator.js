const validTags = ['p', 'br', 's', 'ol', 'ul', 'li', 'span', 'a', 'b', 'div', 'em', 'i', 'img', 'div', 'blockquote', 'font', 'h1', 'h2', 'h3', 'h4', 'h5', 'hr', 'center', 'form', 'input', 'label', 'link', 'nobr', 'script', 'small', 'span', 'strong', 'style', 'sub', 'sup', 'svg', 'table', 'tbody', 'td', 'th', 'tr', 'u', '!--', 'iframe', '']

class XmlValidator {
  validatePossibleXmlText (fieldValue) {
    // if (!fieldValue || !!fieldName) {
    //   return
    // }
    // this.addMessageToErrors(fieldName, 'Please wait until validation processing')
    let message = this.tagsFromText(fieldValue)
    if (message !== '') {
      return message
    }

    return true
  };
  validateEncodedXmlText (fieldValue) {
    let message = this.tagsFromEncodedText(fieldValue)
    if (message !== '') {
      return message
    }

    return true
  };
  tagsFromText (data) {
    let tagName = ''

    if (data === '') {
      return ''
    }

    // new Set() if will change algorithm and show all invalid Tags
    // let tagNames = new Set()

    let i = 0
    while (i < data.length) {
      if (data[i] === '<') {
        // &lt
        i++
        if (data[i] === '/') {
          i++
        }

        while (i < data.length && data[i] !== '/' && data[i] !== ' ' && data[i] !== '>') {
          tagName += data[i]
          i++
        }
        if (i >= data.length) {
          continue
        }

        // tagNames.add(tagName)
        if (!validTags.includes(tagName)) {
          // invalidTagName = tagName
          return 'This text can not be saved as it has invalid XML tag: "' + tagName + '". Please remove it and try to save again.'
        }
        tagName = ''

        while (i < data.length && data[i] !== '>') {
          i++
        }
        if (i >= data.length) {
          continue
        }

        i++
        continue
      }
      i++
    }
    return ''
  };
  tagsFromEncodedText (data) {
    if (data === '') {
      return ''
    }
    let tagName = ''

    // new Set() if will change algorithm and show all invalid Tags
    // let tagNames = new Set()

    let i = 0
    while (i + 3 < data.length) {
      if (
        data[i] === '&' && //
        data[i + 1] === 'l' &&
        data[i + 2] === 't' &&
        data[i + 3] === ';'
      ) {
        // &lt
        i += 4

        if (data[i] === '/') {
          i++
        }

        while (
          i < data.length &&
          data[i] !== ' ' &&
          data[i] !== '&' &&
          data[i] !== '/'
        ) {
          tagName += data[i]
          i++
        }
        if (i >= data.length) {
          continue
        }

        // tagNames.add(tagName)
        if (!validTags.includes(tagName)) {
          return 'This text can not be saved as it has invalid XML tag: "' + tagName + '". Please remove it and try to save again.'
        }
        tagName = ''

        while (
          i + 2 < data.length &&
          data[i] !== 'g' &&
          data[i + 1] !== 't' &&
          data[i + 2] !== ';'
        ) {
          i++
        }
        if (i >= data.length) {
          continue
        }

        i++
        continue
      }
      i++
    }
    return ''
  };
}

export default new XmlValidator()
