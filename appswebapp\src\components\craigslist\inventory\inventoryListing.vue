<template>
  <div v-if="totalItems > 0">
    <b-card no-body>
      <div class="table-responsive">
        <b-table
          class="products-table card-table"
          :items="tableItems"
          :sort-by="tableSortBy"
          :sort-desc="tableSortDesc"
          @sort-changed="onSortChanged"
          :fields="tableFields"
          :striped="true"
          :bordered="false"
          :no-local-sorting="true"
          responsive
        >
          <template #cell(title)="row">
            <div class="media align-items-center">
              <img class="d-block float-left ui-w-50 mr-3" style="min-height: 1px" :src="getVehiclePhotoSrc(row.item)">
              <span>{{row.item | getVehicleTitle}}</span>
            </div>
          </template>
          <template #cell(details)="row">
            <div class="media align-items-center">
              <b-button size="sm" @click.stop="row.toggleDetails" class="text-center">
                {{ row.detailsShowing ? 'Hide' : 'Show' }} Details
              </b-button>
            </div>
          </template>
          <template #row-details="row">
            <b-card>
              <b-row class="mb-2">
                <b-col sm="12" md="4" lg="3" xl="2"><b>Stock#:</b></b-col>
                <b-col>{{ row.item.stockNumber }}</b-col>
              </b-row>
              <b-row class="mb-2">
                <b-col sm="12" md="4" lg="3" xl="2"><b>VIN:</b></b-col>
                <b-col>{{ row.item.vin }}</b-col>
              </b-row>
              <b-row class="mb-2">
                <b-col sm="12" md="4" lg="3" xl="2"><b>Miles:</b></b-col>
                <b-col>{{ row.item.mileage }}</b-col>
              </b-row>
              <b-row class="mb-2">
                <b-col sm="12" md="4" lg="3" xl="2"><b>Engine:</b></b-col>
                <b-col>{{ row.item.engineDescription }}</b-col>
              </b-row>
              <b-row class="mb-2">
                <b-col sm="12" md="4" lg="3" xl="2"><b>Transmission:</b></b-col>
                <b-col>{{ row.item.transmissionDescription }}</b-col>
              </b-row>
              <b-row class="mb-2">
                <b-col sm="12" md="4" lg="3" xl="2"><b>Ext. Color:</b></b-col>
                <b-col>{{ row.item.exteriorColorLabel }}</b-col>
              </b-row>
              <b-row class="mb-2">
                <b-col sm="12" md="4" lg="3" xl="2"><b>Int. Color:</b></b-col>
                <b-col>{{ row.item.interiorColorLabel }}</b-col>
              </b-row>
              <b-button size="sm" @click="row.toggleDetails">Hide Details</b-button>
            </b-card>
          </template>
          <template #cell(currentStatus)="row">
            {{ row.item | getCraigslistPostStatus }}
          </template>
          <template #cell(actions)="data">
            <template v-if="isActionButtonEnabled(data.item)">
              <b-dropdown variant="outline-secondary icon-btn btn-round" size="sm" right no-caret boundary='viewport'>
                <template slot="button-content">
                  <i class="ion ion-ios-more m-0"></i><span class="sr-only">Manage</span>
                </template>
                <b-dropdown-item v-if="isPostingAllowed" :to="{ path: getVehiclePostToCraigslistPath(data.item, 2) }">Post Vehicle</b-dropdown-item>
                <b-dropdown-item v-if="isPostingAllowed" :to="{ path: getVehiclePostToCraigslistPath(data.item, 1) }">Schedule Post</b-dropdown-item>
                <b-dropdown-item v-if="data.item.postedDateTime" v-can="permission.ViewLogs" :to="{ path: getVehicleLogsPath(data.item) }">Vehicle Log</b-dropdown-item>
                <b-dropdown-item v-if='isPostingAllowed && status === 3' @click='showModal(data.item.craigslistDelayedJobs)'>Edit/Delete Scheduled</b-dropdown-item>
              </b-dropdown>
            </template>
          </template>
        </b-table>
      </div>
      <!-- Modal -->
      <b-modal
        :visible="isShow"
        title="Edit/Delete Scheduled"
        size="lg"
        @hide="hideModal"
      >
        <div class="table-responsive">
          <b-table
            :items='modalItems'
            :fields='tableModalFields'
            class="products-table card-table"
          >
          <template #cell(manage)="row">
            <b-row>
              <b-btn class='mr-1 mt-1' size='sm' variant="secondary" :to="{ path: getVehiclePostToCraigslistScheduleEditPath(row.item, 3) }">Edit</b-btn>
              <b-btn @click='DeletedScheduled(row.item)' class='mt-1' size='sm' variant="secondary">Delete</b-btn>
            </b-row>
          </template>
          </b-table>
        </div>
        <template #modal-footer>
          <b-button size="sm" @click="hideModal" class="text-center float-left">Close</b-button>
        </template>
      </b-modal>

      <!-- / Pagination -->
      <paging
        :pageNumber="pageNumber"
        :pageSize="pageSize"
        :totalItems="totalItems"
        titled
        pageSizeSelector
        @numberChanged="onPageNumberChanged"
        @changePageSize="onPageSizeChanged"
      />
    </b-card>

  </div>
  <div v-else>
    No vehicles found
  </div>
</template>

<script>
import permission from '@/shared/common/permissions'
import numeral from 'numeral'
import moment from 'moment'
import paging from './../../../components/_shared/paging.vue'
import craigslistConstants from './../../../shared/craigslist/constants'
import inventoryFiltersMixin from './../../../mixins/craigslist/inventoryFiltersMixin'
import { mapGetters } from 'vuex'

export default {
  name: 'inventory-listing-table',
  components: {
    'paging': paging
  },
  mixins: [inventoryFiltersMixin],
  props: {
    tableItems: { type: Array, required: true },
    totalItems: { type: Number, require: true },
    pageNumber: { type: Number, required: true },
    pageSize: { type: Number, required: true },
    sortType: { type: Number, required: true },
    isPostingAllowed: { type: Boolean, required: true },
    status: { type: Number, required: true }
  },
  data () {
    return {
      permission,
      modalItems: [],
      isShow: false,
      tableFields: [{
        key: 'title',
        label: 'Vehicle',
        sortable: false,
        tdClass: 'py-2 align-middle'
      }, {
        key: 'details',
        label: '',
        sortable: false,
        tdClass: 'py-2 align-middle'
      }, {
        key: 'stockNumber',
        label: 'Stock #',
        sortable: true,
        sortTypeAsc: craigslistConstants.inventorySortTypes.stockNumberAsc,
        sortTypeDesc: craigslistConstants.inventorySortTypes.stockNumberDesc,
        tdClass: 'py-2 align-middle'
      }, {
        key: 'daysInStock',
        label: 'Days in Stock',
        sortable: true,
        sortTypeAsc: craigslistConstants.inventorySortTypes.daysInStockAsc,
        sortTypeDesc: craigslistConstants.inventorySortTypes.daysInStockDesc,
        tdClass: 'py-2 align-middle'
      }, {
        key: 'priceTitle',
        label: 'Vehicle Price',
        sortable: true,
        sortTypeAsc: craigslistConstants.inventorySortTypes.priceAsc,
        sortTypeDesc: craigslistConstants.inventorySortTypes.priceDesc,
        tdClass: 'py-2 align-middle',
        formatter: value => numeral(value).format('$0,0')
      }, {
        key: 'actualPhotosCount',
        label: 'Number of Photos',
        sortable: true,
        sortTypeAsc: craigslistConstants.inventorySortTypes.photosAsc,
        sortTypeDesc: craigslistConstants.inventorySortTypes.photosDesc,
        tdClass: 'py-2 align-middle'
      }, {
        key: 'currentStatus',
        tdClass: 'py-2 align-middle'
      }, {
        key: 'actions',
        tdClass: 'py-2 align-middle'
      }]
    }
  },
  methods: {
    onSortChanged (value) {
      const sortingColumn = this.tableFields.find(x => x.key === value.sortBy)

      this.$emit('update:sortType', sortingColumn
        ? value.sortDesc
          ? sortingColumn.sortTypeDesc
          : sortingColumn.sortTypeAsc
        : 0)
    },
    getVehiclePhotoSrc (item) {
      return item.presentationPhoto + '?q=' + moment().format()
    },
    getVehiclePostToCraigslistPath (item, postingType) {
      return `/craigslist/${item.accountId}/post/${item.vin}/?postingtype=${postingType}`
    },
    getVehicleLogsPath (item) {
      return `/craigslist/${item.accountId}/logs/?search=${item.vin}`
    },
    getVehiclePostToCraigslistScheduleEditPath (item, postingType) {
      return `/craigslist/${item.accountId}/post/${item.id}/?postingtype=${postingType}`
    },
    showModal (item) {
      this.modalItems = item
      this.isShow = true
    },
    hideModal () {
      this.isShow = false
      this.modalItems = []
    },
    DeletedScheduled (item) {
      this.$store.dispatch('craigslist/deleteDelayedJob', { accountId: item.accountId, id: item.id }).then(x => {
        this.modalItems = this.modalItems.filter(x => x.id !== item.id)
        if (this.modalItems.length === 0) {
          this.hideModal()
          this.$router.go()
        }
      })
        .catch(ex => {
          this.$toaster.error(`Failed request`)
        })
    },
    isActionButtonEnabled (item) {
      return this.isPostingAllowed || (!!item.postedDateTime && this.hasViewLogPermission)
    }
  },
  filters: {
    getVehicleTitle: function (item) {
      if (!item) return ''
      let title = ''
      if (item.year > 0) {
        title = item.year.toString()
      }

      title = [title, item.make, item.model, item.trim].filter(v => v && v !== '').join(' ')

      return title.trim()
    },
    getCraigslistPostStatus: function (item) {
      if (item.postedDateTime) {
        return 'Active'
      }

      if (item.deletedDateTime) {
        return 'Deleted ' + moment(item.deletedDateTime).format('MM/DD/YYYY')
      }

      return 'Never Posted'
    }
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {hasPermissions: () => false}
    },
    hasViewLogPermission () {
      return this.user.hasPermissions(permission.ViewLogs)
    },
    tableSortBy () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sortType
      } else {
        return false
      }
    },
    tableModalFields () {
      return [
        {
          key: 'title',
          label: 'Title',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'dateTimeToPost',
          label: 'Date Time to Post',
          tdClass: 'py-2 align-middle',
          formatter: value => {
            return moment(value).format('MM/DD/YYYY hh:mm:ss A')
          }
        },
        {
          key: 'postedBy',
          label: 'Posted By',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle'
        }
      ]
    }
  }
}
</script>
