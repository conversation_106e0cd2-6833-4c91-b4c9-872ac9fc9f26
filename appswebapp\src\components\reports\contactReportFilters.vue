<template>
  <b-modal
    size="lg"
    title="Filter Manager"
    :visible="isVisible"
    @hide="hide"
    hide-footer
  >
    <template v-if="isViewMode">
      <b-table
        :items="items"
        :fields="getTableFields"
        show-empty
        hover
        striped
        caption-top
      >
        <template #table-caption>
          <b-btn class="float-right" variant="primary" @click="onCreate">Create New Filter</b-btn>
        </template>
        <template #cell(campaigns)="data">
          {{ getCampaignsDescription(data.item.campaignTypeIds) }}
        </template>
        <template #cell(manage)="data">
          <b-btn size="sm" class="w-100 mb-2" @click="onEdit(data.item)">Edit</b-btn>
          <b-btn size="sm" class="w-100" variant="primary" @click="onDelete(data.item)">Delete</b-btn>
        </template>
      </b-table>
      <paging
        :pageNumber="filters.page"
        :pageSize="filters.pageSize"
        :totalItems="totalItemsCount"
        titled
        pageSizeSelector
        @numberChanged="pageChanged"
        @changePageSize="changePageSize"
      />
    </template>
    <b-card v-else>
      <h4>{{ getTitle }}</h4>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Name:</span>
        <b-form-input slot="payload" v-model="contactReportFilter.name"></b-form-input>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Campaigns:</span>
        <multiselect slot="payload"
          v-model='selectedCampaigns'
          :options='campaignOptions'
          group-values="data"
          group-label="groupName"
          track-by="value"
          label="text"
          :closeOnSelect='false'
          :group-select="false"
          :multiple="true"
          :showLabels='false'
          :showPointer='false'
          :limit='3'
        />
      </detail-row>
      <l-button :loading="isActionPerformed" size="sm" variant="primary" @click="onSubmit">Submit</l-button>
      <b-btn size="sm" @click="onCancelAction" :disabled="isActionPerformed">Cancel</b-btn>
    </b-card>
  </b-modal>
</template>

<script>
import { ObjectSchema } from '@/shared/common/objectHelpers'
import ReportService from '@/services/reports/ReportService'
import CommunicationService from '@/services/leads/CommunicationService'
import { campaignTypes } from '@/shared/leads/campaignTypes'
import loader from '@/components/_shared/loader'
import detailRow from '@/components/details/helpers/detailRow'
import Multiselect from 'vue-multiselect'
import globals from '../../globals'

const defaultFilters = new ObjectSchema({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 }
})

export default {
  name: 'contact-report-filters',
  props: {
    isVisible: {
      type: Boolean,
      required: true
    }
  },
  data () {
    return {
      isLoading: true,
      isActionPerformed: false,
      filters: defaultFilters.getObject(),
      items: [],
      totalItemsCount: 0,
      campaignOptions: [],
      isEditMode: false,
      contactReportFilter: {},
      campaigns: [],
      selectedCampaigns: [],
      isViewMode: true
    }
  },
  components: {
    loader,
    detailRow,
    'paging': () => import('@/components/_shared/paging'),
    'multiselect': Multiselect
  },
  mounted () {
    this.fillData()
    this.populateContactReportFilters()
  },
  computed: {
    getTitle () {
      return this.isEditMode ? 'Edit' : 'Create'
    },
    getTableFields () {
      return [
        {
          key: 'name',
          label: 'Name',
          tdClass: 'py-2 align-middle',
          thClass: 'col-lg-1 col-2'
        },
        {
          key: 'campaigns',
          label: 'Campaigns',
          tdClass: 'py-2 align-middle',
          thClass: 'col-lg-10 col-8'
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle',
          thClass: 'col-lg-1 col-2'
        }
      ]
    }
  },
  methods: {
    pageChanged (newPage) {
      this.filters.page = newPage
      this.populateContactReportFilters()
    },
    changePageSize (newSize) {
      this.filters.page = 1
      this.filters.pageSize = newSize
      this.populateContactReportFilters()
    },
    populateContactReportFilters () {
      let apiFilter = {
        skip: (this.filters.page - 1) * this.filters.pageSize,
        limit: this.filters.pageSize
      }

      ReportService.getContactReportFilterListing(apiFilter).then(res => {
        this.items = res.data.items
        this.totalItemsCount = res.data.totalItemsCount
      }).catch(ex => {
        this.$toaster.error('Failed on getting contact report filters')
      }).finally(() => {
        this.isLoading = false
      })
    },
    async fillData () {
      try {
        const apiResults = await Promise.all(
          [
            CommunicationService.getAvailableCampaigns(campaignTypes.onSite.value),
            CommunicationService.getAvailableCampaigns(campaignTypes.offSite.value),
            CommunicationService.getAvailableCampaigns(campaignTypes.offSiteParent.value)
          ]
        )
        this.campaignTypesOptions = []
        let campaigns = apiResults[0].data
        if (campaigns && campaigns.length > 0) {
          this.campaigns = [...campaigns]
          this.campaignOptions.push({
            groupName: campaignTypes.onSite.text,
            data: campaigns.map(x => { return { value: x.campaignTypeId, text: x.campaignName } })
          })
        }
        campaigns = apiResults[1].data
        if (campaigns && campaigns.length > 0) {
          this.campaigns = this.campaigns.concat(campaigns)
          this.campaignOptions.push({
            groupName: campaignTypes.offSite.text,
            data: campaigns.map(x => { return { value: x.campaignTypeId, text: x.campaignName } })
          })
        }
        campaigns = apiResults[2].data
        if (campaigns && campaigns.length > 0) {
          this.campaigns = this.campaigns.concat(campaigns)
          this.campaignOptions.push({
            groupName: campaignTypes.offSiteParent.text,
            data: campaigns.map(x => { return { value: x.campaignTypeId, text: x.campaignName } })
          })
        }
      } catch (ex) {
        this.$toaster.error('Failed on receiving data from server')
        this.$logger.handleError(ex, 'Failed on getting campaign types')
      }
    },
    createContactReportFilter () {
      ReportService.createContactReportFilter(this.contactReportFilter).then(res => {
        this.$toaster.success('Created Successfully')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Failed on creating', {timeout: 5000})
        this.$logger.handleError(ex, 'Failed on creating contact report filter')
      }).finally(() => {
        this.onCancelAction()
        this.populateContactReportFilters()
      })
    },
    updateContactReportFilter () {
      ReportService.updateContactReportFilter(this.contactReportFilter).then(res => {
        this.$toaster.success('Updated Successfully')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Failed on updating', {timeout: 5000})
        this.$logger.handleError(ex, 'Failed on updating contact report filter')
      }).finally(() => {
        this.onCancelAction()
        this.populateContactReportFilters()
      })
    },
    deleteContactReportFilter (id) {
      ReportService.deleteContactReportFilter(id).then(res => {
        this.$toaster.success('Deleted Successfully')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Failed on updating', {timeout: 5000})
        this.$logger.handleError(ex, 'Failed on updating contact report filter')
      }).finally(() => {
        this.filters.page = 1
        this.populateContactReportFilters()
      })
    },
    onCreate () {
      this.contactReportFilter = {}
      this.selectedCampaigns = []
      this.isEditMode = false
      this.isViewMode = false
    },
    onEdit (item) {
      this.contactReportFilter = globals().getClonedValue(item)
      this.selectedCampaigns = this.getSelectedCampaigns(this.contactReportFilter.campaignTypeIds)
      this.isEditMode = true
      this.isViewMode = false
    },
    onDelete (item) {
      this.deleteContactReportFilter(item.id)
    },
    onSubmit () {
      if (this.selectedCampaigns.length === 0) {
        this.$toaster.error('Campaigns is required', {timeout: 4000})
        return
      }
      this.contactReportFilter.campaignTypeIds = this.selectedCampaigns.map(x => x.value)
      this.isActionPerformed = true
      if (this.isEditMode) {
        this.updateContactReportFilter()
      } else {
        this.createContactReportFilter()
      }
    },
    onCancelAction () {
      this.isViewMode = true
      this.isActionPerformed = false
    },
    getSelectedCampaigns (campaignTypeIds) {
      let res = this.campaigns.filter(x => campaignTypeIds.includes(x.campaignTypeId))
      return res ? res.map(x => { return { value: x.campaignTypeId, text: x.campaignName } }) : []
    },
    getCampaignsDescription (campaignTypeIds) {
      let campaigns = this.campaigns.filter(x => campaignTypeIds.includes(x.campaignTypeId))
      let descriptions = campaigns ? campaigns.map(x => x.campaignName) : []
      return descriptions.join(', ')
    },
    hide () {
      this.$emit('hide')
    }
  },
  watch: {
    isVisible: {
      deep: true,
      handler: function () {
        if (this.isVisible) {
          this.populateContactReportFilters()
        }
      }
    }
  }
}
</script>
