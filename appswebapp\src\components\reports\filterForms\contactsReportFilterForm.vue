<template>
  <div>
    <b-form @submit.prevent="generate">
      <div class="contacts-report-filters-container">
        <div class="my-2 contacts-report-filters-item">
          <b-input-group>
            <b-form-input list="search-suggestions" @input.native="onSearchInput" v-model="copyFilters.search" placeholder="Search..."></b-form-input>
            <b-input-group-append is-text>
              <b-form-checkbox v-model="copyFilters.includeRelations" :disabled="!isRelatedFilterOptionEnabled">Show Related</b-form-checkbox>
            </b-input-group-append>
          </b-input-group>
          <datalist id="search-suggestions">
            <option v-for="opt in getSuggestions" :key="opt.value" :value="opt.value">{{opt.text}}</option>
          </datalist>
        </div>
        <div class="my-2 contacts-report-filters-item">
          <b-form-select v-model="copyFilters.displayFilterType" :options="getDisplayFilterTypeOptions"></b-form-select>
        </div>
        <div class="my-2 contacts-report-filters-item">
          <multiSelectWithCheckboxes
            v-model="selectedCampaignGroups"
            :options="getCampaignGroupOptions"
            name="Campaign Groups"
            customMessageOfNoneSelectedItems="All Contacts"
            :searchable="false"/>
        </div>
        <div class="my-2 contacts-report-filters-item">
          <multiSelectWithCheckboxes
            v-model="selectedLocations"
            :options="getLocationTypeOptions"
            name="Locations"
            customMessageOfNoneSelectedItems="All Locations"
            :disabled="!isLocationOptionEnabled"
            :searchable="false"/>
        </div>
        <div class="my-2 contacts-report-filters-item d-flex flex-row">
          <div class="w-100">
            <l-button class="w-100" :loading="isGenerationPerformed" variant="primary" type="submit">Generate</l-button>
          </div>
          <b-overlay variant="white" :show="!csvData.length > 0" class="w-100 ml-1" opacity="0.3">
            <template #overlay>
              <div></div>
            </template>
            <download-csv
              class="btn btn-secondary text-center w-100"
              :data="csvData"
              :fields="csvFields"
              :labels="csvLabels"
              :advanceOptions="{skipEmptyLines: 'greedy'}"
              :name="reportFileName"
            >
              Export CSV
            </download-csv>
          </b-overlay>
        </div>
      </div>
    </b-form>
  </div>
</template>

<script>
import ReportService from '@/services/reports/ReportService'
import multiSelectWithCheckboxes from '@/components/_shared/multiSelectWithCheckboxes'
import { campaignTypes } from '@/shared/leads/campaignTypes'
import constants from '@/shared/reports/constants'
import globals from '../../../globals'
import searchFilterSuggestionsMixin from './searchFilterSuggestionsMixin'

export default {
  name: 'contacts-report-filter-form',
  props: {
    filters: { type: Object, required: true },
    csvData: { type: Array, required: true },
    csvFields: { type: Array, required: true },
    csvLabels: { type: Object, required: true },
    isGenerationPerformed: { type: Boolean, required: true }
  },
  data () {
    return {
      selectedCampaignGroups: [],
      selectedLocations: [],
      campaignGroups: [],
      reportFileName: 'contact_report.csv',
      isReady: false,
      copyFilters: globals().getClonedValue(this.filters)
    }
  },
  created () {
    this.initCommonData().then(() => { this.isReady = true })
  },
  components: {
    multiSelectWithCheckboxes
  },
  mixins: [searchFilterSuggestionsMixin],
  computed: {
    getDisplayFilterTypeOptions () {
      return Object.values(constants.displayFilterTypeOptions)
    },
    getLocationTypeOptions () {
      return Object.values(campaignTypes)
    },
    getCampaignGroupOptions () {
      return this.campaignGroups.map(x => {
        return { value: x.id, text: x.name }
      })
    },
    isLocationOptionEnabled () {
      return this.selectedCampaignGroups.length > 0
    },
    isRelatedFilterOptionEnabled () {
      return !!this.copyFilters.search
    }
  },
  methods: {
    async initCommonData () {
      try {
        let contactReportFiltersResponse = await ReportService.getAllContactReportFilters()
        if (contactReportFiltersResponse && Array.isArray(contactReportFiltersResponse.data)) {
          this.campaignGroups = contactReportFiltersResponse.data
        }
      } catch (ex) {
        this.$toaster.error('Failed on receiving data from server')
      }

      if (this.filters.campaignGroups) {
        let campaignGroups = this.filters.campaignGroups.split(',')
        this.selectedCampaignGroups = this.getCampaignGroupOptions.filter(x => campaignGroups.includes(x.value)).map(x => x.value)
      }

      if (this.filters.locations) {
        let locations = this.filters.locations.split(',')
        this.selectedLocations = this.getLocationTypeOptions.filter(x => locations.includes(`${x.value}`)).map(x => x.value)
      }
    },
    generate () {
      if (this.validate()) {
        this.copyFilters.campaignGroups = this.selectedCampaignGroups.join(',')
        this.copyFilters.locations = this.selectedLocations.join(',')
        this.$emit('generate', this.copyFilters)
      }
    },
    validate () {
      if (!this.copyFilters.search && this.selectedCampaignGroups.length === 0) {
        this.$toaster.error('Please specify some filters for the report')
        return false
      }
      return true
    }
  }
}
</script>

<style>
  .contacts-report-filters-container {
    display: flex;
    flex-direction: row;
    flex-shrink: 2;
    flex-wrap: nowrap;
  }

  .contacts-report-filters-item {
    padding-right: 2px;
    padding-left: 2px;
  }

  .contacts-report-filters-item:nth-child(1)  {
    flex-basis: 32%;
  }
  .contacts-report-filters-item:nth-child(2) {
    flex-basis: 12%;
  }
  .contacts-report-filters-item:nth-child(3) {
    flex-basis: 18%;
  }
  .contacts-report-filters-item:nth-child(4) {
    flex-basis: 18%;
  }
  .contacts-report-filters-item:nth-child(5) {
    flex-basis: 20%;
  }

  @media (width < 1745px) {
    .contacts-report-filters-container {
      flex-wrap: wrap;
    }
  }

  @media (1300px <= width < 1745px) {
    .contacts-report-filters-item:nth-child(1) {
      flex-basis: 60%;
    }
    .contacts-report-filters-item:nth-child(2) {
      flex-basis: 40%;
    }
    .contacts-report-filters-item:nth-child(3) {
      flex-basis: 35%;
    }
    .contacts-report-filters-item:nth-child(4) {
      flex-basis: 35%;
    }
    .contacts-report-filters-item:nth-child(5) {
      flex-basis: 30%;
    }
  }

  @media (800px <= width < 1300px) {
    .contacts-report-filters-item:nth-child(-n + 2) {
      flex-basis: 50%;
    }
    .contacts-report-filters-item:nth-child(3) {
      flex-basis: 50%;
    }
    .contacts-report-filters-item:nth-child(4) {
      flex-basis: 50%;
    }
    .contacts-report-filters-item:nth-child(5) {
      flex-basis: 100%;
    }
  }
  @media (width < 800px) {
    .contacts-report-filters-item:nth-child(n) {
      flex-basis: 100%;
    }
  }
</style>
