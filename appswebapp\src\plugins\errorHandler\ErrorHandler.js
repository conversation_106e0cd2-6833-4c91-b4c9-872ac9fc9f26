import ErrorProcessor from './ErrorProcessor'
import ExceptionRequestModel from '@/plugins/errorHandler/ExceptionRequestModel'
import store from '@/store'
import config from '../../config'

const mustBeIgnoredErrorMessages = ['Network Error']
const ignoredErrorResponseStatusCodes = [400]

export default class ErrorHandler {
  constructor () {
    this.errorProcessor = new ErrorProcessor()
    this.store = store
  };

  handleError (error, message, additionalInfo) {
    console.error(error, message, additionalInfo)
    try {
      if (this.shouldIgnoreApiError(error)) {
        return
      }
      let errorBody = this.getBody(error, additionalInfo)
      if (this.shouldIgnoreError(errorBody)) {
        return
      }
      this.errorProcessor.addError(new ExceptionRequestModel(
        'Apps Vue Web App',
        message || this.getMessage(error),
        errorBody
      ))
    } catch (e) { // error on error
      console.error(e)
      this.errorProcessor.addError(new ExceptionRequestModel(
        'Apps Vue Web App',
        'Can\'t process an error',
        this.getBody(new Error("Can't process an error"), error)
      ))
    }
  };

  shouldIgnoreApiError (error) {
    let statusCode = parseInt(error.status || (error.response || {}).status || 0)
    if (statusCode > 0) {
      return ignoredErrorResponseStatusCodes.some(x => x === statusCode)
    }
    return false
  }

  shouldIgnoreError (errorBody) {
    for (let errorMessage of mustBeIgnoredErrorMessages) {
      if (errorBody.includes(errorMessage)) {
        return true
      }
    }
    return false
  }

  getMessage (error) {
    let message = [`${error.message}`]

    let lineNumber = error.line || error.lineNumber
    if (lineNumber) {
      message.push(`At the line ${lineNumber}`)
    }

    let columnNumber = error.column || error.columnNumber
    if (columnNumber) {
      message.push(`At the column ${columnNumber}`)
    }

    return message.join('. ')
  };

  getBody (error, additionalInfo) {
    let navigator = window.navigator
    let errorDetails = (error.response || {}).data
    let errorObject = {}

    errorObject.message = error.message
    if (this.isString(errorDetails)) {
      errorObject.errorDetails = errorDetails
    }
    errorObject.dateTime = new Date()
    errorObject.userAgent = navigator.userAgent
    errorObject.language = navigator.language
    errorObject.platform = navigator.platform
    errorObject.AppVersion = config.AppVersion
    errorObject.userInfo = this.getUserInfo()
    errorObject.sourceLink = this.getCurrentLink()
    errorObject.stackTrace = this.beautifyStackTrace(error.stack)
    errorObject.originalError = this.getJsonData(error)
    if (error.response) {
      errorObject.apiErrorResponse = this.getJsonData(error.response)
    }

    if (additionalInfo) {
      errorObject.additionalInfo = this.getJsonData(additionalInfo)
    }

    return this.buildHtml(errorObject).innerHTML
  };

  buildHtml (obj) {
    let node = document.createElement('div')
    node.style.paddingLeft = '30px'

    Object.keys(obj).forEach((key) => {
      let value = obj[key]
      if (value === null || value === undefined) {
        return
      }

      let content = document.createElement('span')

      if (this.isStringable(value)) {
        content.innerHTML = this.stringify(value)
      } else {
        content.appendChild(this.buildHtml(value))
      }

      let title = document.createElement('b')
      title.style.textTransform = 'capitalize'
      title.innerHTML = key + ': '

      let p = document.createElement('div')
      p.appendChild(title)
      p.appendChild(content)

      node.appendChild(p)
    })

    return node
  };

  getUserInfo () {
    return (this.store.getters['users/userInfo'] || {}).user
  };

  beautifyStackTrace (stack) {
    let result = (stack || '').replace(/@?https?:\/\/\w+(:\d{1,5})\//gi, ' ')
    let pre = document.createElement('pre')
    pre.innerHTML = result.replace(/(\r\n|\n|\r)/gm, '<br>')

    pre.style.border = 'none'

    return pre.outerHTML
  };

  isStringable (obj) {
    return obj instanceof Date || typeof obj !== 'object'
  };

  stringify (obj) {
    if (obj instanceof Date) {
      return obj.toISOString()
    }

    return obj.toString()
  };

  getCurrentLink () {
    let a = document.createElement('a')
    a.href = window.location.href
    a.innerText = window.location.href

    return a.outerHTML
  };

  getJsonData (obj) {
    let pre = document.createElement('pre')
    pre.style.border = 'none'
    try {
      pre.innerText = JSON.stringify(obj, null, '  ')
      return pre.outerHTML
    } catch (e) {}

    return null
  };

  isString (value) {
    return typeof value === 'string' || value instanceof String
  }
}
