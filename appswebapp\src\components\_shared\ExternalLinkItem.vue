<template>
  <div v-if="(!accountId && menuItem.showOnNonAccountLevel) || (accountId && menuItem.showOnAccountLevel)" class="sidenav-item" v-can=menuItem.requestedPermissions>
    <a :href="buildUrl(menuItem, accountId)"  class="sidenav-link">
      <i v-if="!noIcon" class="sidenav-icon ion ion-ios-link"></i>
      <span>
        <slot>
          {{menuItem.name}}
        </slot>
      </span>
    </a>
  </div>
</template>

<script>
export default {
  name: 'ExternalLinkItem',
  props: {
    accountId: Number,
    menuItem: Object,
    noIcon: Boolean
  },
  methods: {
    buildUrl (menuItem, accountId) {
      if (accountId && menuItem.accountLevelPath) {
        return menuItem.appLink + menuItem.accountLevelPath.replace('{accountId}', accountId)
      }
      return menuItem.appLink
    }
  }
}
</script>

<style scoped>

</style>
