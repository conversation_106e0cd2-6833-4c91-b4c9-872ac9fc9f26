<template>
  <div>
    <website-overview-summary
      :barItems="bar.items"
      :summary="summary"
      :barTimeFormat="barTimeFormat"
      :deviceFilter="page.deviceFilter"
      @deviceFilterChanged="onDeviceFilterChanged"
    />

    <website-overview-account-level-table
      :tableItems="table.items"
      :totalItems="table.totalItems"
      :pageNumber="page.filter.pageNumber"
      :pageSize="page.filter.pageSize"
      :sortType="page.filter.sortType"
      @pageNumberChanged="onPageNumberChanged"
      @pageSizeChanged="onPageSizeChanged"
      @sortTypeChanged="onSortTypeChanged"
    />

  </div>
</template>

<style src="@/vendor/styles/pages/analytics.scss" lang="scss"></style>

<script>
import analyticsConstants from './../../shared/analytics/constants'
import analyticsBuilders from './../../shared/analytics/builders'
import analyticsHelper from './helpers.js'
import baseReportPage from './baseReportPage.js'

import WebsiteOverviewSummary from '../../components/analytics_ga4/summaries/websiteOverviewSummary'
import WebsiteOverviewAccountLevelTable from '../../components/analytics_ga4/tables/websiteOverviewAccountLevelTable'

const filterManager = analyticsBuilders.getFilterManager({
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  pageNumber: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  sortType: { type: Number, default: analyticsConstants.websiteOverviewSortTypes.dateDesc }
})

export default {
  mixins: [baseReportPage],
  name: 'website-overview',
  metaInfo: {
    title: 'Analytics - Website Overview'
  },
  components: {
    WebsiteOverviewAccountLevelTable,
    WebsiteOverviewSummary
  },
  created () {
    this.$store.commit('analyticsGa4/setAnalyticsName', 'Website Overview')
    this.$store.commit('analyticsGa4/setBlackThemeOn', true)
  },
  data () {
    return {
      page: {
        filter: filterManager.defaultValue
      },
      summary: {
        sessions: 0,
        sessionsDelta: null,
        pageViews: 0,
        pageViewsDelta: null,
        totalLeads: 0,
        totalLeadsDelta: null,
        convRatePerSession: 0,
        convRatePerSessionDelta: null
      }
    }
  },
  computed: {
    filterManager () {
      return filterManager
    }
  },
  methods: {
    async updateStatistics () {
      try {
        await Promise.all([
          this.updateAccountLevelGraphAndSummary(),
          this.updateAccountLevelDetails()
        ])
      } catch (err) {
        this.$toaster.error('Failed to get data from server', { timeout: 8000 })
        this.$logger.handleError(err, 'Can\'t update statistics', { filter: this.page.filter, cache: this.cache })
      }
    },
    async updateAccountLevelGraphAndSummary () {
      let storePath = 'analyticsGa4/'
      switch (this.page.deviceFilter) {
        case 'mobile':
          storePath += 'getMobileOverviewGraphAndSummary'
          break
        case 'desktop':
          storePath += 'getDesktopOverviewGraphAndSummary'
          break
        default:
          storePath += 'getWebsiteOverviewGraphAndSummary'
          break
      }

      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        storePath,
        {
          accountId: this.accountId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo
        }
      )

      this.summary = {
        ...this.summary,
        ...store.summary.data
      }
      this.bar.items = store.graph.data.items
    },
    async updateAccountLevelDetails () {
      let storePath = ''
      switch (this.page.deviceFilter) {
        case 'mobile':
          storePath = 'analyticsGa4/getMobileOverviewDetailsPage'
          break
        case 'desktop':
          storePath = 'analyticsGa4/getDesktopOverviewDetailsPage'
          break
        default:
          storePath = 'analyticsGa4/getWebsiteOverviewDetailsPage'
          break
      }

      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        storePath,
        {
          accountId: this.accountId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo,
          pageNumber: this.page.filter.pageNumber,
          pageSize: this.page.filter.pageSize,
          sortType: this.page.filter.sortType
        }
      )

      this.table.items = store.detailedData.data.items
      this.table.totalItems = store.detailedData.data.totalItems
    }
  }
}
</script>
