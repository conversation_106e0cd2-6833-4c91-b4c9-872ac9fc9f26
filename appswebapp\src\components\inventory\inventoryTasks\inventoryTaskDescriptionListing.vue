<template>
  <div>
    <b-table
    class="products-table card-table"
    :fields="tableFields"
    :items="items"
    :striped="true"
    :bordered="false"
    :no-sort-reset="true"
    :no-local-sorting="true"
    :sort-by.sync="sortBy"
    :sort-desc.sync="sortDesc"
    @sort-changed="onSortChanged"
    responsive
    >
      <template #cell(title)="data">
        <div class="media align-items-center">
          <router-link class="media-body text-dark d-flex align-items-center" :to="{ path: getDetailsPath(data.item) }">
            <img class="d-block float-left ui-w-50 mr-3" style="min-height: 1px" :src="getVehiclePhotoSrc(data.item)">
            <span>{{data.item | getVehicleTitle}}</span>
          </router-link>
        </div>
      </template>
      <template #cell(galleryDescription)="data">
        <i class="ion ion-ios-checkmark text-success zoomeds" v-if="hasGalleryDescription(data.item)"></i>
        <i class="ion ion-ios-close text-danger zoomeds" v-else></i>
      </template>
      <template #cell(actions)="data">
        <b-button size="sm" variant="secondary" :to="getDetailsPath(data.item)">Edit Description</b-button>
      </template>
    </b-table>
  </div>
</template>

<script>

export default {
  name: 'inventory-task-description-listing',
  props: {
    sortField: String,
    sortOrderDesc: Boolean,
    items: Array
  },
  data () {
    return {

    }
  },
  filters: {
    getVehicleTitle: function (value) {
      if (!value) return ''
      let title = ''
      if (value.year > 0) {
        title = value.year.toString()
      }

      title = [title, value.make, value.model, value.trim].filter(v => v && v !== '').join(' ')

      return title.trim()
    }
  },
  computed: {
    tableFields () {
      return [
        {
          key: 'title',
          label: 'Vehicle',
          tdClass: 'py-2 align-middle',
          sortable: true
        },
        {
          key: 'stockNumber',
          label: 'Stock #',
          tdClass: 'py-2 align-middle',
          sortable: true
        },
        {
          key: 'age',
          label: 'Age',
          tdClass: 'py-2 align-middle',
          sortable: true
        },
        {
          key: 'galleryDescription',
          label: 'Description',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'actions',
          label: 'Actions',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    sortBy: {
      get () {
        return this.sortField
      },
      set () {}
    },
    sortDesc: {
      get () {
        return this.sortOrderDesc
      },
      set () {}
    }
  },
  methods: {
    onSortChanged (value) {
      this.$emit('sortChanged', value)
    },
    getVehiclePhotoSrc (item) {
      return (item.presentationPhoto || {}).photo107 + '?q=' + item.actualPhotosDateTimeEdited
    },
    getDetailsPath (item, hasToAppendTab) {
      return `/inventory/${item.accountId}/edit/${item.vin}?tabindex=3`
    },
    hasGalleryDescription (item) {
      let div = document.createElement('div')
      div.innerHTML = item.galleryDescription
      return div.textContent.trim() !== ''
    }
  }
}
</script>

<style lang="scss" scoped>
  .zoomeds {
    font-size: 2.2rem;
  }
</style>
