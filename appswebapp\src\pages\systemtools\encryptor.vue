<template>
  <div>
    <h4>eBizAutos Rijndael Encryptor</h4>
    <div>
      <label for="to-encrypt-rijndael">To encrypt</label>
      <b-textarea id="to-encrypt-rijndael" rows="4" v-model="rijndael.encryptedString"></b-textarea>
      <br>
      <label for="to-decrypt-rijndael">To decrypt</label>
      <b-textarea id="to-decrypt-rijndael" rows="4" v-model="rijndael.decryptedString"></b-textarea>
      <br>
      <b-button variant="outline-primary" @click="encryptRijndael">Encrypt</b-button>
      <b-button variant="outline-primary" @click="decryptRijndael">Decrypt</b-button>
    </div>
    <br>
    <h4>eBizAutos TripleDES Encryptor</h4>
    <div>
      <label for="to-encrypt-tripledes">To encrypt</label>
      <b-textarea id="to-encrypt-tripledes" rows="4" v-model="tripleDes.encryptedString"></b-textarea>
      <br>
      <label for="to-decrypt-tripledes">To decrypt</label>
      <b-textarea id="to-decrypt-tripledes" rows="4" v-model="tripleDes.decryptedString"></b-textarea>
      <br>
      <b-button variant="outline-primary" @click="encryptTripleDes">Encrypt</b-button>
      <b-button variant="outline-primary" @click="decryptTripleDes">Decrypt</b-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'encryptor',
  metaInfo: {
    title: 'eBizAutos Encryptor'
  },
  data () {
    return {
      rijndael: {
        encryptedString: '',
        decryptedString: ''
      },
      tripleDes: {
        encryptedString: '',
        decryptedString: ''
      }
    }
  },
  methods: {
    async encryptRijndael () {
      const params = {input: this.rijndael.encryptedString}
      try {
        const result = await this.$store.dispatch('systemTools/rijndaelEncrypt', params)
        this.rijndael.decryptedString = result.data
      } catch (err) {
        if (err.response && err.response.data) {
          this.$toaster.error(err.response.data, { timeout: 8000 })
        } else {
          this.$toaster.error('An error occured. Please try again later.', { timeout: 8000 })
        }
        console.error(err, 'Error occured while encrypting with Rijndael', params)
      }
    },
    async decryptRijndael () {
      const params = {input: this.rijndael.decryptedString}
      try {
        const result = await this.$store.dispatch('systemTools/rijndaelDecrypt', params)
        this.rijndael.encryptedString = result.data
      } catch (err) {
        if (err.response && err.response.data) {
          this.$toaster.error(err.response.data, { timeout: 8000 })
        } else {
          this.$toaster.error('An error occured. Please try again later.', { timeout: 8000 })
        }
        console.error(err, 'Error occured while decrypting with Rijndael', params)
      }
    },
    async encryptTripleDes () {
      const params = {input: this.tripleDes.encryptedString}
      try {
        const result = await this.$store.dispatch('systemTools/tripledesEncrypt', params)
        this.tripleDes.decryptedString = result.data
      } catch (err) {
        if (err.response && err.response.data) {
          this.$toaster.error(err.response.data, { timeout: 8000 })
        } else {
          this.$toaster.error('An error occured. Please try again later.', { timeout: 8000 })
        }
        console.error(err, 'Error occured while encrypting with TripleDes', params)
      }
    },
    async decryptTripleDes () {
      const params = {input: this.tripleDes.decryptedString}
      try {
        const result = await this.$store.dispatch('systemTools/tripledesDecrypt', params)
        this.tripleDes.encryptedString = result.data
      } catch (err) {
        if (err.response && err.response.data) {
          this.$toaster.error(err.response.data, { timeout: 8000 })
        } else {
          this.$toaster.error('An error occured. Please try again later.', { timeout: 8000 })
        }
        console.error(err, 'Error occured while decrypting with TripleDes', params)
      }
    }
  }
}
</script>
