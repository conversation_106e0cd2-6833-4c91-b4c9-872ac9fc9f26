import axios from 'axios'

// const state = {
//   campaign: null
// }

// const getters = {
//   campaign: state => state.campaign
// }

// const mutations ={
//   setCampaign(state, campaign){
//     state.campaign = campaign
//   }
// }

const actions = {
  getGlobalDashboard (_, parameters) {
    return axios.get('/api/craigslist/dashboard', { params: parameters.filter })
  },
  getAccounts (_, paramaters) {
    return axios.get('/api/craigslist/dashboard/accounts', { params: paramaters.filter })
  },
  getAccountDashboard (_, parameters) {
    return axios.get(`/api/craigslist/dashboard/${parameters.accountId}`, { params: parameters.filter })
  },
  getDailytotals (_, parameters) {
    return axios.get('/api/craigslist/dashboard/dailytotals', { params: parameters.filter })
  },
  getAccountDailyTotals (_, parameters) {
    return axios.get(`/api/craigslist/dashboard/${parameters.accountId}/dailytotals`, { params: parameters.filter })
  },
  getTotalSummary (_, parameters) {
    return axios.get('/api/craigslist/dashboard/summary', { params: parameters.filter })
  },
  getAccountTotalSummary (_, parameters) {
    return axios.get(`/api/craigslist/dashboard/${parameters.accountId}/summary`, { params: parameters.filter })
  },
  getCampaignsCategories (_, parameters) {
    return axios.get(`/api/craigslist/dashboard/${parameters.accountId}/campaigns/categories`)
  },
  async fetchCampaign (_, parameters) {
    return axios.get(`/api/craigslist/dashboard/${parameters.accountId}/campaigns/${parameters.campaignId}`)
  },
  getAccountInventory (_, parameters) {
    return axios.get(`/api/craigslist/inventory/${parameters.accountId}`)
  },
  getLogs (_, parameters) {
    return axios.get(`/api/craigslist/logs/${parameters.accountId}`, { params: parameters.filter })
  },
  getDetailLog (_, parameters) {
    return axios.get(`/api/craigslist/logs/${parameters.accountId}/${parameters.guid}`)
  },
  getUserActivity (_, parameters) {
    return axios.get('/api/craigslist/useractivity', { params: parameters.filter })
  },
  getUserActivityLogDetails (_, parameters) {
    return axios.get(`/api/craigslist/useractivity/${parameters.id}/details`)
  },
  getAccountsActive (_, parameters) {
    return axios.get(`/api/craigslist/ads/accountsactive/${parameters.accountId}`, { params: parameters.filter })
  },
  getAccountsInactive (_, parameters) {
    return axios.get(`/api/craigslist/ads/accountsinactive/${parameters.accountId}`, { params: parameters.filter })
  },
  updateCampaign (_, parameters) {
    return axios.put(`/api/craigslist/dashboard/${parameters.accountId}/campaigns/${parameters.campaignId}`, parameters.item)
  },
  createCampaign (_, parameters) {
    return axios.post(`/api/craigslist/dashboard/${parameters.accountId}/campaigns/`, parameters.item)
  },
  deleteCampaign (_, parameters) {
    return axios.delete(`/api/craigslist/dashboard/${parameters.accountId}/campaigns/${parameters.id}`)
  },
  deleteDelayedJob (_, parameters) {
    return axios.delete(`/api/craigslist/inventory/${parameters.accountId}/deletejob/${parameters.id}`)
  },
  changeAccountPostEnabling (_, parameters) {
    return axios.post(`/api/craigslist/dashboard/${parameters.accountId}/settings/change_posting_status`, parameters.data)
  },
  pingPostVehicleToCraigsList (_) {
    return axios.get(`/api/craigslist/ping/cl/post`)
  }
}

export default {
  namespaced: true,
  actions: actions
}
