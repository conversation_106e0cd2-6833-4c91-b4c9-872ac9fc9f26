<template>
  <b-row class="color-picker mobile-adaptive">
    <b-col class="fixed-width d-flex">
      <b-dropdown variant="link" class="color-picker-block__color-dropdown" size="xs" no-caret no-flip>
        <template slot="button-content">
          <span class="ui-product-color align-middle m-0" :style="{ 'background-color': '#' + colorValue.hexCode }"></span>
        </template>

        <template v-if="hasStyleColors">
          <b-dropdown-header >Style Colors</b-dropdown-header>
          <b-dropdown-item-button v-for="i in options.styleColors" :key="'styleColors' + i.text + i.hexCode" @click="onClick(i)" :active="i.text === colorValue.text">
            <span class="ui-product-color align-middle m-0" :style="{ 'background-color': '#' + i.hexCode }"></span>
            <span class="pl-3">{{i.text}}</span>
          </b-dropdown-item-button>
        </template>

        <b-dropdown-header>Standard Colors</b-dropdown-header>
        <b-dropdown-item-button v-for="i in options.standardColors" :key="'standardColors' + i.text + i.hexCode" @click="onClick(i)" :active="i.text === colorValue.text">
          <span class="ui-product-color align-middle m-0" :style="{ 'background-color': '#' + i.hexCode }"></span>
          <span class="pl-3">{{i.text}}</span>
        </b-dropdown-item-button>
      </b-dropdown>

      <b-form-input :value="colorValue.text"  @input="onTextInput" class="flex-grow flex-shrink"></b-form-input>
    </b-col>
  </b-row>
</template>

<script>

let searchTimerId = 0

export default {
  props: {
    name: String,
    colorValue: {},
    options: {}
  },
  computed: {
    hasStyleColors () {
      return this.options.styleColors.length > 0
    }
  },
  data () {
    return {
      inputValue: ''
    }
  },
  methods: {
    onTextInput (newVal) {
      this.inputValue = newVal

      clearTimeout(searchTimerId)

      searchTimerId = setTimeout(() => {
        this.$emit('colorNameChanged', newVal)
      }, 500)
    },
    onClick (newVal) {
      this.$emit('colorChanged', newVal)
    }
  }
}
</script>

<style>
.color-picker .change-input {
  text-decoration: underline;
  color: #ca1713;
  white-space: nowrap;
  cursor: pointer;
  align-self: center;
}
.color-picker-block__color-dropdown {
  max-height: 38px;
  max-width: 38px;
  border: 1px solid rgba(24, 28, 33, 0.1);
  border-radius: 0.25rem;
  margin-right: 5px;
  padding: 0 2px;
}

.color-picker-block__color-dropdown button {
  line-height: 1;
}

.ui-product-color {
  line-height: 1rem;
}

.color-picker-block__color-dropdown .dropdown-menu .dropdown-item:active {
  background-color: transparent;
}

.fixed-width {
  flex: none;
  width: 20.25rem;
}

.mobile-adaptive {
  flex-wrap: nowrap;
}

.color-picker .dropdown-menu.show {
  min-width: auto;
}

  @media (max-width:576px) {
    .fixed-width {
      flex-basis: 0;
      flex-grow: 1;
      width: auto;
    }
    .mobile-adaptive {
      width: 100%;
    }
  }
  .flex-shrink {
    flex-shrink: 5;
  }
</style>
