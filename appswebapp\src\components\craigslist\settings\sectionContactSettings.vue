<template>
<editSettingsHelper title="Contact Settings" @save="saveSettings" @cancel="cancel" @changeMode="changeMode" :isDisabled="isDisabled" :isLoading="isUpdatingProcessing" :isViewMode="isViewMode">
  <div slot="settings-content">
    <b-form-checkbox class="flex-inline-sized status-incoming-inv my-2" v-model="data.hasToUseCustomEmail" :disabled="isViewMode">
      Use Custom Email
    </b-form-checkbox>
    <detail-row :fixed-payload-width="true" v-if="data.hasToUseCustomEmail">
      <span slot="title">
        Custom Email:
      </span>
      <div slot="payload" class="w-100">
        <ValidationProvider name="Email" rules="required|email" v-slot="{errors}">
          <span v-if="isViewMode" size="sm">{{data.customEmail}}</span>
          <b-form-input v-else name='CustomEmail' size="sm" type='email' :state="errors[0] ? false : null" v-model='data.customEmail'></b-form-input>
          <span class='text-danger'>{{ errors[0] }}</span>
        </ValidationProvider>
      </div>
    </detail-row>
    <detail-row :fixed-payload-width="true" v-if="data.hasToUseCustomEmail">
      <span slot="title">
        Email Display Type:
      </span>
      <span v-if="isViewMode" slot="payload" size="sm">{{getEmailDisplayTypeDescription()}}</span>
      <multiselect v-else size="sm" slot="payload" :allowEmpty='false' :disabled='!data.hasToUseCustomEmail' v-model='emailDisplayTypeSelected' :options='emailDisplayTypeOptions'  label='text' :multiple="false" :preselect-first="true"></multiselect>
    </detail-row>
    <b-form-checkbox class="flex-inline-sized status-incoming-inv my-2" v-model="data.hasToUseCustomPhone" :disabled="isViewMode">
      Use Custom Phone
    </b-form-checkbox>
    <detail-row :fixed-payload-width="true" v-if="data.hasToUseCustomPhone">
      <span slot="title">
        Custom Phone:
      </span>
      <span v-if="isViewMode" slot="payload" size="sm">{{data.customPhone}}</span>
      <phone-input
        v-else size="sm"
        slot="payload"
        v-model='data.customPhone'
        customvalidate
        @isPhoneValid='onCustomPhoneValidate'
        :disabled='!data.hasToUseCustomPhone'>
      </phone-input>
    </detail-row>
    <detail-row :fixed-payload-width="true" v-if="data.hasToUseCustomPhone">
      <span slot="title">
        Phone Extension:
      </span>
      <span v-if="isViewMode" slot="payload" size="sm">{{data.phoneExtension}}</span>
      <b-form-input v-else size="sm" slot="payload" type='number' v-model='data.phoneExtension' :disabled='!data.hasToUseCustomPhone'></b-form-input>
    </detail-row>
    <detail-row :fixed-payload-width="true" v-if="data.hasToUseCustomPhone">
      <span slot="title">
        Phone Display Type:
      </span>
      <span v-if="isViewMode" slot="payload" size="sm">{{getPhoneDisplayTypeDescription()}}</span>
      <multiselect v-else size="sm" slot="payload" :allowEmpty='false' :disabled='!data.hasToUseCustomPhone' v-model='phoneDisplayTypeSelected' :preselect-first="true" :options='phoneDisplayTypeOptions' label='text' :multiple="false"></multiselect>
    </detail-row>
  </div>
</editSettingsHelper>
</template>

<script>
import { mapGetters } from 'vuex'
import Multiselect from 'vue-multiselect'
import CraigslistDescriptionHelper from '@/shared/craigslist/craigslistDescriptionHelper'
import phoneInput from '@/components/_shared/phoneInput'
import detailRow from '@/components/details/helpers/detailRow'
import globals from '../../../globals'
import editSettingsHelper from '../../_shared/editSettingsHelper.vue'

const craigslistDescriptionHelper = new CraigslistDescriptionHelper()

export default {
  name: 'section-custom-phone',
  props: {
    isDisabled: {
      type: Boolean,
      required: true
    },
    isUpdatingProcessing: {
      type: Boolean,
      required: true
    }
  },
  created () {
    this.populateData()
  },
  data () {
    return {
      isViewMode: true,
      isCustomPhoneValid: true,
      phoneDisplayTypeOptions: craigslistDescriptionHelper.getPhoneDisplayTypeOptions(),
      phoneDisplayTypeSelected: null,
      countriesAllowed: ['US'],
      emailDisplayTypeOptions: craigslistDescriptionHelper.getEmailDisplayTypeOptions(),
      emailDisplayTypeSelected: null,
      data: {
        hasToUseCustomPhone: false,
        customPhone: '',
        phoneExtension: '',
        phoneDisplayType: 0,
        hasToUseCustomEmail: false,
        customEmail: '',
        emailDisplayType: 0
      }
    }
  },
  components: {
    'multiselect': Multiselect,
    'phone-input': phoneInput,
    'detail-row': detailRow,
    editSettingsHelper
  },
  computed: {
    ...mapGetters('craigslistSettings', ['settingsPutData'])
  },
  methods: {
    onCustomPhoneValidate (isValid) {
      this.isCustomPhoneValid = isValid
    },
    changeMode (newMode) {
      this.isViewMode = newMode
    },
    saveSettings () {
      if (!this.data.hasToUseCustomPhone || this.isCustomPhoneValid) {
        this.isViewMode = true
        this.data.phoneDisplayType = this.phoneDisplayTypeSelected.value
        this.data.emailDisplayType = this.emailDisplayTypeSelected.value
        this.$store.commit('craigslistSettings/setContactSettings', this.data)
        this.putSettingsData()
        this.populateData()
      }
    },
    cancel () {
      this.isViewMode = true
      this.populateData()
    },
    putSettingsData () {
      this.$emit('putSettingsData', this.settingsPutData)
    },
    populateData () {
      this.data.hasToUseCustomPhone = globals().getClonedValue(this.settingsPutData.hasToUseCustomPhone)
      this.data.customPhone = globals().getClonedValue(this.settingsPutData.customPhone)
      this.data.phoneDisplayType = globals().getClonedValue(this.settingsPutData.phoneDisplayType)
      this.data.phoneExtension = globals().getClonedValue(this.settingsPutData.phoneExtension)
      this.phoneDisplayTypeSelected = this.phoneDisplayTypeOptions.find(x => +x.value === this.settingsPutData.phoneDisplayType) || {value: null, text: 'Not Selected'}
      this.data.hasToUseCustomEmail = globals().getClonedValue(this.settingsPutData.hasToUseCustomEmail)
      this.data.customEmail = globals().getClonedValue(this.settingsPutData.customEmail)
      this.emailDisplayTypeSelected = this.emailDisplayTypeOptions.find(x => +x.value === this.settingsPutData.emailDisplayType) || {value: null, text: 'Not Selected'}
    },
    getPhoneDisplayTypeDescription () {
      return craigslistDescriptionHelper.getPhoneDisplayTypeDescription(this.data.phoneDisplayType)
    },
    getEmailDisplayTypeDescription () {
      return craigslistDescriptionHelper.getEmailDisplayTypeDescription(this.settingsPutData.emailDisplayType)
    }
  }
}
</script>

<style scoped>
 @media(min-width: 1600px) {
    .custom-settings-craigslist {
      width: 25%;
    }
 }

 @media(max-width: 1600px) {
    .custom-settings-craigslist {
      width: 30%;
    }
 }

 @media(max-width: 1200px) {
    .custom-settings-craigslist {
      width: 40%;
    }
 }

 @media(max-width: 800px) {
    .custom-settings-craigslist {
      width: 50%;
    }
 }
 @media(max-width: 400px) {
    .custom-settings-craigslist {
      width: 100%;
    }
 }
</style>
