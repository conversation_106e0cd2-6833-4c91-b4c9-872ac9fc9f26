<template>
  <div>
    <div class="border-bottom">
      <b-row>
        <b-col xs="12" sm="6" md="6" lg="6" xl="6" class="m-0"><h6 class="float-left">Page SEO Settings</h6></b-col>
      </b-row>
    </div>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Status</span>
      <label slot="payload" class="switcher">
          <input type="checkbox" v-model="customSeoSettingsChecked" class="switcher-input">
          <span class="switcher-indicator">
            <span class="switcher-yes"></span>
            <span class="switcher-no"></span>
          </span>
          <span class="switcher-label">Custom</span>
      </label>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Browser Page Title:</span>
      <b-form-input v-model="seoSettings.title" slot="payload" :disabled="!customSeoSettingsChecked"></b-form-input>
    </detail-row>
    <detail-row title-position="start" :fixed-payload-width="true">
      <span slot="title">Meta Description:</span>
      <b-form-textarea v-model="seoSettings.description" slot="payload" rows="10" :disabled="!customSeoSettingsChecked"></b-form-textarea>
    </detail-row>
    <detail-row title-position="start" :fixed-payload-width="true">
      <span slot="title">Meta keywords:</span>
      <b-form-textarea v-model="seoSettings.keywords" slot="payload" rows="4" :disabled="!customSeoSettingsChecked"></b-form-textarea>
    </detail-row>
  </div>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'

export default {
  props: {
    seoSettings: { type: Object, required: true },
    customSeoSettings: { type: Boolean, required: true }
  },
  components: {
    'detail-row': detailRow
  },
  computed: {
    customSeoSettingsChecked: {
      get () {
        return this.customSeoSettings
      },
      set (value) {
        this.$emit('customSeoSettingsChanged', value)
      }
    }
  }
}
</script>
