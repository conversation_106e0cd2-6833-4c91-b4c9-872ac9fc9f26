<template>
  <account-listing :application-type="applicationTypes.Undefined.Id" :has-actions="true"></account-listing>
</template>

<script>
import accountlisting from '../../components/_shared/applicationAccountListing/applicationAccountListing'
import applicationTypes from '../../shared/common/applicationTypes'

export default {
  name: 'accounts-listing',
  data () {
    return {
      applicationTypes
    }
  },
  metaInfo: {
    title: 'Account Listing'
  },
  components: {
    'account-listing': accountlisting
  }
}
</script>
