<template>
  <div v-if="!isLoading && !isExceptionOccurred">
    <h4 v-if="type != reviseOptions.modifyVehicleDescription.key">Revise eBay Listing</h4>
    <b-row>
      <b-col>
        <vehicle-description v-if="type != reviseOptions.modifyVehicleDescription.key" class="mb-2" :vehicle="revise"></vehicle-description>
      </b-col>
    </b-row>
    <b-row>
      <b-col>
        <b-btn variant="primary" v-if="type !== reviseOptions.allOptions.key && type != reviseOptions.modifyVehicleDescription.key" class="float-right mb-2" @click="onBackToReviseOptions" size="sm">Back To Revise Options</b-btn>
      </b-col>
    </b-row>
    <b-card v-if="type === reviseOptions.allOptions.key">
      <revise-listing @selectRevise="selectRevise" :auctionId="auctionId"></revise-listing>
      <ebay-listing-status/>
    </b-card>
    <div v-else>
      <revise-option :type="type" :model="revise"></revise-option>
    </div>
  </div>
  <div v-else-if="isLoading && !isExceptionOccurred">
    <loader size="lg"/>
  </div>
  <div v-else>
    <error-alert/>
  </div>
</template>

<script>
import vehicleDescription from '@/components/eBay/helpers/vehicleDescription'
import reviseListing from '@/components/eBay/revise/reviseListing'
import eBayListingStatus from '@/components/eBay/revise/eBayListingStatus'
import reviseOption from '@/components/eBay/revise/reviseOption'
import constants from '@/shared/ebay/constants'
import loader from '@/components/_shared/loader'
import {mapGetters} from 'vuex'

export default {
  name: 'ebay-revise',
  props: {
    accountId: { type: Number, required: true },
    auctionId: { type: Number, required: true }
  },
  metaInfo: {
    title: 'eBay Revise'
  },
  data () {
    return {
      vehicle: {},
      type: constants.reviseOptions.allOptions.key,
      isLoading: true,
      isExceptionOccurred: false,
      reviseOptions: constants.reviseOptions
    }
  },
  created () {
    this.initData()
  },
  components: {
    'vehicle-description': vehicleDescription,
    'revise-listing': reviseListing,
    'ebay-listing-status': eBayListingStatus,
    'revise-option': reviseOption,
    'loader': loader
  },
  computed: {
    ...mapGetters('eBayRevise', ['revise'])
  },
  methods: {
    initData () {
      let apiParams = {
        accountId: this.accountId,
        auctionId: this.auctionId
      }
      this.$store.dispatch('eBayRevise/populateReviseData', apiParams).then(res => {
      }).catch(ex => {
        this.isExceptionOccurred = true
        this.$toaster.error('Something went wrong!')
        this.$logger.handleError(ex, 'Cannot populate revise data')
      }).finally(() => {
        this.isLoading = false
      })
    },
    selectRevise (type) {
      this.type = type
    },
    onBackToReviseOptions () {
      this.type = constants.reviseOptions.allOptions.key
    }
  }
}
</script>
