<template>
  <div>
    <traffic-sources-summary
      :pieSessionsItems="bar.pieSessionsItems"
      :pieLeadsItems="bar.pieLeadsItems"
    ></traffic-sources-summary>

    <div v-if="isAccountLevel">
      <account-level-card v-if="isAccountLevel" @backToGroup="onBackToGroup">
        {{accountName}} ({{page.filter.accountId}})
      </account-level-card>

      <traffic-sources-by-source-table
        :tableItems="table.items"
        :totalItems="table.totalItems"
        :pageNumber="page.filter.pageNumber"
        :pageSize="page.filter.pageSize"
        :sortType="page.filter.sortType"
        @pageNumberChanged="onPageNumberChanged"
        @pageSizeChanged="onPageSizeChanged"
        @sortTypeChanged="onSortTypeChanged"
      ></traffic-sources-by-source-table>
    </div>
    <div v-else>
      <group-report-tables-selector
        defaultSegmentName="Source"
        :isSegmentedByAccountSelected="this.page.filter.segmentedByAccount"
        @reportTablesSelectorChanged="onReportTablesSelectorChanged"
      ></group-report-tables-selector>

      <traffic-sources-by-source-table
        v-if="!page.filter.segmentedByAccount"
        :tableItems="table.items"
        :totalItems="table.totalItems"
        :pageNumber="page.filter.pageNumber"
        :pageSize="page.filter.pageSize"
        :sortType="page.filter.sortType"
        @pageNumberChanged="onPageNumberChanged"
        @pageSizeChanged="onPageSizeChanged"
        @sortTypeChanged="onSortTypeChanged"
      ></traffic-sources-by-source-table>

      <group-traffic-sources-by-account-table
        v-else-if="page.filter.segmentedByAccount"
        :tableItems="table.items"
        :totalItems="table.totalItems"
        :pageNumber="page.filter.pageNumber"
        :pageSize="page.filter.pageSize"
        :sortType="page.filter.sortType"
        @pageNumberChanged="onPageNumberChanged"
        @pageSizeChanged="onPageSizeChanged"
        @sortTypeChanged="onSortTypeChanged"
        @accountNameClicked="onAccountNameClicked"
      ></group-traffic-sources-by-account-table>
    </div>
  </div>
</template>

<style src="@/vendor/styles/pages/analytics.scss" lang="scss"></style>

<script>
import analyticsConstants from './../../../shared/analytics/constants'
import analyticsBuilders from './../../../shared/analytics/builders'
import analyticsHelper from '../helpers.js'
import baseGroupReportPage from './baseGroupReportPage.js'

import TrafficSourcesSummary from '../../../components/analytics_ga4/summaries/trafficSourcesSummary'
import AccountLevelCard from '../../../components/analytics_ga4/accountLevelCard'
import TrafficSourcesBySourceTable from '../../../components/analytics_ga4/tables/trafficSourcesBySourceTable'
import GroupReportTablesSelector from '../../../components/analytics_ga4/groupReportTablesSelector'
import GroupTrafficSourcesByAccountTable from '../../../components/analytics_ga4/tables/groupTrafficSourcesByAccountTable'

const filterManager = analyticsBuilders.getFilterManager({
  accountId: { type: Number, default: 0 },
  segmentedByAccount: { type: Boolean, default: false },
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  pageNumber: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  sortType: { type: Number, default: analyticsConstants.trafficSourceSortTypes.sessionsDesc }
})

export default {
  mixins: [baseGroupReportPage],
  name: 'group-traffic-sources',
  metaInfo: {
    title: 'Analytics - Traffic Sources'
  },
  components: {
    GroupTrafficSourcesByAccountTable,
    GroupReportTablesSelector,
    TrafficSourcesBySourceTable,
    AccountLevelCard,
    TrafficSourcesSummary
  },
  created () {
    this.$store.commit('analyticsGa4/setAnalyticsName', 'Traffic Sources')
    this.$store.commit('analyticsGa4/setBlackThemeOn', true)
  },
  data () {
    return {
      page: {
        filter: filterManager.defaultValue
      },
      bar: {
        pieSessionsItems: [],
        pieLeadsItems: []
      }
    }
  },
  computed: {
    filterManager () {
      return filterManager
    },
    defaultAccountLevelSortType () {
      return filterManager.defaultValue.sortType
    }
  },
  methods: {
    getAccountNameSortTypeCondition () {
      return this.page.filter.sortType === analyticsConstants.trafficSourceSortTypes.accountNameAsc ||
        this.page.filter.sortType === analyticsConstants.trafficSourceSortTypes.accountNameDesc
    },
    getReportTablesSelectorSortTypeCondition () {
      return this.page.filter.sortType === analyticsConstants.trafficSourceSortTypes.accountNameAsc ||
        this.page.filter.sortType === analyticsConstants.trafficSourceSortTypes.accountNameDesc ||
        this.page.filter.sortType === analyticsConstants.trafficSourceSortTypes.channelGroupingAsc ||
        this.page.filter.sortType === analyticsConstants.trafficSourceSortTypes.channelGroupingDesc ||
        this.page.filter.sortType === analyticsConstants.trafficSourceSortTypes.sourceAsc ||
        this.page.filter.sortType === analyticsConstants.trafficSourceSortTypes.sourceDesc
    },
    async updateStatistics () {
      try {
        await Promise.all([
          !this.isAccountLevel ? this.updateGroupLevelGraph() : null,
          !this.isAccountLevel && this.page.filter.segmentedByAccount ? this.updateGroupLevelTableByAccount() : null,
          !this.isAccountLevel && !this.page.filter.segmentedByAccount ? this.updateGroupLevelTableBySource() : null,
          this.isAccountLevel ? this.updateAccountLevelGraph() : null,
          this.isAccountLevel ? this.updateAccountLevelTable() : null
        ])
      } catch (err) {
        this.$toaster.error('Failed to get data from server', { timeout: 8000 })
        this.$logger.handleError(err, 'Can\'t update statistics', { filter: this.page.filter, cache: this.cache })
      }
    },
    async updateGroupLevelGraph () {
      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getGroupTrafficSourcesGraph',
        {
          reportGroupId: this.reportGroupId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo,
          count: 10
        }
      )

      this.bar.pieSessionsItems = store.graph.data.sessionsbysource
      this.bar.pieLeadsItems = store.graph.data.totalleadsbysource
    },
    async updateAccountLevelGraph () {
      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getTrafficSourcesGraph',
        {
          accountId: this.page.filter.accountId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo,
          count: 10
        }
      )

      this.bar.pieSessionsItems = store.graph.data.sessionsbysource
      this.bar.pieLeadsItems = store.graph.data.totalleadsbysource
    },
    async updateGroupLevelTableBySource () {
      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getGroupTrafficSourcesDetailsPageBySource',
        {
          reportGroupId: this.reportGroupId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo,
          pageNumber: this.page.filter.pageNumber,
          pageSize: this.page.filter.pageSize,
          sortType: this.page.filter.sortType
        }
      )

      this.table.items = store.detailedData.data.items
      this.table.totalItems = store.detailedData.data.totalItems
    },
    async updateGroupLevelTableByAccount () {
      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getGroupTrafficSourcesDetailsPageByAccount',
        {
          reportGroupId: this.reportGroupId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo,
          pageNumber: this.page.filter.pageNumber,
          pageSize: this.page.filter.pageSize,
          sortType: this.page.filter.sortType
        }
      )

      this.table.items = store.detailedData.data.items.map(x => {
        x.account.isAccessAllowed = this.isAccessToAccountAllowed(x.account.accountId)
        return x
      })
      this.table.totalItems = store.detailedData.data.totalItems
    },
    async updateAccountLevelTable () {
      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getTrafficSourcesDetailsPage',
        {
          accountId: this.page.filter.accountId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo,
          pageNumber: this.page.filter.pageNumber,
          pageSize: this.page.filter.pageSize,
          sortType: this.page.filter.sortType
        }
      )

      this.table.items = store.detailedData.data.items
      this.table.totalItems = store.detailedData.data.totalItems
    }
  }
}
</script>
