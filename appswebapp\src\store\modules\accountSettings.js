import axios from 'axios'

export default {
  namespaced: true,
  state: {
    accountSettings: {}
  },
  mutations: {
    setAccountSettings (state, { accountId, data }) {
      state.accountSettings[accountId] = data
    }
  },
  actions: {
    async getAccountSettings ({ state, commit }, accountId) {
      if (state.accountSettings[accountId]) {
        return state.accountSettings[accountId]
      }

      const result = await axios.get(`/api/accounts/${accountId}/settings`)

      commit('setAccountSettings', {
        accountId,
        data: result.data
      })

      return result.data
    },
    async getBasicSettings (_, accountId) {
      const result = await axios.get(`/api/accounts/${accountId}/basicsettings`)
      return result.data
    },
    async updateAccountSettings ({ state }, accountSettings) {
      await axios.post(`/api/accounts/${accountSettings.accountId}/settings`, accountSettings)
      state.accountSettings = {}
    }
  }
}
