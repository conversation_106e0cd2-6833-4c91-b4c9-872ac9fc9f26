import permissions from '../shared/common/permissions'
import applicationTypes from '../shared/common/applicationTypes'

const siteManagementDefaultMeta = {
  applicationType: applicationTypes.SiteManagement.Id,
  permissions: [permissions.SMViewSettings],
  applicationFullAccess: permissions.SMFullAccess
}

export default [{
  path: '/website',
  meta: {
    ...siteManagementDefaultMeta
  },
  component: () => import('@/layout/Layout2'),
  props: (route) => ({ accountId: +route.params.accountId }),
  children: [
    {
      path: '',
      meta: {
        ...siteManagementDefaultMeta,
        permissions: permissions.SMFullAccess
      },
      name: 'website-accounts-listing',
      component: () => import('@/pages/website/accounts')
    },
    {
      path: 'useractivity',
      meta: {
        ...siteManagementDefaultMeta,
        permissions: permissions.SMFullAccess
      },
      name: 'website-user-activity',
      component: () => import('@/pages/website/userActivity')
    },
    {
      path: 'useractivity/:id/details',
      meta: {
        ...siteManagementDefaultMeta,
        permissions: permissions.SMFullAccess
      },
      name: 'website-user-activity-details',
      props: (route) => ({ id: route.params.id }),
      component: () => import('@/pages/website/userActivityDetails')
    },
    {
      path: ':accountId(\\d+)',
      meta: {
        ...siteManagementDefaultMeta
      },
      component: () => import('@/pages/website/layout'),
      props: (route) => ({ accountId: +route.params.accountId }),
      redirect: {
        name: 'website-page-builder'
      },
      children: [
        {
          path: 'pagebuilder',
          name: 'website-page-builder',
          meta: {
            ...siteManagementDefaultMeta
          },
          component: () => import('@/pages/website/pageBuilder'),
          props: (route) => ({ accountId: +route.params.accountId })
        },
        {
          path: 'page/:navId(\\d+)/edit',
          name: 'website-edit-page',
          meta: {
            ...siteManagementDefaultMeta
          },
          component: () => import('@/pages/website/editPage'),
          props: (route) => ({ accountId: +route.params.accountId, navId: +route.params.navId })
        }
      ]
    }
  ]
}]
