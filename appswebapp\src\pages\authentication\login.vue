<template>
<div>
  <div class="authentication-wrapper authentication-1 px-1">
    <div class="authentication-inner py-4">

      <!-- Logo -->
      <img id="login-logo" src="/static/img/logo-ebizautos-login.svg" alt="eBizAutos Login Logo">
      <!-- / Logo -->

      <!-- Form -->
      <form class="my-4">
        <b-alert show variant="danger" v-if="!!rejectionMessage">{{rejectionMessage}}</b-alert>
        <b-form-group>
          <b-input v-model="credentials.name" placeholder="Username" autofocus tabindex="1" size="lg" />
        </b-form-group>
        <b-form-group>
          <b-input :type="showPassword ? 'text' : 'password'" v-model="credentials.password" placeholder="Password" tabindex="2" size="lg" />
        </b-form-group>
        <b-form-group class="mb-3">
          <b-form-checkbox v-model="showPassword" tabindex="4">Show Password</b-form-checkbox>
        </b-form-group>

         <!-- Turnstile Widget -->
         <div class="turnstile-wrapper"><div ref="turnstile"></div></div>

        <div class="d-flex justify-content-end align-items-end m-0">
          <b-btn type="submit" @click.prevent="logInUser" variant="primary" :disabled="isLoginLocked" tabindex="3" class="btn-block btn-lg">Sign In</b-btn>
        </div>

        <!-- Additional Links -->
        <div class="d-flex justify-content-between align-items-center mt-3">
          <span class="small policy-links"><a href="https://cp.ebizautos.com/forgot-password/">Forgot Your Password?</a></span>
          <span class="text-muted small policy-links">Don't have an account? <a href="http://www.ebizautos.com/free-demo/">Sign Up</a></span>
        </div>
      </form>
      <!-- / Form -->
    </div>
  </div>
  <app-layout-footer/>
</div>
</template>

<!-- Page -->
<style src="@/vendor/styles/pages/authentication.scss" lang="scss"></style>

<script>
import LayoutFooter from '../../layout/LayoutFooter.vue'

export default {
  name: 'authentication-login',
  metaInfo: {
    title: 'Login'
  },
  data: () => ({
    credentials: {
      name: '',
      password: '',
      rememberMe: false,
      turnstileToken: ''
    },
    rejectionMessage: '',
    isLoginLocked: false,
    showPassword: false,
    turnstileSiteKey: '0x4AAAAAAA1U51KhItGahHlo',
    turnstileWidget: null
  }),
  components: {
    'app-layout-footer': LayoutFooter
  },
  methods: {
    initTurnstile () {
      // Make sure turnstile is loaded
      if (window.turnstile) {
        this.turnstileWidget = window.turnstile.render(this.$refs.turnstile, {
          sitekey: this.turnstileSiteKey,
          size: 'flexible',
          callback: (token) => {
            this.credentials.turnstileToken = token
          }
        })
      } else {
        // If not loaded yet, wait and try again
        setTimeout(() => this.initTurnstile(), 100)
      }
    },
    logInUser () {
      // Clear previous errors
      this.rejectionMessage = ''
      // Field validation
      if (!this.credentials.name.trim()) {
        this.rejectionMessage = 'Please enter your username'
        return
      }
      if (!this.credentials.password.trim()) {
        this.rejectionMessage = 'Please enter your password'
        return
      }
      // if (!this.credentials.turnstileToken) {
      //   this.rejectionMessage = 'Please complete the security verification'
      //   return
      // }
      const userCredentials = {
        name: this.credentials.name,
        password: this.credentials.password,
        turnstileToken: this.credentials.turnstileToken
      }
      this.isLoginLocked = true
      this.$store.dispatch('authentication/logIn', userCredentials)
        .then((res) => {
          this.isLoginLocked = false
          if (res && res.isMfaRequired) {
            this.$router.push({ path: '/mfa', query: this.$route.query })
          } else {
            window.location.href = decodeURIComponent(this.$route.query.returnurl || this.$route.query.redirect || '/')
          }
        })
        .catch((reason) => {
          const errorMessage = (((reason || {}).response || {}).data || '')
          this.isLoginLocked = false
          this.rejectionMessage = (errorMessage.length > 0 && errorMessage.length < 80) ? errorMessage : 'Login failed'
          // Reset Turnstile on error
          if (this.turnstileWidget) {
            window.turnstile.reset(this.turnstileWidget)
          }
        })
    }
  },
  mounted () {
    // Initialize Turnstile when component is mounted
    this.initTurnstile()
  },
  beforeDestroy () {
    // Cleanup Turnstile widget
    if (this.turnstileWidget) {
      window.turnstile.remove(this.turnstileWidget)
    }
  }
}
</script>

<style>
  #login-logo {
    height: 2.25rem;
    margin: 0 auto;
    display: block;
  }
  .turnstile-wrapper {
    display: flex;
    justify-content: center;
    margin: 12px 0 8px;
    width: 100%;
  }
  .turnstile-wrapper > div {
    width: 100% !important;
  }
  .policy-links a {
    color: #4E5155 !important;
    text-decoration: underline !important;
    font-weight: bold !important;
  }
</style>
