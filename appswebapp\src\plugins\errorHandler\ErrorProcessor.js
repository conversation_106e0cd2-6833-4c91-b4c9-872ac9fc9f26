import ErrorService from '@/services/_shared/ErrorService'

class ErrorProcessor {
  constructor () {
    this.errors = []
    this.inProcessCount = 0
    this.errorApiService = new ErrorService()
    setInterval(() => this.processError(), 500)
  };

  addError (exceptionRequestModel) {
    this.errors.push({
      description: exceptionRequestModel,
      count: 0
    })
  };

  async processError () {
    if (this.inProcessCount >= 5 || this.errors.length === 0) {
      return
    }

    this.inProcessCount++

    let exception
    try {
      exception = this.errors.shift()
      exception.count++
      await this.errorApiService.sendError(exception.description)
    } catch (e) {
      if (exception && exception.count < 3) {
        this.errors.push(exception)
      }
    } finally {
      this.inProcessCount--
    }
  }
}

export default ErrorProcessor
