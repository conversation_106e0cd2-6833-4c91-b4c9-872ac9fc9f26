<template>
  <div class="position-relative">
    <h4>Site Hostings</h4>
    <paging
      class="custom-paging d-none d-md-block p-0"
      :pageNumber="filter.page"
      :pageSize="filter.pageSize"
      :totalItems="itemsTotalCount"
      @numberChanged="pageChanged"
      @changePageSize="changePageSize"
    />
    <b-tabs v-model="selectedTab" class="nav-tabs-top nav-responsive-sm" no-fade>
      <b-tab v-for="(tab, index) in tabOptions" :key="index" :title="tab.title" :disabled="isLoading && selectedTab !== index">
        <b-row class="m-4">
          <b-col>
            <b-input-group>
              <b-form-input v-model="filter.search" @keydown.enter.native="applyFilter" placeholder="Site Id, Dealership Name, Host"></b-form-input>
              <b-input-group-append>
                <b-btn variant="primary" @click="applyFilter">Search</b-btn>
              </b-input-group-append>
            </b-input-group>
          </b-col>
          <b-col class="py-2 align-middle">
            <b-form-checkbox @change="applyFilter" v-model="filter.includeInactiveWebsite">Include Inactive Website</b-form-checkbox>
          </b-col>
          <b-col>
            <b-form-select v-if="!isLoadingOptions" @change="applyFilter" v-model="filter.sitebox" :options="getSiteBoxOptions"></b-form-select>
            <loader v-else size="sm"/>
          </b-col>
        </b-row>
      </b-tab>
    </b-tabs>
    <b-card v-if="!isLoading && items && items.length > 0">
      <b-table
        :items="items"
        :fields="getTableFields"
        striped
        responsive
      >
        <template #cell(manage)="data">
          <c-button size="sm" variant="primary" v-if="data.item.migrationSiteBoxId > 0" :disabled="data.item.isDisabledBtn" message="Are you sure you want to finish migration?" @confirm="finishMigration(data.item)">Finish Migration</c-button>
          <b-btn size="sm" :disabled="!siteBoxes || siteBoxes.length <= 1" v-else @click="showMigrationModal(data.item)">Start Migration</b-btn>
        </template>
      </b-table>
      <paging
        class="p-0"
        :pageNumber="filter.page"
        :pageSize="filter.pageSize"
        :totalItems="itemsTotalCount"
        titled
        pageSizeSelector
        @numberChanged="pageChanged"
        @changePageSize="changePageSize"
      />
    </b-card>
    <div v-else-if="isLoading" class="py-3">
      <loader size="lg"/>
    </div>
    <div v-else>
      <span class="text-muted">Not Found</span>
    </div>
    <b-modal
      v-if="itemToMigrate"
      title="Move Site Hosting"
      size="lg"
      :visible="isMigrationModalVisible"
      @hide="onHideMigrationModal"
    >
      <detail-row :fixed-payload-width="true">
        <span slot="title">Site Id:</span>
        <span slot="payload">{{itemToMigrate.siteId}}</span>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Dealership:</span>
        <span slot="payload">{{itemToMigrate.dealershipName}}</span>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">SiteBox:</span>
        <b-form-select slot="payload" v-model="itemToMigrate.migrationSiteBoxId" :options="getMigrationSiteBoxOptions"></b-form-select>
      </detail-row>
      <template #modal-footer>
        <b-btn size="sm" @click="onHideMigrationModal">Cancel</b-btn>
        <b-btn variant="primary" size="sm" @click="startMovingSiteHosting">Submit</b-btn>
      </template>
    </b-modal>
  </div>
</template>

<script>
import globals from '@/globals'
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import loader from '@/components/_shared/loader'
import paging from '@/components/_shared/paging'
import detailRow from '@/components/details/helpers/detailRow'
import { movingSiteHostingsTaskStatusTypes } from '@/shared/siteBoxManager/common/constants'

const defaultValues = new ObjectSchema({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  search: { type: String, default: '' },
  sitebox: { type: Number, default: 0 },
  movingsites: { type: Boolean, default: false },
  includeInactiveWebsite: { type: Boolean, default: false }
})

const queryHelper = new QueryStringHelper(defaultValues)

export default {
  metaInfo: {
    title: 'Site Hostings'
  },
  data () {
    return {
      filter: defaultValues.getObject(),
      isLoading: true,
      isLoadingOptions: true,
      isMigrationModalVisible: false,
      itemToMigrate: null,
      items: [],
      itemsTotalCount: 0,
      siteBoxes: [],
      tabOptions: [ {value: 0, title: 'All Sites'}, {value: 1, title: 'Moving Sites'} ]
    }
  },
  created () {
    this.populateSiteBoxes()
    this.populateData()
  },
  components: {
    loader,
    paging,
    detailRow
  },
  computed: {
    getTableFields () {
      return [
        {
          key: 'siteId',
          label: 'Site Id',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'dealershipName',
          label: 'Dealership',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'siteHost',
          label: 'Host Name',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'websiteStatus',
          label: 'Website Status',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'siteBoxName',
          label: 'SiteBox',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'migrationSiteBoxName',
          label: 'New SiteBox',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    getSiteBoxOptions () {
      let options = [{value: 0, text: 'All'}]
      let items = this.siteBoxes
      items.map(x => {
        options.push({
          value: x.id,
          text: x.name
        })
      })
      return options
    },
    selectedTab: {
      get () {
        if (this.filter.movingsites) {
          return 1
        } else {
          return 0
        }
      },
      set (value) {
        this.filter.movingsites = !!value
        this.isLoading = true
        this.synchronizeUrlAndReload()
      }
    },
    getMigrationSiteBoxOptions () {
      let options = []
      this.siteBoxes.filter(x => x.id !== this.itemToMigrate.siteBoxId).map(x => { options.push({value: x.id, text: x.name}) })
      return options
    }
  },
  methods: {
    applyFilter () {
      this.isLoading = true
      this.synchronizeUrlAndReload()
    },
    pageChanged (newPage) {
      this.filter.page = newPage
      this.isLoading = true
      this.synchronizeUrlAndReload()
    },
    changePageSize (newPageSize) {
      this.filter.pageSize = newPageSize
      this.filter.page = 1
      this.isLoading = true
      this.synchronizeUrlAndReload()
    },
    synchronizeUrlAndReload () {
      queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.populateData()
    },
    showMigrationModal (item) {
      this.itemToMigrate = globals().getClonedValue(item)
      this.itemToMigrate.migrationSiteBoxId = (this.getMigrationSiteBoxOptions || [])[0].value
      this.isMigrationModalVisible = true
    },
    onHideMigrationModal () {
      this.isMigrationModalVisible = false
      this.itemToMigrate = null
    },
    startMovingSiteHosting () {
      let apiData = {
        siteId: this.itemToMigrate.siteId,
        newSiteBoxId: this.itemToMigrate.migrationSiteBoxId
      }
      this.$store.dispatch('siteBoxManager/startMovingSiteHosting', {data: apiData}).then(res => {
        this.$toaster.success('Moved To Process')
        setTimeout(() => this.checkMovingSiteHostingTaskStatus(res.data), 5000)
      }).catch(ex => {
        this.$toaster.exception(ex, 'Failed to Start Migration')
      }).finally(() => {
        this.onHideMigrationModal()
      })
    },
    checkMovingSiteHostingTaskStatus (taskId) {
      let keepChecking = false
      this.$store.dispatch('siteBoxManager/getSiteHostingMovingTaskStatus', {taskId: taskId}).then(res => {
        if (res.data === movingSiteHostingsTaskStatusTypes.completed.value) {
          this.$toaster.success('Finished Start Moving Site Hosting Successfully')
        } else if (res.data === movingSiteHostingsTaskStatusTypes.failed.value) {
          this.$toaster.error('Failed Starting Moving Site Hosting')
        } else {
          keepChecking = true
        }
      }).catch(ex => {
        this.$toaster.exception(ex, 'Failed to Start Migration')
      }).finally(() => {
        if (keepChecking) {
          setTimeout(() => this.checkMovingSiteHostingTaskStatus(taskId), 5000)
        } else {
          this.populateData()
        }
      })
    },
    finishMigration (item) {
      this.$set(item, 'isDisabledBtn', true)
      this.$store.dispatch('siteBoxManager/finishMovingSiteHosting', {siteId: item.siteId}).then(res => {
        this.$toaster.success('Moved to process')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Failed to finish migration')
      }).finally(() => {
        this.populateData()
      })
    },
    populateData () {
      this.$store.dispatch('siteBoxManager/getSiteHostings', { filter: this.filter }).then(res => {
        this.items = res.data.items
        this.itemsTotalCount = res.data.itemsTotalCount
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
        this.$logger.handleError(ex, 'Exception occurred on get site hosting listing')
      }).finally(() => {
        this.isLoading = false
      })
    },
    populateSiteBoxes () {
      this.$store.dispatch('siteBoxManager/getSiteBoxes').then(res => {
        this.siteBoxes = res.data || []
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
        this.$logger.handleError(ex, 'Exception occurred on get site boxes')
      }).finally(() => {
        this.isLoadingOptions = false
      })
    }
  }
}
</script>

<style scoped>
.custom-paging {
  position: absolute;
  right: 5px;
  top: 35px;
  z-index: 2;
}
</style>
