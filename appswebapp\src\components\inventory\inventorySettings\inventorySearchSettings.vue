<template>
  <div>
    <b-row class="border-bottom pb-1">
      <b-col class="align-middle">
          <span>
            <b>
              Inventory Search Settings
            </b>
          </span>
      </b-col>

      <b-btn variant="secondary" size="sm" class="edit-btn" @click="setEditMode" v-if='!isEditMode' >
        <font-awesome-icon icon="pencil-alt" size="sm" />
        <span class="btn-title">Edit</span>
      </b-btn>
      <template v-else>
        <b-btn variant="primary" size="sm" class="save-btn" @click="onSave">
          <font-awesome-icon icon="cloud-upload-alt" />
          <span class="btn-title">Save</span>
        </b-btn>
        <b-btn size="sm" class="ml-2" @click="onCancel">
          <span class="btn-title">Cancel</span>
        </b-btn>
      </template>

    </b-row>

    <auto-detail-row v-if='!isEditMode' title='Vehicle Search Type' :text='getVehicleSearchTypeText(inventorySearchSettings.vehicleSearchType)' />
    <auto-detail-row v-else title='Vehicle Search Type' v-model='inventorySearchSettingsToUpdate.vehicleSearchType' :options='allowedVehicleSearchTypes'/>
  </div>
</template>

<script>
import { vehicleSearchTypes } from '@/shared/inventory/inventoryTypes'
import globals from '../../../globals'

export default {
  name: 'inventory-search-settings',
  components: {
    'auto-detail-row': () => import('@/components/details/helpers/autoDetailRow'),
    'detail-row': () => import('@/components/details/helpers/detailRow')
  },
  props: {
    inventorySearchSettings: { type: Object, required: true }
  },
  data () {
    return {
      isEditMode: false,
      inventorySearchSettingsToUpdate: {}
    }
  },
  created () {
    this.init()
  },
  methods: {
    init () {
      this.inventorySearchSettingsToUpdate = globals().getClonedValue(this.inventorySearchSettings)
    },
    onSave () {
      this.$emit('onUpdateSearchSettings', this.inventorySearchSettingsToUpdate)
      this.isEditMode = false
    },
    setEditMode () {
      this.isEditMode = true
    },
    onCancel () {
      this.inventorySearchSettingsToUpdate = globals().getClonedValue(this.inventorySearchSettings)
      this.isEditMode = false
    },
    getVehicleSearchTypeText (value) {
      return this.allowedVehicleSearchTypes.find(x => x.value === value).text
    }
  },
  computed: {
    allowedVehicleSearchTypes () {
      return Object.values(vehicleSearchTypes)
    }
  }
}
</script>
