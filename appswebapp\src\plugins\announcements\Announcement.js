import Shepherd from 'shepherd.js'
import store from '../../store'
import router from '../../router'
import { tooltipRelativePositionOptions } from '@/shared/announcements/constants'

export default class Announcement {
  constructor () {
    this.store = store
    this.isTourRunning = false
    this.runningAnnouncementId = null
    this.tour = null
  };
  isDynamicAnnouncementsEmpty () {
    return !this.store.state.userAnnouncements.dynamicAnnouncements || this.store.state.userAnnouncements.dynamicAnnouncements.length === 0
  };
  tryToStartNewShepherdTour () {
    if (this.isTourRunning) {
      return false
    }
    if (this.isDynamicAnnouncementsEmpty()) {
      return false
    }

    let announcement = this.store.state.userAnnouncements.dynamicAnnouncements.shift()
    if (!announcement.boxes || announcement.boxes.length === 0) {
      this.review(announcement.announcementId)
      this.isTourRunning = false
      return false
    }

    this.runningAnnouncementId = announcement.announcementId
    try {
      this.tour = this.buildTour(announcement)
      this.isTourRunning = true
      this.tour.start()
    } catch (ex) {
      this.isTourRunning = false
      console.error(ex)
    }
    return true
  };
  tryToStopTour () {
    if (this.isTourRunning && this.tour) {
      try {
        this.tour.complete()
        this.isTourRunning = false
        return true
      } catch (ex) {
        console.error(ex)
      }
    }
    return false
  };
  buildTour (announcement) {
    let tour = new Shepherd.Tour(
      {
        useModalOverlay: true,
        tourName: announcement.announcementId
      }
    )
    let countOfBoxes = announcement.boxes.length
    if (countOfBoxes > 1) {
      for (let boxIndex = 0; boxIndex < countOfBoxes; boxIndex++) {
        let boxOption = announcement.boxes[boxIndex]
        if (boxIndex === 0) {
          this.applyFirstBox(tour, boxOption)
          continue
        }
        if (boxIndex === countOfBoxes - 1) {
          this.applyLastBox(tour, boxOption)
          continue
        }
        this.applyBox(tour, boxOption)
      }
    } else {
      let boxOption = announcement.boxes[0]
      this.applyOnlyOneBox(tour, boxOption)
    }
    return tour
  };
  applyBasicOptionsToStep (step, boxOption) {
    let thisElement = this
    step.beforeShowPromise = function () {
      return new Promise(function (resolve) {
        thisElement.tryRedirectTo(boxOption.pathAndQuery).finally(() => {
          router.app.$nextTick(() => {
            if (boxOption.element && boxOption.element.elementId) {
              isElementRendered(boxOption.element.elementId).then(() => {
                resolve()
              })
            } else {
              resolve()
            }
          })
        })
      })
    }
    step.title = boxOption.title
    step.text = boxOption.message
    if (boxOption.element && boxOption.element.elementId) {
      step.attachTo = {
        element: `#${boxOption.element.elementId}`,
        on: getTooltipRelativePosition(boxOption.element.tooltipRelativePosition)
      }
    }
  }
  applyOnlyOneBox (tour, boxOption) {
    let step = {}
    this.applyBasicOptionsToStep(step, boxOption)
    step.buttons = [
      {
        text: 'Done',
        classes: 'btn btn-sm btn-primary',
        action: () => this.onComplete(tour)
      }
    ]
    tour.addStep(step)
  };
  applyFirstBox (tour, boxOption) {
    let step = {}
    this.applyBasicOptionsToStep(step, boxOption)
    step.buttons = [
      {
        text: 'Exit',
        classes: 'btn btn-sm btn-primary',
        action: () => this.onCancel(tour)
      },
      {
        text: 'Next',
        classes: 'btn btn-sm btn-white',
        action: tour.next
      }
    ]
    tour.addStep(step)
  };
  applyLastBox (tour, boxOption) {
    let step = {}
    this.applyBasicOptionsToStep(step, boxOption)
    step.buttons = [
      {
        text: 'Back',
        classes: 'btn btn-sm btn-secondary',
        action: tour.back
      },
      {
        text: 'Complete',
        classes: 'btn btn-sm btn-primary',
        action: () => this.onComplete(tour)
      }
    ]
    tour.addStep(step)
  };
  applyBox (tour, boxOption) {
    let step = {}
    this.applyBasicOptionsToStep(step, boxOption)
    step.buttons = [
      {
        text: 'Back',
        classes: 'btn btn-sm btn-secondary',
        action: tour.back
      },
      {
        text: 'Next',
        classes: 'btn btn-sm btn-white',
        action: tour.next
      }
    ]
    tour.addStep(step)
  };
  onCancel (tour) {
    this.exit(this.runningAnnouncementId)
    tour.cancel()
    this.runningAnnouncementId = null
    this.isTourRunning = false
    this.tryToStartNewShepherdTour()
  }
  onComplete (tour) {
    this.review(this.runningAnnouncementId)
    tour.complete()
    this.runningAnnouncementId = null
    this.isTourRunning = false
    this.tryToStartNewShepherdTour()
  };
  exit (id) {
    store.dispatch('userAnnouncements/exitAnnouncement', {id: id}).catch(ex => {
      router.app.$logger.handleError(ex, 'Exception on exit announcement api call')
    })
  };
  review (id) {
    store.dispatch('userAnnouncements/reviewAnnouncement', {id: id}).catch(ex => {
      router.app.$logger.handleError(ex, 'Exception on review announcement api call')
    })
  };
  async tryRedirectTo (pathAndQuery) {
    if (pathAndQuery) {
      await router.push({path: pathAndQuery})
    }
  };
}

async function isElementRendered (id) {
  let attempt = 0
  while (!document.querySelector(`#${id}`) && attempt < 5) {
    await new Promise(r => setTimeout(r, 1000), rej => {})
    attempt++
  }
}

function getTooltipRelativePosition (value) {
  let res = tooltipRelativePositionOptions.find(x => x.value === +value)
  if (res) {
    return res.shepherdValue
  }
  return 'top'
}
