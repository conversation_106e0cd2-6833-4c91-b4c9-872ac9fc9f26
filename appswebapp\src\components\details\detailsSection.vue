<template>
<ValidationObserver ref="validator" v-slot="{ invalid }">
   <b-card-body :key="detailsKey" v-if="id !== null">
    <header :class="{ 'subSection': subSection, 'no-border': noBorder, 'hidden': !isSectionVisible }">
      <span class="section-title" :class="{'text-muted' : subSection}">{{title}}</span>
      <div>
        <show-hide-helper v-if="!subSection && isViewMode" class="d-sm-none" :visible="isSectionVisibleMobile" @visibilityChange="onCollapse" variant="arrow" />

        <write-permission-wrapper variant="hidden">
          <show-hide-helper class="d-none d-sm-inline" v-if="canBeHidden" :visible="visible" @visibilityChange="onVisibilityChange" variant="link" :disabled="isVisibleDisabled" />
          <span v-if="!disableEdit" class="ml-4 d-none d-sm-inline">
            <b-btn v-if="isViewMode" variant="secondary" size="sm" class="fixed-sizes" :disabled="!isEditAvailable && !isEditSaveButtonDisabled" @click="setEditMode()"><font-awesome-icon icon="pencil-alt" size="sm" /> <span class="btn-title">Edit</span></b-btn>
            <template v-else>
              <b-btn variant="primary" size="sm" class="fixed-sizes" @click="saveVehicle()" :disabled="isEditSaveButtonDisabled || invalid" ><font-awesome-icon icon="cloud-upload-alt" /> <span class="btn-title">Save</span></b-btn>
              <b-btn size="sm" class="fixed-sizes" :disabled="isEditSaveButtonDisabled" @click="cancel()">Cancel</b-btn>
            </template>
          </span>
        </write-permission-wrapper>

      </div>
    </header>
    <b-collapse :visible="isSectionVisible" :id="getId">
      <main class="w-100">
        <slot></slot>
      </main>
      <footer class="d-flex d-sm-none">
        <write-permission-wrapper variant="hidden">
          <show-hide-helper class="footer-buttons" v-if="canBeHidden" :visible="visible" @visibilityChange="onVisibilityChange" variant="button" :disabled="isVisibleDisabled"/>
          <div class="footer-divider" v-if="!disableEdit && canBeHidden"></div>
          <b-btn v-if="isViewMode && !disableEdit" class="footer-buttons" size="lg" variant="secondary" :disabled="!isEditAvailable && !isEditSaveButtonDisabled" @click="setEditMode()"><font-awesome-icon icon="pencil-alt" size="sm" /> <span class="btn-title">Edit</span></b-btn>
          <template v-else-if="!disableEdit">
            <b-btn variant="primary" class="footer-buttons" size="lg" @click="saveVehicle()" :disabled="isEditSaveButtonDisabled || invalid"><font-awesome-icon icon="cloud-upload-alt" /> <span class="btn-title">Save</span></b-btn>
            <b-btn size="lg" class="footer-buttons" :disabled="isEditSaveButtonDisabled" @click="cancel()">Cancel</b-btn>
          </template>
        </write-permission-wrapper>
      </footer>
    </b-collapse>
  </b-card-body>
</ValidationObserver>
</template>

<script>
import { mapGetters } from 'vuex'
import showHideHelper from './helpers/showHideHelper'
import vehicleSaveMixin from '@/mixins/vehicle/vehicleSaveMixin'
import writePermissionWrapper from '../_shared/writePermissionWrapper'

export default {
  name: 'details-section',
  components: {
    'show-hide-helper': showHideHelper,
    'write-permission-wrapper': writePermissionWrapper
  },
  mixins: [vehicleSaveMixin],
  created () {
    this.$store.dispatch('editSectionController/registerSectionId', {
      onReset: () => {
        this.$emit('input', this.currentMode)
      },
      onEdit: () => {
        this.$emit('input', this.currentMode)
      }
    }).then(x => {
      this.id = x
    })
  },
  mounted () {
    window.addEventListener('resize', this.recalculateMobileMode)
    this.recalculateMobileMode()
  },
  destroyed () {
    window.removeEventListener('resize', this.recalculateMobileMode)
  },
  props: {
    title: String,
    disableEdit: Boolean,
    canBeHidden: Boolean,
    subSection: Boolean,
    noBorder: Boolean,
    syncSave: Boolean,
    visible: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      id: null,
      isMobile: false,
      isVisibleMobile: false,
      isVisible: this.visible,
      isEditSaveButtonDisabled: false,
      isVisibleDisabled: false,
      detailsKey: 0
    }
  },
  computed: {
    ...mapGetters('editSectionController', ['isEditAvailable', 'isEditMode']),
    ...mapGetters('details', ['vehicle', 'vehicleOriginal']),
    getId () {
      return this.title.replace(/ /g, '_')
    },
    currentMode () {
      return this.isEditMode(this.id) ? 'edit' : 'view'
    },
    isViewMode () {
      return this.currentMode === 'view'
    },
    isSectionVisible () {
      if (this.subSection) {
        return true
      }

      if (this.isMobile) {
        return this.isVisibleMobile || !this.isViewMode
      }

      return this.isVisible || !this.isViewMode
    },
    isSectionVisibleMobile () {
      return this.isVisibleMobile || !this.isViewMode
    }
  },
  methods: {
    onVisibilityChange (newValue) {
      this.disableVisibleButton()
      this.isVisible = newValue
      if (!newValue) {
        this.isVisibleMobile = newValue
      }
      this.$emit('visibilityChange', newValue)
      setTimeout(async () => {
        try {
          await this.updateDisplayedSections(this.vehicleOriginal, this.vehicle)
        } catch (e) {
          this.$toaster.error("An error occurred. Can't update the visibility option.", { timeout: 8000 })
        } finally {
          this.enableVisibleButton()
        }
      }, 1)
    },
    recalculateMobileMode () {
      this.isMobile = window.innerWidth < 576
    },
    onCollapse (newVal) {
      this.isVisibleMobile = newVal
    },
    setEditMode () {
      this.changeMode(true)
    },
    setViewMode () {
      this.changeMode(false)
    },
    cancel () {
      this.$store.commit('details/reset')
      this.changeMode(false)
      this.$emit('cancel')
      this.$forceUpdate()
    },
    async saveVehicle () {
      let saveAndUpdateVehicleInfo = async () => {
        try {
          this.disableSaveButton()
          let response = await this.updateVehicle(this.vehicleOriginal, this.vehicle)
          if (response !== undefined) {
            this.$store.commit('details/setVehicleData', response.data)
            this.setViewMode()
          }
        } catch (e) {
          this.$toaster.error("An error occurred. Can't save the vehicle.", { timeout: 8000 })
          this.$logger.handleError(e, "An error occurred. Can't save the vehicle.", {vehicle: this.vehicle})
        } finally {
          this.enableSaveButton()
        }
      }
      this.$emit('presave')

      let isValid = await this.$refs.validator.validate()
      if (!isValid) {
        this.enableSaveButton()
        return
      }

      this.$emit('save', {
        done: this.syncSave
          ? saveAndUpdateVehicleInfo
          : null
      })

      if (!this.syncSave) {
        await saveAndUpdateVehicleInfo()
      }
    },
    changeMode (isEditMode) {
      this.$store.commit('editSectionController/setMode', {
        id: this.id,
        isEditMode: isEditMode
      })
    },
    disableSaveButton () {
      this.isEditSaveButtonDisabled = true
    },
    enableSaveButton () {
      this.isEditSaveButtonDisabled = false
    },
    disableVisibleButton () {
      this.isVisibleDisabled = true
    },
    enableVisibleButton () {
      this.isVisibleDisabled = false
    }
  }
}
</script>

<style scoped lang="scss">
header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-bottom: 10px;
  margin-bottom: 5px;
  border-bottom: 1px solid #eee;

  transition: border-width 0.6s linear;

  &.no-border {
    border-bottom: none;
    &.hidden {
      border-bottom: 1px solid #eee;
    }
  }
  .section-title {
    font-size: 0.894rem;
    font-weight: bold;
  }

  &.subSection {
    border-bottom: none;
    margin-bottom: 0;
    .section-title {
      font-weight: normal;
    }
  }

  .btn-title {
    margin-left: 3px;
  }
}

footer {
  margin-top: 1rem;
  .fixed-sizes {
    min-width: 66px;
    padding: 0.188rem 0.4875rem;
  }

  .footer-buttons {
    flex: 425
  }
  .footer-divider {
    flex: 35;
  }

}
</style>
