let sectionHelper = {
  getVehicleTypesSectionIdMapping () {
    /*
    * Undefined = 0,
    * Atv = 1,
    * Boat = 2,
    * Bus = 3,
    * Motorcycle = 4,
    * Passenger = 5,
    * Plane = 6,
    * Rv = 7,
    * Scooter = 8,
    * Snow = 9,
    * Truck = 10,
    * Misc = 11
    */

    let standartFeatureChecklist = {}
    let vehicleHistory = {}
    let condition = {}
    let tiresAndWheels = {}
    let notesForthisVehicle = {}

    let truckAttributes = {}
    let RVDetails = {}
    let ATVDetails = {}
    let boatDetails = {}
    let motorcycleUpgrades = {}
    let miscDetails = {}
    let snowDetails = {}
    let exteriorDescription = {}

    standartFeatureChecklist[4] = 0
    standartFeatureChecklist[5] = 0
    standartFeatureChecklist[7] = 0
    standartFeatureChecklist[10] = 0

    vehicleHistory[4] = 401
    vehicleHistory[5] = 501
    vehicleHistory[7] = 700
    vehicleHistory[10] = 1000

    condition[5] = 500
    condition[7] = 706
    condition[10] = 1006

    tiresAndWheels[5] = 508
    tiresAndWheels[7] = 707
    tiresAndWheels[10] = 1007

    notesForthisVehicle[5] = 509
    notesForthisVehicle[7] = 701

    // other
    truckAttributes[10] = 1009
    RVDetails[7] = 702
    ATVDetails[1] = 100
    boatDetails[2] = 200
    motorcycleUpgrades[4] = 403
    exteriorDescription[4] = 400
    miscDetails[11] = 1100
    snowDetails[9] = 900

    return {
      standartFeatureChecklist,
      notesForthisVehicle,
      exteriorDescription,
      motorcycleUpgrades,
      truckAttributes,
      tiresAndWheels,
      vehicleHistory,
      boatDetails,
      condition,
      RVDetails,
      ATVDetails,
      miscDetails,
      snowDetails
    }
  }
}

let conditionHelper = {
  GetOverallExteriorKey (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5002
      case 7:
        return -7031
      case 10:
        return -10023
      default:
        return null
    }
  },
  GetPaintKey (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5003
      case 7:
        return -7032
      case 10:
        return -10024
      default:
        return null
    }
  },
  GetTrimKey (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5004
      case 7:
        return -7033
      case 10:
        return -10025
      default:
        return null
    }
  },
  GetGlassKey (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5001
      case 7:
        return -7030
      case 10:
        return -10022
      default:
        return null
    }
  },
  GetConvertibleTopKey (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5000
      case 7:
        return -7029
      case 10:
        return -10021
      default:
        return null
    }
  },
  GetOverallInteriorKey (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5009
      case 7:
        return -7038
      case 10:
        return -10030
      default:
        return null
    }
  },
  GetCarpetsKey (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5007
      case 7:
        return -7036
      case 10:
        return -10028
      default:
        return null
    }
  },
  GetSeatsKey (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5011
      case 7:
        return -7040
      case 10:
        return -10032
      default:
        return null
    }
  },
  GetDashboardKey (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5008
      case 7:
        return -7037
      case 10:
        return -10029
      default:
        return null
    }
  },
  GetPanelsAndHeadlinerKey (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5010
      case 7:
        return -7039
      case 10:
        return -10031
      default:
        return null
    }
  },
  GetOriginalPaintKey (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5015
      case 7:
        return -7044
      case 10:
        return -10036
      default:
        return null
    }
  },
  GetNoVisibleDentsKey (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5013
      case 7:
        return -7042
      case 10:
        return -10034
      default:
        return null
    }
  },
  GetNoVisibleRustKey (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5014
      case 7:
        return -7043
      case 10:
        return -10035
      default:
        return null
    }
  },
  GetFullyDetailedKey (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5012
      case 7:
        return -7041
      case 10:
        return -10033
      default:
        return null
    }
  },
  GetAccidentsKey (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5005
      case 7:
        return -7034
      case 10:
        return -10026
      default:
        return null
    }
  },
  GetNotesSellingPointsKey (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5016
      case 7:
        return -7045
      case 10:
        return -10037
      default:
        return null
    }
  },
  GetBodyWorkKey (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5006
      case 7:
        return -7035
      case 10:
        return -10027
      default:
        return null
    }
  }
}

let tireHelper = {
  GetTireBrandKey (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5041
      case 7:
        return -7098
      case 10:
        return -10083
      default:
        return null
    }
  },
  GetTireWidthFOLKey (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5034
      case 7:
        return -7091
      case 10:
        return -10076
      default:
        return null
    }
  },
  GetTireRatioFOLKey (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5032
      case 7:
        return -7089
      case 10:
        return -10074
      default:
        return null
    }
  },
  GetTireSpeedFOLKey (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5033
      case 7:
        return -7090
      case 10:
        return -10075
      default:
        return null
    }
  },
  GetTireDiameterFOLKey (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5035
      case 7:
        return -7092
      case 10:
        return -10077
      default:
        return null
    }
  },
  GetTireTreadRemainingKey (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5042
      case 7:
        return -7099
      case 10:
        return -10084
      default:
        return null
    }
  },
  GetSpareConditionKey (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5040
      case 7:
        return -7097
      case 10:
        return -10082
      default:
        return null
    }
  },
  GetTireWidthRearKey (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5038
      case 7:
        return -7095
      case 10:
        return -10080
      default:
        return null
    }
  },
  GetTireRatioRearKey (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5036
      case 7:
        return -7093
      case 10:
        return -10078
      default:
        return null
    }
  },
  GetTireSpeedRearKey (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5037
      case 7:
        return -7094
      case 10:
        return -10079
      default:
        return null
    }
  },
  GetTireDiameterRearKey (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5039
      case 7:
        return -7096
      case 10:
        return -10081
      default:
        return null
    }
  },
  GetWheelTypeKey (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5044
      case 7:
        return -7101
      case 10:
        return -10086
      default:
        return null
    }
  },
  GetAdditionalWheelInfoKey (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5043
      case 7:
        return -7100
      case 10:
        return -10085
      default:
        return null
    }
  }
}

let audioVideoHelper = {
  GetAudioVideoBrand (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5027
      case 7:
        return -7027
      case 10:
        return -10019
      default:
        return null
    }
  },
  GetAudioVideoSpeakers (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5029
      case 7:
        return -7026
      case 10:
        return -10018
      default:
        return null
    }
  },
  GetAudioVideoWattsAndChannel (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5030
      case 7:
        return -7028
      case 10:
        return -10020
      default:
        return null
    }
  },
  GetNotesAndSellingPoints (vehicleType) {
    switch (vehicleType) {
      case 5:
        return -5028
      case 7:
        return -7025
      case 10:
        return -10017
      default:
        return null
    }
  }
}

export { sectionHelper, conditionHelper, tireHelper, audioVideoHelper }
