<template>
  <b-table
    v-if="isGenerationFinished"
    :items="items"
    :fields="fields"
    show-empty
    responsive
    bordered
    hover
    striped
  >
    <template #cell(autoArchiveLeads)="data">
      {{getAutoArchiveLeadsText(data.item)}}
    </template>
    <template #cell(deleteArchivedLeads)="data">
      {{getDeleteArchivedLeadsText(data.item)}}
    </template>
    <template #cell(hasCustomFees)="data">
      {{getCustomFeesText(data.item)}}
    </template>
    <template #cell(manageLink)="data">
      <b-button
        v-if="data.item.manageUrl"
        variant="default btn-xs icon-btn md-btn-flat"
        :href="data.item.manageUrl"
        v-b-tooltip.hover
        :title="data.item.manageUrl">
        <i class="ion ion-md-eye"></i>
      </b-button>
    </template>
  </b-table>
</template>

<script>
export default {
  name: 'account-leads-settings-report-listing',
  props: {
    items: {type: Array, required: true},
    fields: {type: Array, required: true},
    isGenerationFinished: {type: <PERSON><PERSON><PERSON>, required: true}
  },
  methods: {
    getAutoArchiveLeadsText (item) {
      if (item.autoArchiveLeads === 'Yes') {
        return `${item.autoArchiveLeads} after ${item.autoArchiveLeadsDaysAfter} days`
      }
      return item.autoArchiveLeads
    },
    getDeleteArchivedLeadsText (item) {
      if (item.deleteArchivedLeads === 'Yes') {
        return `${item.deleteArchivedLeads} after ${item.deleteArchiveLeadsDaysAfter} days`
      }
      return item.deleteArchivedLeads
    },
    getCustomFeesText (item) {
      if (item.hasCustomFees === 'Yes') {
        return `${item.hasCustomFees}, SMS Fee: ${item.twilioSmsFee}, Call Fee: ${item.twilioCallFee}`
      }
      return item.hasCustomFees
    }
  }
}
</script>
