import dateModule from '@/plugins/locale/date'
import CampaignHelper from './campaignHelper'

const campaignHelper = new CampaignHelper()
class CampaignDataBuilder {
  buildDataForPostOrPut (model) {
    let dataForPosOrPut = {}
    dataForPosOrPut.id = model.id
    dataForPosOrPut.accountId = model.accountId
    dataForPosOrPut.campaignName = model.campaignName
    dataForPosOrPut.area = model.area
    dataForPosOrPut.subArea = model.subArea
    dataForPosOrPut.models = model.models
    dataForPosOrPut.makes = model.makes
    dataForPosOrPut.craigslistSchedulerRules = model.craigslistSchedulerRules.map(x => ({
      dayOfWeek: dateModule.getDayNumber(x.dayOfWeek),
      timeFrom: dateModule.timeWithAbbreviationToTime(x.timeFromHour + x.timeFromSuffix + x.timeFromMinutes),
      timeTo: dateModule.timeWithAbbreviationToTime(x.timeToHour + x.timeToSuffix + x.timeToMinutes),
      postsPerDay: x.postsPerDay ? x.postsPerDay : 0,
      olderThanDaysInStock: x.olderThanDaysInStock ? x.olderThanDaysInStock : 0
    }))
    dataForPosOrPut.status = campaignHelper.getStatusValueByDescription(model.status)
    dataForPosOrPut.ebizAutosVehicleTypes = campaignHelper.getEbizAutosVehicleTypesValueByDescription(model.ebizAutosVehicleTypes)
    dataForPosOrPut.stockSearchType = campaignHelper.getStockSerachTypeValueByDescription(model.stockSearchType)
    dataForPosOrPut.postStrategy = campaignHelper.getPostStrategyValueByDescription(model.postStrategy)
    dataForPosOrPut.vehicleTypesFilter = campaignHelper.getVehicleTypeFiltersByDescriptions(model.vehicleTypesFilter)
    dataForPosOrPut.bodyStylesFilter = campaignHelper.getBodyStyleFiltersByDescriptions(model.bodyStylesFilter)
    dataForPosOrPut.endOfPrice = model.endOfPrice
    dataForPosOrPut.specificLocation = model.specificLocation
    dataForPosOrPut.priceFrom = model.priceFrom
    dataForPosOrPut.priceTo = model.priceTo
    dataForPosOrPut.stock = model.stock
    dataForPosOrPut.minRequiredPhotoCount = model.minRequiredPhotoCount ? model.minRequiredPhotoCount : 1
    return dataForPosOrPut
  }

  buildSelectedData (model) {
    let selectedData = {}
    selectedData.id = model.id
    selectedData.accountId = model.accountId
    selectedData.campaignName = model.campaignName
    selectedData.models = model.models
    selectedData.makes = model.makes
    selectedData.craigslistSchedulerRules = model.craigslistSchedulerRules.map(x => ({
      dayOfWeek: dateModule.getDayName(x.dayOfWeek),
      timeFromHour: dateModule.formatHoursWithoutSuffix(x.timeFrom),
      timeFromMinutes: dateModule.minutes(x.timeFrom) === 0 ? '00' : dateModule.minutes(x.timeFrom),
      timeFromSuffix: dateModule.timeSuffix(x.timeFrom),
      timeToHour: dateModule.formatHoursWithoutSuffix(x.timeTo),
      timeToMinutes: dateModule.minutes(x.timeTo) === 0 ? '00' : dateModule.minutes(x.timeTo),
      timeToSuffix: dateModule.timeSuffix(x.timeTo),
      postsPerDay: x.postsPerDay,
      olderThanDaysInStock: x.olderThanDaysInStock
    }))
    selectedData.status = campaignHelper.getStatusDescriptionByValue(model.status)
    selectedData.ebizAutosVehicleTypes = campaignHelper.getEbizAutosVehicleTypesDescriptionByValue(model.ebizAutosVehicleTypes)
    selectedData.stockSearchType = campaignHelper.getStockSerachTypeDescriptionByValue(model.stockSearchType)
    selectedData.postStrategy = campaignHelper.getPostStrategyDescriptionByValue(model.postStrategy)
    selectedData.bodyStylesFilter = campaignHelper.getBodyStyleFiltersByValues(model.bodyStylesFilter)
    selectedData.vehicleTypesFilter = campaignHelper.getVehicleTypeFiltersByValues(model.vehicleTypesFilter)
    selectedData.endOfPrice = model.endOfPrice
    selectedData.specificLocation = model.specificLocation
    selectedData.priceFrom = model.priceFrom
    selectedData.priceTo = model.priceTo
    selectedData.stock = model.stock
    selectedData.minRequiredPhotoCount = model.minRequiredPhotoCount
    return selectedData
  }

  getDefaultSelectedData () {
    return {
      id: '',
      accountId: 0,
      campaignName: '',
      area: '',
      subArea: '',
      status: 'Active',
      minRequiredPhotoCount: 1,
      stockSearchType: 'All',
      stock: '',
      craigslistSchedulerRules: [
        {
          dayOfWeek: 'Monday',
          timeFromHour: '9',
          timeFromMinutes: '00',
          timeFromSuffix: 'AM',
          timeToHour: '12',
          timeToMinutes: '00',
          timeToSuffix: 'PM',
          postsPerDay: 0,
          olderThanDaysInStock: 0
        },
        {
          dayOfWeek: 'Tuesday',
          timeFromHour: '9',
          timeFromMinutes: '00',
          timeFromSuffix: 'AM',
          timeToHour: '12',
          timeToMinutes: '00',
          timeToSuffix: 'PM',
          postsPerDay: 0,
          olderThanDaysInStock: 0
        },
        {
          dayOfWeek: 'Wednesday',
          timeFromHour: '9',
          timeFromMinutes: '00',
          timeFromSuffix: 'AM',
          timeToHour: '12',
          timeToMinutes: '00',
          timeToSuffix: 'PM',
          postsPerDay: 0,
          olderThanDaysInStock: 0
        },
        {
          dayOfWeek: 'Thursday',
          timeFromHour: '9',
          timeFromMinutes: '00',
          timeFromSuffix: 'AM',
          timeToHour: '12',
          timeToMinutes: '00',
          timeToSuffix: 'PM',
          postsPerDay: 0,
          olderThanDaysInStock: 0
        },
        {
          dayOfWeek: 'Friday',
          timeFromHour: '9',
          timeFromMinutes: '00',
          timeFromSuffix: 'AM',
          timeToHour: '12',
          timeToMinutes: '00',
          timeToSuffix: 'PM',
          postsPerDay: 0,
          olderThanDaysInStock: 0
        },
        {
          dayOfWeek: 'Saturday',
          timeFromHour: '9',
          timeFromMinutes: '00',
          timeFromSuffix: 'AM',
          timeToHour: '12',
          timeToMinutes: '00',
          timeToSuffix: 'PM',
          postsPerDay: 0,
          olderThanDaysInStock: 0
        },
        {
          dayOfWeek: 'Sunday',
          timeFromHour: '9',
          timeFromMinutes: '00',
          timeFromSuffix: 'AM',
          timeToHour: '12',
          timeToMinutes: '00',
          timeToSuffix: 'PM',
          postsPerDay: 0,
          olderThanDaysInStock: 0
        }
      ],
      priceFrom: 0,
      priceTo: 0,
      makes: [],
      models: [],
      postStrategy: 'First In, First Out',
      vehicleTypesFilter: [],
      bodyStylesFilter: [],
      ebizAutosVehicleTypes: '',
      specificLocation: '',
      endOfPrice: ''
    }
  }
}

export default CampaignDataBuilder
