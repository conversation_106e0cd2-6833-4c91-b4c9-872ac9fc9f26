﻿using System.ComponentModel.DataAnnotations;

namespace EBizAutos.Apps.Authentication.CommonLib.Models {
	public class MfaSettings {
		[Range(4, 10, ErrorMessage = "OTP code length must be between 4 and 10 characters.")]
		public int OtpCodeLength { get; set; }

		[Range(1, 60, ErrorMessage = "OTP code TTL must be between 1 and 60 minutes.")]
		public int OtpCodeTtlInMinutes { get; set; }

		[Range(1, 10, ErrorMessage = "OTP code verification attempts limit must be between 1 and 10.")]
		public int OtpCodeVerificationAttemptsLimit { get; set; }

		[Range(30, 300, ErrorMessage = "OTP code resend interval must be between 30 and 300 seconds.")]
		public int OtpCodeResendIntervalInSeconds { get; set; }
	}
}