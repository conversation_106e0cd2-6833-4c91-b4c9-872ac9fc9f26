<template>
  <div>
     <b-alert v-if='!isPostingAllowed && isLoaded' variant="warning" show dismissible>
      <strong>Contact us!</strong> Craigslist is not enabled for this account. Please contact Support at <a href="tel:************" class="text-primary"><u>************</u></a> or
      <a href = "mailto:<EMAIL>" class="text-primary"><u><EMAIL></u></a> to setup your Craigslist settings.
    </b-alert>
    <div class="dashboard-header">
      <!-- <div class="align-self-center"> -->
        <h4>Dashboard - {{accountName}} ({{accountId}})</h4>
      <!-- </div> -->

          <range-selector
                @input="onRangeChanged"
                :value="filterDateRange"
                class="button-col"
          />

    </div>

    <div class="mt-4">
      <b-card>
        <b-row align-h='between'>
          <b-col text-right cols='4'>
            <div class='h4'>{{ chartType }}</div>
          </b-col>

          <b-col cols='1' style='text-align:end'>
            <b-dropdown
              variant='outline-secondary icon-btn btn-round'
              size='sm'
              right
              no-caret
            >
              <template slot='button-content'>
                <i class='ion ion-ios-more m-0'></i>
              </template>
              <b-dropdown-item @click='setCostDailyTotals()'>Cost</b-dropdown-item>
              <b-dropdown-item @click='setPostDailyTotals()'>Posts</b-dropdown-item>
            </b-dropdown>
          </b-col>
        </b-row>

        <vue-echart
          id='echart'
          :options='barOptions'
          :auto-resize='true'
        ></vue-echart>
      </b-card>
    </div>

    <div class="container-fluid pl-0 pr-0">
      <div class='row mt-4 widget-metric-higlights'>
        <div class='col-6 col-sm-6 col-xl-6 pr-0'>
          <summary-card
            label='Cost'
            :value='cost.value'
            :delta='cost.delta'
            :rangeLabel='currentDateRange'
            cardClass='bg-white'
          >
            <i class='ion-ios-wallet h1 m-0 opacity-25 d-none d-sm-inline'></i>
          </summary-card>
        </div>
        <div class='col-6 col-sm-6 col-xl-6 pl-0'>
          <summary-card
            label='Posts'
            :value='posts.value'
            :delta='posts.delta'
            :rangeLabel='currentDateRange'
            cardClass='bg-white'
          >
            <i
              class='ion-ios-document h1 m-0 opacity-25 d-none d-sm-inline'
              data-src='../../../static/img/apps-icons/icon-messages-green.png'
            ></i>
          </summary-card>
        </div>
      </div>
    </div>

    <b-row>
    <b-col ><h4 class="mt-4"> Your Budget: {{budget}} Post(s) </h4></b-col>
    <b-col ><b-btn v-if='isPostingAllowed && isAllowedToManage' variant="primary btn-round" class= "float-right mt-4" size="sm" :to='getCampaignPath()'><span class="ion ion-ios-add"></span><span class="d-none d-sm-inline">&nbsp; Add Campaign</span></b-btn></b-col>
    </b-row>
    <div class='mt-2 table-responsive'>
      <b-card>
        <b-table
          hover
          :items='campaignsTotalSummaries'
          :fields='campaignsTotalSummariesFields'
          responsive
          class="products-table card-table"
        >
        <template #cell(campaignName)="data">
          <slot name="campaignName" :data="data">
            <router-link v-if='data.item.schedulerItemId' :to="{ path: `dashboard/campaign/${data.item.schedulerItemId}`}" class="link">
            {{data.item.campaignName}}
            </router-link>
            <span v-else>
              {{data.item.campaignName}}
            </span>
          </slot>
        </template>
        </b-table>
        <!-- Pagination -->
        <paging
        v-if='campaignsTotalSummariesCount > 10'
        :pageNumber="filter.page"
        :pageSize="filter.pageSize"
        :totalItems="campaignsTotalSummariesCount"
        titled
        pageSizeSelector
        @numberChanged="pageChanged"
        @changePageSize="changePageSize" />
      </b-card>
    </div>
  </div>
</template>

<script>
import 'echarts/lib/chart/bar'
import 'echarts/lib/component/tooltip'
import 'echarts/lib/component/legend'
import numeral from 'numeral'
import moment from 'moment'

import AppsListingLink from '../../components/_shared/applicationAccountListing/AppsListingLink'
import dateHelper from '@/plugins/locale/date'
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import Paging from '@/components/_shared/paging.vue'
import {mapGetters} from 'vuex'

const defaultValues = new ObjectSchema({
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 10 }
})
const queryHelper = new QueryStringHelper(defaultValues)

var LRU = require('lru-cache')
const cache = new LRU(50)

export default {
  name: 'dashboard-for-account',
  props: {
    accountId: {
      type: Number,
      required: true
    }
  },
  metaInfo: {
    title: 'Craigslist Dashboard'
  },
  created () {
    if (cache.get('CraiglistAccountDashboardFilter')) {
      this.filter = cache.get('CraiglistAccountDashboardFilter')
    } else {
      this.filter = queryHelper.parseQueryStringToObject(this.$router)
    }
    if (this.accountId) {
      this.getInitialData()
    }
  },
  components: {
    'vue-echart': () => import('vue-echarts/components/ECharts.vue'),
    'summary-card': () => import('../../components/craigslist/summaryCard'),
    'range-selector': () => import('../../components/craigslist/rangeSelector/craigslistRangeSelector'),
    'listing-link': AppsListingLink,
    'paging': Paging
  },
  data () {
    return {
      isLoaded: false,
      DailyTotals: [],
      accountName: '',
      budget: 0,
      filter: defaultValues.getObject(),
      // totals: Object,
      currentDateRange: '03/01/2019 - 03/30/2019',
      posts: { value: 0, delta: 0 },
      cost: { value: 0, delta: 0 },
      rangeInfo: Object,
      chartCost: false,
      typeDailyTotals: 'AmountOfPosts',
      campaignsTotalSummaries: [],
      campaignsTotalSummariesCount: 0,
      isPostingAllowed: false,
      campaignsTotalSummariesFields: [
        { key: 'campaignName', label: 'Campaign Name', sortable: true, tdClass: 'py-2 align-middle' },
        { key: 'areaDescription', label: 'Area', sortable: true, tdClass: 'py-2 align-middle' },
        { key: 'amountOfPosts', label: 'Posts', sortable: true, tdClass: 'py-2 align-middle' },
        { key: 'totalPostPrice', label: 'Cost', sortable: true, formatter: value => numeral(value).format('$0,0'), tdClass: 'py-2 align-middle' }
      ]
    }
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {}
    },
    filterDateRange () {
      if ((this.filter || {}).dateFrom && (this.filter || {}).dateTo) {
        return [this.filter.dateFrom, this.filter.dateTo]
      }

      return null
    },
    isAllowedToManage () {
      return this.user.isEbizAdmin
    },
    chartType () {
      return this.chartCost ? 'Cost' : 'Posts'
    },
    barTimeFormat () {
      if (
        this.rangeInfo &&
        Date.daysBetween(
          new Date(this.rangeInfo.range[1]),
          new Date(this.rangeInfo.range[0]),
          true
        ) > 365
      ) {
        return 'MMM D YYYY'
      }
      return 'MMM.D'
    },
    barOptions () {
      return {
        title: {
          text: this.chartType
        },
        grid: {
          left: '0',
          right: '0',
          bottom: '0',
          top: '5%',
          containLabel: true
        },
        color: '#6b0001',
        tooltip: {
          trigger: 'axis',
          formatter: params => {
            return `${params[0].name}<br />${this.$locale.formatNumber(
              params[0].value
            )}`
          },
          textStyle: {
            fontSize: 13
          }
        },
        xAxis: [
          {
            data: this.DailyTotals.map(x => {
              return dateHelper.getDayFormatted(x.DateTimeFrom, 'MMM.D')
            }),
            axisLabel: {
              color: 'rgba(0, 0, 0, .9)'
            }
          }
        ],
        yAxis: {},
        series: [
          {
            barWidth: '4',
            type: 'bar',
            data: this.DailyTotals.map(x => x[this.typeDailyTotals]),
            itemStyle: {
              normal: {
                color: '#dc3545'
              }
            }
          }
        ],
        animationDuration: 2000
      }
    }
  },
  destroyed () {
    cache.set('CraiglistAccountDashboardFilter', this.filter)
  },
  methods: {
    async onRangeChanged (rangeInfo) {
      if (this.filter.dateFrom === rangeInfo.range[0] && this.filter.dateTo === rangeInfo.range[1] && this.rangeInfo) {
        return
      }
      this.filter.dateFrom = rangeInfo.range[0]
      this.filter.dateTo = rangeInfo.range[1]
      this.rangeInfo = rangeInfo
      this.getInitialData()
    },
    setCostDailyTotals () {
      this.chartCost = true
      this.typeDailyTotals = 'TotalPostPrice'
    },
    setPostDailyTotals () {
      this.chartCost = false
      this.typeDailyTotals = 'AmountOfPosts'
    },
    pageChanged (newPage) {
      this.filter.page = newPage
      queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.getAccountDashboard()
    },
    changePageSize (newPageSize) {
      this.filter.page = 1
      this.filter.pageSize = newPageSize
      queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.getAccountDashboard()
    },
    getCampaignPath () {
      return `/craigslist/${this.accountId}/dashboard/campaign/`
    },
    getInitialData () {
      queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.getTotalSummary()
      this.getDailyTotals()
      this.getAccountDashboard()
    },
    getTotalSummary () {
      return this.$store.dispatch('craigslist/getAccountTotalSummary', {accountId: this.accountId, filter: this.filter}).then(x => {
        this.posts.value = x.data.PostsTotalCount
        this.posts.delta = x.data.PostsDelta
        this.cost.value = numeral(x.data.PostsTotalCost).format('$0,0.00')
        this.cost.delta = x.data.CostDelta
        this.currentDateRange = moment(x.data.DateTimeFrom).format('MMMM D, YYYY') + ' - ' + moment(x.data.DateTimeTo).format('MMMM DD, YYYY')
      })
    },
    getDailyTotals () {
      this.$store.dispatch('craigslist/getAccountDailyTotals', {accountId: this.accountId, filter: this.filter}).then(x => {
        if (x.data.DailyTotals) {
          this.DailyTotals = x.data.DailyTotals.sort(
            (a, b) => new Date(a.DateTimeFrom) - new Date(b.DateTimeFrom)
          )
        }
      })
    },
    getAccountDashboard  () {
      return this.$store.dispatch('craigslist/getAccountDashboard', {accountId: this.accountId, filter: this.filter}).then(x => {
        this.accountName = x.data.model.accountName
        this.budget = x.data.model.accountSettings.budget
        this.campaignsTotalSummaries = x.data.model.campaignsTotalSummaries
        this.campaignsTotalSummariesCount = x.data.model.campaignsCount
        this.isPostingAllowed = x.data.model.accountSettings.isPostingAllowed
      }).finally(() => {
        this.isLoaded = true
      })
    }
  }
}
</script>

<style>

.link{
  color: red;
  background-color: transparent;
  text-decoration: underline;
}
.dashboard-header{
  display: flex;
  justify-content: space-between;
}
.dashboard-header h4 {
  margin: 0;
  align-self: center;
}
#echart {
  width: 100%;
  height: 250px;
}
</style>
