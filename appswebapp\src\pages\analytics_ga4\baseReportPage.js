import {mapGetters} from 'vuex'
import rangeHelper from '../../components/analytics_ga4/rangeSelector/rangeHelper'
import analyticsHelper from './helpers.js'

export default {
  props: {
    accountId: {
      type: Number,
      required: true
    }
  },
  created () {
    this.setFilters()
    this.page.hasToUpdateStatisticsWhenRangeInfoComes = true
    if (this.rangeInfo) {
      this.onRangeChanged(this.rangeInfo)
    }
  },
  data () {
    return {
      page: {
        hasToUpdateStatisticsWhenRangeInfoComes: false,
        deviceFilter: 'all'
      },
      bar: {
        items: [],
        hasToUseYearBarDates: false
      },
      summary: {
        label: ''
      },
      table: {
        items: [],
        totalItems: 0
      },
      cache: {}
    }
  },
  computed: {
    ...mapGetters('analyticsGa4', ['rangeInfo']),
    barTimeFormat () {
      if (this.bar.hasToUseYearBarDates) {
        return 'YYYY'
      } else if (this.rangeInfo && Date.daysBetween(new Date(this.rangeInfo.range[1]), new Date(this.rangeInfo.range[0]), true) > 365) {
        return 'MMM D YYYY'
      }

      return 'MMM.D'
    }
  },
  methods: {
    onRangeChanged (rangeInfo) {
      this.summary.label = rangeInfo.label
      this.bar.hasToUseYearBarDates = rangeInfo.name === rangeHelper.biggestRange.label
      this.page.filter.dateFrom = rangeInfo.range[0]
      this.page.filter.dateTo = rangeInfo.range[1]

      if (this.page.hasToUpdateStatisticsWhenRangeInfoComes) {
        this.updateStatistics()
      } else {
        if (this.page.filter.pageNumber) {
          this.page.filter.pageNumber = 1
        }
        this.synchronizeUrl()
      }
      this.page.hasToUpdateStatisticsWhenRangeInfoComes = false
    },
    onPageNumberChanged (pageNumber) {
      this.page.filter.pageNumber = pageNumber
      this.synchronizeUrl()
    },
    onPageSizeChanged (pageSize) {
      this.page.filter.pageSize = pageSize
      this.page.filter.pageNumber = 1
      this.synchronizeUrl()
    },
    onSortTypeChanged (sortType) {
      this.page.filter.sortType = sortType
      if (this.page.filter.pageNumber) {
        this.page.filter.pageNumber = 1
      }
      this.synchronizeUrl()
    },
    onDeviceFilterChanged (tab) {
      if (this.page.filter.pageNumber) {
        this.page.filter.pageNumber = 1
      }
      this.page.deviceFilter = tab.key
      this.updateStatistics()
    },
    setFilters () {
      this.page.filter = analyticsHelper.getFilterWithDefaultDateRange(this.$router, this.filterManager)
    },
    synchronizeUrl () {
      const filterForRoute = analyticsHelper.getFilterForUrlQueryWithDefaultDateRange(this.page.filter, this.filterManager)
      this.filterManager.urlHelper.rebuildParamsInQueryString(this.$router, filterForRoute)
    }
  },
  watch: {
    '$route' (to, from) {
      this.setFilters()
      this.updateStatistics()
    },
    rangeInfo (value) {
      this.onRangeChanged(value)
    }
  }
}
