<template>
  <editSettingsHelper :readOnlyMode="readOnlyMode" @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="Warranty" :isLoading="isUpdatingProcessed" :isDisabled="isDisabled" :isViewMode="isViewMode">
    <template slot="settings-content">
      <detail-row v-if="accountLevel" :fixed-payload-width="true" :title-position="'start'">
        <span slot="title">Use Custom Warranty Settings:</span>
        <b-form-checkbox v-if="!isViewMode" slot="payload" v-model="updatedSettings.hasToUseCustomWarrantySettings"></b-form-checkbox>
        <span v-else slot="payload">
          {{ updatedSettings.hasToUseCustomWarrantySettings ? 'Yes' : 'No' }}
        </span>
      </detail-row>
      <b-overlay :show="!(!accountLevel || updatedSettings.hasToUseCustomWarrantySettings)" opacity="0.7" blur="1.2px">
        <template #overlay>
          <span></span>
        </template>
        <ValidationProvider
          v-for="warranty in updatedSettings.warrantyItems" :key="warranty.warrantyType"
          :name="getWarrantyDesc(warranty.warrantyType)" :rules="getAccountWarrantyRules(warranty.warrantyType)" v-slot="{errors}">
          <detail-row :fixed-payload-width="true" :title-position="'start'" :error="errors[0]">
            <span slot="title">{{ getWarrantyDesc(warranty.warrantyType) }}</span>
            <span slot="payload" v-if="isViewMode">{{ warranty.descriptionText || '-' }}</span>
            <b-form-input slot="payload" v-else v-model="warranty.descriptionText" :disabled="isDisabled"></b-form-input>
          </detail-row>
        </ValidationProvider>
      </b-overlay>
    </template>
  </editSettingsHelper>
</template>

<script>
import editSettingsHelper from '@/components/_shared/editSettingsHelper'
import detailRow from '@/components/details/helpers/detailRow'
import editSettingsMixin from '../editSettingsMixin'
import globals from '../../../../globals'
import { warranties } from '@/shared/inventory/vehicleTypes'

export default {
  name: 'tts-warranty-settings-section',
  props: {
    settings: { type: Object, required: true },
    isUpdatingProcessed: { type: Boolean, required: true },
    isDisabled: Boolean,
    accountLevel: Boolean,
    readOnlyMode: Boolean
  },
  data () {
    return {
      updatedSettings: globals().getClonedValue(this.settings),
      isViewMode: true
    }
  },
  mixins: [editSettingsMixin],
  components: {
    detailRow,
    editSettingsHelper
  },
  methods: {
    updateSettings () {
      this.$emit('saveChanges', this.updatedSettings)
    },
    getWarrantyDesc (value) {
      let warranty = Object.values(warranties).find(x => x.value === value)
      return (warranty || {text: '-'}).text
    },
    getAccountWarrantyRules (warrantyType) {
      if (this.accountLevel && this.updatedSettings.hasToUseCustomWarrantySettings) {
        return ![warranties.noWarranty.value, warranties.doNotDisplay.value].includes(warrantyType) ? 'required' : ''
      }
      return ''
    }
  },
  watch: {
    'updatedSettings': {
      deep: true,
      handler: function () {
        this.$emit('input', this.updatedSettings)
      }
    }
  }
}
</script>
