<template>
  <div>
    <b-tabs v-model="selectedTab" class="nav-tabs-top nav-responsive-sm" no-fade>
      <b-tab v-for="tab in tabOptions" :key="tab.key" :title="tab.title">
      </b-tab>
      <div class="p-3">
        <siteBoxFetcherInfo v-if="selectedTab === syncMonitorTabTypes.siteBoxFetcher"/>
        <foundationFetcherInfo v-if="selectedTab === syncMonitorTabTypes.foundationFetcher"/>
        <vehicleSynchronizerInfo v-if="selectedTab === syncMonitorTabTypes.appsVehicleSynchronizer"/>
        <synchronizationLogInfo v-if="selectedTab === syncMonitorTabTypes.syncLog"/>
      </div>
    </b-tabs>
  </div>
</template>

<script>
import siteBoxFetcherInfo from '@/components/siteBoxManager/siteBoxFetcherInfo'
import foundationFetcherInfo from '@/components/siteBoxManager/foundationFetcherInfo'
import vehicleSynchronizerInfo from '@/components/siteBoxManager/vehicleSynchronizerInfo'
import synchronizationLogInfo from '@/components/siteBoxManager/synchronizationLogInfo'
import {syncMonitorTabTypes} from '@/shared/siteBoxManager/common/constants'
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'

const defaultValues = new ObjectSchema({
  tab: { type: Number, default: 0 }
})

const queryHelper = new QueryStringHelper(defaultValues)

export default {
  name: 'sitebox-synchronization-monitor',
  metaInfo: {
    title: 'Synchronization Monitor'
  },
  data () {
    return {
      filter: defaultValues.getObject(),
      syncMonitorTabTypes,
      tabOptions: [
        { key: 0, title: 'SiteBox Fetcher' },
        { key: 1, title: 'Foundation Fetcher' },
        { key: 2, title: 'Apps Vehicle Synchronizer' },
        { key: 3, title: 'Synchronization Log' }
      ]
    }
  },
  computed: {
    selectedTab: {
      get () {
        return this.filter.tab
      },
      set (val) {
        this.filter.tab = val
        queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
      }
    }
  },
  created () {
    this.filter = queryHelper.parseQueryStringToObject(this.$router)
  },
  components: {
    siteBoxFetcherInfo,
    foundationFetcherInfo,
    vehicleSynchronizerInfo,
    synchronizationLogInfo
  }
}
</script>
