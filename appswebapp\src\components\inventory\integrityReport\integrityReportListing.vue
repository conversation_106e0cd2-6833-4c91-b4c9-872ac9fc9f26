<template>
  <div>
    <vehiclesLostInSync v-if="type === integrityReportTypes.vehiclesLostInSync.key"/>
    <carfaxReport v-else-if="type === integrityReportTypes.carfaxReport.key" ></carfaxReport>
  </div>
</template>

<script>
import { integrityReportTypes } from '@/shared/inventory/inventoryTypes'
import vehiclesLostInSync from './vehiclesLostInSync'
import carfaxReport from './carfaxReport'

export default {
  props: {
    type: { type: Number, required: true }
  },
  data () {
    return {
      integrityReportTypes
    }
  },
  components: {
    vehiclesLostInSync,
    carfaxReport
  }
}
</script>
