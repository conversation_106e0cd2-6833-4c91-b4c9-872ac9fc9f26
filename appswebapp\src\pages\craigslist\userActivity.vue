<template>
  <div>
    <b-row class="change-marging">
      <h4>User Activity</h4>
      <!-- Pagination -->
      <paging
        class="d-none d-md-block"
        :pageNumber="filter.page"
        :pageSize="filter.pageSize"
        :totalItems="itemsTotalCount"
        @numberChanged="pageChanged"
        @changePageSize="changePageSize"
      />
    </b-row>
    <!---Filters-->
    <b-card>
      <b-form v-on:submit.prevent="applySearch">
        <div class="form-row">
          <b-col class="my-1" sm="4" lg="4" xl="2">
            <b-form-input
              max='200'
              v-model="filter.search"
              placeholder="Search..."
              autocomplete="off"
            >
            </b-form-input>
          </b-col>

          <b-col class="my-1" sm="4" lg="4" xl="2">
            <b-input-group class="flex-nowrap">
              <b-input-group-prepend is-text>
                <i class="ion ion-md-calendar" slot="prepend"></i>
              </b-input-group-prepend>
              <date-time-picker
                ref="timeFrom"
                v-model="filter.dateFrom"
                :options="filterTimeOptions"
                format="MM/DD/YYYY HH:mm"
                placeholder="Date From"
                className="form-control"
                @change="onTimeFromInputChange"
              />
              <b-input-group-append
                is-text
                v-show="filter.dateFrom"
                @click="filter.dateFrom = null"
              >
                <i class="ion ion-md-close"></i>
              </b-input-group-append>
            </b-input-group>
          </b-col>

          <b-col class="my-1" sm="4" lg="4" xl="2">
            <b-input-group class="flex-nowrap">
              <b-input-group-prepend is-text>
                <i class="ion ion-md-calendar" slot="prepend"></i>
              </b-input-group-prepend>
              <date-time-picker
                ref="timeTo"
                v-model="filter.dateTo"
                :options="filterTimeOptions"
                format="MM/DD/YYYY HH:mm"
                placeholder="Date To"
                className="form-control"
                @change="onTimeToInputChange"
              />
              <b-input-group-append
                is-text
                v-show="filter.dateTo"
                @click="filter.dateTo = null"
              >
                <i class="ion ion-md-close"></i>
              </b-input-group-append>
            </b-input-group>
          </b-col>
          <b-col class="my-1" sm="4" lg="4" xl="2">
            <b-form-select v-model="filter.userAction" :options="getUserActionOptions"></b-form-select>
          </b-col>
          <b-col class="my-1" sm="4" lg="4" xl="2">
            <multiSelectWithCheckboxes
              v-model="selectedUserTypes"
              :options="getUserTypeOptions"
              name="User Types"
              customMessageOfNoneSelectedItems="Displayed All User Types">
            </multiSelectWithCheckboxes>
          </b-col>
          <b-col class="my-1" sm="4" lg="4" xl="2">
            <b-btn block  variant="primary" type="submit">Submit</b-btn>
          </b-col>
        </div>
      </b-form>
    </b-card>
    <!-- Listing -->
    <b-card>
      <!-- Table -->
      <div class="table-responsive" v-if="isLoaded">
        <b-table
          :items="items"
          :fields="tableFields"
          :sort-by="tableSortBy"
          :sort-desc="tableSortDesc"
          @sort-changed="onSortChanged"
          :striped="true"
          :bordered="false"
          :no-sort-reset="true"
          :no-local-sorting="true"
          responsive
          show-empty
          class="products-table card-table"
        >
          <template #cell(manage)="row">
            <b-btn size="sm" @click="row.toggleDetails">{{ row.detailsShowing ? 'Hide' : 'Show' }} Details</b-btn>
            <a class="btn btn-outline-secondary btn-sm" :href='getLogDetailsPath(row.item.id)' target="_blank">
              <i class="ion ion-md-open"></i>
            </a>
          </template>
          <template #row-details="row">
            <user-activity-details
              :item="row.item"
            />
          </template>
        </b-table>
        <!-- Pagination -->
        <paging
          :pageNumber="filter.page"
          :pageSize="filter.pageSize"
          :totalItems="itemsTotalCount"
          titled
          pageSizeSelector
          @numberChanged="pageChanged"
          @changePageSize="changePageSize"
        />
      </div>
      <div class="py-3" v-else>
        <loader size="lg"/>
      </div>
    </b-card>
  </div>
</template>

<script>
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import Constants from '@/shared/craigslist/constants'
import {userTypes} from '@/shared/users/constants'
import moment from 'moment'

const defaultFilters = new ObjectSchema({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  search: { type: String, default: '' },
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  sort: { type: Number, default: 6 },
  userAction: { type: Number, default: 0 },
  userTypes: { type: String, default: `${userTypes.cpUser.value},${userTypes.adminUser.value}` }
})
const queryHelper = new QueryStringHelper(defaultFilters)

export default {
  name: 'user-activity',
  metaInfo: {
    title: 'User Activity'
  },
  components: {
    'paging': () => import('@/components/_shared/paging'),
    'date-time-picker': () => import('@gravitano/vue-date-range-picker/src/components/DateRangePicker'),
    'user-activity-details': () => import('@/components/craigslist/userActivity/userActivityDetails'),
    'loader': () => import('@/components/_shared/loader.vue'),
    'multiSelectWithCheckboxes': () => import('@/components/_shared/multiSelectWithCheckboxes.vue')
  },
  data () {
    return {
      items: [],
      itemsTotalCount: 0,
      isLoaded: false,
      filter: defaultFilters.getObject(),
      filterTimeOptions: {
        autoUpdateInput: false,
        singleDatePicker: true,
        timePicker: true,
        timePicker24Hour: true,
        maxDate: new Date()
      }
    }
  },
  created () {
    this.filter = queryHelper.parseQueryStringToObject(this.$router)
    this.loadContent()
  },
  computed: {
    selectedUserTypes: {
      get () {
        return this.getIntegerArrayFromString(this.filter.userTypes)
      },
      set (value) {
        this.filter.userTypes = value ? value.join() : ''
      }
    },
    tableFields () {
      return [
        {
          key: 'requestInformation.userName',
          sortable: true,
          sortTypeAsc: Constants.userActivitySortTypes.userNameAsc,
          sortTypeDesc: Constants.userActivitySortTypes.userNameDesc,
          label: 'User Name',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'requestInformation.user.userType',
          sortable: true,
          sortTypeAsc: Constants.userActivitySortTypes.userTypeAsc,
          sortTypeDesc: Constants.userActivitySortTypes.userTypeDesc,
          label: 'User Type',
          tdClass: 'py-2 align-middle',
          formatter: val => (Object.values(userTypes).find(x => x.value === val) || {text: '-'}).text
        },
        {
          key: 'accountId',
          sortable: true,
          sortTypeAsc: Constants.userActivitySortTypes.accountIdAsc,
          sortTypeDesc: Constants.userActivitySortTypes.accountIdDesc,
          label: 'Account Id',
          tdClass: 'py-2 align-middle',
          formatter: val => val || '-'
        },
        {
          key: 'accountName',
          sortable: true,
          sortTypeAsc: Constants.userActivitySortTypes.accountNameAsc,
          sortTypeDesc: Constants.userActivitySortTypes.accountNameDesc,
          tdClass: 'py-2 align-middle',
          formatter: val => val || '-'
        },
        {
          key: 'action',
          sortable: true,
          sortTypeAsc: Constants.userActivitySortTypes.actionAsc,
          sortTypeDesc: Constants.userActivitySortTypes.actionDesc,
          label: 'Action Name',
          tdClass: 'py-2 align-middle',
          formatter: value => {
            let action = Object.values(Constants.userActivityActionTypes).find(x => x.value === value)
            return action ? action.text : '-'
          }
        },
        {
          key: 'requestInformation.callDateTime',
          sortable: true,
          sortTypeAsc: Constants.userActivitySortTypes.dateAsc,
          sortTypeDesc: Constants.userActivitySortTypes.dateDesc,
          label: 'Date',
          tdClass: 'py-2 align-middle',
          formatter: value => {
            return moment(value).format('MM/DD/YYYY hh:mm:ss A')
          }
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    getUserActionOptions () {
      return Object.values(Constants.userActivityActionTypes).sort((left, right) => left.text.localeCompare(right.text))
    },
    getUserTypeOptions () {
      return Object.values(userTypes)
    },
    refDateTimeFrom () {
      return (this.$refs.timeFrom || {}).$el || {}
    },
    refDateTimeTo () {
      return (this.$refs.timeTo || {}).$el || {}
    },
    tableSortBy () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sortType
      } else {
        return false
      }
    }
  },
  methods: {
    loadContent () {
      this.$store.dispatch('craigslist/getUserActivity', {filter: this.filter}).then(x => {
        this.items = x.data.model.items
        this.itemsTotalCount = x.data.model.totalItemsCount
      }).catch(ex => {
        this.$toaster.error('Something went wrong!', {timeout: 7000})
        this.$logger.handleError(ex, 'Can\'t get craigslist user activity listing', {filters: this.filters})
      }).finally(() => {
        this.isLoaded = true
      })
    },
    pageChanged (newPage) {
      this.filter.page = newPage
      this.isLoaded = false
      this.synchronizeUrlAndReload()
    },
    changePageSize (newSize) {
      this.filter.pageSize = newSize
      this.filter.page = 1
      this.isLoaded = false
      this.synchronizeUrlAndReload()
    },
    onSortChanged (value) {
      const sortingColumn = this.tableFields.find(x => x.key === value.sortBy)
      if (value.sortDesc) {
        this.filter.sort = sortingColumn.sortTypeDesc
      } else {
        this.filter.sort = sortingColumn.sortTypeAsc
      }
      this.synchronizeUrlAndReload()
    },
    onTimeFromInputChange (newVal) {
      this.refDateTimeFrom.value = newVal || this.filter.dateFrom || null
    },
    onTimeToInputChange (newVal) {
      this.refDateTimeTo.value = newVal || this.filter.dateTo || null
    },
    applySearch () {
      this.filter.page = 1
      this.isLoaded = false
      this.synchronizeUrlAndReload()
    },
    synchronizeUrlAndReload () {
      queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.loadContent()
    },
    getIntegerArrayFromString (str) {
      if (!str || !str.trim()) {
        return []
      }
      let array = []
      for (let numberStr of str.split(',')) {
        let number = parseInt(numberStr)
        if (!isNaN(number)) {
          array.push(number)
        }
      }
      return array
    },
    getLogDetailsPath (id) {
      return `/craigslist/useractivity/${id}/details`
    }
  }
}
</script>

<style lang="scss">
.change-marging {
  margin-right: 0.25rem;
  margin-left: 0.25rem;
}
</style>
