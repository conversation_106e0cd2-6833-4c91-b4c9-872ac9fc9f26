﻿using EBizAutos.ApplicationCommonLib.Applications.Photos;
using EBizAutos.Apps.AccountManagement.Api.HostedServices;
using EBizAutos.Apps.AccountManagement.Api.Models.Messages;
using EBizAutos.Apps.AccountManagement.Api.ServiceBus.Consumers;
using EBizAutos.Apps.AccountManagement.Api.ServiceBus.Handlers;
using EBizAutos.Apps.AccountManagement.Api.Utilities.Managers;
using EBizAutos.Apps.AccountManagement.WebApp.Utilities.Managers;
using EBizAutos.Apps.Api.Client;
using EBizAutos.Apps.Authentication.MongoDbRepository;
using EBizAutos.Apps.Common.MongoDbRepository;
using EBizAutos.Apps.Common.MongoDbRepository.Logs;
using EBizAutos.Apps.CommonLib.Abstract.Repositories.AppsAccountGroups;
using EBizAutos.Apps.CommonLib.Abstract.Repositories.AppsAccountSettings;
using EBizAutos.Apps.CommonLib.Abstract.Repositories.AppsContactDependencies;
using EBizAutos.Apps.CommonLib.Abstract.Repositories.AppsContactInformation;
using EBizAutos.Apps.CommonLib.Abstract.Repositories.AppsSiteSettings;
using EBizAutos.Apps.CommonLib.Abstract.Repositories.AppsUser;
using EBizAutos.Apps.CommonLib.Abstract.Repositories.Logs;
using EBizAutos.Apps.CommonLib.Abstract.Repositories.Logs.AccountManagement;
using EBizAutos.Apps.CommonLib.Abstract.Repositories.PhotoServerSettings;
using EBizAutos.Apps.CommonLib.Utilities.Extensions;
using EBizAutos.Apps.ServiceBus.Configuration;
using EBizAutos.Apps.ServiceBus.Consumers.Tests;
using EBizAutos.Apps.ServiceBus.Events.Tests;
using EBizAutos.Apps.ServiceBus.Events.User;
using EBizAutos.Apps.ServiceBus.Extensions;
using EBizAutos.Apps.ServiceBus.Logging;
using EBizAutos.Apps.SiteBoxManager.MongoDbRepository;
using EBizAutos.CommonLib.Encryption;
using EBizAutos.CommonLib.Exceptions;
using EBizAutos.CommonLib.Exceptions.ServiceBus.Events;
using EBizAutos.CommonLib.Interfaces;
using EBizAutos.CommonLib.Messaging.MongoDB;
using EBizAutos.CommonLib.RepositoryClasses.MongoDb;
using EBizAutos.CommonLib.ServiceBus;
using EBizAutos.CommonLib.ServiceBus.Configuration;
using EBizAutos.FoundationCommonLib.Abstract.Repositories.AppsNet.SiteBoxManagement;
using GreenPipes.Partitioning;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Serilog;
using Serilog.Exceptions;
using Serilog.Filters;
using System;
using System.Collections.Generic;

namespace EBizAutos.Apps.AccountManagement.Api.Utilities.Extensions {
	public static class ServiceCollectionExtensions {
		private const string _constModifyAccountQueueName = "_QueueTaskModifyAccountCollection";
		private const string _constAccountModificationLogsCollection = "AccountModificationLogsCollection";

		public static void RegisterCustomServices(this IServiceCollection services, AppConfiguration appConfiguration) {
			services.AddSingleton(
				sp => new ExceptionHandler(
					appConfiguration.ExceptionSettings,
					appConfiguration.AppSettings.ApplicationName,
					sp
				)
			);

			#region Configuration
			services.AddSingleton(appConfiguration);
			services.AddSingleton(appConfiguration.AppsApiSettings);
			#endregion

			#region Http Accessor
			services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
			#endregion

			#region Encryption
			services.AddSingleton<IEncryptionProvider, RijndaelEncryptionProvider>(serviceProvider => {
				return new RijndaelEncryptionProvider(appConfiguration.AppSettings.DataAccessEncryptKey, appConfiguration.AppSettings.DataAccessEncryptIV);
			}
			);
			#endregion

			services.AddLogHostedService(appConfiguration.AppSettings.ApplicationName, appConfiguration.AppSettings.LogParallelismDegree);

			#region Repositories
			services.AddScoped<IUserRepository, CPUserMongoDbRepository>(serviceProvider => {
				return new CPUserMongoDbRepository(
					appConfiguration.DbSettings.UsersMongoDbRepositoryConnectionString,
					serviceProvider.GetService<IEncryptionProvider>()
				);
			}
			);

			services.AddScoped<IAppsPhotoServerSettingsRepository, AppsPhotoServerSettingsMongoDbRepository>(serviceProvider => {
				return new AppsPhotoServerSettingsMongoDbRepository(appConfiguration.DbSettings.AppsPhotoServerSettingsMongoDbRepositoryConnectionString);
			}
			);

			services.AddScoped<IAppsAccountSettingsRepository, AppsAccountSettingsMongoDbRepository>(serviceProvider =>
																										new AppsAccountSettingsMongoDbRepository(appConfiguration.DbSettings.AppsAccountSettingsMongoDbRepositoryConnectionString)
			);

			services.AddScoped<IAppsContactRepository, AppsContactMongoDbRepository>(serviceProvider =>
																						new AppsContactMongoDbRepository(appConfiguration.DbSettings.AppsContactsMongoDbRepositoryConnectionString)
			);

			services.AddScoped<IAppsAccountGroupRepository, AppsAccountGroupMongoDbRepository>(serviceProvider =>
																									new AppsAccountGroupMongoDbRepository(appConfiguration.DbSettings.AppsGroupsMongoDbRepositoryConnectionString)
			);

			services.AddTransient<IAppsServiceBusLogRepository, AppsServiceBusLogRepository>(serviceProvider =>
																								new AppsServiceBusLogRepository(
																									appConfiguration.DbSettings.AppsServiceBusLogsMongoDbRepositoryConnectionString,
																									serviceProvider.GetService<ExceptionHandler>(),
																									appConfiguration.AppSettings.ApplicationName,
																									appConfiguration.AppSettings.IsLoggingOn,
																									null
																								)
			);

			services.AddScoped<IAppsAccountManagementLogRepository, AppsAccountManagementLogMongoDbRepository>(serviceProvider => new AppsAccountManagementLogMongoDbRepository(
					appConfiguration.DbSettings.AppsAccountManagementLogsMongoDbRepositoryConnectionString,
					serviceProvider.GetService<ExceptionHandler>(),
					appConfiguration.AppSettings.ApplicationName,
					appConfiguration.AppSettings.IsLoggingOn,
					serviceProvider.GetService<Func<LogTaskScheduler>>().Invoke()
				)
			);

			services.AddScoped<IAppsContactManagementLogRepository, AppsContactManagementLogMongoDbRepository>(serviceProvider =>
				new AppsContactManagementLogMongoDbRepository(
					appConfiguration.DbSettings.AppsAccountManagementLogsMongoDbRepositoryConnectionString,
					serviceProvider.GetService<ExceptionHandler>(),
					appConfiguration.AppSettings.ApplicationName,
					appConfiguration.AppSettings.IsLoggingOn,
					serviceProvider.GetService<Func<LogTaskScheduler>>().Invoke()
				)
			);

			services.AddScoped<IAppsAccountGroupManagementLogRepository, AppsAccountGroupManagementLogMongoDbRepository>(serviceProvider =>
				new AppsAccountGroupManagementLogMongoDbRepository(
					appConfiguration.DbSettings.AppsAccountManagementLogsMongoDbRepositoryConnectionString,
					serviceProvider.GetService<ExceptionHandler>(),
					appConfiguration.AppSettings.ApplicationName,
					appConfiguration.AppSettings.IsLoggingOn,
					serviceProvider.GetService<Func<LogTaskScheduler>>().Invoke()
				)
			);

			services.AddScoped<IContactDependenciesConfigurationRepository, AppsContactDependenciesConfigurationMongoDbRepository>(serviceProvider =>
				new AppsContactDependenciesConfigurationMongoDbRepository(
					appConfiguration.DbSettings.AppsAccountSettingsMongoDbRepositoryConnectionString
				)
			);

			services.AddScoped<IAppsSiteboxSettingsRepository, AppsSiteboxSettingsMongoDbRepository>(serviceProvider =>
				new AppsSiteboxSettingsMongoDbRepository(
					appConfiguration.DbSettings.AppsSiteboxMongoDbRepositoryConnectionString
				)
			);

			services.AddScoped<IAppsSiteHostingSettingsRepository, AppsSiteHostingSettingsMongoDbRepository>(serviceProvider =>
				new AppsSiteHostingSettingsMongoDbRepository(
					appConfiguration.DbSettings.AppsSiteboxMongoDbRepositoryConnectionString
				)
			);

			services.AddScoped<IAppsClosedAccountRepository, AppsClosedAccountMongoDbRepository>(serviceProvider =>
				new AppsClosedAccountMongoDbRepository(appConfiguration.DbSettings.AppsAccountSettingsMongoDbRepositoryConnectionString)
			);

			services.AddScoped<IAppsSiteSettingsRepository, AppsSiteSettingsMongoDbRepository>(serviceProvider =>
				new AppsSiteSettingsMongoDbRepository(appConfiguration.DbSettings.AppsSiteSettingsMongoDbRepositoryConnectionString)
			);
			#endregion

			#region Factories
			services.AddSingleton<Func<string, IContactDependenciesCheckingRepository>>(
				serviceProvider => (connectionString) => new AppsContactDependenciesCheckingMongoDbRepository(connectionString)
			);
			#endregion

			#region Photo Server Settings
			services.AddSingleton(sp => {
				IAppsPhotoServerSettingsRepository repository = new AppsPhotoServerSettingsMongoDbRepository(appConfiguration.DbSettings.AppsPhotoServerSettingsMongoDbRepositoryConnectionString);
				PhotoServersSet photoServersSet = repository.GetSettings();

				return photoServersSet;
			}
			);
			#endregion

			#region Business
			services.AddScoped<AppsAccountManager>();
			services.AddScoped<AppsContactManager>();
			services.AddScoped<AppsGroupManager>();
			services.AddScoped<AppsUserActivityManager>();
			#endregion

			#region Apps Web APi Client
			services.AddScoped(serviceProvider => {
				return new AppsWebApiClient(
					appConfiguration.AppsApiSettings.AppsBaseUrl,
					appConfiguration.AppsApiSettings.AppsAuthorizationToken,
					appConfiguration.AppsApiSettings.RequestTimeoutInMs
				);
			}
			);
			#endregion

			#region MongoDB Queue
			services.AddSingleton<IQueueProducer<ModifyAccountMessage>>(serviceProvider => {
				return new MongoDbQueueProducer<ModifyAccountMessage>(appConfiguration.DbSettings.AppsAccountSettingsMongoDbRepositoryConnectionString, _constModifyAccountQueueName);
			}
			);
			services.AddSingleton<IQueueConsumer<ModifyAccountMessage>>(serviceProvider => {
				return new MongoDbQueueConsumer<ModifyAccountMessage>(
					appConfiguration.DbSettings.AppsAccountSettingsMongoDbRepositoryConnectionString,
					_constModifyAccountQueueName,
					defaultMessageLeaseSeconds: appConfiguration.AppSettings.ModifyAccountRetryTimeOnFailInSeconds,
					maxDeliveryAttempts: appConfiguration.AppSettings.ModifyAccountAttemptsCount
				);
			}
			);
			#endregion

			services.AddSingleton(new LoggerConfiguration()
									.WriteTo.Logger(lc => lc
														.MinimumLevel.Information()
														.Enrich.WithExceptionDetails()
														.Filter.ByIncludingOnly(Matching.FromSource<ModifyAccountService>())
														.WriteTo.MongoDB(appConfiguration.DbSettings.AppsAccountManagementLogsMongoDbRepositoryConnectionString, collectionName: _constAccountModificationLogsCollection)
									)
									.CreateLogger() as ILogger
			);

			#region Service Bus
			services.AddTransient<TestEventsConsumer>();

			services.AddTransient<IConsumer<ITestErrorEvent>, TestErrorEventConsumer>(
				sp => new TestErrorEventConsumer(sp.GetRequiredService<ExceptionHandler>(), appConfiguration.AppSettings.ApplicationName)
			);

			services.Scan(scan => scan
				.FromAssemblyOf<UserUpdatedEventHandler>()
				.AddClasses(classes => classes.AssignableTo(typeof(IEventHandler<,>))).AsImplementedInterfaces().WithTransientLifetime()
				.FromAssemblyOf<UserUpdatedEventConsumer>()
				.AddClasses(classes => classes.AssignableTo(typeof(IConsumer<>))).AsImplementedInterfaces().WithTransientLifetime()
			);

			HashSet<Type> ignoreLoggingEventTypes = new HashSet<Type> { typeof(ISaveErrorEvent) };

			IHostSettings serviceBusHostSettings = new HostSettings(appConfiguration.ServiceBusSettings.Host,
																	appConfiguration.ServiceBusSettings.Port,
																	appConfiguration.ServiceBusSettings.Username,
																	appConfiguration.ServiceBusSettings.Password,
																	appConfiguration.ServiceBusSettings.FailoverHosts,
																	appConfiguration.ServiceBusSettings.HasToUseSsl
			);

			IPublishSettings publishSettings = new PublishSettings(
				appConfiguration.ServiceBusSettings.PublishSettings.RetryAttempts,
				appConfiguration.ServiceBusSettings.PublishSettings.MinRetryDelayInMs,
				appConfiguration.ServiceBusSettings.PublishSettings.MaxRetryDelayInMs
			);

			services.AddLogDecoratedAppsServiceBus(
				serviceBusHostSettings,
				publishSettings,
				RegisterServiceBusHandlers,
				ignoreLoggingEventTypes
			);
			#endregion

			services.AddHostedService<ModifyAccountService>();
		}

		private static void RegisterServiceBusHandlers(IServiceBusConfigurator serviceBusConfigurator, IServiceProvider serviceProvider) {
			serviceBusConfigurator.ReceiveEndpoint("ebiz_apps_accountmanagement_api_user_events", configurator => {
				configurator.Durable = true;
				IPartitioner partitioner = configurator.CreatePartitioner(1);

				configurator.Consumer(
					() => serviceProvider.GetRequiredService<IConsumer<IUserUpdatedEvent>>(),
					cfg => {
						cfg.UsePartitioner(partitioner, x => x.ContactId);
					}
				);
			});

			serviceBusConfigurator.ReceiveEndpoint("ebiz_apps_accountmanagement_api_test_events", x => {
				x.Durable = true;
				x.Consumer(
					() => serviceProvider.GetRequiredService<TestEventsConsumer>(),
					configurator => configurator.UseImmediateMessageRetry(3)
				);

				x.Consumer(
					() => serviceProvider.GetRequiredService<IConsumer<ITestErrorEvent>>(),
					configurator => configurator.UseImmediateMessageRetry(3)
				);
			});
		}
	}
}