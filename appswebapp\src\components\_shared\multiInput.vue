<template>
<ValidationObserver slim ref="validator">
  <div class='w-100'>
    <div v-if='options'>
      <b-input-group v-for='(item, index) in values' :key='index' class='mt-1'>
        <b-form-select  v-model='values[index]' :value='item' class="multi-select" :options='options' :disabled="disabled"/>
        <b-input-group-append v-if="!disabled">
          <b-btn class="multi-input-btn" @click='remove(index)' variant="primary"><i class="ion ion-ios-close h4 m-0 opacity-75"></i></b-btn>
        </b-input-group-append>
      </b-input-group>
      <b-input-group class='mt-1' v-if="!disabled">
        <b-form-select  v-model='newValue' class="multi-select" :options='options'/>
        <b-input-group-append>
          <b-btn class="multi-input-btn" @click='add()' variant="success"><i class="ion ion-ios-checkmark h4 m-0 opacity-75"></i></b-btn>
        </b-input-group-append>
      </b-input-group>
    </div>
    <div v-else>
      <b-input-group v-for='(item, index) in values' :key='index' class='mt-1'>
        <ValidationProvider slim immediate :rules="validateRules" v-slot="{errors}">
          <b-form-input v-model='values[index]' :state="errors[0] ? false : null" :type='type' :placeholder='placeholder' :disabled="disabled"></b-form-input>
        </ValidationProvider>
        <b-input-group-append v-if="!disabled">
          <b-btn class="multi-input-btn" @click='remove(index)' variant="primary"><i class="ion ion-ios-close h4 m-0 opacity-75"></i></b-btn>
        </b-input-group-append>
      </b-input-group>
      <ValidationObserver slim v-slot="{ invalid }" v-if="!disabled">
        <ValidationProvider immediate :rules="validateRules" v-slot="{errors}">
          <b-input-group class='mt-1'>
            <b-form-input :name='type' v-model='newValue' :type='type' :placeholder='placeholder'/>
            <b-input-group-append>
              <b-btn class="multi-input-btn" @click='add()' :disabled="invalid" variant="success"><i class="ion ion-ios-checkmark h4 m-0 opacity-75"></i></b-btn>
            </b-input-group-append>
          </b-input-group>
          <span class='text-danger'>{{ errors[0] }}</span>
        </ValidationProvider>
      </ValidationObserver>
      <div v-if="!state">
        <span class="text-danger">List cannot be empty</span>
      </div>
    </div>
  </div>
</ValidationObserver>
</template>

<script>
export default {
  name: 'multi-input',
  props: {
    options: Array,
    values: { type: Array, default: () => [] },
    disabled: Boolean,
    type: { type: String, default: 'text' },
    placeholder: { type: String, default: '' },
    validateRules: String,
    state: { type: Boolean, default: true }
  },
  data () {
    return {
      newValue: ''
    }
  },
  methods: {
    add () {
      this.$refs.validator.validate().then(res => {
        if (res && this.newValue !== '') {
          this.values.push(this.newValue)
          this.newValue = ''
          this.$emit('input', this.values)
        }
      })
    },
    remove (index) {
      this.values.splice(index, 1)
    }
  }
}
</script>

<style>
.multi-input-btn {
  height: 38px;
}
.multi-select {
  flex: 1 1 auto !important;
}
</style>
