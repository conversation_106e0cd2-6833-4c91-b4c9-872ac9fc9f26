<template>
  <div v-if='!isLoading && !isException'>
    <div class="py-2">
      <b-row>
        <b-col class="d-flex align-items-center">
          <h4 class="m-0">{{isEditMode ? 'Edit' : 'Add'}} Email Proxy Campaign</h4>
        </b-col>
        <b-col class="d-flex justify-content-end">
          <b-btn variant="primary" size="sm" @click="onSubmit" class="mr-2 custom-btn-height" :disabled="isSubmitButtonDisabled">Submit</b-btn>
          <b-btn variant="secondary" size="sm" class="custom-btn-height" @click="$router.go(-1)">Close</b-btn>
        </b-col>
      </b-row>
    </div>
    <b-card>
      <!-- Proxy Email Settings Section -->
      <div class="mt-1">
        <div class="border-bottom">
          <b-row>
            <b-col><h6 class="float-left">Proxy Email Settings</h6></b-col>
          </b-row>
        </div>
        <div>
          <detail-row bigPayloadWidth>
            <span slot="title">Email Proxy:</span>
            <b-input-group slot="payload">
              <b-form-input v-model='emailCommunication.proxyEmailAlias' :disabled='isEditMode'></b-form-input>
              <b-input-group-append>
                <b-btn size="sm" disabled variant="light">
                  <b>@ebizdealers.com</b>
                </b-btn>
              </b-input-group-append>
            </b-input-group>
          </detail-row>
        </div>
      </div>
      <!-- Proxy Email Settings Section End -->
      <!-- Campaign Settings Section -->
      <div class="mt-4">
        <div class="border-bottom">
          <b-row>
            <b-col><h6 class="float-left">Campaign Settings</h6></b-col>
          </b-row>
        </div>
        <div>
          <detail-row fixedPayloadWidth v-if='!isEditMode'>
            <span slot="title">Location Type:</span>
            <b-form-select v-model='selectedCampaignsType' :value='getLocationOptions[0].value' :options='getLocationOptions' slot="payload"/>
          </detail-row>
          <detail-row fixedPayloadWidth>
            <span slot="title">Campaign(s):</span>
            <multiselect slot="payload"
              v-model='selectedCampaigns'
              track-by="campaignTypeId"
              :options='availableCampaigns'
              label='campaignName'
              :multiple="true"
              :disabled='isEditMode'
              :closeOnSelect='false'
              :showLabels='false'
              :showPointer='false'
              :limit='3'
            />
          </detail-row>
          <detail-row fixedPayloadWidth>
            <span slot="title">Contact(s):</span>
            <multiselect slot="payload"
              v-model='emailCommunication.contacts'
              :options='availableContacts'
              group-values="data"
              group-label="groupName"
              track-by="contactId"
              label="email"
              :closeOnSelect='false'
              :group-select="true"
              :multiple="true"
              :showLabels='false'
              :showPointer='false'
              :limit='3'
            />
          </detail-row>
          <detail-row fixedPayloadWidth>
            <span slot="title">Legacy Campaign Id:</span>
            <b-form-select v-model='emailCommunication.legacyCampaignId' slot="payload" :options='legacyCampaignIds' />
          </detail-row>
          <detail-row fixedPayloadWidth v-if='emailCommunication.campaignsType === campaignTypes.offSiteParent.value'>
            <span slot="title">Departments:</span>
            <b-form-select v-model='emailCommunication.departmentType' text-field='name' value-field='id' slot="payload" :options='availableDepartmentTypes' />
          </detail-row>
        </div>
      </div>
      <!-- Campaign Settings Section End -->
      <!-- Tracking & Notification Settings Section -->
      <div class="mt-4">
        <div class="border-bottom">
          <b-row>
            <b-col><h6 class="float-left">Tracking & Notification Settings</h6></b-col>
          </b-row>
        </div>
        <div>
          <div class="d-flex flex-column mt-2">
            <b-form-checkbox v-model='emailCommunication.notificationSettings.hasToSendUserEmail' class="mt-3">Send Auto Response Email to Lead</b-form-checkbox>
            <b-form-checkbox v-if="emailCommunication.notificationSettings.hasToShowDealerSocketSettings" v-model='emailCommunication.notificationSettings.hasToSendDealerSocketLead' class="mt-3">Send Dealer Socket Lead</b-form-checkbox>
            <b-form-checkbox v-if="emailCommunication.notificationSettings.hasToShowShiftDigitalSettings" v-model='emailCommunication.notificationSettings.hasToSendShiftDigitalLead' class="mt-3">Send Shift Digital Lead</b-form-checkbox>
            <b-form-checkbox v-model='emailCommunication.notificationSettings.hasToSendDealerEmail' class="mt-3">Send Notification Email to Dealership</b-form-checkbox>
            <div class="mt-3 d-flex flex-row ml-4" v-if='emailCommunication.notificationSettings.hasToSendDealerEmail'>
              <div>
                <label class="text-muted" for="DealershipEmailTo">Dealership Emails (To):</label>
                <multi-input
                  type="email"
                  validateRules='email'
                  :values='emailCommunication.notificationSettings.dealerEmailAddresses'
                  placeholder="<EMAIL>"
                  v-model='emailCommunication.notificationSettings.dealerEmailAddresses'
                />
              </div>
              <div class="ml-4">
                <label class="text-muted" for="DealershipEmailCC">Dealership Emails (CC):</label>
                <multi-input
                  type="email"
                  validateRules='email'
                  :values='emailCommunication.notificationSettings.dealerEmailAddressesCc'
                  placeholder="<EMAIL>"
                  v-model="emailCommunication.notificationSettings.dealerEmailAddressesCc"
                />
              </div>
            </div>
            <b-form-checkbox v-model='emailCommunication.notificationSettings.hasToSendAdfEmail' class="mt-3">Send ADF Email to CRM</b-form-checkbox>
            <div v-if='emailCommunication.notificationSettings.hasToSendAdfEmail'>
              <div class="mt-3 d-flex flex-row ml-4">
                <div>
                  <label class="text-muted" for="ADFEmailTo">ADF Emails (To):</label>
                  <multi-input
                    type="email"
                    validateRules='email'
                    :values='emailCommunication.notificationSettings.adfEmailAddresses'
                    placeholder="<EMAIL>"
                    v-model="emailCommunication.notificationSettings.adfEmailAddresses"
                  />
                </div>
                <div class="ml-4">
                  <label class="text-muted" for="ADFEmailCC">ADF Emails (CC):</label>
                  <multi-input
                    type="email"
                    validateRules='email'
                    :values='emailCommunication.notificationSettings.adfEmailAddressesCc'
                    placeholder="<EMAIL>"
                    v-model="emailCommunication.notificationSettings.adfEmailAddressesCc"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- Tracking & Notification Settings Section End -->
    </b-card>
  </div>
  <div v-else-if='isLoading && !isException' class="mt-5">
    <loader size="lg"/>
  </div>
  <div v-else class="mt-5">
    <h4 class="text-center text-muted">Something went wrong! Please reload page</h4>
  </div>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import CommunicationService from '@/services/leads/CommunicationService'
import { communicationTypes } from '@/shared/leads/common'
import { campaignTypes } from '@/shared/leads/campaignTypes'
import Multiselect from 'vue-multiselect'
import multiInput from '@/components/_shared/multiInput'
import loader from '@/components/_shared/loader'

export default {
  name: 'leads-add-edit-email-proxy-campaign',
  props: {
    accountId: { type: Number, required: true },
    communicationId: { type: String }
  },
  data () {
    return {
      campaignTypes,
      isLoading: true,
      isException: false,
      isSubmitButtonDisabled: false,
      emailCommunication: null,
      legacyCampaignIds: [],
      selectedCampaigns: [],
      availableCampaigns: [],
      availableContacts: [],
      availableDepartmentTypes: []
    }
  },
  created () {
    if (this.isEditMode) {
      this.populateCommunicationModelById(this.communicationId)
    } else {
      this.populateCommunicationPrototypeModel()
    }
  },
  computed: {
    isEditMode () {
      if (this.communicationId) {
        return true
      }

      return false
    },
    getLocationOptions () {
      return Object.values(campaignTypes)
    },
    selectedCampaignsType: {
      get () {
        if (this.emailCommunication && this.emailCommunication.campaignsType !== 0) {
          return this.emailCommunication.campaignsType
        }

        return 1
      },
      set (value) {
        this.emailCommunication.campaignsType = value
        this.selectedCampaigns = []
        this.populateAvailableCampaigns()
        this.populateAvailableDepartmentTypes()
      }
    }
  },
  components: {
    'detail-row': detailRow,
    'multiselect': Multiselect,
    'multi-input': multiInput,
    'loader': loader
  },
  methods: {
    finalize () {
      this.$router.push({name: 'leads-dashboard', params: {accountId: this.accountId}})
    },
    populateCommunicationModelById () {
      CommunicationService.getCommunicationSettings(this.accountId, this.communicationId).then(res => {
        this.availableDepartmentTypes = res.data.availableDepartmentTypes
        this.legacyCampaignIds = res.data.availableLegacyCampaignIds
        this.emailCommunication = res.data.communication
        this.availableCampaigns = res.data.communication.campaignTypes
        this.selectedCampaigns = res.data.communication.campaignTypes
        this.setAvailableContacts(res.data.availableContacts)
      }).catch(ex => {
        this.isException = true
        this.$toaster.exception(ex, `Cannot get Communication Details Model`, {timeout: 5000})
        this.$logger.handleError(ex, `Cannot get Communication Details Model with id:${this.communicationId} for accountId:${this.accountId}`)
      }).finally(() => {
        this.isLoading = false
      })
    },
    populateCommunicationPrototypeModel () {
      CommunicationService.getCommunicationPrototype(this.accountId, communicationTypes.email.value).then(res => {
        this.emailCommunication = res.data.communication
        this.availableCampaigns = res.data.availableCampaigns
      }).catch(ex => {
        this.isException = true
        this.$toaster.error(`Cannot get Communication Details Model`)
        this.$logger.handleError(ex, `Cannot get Communication Details Model with id:${this.communicationId} for accountId:${this.accountId}`)
      }).finally(() => {
        this.isLoading = false
      })
    },
    setAvailableLegacyCampaignIds () {
      if (!this.isEditMode) {
        let campaigns = this.selectedCampaigns.map(x => x.campaignTypeId).toString()
        if (campaigns && campaigns.length > 0) {
          CommunicationService.getLegacyCampaignIds(campaigns).then(res => {
            this.legacyCampaignIds = res.data
          }).catch(ex => {
            this.legacyCampaignIds = []
            this.$toaster.error('Cannot get available legacy campaign ids')
            this.$logger.handleError(ex, 'Cannot get available legacy campaign ids')
          })
        } else {
          this.legacyCampaignIds = []
          this.emailCommunication.legacyCampaignId = null
        }
      }
    },
    populateAvailableCampaigns () {
      CommunicationService.getAvailableCampaigns(this.emailCommunication.campaignsType, null).then(res => {
        this.availableCampaigns = res.data
      }).catch(ex => {
        this.$toaster.error('Cannot get available campaigns')
        this.$logger.handleError(ex, `cannot get available campaigns for campaign type: ${this.emailCommunication.campaignsType}`)
      })
    },
    populateAvailableDepartmentTypes () {
      if (this.emailCommunication.campaignsType === this.campaignTypes.offSiteParent.value && !this.isEditMode) {
        let campaigns = this.selectedCampaigns.map(x => x.campaignTypeId).toString()
        if (campaigns && campaigns.length > 0) {
          let filter = {
            communicationType: communicationTypes.email.value,
            campaignTypeIds: campaigns
          }
          CommunicationService.getAvailableDepartmentTypes(this.accountId, filter).then(res => {
            this.availableDepartmentTypes = res.data
          }).catch(ex => {
            this.availableDepartmentTypes = []
            this.$toaster.error('Cannot get available department types')
            this.$logger.handleError(ex, 'Cannot get available department types')
          })
        } else {
          this.availableDepartmentTypes = []
          this.emailCommunication.departmentType = null
        }
      }
    },
    populateAvailableContacts () {
      if (!this.isEditMode) {
        let campaigns = this.selectedCampaigns.map(x => x.campaignTypeId).toString()
        if (campaigns && campaigns.length > 0) {
          let filter = {
            communicationType: communicationTypes.email.value,
            campaignTypeIds: campaigns,
            includeExistingContacts: this.selectedCampaignsType === campaignTypes.offSiteParent.value
          }
          CommunicationService.getAvailableContacts(this.accountId, filter).then(res => {
            this.setAvailableContacts(res.data)
          }).catch(ex => {
            this.availableContacts = []
            this.$toaster.error('Cannot get available contacts')
            this.$logger.handleError(ex, 'Cannot get available legacy campaign ids')
          })
        } else {
          this.legacyCampaignIds = []
          this.emailCommunication.legacyCampaignId = null
        }
      }
    },
    setAvailableContacts (newContacts) {
      this.availableContacts = []
      if (newContacts && newContacts.length > 0) {
        newContacts.forEach(contact => {
          this.availableContacts.push({
            groupName: contact.contactNameWithDepartments,
            data: [contact]
          })
        })
      }
    },
    onSubmit () {
      this.isSubmitButtonDisabled = true
      if (this.validate()) {
        if (this.isEditMode) {
          this.updateCommunication()
        } else {
          this.createNewCommunication()
        }
      } else {
        this.isSubmitButtonDisabled = false
      }
    },
    updateCommunication () {
      CommunicationService.updateCommunication(this.accountId, this.emailCommunication).then(res => {
        this.$toaster.success('Email Campaign Successfully Updated')
      }).catch(ex => {
        this.$toaster.exception(ex, `Cannot updated Email Campaign.`, {timeout: 5000})
        this.$logger.handleError(ex, 'Cannot updated Email Campaign', this.emailCommunicationModel)
      }).finally(() => {
        this.finalize()
      })
    },
    createNewCommunication () {
      this.emailCommunication.campaignTypes = this.selectedCampaigns
      CommunicationService.createNewCommunication(this.accountId, this.emailCommunication).then(res => {
        this.$toaster.success('New Email Campaign Successfully Created')
      }).catch(ex => {
        this.$toaster.exception(ex, `Cannot create Email Campaign.`, {timeout: 5000})
        this.$logger.handleError(ex, 'Cannot create Email Campaign', this.emailCommunicationModel)
      }).finally(() => {
        this.finalize()
      })
    },
    validate () {
      if (!this.emailCommunication.proxyEmailAlias || this.emailCommunication.proxyEmailAlias === '') {
        this.$toaster.error('Email Proxy field cannot be empty')
        return false
      }
      if (this.selectedCampaigns.length === 0) {
        this.$toaster.error('Campaigns field cannot be empty')
        return false
      }
      if (this.emailCommunication.contacts.length === 0) {
        this.$toaster.error('Contacts field cannot be empty')
        return false
      }
      if (!this.emailCommunication.legacyCampaignId || this.emailCommunication.legacyCampaignId === '') {
        this.$toaster.error('Legacy Campaign Id field cannot be empty')
        return false
      }
      if (this.emailCommunication.notificationSettings.hasToSendDealerEmail &&
          (!this.emailCommunication.notificationSettings.dealerEmailAddresses ||
          this.emailCommunication.notificationSettings.dealerEmailAddresses.length === 0)) {
        this.$toaster.error('Dealership Emails (To) field cannot be empty')
        return false
      }
      if (this.emailCommunication.notificationSettings.hasToSendAdfEmail &&
          (!this.emailCommunication.notificationSettings.adfEmailAddresses ||
          this.emailCommunication.notificationSettings.adfEmailAddresses.length === 0)) {
        this.$toaster.error('ADF Emails (To) field cannot be empty')
        return false
      }
      if (this.emailCommunication.campaignsType === this.campaignTypes.offSiteParent.value && !this.emailCommunication.departmentType) {
        this.$toaster.error('Departments: field cannot be empty')
        return false
      }

      return true
    }
  },
  watch: {
    'selectedCampaigns': {
      deep: true,
      handler: function () {
        if (!this.emailCommunication) {
          return
        }
        this.setAvailableLegacyCampaignIds()
        this.populateAvailableContacts()
        this.populateAvailableDepartmentTypes()
      }
    }
  }
}
</script>

<style>
.custom-btn-height {
  height: 35px;
}

.multiselect__option--group {
  font-size: 12px !important;
  padding-top: 12px !important;
  padding-left: 4px !important;
  margin-top: 0.5rem;
}
</style>
