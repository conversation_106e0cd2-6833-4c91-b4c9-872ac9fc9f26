<template>
  <div class="position-relative">
    <template v-if="isInventoryLoaded && !isException">
      <inventory-filters
        :refineSearchData="inventory.refineSearchData"
        :totalItems="inventory.vehiclesTotal"
        :pageNumber.sync="inventory.filters.page"
        :pageSize.sync="inventory.filters.pageSize"
        :sortType.sync="inventory.filters.sort"
        @filtersChanged="onFiltersChanged"
      />

      <inventory-listing-table
        :tableItems="inventory.vehicles"
        :totalItems="inventory.vehiclesTotal"
        :pageNumber.sync="inventory.filters.page"
        :pageSize.sync="inventory.filters.pageSize"
        :sortType.sync="inventory.filters.sort"
        :isPostingAllowed="isPostingAllowed"
        :status="inventory.filters.status"
      />
    </template>
    <div v-else-if="!isInventoryLoaded && !isException">
      <span class='text-muted'>Loading...</span>
    </div>
    <div v-else>
      <span class='text-muted'>Something has gone wrong. Please reload page</span>
    </div>
  </div>
</template>

<script>
import inventoryListingTable from './../../components/craigslist/inventory/inventoryListing'
import inventoryFilters from './../../components/craigslist/inventory/inventoryFilters'
import inventoryCraigslistService from './../../services/craigslist/InventoryCraigslistService'
import QueryStringHelper from './../../shared/common/queryStringHelper'
import defaultCraigslistInventoryFilters from './../../shared/craigslist/inventoryFilters'
import globals from './../../globals'
import lodash from 'lodash'

const queryStringHelper = new QueryStringHelper(defaultCraigslistInventoryFilters)

export default {
  metaInfo: {
    title: 'Craigslist Inventory'
  },
  components: {
    inventoryListingTable,
    inventoryFilters
  },
  data: function () {
    return {
      accountId: +this.$route.params.accountId,
      isPostingAllowed: false,
      isInventoryLoaded: false,
      isException: false,
      inventory: {
        vehicles: [],
        vehiclesTotal: 0,
        filters: this.convertQueryToInventoryFilters(),
        refineSearchData: {}
      }
    }
  },
  mounted: async function () {
    await this.fetchCraigslistInventory()
  },
  methods: {
    convertQueryToInventoryFilters () {
      const queryFilters = queryStringHelper.parseQueryStringToObject(this.$router)

      return globals().getClonedValue(queryFilters)
    },
    async fetchCraigslistInventory () {
      try {
        const result = await inventoryCraigslistService.getCraigslistInventory(this.accountId, this.inventory.filters)
        if (result.data.model) {
          this.isPostingAllowed = result.data.model.isPostingAllowed
          this.inventory.vehicles = result.data.model.vehicles
          this.inventory.vehiclesTotal = result.data.model.vehiclesTotal
          this.inventory.refineSearchData = result.data.model.filters
          this.isInventoryLoaded = true
        } else {
          this.$toaster.error('Cannot get craigslist inventory data')
          this.isException = true
        }
      } catch (ex) {
        this.isException = true
        this.$toaster.error('Can\'t get craigslist inventory data', { timeout: 8000 })
        this.$logger.handleError(ex, 'Can\'t fetch craigslist inventory model', this.inventory.filters)
      }
    },
    onFiltersChanged (filters) {
      lodash.assign(this.inventory.filters, filters)
    }
  },
  watch: {
    'inventory.filters': {
      deep: true,
      async handler () {
        queryStringHelper.rebuildParamsInQueryString(this.$router, this.inventory.filters)
        this.inventory.vehicles = []
        this.inventory.vehiclesTotal = 0
        this.isException = false
        await this.fetchCraigslistInventory()
      }
    }
  }
}
</script>
