﻿using System.Diagnostics;
using System.Threading.Tasks;
using EBizAutos.Apps.ServiceBus.Events;
using EBizAutos.CommonLib.ServiceBus;

namespace EBizAutos.Apps.AccountManagement.Api.ServiceBus.Consumers {
	public class TestEventsConsumer : IConsumer<ITestEvent> {
		public TestEventsConsumer(AppConfiguration appConfiguration) {
			var a = appConfiguration;
		}
		public async Task Consume(IConsumeContext<ITestEvent> context) {
			Debug.WriteLine($"AccountManagementApi consumed test event with name {context.Message.Name}");
		}
	}
}