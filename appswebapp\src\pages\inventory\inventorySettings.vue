<template>
  <div v-if="!isLoading">

    <div class="d-flex align-items-center justify-content-between">
      <h4 class="d-flex justify-content-start">
        Inventory Settings
      </h4>
    </div>

    <b-card>
      <div>
      <b-row class="border-bottom pb-1">
        <b-col class="align-middle">
          <span>
            <b>
              Vehicle History Reports
            </b>
          </span>
          <span class="text-muted" style="font-size:0.7rem;">
            / Last Modified: {{ getLastModifiedDateTime }}
          </span>
        </b-col>

          <b-btn variant="secondary" size="sm" @click="setEditInventorySettings" v-if='!isEditInventorySettings' >
            <font-awesome-icon icon="pencil-alt" size="sm" />
            <span class="btn-title">Edit</span>
          </b-btn>
          <template v-else>
            <b-btn variant="primary" size="sm" @click="onSave" >
              <font-awesome-icon icon="cloud-upload-alt" />
              <span class="btn-title">Save</span>
            </b-btn>
            <b-btn variant="secondary" class="ml-2" size="sm" @click="onCancel">
              <span class="btn-title">Cancel</span>
            </b-btn>
          </template>

      </b-row>

      <auto-detail-row v-if='!isEditInventorySettings' title='Vehicle History Type' :text='getVehicleHistoryTypeText(accountInventorySettingsModelOriginal.vehicleHistorySettings.vehicleHistoryType)' />
      <auto-detail-row v-else title='Vehicle History Type' v-model='accountInventorySettingsUpdateModel.vehicleHistorySettings.vehicleHistoryType' :options='vehicleHistoryTypeOptions'/>
      <b-alert
        v-if="accountInventorySettingsUpdateModel.vehicleHistorySettings.vehicleHistoryType === vehicleHistoryReportTypes.carfax.value
        && !accountInventorySettingsUpdateModel.vehicleHistorySettings.carfax.exportSettings.isAccountIncludedInCarfaxExport"
        show
        variant="warning"
      >
        <span>
          Carfax does not provide reports for vehicles in your list. Please contact your eBizAutos representatives to enable exporting the listing into the Carfax System.
        </span>
      </b-alert>

      <div v-if="accountInventorySettingsUpdateModel.vehicleHistorySettings.vehicleHistoryType === vehicleHistoryReportTypes.carfax.value">

        <auto-detail-row v-if='!isEditInventorySettings' title='Automatic Linking' :text='getAutomaticLinkingDescription(accountInventorySettingsModelOriginal.vehicleHistorySettings.carfax.isAutoLink)' />
        <detail-row v-else fixedPayloadWidth :editMode='isEditInventorySettings' largePayloadWidth>
          <span slot="title">Activate Automatic Linking:</span>
          <b-form-checkbox v-model="accountInventorySettingsUpdateModel.vehicleHistorySettings.carfax.isAutoLink" slot="payload">
          </b-form-checkbox>
        </detail-row>

        <div v-if="isEditInventorySettings">
          <auto-detail-row title='Auto Purchase Reports' v-model='accountInventorySettingsUpdateModel.vehicleHistorySettings.carfax.autoPurchaseType' :options='carfaxAutoPurchaseReports'/>
        </div>
        <div v-else>
          <auto-detail-row title='Auto Purchase Reports' :text='getTextByValueCarfaxAutoPurchaseReportsText(accountInventorySettingsModelOriginal.vehicleHistorySettings.carfax.autoPurchaseType)'/>
        </div>

        <div v-if="!isLoginDisabled">

          <b-row class="mt-3">
            <b-col>
              <span>
                To activate CARFAX integration within eBizAutos you must update or save your credentials by clicking Log In below.
              </span>
            </b-col>
          </b-row>

          <b-row class="mt-3">
            <b-col>
              <span>
                Log In to CARFAX with your email and password. You will be asked to save your login credentials.
              </span>
            </b-col>
          </b-row>

          <b-row>
            <b-col>
              <div class="d-flex justify-content-start align-items-start mt-2">
                <b-btn
                  type="submit"
                  @click="onLogIn"
                  variant="primary"
                  :disabled="isLoginDisabled"
                  size="sm"
                  >
                    Log In
                  </b-btn>
              </div>
            </b-col>
          </b-row>

        </div>

        <div v-else>
          <b-row>
            <b-col>
              <span class="mt-3">
                Sign out of CARFAX. No CARFAX features are available after signing out.
              </span>
            </b-col>
          </b-row>

          <b-row>
            <b-col>
              <div class="d-flex justify-content-start align-items-start mt-2">
                <b-btn
                  type="submit"
                  @click="onLogout"
                  variant="secondary"
                  :disabled="!isLoginDisabled"
                  size="sm"
                >
                  Sign Out
                </b-btn>
              </div>
            </b-col>
          </b-row>
        </div>

      </div>

      <div v-if="accountInventorySettingsUpdateModel.vehicleHistorySettings.vehicleHistoryType === vehicleHistoryReportTypes.autoCheck.value">

        <div v-if="isEditInventorySettings">
          <auto-detail-row title='AutoCheck ID' v-model='accountInventorySettingsUpdateModel.vehicleHistorySettings.autocheck.autocheckSid'/>
        </div>
        <div v-else>
          <auto-detail-row
            v-if="accountInventorySettingsModelOriginal.vehicleHistorySettings.autocheck.autocheckSid"
            title='AutoCheck ID'
            :text='accountInventorySettingsModelOriginal.vehicleHistorySettings.autocheck.autocheckSid'/>
            <span v-else class="text-primary">AutocheckID is missing</span>
        </div>

        <auto-detail-row v-if='!isEditInventorySettings' title='Automatic Linking' :text='getAutomaticLinkingDescription(accountInventorySettingsModelOriginal.vehicleHistorySettings.autocheck.isAutoLink)' />
        <detail-row v-else fixedPayloadWidth :editMode='isEditInventorySettings' largePayloadWidth>
          <span slot="title">Activate Automatic Linking:</span>
          <b-form-checkbox v-model="accountInventorySettingsUpdateModel.vehicleHistorySettings.autocheck.isAutoLink" slot="payload">
            Recommended
          </b-form-checkbox>
        </detail-row>

      </div>

      <inventory-photo-settings v-if="canManagePhotoSettings" :inventoryPhotoSettings="accountInventorySettingsUpdateModel.photoSettings" @onUpdatePhotoSettings='updatePhotoSettings'/>
      <inventory-search-settings v-if="canManageSearchSettings" :inventorySearchSettings="accountInventorySettingsUpdateModel.searchSettings" @onUpdateSearchSettings='updateSearchSettings'/>
      <inventory-task-settings :inventoryAlertSettings="accountInventorySettingsUpdateModel.inventoryAlertSettings" @onUpdateAlertSettings='updateAlertSettings'/>
    </div>
    </b-card>

  </div>
  <loader v-else size="lg"/>
</template>

<script>
import {ObjectSchema} from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import { carfaxIntegrationType, carfaxAutoPurchaseReports, vehicleHistoryReportTypes } from '@/shared/inventory/inventoryTypes'
import inventoryTaskSettings from '@/components/inventory/inventoryTasks/inventoryTaskSettings'
import inventoryPhotoSettings from '@/components/inventory/inventorySettings/inventoryPhotoSettings'
import inventorySearchSettings from '@/components/inventory/inventorySettings/inventorySearchSettings'
import loader from '../../components/_shared/loader'
import globals from '../../globals'
import moment from 'moment'
import permissions from '@/shared/common/permissions'
import {mapGetters} from 'vuex'

const defaultValuesForSettings = new ObjectSchema({
  loggedinnow: { type: Boolean, default: false }
})
let queryHelper = new QueryStringHelper(defaultValuesForSettings)

export default {
  name: 'inventory-settings',
  metaInfo: {
    title: 'Inventory Settings'
  },
  props: {
    accountId: Number
  },
  components: {
    'auto-detail-row': () => import('@/components/details/helpers/autoDetailRow'),
    'detail-row': () => import('@/components/details/helpers/detailRow'),
    'inventory-task-settings': inventoryTaskSettings,
    'inventory-photo-settings': inventoryPhotoSettings,
    'inventory-search-settings': inventorySearchSettings,
    loader
  },
  created () {
    this.getInventorySettings()
    this.getSettings()
    this.carfaxLogin = 'carfaxLogin'
    this.carfaxPassword = 'carfaxPassword'
  },
  data () {
    return {
      vehicleHistoryReportTypes,
      isLoading: true,
      filters: defaultValuesForSettings.getObject(),
      isEditInventorySettings: false,
      loggedOutNow: false,
      accountInventorySettingsUpdateModel: {},
      accountInventorySettingsModelOriginal: {
        accountId: this.accountId,
        dateTimeInserted: '',
        dateTimeUpdated: '',
        vehicleHistorySettings: {
          vehicleHistoryType: 0,
          autocheck: {
            autocheckSid: '',
            integrationType: 0,
            isAutoLink: false
          },
          carfax: {
            accessToken: '',
            accessTokenExpirationDate: '',
            autoPurchaseType: 0,
            integrationType: 0,
            isAutoLink: false,
            isAutoPurchaseEnabled: false,
            isLoggedIn: false,
            isRefreshTokenValid: false,
            refreshToken: ''
          }
        },
        inventoryAlertSettings: null,
        photoSettings: null,
        searchSettings: null
      },
      vehicleHistoryTypeOptions: Object.values(vehicleHistoryReportTypes),
      carfaxIntegrationType: carfaxIntegrationType,
      carfaxAutoPurchaseReports: carfaxAutoPurchaseReports,
      initModel: false,
      saveDisabled: true,
      isLoginLocked: true,
      isLogoutLocked: false,
      activateAutomaticLinking: false,
      status: 'not_accepted',
      carfaxLogin: '',
      carfaxPassword: '',
      authCarfaxApiUrl: '',
      clientId: '',
      refreshTokenAudience: '',
      state: ''
    }
  },
  computed: {
    isLoginDisabled () {
      return (this.accountInventorySettingsModelOriginal.vehicleHistorySettings.carfax.isLoggedIn && this.accountInventorySettingsModelOriginal.vehicleHistorySettings.carfax.isRefreshTokenValid)
    },
    loginRedirectLink () {
      return this.authCarfaxApiUrl +
        '?client_id=' + this.clientId +
        '&redirect_uri=' + this.getApiLink +
        '&state=' + this.state +
        '&response_type=code' +
        '&audience=' + this.refreshTokenAudience +
        '&scope=offline_access'
    },
    getVueLink () {
      return window.location.href + '?loggedinnow=true'
    },
    getApiLink () {
      return window.location.origin + '/api/inventory/settings/carfax/login'
    },
    getLastModifiedDateTime () {
      return this.accountInventorySettingsModelOriginal.vehicleHistorySettings.lastModifiedDateTime
        ? moment(this.accountInventorySettingsModelOriginal.vehicleHistorySettings.lastModifiedDateTime).format('MM/DD/YYYY HH:mm')
        : '-'
    },
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {hasPermissions: () => false}
    },
    canManagePhotoSettings () {
      return this.user && this.user.hasPermissions && this.user.hasPermissions(permissions.IMFullAccess)
    },
    canManageSearchSettings () {
      return this.user && this.user.hasPermissions && this.user.hasPermissions(permissions.IMFullAccess)
    }
  },
  methods: {
    getTextByValueCarfaxAutoPurchaseReportsText (key) {
      return carfaxAutoPurchaseReports.find(x => x.value === key).text
    },
    getVehicleHistoryTypeText (key) {
      return this.vehicleHistoryTypeOptions.find(x => x.value === key).text
    },
    getAutomaticLinkingDescription (key) {
      return key ? 'Enabled' : 'Disabled'
    },
    onSave () {
      this.updateInventorySettings()
      this.isEditInventorySettings = false
    },
    onCancel () {
      this.accountInventorySettingsUpdateModel = globals().getClonedValue(this.accountInventorySettingsModelOriginal)
      this.isEditInventorySettings = false
    },
    setEditInventorySettings () {
      this.isEditInventorySettings = true
    },
    updateAlertSettings (alertSettings) {
      this.accountInventorySettingsUpdateModel.inventoryAlertSettings = alertSettings
      this.updateInventorySettings()
    },
    updatePhotoSettings (photoSettings) {
      this.accountInventorySettingsUpdateModel.photoSettings = photoSettings
      return this.$store.dispatch(
        'accountManagement/updateInventoryPhotoSettings',
        {
          accountId: this.accountId,
          photoSettings: this.accountInventorySettingsUpdateModel.photoSettings
        }
      ).then(x => {
        this.$toaster.success('Settings were updated', { timeout: 8000 })
      }).catch(reason => {
        this.$toaster.error(reason, { timeout: 8000 })
        this.$logger.handleError(reason, reason)
      }).finally(() => {
        this.getInventorySettings()
      })
    },
    updateSearchSettings (searchSettings) {
      this.accountInventorySettingsUpdateModel.searchSettings = searchSettings
      return this.$store.dispatch(
        'accountManagement/updateInventorySearchSettings',
        {
          accountId: this.accountId,
          searchSettings: this.accountInventorySettingsUpdateModel.searchSettings
        }
      ).then(x => {
        this.$toaster.success('Settings were updated', { timeout: 8000 })
      }).catch(reason => {
        this.$toaster.error(reason, { timeout: 8000 })
        this.$logger.handleError(reason, reason)
      }).finally(() => {
        this.getInventorySettings()
      })
    },
    onLogout () {
      this.loggedOutNow = true
      return this.$store.dispatch('accountManagement/carfaxLogout', { accountId: this.accountId }).then(x => {
        this.getInventorySettings()
      }).catch(reason => {
        this.$toaster.error(reason, { timeout: 8000 })
      })
    },
    onLogIn () {
      return this.$store.dispatch('accountManagement/updateAccountCarfaxSettingsState', { accountId: this.accountId, redirectUrl: this.getVueLink, callbackUrl: this.getApiLink }).then(x => {
        this.state = x.data
        window.location.href = this.loginRedirectLink
      }).catch(reason => {
        this.$toaster.error(reason, { timeout: 8000 })
      })
    },
    updateInventorySettings () {
      return this.$store.dispatch('accountManagement/updateInventorySettings', { accountId: this.accountId, inventorySettings: this.accountInventorySettingsUpdateModel }).then(x => {
        this.$toaster.success('Settings were updated', { timeout: 8000 })
      }).catch(reason => {
        this.$toaster.error(reason, { timeout: 8000 })
        this.$logger.handleError(reason, reason)
      }).finally(() => {
        this.getInventorySettings()
      })
    },
    getInventorySettings () {
      this.isLoading = true
      return this.$store.dispatch('accountManagement/getInventorySettings', { accountId: this.accountId }).then(x => {
        this.accountInventorySettingsModelOriginal = x.data
        this.initModel = true
        this.saveDisabled = true
        this.filters = queryHelper.parseQueryStringToObject(this.$router)

        if (!this.accountInventorySettingsModelOriginal.inventoryAlertSettings) {
          this.accountInventorySettingsModelOriginal.inventoryAlertSettings = this.getDefaultInventoryAlertSettings()
        }

        if (this.accountInventorySettingsModelOriginal.vehicleHistorySettings.vehicleHistoryType === 1) {
          if (this.accountInventorySettingsModelOriginal.vehicleHistorySettings.carfax.isLoggedIn) {
            if (this.accountInventorySettingsModelOriginal.vehicleHistorySettings.carfax.isRefreshTokenValid) {
              if (this.filters.loggedinnow) {
                this.$toaster.success('You have logged in successfully.', { timeout: 8000 })
                this.filters.loggedinnow = false
                this.$router.push({query: {}})
              }
            } else {
              this.$toaster.success('Authorization expired. Please log in again.', { timeout: 8000 })
            }
          } else if (this.loggedOutNow) {
            this.$toaster.success('You have been logged out.', { timeout: 8000 })
            this.loggedOutNow = false
          }
        }
        this.accountInventorySettingsUpdateModel = globals().getClonedValue(this.accountInventorySettingsModelOriginal)
      }).catch(reason => {
        console.error(reason)
      }).finally(() => {
        this.isLoading = false
      })
    },
    getSettings () {
      return this.$store.dispatch('accountManagement/getCarfaxCredentialsSettings', { accountId: this.accountId }).then(x => {
        this.authCarfaxApiUrl = x.data.authApiUrl
        this.clientId = x.data.clientId
        this.refreshTokenAudience = x.data.refreshTokenAudience
        this.isLoginLocked = false
      }).catch(reason => {
        console.error(reason)
      })
    },
    getDefaultInventoryAlertSettings () {
      return {
        photoAlertSettings: {
          photoAlertSettingsNew: {
            minimumPhotosAmount: 1,
            hasToHideAlerts: false
          },
          photoAlertSettingsUsed: {
            minimumPhotosAmount: 1,
            hasToHideAlerts: false
          }
        },
        videoAlertSettings: {
          videoAlertSettingsNew: {
            minimumVideosAmount: 1,
            hasToHideAlerts: false
          },
          videoAlertSettingsUsed: {
            minimumVideosAmount: 1,
            hasToHideAlerts: false
          }
        },
        priceAlertSettings: {
          priceAlertTypeNew: 0,
          priceAlertTypeUsed: 0
        },
        descriptionAlertSettings: {
          hasToHideAlertsNew: false,
          hasToHideAlertsUsed: false
        }
      }
    }
  },
  watch: {
    accountInventorySettingsUpdateModel: {
      handler: function (newValue) {
        if (this.initModel) {
          this.initModel = false
        } else {
          this.saveDisabled = false
        }
      },
      deep: true
    }
  }
}
</script>
