<template>
  <div class="position-relative">
    <h4>eBay Errors &amp; Scheduled</h4>
    <paging
      class="custom-paging d-none d-md-block p-0"
      :pageNumber="filters.page"
      :pageSize="filters.pageSize"
      :totalItems="itemsTotalCount"
      @numberChanged="pageChanged"
      @changePageSize="changePageSize"
    />
    <b-tabs class="nav-tabs-top nav-responsive-sm" v-model="selectedTab" no-fade>
      <b-tab v-for="tab in tabs" :key="tab.key" :title="tab.title" class="p-4">
        <b-row>
          <b-col xl="5" lg="6">
            <b-input-group>
              <b-form-input v-model="filters.search" placeholder="Search by VIN, IID, Stock# or Account ID"></b-form-input>
              <b-input-group-append>
                <b-btn variant="primary" @click="applySearch">Go</b-btn>
              </b-input-group-append>
            </b-input-group>
          </b-col>
          <b-col xl="7" lg="6">
            <b-row class="mt-sm-3 mt-md-3 mt-lg-0">
              <b-col xl="4" lg="4" md="2" sm="12" class="py-2 d-flex justify-content-sm-start justify-content-lg-end">
                <label for="listing-type">Listing Type:</label>
              </b-col>
              <b-col xl="8" lg="8" md="10" sm="12">
                <b-form-select id="listing-type" @input="synchronizeUrlAndReload" v-model="filters.listingtype" :options="listingOptions"/>
              </b-col>
            </b-row>
          </b-col>
        </b-row>
      </b-tab>
    </b-tabs>
    <b-card no-body v-if="!isLoading && items && items.length > 0">
      <b-table
        :fields="tableFields"
        :items="items"
        :sort-by="tableSortBy"
        :sort-desc="tableSortDesc"
        @sort-changed="onSortChanged"
        :no-sort-reset="true"
        :no-local-sorting="true"
        striped
        responsive
      >
        <template #cell(manage)="row">
          <b-dropdown variant="outline-secondary icon-btn btn-round" size="sm" right no-caret boundary='viewport'>
            <template slot="button-content">
              <i class="ion ion-ios-more m-0"></i><span class="sr-only">Manage</span>
            </template>
            <b-dropdown-item :to="{ path: `/inventory/${row.item.AccountId}/edit/${row.item.Vin}` }">Edit Vehicle</b-dropdown-item>
            <b-dropdown-item @click="showEBayLaunchModal(row.item)">eBay Launch</b-dropdown-item>
            <b-dropdown-item @click="showRecordAuctionModal(row.item)">Record Auction</b-dropdown-item>
            <b-dropdown-item :href="getViewTemplateHref(row.item)">View Template</b-dropdown-item>
          </b-dropdown>
        </template>
        <template #cell(title)="row">
            <div class="media align-items-center">
              <img class="d-block float-left ui-w-50 mr-3" style="min-height: 1px" :src="row.item.PresentationPhoto || '#'">
              <span>{{row.item | getVehicleTitle}}</span>
            </div>
        </template>
        <template #cell(type)="row">
          {{getTypeDesc(row.item)}}
        </template>
        <template #cell(research)="row">
          <font-awesome-icon icon="exclamation-triangle" class="text-primary" /> <b-link :href="getXlmHistoryUrl(row.item)"><u>View XML History</u></b-link>
        </template>
        <template #cell(show_details)="row">
          <div class="media align-items-center">
            <b-button size="sm" @click.stop="row.toggleDetails" class="text-center">
              {{ row.detailsShowing ? 'Hide' : 'Show' }} Details
            </b-button>
          </div>
        </template>
        <template #row-details="row">
          <b-card>
            <b-row>
              <b-col>
                <detail-row :fixed-payload-width="true">
                  <span class="font-weight-bold text-dark" slot="title">Listing Title:</span>
                  <span slot="payload">{{row.item.ListingTitle}}</span>
                </detail-row>
              </b-col>
              <b-col>
                <detail-row :fixed-payload-width="true">
                  <span class="font-weight-bold text-dark" slot="title">Is this a Relist?</span>
                  <span slot="payload">{{row.item.IsRelist ? 'Yes' : 'No'}}</span>
                </detail-row>
              </b-col>
            </b-row>
            <b-row>
              <b-col>
                <detail-row :fixed-payload-width="true">
                  <span class="font-weight-bold text-dark" slot="title">Parent Category:</span>
                  <span slot="payload">{{row.item.EBayCategoryParentName}}</span>
                </detail-row>
              </b-col>
              <b-col>
                <detail-row :fixed-payload-width="true">
                  <span class="font-weight-bold text-dark" slot="title">Private Listing?</span>
                  <span slot="payload">{{row.item.IsPrivate ? "Yes" : "No"}}</span>
                </detail-row>
              </b-col>
            </b-row>
            <b-row>
              <b-col>
                <detail-row :fixed-payload-width="true">
                  <span class="font-weight-bold text-dark" slot="title">eBay Category:</span>
                  <span slot="payload">{{row.item.EBayCategoryName}}</span>
                </detail-row>
              </b-col>
              <b-col>
                <detail-row :fixed-payload-width="true">
                  <span class="font-weight-bold text-dark" slot="title">VIN:</span>
                  <span slot="payload">{{row.item.Vin}}</span>
                </detail-row>
              </b-col>
            </b-row>
            <b-row>
              <b-col>
                <detail-row :fixed-payload-width="true">
                  <span class="font-weight-bold text-dark" slot="title">Upgrades:</span>
                  <span slot="payload">{{getUpgradesTitle(row.item)}}</span>
                </detail-row>
              </b-col>
              <b-col>
                <detail-row :fixed-payload-width="true">
                  <span class="font-weight-bold text-dark" slot="title">Stock#:</span>
                  <span slot="payload">{{row.item.StockNumber}}</span>
                </detail-row>
              </b-col>
            </b-row>
            <b-row>
              <b-col>
                <detail-row :fixed-payload-width="true">
                  <span class="font-weight-bold text-dark" slot="title">Duration:</span>
                  <span slot="payload">{{row.item.AuctionDuration > 30 ? "Good Till Canceled" : row.item.AuctionDuration + " Days"}}</span>
                </detail-row>
              </b-col>
            </b-row>
            <b-row>
              <b-col>
                <detail-row :fixed-payload-width="true">
                  <span class="font-weight-bold text-dark" slot="title">Start Price:</span>
                  <span slot="payload">{{getPriceText(row.item.StartPrice)}}</span>
                </detail-row>
              </b-col>
            </b-row>
            <b-row>
              <b-col>
                <detail-row :fixed-payload-width="true">
                  <span class="font-weight-bold text-dark" slot="title">Fixed Price:</span>
                  <span slot="payload">{{getPriceText(row.item.BinPrice)}}</span>
                </detail-row>
              </b-col>
            </b-row>
            <b-row>
              <b-col>
                <detail-row :fixed-payload-width="true">
                  <span class="font-weight-bold text-dark" slot="title">Reserve Amount:</span>
                  <span slot="payload">{{getPriceText(row.item.ReservePrice)}}</span>
                </detail-row>
              </b-col>
            </b-row>
          </b-card>
        </template>
      </b-table>
    </b-card>
    <div v-else-if="!isLoading">
      <span class="text-muted">Not Found</span>
    </div>
    <div v-else class="mt-3 pt-3">
      <loader size="lg"/>
    </div>
    <b-modal
      v-if="isEBayLaunchModalVisible"
      name="ebay-launch"
      title="eBay launch"
      size="lg"
      :visible="isEBayLaunchModalVisible"
      @hide="hideEBayLaunchModal"
    >
      <vehicleDescription class="mb-2" :vehicle="item"/>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Account:</span>
        <span slot="payload">{{item.AccountId}}</span>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">View Template:</span>
        <b-link :href="getViewTemplateHref(item)" slot="payload"><u>eBizAutos Gallery Template</u></b-link>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Error Research:</span>
        <span slot="payload"><font-awesome-icon icon="exclamation-triangle" class="text-primary" /> <b-link :href="getXlmHistoryUrl(item)"><u>View XML History</u></b-link></span>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">eBay Launch:</span>
        <b-form-checkbox slot="payload" v-model="ebayLaunchModel.IsForceLaunch">Force UUID Override</b-form-checkbox>
      </detail-row>
      <template #modal-footer>
        <b-btn variant="primary" @click="onAddItem">Add Item</b-btn>
      </template>
    </b-modal>
    <b-modal
      v-if="isRecordAuctionModalVisible"
      name="record-auction"
      title="Record Auction"
      size="lg"
      :visible="isRecordAuctionModalVisible"
      @hide="hideRecordAuctionModal"
    >
      <ValidationObserver ref="validator">
      <vehicleDescription class="mb-2" :vehicle="item"/>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Account:</span>
        <span slot="payload">{{item.AccountId}}</span>
      </detail-row>
      <ValidationProvider name="Listing Id" rules="numeric|required" v-slot="{errors}">
      <detail-row title-position="start" :fixed-payload-width="true" :error="errors[0]">
        <span slot="title">Listing ID:</span>
        <b-form-input name="Listing_ID" slot="payload" v-model="recordAuctionModel.AuctionId"></b-form-input>
      </detail-row>
      </ValidationProvider>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Auction End Year:</span>
        <b-form-select slot="payload" v-model="recordAuctionModel.EndYear" :options="getYearOptions"></b-form-select>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">End Month:</span>
        <b-form-select slot="payload" v-model="recordAuctionModel.EndMonth" :options="getMonthOptions"></b-form-select>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">End Day:</span>
        <b-form-select slot="payload" v-model="recordAuctionModel.EndDay" :options="getDayOptions"></b-form-select>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">End Time:</span>
        <b-form-input type="time" :value="recordAuctionModel.EndTime" v-model="recordAuctionModel.EndTime" slot="payload"></b-form-input>
      </detail-row>
      </ValidationObserver>
      <template #modal-footer>
        <b-btn variant="primary" @click="onRecordAuction">Record Auction</b-btn>
      </template>
    </b-modal>
  </div>
</template>

<script>
import constants from '@/shared/ebay/constants'
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import loader from '@/components/_shared/loader'
import paging from '@/components/_shared/paging'
import detailRow from '@/components/details/helpers/detailRow'
import vehicleDescription from '@/components/eBay/helpers/vehicleDescription'
import moment from 'moment'
import numeral from 'numeral'

const defaultValues = new ObjectSchema({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  search: { type: String, default: '' },
  listingtype: { type: Number, default: 1 },
  sort: { type: Number, default: 1 },
  tab: { type: Number, default: 0 }
})

const queryHelper = new QueryStringHelper(defaultValues)

export default {
  name: 'ebay-errors-and-scheduled',
  metaInfo: {
    title: 'eBay Errors & Scheduled'
  },
  data () {
    return {
      isLoading: true,
      filters: defaultValues.getObject(),
      tabs: Object.values(constants.errorsAndScheduledOptions),
      listingOptions: constants.listingTypes,
      itemsTotalCount: 0,
      items: [],
      item: {},
      sitesInfo: [],
      ebayLaunchModel: {
        IsForceLaunch: false
      },
      recordAuctionModel: {},
      isEBayLaunchModalVisible: false,
      isRecordAuctionModalVisible: false
    }
  },
  created () {
    this.filters = queryHelper.parseQueryStringToObject(this.$router)
    this.populateData()
  },
  computed: {
    selectedTab: {
      get () {
        let res = this.tabs.find(x => x.key === this.filters.tab)
        if (res) {
          return this.filters.tab
        }

        return 0
      },
      set (value) {
        this.filters.tab = value
        this.isLoading = true
        this.synchronizeUrlAndReload()
      }
    },
    tableFields () {
      return [
        {
          key: 'title',
          label: 'Vehicle',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'show_details',
          label: '',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'AuctionStartDateTime',
          label: 'Start Time',
          tdClass: 'py-2 align-middle',
          sortTypeAsc: 1,
          sortTypeDesc: 2,
          formatter: value => {
            if (value) {
              return moment(value).format('MM/DD/YYYY hh:mm A')
            }
            return '-'
          },
          sortable: true
        },
        {
          key: 'type',
          label: 'Type',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'research',
          label: 'Research',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    tableSortBy () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sortType
      } else {
        return false
      }
    },
    getYearOptions () {
      let yearNow = moment().year()
      return [yearNow, yearNow + 1]
    },
    getMonthOptions () {
      let options = []
      Array(12).fill().map((_, i) => {
        options.push({
          value: i,
          text: moment().month(i).format('MMM')
        })
      })

      return options
    },
    getDayOptions () {
      let options = []
      let daysCount = moment().month(this.recordAuctionModel.EndMonth).daysInMonth()
      for (let day = 1; day <= daysCount; day++) {
        options.push(day)
      }
      return options
    }
  },
  filters: {
    getVehicleTitle: function (item) {
      if (!item) return ''
      let title = ''
      if (item.Year > 0) {
        title = item.Year.toString()
      }

      title = [title, item.Make, item.Model, item.Trim].filter(v => v && v !== '').join(' ')

      return title.trim()
    }
  },
  components: {
    paging,
    loader,
    detailRow,
    vehicleDescription
  },
  methods: {
    applySearch () {
      this.filters.page = 1
      this.isLoading = true
      this.synchronizeUrlAndReload()
    },
    onSortChanged (value) {
      const sortingColumn = this.tableFields.find(x => x.key === value.sortBy)

      this.filters.sort = sortingColumn
        ? value.sortDesc
          ? sortingColumn.sortTypeDesc
          : sortingColumn.sortTypeAsc
        : 0
      this.synchronizeUrlAndReload()
    },
    pageChanged (newPage) {
      this.filters.page = newPage
      this.isLoading = true
      this.synchronizeUrlAndReload()
    },
    changePageSize (newPageSize) {
      this.filters.pageSize = newPageSize
      this.filters.page = 1
      this.isLoading = true
      this.synchronizeUrlAndReload()
    },
    synchronizeUrlAndReload () {
      queryHelper.rebuildParamsInQueryString(this.$router, this.filters)
      this.populateData()
    },
    async populateData () {
      try {
        let errorAndScheduledItemsData = await this.$store.dispatch('eBay/getErrorsAndScheduledItems', this.filters)
        this.itemsTotalCount = errorAndScheduledItemsData.data.TotalItems
        this.items = errorAndScheduledItemsData.data.Items
        let siteIds = errorAndScheduledItemsData.data.Items.map(x => { return x.AccountId })
        if (siteIds && siteIds.length > 0) {
          let sitesInfoData = await this.$store.dispatch('website/getSitesBasicInfo', { data: { siteIds: siteIds } })
          this.sitesInfo = sitesInfoData.data.sites
        }
      } catch (ex) {
        this.$toaster.error('Something went wrong!')
        this.$logger.handleError(ex, 'Cannot populate errors and scheduled items')
      } finally {
        this.isLoading = false
      }
    },
    onAddItem () {
      this.ebayLaunchModel.AccountId = this.item.AccountId
      this.ebayLaunchModel.Vin = this.item.Vin
      this.$store.dispatch('eBay/ebayLaunch', this.ebayLaunchModel).then(res => {
        this.$toaster.success('Moved to Process Successfully')
      }).catch(ex => {
        this.$toaster.error('Something went wrong!')
        this.$logger.handleError(ex, 'Cannot force launch eBat item', this.ebayLaunchModel)
      }).finally(() => {
        this.item = {}
        this.isEBayLaunchModalVisible = false
      })
    },
    onRecordAuction () {
      this.$refs.validator.validate().then(res => {
        if (res) {
          this.recordAuctionModel.EndMonth = this.recordAuctionModel.EndMonth + 1
          this.recordAuctionModel.AccountId = this.item.AccountId
          this.recordAuctionModel.Vin = this.item.Vin
          this.$store.dispatch('eBay/recordAuction', this.recordAuctionModel).then(res => {
            this.$toaster.success('Recorded Auction Successfully')
          }).catch(ex => {
            this.$toaster.error('Something went wrong!')
            this.$logger.handleError(ex, 'Cannot record auction', this.recordAuctionModel)
          }).finally(() => {
            this.item = {}
            this.isRecordAuctionModalVisible = false
          })
        }
      })
    },
    getUpgradesTitle (item) {
      let titles = []
      if (item.IsBold) {
        titles.push('Bold Face Title')
      }
      if (item.IsSubTitle) {
        titles.push('Sub Title')
      }
      return titles.join(', ')
    },
    getPriceText (price) {
      if (price && price > 0) {
        return numeral(price).format('$0,0')
      }
      return 'N/A'
    },
    getTypeDesc (item) {
      if (item.IsLocalAuction) {
        return 'eBay Local'
      }
      if (item.IsPriceFixed) {
        return 'Fixed Price Listing'
      }

      return `Auction (${item.AuctionDuration}d)`
    },
    getXlmHistoryUrl (item) {
      return `https://admin.ebizautos.com/api_component/find_auction.cfm?iid=${item.Iid}`
    },
    getViewTemplateHref (item) {
      let res = this.sitesInfo[item.AccountId]
      if (res) {
        return `${res.host}/auction.aspx?vin=${item.Vin}`
      }
      return ''
    },
    showEBayLaunchModal (item) {
      this.item = item
      this.isEBayLaunchModalVisible = true
    },
    hideEBayLaunchModal () {
      this.item = {}
      this.isEBayLaunchModalVisible = false
    },
    showRecordAuctionModal (item) {
      this.item = item
      this.recordAuctionModel.EndYear = moment().year()
      this.recordAuctionModel.EndMonth = moment().month()
      this.recordAuctionModel.EndDay = moment().date()
      this.recordAuctionModel.EndTime = moment().format('HH:mm')
      this.isRecordAuctionModalVisible = true
    },
    hideRecordAuctionModal () {
      this.isRecordAuctionModalVisible = false
    }
  }
}
</script>

<style scoped>
.custom-paging {
  position: absolute;
  right: 5px;
  top: 35px;
  z-index: 2;
}
</style>
