<template>
  <div>
    <inventory-task-photo-listing
    v-if='alertType === vehicleTasks.photo.value'
    :items='inventoryData.vehicles'
    @sortChanged='onSortChange'
    :sortField="sortBy"
    :sortOrder="sortDesc"
    />

    <inventory-task-video-listing
    v-else-if='alertType === vehicleTasks.video.value'
    :items='inventoryData.vehicles'
    @sortChanged='onSortChange'
    :sortField="sortBy"
    :sortOrder="sortDesc"
    />

    <inventory-task-price-listing
    v-else-if='alertType === vehicleTasks.price.value'
    :items='inventoryData.vehicles'
    @sortChanged='onSortChange'
    :sortField="sortBy"
    :sortOrder="sortDesc"
    />

    <inventory-task-description-listing
    v-else-if='alertType === vehicleTasks.description.value'
    :items='inventoryData.vehicles'
    @sortChanged='onSortChange'
    :sortField="sortBy"
    :sortOrder="sortDesc"
    />

    <inventory-task-all-listing
    v-else-if='alertType === vehicleTasks.all.value'
    :items='inventoryData.vehicles'
    @sortChanged='onSortChange'
    :sortField="sortBy"
    :sortOrder="sortDesc"
    />
  </div>
</template>

<script>
import globals from '@/globals'
import vehicleTasks from '@/shared/common/vehicle/vehicleTasks'
import inventoryTaskPhotoListing from './inventoryTaskPhotoListing'
import inventoryTaskVideoListing from './inventoryTaskVideoListing'
import inventoryTaskPriceListing from './inventoryTaskPriceListing'
import inventoryTaskDescriptionListing from './inventoryTaskDescriptionListing'
import inventoryTaskAllListing from '@/components/inventory/inventoryTasks/inventoryTaskAllListing'
import { alertSortTypes } from '@/shared/inventory/inventoryTypes'

export default {
  name: 'inventory-tasks-listing',
  props: {
    inventoryData: { type: Object, required: true },
    alertType: { type: Number, required: true }
  },
  components: {
    'inventory-task-photo-listing': inventoryTaskPhotoListing,
    'inventory-task-video-listing': inventoryTaskVideoListing,
    'inventory-task-price-listing': inventoryTaskPriceListing,
    'inventory-task-description-listing': inventoryTaskDescriptionListing,
    'inventory-task-all-listing': inventoryTaskAllListing
  },
  data () {
    return {
      vehicleTasks,
      currentTileSortType: 0,
      sortMappings: [
        { sortType: alertSortTypes.stockAsc, sortBy: 'stockNumber', sortDesc: false },
        { sortType: alertSortTypes.stockDesc, sortBy: 'stockNumber', sortDesc: true },

        { sortType: alertSortTypes.inStockAsc, sortBy: 'age', sortDesc: true },
        { sortType: alertSortTypes.inStockDesc, sortBy: 'age', sortDesc: false },

        { sortType: alertSortTypes.photosCountAsc, sortBy: 'actualPhotosCount', sortDesc: false },
        { sortType: alertSortTypes.photosCountDesc, sortBy: 'actualPhotosCount', sortDesc: true },

        { sortType: alertSortTypes.lowPriceAsc, sortBy: 'lowPrice', sortDesc: false },
        { sortType: alertSortTypes.lowPriceDesc, sortBy: 'lowPrice', sortDesc: true },

        { sortType: alertSortTypes.highPriceAsc, sortBy: 'highPrice', sortDesc: false },
        { sortType: alertSortTypes.highPriceDesc, sortBy: 'highPrice', sortDesc: true },

        { sortType: alertSortTypes.yearDescMakeAsc, sortBy: 'title', sortDesc: true },
        { sortType: alertSortTypes.yearAscMakeAsc, sortBy: 'title', sortDesc: false },

        { sortType: alertSortTypes.videosCountAsc, sortBy: 'videoItemsCount', sortDesc: true },
        { sortType: alertSortTypes.videosCountDesc, sortBy: 'videoItemsCount', sortDesc: false },

        { sortType: alertSortTypes.totalAlertsAsc, sortBy: 'totalAlerts', sortDesc: true },
        { sortType: alertSortTypes.totalAlertsDesc, sortBy: 'totalAlerts', sortDesc: false }
      ],
      selectedFilters: globals().getClonedValue(this.inventoryData.filters)
    }
  },
  methods: {
    onSortChange (arg) {
      if (arg.sortBy === 'title') {
        switch (this.currentTileSortType) {
          case alertSortTypes.yearDescMakeAsc:
            this.currentTileSortType = this.sortMappings.find(x => x.sortType === alertSortTypes.yearAscMakeAsc).sortType
            break
          case alertSortTypes.yearAscMakeAsc:
            this.currentTileSortType = this.sortMappings.find(x => x.sortType === alertSortTypes.yearDescMakeAsc).sortType
            break
          default:
            this.currentTileSortType = this.sortMappings.find(x => x.sortType === alertSortTypes.yearDescMakeAsc).sortType
            break
        }
      } else {
        this.currentTileSortType = this.getSortType(arg.sortBy, arg.sortDesc)
      }

      this.$emit('sortChanged', this.currentTileSortType)
    },
    getSortType (orderBy, sortDesc) {
      if (orderBy === 'title') {
        return this.currentTileSortType
      } else {
        this.currentTileSortType = undefined
        const sortMapping = this.sortMappings.find(v => v.sortBy === orderBy && v.sortDesc === sortDesc)

        if (sortMapping !== undefined) {
          return sortMapping.sortType
        } else {
          return this.sortMappings.find(x => x.sortType === alertSortTypes.yearDescMakeAsc).sortType
        }
      }
    }
  },
  computed: {
    sortBy: {
      get () {
        const sortMapping = this.sortMappings.find(v => v.sortType === this.selectedFilters.sortType)
        return (sortMapping !== undefined) ? sortMapping.sortBy : 'title'
      },
      set: function (value) {
      }
    },
    sortDesc: {
      get () {
        const sortMapping = this.sortMappings.find(v => v.sortType === this.selectedFilters.sortType)
        return (sortMapping !== undefined) ? sortMapping.sortDesc : true
      },
      set (value) {
      }
    }
  }
}
</script>
