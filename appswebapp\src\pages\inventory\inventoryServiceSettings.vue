<template>
<div>
  <h4>
    Inventory Tuneup
  </h4>
  <b-tabs v-if="!isLoading" class="nav-tabs-top nav-responsive-sm" no-fade>
    <b-tab title="History Settings" class="p-3" active>
      <editSettingsHelper @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="Autocheck Settings" :isLoading="isRequesting" :isDisabled="isDisabled" :isViewMode="isViewMode">
        <div slot="settings-content">
          <detail-row title-position="start" :fixed-payload-width="true" :bootstrap-mode="true">
            <span slot="title">Phrases That Indicate Validity For Apps:</span>
            <multi-input
              slot="payload"
              v-model="settingsToUpdate.vehicleHistoryReportSettings.autocheckReportSettings.phrasesThatIndicateValidityForApps"
              :values="settingsToUpdate.vehicleHistoryReportSettings.autocheckReportSettings.phrasesThatIndicateValidityForApps"
              :disabled="isViewMode"
            ></multi-input>
          </detail-row>
          <detail-row title-position="start" :fixed-payload-width="true" :bootstrap-mode="true">
            <span slot="title">Phrases That Indicate Validity For Gallery:</span>
            <multi-input
              slot="payload"
              v-model="settingsToUpdate.vehicleHistoryReportSettings.autocheckReportSettings.phrasesThatIndicateValidityForGallery"
              :values="settingsToUpdate.vehicleHistoryReportSettings.autocheckReportSettings.phrasesThatIndicateValidityForGallery"
              :disabled="isViewMode"
            ></multi-input>
          </detail-row>
        </div>
      </editSettingsHelper>
    </b-tab>
  </b-tabs>
  <loader v-else size="lg"/>
</div>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import multiInput from '@/components/_shared/multiInput'
import InventoryService from '@/services/inventory/InventoryService'
import globals from '../../globals'
import loader from '../../components/_shared/loader.vue'

export default {
  name: 'inventory-tuneup',
  metaInfo: {
    title: 'Inventory Tuneup'
  },
  data () {
    return {
      originalSettings: null,
      settingsToUpdate: null,
      isLoading: true,
      isDisabled: false,
      isViewMode: true,
      isRequesting: false
    }
  },
  components: {
    'detail-row': detailRow,
    multiInput,
    loader,
    editSettingsHelper: () => import('@/components/_shared/editSettingsHelper')
  },
  created () {
    this.populateData()
  },
  methods: {
    populateData () {
      InventoryService.getInventoryServiceSettings().then(res => {
        this.originalSettings = res.data || {}
        if (!this.originalSettings.vehicleHistoryReportSettings) {
          this.originalSettings.vehicleHistoryReportSettings = {}
        }
        if (!this.originalSettings.vehicleHistoryReportSettings.autocheckReportSettings) {
          this.originalSettings.vehicleHistoryReportSettings.autocheckReportSettings = {
            phrasesThatIndicateValidityForApps: [],
            phrasesThatIndicateValidityForGallery: []
          }
        }
        this.settingsToUpdate = globals().getClonedValue(this.originalSettings)
      }).catch(ex => {
        this.$toaster.error('Something went wrong!!!')
        this.logger.handleError(ex, 'Failed on getting inventory services settings')
      }).finally(() => {
        this.isLoading = false
      })
    },
    changeMode (isViewMode) {
      this.isViewMode = isViewMode
    },
    cancel () {
      this.settingsToUpdate = globals().getClonedValue(this.originalSettings)
      this.changeMode(true)
    },
    saveSettings () {
      this.changeMode(true)
      this.isRequesting = true
      InventoryService.updateInventoryServiceSettings(this.settingsToUpdate).then(res => {
        this.$toaster.success('Service Settings Updated Successfully')
      }).catch(ex => {
        this.$toaster.error('Something went wrong!!!')
        this.logger.handleError(ex, 'Failed on updating inventory services settings')
      }).finally(() => {
        this.isRequesting = false
        this.isLoading = true
        this.populateData()
      })
    }
  }
}
</script>
