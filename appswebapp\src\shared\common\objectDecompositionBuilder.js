class ObjectDecompositionBuilder {
  getObjectPropertiesTree (obj) {
    if (!obj) {
      return []
    }
    let nodes = []
    let keys = Object.keys(obj)
    for (let key of keys) {
      let node = {
        name: key
      }

      if (Array.isArray(obj[key])) {
        node.values = obj[key].map(x => {
          if (this.isPrimitiveType(typeof x)) {
            return {
              value: x
            }
          } else {
            return {
              nodes: this.getObjectPropertiesTree(x)
            }
          }
        })
      } else if (typeof obj[key] === 'object') {
        node.nodes = this.getObjectPropertiesTree(obj[key])
      } else {
        node.value = obj[key]
      }

      nodes.push(node)
    }

    return nodes
  };
  getObjectPropertiesTreeFromJsonString (jsonString) {
    let obj = {}
    try {
      obj = JSON.parse(jsonString)
      return this.getObjectPropertiesTree(obj)
    } catch (ex) {
      console.error(ex)
      return []
    }
  };
  isPrimitiveType (type) {
    return ['number', 'boolean', 'string', 'bigint', 'null', 'undefined'].includes(type)
  }
}

export default new ObjectDecompositionBuilder()
