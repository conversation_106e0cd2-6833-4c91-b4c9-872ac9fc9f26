<template>
  <div class="mt-3">
    <h4>
      Apps Vehicle Synchronizer Processors
      <b-btn @click="refreshVehicleSynchronizerProcessorItems" class="float-right" variant="primary btn-round" size="sm">
        <span class="ion ion-ios-sync"></span><span class="d-none d-sm-inline">&nbsp; Refresh</span>
      </b-btn>
    </h4>
    <b-table
      v-if="!isVehicleSynchronizerProcessorItemsLoading && vehicleSynchronizerProcessorItems && vehicleSynchronizerProcessorItems.length > 0"
      :items="vehicleSynchronizerProcessorItems"
      :fields="getVehicleSyncProcessorTableFields"
      striped
      bordered
      responsive
    >
      <template #cell(processorsCount)="data">
        <span v-if="data.item.processorsCount > 0" class="text-success">{{data.item.processorsCount}}</span>
        <span v-else class="text-danger">Unavailable</span>
      </template>
    </b-table>
    <loader v-else-if="isVehicleSynchronizerProcessorItemsLoading" size="lg" class="my-5"/>
    <span v-else class="text-muted">Not Found</span>
    <h4 class="mt-3">
      Apps Vehicle Accounts Synchronization
      <b-btn @click="refreshVehicleAccountsSyncItems" class="float-right" variant="primary btn-round" size="sm">
        <span class="ion ion-ios-sync"></span><span class="d-none d-sm-inline">&nbsp; Refresh</span>
      </b-btn>
    </h4>
    <b-row>
      <b-col cols="6">
        <b-input-group>
          <b-form-input v-model="vehicleAccountsFilter.accountId" placeholder="Account Id"></b-form-input>
          <b-input-group-append>
            <b-btn variant="primary" @click="refreshVehicleAccountsSyncItems">Search</b-btn>
          </b-input-group-append>
        </b-input-group>
      </b-col>
    </b-row>
    <b-table
      class="mt-3"
      v-if="!isVehicleAccountsSyncItemsLoading && vehicleAccountsSyncItems && vehicleAccountsSyncItems.length > 0"
      :items="vehicleAccountsSyncItems"
      :fields="getVehicleAccountsSyncTableFields"
      striped
      bordered
      responsive
    >
      <template #cell(result)="data">
        <span v-if="data.item.result === 'Success'" class="text-success">{{data.item.result}}</span>
        <span class="text-danger" v-else>{{data.item.result}}</span>
      </template>
    </b-table>
    <loader v-else-if="isVehicleAccountsSyncItemsLoading" size="lg" class="my-5"/>
    <span v-else class="text-muted">Not Found</span>
    <paging
      v-if="!isVehicleAccountsSyncItemsLoading && vehicleAccountsSyncItems && vehicleAccountsSyncItems.length > 0"
      class="p-0"
      :pageNumber="vehicleAccountsFilter.page"
      :pageSize="vehicleAccountsFilter.pageSize"
      :totalItems="vehicleAccountsItemsTotalCount"
      titled
      pageSizeSelector
      @numberChanged="pageChanged"
      @changePageSize="changePageSize"
    />
  </div>
</template>

<script>
import loader from '@/components/_shared/loader'
import paging from '@/components/_shared/paging'
import SynchronizationMonitorService from '@/services/siteBoxManager/SynchronizationMonitorService'
import moment from 'moment'

export default {
  data () {
    return {
      vehicleAccountsItemsTotalCount: 0,
      vehicleAccountsFilter: {
        accountId: null,
        page: 1,
        pageSize: 25
      },
      isVehicleAccountsSyncItemsLoading: true,
      isVehicleSynchronizerProcessorItemsLoading: true,
      vehicleSynchronizerProcessorItems: [],
      vehicleAccountsSyncItems: []
    }
  },
  components: {
    loader,
    paging
  },
  computed: {
    getVehicleSyncProcessorTableFields () {
      return [
        {
          key: 'server',
          label: 'Server',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'port',
          label: 'Port',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'lastActivityDateTime',
          label: 'Last Activity Date',
          tdClass: 'py-2 align-middle',
          formatter: value => moment(value).format('MM/DD/YYYY hh:mm A')
        },
        {
          key: 'processorsCount',
          label: 'Processors Count',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    getVehicleAccountsSyncTableFields () {
      return [
        {
          key: 'accountId',
          label: 'Account Id',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'startProcessingDateTime',
          label: 'Start Processing',
          tdClass: 'py-2 align-middle',
          formatter: value => moment(value).format('MM/DD/YYYY hh:mm A')
        },
        {
          key: 'endProcessingDateTime',
          label: 'End Processing',
          tdClass: 'py-2 align-middle',
          formatter: value => moment(value).format('MM/DD/YYYY hh:mm A')
        },
        {
          key: 'result',
          label: 'Result',
          tdClass: 'py-2 align-middle'
        }
      ]
    }
  },
  mounted () {
    this.populateVehicleSynchronizerProcessorItems()
    this.populateVehicleAccountsSyncItems()
  },
  methods: {
    pageChanged (newPage) {
      this.vehicleAccountsFilter.page = newPage
      this.isVehicleAccountsSyncItemsLoading = true
      this.populateVehicleAccountsSyncItems()
    },
    changePageSize (newSize) {
      this.vehicleAccountsFilter.pageSize = newSize
      this.vehicleAccountsFilter.page = 1
      this.isVehicleAccountsSyncItemsLoading = true
      this.populateVehicleAccountsSyncItems()
    },
    refreshVehicleSynchronizerProcessorItems () {
      this.isVehicleSynchronizerProcessorItemsLoading = true
      this.populateVehicleSynchronizerProcessorItems()
    },
    refreshVehicleAccountsSyncItems () {
      this.vehicleAccountsFilter.page = 1
      this.isVehicleAccountsSyncItemsLoading = true
      this.populateVehicleAccountsSyncItems()
    },
    populateVehicleSynchronizerProcessorItems () {
      SynchronizationMonitorService.getAppsSynchronizerProcessorsInfo().then(res => {
        this.vehicleSynchronizerProcessorItems = res.data
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
      }).finally(() => {
        this.isVehicleSynchronizerProcessorItemsLoading = false
      })
    },
    populateVehicleAccountsSyncItems () {
      SynchronizationMonitorService.getVehicleAccountSynchronizationListing(this.vehicleAccountsFilter).then(res => {
        this.vehicleAccountsItemsTotalCount = res.data.totalCount
        this.vehicleAccountsSyncItems = res.data.items
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
      }).finally(() => {
        this.isVehicleAccountsSyncItemsLoading = false
      })
    }
  }
}
</script>
