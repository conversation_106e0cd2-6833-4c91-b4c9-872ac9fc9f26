<template>
  <div v-if="isReadyToShow">
    <h2>{{this.group.groupName}}</h2>
    <h4>{{accountId}} {{getAccountNameByAccountId(accountId)}}</h4>
    <b-card v-for="user in users" :key="user.userId" class="mb-3">
      <div class="d-flex justify-content-between">
        <h5 class="text-muted">{{user.firstName}} {{user.lastName}} ({{user.userName}}) {{getUserPermissionRulesCountDesc(user)}}</h5>
        <account-permission-selector @update="populateRuleDetails" :group-id="groupId" :user-id="user.userId"/>
      </div>
      <b-row>
        <b-col xl="6">
          <b-form @submit.prevent="searchChanged(user.userId)" class="mb-2">
            <div class="form-row">
              <b-col cols="12">
                <b-input-group class="search-group">
                  <b-form-input ref="search" v-model="(user.filters || {}).search" placeholder="Search..." class="d-inline-block float-sm-right"></b-form-input>
                  <b-input-group-append>
                    <b-btn type="submit">Go</b-btn>
                  </b-input-group-append>
                </b-input-group>
              </b-col>
            </div>
          </b-form>
        </b-col>
      </b-row>
      <hr class="d-sm-none xs-header-line">
      <b-row class="rule-table-head d-none d-sm-flex">
        <b-col sm="2" xl="2">Account Id</b-col>
        <b-col sm="7" xl="3">Account Name</b-col>
        <b-col sm="3" xl="2">Details</b-col>
      </b-row>
      <b-row v-for="rule in getUserPermissionRules(user.userId)" :key="rule.targetAccountId" class="permission-rule-row">
        <b-col cols="6" class="d-sm-none left-head">Account Id</b-col>
        <b-col cols="6" sm="2" xl="2">{{rule.targetAccountId}}</b-col>

        <b-col cols="6" class="d-sm-none left-head">Account Name</b-col>
        <b-col cols="6" sm="7" xl="3">{{getAccountNameByAccountId(rule.targetAccountId)}}</b-col>

        <b-col cols="6" class="d-sm-none left-head">Details</b-col>
          <b-col cols="6" sm="3" xl="2">
          <button class="btn btn-outline-primary btn-sm"
            @click="manageDisplayRuleDetails(rule)">
            {{getRuleDetailsButtonText(rule)}}
          </button>
        </b-col>

        <b-col cols="12" xl="4" class="permission-details-col mt-2 mt-xl-0">
          <b-collapse id="permission-details-collapse" :visible="rule.showRuleDetails">
            <b-card>
              <b-row>
                <b-col md="6" lg="8">
                  <div v-for="application in applications" :key="application.Id">
                    <b-row class="mb-1">
                      <b-col cols="6" >
                        <span>{{application.Label}}</span>
                      </b-col>
                      <b-col cols="6" class="app-permission-select-box">
                        <b-form-select v-model="rule.appPermissionDetails[application.Id].permissionType" @change="changeRulePermissionDetails(rule)" size="sm" class="ml-1 app-permissions-select">
                          <option
                          v-for="permissionType in permissionTypes"
                          :key="permissionType.Id"
                          :value="permissionType.Id">
                              {{permissionType.Label}}
                          </option>
                        </b-form-select>
                      </b-col>
                      <b-col v-if="application.Id === appLeadsId && rule.appPermissionDetails[application.Id].permissionType !== accountGroupPermissionTypes.Default.Id"
                        cols="12"
                        class="mb-2 mt-1"
                      >
                        <b-form-checkbox
                          v-model="rule.appPermissionDetails[application.Id].hasToIncludeLeadsToLeadsManager"
                          @change="changeRulePermissionDetails(rule)"
                          >
                          Display {{rule.targetAccountId}} Leads in {{accountId}} Lead Manager
                        </b-form-checkbox>
                      </b-col>
                    </b-row>
                  </div>
                </b-col>
                <b-col md="6" lg="4" class="save-permission-changes-col">
                  <div>
                    <button v-show="rule.isPermissionDetailsChanged" class="btn btn-outline-primary btn-sm mt-2 mt-sm-0" @click="saveEditPermissionRule(rule)">
                      Save Changes
                    </button>
                  </div>
                </b-col>
              </b-row>
            </b-card>
          </b-collapse>
        </b-col>
        <b-btn class="btn btn-sm btn-round btn-outline-primary delete-rule-button"
          @click.prevent="tryToDeleteRule(rule.ruleId, user.userId)">
          ×
        </b-btn>
      </b-row>
      <paging v-if="hasToShowPaging(user.userId)"
        :pageNumber="(user.filters || {}).pageNumber"
        :pageSize="(user.filters || {}).pageSize"
        :totalItems="(user.filters || {}).itemsCount"
        titled
        pageSizeSelector
        @numberChanged="pageChanged($event, user.userId)"
        @changePageSize="changePageSize($event, user.userId)">
      </paging>
      <button class="btn btn-sm btn-primary btn-round mt-3 float-right"
        :disabled="!hasAccountsWithoutRules(user.userId)"
        @click="onAddNewRules(user.userId)">Add New Rules
      </button>
      <button class="btn btn-sm btn-primary btn-round mt-3 mr-1 float-right"
        :disabled="getUserPermissionRules(user.userId).length === 0"
        @click="onModifyRules(user.userId)">Edit All Rules
      </button>
    </b-card>
    <b-modal id="delete-rule-confirmation-modal" v-model="deleteRule.showRuleDeleteConfirmation" title="Delete Rule" lazy @ok="deletePermissionRule()">
      <p>
        Are you sure you want to delete this rule?
      </p>
    </b-modal>
    <b-modal
      :title="editRulesTitle"
      v-model="manageRule.show"
      lazy>
      <b-row v-show="!manageRule.isEditAllMode">
        <b-col cols="6"><h6>Select Accounts</h6></b-col>
        <b-col cols="6" class="add-rule-select-all-col"><span @click="manageSelectAllAccounts"> {{manageRule.selectAll ? "Unselect All" : "Select All"}}</span></b-col>
        <b-col cols="12" class="mb-3">
          <multiselect
            v-model="manageRule.selectedAccounts"
            :options="freeUserAccountsAddRule"
            :multiple="true"
            :close-on-select="false"
            :clear-on-select="false"
            :preserve-search="true"
            placeholder="Select accounts"
            :custom-label="accountSelectOption"
            :show-labels="false"
            track-by="accountId">
            <!-- needed to fource select to not use tags -->
            <template slot="tag">{{''}}</template>
            <template slot="selection" slot-scope="{ values, search, isOpen }">
              <span class="multiselect__single" v-if="values.length && !isOpen">{{ selectedAccountsTitle }}</span>
            </template>
            <template slot="option" slot-scope="props">
              <div class="option__desc"><font-awesome-icon icon="check" size="sm" hidden/> <span>({{ props.option.accountId }}) - {{ props.option.dealershipName }}</span></div>
            </template>
          </multiselect>
          <div class="invalid-add-rule-feedback" v-show="manageRule.showAddRuleAccountError">
            Please select accountId
          </div>
        </b-col>
      </b-row>
      <h6>Select Application Permissions</h6>
      <b-card v-show="!manageRule.isEditAllConfirmationMode" :class="{'add-rule-error': manageRule.showAddRulePermissionsError}">
        <div v-for="application in applications" :key="application.Id">
          <b-row class="mb-1">
            <b-col cols="6" md="4">
              <span>{{application.Label}}</span>
            </b-col>
            <b-col cols="6" md="6" class="app-permission-select-box">
              <b-form-select size="sm"
                v-model="manageRule.appPermissionDetails[application.Id].permissionType"
                @change="manageRule.showAddRulePermissionsError = false"
                required
                class="app-permissions-select"
              >
                <option
                v-for="permissionType in permissionTypes"
                :key="permissionType.Id"
                :value="permissionType.Id">
                    {{permissionType.Label}}
                </option>
              </b-form-select>
            </b-col>
            <b-col v-if="application.Id === appLeadsId"
              cols="12"
              class="mb-2 mt-1"
            >
              <b-form-checkbox
                v-model="manageRule.appPermissionDetails[application.Id].hasToIncludeLeadsToLeadsManager"
              >
                Display selected accounts Leads in {{accountId}} Lead Manager
              </b-form-checkbox>
            </b-col>
          </b-row>
        </div>
        <div class="invalid-add-rule-feedback" v-show="manageRule.showAddRulePermissionsError">
          Please specify at least one permission
        </div>
      </b-card>
      <b-card v-show="manageRule.isEditAllConfirmationMode">
        <div class="edit-rule-confirmation">
          Are you sure you want to edit all rules? All previously made changes will be overwritten!
        </div>
      </b-card>
      <div slot="modal-footer" class="w-10">
         <b-btn class="float-right" variant="secondary" @click="cancelAddRule()">
           Cancel
         </b-btn>
      </div>

      <div slot="modal-footer" class="w-10">
        <b-btn class="float-right" variant="primary" @click.prevent="submitManageRules()">
          {{manageRule.isEditAllMode ? 'Edit Rules' : 'Create'}}
        </b-btn>
      </div>
    </b-modal>
  </div>
  <div class="h-100 d-flex align-items-center justify-content-center" v-else>
    <loader size="lg"/>
  </div>
</template>

<script>
import globals from '@/globals'
import accountGroupPermissionTypes from '@/shared/accounts/groups/accountGroupPermissionTypes'
import groupsManagementService from '@/services/accounts/groups/GroupsManagementService'
import { ObjectSchema } from '@/shared/common/objectHelpers'
import paging from '@/components/_shared/paging.vue'
import Multiselect from 'vue-multiselect'
import accountPermissionSelector
  from '../../../components/account/groups/permissionsSelector/accountPermissionComponent'
import applicationTypes from '../../../shared/common/applicationTypes'
import loader from '@/components/_shared/loader'

const defaultValues = new ObjectSchema({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  search: { type: String, default: '' }
})

export default {
  name: 'Account-GroupPermissions-Management',
  components: {
    'paging': paging,
    'multiselect': Multiselect,
    'account-permission-selector': accountPermissionSelector,
    'loader': loader
  },
  metaInfo: {
    title: 'Account Group'
  },
  props: {
    groupId: {
      type: String,
      required: true
    },
    accountId: {
      type: Number,
      required: true
    }
  },
  data () {
    return {
      appLeadsId: applicationTypes.AppsLeads.Id,
      users: null,
      groupPermissionRules: [],
      group: null,
      applications: [applicationTypes.AppsCraigslist, applicationTypes.AppsLeads, applicationTypes.AppsAnalytics, applicationTypes.InventoryManagement],
      permissionTypes: [accountGroupPermissionTypes.Default, accountGroupPermissionTypes.ReadOnly, accountGroupPermissionTypes.FullAccess],
      accountGroupPermissionTypes,
      isReadyToShow: false,
      manageRule: {
        show: false,
        selectAll: false,
        isEditAllMode: false,
        isEditAllConfirmationMode: false,
        selectedAccounts: [],
        accountId: 0,
        userId: null,
        showAddRuleAccountError: false,
        showAddRulePermissionsError: false
      },
      deleteRule: {
        ruleId: null,
        userId: null,
        showRuleDeleteConfirmation: false
      }
    }
  },
  mounted () {
    this.populateRuleDetails()
  },
  computed: {
    freeUserAccountsAddRule () {
      let accounts = []
      if (this.manageRule.userId) {
        if (this.groupPermissionRules[this.manageRule.userId]) {
          this.group.accounts.forEach(account => {
            if (account.accountId !== this.accountId && this.groupPermissionRules[this.manageRule.userId].accountIdsWithNoRules.some(x => x === account.accountId)) {
              accounts.push(account)
            }
          })
        } else {
          accounts = this.group.accounts.filter(x => x.accountId !== this.accountId)
        }
      }

      return accounts
    },

    editRulesTitle () {
      return !this.manageRule.isEditAllMode ? 'Add New Rule' : 'Edit Rules'
    },

    selectedAccountsTitle () {
      return this.manageRule.selectedAccounts.length.toString() + (this.manageRule.selectedAccounts.length > 1 ? ' accounts' : ' account') + ' selected'
    }
  },
  methods: {
    manageDisplayRuleDetails (rule) {
      if (rule.showRuleDetails === undefined) {
        this.$set(rule, 'showRuleDetails', true)
      } else {
        rule.showRuleDetails = !rule.showRuleDetails
        if (rule.isPermissionDetailsChanged) {
          this.buildRulePermissionDetails(rule)
          rule.isPermissionDetailsChanged = false
        }
      }
    },

    hasAccountsWithoutRules (userId) {
      if (!this.groupPermissionRules[userId] || !this.groupPermissionRules[userId].rules) {
        return this.group.accounts.length > 1
      } else {
        return (this.group.accounts.length - this.groupPermissionRules[userId].totalRulesCount) > 1
      }
    },

    getUserPermissionRules (userId) {
      return this.groupPermissionRules[userId] ? this.groupPermissionRules[userId].rules : []
    },

    getUserPermissionRulesCountDesc (user) {
      let count = (user.filters || {itemsCount: 0}).itemsCount
      if (count > 0) {
        return `has access to ${count} account${count > 1 ? 's' : ''}`
      }
      return ''
    },

    getRuleDetailsButtonText (rule) {
      return rule.showRuleDetails ? (rule.isPermissionDetailsChanged ? 'Discard And Hide' : 'Hide Details') : 'Show Details'
    },

    hasToShowPaging (userId) {
      return this.groupPermissionRules[userId] && this.groupPermissionRules[userId].rules.length > 0
    },

    buildRulePermissionDetails (rule) {
      let appPermission
      rule.appPermissionDetails = Object.create(null)
      this.applications.forEach(application => {
        appPermission = rule.appPermissions.find(x => x.application === application.Id)
        rule.appPermissionDetails[application.Id] = appPermission !== undefined
          ? {permissionType: appPermission.permissionType, hasToIncludeLeadsToLeadsManager: appPermission.hasToIncludeLeadsToLeadsManager}
          : {permissionType: accountGroupPermissionTypes.Default.Id, hasToIncludeLeadsToLeadsManager: false}
      })
    },

    populateRuleDetails () {
      this.isReadyToShow = false
      let usersInfo = groupsManagementService.getAccountUsers(this.accountId).then(x => {
        this.users = Object.create(null)
        x.data.forEach(user => {
          this.users[user.userId] = user
        })
      })

      let rulesInfo = groupsManagementService.getGroupAccountPermissionRules(this.groupId, this.accountId, defaultValues.getObject()).then(x => {
        this.groupPermissionRules = x.data
        for (let userRuleId in this.groupPermissionRules) {
          this.groupPermissionRules[userRuleId].rules.forEach(rule => {
            this.buildRulePermissionDetails(rule)
          })
        }
      })

      let groupInfo = groupsManagementService.getGroup(this.groupId).then(x => {
        this.group = x.data
      })

      this.manageRule.appPermissionDetails = Object.create(null)
      this.applications.forEach(application => {
        this.manageRule.appPermissionDetails[application.Id] = {permissionType: accountGroupPermissionTypes.Default.Id, hasToIncludeLeadsToLeadsManager: false}
      })

      Promise.all([rulesInfo, usersInfo, groupInfo]).then(() => {
        let userRulesModel
        for (let userId in this.users) {
          userRulesModel = this.groupPermissionRules[userId]
          this.users[userId].filters = this.buildFilters(userRulesModel, userId)
        }
        this.isReadyToShow = true
      }).catch(reason => {
        this.$logger.handleError(reason, 'Can\'t populate rules details')
      })
    },

    getAccountNameByAccountId (accountId) {
      return this.group.accounts.find(x => x.accountId === accountId).dealershipName
    },

    onAddNewRules (userId) {
      this.manageRule.userId = userId
      this.manageRule.isEditAllMode = false
      this.manageRule.show = true
      this.resetCreationRuleModal()
    },

    onModifyRules (userId) {
      this.manageRule.userId = userId
      this.manageRule.isEditAllMode = true
      this.manageRule.show = true
      this.resetCreationRuleModal()
    },

    submitManageRules () {
      if (this.validateAddRuleForm()) {
        let userId = this.manageRule.userId
        let userAccountId = this.accountId
        let appPermissions = []
        let permissionDetails
        let targetAccountIds

        for (let applicationId in this.manageRule.appPermissionDetails) {
          permissionDetails = this.manageRule.appPermissionDetails[applicationId]
          if (permissionDetails.permissionType !== accountGroupPermissionTypes.Default.Id) {
            appPermissions.push({application: parseInt(applicationId), permissionType: permissionDetails.permissionType, hasToIncludeLeadsToLeadsManager: permissionDetails.hasToIncludeLeadsToLeadsManager})
          }
        }

        if (!this.manageRule.isEditAllMode) {
          targetAccountIds = this.manageRule.selectedAccounts.map(x => x.accountId)

          groupsManagementService.createPermissionRules(
            this.groupId,
            this.getRuleUpsertModel(userId, userAccountId, targetAccountIds, appPermissions)
          ).then(x => {
            this.applyUserRulesFilter(this.manageRule.userId)
            this.manageRule.selectedAccounts = []
            this.manageRule.show = false
          }).catch(reason => {
            this.$toaster.error('Error occurred on adding rules')
            this.$logger.handleError(reason, 'Can\'t add rules', {
              groupId: this.groupId,
              userId: userId,
              userAccountId: userAccountId,
              targetAccountIds: targetAccountIds,
              appPermissions: appPermissions
            })
          })
        } else {
          if (!this.manageRule.isEditAllConfirmationMode) {
            this.manageRule.isEditAllConfirmationMode = true
            return
          }

          targetAccountIds = this.groupPermissionRules[userId].rules.map(x => x.targetAccountId)

          groupsManagementService.updatePermissionRules(
            this.groupId,
            this.getRuleUpsertModel(userId, userAccountId, targetAccountIds, appPermissions)
          ).then(x => {
            this.applyUserRulesFilter(this.manageRule.userId)
            this.manageRule.selectedAccounts = []
            this.manageRule.show = false
            this.manageRule.isEditAllConfirmationMode = false
          }).catch(reason => {
            this.$toaster.error('Error occured on editing rules')
            this.manageRule.isEditAllConfirmationMode = false
            this.$logger.handleError(reason, 'Can\'t edit rules', {
              groupId: this.groupId,
              userId: userId,
              userAccountId: userAccountId,
              targetAccountIds: targetAccountIds,
              appPermissions: appPermissions
            })
          })
        }
      }
    },

    validateAddRuleForm () {
      this.manageRule.showAddRuleAccountError = false
      this.manageRule.showAddRulePermissionsError = false

      if (!this.manageRule.isEditAllMode && this.manageRule.selectedAccounts.length <= 0) {
        this.manageRule.showAddRuleAccountError = true
      }

      let isAtLeastOnePermissionSelected = false
      for (let applicationId in this.manageRule.appPermissionDetails) {
        if (this.manageRule.appPermissionDetails[applicationId] !== accountGroupPermissionTypes.Default.Id) {
          isAtLeastOnePermissionSelected = true
          break
        }
      }

      if (!isAtLeastOnePermissionSelected) {
        this.manageRule.showAddRulePermissionsError = true
      }

      return !(this.manageRule.showAddRuleAccountError || this.manageRule.showAddRulePermissionsError)
    },

    cancelAddRule () {
      this.manageRule.show = false
      this.manageRule.isEditAllConfirmationMode = false
    },

    resetCreationRuleModal () {
      this.manageRule.showAddRuleAccountError = false
      this.manageRule.showAddRulePermissionsError = false
      this.manageRule.accountId = null
      this.manageRule.selectedAccounts = []
      this.applications.forEach(application => {
        this.manageRule.appPermissionDetails[application.Id] = {permissionType: accountGroupPermissionTypes.Default.Id, hasToIncludeLeadsToLeadsManager: false}
      })
    },

    changeRulePermissionDetails (rule) {
      this.$set(rule, 'isPermissionDetailsChanged', false)
      let permissionDetails
      let applicationIdInt
      this.$nextTick(() => {
        for (let applicationId in rule.appPermissionDetails) {
          applicationIdInt = parseInt(applicationId)
          permissionDetails = rule.appPermissionDetails[applicationId]
          if (permissionDetails.PermissionType !== accountGroupPermissionTypes.Default.Id) {
            if (!rule.appPermissions.some(x => x.application === applicationIdInt && x.permissionType === permissionDetails.permissionType && x.hasToIncludeLeadsToLeadsManager === permissionDetails.hasToIncludeLeadsToLeadsManager)) {
              rule.isPermissionDetailsChanged = true
            }
          } else if (rule.appPermissions.some(x => x.application === applicationIdInt)) { // empty now but has permission type not None before
            rule.isPermissionDetailsChanged = true
          }
        }
      })
    },

    saveEditPermissionRule (rule) {
      let permissionDetails
      let permissionRulesBeforeChange = Array.from(rule.appPermissions)
      rule.appPermissions = []
      for (let applicationId in rule.appPermissionDetails) {
        permissionDetails = rule.appPermissionDetails[applicationId]
        if (permissionDetails.permissionType !== accountGroupPermissionTypes.Default.Id) {
          rule.appPermissions.push({application: parseInt(applicationId), permissionType: permissionDetails.permissionType, hasToIncludeLeadsToLeadsManager: permissionDetails.hasToIncludeLeadsToLeadsManager})
        }
      }

      groupsManagementService.updatePermissionRule(this.groupId, rule).then(x => {
        rule.isPermissionDetailsChanged = false
      }).catch(reason => {
        // show old settings if failed to save new
        rule.appPermissions = permissionRulesBeforeChange
      })
    },

    tryToDeleteRule (ruleId, userId) {
      this.deleteRule.ruleId = ruleId
      this.deleteRule.userId = userId
      this.deleteRule.showRuleDeleteConfirmation = true
    },

    deletePermissionRule () {
      if (this.deleteRule.ruleId) {
        groupsManagementService.deletePermissionRule(this.groupId, this.deleteRule.ruleId).then(x => {
          this.applyUserRulesFilter(this.deleteRule.userId)
          this.deleteRule.showRuleDeleteConfirmation = false
          this.deleteRule.userId = null
          this.deleteRule.ruleId = null
        }).catch(reason => {
          console.error(reason)
        })
      }
    },

    applyUserRulesFilter (userId) {
      let user = this.users[userId]
      let filters = {pageNumber: user.filters.pageNumber, pageSize: user.filters.pageSize, search: user.filters.search}
      groupsManagementService.getUserGroupPermissionRulesFiltered(this.groupId, this.accountId, userId, filters).then(x => {
        this.users[userId].filters = this.buildFilters(x.data, userId)
        this.$set(this.groupPermissionRules, userId, x.data)
        this.groupPermissionRules[userId].rules.forEach(rule => {
          this.buildRulePermissionDetails(rule)
        })
      })
    },

    buildFilters (userRulesModel, userId) {
      let filters
      if (userRulesModel) {
        filters = {pageNumber: userRulesModel.pageNumber,
          pageSize: userRulesModel.pageSize,
          search: userRulesModel.search,
          itemsCount: userRulesModel.totalRulesCount
        }
      } else {
        filters = globals().getClonedValue(defaultValues.getObject())
        filters.itemsCount = 0
      }

      if (this.users[userId].filters) {
        filters.previousSearch = this.users[userId].filters.previousSearch
      }

      return filters
    },

    pageChanged (pageNumber, userId) {
      if (this.users[userId].filters.pageNumber !== pageNumber) {
        this.users[userId].filters.pageNumber = pageNumber
        this.applyUserRulesFilter(userId)
      }
    },

    searchChanged (userId) {
      let userFilters = this.users[userId].filters
      if (userFilters.search !== userFilters.previousSearch) {
        this.$set(userFilters, 'previousSearch', userFilters.search)
        this.applyUserRulesFilter(userId)
      }
    },

    changePageSize (newSize, userId) {
      if (this.users[userId].filters.pageSize !== newSize) {
        this.users[userId].filters.pageSize = newSize
        this.users[userId].filters.pageNumber = 1
        this.applyUserRulesFilter(userId)
      }
    },

    accountSelectOption (accountInfo) {
      return '(' + accountInfo.accountId.toString() + ')' + ' - ' + accountInfo.dealershipName
    },

    manageSelectAllAccounts () {
      if (!this.manageRule.selectAll) {
        this.manageRule.selectAll = true
        this.manageRule.selectedAccounts = this.freeUserAccountsAddRule
      } else {
        this.manageRule.selectAll = false
        this.manageRule.selectedAccounts = []
      }
    },
    getRuleUpsertModel (userId, userAccountId, targetAccountIds, appPermissions) {
      return { userId: userId, userAccountId: userAccountId, targetAccountIds: targetAccountIds, appPermissions: appPermissions }
    }
  }
}
</script>

<style lang="scss" scoped>
  .permission-rule-row {
    border-bottom: 1px solid #dee2e6;
    padding: 8px 0;
    position: relative;

    &:hover {
      background-color: rgba(24, 28, 33, 0.035);
    }

    &:nth-child(odd) {
      background-color: rgba(24, 28, 33, 0.025);
    }

    hr {
      background-color: #dee2e6;
      width: 100%;
    }

    :last {
      border-bottom: none;
    }

    .delete-rule-button {
      position: absolute;
      width: 30px;
      height: 26px;
      top: 3px;
      right: 3px;
    }
  }

  .rule-table-head {
    font-weight: bold;
    border-bottom: 2px solid #dee2e6;
    border-top: 1px solid #dee2e6;
    padding: 10px 0;
  }

  .left-head {
    font-weight: bold;
  }

  .app-permissions-select {
    width: 114px;
    display: inline-block;
    margin-bottom: 2px;
    padding-right: 1.5rem;
  }

  .permission-details-col {
    padding: 0;

    .card-body {
      padding: 15px;
    }
  }

  .app-permission-select-box {
    padding-left: 0;
  }

  .xs-header-line {
    height: 1px;
    margin-bottom: 0;
  }

  .add-rule-error {
    border-color: #d9534f;
  }

  .invalid-add-rule-feedback {
    margin-top: 0.25rem;
    color: #d9534f;
  }

  .save-permission-changes-col {
    display: flex;
    flex-direction: column;
    justify-content: end;
    padding-right: 0;

    div {
      margin-top: auto;
      margin-bottom: 0.25rem;
    }
  }

  #delete-rule-confirmation-modal p {
    font-weight: 400;
    font-size: 1.313rem;
    color: #C90F17;
  }

  .add-rule-select-all-col {
    text-align: right;

    span {
      cursor: pointer;
      color: #C90F17;
    }
  }

  .multiselect__option--selected svg {
    display: inline-block !important;
  }

  .multiselect__option--highlight, .multiselect__option--selected.multiselect__option--highlight {
      background: #C90F17 !important;
      color: #fff !important;
  }

  .multiselect, .multiselect__input, .multiselect__single {
    font-family: inherit;
    font-size: inherit;
}

  .edit-rule-confirmation {
    font-weight: 400;
    font-size: 1.313rem;
    color: #C90F17;
  }
</style>
