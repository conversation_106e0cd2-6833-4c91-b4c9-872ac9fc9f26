<template>
  <div class="position-relative">
    <paging
    class="custom-tab-paging d-none d-md-block"
    :pageNumber="inventoryData.filters.pageNumber"
    :pageSize="inventoryData.filters.pageSize"
    :totalItems="inventoryData.vehiclesTotalCount"
    @numberChanged="onPageChanged"
    @changePageSize="onPageSizeChanged" />

    <b-tabs v-model="selectedTabIndex" class="nav-tabs-top nav-responsive-sm" no-fade>

        <b-tab v-for="(tab, index) in tabs" :key="index" :title="`${tab.title} (${tab.vehiclesTotal})`">
          <inventory-filters-form
          :buttons="filterOptions"
          :search="inventoryData.filters.search"
          variant="buttons"
          @changeActive="onChangeActiveButton"
          @searchChanged="onChangeSearch" />
        </b-tab>

    </b-tabs>
    <b-card no-body>
      <inventory-tasks-listing :alertType='this.inventoryData.filters.alertType' :inventoryData='inventoryData' @sortChanged='onSortChanged'/>

      <paging
      :pageNumber="inventoryData.filters.pageNumber"
      :pageSize="inventoryData.filters.pageSize"
      :totalItems="inventoryData.vehiclesTotalCount"
      titled
      pageSizeSelector
      @numberChanged="onPageChanged"
      @changePageSize="onPageSizeChanged" />
    </b-card>
  </div>
</template>

<script>
import paging from '@/components/_shared/paging.vue'
import inventoryFiltersForm from '../inventoryFiltersForm'
import vehicleTasks from '@/shared/common/vehicle/vehicleTasks'
import defaultInventoryTasksFilters from '@/shared/inventory/inventoryTasksFilters'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import conditions from '@/shared/common/vehicle/vehicleConditions'
import ConditionFilter from '@/shared/inventory/filterOption/conditionFilter'
import inventoryTasksListing from './inventoryTasksListing'
import InventoryService from '../../../services/inventory/InventoryService'

const queryStringHelper = new QueryStringHelper(defaultInventoryTasksFilters)

export default {
  name: 'inventory-tasks-filters',
  components: {
    'inventory-filters-form': inventoryFiltersForm,
    'inventory-tasks-listing': inventoryTasksListing,
    'paging': paging
  },
  data () {
    return {
      inventoryData: {
        accountId: +this.$route.params.accountId,
        vehiclesTotalCount: null,
        vehicles: [],
        filters: defaultInventoryTasksFilters.getObject()
      },
      filterOptions: [
        new ConditionFilter(0, conditions.all, false, 'Total', 0),
        new ConditionFilter(1, conditions.new, false, 'New', 0),
        new ConditionFilter(2, conditions.used, false, 'Used', 0),
        new ConditionFilter(3, conditions.cpo, false, 'CPO', 0)
      ],
      tabs: [
        { alertTypeKey: vehicleTasks.all.key, alertType: vehicleTasks.all.value, title: vehicleTasks.all.title, vehiclesTotal: 0 },
        { alertTypeKey: vehicleTasks.photo.key, alertType: vehicleTasks.photo.value, title: vehicleTasks.photo.title, vehiclesTotal: 0 },
        { alertTypeKey: vehicleTasks.video.key, alertType: vehicleTasks.video.value, title: vehicleTasks.video.title, vehiclesTotal: 0 },
        { alertTypeKey: vehicleTasks.price.key, alertType: vehicleTasks.price.value, title: vehicleTasks.price.title, vehiclesTotal: 0 },
        { alertTypeKey: vehicleTasks.description.key, alertType: vehicleTasks.description.value, title: vehicleTasks.description.title, vehiclesTotal: 0 }
      ]
    }
  },
  beforeMount () {
    this.filterOptions.find(x => x.conditionType === conditions.all).isActive = true
  },
  created () {
    this.inventoryData.filters = queryStringHelper.parseQueryStringToObject(this.$router)
    this.setActiveById(this.inventoryData.filters.condition)
    this.fetchAlertsData()
    this.fetchAlertsTotals()
  },
  computed: {
    selectedTabIndex: {
      get: function () {
        return this.getTabIndex(this.inventoryData.filters.alertType)
      },
      set: function (index) {
        this.setActiveById(this.filterOptions.find(x => x.conditionType === conditions.all).id)
        this.setDefaultFilters()
        this.inventoryData.filters.alertType = index + 1
        this.synchronizeUrlAndReload()
      }
    }
  },
  methods: {
    getTabIndex (alertType) {
      if (alertType < 0 || alertType > 4) {
        return 0
      }
      let index = this.tabs.find(x => x.alertType === alertType).alertType
      if (index >= 0) {
        return index - 1
      }

      return 0
    },
    setActiveById (id) {
      let filterOption = this.filterOptions.find(x => x.id === id)
      this.filterOptions.forEach(x => { x.isActive = false })
      filterOption.isActive = true
    },
    onPageChanged (newVal) {
      this.inventoryData.filters.pageNumber = newVal
      this.synchronizeUrlAndReload()
    },
    onPageSizeChanged (newPageSize) {
      this.inventoryData.filters.pageSize = newPageSize
      this.inventoryData.filters.pageNumber = 1
      this.synchronizeUrlAndReload()
    },
    onChangeActiveButton (newValue) {
      this.inventoryData.filters.condition = newValue
      this.setActiveById(newValue)
      this.synchronizeUrlAndReload()
    },
    onChangeSearch (newVal) {
      this.inventoryData.filters.search = newVal
      this.synchronizeUrlAndReload()
    },
    onSortChanged (sortType) {
      this.inventoryData.filters.sortType = sortType
      this.synchronizeUrlAndReload()
    },
    synchronizeUrlAndReload () {
      queryStringHelper.rebuildParamsInQueryString(this.$router, this.inventoryData.filters)
      this.fetchAlertsData()
    },
    setDefaultFilters () {
      this.inventoryData.filters.condition = 0
      this.inventoryData.filters.search = ''
      this.inventoryData.filters.sortType = 0
      this.inventoryData.filters.pageNumber = 1
    },
    fetchAlertsData () {
      return InventoryService.getAlertsListing(this.inventoryData.accountId, this.inventoryData.filters)
        .then(result => {
          let responseData = result.data || {conditions: []}
          this.inventoryData.vehiclesTotalCount = responseData.alertsTotalCount || 0
          this.inventoryData.vehicles = responseData.vehicles || []
          let allCount = 0
          let newCount = 0
          let usedCount = 0
          let cpoCount = 0
          responseData.conditions.forEach((element) => {
            switch (element.condition) {
              case conditions.all:
                allCount = element.count
                break
              case conditions.new:
                newCount = element.count
                break
              case conditions.used:
                usedCount = element.count
                break
              case conditions.cpo:
                cpoCount = element.count
                break
            }
          })

          this.filterOptions.find(x => x.conditionType === conditions.all).total = allCount
          this.filterOptions.find(x => x.conditionType === conditions.new).total = newCount
          this.filterOptions.find(x => x.conditionType === conditions.used).total = usedCount
          this.filterOptions.find(x => x.conditionType === conditions.cpo).total = cpoCount
        }).catch(ex => {
          this.$logger.handleError(ex, `Can't get alerts listing for accountId: ${this.inventoryData.accountId}`, this.inventoryData.filters)
        })
    },
    fetchAlertsTotals () {
      return InventoryService.getAlertsTotals(this.inventoryData.accountId)
        .then(result => {
          let response = result.data || {}
          this.tabs[0].vehiclesTotal = response.totalAlertsCount || 0
          this.tabs[1].vehiclesTotal = response.photoAlertsCount || 0
          this.tabs[2].vehiclesTotal = response.videoAlertsCount || 0
          this.tabs[3].vehiclesTotal = response.priceAlertsCount || 0
          this.tabs[4].vehiclesTotal = response.descriptionAlertsCount || 0
        }).catch(ex => {
          this.$logger.handleError(ex, `Can't get alerts totals for accountId: ${this.inventoryData.accountId}`)
        })
    }
  }
}
</script>

<style lang="scss">
.custom-tab-paging {
  position: absolute;
  right: -10px;
  top: -15px;
  z-index: 2;
}
@media (max-width: 575px) {
  .nav-responsive-sm > .nav, .nav-responsive-sm > div > .nav {
      flex-wrap: nowrap!important;
      -webkit-box-orient: horizontal;
      -webkit-box-direction: normal;
      -ms-flex-direction: row;
      flex-direction: row;
      border: 0;
      overflow-x: scroll;
      white-space: nowrap;
      -webkit-overflow-scrolling: touch;
  }
  .nav-responsive-sm > div > .nav-tabs .nav-item {
    -webkit-box-flex: 1 !important;
    -ms-flex: 1 1 auto !important;
    flex: 1 1 auto !important;
  }
}
</style>
