<template>
  <div class="table-responsive">
    <b-table
      striped
      hover
      :items="[...items]"
      :fields="tableFields"
      :no-sort-reset="true"
      class="penske-report-table"
      :sort-by.sync="sortBy"
      :sort-desc.sync="sortDesc"
    >
    </b-table>
  </div>
</template>

<script>
import penskeTableSortTypes from './penskeTableSortTypes'

export default {
  name: 'month-table',
  props: {
    items: Array,
    sort: Number
  },
  created () {
    this.updateTableSort(this.sort)
  },
  data () {
    return {
      timerId: -1,
      filters: {
        sortBy: null,
        sortDesc: false,
        dateFrom: new Date(),
        dateTo: new Date()
      },
      tableFields: [{
        key: 'accountId',
        label: 'AID',
        sortable: true,
        sortTypeAsc: penskeTableSortTypes.accountIDAsc,
        sortTypeDesc: penskeTableSortTypes.accountIDDesc
      },
      {
        key: 'accountName',
        label: 'Account Name',
        sortable: true,
        sortTypeAsc: penskeTableSortTypes.accountNameAsc,
        sortTypeDesc: penskeTableSortTypes.accountNameDesc
      },
      {
        key: 'newUsers',
        label: 'Total New Visitors',
        sortable: true,
        sortTypeAsc: penskeTableSortTypes.newUsersAsc,
        sortTypeDesc: penskeTableSortTypes.newUsersDesc,
        formatter: val => this.$locale.formatNumber(val)
      },
      {
        key: 'sessions',
        label: 'Total Visits',
        sortable: true,
        sortTypeAsc: penskeTableSortTypes.sessionsAsc,
        sortTypeDesc: penskeTableSortTypes.sessionsDesc,
        formatter: val => this.$locale.formatNumber(val)
      },
      {
        key: 'mobileSessions',
        label: 'Mobile Visits',
        sortable: true,
        sortTypeAsc: penskeTableSortTypes.mobileSessionsAsc,
        sortTypeDesc: penskeTableSortTypes.mobileSessionsDesc,
        formatter: val => this.$locale.formatNumber(val)
      },
      {
        key: 'organicSearchVisits',
        label: 'Organic Visits',
        sortable: true,
        sortTypeAsc: penskeTableSortTypes.organicSearchVisitsAsc,
        sortTypeDesc: penskeTableSortTypes.organicSearchVisitsDesc,
        formatter: val => this.$locale.formatNumber(val)
      },
      {
        key: 'organicSearchFormLeads',
        label: 'Organic Form Submissions',
        sortable: true,
        sortTypeAsc: penskeTableSortTypes.organicSearchFormLeadsAsc,
        sortTypeDesc: penskeTableSortTypes.organicSearchFormLeadsDesc,
        formatter: val => this.$locale.formatNumber(val)
      },
      {
        key: 'totalFormLeads',
        label: 'Total Form Submissions',
        sortable: true,
        sortTypeAsc: penskeTableSortTypes.totalFormLeadsAsc,
        sortTypeDesc: penskeTableSortTypes.totalFormLeadsDesc,
        formatter: val => this.$locale.formatNumber(val)
      },
      {
        key: 'phoneLeads',
        label: 'Total Phone Leads',
        sortable: true,
        sortTypeAsc: penskeTableSortTypes.phoneLeadsAsc,
        sortTypeDesc: penskeTableSortTypes.phoneLeadsDesc,
        formatter: val => this.$locale.formatNumber(val)
      },
      {
        key: 'totalLeads',
        label: 'Total Leads (Form + Phone)',
        sortable: true,
        sortTypeAsc: penskeTableSortTypes.totalLeadsAsc,
        sortTypeDesc: penskeTableSortTypes.totalLeadsDesc,
        formatter: val => this.$locale.formatNumber(val)
      },
      {
        key: 'convRatePerSession',
        label: 'Conversion Rate per Visit',
        sortable: true,
        sortTypeAsc: penskeTableSortTypes.convRatePerSessionAsc,
        sortTypeDesc: penskeTableSortTypes.convRatePerSessionDesc,
        formatter: val => this.$locale.formatNumberPercentage(val / 100)
      },
      {
        key: 'paidSearchSpend',
        label: 'Total Paid Search Spend',
        sortable: true,
        sortTypeAsc: penskeTableSortTypes.paidSearchSpendAsc,
        sortTypeDesc: penskeTableSortTypes.paidSearchSpendDesc,
        formatter: val => this.$locale.formatCurrency(val)
      },
      {
        key: 'paidSearchSessions',
        label: 'Paid Search Visits',
        sortable: true,
        sortTypeAsc: penskeTableSortTypes.paidSearchSessionsAsc,
        sortTypeDesc: penskeTableSortTypes.paidSearchSessionsDesc,
        formatter: val => this.$locale.formatNumber(val)
      },
      {
        key: 'paidSearchFormLeads',
        label: 'Paid Search Form Submissions',
        sortable: true,
        sortTypeAsc: penskeTableSortTypes.paidSearchFormLeadsAsc,
        sortTypeDesc: penskeTableSortTypes.paidSearchFormLeadsDesc,
        formatter: val => this.$locale.formatNumber(val)
      },
      {
        key: 'paidSearchPhoneLeads',
        label: 'Paid Search Phone Calls',
        sortable: true,
        sortTypeAsc: penskeTableSortTypes.paidSearchPhoneLeadsAsc,
        sortTypeDesc: penskeTableSortTypes.paidSearchPhoneLeadsDesc,
        formatter: val => this.$locale.formatNumber(val)
      },
      {
        key: 'paidSearchTotalLeads',
        label: 'Paid Search Leads (Form + Phone)',
        sortable: true,
        sortTypeAsc: penskeTableSortTypes.paidSearchTotalLeadsAsc,
        sortTypeDesc: penskeTableSortTypes.paidSearchTotalLeadsDesc,
        formatter: val => this.$locale.formatNumber(val)
      },
      {
        key: 'paidSearchConvRatePerSession',
        label: 'Paid Search Conversions per Visit',
        sortable: true,
        sortTypeAsc: penskeTableSortTypes.paidSearchConvRatePerSessionAsc,
        sortTypeDesc: penskeTableSortTypes.paidSearchConvRatePerSessionDesc,
        formatter: val => this.$locale.formatNumberPercentage(val / 100)
      },
      {
        key: 'paidSearchCostPerLead',
        label: 'Paid Search Cost per Lead',
        sortable: true,
        sortTypeAsc: penskeTableSortTypes.paidSearchCostPerLeadAsc,
        sortTypeDesc: penskeTableSortTypes.paidSearchCostPerLeadDesc,
        formatter: val => this.$locale.formatCurrency(val)
      }]
    }
  },
  computed: {
    sortBy: {
      get () {
        return this.filters.sortBy
      },
      set (sortBy) {
        if (this.filters.sortBy === sortBy) {
          return
        }

        this.filters.sortBy = sortBy
        this.delayedEmit('sortTypeChanged', this.sortType)
      }
    },
    sortDesc: {
      get () {
        return this.filters.sortDesc
      },
      set (sortDesc) {
        if (this.filters.sortDesc === sortDesc) {
          return
        }

        this.filters.sortDesc = sortDesc
        this.delayedEmit('sortTypeChanged', this.sortType)
      }
    },
    sortType () {
      const sortingColumn = this.tableFields.find(x => x.key === this.filters.sortBy)

      return sortingColumn
        ? this.filters.sortDesc
          ? sortingColumn.sortTypeDesc
          : sortingColumn.sortTypeAsc
        : penskeTableSortTypes.Undefined
    }
  },
  methods: {
    delayedEmit (eventName, data) {
      if (this.timerId !== -1) {
        clearTimeout(this.timerId)
      }

      this.timerId = setTimeout(() => {
        this.$emit(eventName, data)
      }, 10)
    },
    updateTableSort (sortType) {
      const sortedColumn = this.tableFields.find(x =>
        x.sortTypeAsc === sortType ||
        x.sortTypeDesc === sortType
      )

      if (sortedColumn) {
        this.filters.sortBy = sortedColumn.key
        this.filters.sortDesc = sortedColumn.sortTypeDesc === sortType
      }
    }
  }
}
</script>

<style>
  .penske-report-table thead tr th {
    white-space: nowrap;
  }
</style>
