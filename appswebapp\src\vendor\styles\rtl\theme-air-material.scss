$rtl-support: true;

@import "../_appwork/include-material";
@import "../_theme/common";
@import "../_theme/libs";
@import "../_theme/uikit";

$primary-color: #3c97fe;
$body-bg: #f8f8f8;

body {
  background: $body-bg;
}

@include appwork-common-material-theme($primary-color);
@include appwork-libs-material-theme($primary-color);
@include appwork-uikit-theme($primary-color);

// Navbar

@include appwork-navbar-variant('.bg-navbar-theme', $body-bg, $color: $text-muted, $active-color: $body-color);

.layout-navbar {
  box-shadow: 0 1px 0 rgba($black, 0.04);
}

// Sidenav

@include appwork-sidenav-variant('.bg-sidenav-theme', $body-bg, $color: $text-muted, $active-color: $body-color, $menu-bg: darken($body-bg, 2.5%));

.bg-sidenav-theme {
  .sidenav-inner > .sidenav-item > .sidenav-link .sidenav-icon,
  .sidenav-inner > .sidenav-item > .sidenav-link:hover .sidenav-icon,
  .sidenav-inner > .sidenav-item > .sidenav-link:focus .sidenav-icon {
    color: $gray-300;
  }
  .sidenav-inner > .sidenav-item.active > .sidenav-link .sidenav-icon {
    color: $primary-color !important;
  }
  .sidenav-item.active > .sidenav-link:not(.sidenav-toggle) {
    background: none !important;
  }
}

.layout-sidenav {
  box-shadow: 0 0 0 1px rgba($black, .04);
}

.layout-sidenav-horizontal {
  box-shadow: 0 -1px 0 rgba($black, .04) inset;
}

// Footer

@include appwork-footer-variant('.bg-footer-theme', $body-bg, $color: $text-muted, $active-color: $body-color);

.layout-footer {
  box-shadow: 0 -1px 0 rgba($black, 0.04);
}

// Custom styling

hr {
  border-color: rgba($black, 0.04);
}

.card,
.nav-tabs-top > .tab-content,
.nav-tabs-right > .tab-content,
.nav-tabs-bottom > .tab-content,
.nav-tabs-left > .tab-content {
  border-color: transparent;
  box-shadow: 0 10px 30px 0 rgba(24, 28, 33, .04);
}
.ng2-archwizard-boxed-steps ul.steps-indicator,
.form-wizard-boxed-steps .wizard-nav {
  border-color: transparent !important;
  box-shadow: 0 10px 30px 0 rgba(24, 28, 33, .04) !important;
}

.nav-tabs-top,
.nav-tabs-right,
.nav-tabs-bottom,
.nav-tabs-left {
   > .nav-tabs:not(.tabs-alt) .nav-link:not(.active),
   > div > .nav-tabs:not(.tabs-alt) .nav-link:not(.active) {
    border-color: transparent !important;
  }

   > .nav .nav-link.active,
   > div > .nav .nav-link.active {
    border-color: $nav-tabs-link-active-bg !important;
  }
}

.nav-tabs-top > .nav-tabs:not(.tabs-alt),
.nav-tabs-top > div > .nav-tabs:not(.tabs-alt),
.nav-tabs-bottom > .nav-tabs:not(.tabs-alt),
.nav-tabs-bottom > div > .nav-tabs:not(.tabs-alt) {
  padding-right: $border-width;
  padding-left: $border-width;
}
.nav-tabs-right > .nav-tabs:not(.tabs-alt),
.nav-tabs-right > div > .nav-tabs:not(.tabs-alt),
.nav-tabs-left > .nav-tabs:not(.tabs-alt),
.nav-tabs-left > div > .nav-tabs:not(.tabs-alt) {
  padding-top: $border-width;
  padding-bottom: $border-width;
}
