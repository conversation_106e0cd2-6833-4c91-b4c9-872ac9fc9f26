export default {
  reportTypes: {
    contactsReport: { value: 0, text: 'Contacts Report' },
    accountLeadsWebFormsReport: { value: 1, text: 'Account Leads Web Form Report' },
    accountLeadsSettingsReport: { value: 2, text: 'Account Leads Settings Report' }
  },
  displayFilterTypeOptions: {
    all: { value: 0, text: 'Display All' },
    phoneOnly: { value: 1, text: 'Phones Only' },
    emailOnly: { value: 2, text: 'Emails Only' }
  },
  contactsReportTableFields: [
    {
      key: 'accountId',
      label: 'Account Id',
      thClass: 'account-id-fix-column',
      tdClass: 'py-2 align-middle account-id-fix-column',
      thStyle: 'background: #fff'
    },
    {
      key: 'accountName',
      label: 'Account Name',
      thClass: 'account-name-fix-column',
      tdClass: 'py-2 align-middle account-name-fix-column',
      thStyle: 'background: #fff'
    },
    {
      key: 'location',
      label: 'Location',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'campaignName',
      label: 'Campaign',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'communicationType',
      label: 'Type',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'proxyPhoneNumber',
      label: 'Proxy Phone Number',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'forwardsToNumber',
      label: 'Forwards to Number',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'proxyEmail',
      label: 'Proxy Email',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'forwardsToEmail',
      label: 'Forwards to Email',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'contactId',
      label: 'Contact Id',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'forwardsToContactName',
      label: 'Forwards To Contact Name',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'manageLink',
      label: 'Manage Link',
      tdClass: 'py-2 align-middle text-center'
    }],
  leadsSettingsReportTableFields: [
    {
      key: 'accountId',
      label: 'Account Id',
      thClass: 'account-id-fix-column',
      tdClass: 'py-2 align-middle account-id-fix-column',
      thStyle: 'background: #fff'
    },
    {
      key: 'accountName',
      label: 'Account Name',
      thClass: 'account-name-fix-column',
      tdClass: 'py-2 align-middle account-name-fix-column',
      thStyle: 'background: #fff'
    },
    {
      key: 'sendDailyLeadsReport',
      label: 'Send Daily Leads Report',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'reportRecipients',
      label: 'Report Recipients',
      tdClass: 'py-2 align-middle text-wrap'
    },
    {
      key: 'reportDeliveryTime',
      label: 'Report Delivery Time',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'autoArchiveLeads',
      label: 'Auto Archive Leads',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'autoArchiveLeadsDaysAfter',
      label: 'Auto Archive Leads Days After',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'deleteArchivedLeads',
      label: 'Delete Archive Leads',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'deleteArchiveLeadsDaysAfter',
      label: 'Delete Archive Leads Days After',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'adfLeadSource',
      label: 'ADF Lead Source',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'dealerSocketApiId',
      label: 'Dealer Socket API Id',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'shiftDigitalApiId',
      label: 'Shift Digital API Id',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'shiftDigitalProgram',
      label: 'Shift Digital Program',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'shiftDigitalPostUrl',
      label: 'Shift Digital Post Url',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'premierTruckDbName',
      label: 'Premier Truck Db Name',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'premierTruckPostUrl',
      label: 'Premier Truck Post Url',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'showProviderId',
      label: 'Show Provider Id',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'addLeadTypeServiceToAdfEmail',
      label: 'Add LeadType Service to ADF Email Id',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'showAdfEBaySource',
      label: 'Show ADF eBay Source',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'isActive',
      label: 'Is Active',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'buyCallerName',
      label: 'Buy Caller Name',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'hasCustomFees',
      label: 'Has Custom Fees',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'twilioSmsFee',
      label: 'Twilio SMS Fee',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'twilioCallFee',
      label: 'Twilio Call Fee',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'displayedContactPercent',
      label: 'Displayed Contact Percent',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'copyToEmailAddressesPercent',
      label: 'Copy To Email Addresses Percent',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'copyToAdfAddressesPercent',
      label: 'Copy To ADF Addresses Percent',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'manageLink',
      label: 'Manage Link',
      tdClass: 'py-2 align-middle text-center'
    }
  ],
  leadsWebFormReportTableFields: [
    {
      key: 'accountId',
      label: 'Account Id',
      thClass: 'account-id-fix-column',
      tdClass: 'py-2 align-middle account-id-fix-column',
      thStyle: 'background: #fff'
    },
    {
      key: 'accountName',
      label: 'Account Name',
      thClass: 'account-name-fix-column',
      tdClass: 'py-2 align-middle account-name-fix-column',
      thStyle: 'background: #fff'
    },
    {
      key: 'leadFormType',
      label: 'Lead Form Type',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'locationType',
      label: 'Location Type',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'campaigns',
      label: 'Campaigns',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'legacyCampaignId',
      label: 'Legacy Campaign Id',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'departments',
      label: 'Departments',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'sendEmailToDisplayedContact',
      label: 'Send Email To Displayed Contact',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'sendAutoResponseEmailToLead',
      label: 'Send Auto Response Email to Lead',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'userEmailTemplate',
      label: 'User Email Template',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'sendDealerSocketLead',
      label: 'Send Dealer Socket Lead',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'sendShiftDigitalLead',
      label: 'Send Shift Digital Lead',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'sendPremierTruckLead',
      label: 'Send Premier Truck Lead',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'sendNotificationEmailToDealership',
      label: 'Send Notification Email to Dealership',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'dealershipEmailsTo',
      label: 'Dealership Emails To',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'dealershipEmailsCc',
      label: 'Dealership Emails CC',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'sendAdfEmailToCrm',
      label: 'Send ADF Email to CRM',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'adfLeadSourcesNotes',
      label: 'ADF Lead Sources Notes',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'adfEmailsTo',
      label: 'ADF Emails To',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'adfEmailsCc',
      label: 'ADF Emails CC',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'manageLink',
      label: 'Manage Link',
      tdClass: 'py-2 align-middle text-center'
    }
  ]
}
