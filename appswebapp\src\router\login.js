import store from '../store'
import axios from 'axios'

function getLoginRouteWithRedirection (router) {
  let basePath = ''
  switch (router.mode) {
    case 'history':
      basePath = window.location.pathname + window.location.search
      break
    case 'hash':
      basePath = window.location.hash.slice(1) + window.location.search
      break
  }

  const splittedUrl = basePath.split('returnurl=') // ex.: ["/login/?", "http%3A%2F%2Fapps.sandbox.ebizautos.com%2Fcraigslist%2Fdashboard"]

  if (splittedUrl.length === 2) {
    return { path: '/login', query: { returnurl: splittedUrl[1] } }
  }

  return { path: '/login', query: { redirect: basePath } }
}

function bindAuthorizationModules (router) {
  axios.interceptors.response.use(undefined, function (err) {
    return new Promise(function (resolve, reject) {
      if (err.response && err.response.status === 401) {
        store.commit('authentication/setIsAuthenticated', false)
        router.push(getLoginRouteWithRedirection(router))
        resolve(err)
      }
      reject(err)
    })
  })

  router.beforeEach((to, from, next) => {
    if (!store.getters['authentication/isAuthenticated'] && !to.meta.allowAnonymous) {
      next(getLoginRouteWithRedirection(router))
    } else {
      next()
    }
  })
}

export default {
  routes: [
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/pages/authentication/login.vue'),
      meta: {
        allowAnonymous: true
      },
      beforeEnter: (to, from, next) => {
        if (!store.getters['authentication/isAuthenticated']) {
          next()
        } else {
          next('/')
        }
      }
    },
    {
      path: '/mfa',
      name: 'MfaMethod',
      component: () => import('@/pages/authentication/mfa-method.vue'),
      meta: { allowAnonymous: true },
      beforeEnter: (to, from, next) => {
        if (store.state.mfa.challengeId) { next() } else { next('/login') }
      }
    },
    {
      path: '/mfa/verify',
      name: 'MfaVerify',
      component: () => import('@/pages/authentication/mfa-verify.vue'),
      meta: { allowAnonymous: true },
      beforeEnter: (to, from, next) => {
        if (store.state.mfa.challengeId) { next() } else { next('/login') }
      }
    },
    {
      path: '/reset',
      name: 'Password Reset',
      component: () => import('@/pages/authentication/reset.vue'),
      meta: {
        allowAnonymous: true
      }
    },
    {
      path: '/reset-done',
      name: 'Password Sent',
      component: () => import('@/pages/authentication/resetDone'),
      meta: {
        allowAnonymous: true
      }
    }
  ],
  bindAuthorizationModules: bindAuthorizationModules
}
