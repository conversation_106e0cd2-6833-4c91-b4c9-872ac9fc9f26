import BaseService from './../BaseService'

class LeadsLogService extends BaseService {
  getLeadsVoiceApiLogs (filters) {
    return this.axios.get(`/api/leads/logs/voice`, { params: filters })
  };
  getLeadsSMSApiLogs (filters) {
    return this.axios.get(`/api/leads/logs/sms`, { params: filters })
  };
  getLeadsEmailApiLogs (filters) {
    return this.axios.get(`/api/leads/logs/email`, { params: filters })
  };
  getLeadsGalleryApiLogs (filters) {
    return this.axios.get(`/api/leads/logs/web_forms`, { params: filters })
  };
  getLeadsEbayApiLogs (filters) {
    return this.axios.get(`/api/leads/logs/ebay`, { params: filters })
  };
  getLeadsStatisticApiLogs (filters) {
    return this.axios.get(`/api/leads/logs/statistic`, { params: filters })
  };
  getLeadsNotificationApiLogs (filters) {
    return this.axios.get(`/api/leads/logs/notification`, { params: filters })
  };
  getLeadsServiceApiLogs (filters) {
    return this.axios.get(`/api/leads/logs/service`, { params: filters })
  };
  getLeadsSynchronizationApiLogs (filters) {
    return this.axios.get(`/api/leads/logs/synchronization`, { params: filters })
  }
  getLeadsApiDetailsLog (logType, logId) {
    return this.axios.get(`/api/leads/logs/${logType}/${logId}/details`)
  }
}

export default new LeadsLogService()
