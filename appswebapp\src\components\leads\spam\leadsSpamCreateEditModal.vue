<template>
  <b-modal
    :title='getTitle'
    :visible='isShow'
    @hide='onHide'
  >
    <detail-row fixedPlayloadWidth>
      <span slot="title">Application:</span>
      <b-form-select v-model='item.applicationType' slot="payload" @input="onInputApplicationType" text-field='label' :options='getApplicationOptions'></b-form-select>
    </detail-row>

    <detail-row fixedPlayloadWidth>
      <span slot="title">Field:</span>
      <b-form-select v-model='item.fieldType' slot="payload" text-field='label' :options='getFieldOptions'></b-form-select>
    </detail-row>

    <detail-row fixedPlayloadWidth>
      <span slot="title">Condition:</span>
      <b-form-select v-model='item.conditionType' slot="payload" text-field='label' :options='getConditionTypeOptions'></b-form-select>
    </detail-row>
    <detail-row fixedPlayloadWidth>
      <span slot="title">Ignore Case:</span>
      <b-form-checkbox v-model='item.hasToIgnoreCase' slot="payload"></b-form-checkbox>
    </detail-row>
    <detail-row fixedPlayloadWidth>
      <span slot="title">Value:</span>
      <b-form-input v-model='item.value' type="text" slot="payload"></b-form-input>
    </detail-row>

    <template #modal-footer>
      <b-button size="sm" @click="onHide" class="text-center">Close</b-button>
      <b-button size="sm" @click='onSubmit' variant="primary">Submit</b-button>
    </template>
  </b-modal>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import { conditionType, applicationType } from '@/shared/leads/spam'

export default {
  name: 'leads-spam-create-edit-modal',
  props: {
    item: { type: Object, required: true },
    isEditMode: { type: Boolean, required: true },
    isShow: { type: Boolean, required: true }
  },
  data () {
    return {
    }
  },
  computed: {
    getTitle () {
      if (this.isEditMode) {
        return 'Edit Spam Filter'
      }

      return 'Create Spam Filter'
    },
    getConditionTypeOptions () {
      return Object.values(conditionType)
    },
    getFieldOptions () {
      let application = Object.values(applicationType).find(x => x.value === this.item.applicationType)
      return (application || {fields: []}).fields
    },
    getApplicationOptions () {
      return Object.values(applicationType).filter(x => x.value !== 0)
    }
  },
  components: {
    'detail-row': detailRow
  },
  methods: {
    onInputApplicationType () {
      let field = this.getFieldOptions[0]
      this.$set(this.item, 'fieldType', (field || {value: 0}).value)
    },
    onHide () {
      this.$emit('hide')
    },
    async onSubmit () {
      try {
        if (this.isEditMode) {
          if (this.item.applicationType === 0 || this.item.fieldType === 0 || this.item.value.trim() === '') {
            this.$toaster.error('Cannot create spam filter.')
          } else {
            await this.$store.dispatch('leads/updateSpamFilter', { id: this.item.id, data: this.item })
            this.$toaster.success('Spam Filter Successfully Updated')
            this.onHide()
          }
        } else {
          if (this.item.applicationType === 0 || this.item.fieldType === 0 || this.item.value.trim() === '') {
            this.$toaster.error('Cannot create spam filter.')
          } else {
            await this.$store.dispatch('leads/createNewSpamFilter', this.item)
            this.$toaster.success('Spam Filter Successfully Created')
            this.onHide()
          }
        }
      } catch (ex) {
        if (this.isEditMode) {
          this.$toaster.error(`Cannot update spam filter. Exception message: ${ex.message}`)
        } else {
          this.$toaster.error(`Cannot create spam filter. Exception message: ${ex.message}`)
        }
        this.$logger.handleError(ex, `Cannot create/edit spam filter. Item: ${this.item}`)
        this.onHide()
      }
    }
  }
}
</script>
