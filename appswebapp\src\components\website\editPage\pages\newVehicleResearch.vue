<template>
  <div v-if="franchiseMakesOptions">
    <page-status-section :page-edit-model="pageEditModel"/>
    <div>
      <div class="border-bottom">
        <b-row>
          <b-col class="m-0"><h6>Page Settings</h6></b-col>
        </b-row>
      </div>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Featured Make:</span>
        <b-form-select slot="payload" @input="onInputFeaturedMake" v-model="pageEditModel.navigationSettings.newVehicleSettings.makeId" text-field="make" value-field="vehicleMakeId" :options="franchiseMakesOptions"></b-form-select>
      </detail-row>
      <detail-row :large-payload-width="true">
        <span slot="title">Featured Make URL:</span>
        <div slot="payload">
          <b-link :href="getFeaturedMakeUrl">{{getFeaturedMakePath}}</b-link>
          <span>(The URL for this page cannot be changed)</span>
        </div>
      </detail-row>
    </div>
  </div>
</template>

<script>
import pageStatusSection from '../sections/pageStatusSection'
import detailRow from '@/components/details/helpers/detailRow'

export default {
  props: {
    pageEditModel: {type: Object, required: true}
  },
  data () {
    return {
      accountId: +this.$route.params.accountId,
      franchiseMakesOptions: null
    }
  },
  components: {
    pageStatusSection,
    detailRow
  },
  created () {
    this.populateFranchiseMakes()
  },
  computed: {
    getFeaturedMakePath () {
      let res = this.franchiseMakesOptions.find(x => x.vehicleMakeId === this.pageEditModel.navigationSettings.newVehicleSettings.makeId)
      if (res && res.vehicleMakeId > 0) {
        return `/${this.pageEditModel.pageSettings.pageName}-${res.make.toLowerCase()}/`
      } else if (res) {
        return `/${this.pageEditModel.pageSettings.pageName}/`
      }

      return ''
    },
    getFeaturedMakeUrl () {
      let res = this.franchiseMakesOptions.find(x => x.vehicleMakeId === this.pageEditModel.navigationSettings.newVehicleSettings.makeId)
      if (res && res.vehicleMakeId > 0) {
        return `${this.pageEditModel.navigationSettings.pageUrl}/${res.make.toLowerCase()}/`
      } else if (res) {
        return `${this.pageEditModel.navigationSettings.pageUrl}`
      }

      return '#'
    }
  },
  methods: {
    onInputFeaturedMake () {
      let res = this.franchiseMakesOptions.find(x => x.vehicleMakeId === this.pageEditModel.navigationSettings.newVehicleSettings.makeId)
      if (res && res.vehicleMakeId > 0) {
        this.pageEditModel.navigationSettings.newVehicleSettings.makeName = res.make
      } else {
        this.pageEditModel.navigationSettings.newVehicleSettings.makeName = ''
      }
    },
    populateFranchiseMakes () {
      this.$store.dispatch('accountManagement/getFranchiseMakes', { accountId: this.accountId }).then(res => {
        this.franchiseMakesOptions = [{make: 'All  Makes', vehicleMakeId: 0}].concat(res.data)
        this.pageEditModel.navigationSettings.newVehicleSettings.makeId = this.pageEditModel.navigationSettings.newVehicleSettings.makeId || 0
      }).catch(ex => {
        this.$toaster.error('Something went wrong! Please reload page')
        this.$logger.handleError(ex, 'Cannot get franchise makes')
      })
    }
  }
}
</script>
