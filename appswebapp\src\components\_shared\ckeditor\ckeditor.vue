<template>
  <div>
    <textarea name="editor" v-model="value" v-on:change="onInput" :id="id"></textarea>
  </div>
</template>

<script>

export default {
  props: {
    value: { type: String }
  },
  data () {
    return {
      ckeditor: {},
      id: this.$uuid.v4()
    }
  },
  created () {
    if (!this.value) {
      this.$emit('input', '')
    }
  },
  mounted () {
    this.initCKEditor()
  },
  methods: {
    initCKEditor () {
      if (!window.CKEDITOR) {
        this.$toaster.error('An error occurred while initializing the text editor. Please reload the page or try to open it in a different browser', {timeout: 8000})
        return
      }
      this.ckeditor = window.CKEDITOR.replace(this.id,
        {
          toolbar: 'AdvancedToolbar',
          height: '75',
          removePlugins: 'elementspath'
        })
      this.ckeditor.on('change', this.onInput)
      this.ckeditor.on('blur', this.onBlur)
    },
    onInput (event) {
      this.$emit('input', event.editor.getData())
    },
    onBlur (event) {
      this.$emit('input', event.editor.getData())
      this.$emit('blur', event.editor.getData())
    }
  }
}
</script>
