import { ObjectSchema } from './../common/objectHelpers'
import QueryStringHelper from './../common/queryStringHelper'

const getFilterManager = function (rawSchema) {
  const filterSchema = new ObjectSchema(rawSchema)
  const defaultFilter = filterSchema.getObject()
  const queryHelper = new QueryStringHelper(filterSchema)

  return {
    schema: filterSchema,
    defaultValue: defaultFilter,
    urlHelper: queryHelper
  }
}

export default {
  getFilterManager: getFilterManager
}
