const logTypes = Object.freeze({
  voice: { key: 0, label: 'Voice', description: 'Voice Communication Log Information', type: 4 },
  sms: { key: 1, label: 'SMS', description: 'SMS Communication Log Information', type: 1 },
  email: { key: 2, label: 'Email', description: 'Email Communication Log Information', type: 6 },
  gallery: { key: 3, label: 'Gallery', description: 'Gallery Log Information', type: 7 },
  eBay: { key: 4, label: 'eBay', description: 'eBay Log Information', type: 8 },
  statistic: { key: 5, label: 'Statistic', description: 'Statistic Log Information', type: 5 },
  leadsNotification: { key: 6, label: 'Leads Notification', description: 'Leads Notification Log Information', type: 0 },
  serviceLog: { key: 7, label: 'Service Log', description: 'Service Log Information', type: 3 },
  synchronization: { key: 8, label: 'Synchronization', description: 'Synchronization Log Information', type: 2 }
})

const spamTabTypes = Object.freeze({
  spamMessages: { value: 0, label: 'Spam Messages' },
  spamFilters: { value: 1, label: 'Spam Filters' }
})

const integrityReportTabTypes = Object.freeze({
  missedTwilioConversations: { value: 2, label: 'User\'s Twilio phones missed in Conversations' },
  missedTwilioCampaigns: { value: 1, label: 'Dealer\'s Twilio phones missed in Campaigns' },
  missedTwilioPhones: { value: 0, label: 'Campaigns missed on Twilio' },
  notMatchedEmails: { value: 3, label: 'Not Matched Emails' }
})

const leadType = Object.freeze({
  undefined: { value: 0, label: 'All Other' },
  email: { value: 1, label: 'Email' },
  asq: { value: 2, label: 'ASQ' },
  bid: { value: 3, label: 'Bid' },
  bestOffer: { value: 4, label: 'Best Offer' },
  phone: { value: 5, label: 'Phone' },
  tradeAppraisal: { value: 9, label: 'Trade Appraisal' },
  creditApp: { value: 10, label: 'Credit Application' },
  vehicleFinder: { value: 13, label: 'Vehicle Finder' },
  scheduleAppointmentRequest: { value: 14, label: 'Schedule Appointment Request' },
  buyItNow: { value: 15, label: 'Buy it now' },
  successfulHighBidder: { value: 16, label: 'eBay Sale' },
  declinedOffer: { value: 21, label: 'Declined Offer' },
  expiredOffer: { value: 23, label: 'Expired Offer' },
  coupons: { value: 25, label: 'Coupons' },
  requestATestDrive: { value: 27, label: 'Request A Test Drive' },
  partsRequest: { value: 29, label: 'Parts Request' },
  survey: { value: 31, label: 'Survey' },
  specialsInventory: { value: 32, label: 'Specials Inventory' },
  specialsParts: { value: 33, label: 'Specials Parts' },
  specialsService: { value: 34, label: 'Specials Service' },
  specialsOther: { value: 35, label: 'Specials Other' },
  contactUs: { value: 40, label: 'Contact Us' },
  priceAlertSubscription: { value: 44, label: 'Price Alert Subscription' },
  textRequest: { value: 45, label: 'Text Request' },
  textMessage: { value: 46, label: 'Text Message' }
})

const communicationTypes = Object.freeze({
  undefined: { value: 0, label: 'Undefined' },
  sms: { value: 2, label: 'Message' },
  voice: { value: 3, label: 'Voice' },
  email: { value: 4, label: 'Email' },
  webForm: { value: 5, label: 'Web Form' },
  eBay: { value: 6, label: 'EBay' }
})

const communicationType = Object.freeze({
  undefined: { value: 0, label: 'All types' },
  email: { value: 4, label: 'Email' },
  webForm: { value: 5, label: 'Web Form' }
})

const contactTypes = [
  { value: 0, label: 'Default' },
  { value: 1, label: 'New' },
  { value: 2, label: 'Used' },
  { value: 3, label: 'Parts' },
  { value: 4, label: 'Service' },
  { value: 5, label: 'General' }
]

const processingStatusType = [
  { key: '', label: 'All' },
  { key: 0, label: 'Waiting' },
  { key: 1, label: 'In queue' },
  { key: 2, label: 'In process' },
  { key: 3, label: 'Failed' },
  { key: 4, label: 'Completed' },
  { key: 5, label: 'Sync. Processing' },
  { key: 7, label: 'Invalid Task' },
  { key: 8, label: 'Canceled Task' }
]

const accountListingSortType = Object.freeze({
  accountIdAsc: 1,
  accountIdDesc: 2,
  accountNameAsc: 3,
  accountNameDesc: 4,
  costAsc: 5,
  costDesc: 6,
  countSmsAsc: 7,
  countSmsDesc: 8,
  countCallsAsc: 9,
  countCallsDesc: 10,
  countEmailsAsc: 11,
  countEmailsDesc: 12,
  countWebFormsAsc: 13,
  countWebFormsDesc: 14
})

const userActivitySortTypes = Object.freeze({
  accountIdAsc: 1,
  accountIdDesc: 2,
  userNameAsc: 3,
  userNameDesc: 4,
  dateAsc: 5,
  dateDesc: 6,
  accountNameAsc: 7,
  accountNameDesc: 8,
  actionAsc: 9,
  actionDesc: 10,
  userTypeAsc: 11,
  userTypeDesc: 12
})

const conversationSortTypes = Object.freeze({
  dateCreatedAsc: 1,
  dateCreatedDesc: 2,
  leadTypeAsc: 3,
  leadTypeDesc: 4,
  accountIdAsc: 5,
  accountIdDesc: 6
})

const conversationTabTypes = Object.freeze({
  leads: { value: 0, label: 'Leads' },
  archived: { value: 1, label: 'Archived' }
})

const preferredContactTypes = [
  { value: 1, text: 'Email' },
  { value: 2, text: 'Call' },
  { value: 3, text: 'Text' }
]

const notificationTypes = [
  { value: 1, text: 'User Email' },
  { value: 2, text: 'Dealer Email' },
  { value: 3, text: 'ADF Email' },
  { value: 4, text: 'Dealer Socket' },
  { value: 5, text: 'Shift Digital' },
  { value: 6, text: 'Premier Truck' },
  { value: 7, text: 'User Auto Notification' },
  { value: 8, text: 'Database' }
]

const turnstileFailureAction = Object.freeze({
  rejectOnFormSubmission: { value: 0, label: 'Reject On Form Submission' },
  acceptAndGoToSpam: { value: 1, label: 'Accept And Go To Spam' }
})

const turnstileAllowedLeadTypes = Object.freeze({
  email: { value: 1, label: 'Email' },
  tradeAppraisal: { value: 9, label: 'Trade Appraisal' },
  creditApp: { value: 10, label: 'Credit Application' },
  vehicleFinder: { value: 13, label: 'Vehicle Finder' },
  scheduleAppointmentRequest: { value: 14, label: 'Schedule Appointment Request' },
  requestATestDrive: { value: 27, label: 'Request A Test Drive' },
  partsRequest: { value: 29, label: 'Parts Request' },
  survey: { value: 31, label: 'Survey' },
  specialsInventory: { value: 32, label: 'Specials Inventory' },
  specialsParts: { value: 33, label: 'Specials Parts' },
  specialsService: { value: 34, label: 'Specials Service' },
  specialsOther: { value: 35, label: 'Specials Other' },
  contactUs: { value: 40, label: 'Contact Us' },
  textRequest: { value: 45, label: 'Text Request' }
})

const userActivityActionTypes = Object.freeze({
  undefined: {value: 0, text: 'All Actions'},
  createCommunication: {value: 1, text: 'Create Communication'},
  updateCommunication: {value: 2, text: 'Update Communication'},
  deleteCommunication: {value: 3, text: 'Delete Communication'},
  insertAccountLeadSettings: {value: 4, text: 'Insert Account Lead Settings'},
  updateAccountLeadSettings: {value: 5, text: 'Update Account Lead Settings'},
  insertUserEmailTemplate: {value: 6, text: 'Insert User Email Template'},
  updateUserEmailTemplate: {value: 7, text: 'Update User Email Template'},
  deleteUserEmailTemplate: {value: 8, text: 'Delete User Email Template'},
  deleteConversationDetails: {value: 9, text: 'Delete Conversation Details'},
  updateLeadSystemSettings: {value: 10, text: 'Update Lead System Settings'},
  reprocessSpamMessage: {value: 11, text: 'Reprocess Spam Message'},
  createSpamFilter: {value: 12, text: 'Create Spam Filter'},
  updateSpamFilter: {value: 13, text: 'Update Spam Filter'},
  deleteSpamFilter: {value: 14, text: 'Delete Spam Filter'},
  createCampaignType: {value: 15, text: 'Create Campaign Type'},
  updateCampaignType: {value: 16, text: 'Update Campaign Type'},
  deleteCampaignType: {value: 17, text: 'Delete Campaign Type'},
  moveUnmatchedCampaignType: {value: 18, text: 'Move Unmatched Campaign Type'},
  sendNotifications: {value: 19, text: 'Send Notifications'},
  sendMessage: {value: 20, text: 'Send Message'},
  sendEmail: {value: 21, text: 'Send Email'},
  updateConversationUser: {value: 22, text: 'Update Conversation User'},
  deleteConversation: {value: 23, text: 'Delete Conversation'},
  archiveConversationDetails: {value: 24, text: 'Archive Conversation Details'},
  deleteIntegrityReport: {value: 25, text: 'Delete Integrity Report'},
  refreshAccountSettingsFromLegacy: {value: 26, text: 'Refresh Account Settings From Legacy'},
  deleteSpamMessage: {value: 27, text: 'Delete Spam Message'},
  generateCreditAppPdf: {value: 28, text: 'Generate Credit App PDF'},
  reprocessUnmatchedEmail: {value: 29, text: 'Reprocess Unmatched Email'},
  runIntegrityReportProcess: {value: 30, text: 'Run Integrity Report Process'},
  createContactReportFilter: {value: 31, text: 'Create Contacts Report Filter'},
  updateContactReportFilter: {value: 32, text: 'Update Contacts Report Filter'},
  deleteContactReportFilter: {value: 33, text: 'Delete Contacts Report Filter'}
})

export {
  logTypes,
  spamTabTypes,
  processingStatusType,
  leadType,
  communicationTypes,
  communicationType,
  integrityReportTabTypes,
  accountListingSortType,
  userActivitySortTypes,
  contactTypes,
  conversationSortTypes,
  conversationTabTypes,
  preferredContactTypes,
  notificationTypes,
  userActivityActionTypes,
  turnstileFailureAction,
  turnstileAllowedLeadTypes
}
