<template>
  <div>
    <div class="d-flex flex-row">
      <h4 class="mt-3">User Activity</h4>
      <!-- Pagination -->
      <paging
        class="d-none d-md-block p-0"
        :pageNumber="filter.page"
        :pageSize="filter.pageSize"
        :totalItems="itemsTotalCount"
        @numberChanged="pageChanged"
        @changePageSize="changePageSize"
      />
    </div>
    <b-card>
      <b-form v-on:submit.prevent="applyFilter">
        <div class="form-row">
          <b-col class="my-1" sm="4" lg="4" xl="2">
            <b-form-input
              max='200'
              v-model="filter.search"
              placeholder="Search..."
              autocomplete="off"
            >
            </b-form-input>
          </b-col>

          <b-col class="my-1" sm="4" lg="4" xl="2">
            <b-input-group class="flex-nowrap">
              <b-input-group-prepend is-text>
                <i class="ion ion-md-calendar" slot="prepend"></i>
              </b-input-group-prepend>
              <date-time-picker
                ref="timeFrom"
                v-model="filter.dateFrom"
                :options="filterTimeOptions"
                format="MM/DD/YYYY HH:mm"
                placeholder="Date From"
                className="form-control"
                @change="onTimeFromInputChange"
              />
              <b-input-group-append
                is-text
                v-show="filter.dateFrom"
                @click="filter.dateFrom = null"
              >
                <i class="ion ion-md-close"></i>
              </b-input-group-append>
            </b-input-group>
          </b-col>

          <b-col class="my-1" sm="4" lg="4" xl="2">
            <b-input-group class="flex-nowrap">
              <b-input-group-prepend is-text>
                <i class="ion ion-md-calendar" slot="prepend"></i>
              </b-input-group-prepend>
              <date-time-picker
                ref="timeTo"
                v-model="filter.dateTo"
                :options="filterTimeOptions"
                format="MM/DD/YYYY HH:mm"
                placeholder="Date To"
                className="form-control"
                @change="onTimeToInputChange"
              />
              <b-input-group-append
                is-text
                v-show="filter.dateTo"
                @click="filter.dateTo = null"
              >
                <i class="ion ion-md-close"></i>
              </b-input-group-append>
            </b-input-group>
          </b-col>
          <b-col class="my-1" sm="4" lg="4" xl="2">
            <b-form-select v-model="filter.action" :options="getActionOptions"></b-form-select>
          </b-col>
          <b-col class="my-1" sm="4" lg="4" xl="2">
            <multiSelectWithCheckboxes
              v-model='selectedUserTypes'
              :options='getUserTypeOptions'
              name="User Types"
              customMessageOfNoneSelectedItems="All User Types"
            ></multiSelectWithCheckboxes>
          </b-col>
          <b-col class="my-1" sm="4" lg="4" xl="2">
            <b-btn block  variant="primary" type="submit">Submit</b-btn>
          </b-col>
        </div>
      </b-form>
    </b-card>

    <b-card v-if="!isLoading">
      <b-table
        :items="items"
        :fields="tableFields"
        :sort-by="tableSortBy"
        :sort-desc="tableSortDesc"
        @sort-changed="onSortChanged"
        :no-sort-reset="true"
        :no-local-sorting="true"
        striped
        show-empty
        hover
        responsive>
        <template #cell(manage)="data">
          <b-btn size="sm" @click="showDetails(data)">{{ data.detailsShowing ? 'Hide' : 'Show' }} Details</b-btn>
          <a class="btn btn-outline-secondary btn-sm" :href='getLogDetailsPath(data.item.id)' target="_blank">
            <i class="ion ion-md-open"></i>
          </a>
        </template>
        <template #row-details="data">
          <b-card>
            <log-node
              :data="data.item.nodes"
              :isExpandedShallow="true"
              :isExpandedDeep="false"
            />
          </b-card>
        </template>
      </b-table>
      <paging
          :pageNumber="filter.page"
          :pageSize="filter.pageSize"
          :totalItems="itemsTotalCount"
          titled
          pageSizeSelector
          @numberChanged="pageChanged"
          @changePageSize="changePageSize"
        />
    </b-card>
    <div v-else>
      <loader size="lg"/>
    </div>
  </div>
</template>

<script>
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import Constants from '@/shared/systemTools/constants'
import {userTypes} from '@/shared/users/constants'
import moment from 'moment'

const defaultFilters = new ObjectSchema({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  search: { type: String, default: '' },
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  sort: { type: Number, default: Constants.userActivitySortTypes.dateDesc },
  action: { type: Number, default: 0 },
  userTypes: { type: String, default: `${userTypes.cpUser.value},${userTypes.adminUser.value}` }
})
const queryHelper = new QueryStringHelper(defaultFilters)

export default {
  name: 'user-activity',
  metaInfo: {
    title: 'User Activity'
  },
  components: {
    'paging': () => import('@/components/_shared/paging'),
    'date-time-picker': () => import('@gravitano/vue-date-range-picker/src/components/DateRangePicker'),
    'log-node': () => import('@/components/_shared/logItemNode.vue'),
    'loader': () => import('@/components/_shared/loader.vue'),
    'multiSelectWithCheckboxes': () => import('@/components/_shared/multiSelectWithCheckboxes.vue')
  },
  data () {
    return {
      items: [],
      itemsTotalCount: 0,
      isLoading: true,
      filter: defaultFilters.getObject(),
      filterTimeOptions: {
        autoUpdateInput: false,
        singleDatePicker: true,
        timePicker: true,
        timePicker24Hour: true,
        maxDate: new Date()
      }
    }
  },
  created () {
    this.filter = queryHelper.parseQueryStringToObject(this.$router)
    this.loadContent()
  },
  computed: {
    selectedUserTypes: {
      get () {
        return this.getIntegerArrayFromString(this.filter.userTypes)
      },
      set (value) {
        this.filter.userTypes = value ? value.join() : ''
      }
    },
    tableFields () {
      return [
        {
          key: 'requestInformation.userName',
          sortable: true,
          sortTypeAsc: Constants.userActivitySortTypes.userNameAsc,
          sortTypeDesc: Constants.userActivitySortTypes.userNameDesc,
          label: 'User Name',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'requestInformation.user.userType',
          sortable: true,
          sortTypeAsc: Constants.userActivitySortTypes.userTypeAsc,
          sortTypeDesc: Constants.userActivitySortTypes.userTypeDesc,
          label: 'User Type',
          tdClass: 'py-2 align-middle',
          formatter: val => (Object.values(userTypes).find(x => x.value === val) || {text: '-'}).text
        },
        {
          key: 'action',
          sortable: true,
          sortTypeAsc: Constants.userActivitySortTypes.actionAsc,
          sortTypeDesc: Constants.userActivitySortTypes.actionDesc,
          label: 'Action Name',
          tdClass: 'py-2 align-middle',
          formatter: value => {
            let action = Object.values(Constants.userActionTypes).find(x => x.value === value)
            return action ? action.text : '-'
          }
        },
        {
          key: 'requestInformation.clientIpAddress',
          label: 'IP Address',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: Constants.userActivitySortTypes.ipAddressAsc,
          sortTypeDesc: Constants.userActivitySortTypes.ipAddressDesc
        },
        {
          key: 'requestInformation.callDateTime',
          sortable: true,
          sortTypeAsc: Constants.userActivitySortTypes.dateAsc,
          sortTypeDesc: Constants.userActivitySortTypes.dateDesc,
          label: 'Date',
          tdClass: 'py-2 align-middle',
          formatter: value => {
            return moment(value).format('MM/DD/YYYY hh:mm:ss A')
          }
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    getActionOptions () {
      return Object.values(Constants.userActionTypes)
    },
    getUserTypeOptions () {
      return Object.values(userTypes)
    },
    refDateTimeFrom () {
      return (this.$refs.timeFrom || {}).$el || {}
    },
    refDateTimeTo () {
      return (this.$refs.timeTo || {}).$el || {}
    },
    sortType () {
      return this.filter.sort
    },
    tableSortBy () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sortType
      } else {
        return false
      }
    }
  },
  methods: {
    loadContent () {
      this.$store.dispatch('systemTools/getUserActivity', {filter: this.filter}).then(x => {
        this.items = x.data.items
        this.itemsTotalCount = x.data.totalItemsCount
      }).catch(ex => {
        this.$toaster.error('Something went wrong!', {timeout: 7000})
        this.$logger.handleError(ex, 'Can\'t get system tools user activity listing', {filter: this.filter})
      }).finally(() => {
        this.isLoading = false
      })
    },
    showDetails (data) {
      if (data.item.nodes) {
        data.toggleDetails()
      } else {
        this.$store.dispatch('systemTools/getUserActivityLogDetails', { id: data.item.id }).then(res => {
          data.item.nodes = { nodes: res.data.details }
          data.toggleDetails()
        }).catch(ex => {
          this.$toaster.error('Something went wrong!')
          this.$logger.handleError(ex, 'Cannot get system tools user activity log details')
        })
      }
    },
    pageChanged (newPage) {
      this.filter.page = newPage
      this.isLoading = true
      this.synchronizeUrlAndReload()
    },
    changePageSize (newSize) {
      this.filter.pageSize = newSize
      this.filter.page = 1
      this.isLoading = true
      this.synchronizeUrlAndReload()
    },
    onSortChanged (value) {
      const sortingColumn = this.tableFields.find(x => x.key === value.sortBy)
      if (value.sortDesc) {
        this.filter.sort = sortingColumn.sortTypeDesc
      } else {
        this.filter.sort = sortingColumn.sortTypeAsc
      }
      this.synchronizeUrlAndReload()
    },
    onTimeFromInputChange (newVal) {
      this.refDateTimeFrom.value = newVal || this.filter.dateFrom || null
    },
    onTimeToInputChange (newVal) {
      this.refDateTimeTo.value = newVal || this.filter.dateTo || null
    },
    applyFilter () {
      this.filter.search = (this.filter.search || '').trim()
      this.filter.page = 1
      this.isLoading = true
      this.synchronizeUrlAndReload()
    },
    synchronizeUrlAndReload () {
      queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.loadContent()
    },
    getIntegerArrayFromString (str) {
      if (!str || !str.trim()) {
        return []
      }
      let array = []
      for (let numberStr of str.split(',')) {
        let number = parseInt(numberStr)
        if (!isNaN(number)) {
          array.push(number)
        }
      }
      return array
    },
    getLogDetailsPath (id) {
      return `/system/useractivity/${id}/details`
    }
  }
}
</script>

<style lang="scss">
.change-marging {
  margin-right: 0.25rem;
  margin-left: 0.25rem;
}
</style>
