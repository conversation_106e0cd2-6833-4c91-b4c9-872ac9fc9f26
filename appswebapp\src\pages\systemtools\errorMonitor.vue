<template>
  <div>
    <!-- Filters -->
    <h4>Error Monitor</h4>
    <b-card no-body class="px-3 pt-2">
      <b-row class="form-row error-monitor-filters">
        <b-col id="categories" sm="12" md="6" lg class="d-flex border-right">
          <b-form-group class="mb-0" label="Categories:" label-class="pt-1 mb-2">
            <b-form-checkbox
              v-for="option in categoriesOptions"
              v-model="errorCategories"
              :key="option.value"
              :value="option.value"
              inline
              :disabled="errorCategories.length === 1 && errorCategories.includes(option.value)"
            >
              {{ option.text }}
            </b-form-checkbox>
          </b-form-group>
        </b-col>

        <b-col v-if="!isProduction" id="levels" sm="12" md="6" lg class="d-flex pl-3">
          <b-form-group class="mb-0" label="Levels:" label-class="pt-1 mb-2">
            <b-form-checkbox
              v-for="option in levelsOptions"
              v-model="errorLevels"
              :key="option.value"
              :value="option.value"
              inline
            >
              {{ option.text }}
            </b-form-checkbox>
          </b-form-group>
        </b-col>

        <b-col id="groups" sm="12" md="6" lg class="d-flex justify-content-center" :class="{ 'pl-xl-4': isProduction}">
          <b-form-group class="mb-0 w-100" label="Groups:" v-if="isErrorGroupsLoaded">
            <multiselect
              v-model="errorGroups"
              :options='groupsOptions'
              :multiple="true"
              :show-labels="false"
              :custom-label="customMultiselectLabel"
              :close-on-select="false"
              :clear-on-select="false"
              track-by="id"
              :searchable="true"
              placeholder='Search'
              class="error-monitor-multiselect"
              @select="onGroupSelect"
              @remove="onGroupRemove"
            >
              <template slot="tag">{{''}}</template>
              <template slot="selection">
                <span class="multiselect__single" style="width:100%">{{multiselectTitle(this.errorGroups.length, this.groupsOptions.length)}}</span>
              </template>
              <template slot="beforeList">
                <b-btn variant="danger" class="w-100 p-0">
                  <span class="filter-selector px-2" v-on:click="selectAllGroups()"><i class="ion ion-md-checkmark"></i> All</span>
                  <span v-if="!isErrorGroupsDefault" class="filter-selector px-4" v-on:click="selectOnlyDefaultGroups()"><i class="ion ion-md-checkmark"></i> Default</span>
                  <span class="filter-selector px-2" v-on:click="deselectAllGroups()"><i class="ion ion-md-close"></i> None</span>
                </b-btn>
              </template>
              <template slot="option" slot-scope="scope">
                <b-form-checkbox
                  v-model="scope.option.checked"
                  disabled
                >
                  {{scope.option.text}}
                </b-form-checkbox>
              </template>
            </multiselect>
          </b-form-group>
          <loader v-else-if="!isErrorGroupsLoaded" class="pt-4" size="sm"/>
        </b-col>

        <b-col id="applications" sm="12" md="6" lg class="d-flex justify-content-center">
          <b-form-group class="mb-0 w-100" label="Applications:" v-if="isApplicationLocationsLoaded">
            <multiselect
              v-model="errorApplications"
              :options='applicationsOptions'
              :multiple="true"
              :show-labels="false"
              :custom-label="customMultiselectLabel"
              :close-on-select="false"
              :clear-on-select="false"
              track-by="id"
              :searchable="true"
              placeholder='Search'
              class="error-monitor-multiselect"
              @select="onApplicationSelect"
              @remove="onApplicationRemove"
            >
              <template slot="tag">{{''}}</template>
              <template slot="selection">
                <span class="multiselect__single" style="width:100%">{{multiselectTitle(this.errorApplications.length, this.errorApplicationsOptions.length)}}</span>
              </template>
              <template slot="beforeList">
                <b-btn variant="danger" class="w-100 p-0">
                  <span class="filter-selector px-3" v-on:click="selectAllApplicationLocations()"><i class="ion ion-md-checkmark"></i> All</span>
                  <span class="filter-selector px-3" v-on:click="deselectAllApplicationLocations()"><i class="ion ion-md-close"></i> None</span>
                </b-btn>
              </template>
              <template slot="option" slot-scope="scope">
                <b-form-checkbox
                  v-model="scope.option.checked"
                  disabled
                >
                  {{scope.option.text}}
                </b-form-checkbox>
              </template>
            </multiselect>
          </b-form-group>
          <loader v-else-if="!isApplicationLocationsLoaded" class="pt-4" size="sm"/>
        </b-col>

        <b-col id="servers" sm="12" md="6" lg class="d-flex justify-content-center">
          <b-form-group class="mb-0 w-100" label="Servers:" v-if="isApplicationLocationsLoaded">
            <multiselect
              v-model="errorServers"
              :options='serversOptions'
              :multiple="true"
              :show-labels="false"
              :custom-label="customMultiselectLabel"
              :close-on-select="false"
              :clear-on-select="false"
              track-by="id"
              :searchable="true"
              placeholder='Search'
              class="error-monitor-multiselect"
              @select="onServerSelect"
              @remove="onServerRemove"
            >
              <template slot="tag">{{''}}</template>
              <template slot="selection">
                <span class="multiselect__single" style="width:100%">{{multiselectTitle(this.errorServers.length, this.errorServersOptions.length)}}</span>
              </template>
              <template slot="beforeList">
                <b-btn variant="danger" class="w-100 p-0">
                  <span class="filter-selector px-3" v-on:click="selectAllApplicationLocations()"><i class="ion ion-md-checkmark"></i> All</span>
                  <span class="filter-selector px-3" v-on:click="deselectAllApplicationLocations()"><i class="ion ion-md-close"></i> None</span>
                </b-btn>
              </template>
              <template slot="option" slot-scope="scope">
                <b-form-checkbox
                  v-model="scope.option.checked"
                  disabled
                >
                  {{scope.option.text}}
                </b-form-checkbox>
              </template>
            </multiselect>
          </b-form-group>
          <loader v-else-if="!isApplicationLocationsLoaded" class="pt-4" size="sm"/>
        </b-col>

        <b-col id="dates" sm="12 pt-3" md="6" lg class="auto d-flex px-0 pb-1 align-items-end justify-content-center">
          <b-form-group class="mb-0">
            <range-selector
              @input="onDateRangeChanged"
              :value="dateRange"
              class="button-col"
            />
          </b-form-group>
        </b-col>
      </b-row>
      <!-- / Errors Data -->
      <b-row class="mx-0">
        <b-col md="12 pt-2" lg="4" class="d-flex align-items-center justify-content-between">
          <div class="text-nowrap">
            <span><b>Total Errors: {{errorsTotal}}</b></span>
          </div>
          <b-form-input class="w-50" @keyup.enter.native="onSearch()" v-model="filters.search" placeholder="Search in body"></b-form-input>
        </b-col>
        <b-col md="12" lg="4" class="d-flex align-items-center justify-content-center my-3">
          <b-btn :disabled="!isApplicationLocationsLoaded || !isGroupsPresent" variant="primary" type="submit" v-on:click="onApplyChanges()">Apply changes</b-btn>
        </b-col>
        <b-col md="12" lg="4" class="d-flex align-items-center justify-content-between">
          <!-- / Pagination -->
          <paging
            :totalItems="errorsTotal"
            :pageNumber="filters.page"
            :pageSize="filters.pageSize"
            @numberChanged="onPageChanged"
          />
        </b-col>
      </b-row>
    </b-card>
    <!-- Listing -->
    <b-card no-body class="px-3" >
      <div v-if="(isErrorsDataLoaded || isRescheduleLoading) && errorsTotal > 0">
        <!-- Table -->
        <div class="table-responsive">
          <b-table
            class="errors-table card-table"
            striped
            responsive
            small
            :items="errorListing"
            :fields="tableFields"
            @row-clicked="onTableRowClicked"
            @row-middle-clicked="onTableRowMiddleClicked"
          >
          </b-table>
        </div>
        <!-- / Pagination -->
        <b-row class="mx-0">
          <b-col></b-col><b-col></b-col>
          <b-col>
            <paging
              :totalItems="errorsTotal"
              :pageNumber="filters.page"
              :pageSize="filters.pageSize"
              pageSizeSelector
              @numberChanged="onPageChanged"
              @changePageSize="onPageSizeChanged"
            />
          </b-col>
        </b-row>
      </div>
      <div v-if="!isErrorsDataLoaded && !isRescheduleLoading">
        <loader class="py-5" size="md"/>
      </div>
      <h5 class="py-3 mb-0" v-else-if="isErrorsDataLoaded && errorsTotal == 0">
        No errors found.
      </h5>
    </b-card>
    <b-modal
      id="error-details-modal"
      :visible="isErrorDetailsModalOpen"
      size="xl"
      lazy
      ok-title="Details"
      @ok="onModalDetailsOk"
      cancel-title="Close"
      @hide="onHideErrorDetailsModal"
      body-class="p-0"
      header-class="p-3"
      scrollable
    >
      <template #modal-header>
        <div class="mx-auto text-center" v-if="errorIsFetched">
          <router-link @click.native="onHideErrorDetailsModal()" class="error-block" :to="{name: 'error-details', params: {errorId: detailsError.id}}" target='_blank'>
            <h4 class="mb-0">Error body View for ErrorID {{detailsError.id}}
              <br>Category - {{detailsError.errorCategoryLabel}}
              <br>Error level - {{detailsError.errorLevelLabel}}
            </h4>
          </router-link>
        </div>
        <b-btn size="sm" @click="onHideErrorDetailsModal()" variant="primary">X</b-btn>
      </template>
      <div class="p-3">
        <loader v-if="!errorIsFetched" size="sm" />
        <div v-if="errorIsFetched">
          <b-table
            table-variant="secondary"
            :items="[detailsError]"
            :fields="getDetailsErrorField"
            bordered
            responsive
          >
          </b-table>
          <div v-html="detailsError.body"/>
        </div>
      </div>
    </b-modal>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { errorCategories, errorLevels, errorMonitorTimezone } from '@/shared/errorReport/constants'
import { ObjectSchema } from '../../shared/common/objectHelpers'
import moment from 'moment-timezone'

const defaultFilters = new ObjectSchema({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 50 },
  search: { type: String, default: '' }
})

export default {
  name: 'error-monitor',
  metaInfo: {
    title: 'Error Monitor'
  },
  components: {
    'paging': () => import('@/components/_shared/paging.vue'),
    'multiselect': () => import('vue-multiselect'),
    'loader': () => import('@/components/_shared/loader'),
    'range-selector': () => import('@/components/errors/rangeSelector/errorMonitorRangeSelector')
  },
  data () {
    return {
      filters: defaultFilters.getObject(),
      errorCategories: Object.values(errorCategories).map(x => x.value),
      errorLevels: [],
      errorGroups: [],
      errorGroupsOptions: [],
      isErrorGroupsLoaded: false,
      isErrorGroupsDefault: false,
      applicationLocations: [],
      errorApplications: [],
      errorApplicationsOptions: [],
      errorServers: [],
      errorServersOptions: [],
      isApplicationLocationsLoaded: false,
      errorDateTimeFrom: moment().tz(errorMonitorTimezone),
      errorDateTimeTo: moment().tz(errorMonitorTimezone),
      errorsData: [],
      isErrorsDataLoaded: false,
      isRescheduleLoading: false,
      isReadCookiesOnce: false,
      timeout: {},
      isErrorDetailsModalOpen: false,
      detailsErrorId: '',
      detailsError: {}
    }
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {
        canManageAccountApplicationType: () => false
      }
    },
    categoriesOptions () {
      return errorCategories
    },
    levelsOptions () {
      if (this.isProduction) {
        return []
      }

      return [errorLevels.sandbox, errorLevels.dev]
    },
    groupsOptions () {
      return this.errorGroupsOptions
    },
    isGroupsPresent () {
      return this.errorGroups.length > 0
    },
    applicationsOptions () {
      return this.errorApplicationsOptions
    },
    serversOptions () {
      return this.errorServersOptions
    },
    isApplicationLocationsPresent () {
      return this.errorApplications.length > 0 && this.errorServers.length > 0
    },
    dateRange () {
      if ((this || {}).errorDateTimeFrom && (this || {}).errorDateTimeTo) {
        return [this.errorDateTimeFrom, this.errorDateTimeTo]
      }

      return null
    },
    errorListing () {
      return this.errorsData.errorListing
    },
    errorsTotal () {
      return this.errorsData.errorsTotal
    },
    isProduction () {
      return process.env.NODE_ENV === 'production'
    },
    tableFields () {
      return [
        {
          isActive: true,
          key: 'errorCategoryLabel',
          label: 'Category',
          tdClass: 'py-2 align-middle'
        },
        {
          isActive: true,
          key: 'subject',
          label: 'Subject',
          tdAttr: (_, __, { subject }) => ({
            title: subject
          }),
          tdClass: 'py-2 align-middle error-subject-cell text-truncate overflow-hidden pr-4'
        },
        {
          isActive: true,
          key: 'applicationName',
          label: 'ApplicationName',
          tdClass: 'py-2 align-middle'
        },
        {
          isActive: !this.isProduction,
          key: 'errorLevelLabel',
          label: 'Level',
          tdClass: 'py-2 align-middle'
        },
        {
          isActive: true,
          key: 'machineName',
          label: 'MachineName',
          tdClass: 'py-2 align-middle'
        },
        {
          isActive: true,
          key: 'clientIpAddress',
          label: 'ClientIpAddress',
          tdClass: 'py-2 align-middle text-break client-api-address-cell'
        },
        {
          isActive: true,
          key: 'errorLocalDateTime',
          label: 'ErrorDateTime',
          tdClass: 'py-2 align-middle'
        },
        {
          isActive: true,
          key: 'requestInsertedDateTime',
          label: 'RequestInserted',
          tdClass: 'py-2 align-middle'
        }
      ].filter(x => x.isActive)
    },
    errorIsFetched () {
      return this.detailsError.body !== undefined
    },
    getDetailsErrorField () {
      return [
        {
          key: 'applicationName',
          label: 'Application Name'
        },
        {
          key: 'machineName',
          label: 'Machine Name'
        },
        {
          key: 'clientIpAddress',
          label: 'Client Ip Address'
        },
        {
          key: 'errorLocalDateTime',
          label: 'Error DateTime',
          formatter: value => this.formatDate(value)
        },
        {
          key: 'requestInsertedDateTime',
          label: 'Date Inserted',
          formatter: value => this.formatDate(value)
        }
      ]
    }
  },
  methods: {
    multiselectTitle (selectedCount, count) {
      if (selectedCount && count) {
        return selectedCount + ' of ' + count + ' selected'
      } else {
        return '0 of ' + count + ' selected'
      }
    },
    customMultiselectLabel ({id, text}) {
      if (text) {
        return `${id} - ${text}`
      }
    },
    formatDate (str) {
      return moment(str).tz(errorMonitorTimezone).format('MM/DD/YYYY hh:mm:ss A')
    },
    setCookies () {
      if (this.isReadCookiesOnce) {
        var cookie = ['errorMonitor_' + this.user.userId, '=', JSON.stringify({
          categories: this.errorCategories,
          pageSize: this.filters.pageSize,
          groups: this.errorGroups
        })].join('')
        document.cookie = cookie
      }
    },
    readCookies () {
      var result = document.cookie.match(new RegExp('errorMonitor_' + this.user.userId + '=([^;]+)'))
      this.isReadCookiesOnce = true
      if (result) {
        var parsedResult = JSON.parse(result[1])
        if (parsedResult.categories !== undefined) {
          this.errorCategories = parsedResult.categories
        }
        if (parsedResult.pageSize !== undefined) {
          this.filters.pageSize = parsedResult.pageSize
        }
        if (parsedResult.groups !== undefined) {
          this.errorGroups = parsedResult.groups
          this.errorGroupsOptions = this.errorGroupsOptions.map(groupOption => ({id: groupOption.id, text: groupOption.text, checked: this.errorGroups.some(group => group.id === groupOption.id && group.checked)}))
        }
      } else {
        this.setCookies()
        this.getErrorsApplicationLocations()
      }
    },
    async getErrorGroups () {
      try {
        this.isErrorGroupsLoaded = false
        let res = await this.$store.dispatch('systemTools/getErrorGroups')
        let responseModel = res.data.model || []
        this.errorGroupsOptions = responseModel.map(group => ({id: group.id, text: group.name, checked: group.isSelectedByDefault}))
        this.errorGroups = responseModel.filter(group => group.isSelectedByDefault).map(selectedGroup => ({id: selectedGroup.id, text: selectedGroup.name, checked: true}))
      } catch (ex) {
        this.$logger.handleError(ex, 'Cannot get error groups from api')
      } finally {
        this.isErrorGroupsLoaded = true
        this.isErrorGroupsDefault = true
      }
    },
    async getErrorsApplicationLocations () {
      try {
        this.isApplicationLocationsLoaded = false
        let dateFrom = moment(this.errorDateTimeFrom)
        let dateTo = moment(this.errorDateTimeTo)
        let requestModel = {
          categories: this.errorCategories.join(','),
          levels: this.errorLevels.join(','),
          dateFrom: moment().tz(errorMonitorTimezone).set({'year': dateFrom.year(), 'month': dateFrom.month(), 'date': dateFrom.date(), 'hour': 0, 'minute': 0, 'second': 0, 'millisecond': 0}).format(),
          dateTo: moment().tz(errorMonitorTimezone).set({'year': dateTo.year(), 'month': dateTo.month(), 'date': dateTo.date(), 'hour': 23, 'minute': 59, 'second': 59, 'millisecond': 999}).format()
        }
        let res = await this.$store.dispatch('systemTools/getErrorsApplicationLocations', requestModel)
        let responseModel = res.data.model || {applications: [], machines: [], applicationLocations: []}
        this.applicationLocations = responseModel.applicationLocations
        this.errorApplicationsOptions = responseModel.applications.map(application => ({id: application.toLowerCase(), text: application, checked: true})).sort((x, y) => (x.id > y.id) ? 1 : ((y.id > x.id) ? -1 : 0))
        this.errorApplications = this.errorApplicationsOptions
        this.errorServersOptions = responseModel.machines.map(machine => ({id: machine.toLowerCase(), text: machine, checked: true})).sort((x, y) => (x.id > y.id) ? 1 : ((y.id > x.id) ? -1 : 0))
        this.errorServers = this.errorServersOptions
      } catch (ex) {
        this.$logger.handleError(ex, 'Cannot get errors applications locations from api')
      } finally {
        this.isApplicationLocationsLoaded = true
      }
    },
    async getFilters () {
      await this.getErrorGroups()
      this.readCookies()
    },
    async getErrorsData (isRescheduleLoading) {
      clearTimeout(this.timeout)
      try {
        this.isRescheduleLoading = isRescheduleLoading
        this.isErrorsDataLoaded = false
        if (this.errorServers.length === this.errorServersOptions.length && this.errorApplications.length === this.errorApplicationsOptions.length) {
          await this.getErrorsApplicationLocations()
        } else if (this.errorApplicationsOptions.some(application => !application.checked) || this.errorServersOptions.some(server => !server.checked)) {
          let applicationsOptionsNotSelected = this.errorApplicationsOptions.filter(applicationOption => !applicationOption.checked)
          let serversOptionsNotSelected = this.errorServersOptions.filter(serverOption => !serverOption.checked)
          await this.getErrorsApplicationLocations()
          this.errorApplicationsOptions = this.errorApplicationsOptions.map(x => ({id: x.id, text: x.text, checked: !applicationsOptionsNotSelected.some(y => y.id === x.id)}))
          this.errorApplications = this.errorApplications.filter(x => !applicationsOptionsNotSelected.some(y => y.id === x.id))
          this.errorServersOptions = this.errorServersOptions.map(x => ({id: x.id, text: x.text, checked: !serversOptionsNotSelected.some(y => y.id === x.id)}))
          this.errorServers = this.errorServers.filter(x => !serversOptionsNotSelected.some(y => y.id === x.id))
        }
        let dateFrom = moment(this.errorDateTimeFrom)
        let dateTo = moment(this.errorDateTimeTo)
        let requestModel = {
          Categories: this.errorCategories,
          Levels: this.errorLevels,
          Groups: this.errorGroups.map(x => x.id),
          ApplicationNames: this.errorApplications.map(x => x.text),
          MachineNames: this.errorServers.map(x => x.text),
          DateTimeFrom: moment().tz(errorMonitorTimezone).set({'year': dateFrom.year(), 'month': dateFrom.month(), 'date': dateFrom.date(), 'hour': 0, 'minute': 0, 'second': 0, 'millisecond': 0}).format(),
          DateTimeTo: moment().tz(errorMonitorTimezone).set({'year': dateTo.year(), 'month': dateTo.month(), 'date': dateTo.date(), 'hour': 23, 'minute': 59, 'second': 59, 'millisecond': 999}).format(),
          Search: this.filters.search.trim() || '',
          Page: this.filters.page,
          PageSize: this.filters.pageSize
        }
        let res = await this.$store.dispatch('systemTools/getErrors', requestModel)
        this.errorsData = res.data.model || { errorListing: [], errorsTotal: 0 }
        this.errorsData.errorListing.forEach(errorMessage => {
          if (moment(errorMessage.requestInsertedDateTime).utc().isAfter(moment().utc().subtract(1, 'hours'))) {
            errorMessage._rowVariant = 'font-weight-bolder'
          }
          errorMessage.errorLocalDateTime = this.formatDate(errorMessage.errorLocalDateTime)
          errorMessage.requestInsertedDateTime = this.formatDate(errorMessage.requestInsertedDateTime)
        })
      } catch (ex) {
        this.$logger.handleError(ex, 'Cannot get errors data from api')
      } finally {
        this.isErrorsDataLoaded = true
        this.isRescheduleLoading = false
        this.timeout = setTimeout(() => (this.getErrorsData(true)), 30000)
      }
    },
    async loadContent () {
      await this.getFilters()
      this.onPageChanged(1)
    },
    onSearch () {
      if (this.isApplicationLocationsLoaded && this.isApplicationLocationsPresent && this.isGroupsPresent) {
        this.onPageChanged(1)
      }
    },
    onPageChanged (newPage) {
      this.filters.page = newPage
      this.getErrorsData(false)
    },
    onApplyChanges () {
      this.setCookies()
      this.onPageChanged(1)
    },
    onPageSizeChanged (newSize) {
      this.filters.pageSize = newSize
      this.setCookies()
      this.onPageChanged(1)
    },
    async onDateRangeChanged (rangeInfo) {
      if (moment(this.errorDateTimeFrom).format('MM/DD/YYYY') === rangeInfo.range[0] && moment(this.errorDateTimeTo).format('MM/DD/YYYY') === rangeInfo.range[1]) {
        return
      }
      this.errorDateTimeFrom = new Date(rangeInfo.range[0])
      this.errorDateTimeTo = new Date(rangeInfo.range[1])
      await this.getErrorsApplicationLocations()
      this.onPageChanged(1)
    },
    onGroupSelect (option) {
      option.checked = true
      this.isErrorGroupsDefault = false
    },
    onGroupRemove (option) {
      option.checked = false
      this.isErrorGroupsDefault = false
    },
    selectAllGroups () {
      this.isErrorGroupsDefault = false
      this.errorGroupsOptions.forEach(groupOption => {
        groupOption.checked = true
      })
      this.errorGroups = this.errorGroupsOptions
    },
    selectOnlyDefaultGroups () {
      this.getErrorGroups()
    },
    deselectAllGroups () {
      this.isErrorGroupsDefault = false
      this.errorGroupsOptions.forEach(groupOption => {
        groupOption.checked = false
      })
      this.errorGroups = []
    },
    changeServerOption (machineName, checked) {
      if (checked) {
        if (this.errorServers.filter(x => x.text === machineName).length <= 0) {
          this.errorServers.push(this.errorServersOptions.filter(server => server.text === machineName)[0])
          this.errorServersOptions.filter(server => server.text === machineName)[0].checked = checked
        }
      } else {
        this.errorServers = this.errorServers.filter(server => server.text !== machineName)
        this.errorServersOptions.filter(server => server.text === machineName)[0].checked = checked
      }
    },
    isApplicationChecked (applicationName) {
      return this.errorApplicationsOptions.some(x => x.text === applicationName && x.checked)
    },
    checkServers (applicationOption, checked) {
      this.applicationLocations.forEach(x => {
        if (applicationOption.text === x.applicationName) {
          let applicationsOnServer = this.applicationLocations.filter(y => y.machineName === x.machineName).map(z => ({applicationName: z.applicationName, checked: this.isApplicationChecked(z.applicationName)}))
          if (applicationsOnServer.length === 0) {
            this.changeServerOption(x.machineName, checked)
          } else if (applicationsOnServer.length > 0) {
            if (!checked) {
              if (applicationsOnServer.some(application => application.checked)) {
                return true
              } else {
                this.changeServerOption(x.machineName, checked)
              }
            } else {
              this.changeServerOption(x.machineName, checked)
            }
          }
        }
      })
    },
    onApplicationSelect (applicationOption) {
      applicationOption.checked = true
      this.checkServers(applicationOption, true)
    },
    onApplicationRemove (applicationOption) {
      applicationOption.checked = false
      this.checkServers(applicationOption, false)
    },
    changeApplicationOption (applicationName, checked) {
      if (checked) {
        if (this.errorApplications.filter(x => x.text === applicationName).length <= 0) {
          this.errorApplications.push(this.errorApplicationsOptions.filter(application => application.text === applicationName)[0])
          this.errorApplicationsOptions.filter(application => application.text === applicationName)[0].checked = checked
        }
      } else {
        this.errorApplications = this.errorApplications.filter(application => application.text !== applicationName)
        this.errorApplicationsOptions.filter(application => application.text === applicationName)[0].checked = checked
      }
    },
    isServerChecked (machineName) {
      return this.errorServersOptions.some(x => x.text === machineName && x.checked)
    },
    checkApplications (serverOption, checked) {
      this.applicationLocations.forEach(x => {
        if (serverOption.text === x.machineName) {
          let serversWithApplication = this.applicationLocations.filter(y => y.applicationName === x.applicationName).map(z => ({machineName: z.machineName, checked: this.isServerChecked(z.machineName)}))
          if (serversWithApplication.length === 0) {
            this.changeApplicationOption(x.applicationName, checked)
          } else if (serversWithApplication.length > 0) {
            if (!checked) {
              if (serversWithApplication.some(server => server.checked)) {
                return true
              } else {
                this.changeApplicationOption(x.applicationName, checked)
              }
            } else {
              this.changeApplicationOption(x.applicationName, checked)
            }
          }
        }
      })
    },
    onServerSelect (serverOption) {
      serverOption.checked = true
      this.checkApplications(serverOption, true)
    },
    onServerRemove (serverOption) {
      serverOption.checked = false
      this.checkApplications(serverOption, false)
    },
    selectAllApplicationLocations () {
      this.errorApplicationsOptions.forEach(applicationOption => {
        applicationOption.checked = true
      })
      this.errorApplications = this.errorApplicationsOptions
      this.errorServersOptions.forEach(serverOption => {
        serverOption.checked = true
      })
      this.errorServers = this.errorServersOptions
    },
    deselectAllApplicationLocations () {
      this.errorApplicationsOptions.forEach(applicationOption => {
        applicationOption.checked = false
      })
      this.errorApplications = []
      this.errorServersOptions.forEach(serverOption => {
        serverOption.checked = false
      })
      this.errorServers = []
    },
    onTableRowClicked (item) {
      this.detailsErrorId = item.id
      this.getErrorDetails(item.id)
      this.isErrorDetailsModalOpen = true
    },
    onTableRowMiddleClicked (item) {
      window.open(this.$router.resolve({name: 'error-details', params: { errorId: item.id }}).href, '_blank')
    },
    onHideErrorDetailsModal () {
      this.detailsErrorId = ''
      this.isErrorDetailsModalOpen = false
    },
    onModalDetailsOk () {
      window.open(this.$router.resolve({name: 'error-details', params: { errorId: this.detailsErrorId }}).href, '_blank')
    },
    getErrorDetails (errorId) {
      return this.$store.dispatch('systemTools/getErrorDetails', errorId)
        .then(x => {
          this.detailsError = x.data.model
        })
        .catch(ex => {
          if (ex.response && ex.response.status && ex.response.status === 400) {
            this.$toaster.error(ex.response.data.executionResultMessage + ' ' + errorId, { timeout: 8000 })
          } else {
            this.$toaster.error(`Error Occurred on call api.`)
            this.$logger.handleError(ex, 'Can\'t get error', errorId)
          }

          this.isErrorDetailsModalOpen = false
        })
    },
    attachMiddleMouseButtonEventHandler () {
      document.body.onmousedown = (event) => {
        if (event.button === 1) {
          return false
        }
      }
    }
  },
  mounted: async function () {
    this.errorLevels = this.isProduction ? [] : [errorLevels.sandbox.value]
    await this.loadContent()
    this.attachMiddleMouseButtonEventHandler()
  },
  beforeDestroy () {
    clearTimeout(this.timeout)
  },
  watch: {
    'errorCategories': {
      deep: true,
      immediate: true,
      handler: function (newVal, oldVal) {
        if (oldVal !== undefined) {
          this.getErrorsApplicationLocations()
        }
      }
    },
    'errorLevels': {
      deep: true,
      immediate: true,
      handler: function (newVal, oldVal) {
        if (oldVal !== undefined && newVal.length !== oldVal.length) {
          this.getErrorsApplicationLocations()
        }
      }
    }
  }
}
</script>

<style scoped>
.filter-selector:hover {
  text-decoration: underline;
}
</style>

<style>
#error-details-modal pre {
  white-space: pre-wrap;
  margin: 0px;
  border: #a0a0c0 1px solid;
  background: #e8faff;
}
@media (min-width: 800px) {
  #error-details-modal .modal-xl {
    max-width: 75%;
  }
}
@media (min-width: 1200px) {
  #error-details-modal .modal-xl {
    max-width: 75%;
  }
  #error-details-modal .modal-dialog {
    transform: translateX(12%);
  }
}
@media (min-width: 1600px) {
  #error-details-modal .modal-xl {
    max-width: 81%;
  }
  #error-details-modal .modal-dialog {
    transform: translateX(8%);
  }
}
.errors-table .table-font-weight-bolder {
  font-weight: 600;
}
.errors-table tr:hover td {
  cursor: pointer;
  background-color: #C90F17 !important;
  border-color: #C90F17 !important;
  color: #fff !important;
}
.errors-table .table th,
.errors-table .table td {
  vertical-align: middle !important;
}
.error-monitor-filters .custom-control-input:disabled:checked ~ .custom-control-label::before,
.error-monitor-filters fieldset[disabled] .custom-control-input ~ .custom-control-label::before {
  border-color: #C90F17;
  background-color: #C90F17 !important;
}
.error-monitor-filters .custom-checkbox .custom-control-input:disabled:checked ~ .custom-control-label::before,
.error-monitor-filters fieldset[disabled] .custom-control-input:checked ~ .custom-control-label::before {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9.5 7.5'%3E%3Cpolyline points='0.75 4.35 4.18 6.75 8.75 0.75' style='fill:none;stroke:%23fff;stroke-linecap:round;stroke-linejoin:round;stroke-width:1.5px'/%3E%3C/svg%3E") !important;
}
.error-monitor-filters .custom-control-input:disabled ~ .custom-control-label {
  color: #4E5155;
}
.error-monitor-multiselect .multiselect__option--highlight .custom-control-label {
  color: #fff !important;
}
.error-monitor-multiselect .multiselect__option--highlight.multiselect__option--selected > .custom-control.custom-checkbox .custom-control-input:checked ~ .custom-control-label::before,
.error-monitor-multiselect .multiselect__option--highlight.multiselect__option--selected > .custom-control.custom-radio .custom-control-input:checked ~ .custom-control-label::before {
  border-color: #fff !important;
}
.error-monitor-multiselect .custom-control-label {
  padding-top: 0.15rem;
}
.error-monitor-multiselect .multiselect__option {
  padding: 0 0 0 4px !important;
  display: flex !important;
  align-items: center !important;
  min-height: 32px !important;
}
.error-monitor-multiselect,
.error-monitor-multiselect .multiselect__input,
.error-monitor-multiselect .multiselect__single {
  font-size: 14px !important;
}
.error-monitor-multiselect.multiselect--active .multiselect__single {
  display: none !important;
}
.error-monitor-multiselect .multiselect__placeholder {
  display: none !important;
}
.error-monitor-multiselect .multiselect__content-wrapper {
  max-height: 480px !important;
}
.error-monitor-multiselect.multiselect--active .multiselect__content-wrapper {
  min-width: 100%;
  width: auto;
}
.error-subject-cell {
  min-width: 20rem;
  max-width: 28rem;
}
.error-monitor-filters #categories {
  -ms-flex: 0 0 362px;
  flex: 0 0 362px;
}
.error-monitor-filters #levels {
  -ms-flex: 0 0 200px;
  flex: 0 0 200px;
}
.client-api-address-cell {
  width: 120px !important;
}
</style>
