import permissions from '../shared/common/permissions'
import applicationTypes from '../shared/common/applicationTypes'
import store from '../store'

let analyticsDefaultMeta = {
  permissions: [permissions.GoogleAnalytics4View],
  applicationFullAccess: permissions.AnalyticsFullAccess,
  applicationType: applicationTypes.AppsAnalytics.Id
}

function getRequiredProps (route) {
  return {
    accountId: +route.params.accountId,
    reportGroupId: route.params.reportGroupId,
    query: route.query.datefrom && route.query.dateto
      ? {
        datefrom: route.query.datefrom,
        dateto: route.query.dateto
      }
      : null
  }
}

function bindExtensionMethods (router) {
  router.analyticsPush = function (to) {
    if ((!to.query || !to.query.datefrom) && store.getters['analyticsGa4/dateRangeQuery']) {
      to.query = {
        ...to.query,
        ...store.getters['analyticsGa4/dateRangeQuery']
      }
    }

    this.push(to)
  }
}

export default {
  routes: [{
    path: '/ebizanalytics/:path*',
    redirect: (to) => {
      return `/ebiz_analytics_ga4/${to.params.path || ''}`
    }
  },
  {
    path: '/ebiz_analytics_ga4',
    component: () => import('@/layout/Layout2'),
    props: getRequiredProps,
    children: [
      {
        path: 'groups',
        name: 'analytics-ga4-groups',
        meta: {
          ...analyticsDefaultMeta,
          permissions: [permissions.EbizAutosAdmin],
          applicationFullAccess: permissions.EbizAutosAdmin
        },
        component: () => import('../pages/analytics_ga4/groups')
      },
      {
        path: 'brandexports',
        name: 'analytics-ga4-brand-exports',
        meta: {
          ...analyticsDefaultMeta,
          permissions: [permissions.AnalyticsFullAccess],
          applicationFullAccess: permissions.AnalyticsFullAccess
        },
        component: () => import('../pages/analytics_ga4/brandExports')
      },
      {
        path: 'brandexports/logs/:logId/details',
        name: 'analytics-ga4-brand-export-log-details',
        props: (route) => ({ logId: route.params.logId }),
        meta: {
          ...analyticsDefaultMeta,
          permissions: [permissions.AnalyticsFullAccess],
          applicationFullAccess: permissions.AnalyticsFullAccess
        },
        component: () => import('../pages/analytics_ga4/brandExportLogDetails')
      },
      {
        path: 'useractivity',
        name: 'analytics-ga4-user-activity',
        meta: {
          ...analyticsDefaultMeta,
          permissions: [permissions.AnalyticsFullAccess],
          applicationFullAccess: permissions.AnalyticsFullAccess
        },
        component: () => import('../pages/analytics_ga4/userActivity')
      },
      {
        path: 'useractivity/:logId/details',
        name: 'analytics-ga4-user-activity-log-details',
        props: (route) => ({ logId: route.params.logId }),
        meta: {
          ...analyticsDefaultMeta,
          permissions: [permissions.AnalyticsFullAccess],
          applicationFullAccess: permissions.AnalyticsFullAccess
        },
        component: () => import('../pages/analytics_ga4/userActivityDetails')
      },
      {
        path: '',
        name: 'analytics-ga4-accounts',
        meta: {
          ...analyticsDefaultMeta,
          permissions: [permissions.AnalyticsFullAccess],
          applicationFullAccess: permissions.AnalyticsFullAccess
        },
        component: () => import('../pages/analytics_ga4/analyticsAccountListing')
      },
      {
        path: ':accountId(\\d+)',
        component: () => import('@/layout/analyticsGa4Layout'),
        props: getRequiredProps,
        redirect: {
          name: 'analyticsGa4Dashboard'
        },
        meta: {
          ...analyticsDefaultMeta
        },
        children: [
          {
            path: 'dashboard',
            name: 'analyticsGa4Dashboard',
            component: () => import('@/pages/analytics_ga4/dashboard'),
            props: (route) => ({ accountId: +route.params.accountId }),
            meta: {
              ...analyticsDefaultMeta,
              relatedPageName: 'ga4GroupAnalyticsDashboard',
              analyticsReportPage: true
            }
          },
          {
            path: 'website-overview',
            name: 'ga4WebsiteOverview',
            component: () => import('@/pages/analytics_ga4/websiteOverview'),
            props: (route) => ({ accountId: +route.params.accountId }),
            meta: {
              ...analyticsDefaultMeta,
              relatedPageName: 'ga4GroupWebsiteOverview',
              analyticsReportPage: true
            }
          },
          {
            path: 'website-engagement',
            name: 'ga4WebsiteEngagement',
            component: () => import('@/pages/analytics_ga4/websiteEngagement'),
            props: (route) => ({ accountId: +route.params.accountId }),
            meta: {
              ...analyticsDefaultMeta,
              relatedPageName: 'ga4GroupWebsiteEngagement',
              analyticsReportPage: true
            }
          },
          {
            path: 'vehicles',
            name: 'ga4Vehicles',
            component: () => import('@/pages/analytics_ga4/vehicles'),
            props: (route) => ({ accountId: +route.params.accountId }),
            meta: {
              ...analyticsDefaultMeta,
              relatedPageName: 'ga4GroupVehicles',
              analyticsReportPage: true
            }
          },
          {
            path: 'channel-segments',
            name: 'ga4ChannelSegments',
            component: () => import('@/pages/analytics_ga4/channelSegments'),
            props: (route) => ({ accountId: +route.params.accountId }),
            meta: {
              ...analyticsDefaultMeta,
              relatedPageName: 'ga4GroupChannelSegments',
              analyticsReportPage: true
            }
          },
          {
            path: 'traffic-sources',
            name: 'ga4TrafficSources',
            component: () => import('@/pages/analytics_ga4/trafficSources'),
            props: (route) => ({ accountId: +route.params.accountId }),
            meta: {
              ...analyticsDefaultMeta,
              relatedPageName: 'ga4GroupTrafficSources',
              analyticsReportPage: true
            }
          },
          {
            path: 'paid-search',
            name: 'ga4PaidSearch',
            component: () => import('@/pages/analytics_ga4/paidSearch'),
            props: (route) => ({ accountId: +route.params.accountId }),
            meta: {
              ...analyticsDefaultMeta,
              relatedPageName: 'ga4GroupPaidSearch',
              analyticsReportPage: true
            }
          },
          {
            path: 'display',
            name: 'ga4Display',
            component: () => import('@/pages/analytics_ga4/display'),
            props: (route) => ({ accountId: +route.params.accountId }),
            meta: {
              ...analyticsDefaultMeta,
              relatedPageName: 'ga4GroupDisplay',
              analyticsReportPage: true
            }
          },
          {
            path: 'remarketing',
            name: 'ga4Remarketing',
            component: () => import('@/pages/analytics_ga4/remarketing'),
            props: (route) => ({ accountId: +route.params.accountId }),
            meta: {
              ...analyticsDefaultMeta,
              relatedPageName: 'ga4GroupRemarketing',
              analyticsReportPage: true
            }
          }
        ]
      },
      {
        path: 'authorization',
        name: 'analytics-ga4-authorization',
        meta: {
          ...analyticsDefaultMeta,
          permissions: [permissions.AnalyticsFullAccess],
          applicationFullAccess: permissions.AnalyticsFullAccess
        },
        component: () => import('../pages/analytics_ga4/analyticsAuthorization')
      },
      {
        path: ':accountId(\\d+)/settings',
        name: 'ga4AnalyticsAccountSettings',
        component: () => import('../pages/analytics_ga4/accountSettings'),
        props: (route) => ({ accountId: +route.params.accountId }),
        meta: {
          ...analyticsDefaultMeta,
          exactAccountMatchWithUser: true,
          analyticsReportGroupsAccess: true
        }
      },
      {
        path: 'groups/:reportGroupId',
        component: () => import('@/layout/analyticsGa4Layout'),
        props: getRequiredProps,
        redirect: {
          name: 'ga4GroupAnalyticsDashboard'
        },
        meta: {
          ...analyticsDefaultMeta,
          analyticsReportGroupsAccess: true
        },
        children: [
          {
            path: 'dashboard',
            name: 'ga4GroupAnalyticsDashboard',
            component: () => import('@/pages/analytics_ga4/groupReports/groupDashboard'),
            props: (route) => ({ reportGroupId: route.params.reportGroupId }),
            meta: {
              ...analyticsDefaultMeta,
              relatedPageName: 'analyticsGa4Dashboard',
              analyticsReportPage: true,
              analyticsReportGroupsAccess: true
            }
          },
          {
            path: 'website-overview',
            name: 'ga4GroupWebsiteOverview',
            component: () => import('@/pages/analytics_ga4/groupReports/groupWebsiteOverview'),
            props: (route) => ({ reportGroupId: route.params.reportGroupId }),
            meta: {
              ...analyticsDefaultMeta,
              relatedPageName: 'ga4WebsiteOverview',
              analyticsReportPage: true,
              analyticsReportGroupsAccess: true
            }
          },
          {
            path: 'website-engagement',
            name: 'ga4GroupWebsiteEngagement',
            component: () => import('@/pages/analytics_ga4/groupReports/groupWebsiteEngagement'),
            props: (route) => ({ reportGroupId: route.params.reportGroupId }),
            meta: {
              ...analyticsDefaultMeta,
              relatedPageName: 'ga4WebsiteEngagement',
              analyticsReportPage: true,
              analyticsReportGroupsAccess: true
            }
          },
          {
            path: 'vehicles',
            name: 'ga4GroupVehicles',
            component: () => import('@/pages/analytics_ga4/groupReports/groupVehicles'),
            props: (route) => ({ reportGroupId: route.params.reportGroupId }),
            meta: {
              ...analyticsDefaultMeta,
              relatedPageName: 'ga4Vehicles',
              analyticsReportPage: true,
              analyticsReportGroupsAccess: true
            }
          },
          {
            path: 'channel-segments',
            name: 'ga4GroupChannelSegments',
            component: () => import('@/pages/analytics_ga4/groupReports/groupChannelSegments'),
            props: (route) => ({ reportGroupId: route.params.reportGroupId }),
            meta: {
              ...analyticsDefaultMeta,
              relatedPageName: 'ga4ChannelSegments',
              analyticsReportPage: true,
              analyticsReportGroupsAccess: true
            }
          },
          {
            path: 'traffic-sources',
            name: 'ga4GroupTrafficSources',
            component: () => import('@/pages/analytics_ga4/groupReports/groupTrafficSources'),
            props: (route) => ({ reportGroupId: route.params.reportGroupId }),
            meta: {
              ...analyticsDefaultMeta,
              relatedPageName: 'ga4TrafficSources',
              analyticsReportPage: true,
              analyticsReportGroupsAccess: true
            }
          },
          {
            path: 'paid-search',
            name: 'ga4GroupPaidSearch',
            component: () => import('@/pages/analytics_ga4/groupReports/groupPaidSearch'),
            props: (route) => ({ reportGroupId: route.params.reportGroupId }),
            meta: {
              ...analyticsDefaultMeta,
              relatedPageName: 'ga4PaidSearch',
              analyticsReportPage: true,
              analyticsReportGroupsAccess: true
            }
          },
          {
            path: 'display',
            name: 'ga4GroupDisplay',
            component: () => import('@/pages/analytics_ga4/groupReports/groupDisplay'),
            props: (route) => ({ reportGroupId: route.params.reportGroupId }),
            meta: {
              ...analyticsDefaultMeta,
              relatedPageName: 'ga4Display',
              analyticsReportPage: true,
              analyticsReportGroupsAccess: true
            }
          },
          {
            path: 'remarketing',
            name: 'ga4GroupRemarketing',
            component: () => import('@/pages/analytics_ga4/groupReports/groupRemarketing'),
            props: (route) => ({ reportGroupId: route.params.reportGroupId }),
            meta: {
              ...analyticsDefaultMeta,
              relatedPageName: 'ga4Remarketing',
              analyticsReportPage: true,
              analyticsReportGroupsAccess: true
            }
          }
        ]
      },
      {
        path: 'penske',
        component: () => import('@/pages/analytics_ga4/penskeReport/penske'),
        props: (route) => ({ accountId: +route.params.accountId }),
        name: 'ga4PAGGroupReport',
        meta: {
          ...analyticsDefaultMeta,
          permissions: [permissions.AnalyticsFullAccess]
        }
      },
      {
        path: 'googleAdsRebuild',
        component: () => import('@/pages/analytics_ga4/googleAdsRebuild'),
        name: 'ga4AdsRebuild',
        meta: {
          ...analyticsDefaultMeta,
          permissions: [permissions.FullAccess],
          applicationFullAccess: permissions.FullAccess
        }
      },
      {
        path: 'logs',
        name: 'analytics-ga4-logs',
        meta: {
          ...analyticsDefaultMeta,
          permissions: [permissions.AnalyticsFullAccess],
          applicationFullAccess: permissions.AnalyticsFullAccess
        },
        component: () => import('../pages/analytics_ga4/logs/logs.vue')
      }
    ]
  }],
  bindExtensionMethods: bindExtensionMethods
}
