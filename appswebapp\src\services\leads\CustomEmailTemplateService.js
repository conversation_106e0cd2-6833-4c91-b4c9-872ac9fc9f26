
import BaseService from '../BaseService'

class CustomEmailTemplateService extends BaseService {
  getCustomEmailTemplates (accountId) {
    return this.axios.get(`/api/leads/accounts/${accountId}/email_templates`)
  };
  createCustomEmailTemplates (accountId, data) {
    return this.axios.post(`/api/leads/accounts/${accountId}/email_templates`, data)
  };
  getCustomEmailTemplateDetails (accountId, id) {
    return this.axios.get(`/api/leads/accounts/${accountId}/email_templates/${id}`)
  };
  getCustomEmailTemplatePrototype (accountId) {
    return this.axios.get(`/api/leads/accounts/${accountId}/email_templates/new`)
  };
  updateCustomEmailTemplate (parameters) {
    return this.axios.post(`/api/leads/accounts/${parameters.accountId}/email_templates/${parameters.id}/update`, parameters.data)
  };
  deleteCustomEmailTemplate (accountId, id) {
    return this.axios.post(`/api/leads/accounts/${accountId}/email_templates/${id}/delete`)
  };
}

export default new CustomEmailTemplateService()
