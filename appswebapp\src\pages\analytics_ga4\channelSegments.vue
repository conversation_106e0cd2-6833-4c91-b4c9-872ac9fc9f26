<template>
  <div>
    <channel-segments-summary
      :barItems="bar.items"
      :pieItems="table.items"
      :summary="summary"
      :barTimeFormat="barTimeFormat"
    ></channel-segments-summary>

    <channel-segments-by-segment-table
      :tableItems="table.items"
      :sortType="page.filter.sortType"
      @sortTypeChanged="onSortTypeChanged"
    ></channel-segments-by-segment-table>

  </div>
</template>

<style src="@/vendor/styles/pages/analytics.scss" lang="scss"></style>

<script>
import analyticsConstants from './../../shared/analytics/constants'
import analyticsBuilders from './../../shared/analytics/builders'
import analyticsHelper from './helpers.js'
import baseReportPage from './baseReportPage.js'

import ChannelSegmentsSummary from '../../components/analytics_ga4/summaries/channelSegmentsSummary'
import ChannelSegmentsBySegmentTable from '../../components/analytics_ga4/tables/channelSegmentsBySegmentTable'

const filterManager = analyticsBuilders.getFilterManager({
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  sortType: { type: Number, default: analyticsConstants.channelSegmentSortTypes.sessionsDesc },
  rangeType: { type: Number, default: analyticsConstants.rangeTypes.month }
})

export default {
  mixins: [baseReportPage],
  name: 'channel-segments',
  metaInfo: {
    title: 'Analytics - Channel Segments'
  },
  components: {
    ChannelSegmentsBySegmentTable,
    ChannelSegmentsSummary
  },
  created () {
    this.$store.commit('analyticsGa4/setAnalyticsName', 'Channel Segments')
    this.$store.commit('analyticsGa4/setBlackThemeOn', true)
  },
  data () {
    return {
      page: {
        filter: filterManager.defaultValue
      },
      summary: {
        sessions: 0,
        sessionsDelta: null,
        pageViews: 0,
        pageViewsDelta: null,
        avgSessionDuration: 0,
        avgSessionDurationDelta: null,
        totalLeads: 0,
        totalLeadsDelta: null
      }
    }
  },
  computed: {
    filterManager () {
      return filterManager
    }
  },
  methods: {
    async updateStatistics () {
      try {
        const store = await analyticsHelper.fetchOrGetCachedApiResult(
          this.cache,
          'analyticsGa4/getChannelSegmentsReport',
          {
            accountId: this.accountId,
            dateFrom: this.rangeInfo.range[0],
            dateTo: this.rangeInfo.range[1],
            sortType: analyticsConstants.channelSegmentSortTypes.sessionsDesc
          }
        )

        this.summary = {
          ...this.summary,
          ...store.summary.data
        }
        this.bar.items = store.graph.data
        this.table.items = store.detailedData.data.items
      } catch (err) {
        this.$toaster.error('Failed to get data from server', { timeout: 8000 })
        this.$logger.handleError(err, 'Can\'t get channel segments report', { filter: this.page.filter, cache: this.cache })
      }
    }
  }
}
</script>
