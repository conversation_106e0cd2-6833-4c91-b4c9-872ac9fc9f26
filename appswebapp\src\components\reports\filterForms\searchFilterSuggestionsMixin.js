import ReportService from '@/services/reports/ReportService'

export default {
  data () {
    return {
      suggestions: []
    }
  },
  computed: {
    getSuggestions () {
      if (this.suggestions.length > 10) {
        return this.suggestions.slice(0, 10)
      }
      return this.suggestions
    }
  },
  methods: {
    onSearchInput () {
      if (!this.copyFilters.search) {
        this.copyFilters.includeRelations = false
      }

      if (this.copyFilters.search && this.copyFilters.search.length < 3) {
        return
      }
      ReportService.getSearchSuggestions(this.copyFilters.search).then(res => {
        this.suggestions = res.data || []
      }).catch(ex => {
        console.error(ex)
      })
    }
  }
}
