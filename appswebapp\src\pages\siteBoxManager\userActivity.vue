<template>
  <div class="position-relative">
    <h4>
      Logs
    </h4>
    <paging
       class="custom-paging d-none d-md-block p-0"
      :pageNumber="filter.page"
      :pageSize="filter.pageSize"
      :totalItems="itemsTotalCount"
      @numberChanged="pageChanged"
      @changePageSize="changePageSize"
    />
    <b-card>
      <b-form  v-on:submit.prevent="applyFilter">
        <div class="form-row">
          <b-col class="my-1" sm="4" lg="4" xl="2">
            <b-form-input
              max='200'
              v-model="filter.search"
              placeholder="Search..."
              autocomplete="off"
            >
            </b-form-input>
          </b-col>

          <b-col class="my-1" sm="4" lg="4" xl="2">
            <b-input-group class="flex-nowrap">
              <b-input-group-prepend is-text>
                <i class="ion ion-md-calendar" slot="prepend"></i>
              </b-input-group-prepend>
              <date-time-picker
                ref="timeFrom"
                v-model="filter.dateFrom"
                :options="filterTimeOptions"
                format="MM/DD/YYYY HH:mm"
                placeholder="Date From"
                className="form-control"
                @change="onTimeFromInputChange"
              />
              <b-input-group-append
                is-text
                v-show="filter.dateFrom"
                @click="filter.dateFrom = null"
              >
                <i class="ion ion-md-close"></i>
              </b-input-group-append>
            </b-input-group>
          </b-col>

          <b-col class="my-1" sm="4" lg="4" xl="2">
            <b-input-group class="flex-nowrap">
              <b-input-group-prepend is-text>
                <i class="ion ion-md-calendar" slot="prepend"></i>
              </b-input-group-prepend>
              <date-time-picker
                ref="timeTo"
                v-model="filter.dateTo"
                :options="filterTimeOptions"
                format="MM/DD/YYYY HH:mm"
                placeholder="Date To"
                className="form-control"
                @change="onTimeToInputChange"
              />
              <b-input-group-append
                is-text
                v-show="filter.dateTo"
                @click="filter.dateTo = null"
              >
                <i class="ion ion-md-close"></i>
              </b-input-group-append>
            </b-input-group>
          </b-col>
          <b-col class="my-1" sm="4" lg="4" xl="2">
            <b-form-select v-model="filter.action" :options="getActionOptions"></b-form-select>
          </b-col>
          <b-col class="my-1" sm="4" lg="4" xl="2">
            <multiSelectWithCheckboxes
              v-model='selectedUserTypes'
              :options='getUserTypeOptions'
              name="User Types"
              customMessageOfNoneSelectedItems="All User Types"
            ></multiSelectWithCheckboxes>
          </b-col>
          <b-col class="my-1" sm="4" lg="4" xl="2">
            <b-btn block  variant="primary" type="submit">Submit</b-btn>
          </b-col>
        </div>
      </b-form>
    </b-card>
    <b-card v-if="!isLoading">
      <b-table
        :items="items"
        :fields="getTableFields"
        :sort-by="tableSortBy"
        :sort-desc="tableSortDesc"
        @sort-changed="onSortChanged"
        :striped="true"
        :bordered="false"
        :no-sort-reset="true"
        :no-local-sorting="true"
        hover
        responsive
        show-empty
      >
        <template #cell(manage)="row">
          <b-btn size="sm" @click="showDetails(row)">
            {{ row.detailsShowing ? 'Hide' : 'Show' }} Details
          </b-btn>
          <a class="btn btn-outline-secondary btn-sm" :href='getLogDetailsPath(row.item.id)' target="_blank">
            <i class="ion ion-md-open"></i>
          </a>
        </template>
        <template #row-details="row">
          <b-card>
            <log-node
              :data="row.item.details"
              :isExpandedShallow="true"
              :isExpandedDeep="false"
            />
          </b-card>
        </template>
      </b-table>
      <paging
        class="p-0"
        :pageNumber="filter.page"
        :pageSize="filter.pageSize"
        :totalItems="itemsTotalCount"
        titled
        pageSizeSelector
        @numberChanged="pageChanged"
        @changePageSize="changePageSize"
      />
    </b-card>
    <div v-else-if="isLoading" class="my-3 py-3">
      <loader size="lg"/>
    </div>
  </div>
</template>

<script>
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import moment from 'moment'
import { userActivitySortTypes, userActivityActionTypes } from '@/shared/siteBoxManager/common/constants'
import {userTypes} from '@/shared/users/constants'
import multiSelectWithCheckboxes from '../../components/_shared/multiSelectWithCheckboxes.vue'

const defaultValues = new ObjectSchema({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  search: { type: String, default: '' },
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  sort: { type: Number, default: userActivitySortTypes.dateDesc },
  action: {type: Number, default: 0},
  userTypes: {type: String, default: `${userTypes.cpUser.value},${userTypes.adminUser.value}`}
})

const queryHelper = new QueryStringHelper(defaultValues)

export default {
  metaInfo: {
    title: 'User Activity'
  },
  data () {
    return {
      filter: defaultValues.getObject(),
      items: [],
      itemsTotalCount: 0,
      isLoading: true,
      filterTimeOptions: {
        autoUpdateInput: false,
        singleDatePicker: true,
        maxDate: new Date()
      }
    }
  },
  components: {
    'date-time-picker': () => import('@gravitano/vue-date-range-picker/src/components/DateRangePicker'),
    'paging': () => import('@/components/_shared/paging'),
    'loader': () => import('@/components/_shared/loader'),
    'log-node': () => import('@/components/_shared/logItemNode.vue'),
    multiSelectWithCheckboxes
  },
  computed: {
    selectedUserTypes: {
      get () {
        return this.getIntegerArrayFromString(this.filter.userTypes)
      },
      set (value) {
        this.filter.userTypes = (value || []).join()
      }
    },
    getTableFields () {
      return [
        {
          key: 'userName',
          label: 'User Name',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: userActivitySortTypes.userNameAsc,
          sortTypeDesc: userActivitySortTypes.userNameDesc
        },
        {
          key: 'userType',
          label: 'User Type',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: userActivitySortTypes.userTypeAsc,
          sortTypeDesc: userActivitySortTypes.userTypeDesc,
          formatter: val => (Object.values(userTypes).find(x => x.value === val) || {text: '-'}).text
        },
        {
          key: 'accountId',
          label: 'Account Id',
          tdClass: 'py-2 align-middle',
          formatter: value => value || '-',
          sortable: true,
          sortTypeAsc: userActivitySortTypes.accountIdAsc,
          sortTypeDesc: userActivitySortTypes.accountIdDesc
        },
        {
          key: 'accountName',
          label: 'Account Name',
          tdClass: 'py-2 align-middle',
          formatter: val => val || '-',
          sortable: true,
          sortTypeAsc: userActivitySortTypes.accountNameAsc,
          sortTypeDesc: userActivitySortTypes.accountNameDesc
        },
        {
          key: 'action',
          label: 'Action Name',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: userActivitySortTypes.actionAsc,
          sortTypeDesc: userActivitySortTypes.actionDesc,
          formatter: val => (Object.values(userActivityActionTypes).find(x => x.value === val) || {text: '-'}).text
        },
        {
          key: 'dateTime',
          label: 'Date',
          tdClass: 'py-2 align-middle',
          formatter: value => moment(value).format('MM/DD/YYYY hh:mm:ss A'),
          sortable: true,
          sortTypeAsc: userActivitySortTypes.dateAsc,
          sortTypeDesc: userActivitySortTypes.dateDesc
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    refDateTimeFrom () {
      return (this.$refs.timeFrom || {}).$el || {}
    },
    refDateTimeTo () {
      return (this.$refs.timeTo || {}).$el || {}
    },
    tableSortBy () {
      const sortedColumn = this.getTableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.getTableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sortType
      } else {
        return false
      }
    },
    getUserTypeOptions () {
      return Object.values(userTypes)
    },
    getActionOptions () {
      return Object.values(userActivityActionTypes).sort((left, right) => left.text.localeCompare(right.text))
    }
  },
  created () {
    this.filter = queryHelper.parseQueryStringToObject(this.$router)
    this.populateData()
  },
  methods: {
    applyFilter () {
      this.isLoading = true
      this.filter.page = 1
      this.synchronizeUrlAndReload()
    },
    onSortChanged (value) {
      const sortingColumn = this.getTableFields.find(x => x.key === value.sortBy)
      if (value.sortDesc) {
        this.filter.sort = sortingColumn.sortTypeDesc
      } else {
        this.filter.sort = sortingColumn.sortTypeAsc
      }
      this.synchronizeUrlAndReload()
    },
    onTimeFromInputChange (newVal) {
      this.refDateTimeFrom.value = newVal || this.filter.dateFrom || null
    },
    onTimeToInputChange (newVal) {
      this.refDateTimeTo.value = newVal || this.filter.dateTo || null
    },
    pageChanged (newPage) {
      this.filter.page = newPage
      this.isLoading = true
      this.synchronizeUrlAndReload()
    },
    changePageSize (newPageSize) {
      this.filter.pageSize = newPageSize
      this.filter.page = 1
      this.isLoading = true
      this.synchronizeUrlAndReload()
    },
    synchronizeUrlAndReload () {
      queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.populateData()
    },
    getLogDetailsPath (logId) {
      return `/siteboxmanager/useractivity/${logId}/details`
    },
    async showDetails (row) {
      if (row.item.details) {
        row.toggleDetails()
      } else {
        this.$set(row.item, 'isLoading', true)

        try {
          const apiResult = await this.$store.dispatch('siteBoxManager/getLogDetails', {id: row.item.id})

          row.item.details = {
            nodes: apiResult.data.properties
          }

          row.toggleDetails()
        } catch (err) {
          this.$toaster.exception(err, 'Failed to get data from server')
          this.$logger.handleError(err, 'Can\'t load log details', row.item)
        } finally {
          this.$set(row.item, 'isLoading', false)
        }
      }
    },
    getIntegerArrayFromString (str) {
      if (!str || !str.trim()) {
        return []
      }
      let array = []
      for (let numberStr of str.split(',')) {
        let number = parseInt(numberStr)
        if (!isNaN(number)) {
          array.push(number)
        }
      }
      return array
    },
    populateData () {
      this.$store.dispatch('siteBoxManager/getLogs', { filter: this.filter }).then(res => {
        this.items = res.data.items
        this.itemsTotalCount = res.data.totalCount
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
      }).finally(() => {
        this.isLoading = false
      })
    }
  }
}
</script>

<style scoped>
.custom-paging {
  position: absolute;
  right: 5px;
  top: -10px;
  z-index: 2;
}
</style>
