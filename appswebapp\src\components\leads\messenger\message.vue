<template>
  <div class="leads-message" :class="messagePosition">
    <div class="leads-message-content">
      <div :class="contentPosition">
        <div :class="leadsMessageClass">
          <div v-if='conversation.communicationType === communicationTypes.sms.value'>
            <sms :model='conversation' />
          </div>
          <div v-if='conversation.communicationType === communicationTypes.voice.value'>
            <voice :model='conversation' />
          </div>
          <div v-if='conversation.communicationType === communicationTypes.email.value'>
            <email :model='conversation' />
          </div>
          <div v-if='conversation.communicationType === communicationTypes.webForm.value'>
            <web-form :model='conversation' />
          </div>
          <div v-if='conversation.communicationType === communicationTypes.eBay.value'>
            <ebay :model='conversation' />
          </div>
        </div>
      </div>
      <div :class="contentPosition">
        <span class="leads-message-extra-content text-muted">{{getMessageInfo}}</span>
      </div>
      <div v-if='conversation.isIncoming && hasSendMessage' :class="contentPosition">
        <b-btn size="sm" @click="archive">Archive</b-btn>
        <b-btn size="sm" variant="primary" @click="showModal" class="ml-1" v-if='!conversation.isCommunicationClosed && hasFullAccess'>Resend Notification</b-btn>
        <b-btn size="sm" class="ml-1" v-if="conversation.leadType === leadType.creditApp.value && hasAccessToDownloadCreditApp" variant="primary" @click="onDownload(conversation.id)">Download</b-btn>
      </div>
    </div>
    <b-modal
      :visible='isShowModal'
      @hide="hideModal"
      title="Resend notifications"
      size="sm"
      centered
    >
      <div class="d-flex justify-content-center">
        <b-form-checkbox-group
          v-model="selectedNotificationTypes"
          :options='notificationTypes'
          stacked
        >
        </b-form-checkbox-group>
      </div>
      <template #modal-footer>
        <b-btn size="md" variant="primary" @click="resend">Resend</b-btn>
        <b-btn size="md" class="ml-2" @click="hideModal">Cancel</b-btn>
      </template>
    </b-modal>
    <b-modal
     :visible="isShowDownloadCreditAppModal"
     @hide="hideDownloadCreditAppModal"
     no-close-on-backdrop
     centered
    >
      <detail-row fixedPayloadWidth>
        <span slot="title">Credit Application Password:</span>
        <b-form-input slot="payload" name="creditAppPassword" type="password" autocomplete="new-password" v-model="creditAppPassword"></b-form-input>
      </detail-row>
      <template #modal-footer>
        <b-btn variant="primary" @click="onSubmitAndDownload">Submit and Download</b-btn>
      </template>
    </b-modal>
  </div>
</template>

<script>
import moment from 'moment'
import { communicationTypes, notificationTypes, leadType } from '@/shared/leads/common'
import detailRow from '@/components/details/helpers/detailRow'
import permissions from '@/shared/common/permissions'
import applicationTypes from '@/shared/common/applicationTypes'
import webForm from '@/components/leads/messenger/messageTypes/webForm'
import voice from '@/components/leads/messenger/messageTypes/voice'
import email from '@/components/leads/messenger/messageTypes/email'
import sms from '@/components/leads/messenger/messageTypes/sms'
import ebay from '@/components/leads/messenger/messageTypes/ebay'
import { mapGetters } from 'vuex'

export default {
  name: 'leads-messenger-message',
  props: {
    conversation: { type: Object, required: true }
  },
  data () {
    return {
      communicationTypes,
      leadType,
      notificationTypes,
      selectedNotificationTypes: notificationTypes.map(x => x.value),
      isShowModal: false,
      isShowDownloadCreditAppModal: false,
      creditAppPassword: '',
      conversationDetailsId: '',
      messagePosition: 'float-right',
      contentPosition: 'd-flex justify-content-end',
      leadsMessageClass: 'leads-message-out-coming'
    }
  },
  components: {
    'detail-row': detailRow,
    'sms': sms,
    'ebay': ebay,
    'web-form': webForm,
    'voice': voice,
    'email': email
  },
  created () {
    if (this.conversation.isIncoming) {
      this.messagePosition = 'float-left'
      this.contentPosition = 'd-flex justify-content-start'
      this.leadsMessageClass = 'leads-message-in-coming'
    }
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {
        canManageAccountApplicationType: () => false,
        hasWritePermissions: () => false
      }
    },
    hasSendMessage () {
      return this.user.hasWritePermissions(this.conversation.accountId, permissions.LeadsSendMessages, applicationTypes.AppsLeads.Id, permissions.LeadsFullAccess)
    },
    hasAccessToDownloadCreditApp () {
      return this.user && this.user.accountId > 0 && this.user.hasWritePermissions(this.conversation.accountId, permissions.LeadsView, applicationTypes.AppsLeads.Id, permissions.LeadsFullAccess)
    },
    hasFullAccess () {
      return this.user && this.user.hasPermissions && this.user.hasPermissions(permissions.LeadsFullAccess)
    },
    getDateFormat () {
      return moment(this.conversation.dateTime).format('MM/DD/YYYY hh:mm A')
    },
    getCommunicationTypeDesc () {
      let res = Object.values(communicationTypes).find(x => x.value === this.conversation.communicationType)
      if (res) {
        return res.label
      }

      return ''
    },
    getMessageInfo () {
      let messageInfo = `${this.getDateFormat}/`
      let dealerName = [this.conversation.dealerContact.firstName, this.conversation.dealerContact.lastName].filter(v => v && v !== '').join(' ')
      if (dealerName.length > 0) {
        return messageInfo + dealerName + `/${this.getCommunicationTypeDesc}`
      }

      return messageInfo + `${this.getCommunicationTypeDesc}`
    }
  },
  methods: {
    archive () {
      this.$emit('archive', this.conversation.id)
    },
    showModal () {
      this.isShowModal = true
    },
    hideModal () {
      this.isShowModal = false
    },
    resend () {
      if (this.selectedNotificationTypes.length > 0) {
        let data = {
          selectedNotificationTypes: this.selectedNotificationTypes,
          id: this.conversation.id
        }
        this.$emit('resend', data)
        this.isShowModal = false
      } else {
        this.$toaster.error('At least one notification type required')
      }
    },
    onDownload (conversationDetailsId) {
      this.conversationDetailsId = conversationDetailsId
      this.isShowDownloadCreditAppModal = true
    },
    hideDownloadCreditAppModal () {
      this.isShowDownloadCreditAppModal = false
    },
    onSubmitAndDownload () {
      if (!this.creditAppPassword.trim()) {
        this.$toaster.error('Password is required')
        return
      }
      this.$emit('downloadCreditApp', { id: this.conversationDetailsId, password: this.creditAppPassword })
      this.isShowDownloadCreditAppModal = false
    }
  }
}
</script>

<style scoped>
.leads-message-in-coming {
  max-width: 95%;
  padding: 15px;
  background: rgb(245, 244, 244);
}
.leads-message-out-coming {
  max-width: 95%;
  padding: 15px;
  background: rgb(233, 233, 233);
}

.leads-message {
  position: relative;
  display: block;
  padding-top: 1rem;
  width: 100%;
}
.leads-message-content {
  display: flex;
  flex-direction: column;
  flex-shrink: 1;
}

.leads-message-extra-content {
  font-size: 12px;
  margin-top: 5px;
}
</style>
