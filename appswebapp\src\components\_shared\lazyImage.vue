<template>
  <div ref="image" class="asyncImage" :class="{'done': isLoaded}" :style="{ 'background-image': 'url(' + (fullImageSrc || '').replace(/\\/g, '/') +')' }" @click="onClick"></div>
</template>

<script>
export default {
  name: 'image-lazy',
  props: {
    src: String,
    preloadSrc: String,
    query: String,
    alt: String
  },
  data () {
    return {
      imageSrc: '',
      initialized: false,
      isObserved: false,
      isLoaded: false
    }
  },
  mounted () {
    this.intersectionObserver()
  },
  computed: {
    fullImageSrc: {
      set (value) {
        // q param prevents image cache
        this.imageSrc = value + `?q=${this.query || ''}`
      },
      get () {
        return this.imageSrc
      }
    }
  },
  methods: {
    intersectionObserver () {
      if (this.isObserved) {
        return
      }

      if (typeof IntersectionObserver === 'undefined') {
        // no lazy loading for ie, old safari users :(
        this.fullImageSrc = this.src
        return
      }

      this.fullImageSrc = this.preloadSrc

      let observer = new IntersectionObserver((entries, self) => {
        let entry = entries[0]
        if (entry.isIntersecting) {
          self.unobserve(entry.target)

          let img = new Image()

          setTimeout(x => {
            img.src = this.src + `?q=${this.query || ''}`
            img.onload = () => {
              this.fullImageSrc = this.src
              this.isLoaded = true
            }
          }, 50) // timeout to prevent loading big images before small
        }
      })

      let target = this.$refs.image

      observer.observe(target)

      this.isObserved = true
    },
    onClick (e) {
      this.$emit('click', e)
    }
  },
  watch: {
    src (val) {
      this.fullImageSrc = val
    }
  }
}
</script>

<style scoped>
  div {
    background-color: #efefef;
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;

    transition: .15s filter linear;
    -webkit-transition: .15s filter linear;
    -moz-transition: .15s filter linear;
    -ms-transition: .15s filter linear;
    -o-transition: .15s filter linear;
  }

  .asyncImage {
    filter: blur(5px);
  }

  .asyncImage.done {
    filter: blur(0px);
  }
</style>
