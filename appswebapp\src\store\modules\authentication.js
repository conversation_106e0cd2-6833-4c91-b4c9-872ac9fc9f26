import axios from 'axios'

export default {
  namespaced: true,
  state: {
    isAuthenticated: !!localStorage.getItem('isAuthenticated')
  },
  getters: {
    isAuthenticated: state => state.isAuthenticated
  },
  mutations: {
    setIsAuthenticated (state, isAuthenticated) {
      state.isAuthenticated = isAuthenticated
      if (isAuthenticated) {
        localStorage.setItem('isAuthenticated', true)
      } else {
        localStorage.removeItem('isAuthenticated')
      }
    }
  },
  actions: {
    logIn ({ commit }, userCredentials) {
      commit('setIsAuthenticated', false)
      commit('users/clearInfoForUser', null, { root: true })

      return axios.post('/api/auth/login', {
        UserName: userCredentials.name,
        Password: userCredentials.password,
        TurnstileToken: userCredentials.turnstileToken,
        DeviceId: 'Apps VueJS Web Application',
        DeviceModel: window.navigator.userAgent,
        ApplicationName: 'AppsWeb'
      })
        .then((resp) => {
          const data = resp && resp.data
          if (data && data.isMfaRequired) {
            // store MFA challenge and redirect will be handled by page
            commit('mfa/setChallenge', {
              challengeId: data.challengeId,
              availableMethods: data.availableMethods
            }, { root: true })
            return { isMfaRequired: true }
          } else {
            commit('setIsAuthenticated', true)
            return { isMfaRequired: false }
          }
        })
        .catch(function (reason) {
          commit('setIsAuthenticated', false)
          throw reason
        })
    },

    logOut ({ commit }) {
      return axios.get('/api/auth/logout')
        .then(_ => {})
        .catch(_ => {})
        .then(_ => {
          commit('setIsAuthenticated', false)
          commit('mfa/clear', null, { root: true })
        })
    }
  }
}
