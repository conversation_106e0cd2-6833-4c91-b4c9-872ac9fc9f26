<template>
  <div class="d-flex">
    <template v-if="isAccountActive(account.accountStatus) || isAccountOnHold(account.accountStatus)">
      <c-button v-if="isAccountActive(account.accountStatus)"
        :message="`Are you sure you want to move account ${account.accountId} to OnHold?`"
        variant="secondary"
        className="mr-1"
        size="sm"
        :disabled="isLoading"
        @confirm="moveAccountToOnHold(account)">
        Move to OnHold
      </c-button>
      <c-button v-else
        :message="`Are you sure you want to move account ${account.accountId} to Active?`"
        variant="secondary"
        className="mr-1"
        size="sm"
        :disabled="isLoading"
        @confirm="moveAccountToActive(account)">
        Move to Active
      </c-button>
      <c-button
        :message="`Are you sure you want to move account ${account.accountId} to Pending? You will not be able to revert this!`"
        variant="primary"
        size="sm"
        :disabled="isLoading"
        @confirm="moveAccountToPending(account)">
        Move to Pending
      </c-button>
    </template>
    <template v-else-if="isAccountPending(account.accountStatus)">
      <c-button :message="`Are you sure you want to Close account ${account.accountId}? You will not be able to revert this!`" variant="primary" size="sm" :disabled="isLoading" @confirm="closeAccount(account)">Close</c-button>
    </template>
  </div>
</template>

<script>
import accountStatuses from '@/shared/accounts/accountStatuses'

export default {
  name: 'AccountStatusActionButtons',
  props: {
    account: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      isLoading: false
    }
  },
  methods: {
    isAccountActive (accountStatus) {
      return accountStatus === accountStatuses.Active.Value
    },
    isAccountOnHold (accountStatus) {
      return accountStatus === accountStatuses.OnHold.Value
    },
    isAccountPending (accountStatus) {
      return accountStatus === accountStatuses.Pending.Value
    },
    moveAccountToPending (accountItem) {
      this.isLoading = true
      this.$store.dispatch('accountManagement/moveAccountToPending', {accountId: accountItem.accountId})
        .then(x => {
          accountItem.accountStatus = accountStatuses.Pending.Value
          this.$toaster.success(`Account ${accountItem.accountId} is moved to Pending.`, { timeout: 8000 })
        })
        .catch(ex => {
          this.$toaster.error(`Failed to move account ${accountItem.accountId} to Pending. Try again later.`, { timeout: 8000 })
          this.$logger.handleError(ex, `Failed to move account ${accountItem.accountId} to Pending`)
        })
        .finally(() => {
          this.isLoading = false
        })
    },
    moveAccountToOnHold (accountItem) {
      this.isLoading = true
      this.$store.dispatch('accountManagement/moveAccountToOnHold', {accountId: accountItem.accountId})
        .then(x => {
          accountItem.accountStatus = accountStatuses.OnHold.Value
          this.$toaster.success(`Account ${accountItem.accountId} is moved to OnHold.`, { timeout: 8000 })
        })
        .catch(ex => {
          this.$toaster.error(`Failed to move account ${accountItem.accountId} to OnHold. Try again later.`, { timeout: 8000 })
          this.$logger.handleError(ex, `Failed to move account ${accountItem.accountId} to OnHold`)
        })
        .finally(() => {
          this.isLoading = false
        })
    },
    moveAccountToActive (accountItem) {
      this.isLoading = true
      this.$store.dispatch('accountManagement/moveAccountToActive', {accountId: accountItem.accountId})
        .then(x => {
          accountItem.accountStatus = accountStatuses.Active.Value
          this.$toaster.success(`Account ${accountItem.accountId} is moved to Active.`, { timeout: 8000 })
        })
        .catch(ex => {
          this.$toaster.error(`Failed to move account ${accountItem.accountId} to Active. Try again later.`, { timeout: 8000 })
          this.$logger.handleError(ex, `Failed to move account ${accountItem.accountId} to Active`)
        })
        .finally(() => {
          this.isLoading = false
        })
    },
    closeAccount (accountItem) {
      this.isLoading = true
      this.$store.dispatch('accountManagement/closeAccount', {accountId: accountItem.accountId})
        .then(x => {
          accountItem.accountStatus = accountStatuses.Closed.Value
          this.$toaster.success(`Account ${accountItem.accountId} is Closed.`, { timeout: 8000 })
        })
        .catch(ex => {
          this.$toaster.error(`Failed to close account ${accountItem.accountId}. Try again later.`, { timeout: 8000 })
          this.$logger.handleError(ex, `Failed to close account ${accountItem.accountId}.`)
        })
        .finally(() => {
          this.isLoading = false
        })
    }
  }
}
</script>
