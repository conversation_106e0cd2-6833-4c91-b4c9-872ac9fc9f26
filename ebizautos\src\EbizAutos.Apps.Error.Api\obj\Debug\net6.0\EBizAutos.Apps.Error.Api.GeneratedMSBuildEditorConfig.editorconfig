is_global = true
build_property.TargetFramework = net6.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = EBizAutos.Apps.Error.Api
build_property.RootNamespace = EBizAutos.Apps.Error.Api
build_property.ProjectDir = d:\Work\ebizautos\src\EBizAutos.Apps.Error.Api\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 6.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = d:\Work\ebizautos\src\EBizAutos.Apps.Error.Api
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 6.0
build_property.EnableCodeStyleSeverity = 

[d:/Work/ebizautos/src/EBizAutos.Apps.Error.Api/Views/ErrorReportTemplate.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRXJyb3JSZXBvcnRUZW1wbGF0ZS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 
