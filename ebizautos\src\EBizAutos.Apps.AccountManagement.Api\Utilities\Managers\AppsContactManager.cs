﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using EBizAutos.Apps.AccountManagement.Api.Models.ContactDependencies;
using EBizAutos.Apps.AccountManagement.Api.Models.ContactsInformation;
using EBizAutos.Apps.AccountManagement.Api.ServiceBus.Events;
using EBizAutos.Apps.AccountManagement.Api.Utilities.Builders;
using EBizAutos.Apps.AccountManagement.Api.Utilities.Converters;
using EBizAutos.Apps.Api.Client;
using EBizAutos.Apps.CommonLib.Abstract.Repositories.AppsContactDependencies;
using EBizAutos.Apps.CommonLib.Abstract.Repositories.AppsContactInformation;
using EBizAutos.Apps.CommonLib.Abstract.Repositories.Logs.AccountManagement;
using EBizAutos.Apps.CommonLib.Models.AppsContactDependencies;
using EBizAutos.Apps.CommonLib.Models.AppsContactInformation;
using EBizAutos.Apps.CommonLib.Models.Web;
using EBizAutos.Apps.ServiceBus.Events.Contact;
using EBizAutos.Apps.ServiceBus.Events.User;
using EBizAutos.CommonLib.Extensions;
using EBizAutos.CommonLib.PublicConstants;
using EBizAutos.CommonLib.ServiceBus;
using EBizAutos.CommonLibCore;
using EBizAutos.FoundationCommonLib.Abstract.Repositories.AppsNet.SiteBoxManagement;
using EBizAutos.FoundationCommonLib.Models.Common.ContactsInformation;
using EBizAutos.FoundationCommonLib.Models.Common.SiteBoxManagement;
using Newtonsoft.Json.Linq;

namespace EBizAutos.Apps.AccountManagement.Api.Utilities.Managers {
	public class AppsContactManager {
		private const string ConstGalleryJsonRootPropertyName = "pageData";
		private const string ConstGalleryHasContactDependenciesPropertyName = "HasContactDependencies";
		private const int ConstGalleryRequestTimeoutInMs = 30000;

		private readonly IAppsContactRepository _contactRepository = null;
		private readonly AppsWebApiClient _appsWebApiClient = null;
		private readonly IServiceBusPublisher _serviceBusPublisher = null;
		private readonly IContactDependenciesConfigurationRepository _contactDependenciesConfigurationRepository = null;
		private readonly Func<string, IContactDependenciesCheckingRepository> _contactDependenciesCheckingRepositoryFactory = null;
		private readonly IAppsSiteboxSettingsRepository _siteboxSettingsRepository = null;
		private readonly IAppsSiteHostingSettingsRepository _siteHostingSettingsRepository = null;
		private readonly IAppsContactManagementLogRepository _logRepository = null;

		public AppsContactManager(IAppsContactRepository contactRepository,
			AppsWebApiClient appsWebApiClient,
			IContactDependenciesConfigurationRepository contactDependenciesConfigurationRepository,
			Func<string, IContactDependenciesCheckingRepository> contactDependenciesCheckingRepositoryFactory,
			IAppsSiteboxSettingsRepository siteboxSettingsRepository,
			IAppsSiteHostingSettingsRepository siteHostingSettingsRepository,
			IServiceBusPublisher serviceBusPublisher,
			IAppsContactManagementLogRepository appsContactManagementLogRepository
		) {
			_contactRepository = contactRepository;
			_appsWebApiClient = appsWebApiClient;
			_serviceBusPublisher = serviceBusPublisher;
			_contactDependenciesConfigurationRepository = contactDependenciesConfigurationRepository;
			_contactDependenciesCheckingRepositoryFactory = contactDependenciesCheckingRepositoryFactory;
			_siteboxSettingsRepository = siteboxSettingsRepository;
			_siteHostingSettingsRepository = siteHostingSettingsRepository;
			_logRepository = appsContactManagementLogRepository;
		}

		public async Task<PromiseResultModel<bool>> SetAccountContactsAsync(int accountId, List<AppsDealerContactUpsertModel> vwContacts) {
			Dictionary<int, AppsDealerContact> contactsToSet = new Dictionary<int, AppsDealerContact>();
			AppsContactBuilder contactBuilder = new AppsContactBuilder(accountId);

			foreach (AppsDealerContactUpsertModel contactUpsertModel in vwContacts) {
				AppsDealerContact contact = contactBuilder
					.SetUpdateModel(contactUpsertModel)
					.Build();

				contactsToSet.Add(contact.ContactId, contact);
			}

			return await SetAccountContactsAsync(accountId, contactsToSet);
		}

		public async Task<PromiseResult<bool>> SetAccountContactByUserInfoAsync(IUserUpdatedEvent userUpdatedEvent) {
			try {
				var contactToUpdate = await _contactRepository.GetContactAsync(userUpdatedEvent.ContactId);
				if (contactToUpdate == null)
					return PromiseResult<bool>.Done(false);

				bool hasChanges =
					!string.Equals(contactToUpdate.FirstName, userUpdatedEvent.FirstName, StringComparison.Ordinal) ||
					!string.Equals(contactToUpdate.LastName, userUpdatedEvent.LastName, StringComparison.Ordinal) ||
					!string.Equals(contactToUpdate.Email, userUpdatedEvent.Email, StringComparison.OrdinalIgnoreCase);

				if (!hasChanges)
					return PromiseResult<bool>.Done(false);

				contactToUpdate.FirstName = userUpdatedEvent.FirstName;
				contactToUpdate.LastName = userUpdatedEvent.LastName;
				contactToUpdate.Email = userUpdatedEvent.Email;

				await _contactRepository.UpdateContactAsync(contactToUpdate);
				string correlationId = Guid.NewGuid().ToString();
				_serviceBusPublisher.Publish<IContactUpdatedEvent, ContactUpdatedEvent>(ContactConverter.ConvertToContactUpdateEvent(contactToUpdate, correlationId));

				return PromiseResult<bool>.Done(true);
			} catch (Exception ex) {
				return PromiseResult<bool>.Failed(ex);
			}
		}

		private async Task<PromiseResultModel<bool>> SetAccountContactsAsync(int accountId, Dictionary<int, AppsDealerContact> contacts) {
			try {
				Dictionary<int, AppsDealerContact> existedContacts = (await _contactRepository.GetContactsAsync(accountId)).ToDictionary(x => x.ContactId);

				bool isModified = false;

				//upsert contacts
				List<AppsDealerContact> contactsToUpdate = new List<AppsDealerContact>();
				List<AppsDealerContact> contactsToInsert = new List<AppsDealerContact>();

				AppsDealerContact existingContact = null;
				foreach (AppsDealerContact contact in contacts.Values) {
					if (!existedContacts.TryGetValue(contact.ContactId, out existingContact)) {
						contactsToInsert.Add(contact);
					} else if (!existingContact.DeepCompare(contact).AreEquals) {
						contact.SetVersion(existingContact.Version + 1);
						contactsToUpdate.Add(contact);
					}
				}

				string correlationId = Guid.NewGuid().ToString();
				foreach (AppsDealerContact contact in contactsToInsert) {
					await _contactRepository.InsertOrReplaceContactAsync(contact).ConfigureAwait(false);
					_serviceBusPublisher.Publish<IContactCreatedEvent, ContactCreatedEvent>(ContactConverter.ConvertToContactCreatedEvent(contact, correlationId));
				}

				_logRepository.LogCreatedContactIds(contactsToInsert?.Select(x => x.ContactId).ToList());

				foreach (AppsDealerContact contact in contactsToUpdate) {
					await _contactRepository.InsertOrReplaceContactAsync(contact).ConfigureAwait(false);
					_serviceBusPublisher.Publish<IContactUpdatedEvent, ContactUpdatedEvent>(ContactConverter.ConvertToContactUpdateEvent(contact, correlationId));
				}

				_logRepository.LogUpdatedContactIds(contactsToUpdate?.Select(x => x.ContactId).ToList());

				isModified = contactsToInsert.Any() || contactsToUpdate.Any();

				//delete contacts
				List<AppsDealerContact> contactsToDelete = existedContacts.Where(x => !contacts.ContainsKey(x.Key)).Select(x => x.Value).ToList();
				if (contactsToDelete.Count > 0) {
					IEnumerable<int> contactIdsToDelete = contactsToDelete.Select(x => x.ContactId);
					await _contactRepository.DeleteContactsAsync(contactIdsToDelete);

					isModified = true;

					foreach (AppsDealerContact contact in contactsToDelete) {
						await _serviceBusPublisher.PublishAsync<IContactDeletedEvent, ContactDeletedEvent>(ContactConverter.ConvertToContactDeletedEvent(contact, correlationId));
					}
				}

				_logRepository.LogDeletedContactIds(contactsToDelete?.Select(x => x.ContactId).ToList());

				return PromiseResultModel<bool>.SuccessResult(isModified);
			} catch (Exception ex) {
				return PromiseResultModel<bool>.ExceptionResult(new Exception($"Exception occured on set account contacts for {accountId}", ex), true);
			}
		}

		public async Task<PromiseResultModel<bool>> SetAccountContactsMapAsync(int accountId, AppsDealerContactsMapUpsertModel contactsMap) {
			try {
				AppsContactsMapBuilder contactsMapBuilder = new AppsContactsMapBuilder(accountId);
				AppsDealerContactsMap contactsMapToSet = contactsMapBuilder.SetUpdateModel(contactsMap).Build();

				AppsDealerContactsMap existingContactsMap = _contactRepository.GetContactsMap(accountId);

				string correlationId = Guid.NewGuid().ToString();
				if (existingContactsMap == null) {
					await _contactRepository.InsertOrReplaceContactsMapAsync(contactsMapToSet).ConfigureAwait(false);
					await _serviceBusPublisher.PublishAsync<IContactsMapCreatedEvent, ContactsMapCreatedEvent>(
						new ContactsMapCreatedEvent() {
							AccountId = contactsMapToSet.AccountId,
							LocalEBayContactId = contactsMapToSet.GetCampaignDefaultContact(ContactMapping.DealerCampaignConstants.LocalEbayId),
							CorrelationId = correlationId
						}
					);
				} else {
					ObjectsComparer.Comparer<AppsDealerContactsMap> contactsMapCompare = new ObjectsComparer.Comparer<AppsDealerContactsMap>();
					if (!contactsMapCompare.Compare(contactsMapToSet, existingContactsMap)) {
						await _contactRepository.UpdateContactsMapAsync(contactsMapToSet).ConfigureAwait(false);
						await _serviceBusPublisher.PublishAsync<IContactsMapUpdatedEvent, ContactsMapUpdatedEvent>(
							new ContactsMapUpdatedEvent() {
								AccountId = contactsMapToSet.AccountId,
								LocalEBayContactId = contactsMapToSet.GetCampaignDefaultContact(ContactMapping.DealerCampaignConstants.LocalEbayId),
								CorrelationId = correlationId
							}
						);
					}
				}

				return PromiseResultModel<bool>.SuccessResult(true);
			} catch (Exception ex) {
				return PromiseResultModel<bool>.ExceptionResult(new Exception($"Exception occured on set account contacts map for {accountId}", ex), true);
			}
		}

		public async Task<PromiseResultModel<List<AppsDealerContact>>> GetAccountContactsAsync(int accountId) {
			try {
				List<AppsDealerContact> contacts = await _contactRepository.GetContactsAsync(accountId);

				return PromiseResultModel<List<AppsDealerContact>>.SuccessResult(contacts);
			} catch (Exception ex) {
				return PromiseResultModel<List<AppsDealerContact>>.ExceptionResult(new Exception($"Exception occured on get account contacts for {accountId}", ex), true);
			}
		}

		public async Task<PromiseResultModel<ContactDependenciesResponseModel>> CheckContactDependenciesAsync(int accountId, int contactId) {
			try {
				ContactDependenciesResponseModel responseModel = new ContactDependenciesResponseModel() {
					HasDependencies = false,
					Dependencies = new List<string>()
				};

				ContactDependenciesConfiguration contactDependenciesConfiguration = await _contactDependenciesConfigurationRepository.GetDependenciesConfigurationAsync();

				//apps dependencies
				foreach (ContactDependenciesConfigurationConnection connection in contactDependenciesConfiguration.Connections) {
					IContactDependenciesCheckingRepository repository = _contactDependenciesCheckingRepositoryFactory(connection.ConnectionString);
					foreach (ContactDependenciesConfigurationConnectionQuery query in connection.Queries) {
						try {
							bool hasDependencies = await repository.CheckDependencyAsync(query.CollectionName, query.FilterPath, contactId);

							if (!hasDependencies) {
								continue;
							}
						} catch (Exception ex) {
							return PromiseResultModel<ContactDependenciesResponseModel>.ExceptionResult(
								new Exception(
									$"Exception occurred on check contact dependencies for ContactId - {contactId}, AccountId - {accountId}. Database - {connection.Name}, Collection - {query.CollectionName}", ex
								),
								true
							);
						}

						responseModel.HasDependencies = true;
						responseModel.Dependencies.Add($"{query.CollectionName} collection in {connection.Name} database");
					}
				}

				//gallery aws dependencies
				//primary sitebox
				PromiseResultModel<GalleryContactDependenciesResponseModel> galleryCheckingResult = await CheckGalleryContactDependenciesAsync(accountId, contactId, false);
				if (galleryCheckingResult.IsRejected) {
					return PromiseResultModel<ContactDependenciesResponseModel>.ExceptionResult(galleryCheckingResult.RaisedException, true, responseModel);
				} else if (galleryCheckingResult.Model.IsSiteboxDefined && galleryCheckingResult.Model.HasContactDependencies) {
					responseModel.HasDependencies = true;
					responseModel.Dependencies.Add($"Gallery has dependencies on primary sitebox - {galleryCheckingResult.Model.SiteboxName}");
				}

				//migration sitebox
				galleryCheckingResult = await CheckGalleryContactDependenciesAsync(accountId, contactId, true);
				if (galleryCheckingResult.IsRejected) {
					return PromiseResultModel<ContactDependenciesResponseModel>.ExceptionResult(galleryCheckingResult.RaisedException, true, responseModel);
				} else if (galleryCheckingResult.Model.IsSiteboxDefined && galleryCheckingResult.Model.HasContactDependencies) {
					responseModel.HasDependencies = true;
					responseModel.Dependencies.Add($"Gallery has dependencies on migration sitebox - {galleryCheckingResult.Model.SiteboxName}");
				}

				return PromiseResultModel<ContactDependenciesResponseModel>.SuccessResult(responseModel);
			} catch (Exception ex) {
				return PromiseResultModel<ContactDependenciesResponseModel>.ExceptionResult(new Exception($"Exception occurred on check contact dependencies for ContactId - {contactId}, AccountId - {accountId}", ex), true);
			}
		}

		private async Task<PromiseResultModel<GalleryContactDependenciesResponseModel>> CheckGalleryContactDependenciesAsync(int accountId, int contactId, bool isMigrationSitebox) {
			string url = GetAwsGallerySystemBaseUrl(accountId, isMigrationSitebox, out string siteboxName);

			GalleryContactDependenciesResponseModel result = new GalleryContactDependenciesResponseModel() {
				HasContactDependencies = false,
				SiteboxName = siteboxName,
				IsSiteboxDefined = !string.IsNullOrEmpty(siteboxName)
			};

			if (!result.IsSiteboxDefined || string.IsNullOrEmpty(url)) { //not hosted on sitebox or sitebox has no internal url
				return PromiseResultModel<GalleryContactDependenciesResponseModel>.SuccessResult(result);
			}

			url += $"checkcontactdependencies.aspx?accountid={accountId}&contactid={contactId}";

			HttpWebRequest request = (HttpWebRequest) WebRequest.Create(url);
			request.Method = WebRequestMethods.Http.Get;
			request.Timeout = ConstGalleryRequestTimeoutInMs;
			request.AllowAutoRedirect = true;
			request.MaximumAutomaticRedirections = 1;

			try {
				using (HttpWebResponse response = (HttpWebResponse) await request.GetResponseAsync()) {
					using (Stream responseStream = response.GetResponseStream()) {
						using (StreamReader reader = new StreamReader(responseStream)) {
							string responseContent = reader.ReadToEnd();
							if (string.IsNullOrEmpty(responseContent)) {
								throw new Exception("Response content is empty");
							}

							try {
								JObject json = JObject.Parse(responseContent);

								if (!json.ContainsKey(ConstGalleryJsonRootPropertyName)) {
									throw new Exception($"Response content does not have {ConstGalleryJsonRootPropertyName} property");
								}

								if (!(json[ConstGalleryJsonRootPropertyName] is JObject pageDataNode)) {
									throw new Exception($"Invalid {ConstGalleryJsonRootPropertyName} property");
								}

								if (!pageDataNode.ContainsKey(ConstGalleryHasContactDependenciesPropertyName)) {
									throw new Exception($"Response content does not have {ConstGalleryHasContactDependenciesPropertyName} property");
								}

								if (!bool.TryParse((string) pageDataNode[ConstGalleryHasContactDependenciesPropertyName], out bool hasContactDependencies)) {
									throw new Exception($"Couldn't parse {ConstGalleryHasContactDependenciesPropertyName} property to boolean type");
								}

								result.HasContactDependencies = hasContactDependencies;
							} catch (Exception ex) {
								throw new Exception($"Exception occurred at parsing response content. Content: {responseContent}", ex);
							}
						}
					}

					return PromiseResultModel<GalleryContactDependenciesResponseModel>.SuccessResult(result);
				}
			} catch (Exception ex) {
				return PromiseResultModel<GalleryContactDependenciesResponseModel>.ExceptionResult(new Exception($"Exception occurred on gallery call. Url: {url}, Method: GET", ex));
			}
		}

		private string GetAwsGallerySystemBaseUrl(int siteId, bool isMigrationSiteboxCase, out string siteboxName) {
			string url = "";
			siteboxName = "";
			SiteboxSettings siteboxSettings = null;

			SiteHostingSettings siteHostingSettings = _siteHostingSettingsRepository.GetSiteHostingSettings(siteId);

			if (siteHostingSettings != null) {
				if (isMigrationSiteboxCase && siteHostingSettings.MigrationSiteboxId > 0) {
					siteboxSettings = _siteboxSettingsRepository.GetSitebox(siteHostingSettings.MigrationSiteboxId);
				} else if (!isMigrationSiteboxCase) {
					siteboxSettings = _siteboxSettingsRepository.GetSitebox(siteHostingSettings.SiteboxId);
				}
			}

			if (siteboxSettings == null) {
				return "";
			}

			siteboxName = siteboxSettings.Name;

			if (!string.IsNullOrEmpty(siteboxSettings.AwsGalleryInternalUrl)) {
				url = $"{Constants.ConstHttpSheme}{siteboxSettings.AwsGalleryInternalUrl}/";
			}

			return url;
		}
	}
}