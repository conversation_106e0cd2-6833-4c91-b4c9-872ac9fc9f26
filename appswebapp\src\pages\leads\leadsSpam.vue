<template>
  <div class="position-relative">
    <div class="d-flex flex-row">
      <h4 class="mt-3">Spam</h4>
      <!-- Pagination -->
      <paging
        class="custom-leads-spam-paging d-none d-md-block p-0"
        :pageNumber="filter.page"
        :pageSize="filter.pageSize"
        :totalItems="itemsTotalCount"
        @numberChanged="pageChanged"
        @changePageSize="changePageSize"
      />
    </div>
    <b-tabs v-model="selectedTab" :value="selectedTab" class="nav-tabs-top nav-responsive-sm" no-fade>
      <b-tab v-for='tab in spamTabTypes' :key='tab.value' :title="tab.label">
        <div>
          <leads-spam-filter-form :id='id' @applyFilter='applyFilter' :filter='filter' :type='tab.value'/>
        </div>
      </b-tab>
    </b-tabs>
    <div v-if="!isLoading && itemsTotalCount > 0">
      <b-card>
        <leads-spam-message-listing :sort='filter.sort' @sortChange='spamMessageSortChange' @resubmit='resubmitSpamMessage' @delete='deleteSpamMessage' :items='spamMessages' v-if='selectedTab === spamTabTypes.spamMessages.value'/>
        <leads-spam-filter-listing :sort='filter.sort' @sortChange='spamFilterSortChange' @edit='editSpamFilter' @delete='deleteSpamFilter' :items='spamFilters' v-if='selectedTab === spamTabTypes.spamFilters.value'/>
        <paging
          class="p-0"
          :pageNumber="filter.page"
          :pageSize="filter.pageSize"
          :totalItems="itemsTotalCount"
          titled
          pageSizeSelector
          @numberChanged="pageChanged"
          @changePageSize="changePageSize"
        />
      </b-card>
    </div>
    <div v-else-if='isLoading' class="my-5">
      <loader size='lg'/>
    </div>
    <div v-else class="my-2">
      <span class="text-muted">Not Found</span>
    </div>
  </div>
</template>

<script>
import leadsSpamFilterForm from '@/components/leads/spam/leadsSpamFilterForm'
import leadsSpamFilterListing from '@/components/leads/spam/leadsSpamFilterListing'
import leadsSpamMessageListing from '@/components/leads/spam/leadsSpamMessageListing'
import loader from '@/components/_shared/loader'
import paging from '@/components/_shared/paging'
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import { spamTabTypes } from '@/shared/leads/common'

const defaultFilters = new ObjectSchema({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  accountId: { type: Number, default: 0 },
  communicationtype: { type: Number, default: 0 },
  spamreasontype: { type: Number, default: 0 },
  applicationtype: { type: Number, default: 0 },
  fieldtype: { type: Number, default: 0 },
  email: { type: String, default: '' },
  search: { type: String, default: '' },
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  sort: { type: Number, default: 0 },
  typeTab: { type: Number, default: 0 }
})
const queryHelper = new QueryStringHelper(defaultFilters)

export default {
  name: 'leads-spam',
  metaInfo: {
    title: 'Leads Spam'
  },
  data () {
    return {
      isLoading: true,
      id: '',
      filter: defaultFilters.getObject(),
      spamMessages: [],
      spamFilters: [],
      itemsTotalCount: 0,
      spamTabTypes
    }
  },
  computed: {
    selectedTab: {
      get () {
        if (this.filter.typeTab >= 0 && this.filter.typeTab <= 1) {
          return this.filter.typeTab
        }

        return 0
      },
      set (value) {
        this.filter.typeTab = value
        this.filter.sort = 0
        this.itemsTotalCount = 0
        this.spamFilters = []
        this.spamMessages = []
        this.synchronizeUrlAndReload()
      }
    }
  },
  components: {
    'leads-spam-filter-form': leadsSpamFilterForm,
    'leads-spam-filter-listing': leadsSpamFilterListing,
    'leads-spam-message-listing': leadsSpamMessageListing,
    'paging': paging,
    'loader': loader
  },
  created () {
    this.filter = queryHelper.parseQueryStringToObject(this.$router)
    this.loadContent()
  },
  methods: {
    loadContent () {
      switch (this.selectedTab) {
        case this.spamTabTypes.spamMessages.value:
          this.populateSpamMessages()
          return
        case this.spamTabTypes.spamFilters.value:
          this.populateSpamFilters()
      }
    },
    synchronizeUrlAndReload () {
      queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.isLoading = true
      this.loadContent()
    },
    applyFilter () {
      this.filter.page = 1
      this.synchronizeUrlAndReload()
    },
    pageChanged (newPage) {
      this.filter.page = newPage
      this.synchronizeUrlAndReload()
    },
    changePageSize (newSize) {
      this.filter.pageSize = newSize
      this.filter.page = 1
      this.synchronizeUrlAndReload()
    },
    spamFilterSortChange (newSort) {
      this.filter.sort = newSort
      queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.loadContent()
    },
    spamMessageSortChange (newSort) {
      this.filter.sort = newSort
      queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.loadContent()
    },
    deleteSpamFilter (id) {
      this.$store.dispatch('leads/deleteSpamFilter', id).then(() => {
        this.$toaster.success('Spam Filter Successfully Deleted')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Failed on deleting the spam filter')
        this.$logger.handleError(ex, `Cannot delete the spam filter with id: ${id}`)
      }).finally(() => {
        this.synchronizeUrlAndReload()
      })
    },
    deleteSpamMessage (id) {
      this.$store.dispatch('leads/deleteSpamMessage', id).then(() => {
        this.$toaster.success('Spam Message Successfully Deleted')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Failed on deleting the spam message')
        this.$logger.handleError(ex, `Cannot delete the spam message with id: ${id}`)
      }).finally(() => {
        this.synchronizeUrlAndReload()
      })
    },
    resubmitSpamMessage (id) {
      this.$store.dispatch('leads/reprocessSpamMessage', id).then(() => {
        this.$toaster.success('Spam Message Successfully Resubmitted')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Failed on resubmitting the spam message')
        this.$logger.handleError(ex, `Cannot reprocess the spam message with id: ${id}`)
      }).finally(() => {
        this.synchronizeUrlAndReload()
      })
    },
    editSpamFilter (id) {
      this.id = id
    },
    populateSpamMessages () {
      this.$store.dispatch('leads/getSpamMessages', this.filter).then(res => {
        this.spamMessages = res.data.spamMessages
        this.itemsTotalCount = res.data.totalCount
      }).catch(ex => {
        this.$toaster.exception(ex, `Failed on receiving the list of spam messages`)
        this.$logger.handleError(ex, `Cannot get the list of spam messages`, {requestFilter: this.filter})
      }).finally(() => {
        this.isLoading = false
      })
    },
    populateSpamFilters () {
      this.$store.dispatch('leads/getSpamFilters', this.filter).then(res => {
        this.spamFilters = res.data.spamFilters
        this.itemsTotalCount = res.data.totalCount
      }).catch(ex => {
        this.$toaster.ex(ex, `Failed on receiving the list of spam filters`)
        this.$logger.handleError(ex, `Cannot get the list of spam filters`, {requestFilter: this.filter})
      }).finally(() => {
        this.isLoading = false
      })
    }
  }
}
</script>

<style>
.custom-leads-spam-paging {
  position: absolute;
  right: 5px;
  top: 40px;
  z-index: 2;
}
</style>
