﻿using System.ComponentModel.DataAnnotations;

namespace EBizAutos.Apps.Authentication.CommonLib.Models {
	public class EmailSettings {
		[Required(ErrorMessage = "Email from address is required.")]
		[EmailAddress(ErrorMessage = "Invalid email address format.")]
		public string From { get; set; }

		[Required(ErrorMessage = "OTP email subject is required.")]
		[StringLength(200, ErrorMessage = "OTP email subject cannot exceed 200 characters.")]
		public string OtpSubject { get; set; }

		[Required(ErrorMessage = "OTP email body template is required.")]
		public string OtpBodyTemplate { get; set; }
	}
}