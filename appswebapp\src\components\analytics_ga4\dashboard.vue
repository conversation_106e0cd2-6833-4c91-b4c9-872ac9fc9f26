<template>
  <div>

    <h4 class="d-flex justify-content-between align-items-center w-100 font-weight-bold pb-0 mb-4">
      <div>Dashboard</div>
      <b-dropdown right text="September 23, 2018" variant="primary btn-round" class="d-block" size="md">
        <div role="group" class="ddown-menu p-3" style="width:280px;">
          <b-form>
            <b-row class="mb-3 align-items-center">
              <b-col cols="3">
                <label for="daterange-year">Year</label>
              </b-col>
              <b-col cols="9">
                <b-form-input id="daterange-year" size="md" type="text" value="2018"></b-form-input>
              </b-col>
            </b-row>
            <b-row class="mb-3 align-items-center">
              <b-col cols="3">
                <label for="daterange-quarter">Quarter</label>
              </b-col>
              <b-col cols="9">
                <b-form-select id="daterange-quarter" size="md" v-model="selectedQuarter" :options="optionsQuarter" />
              </b-col>
            </b-row>
            <b-row class="mb-3 align-items-center">
              <b-col cols="3">
                <label for="daterange-month">Month</label>
              </b-col>
              <b-col cols="9">
                <b-form-select id="daterange-month" size="md" v-model="selectedMonth" :options="optionsMonth" />
              </b-col>
            </b-row>
            <b-row>
              <b-col>
                <b-button variant="primary btn-round" class="ml-auto d-block">Apply</b-button>
              </b-col>
            </b-row>
          </b-form>
        </div>
      </b-dropdown>
    </h4>

    <div class="row no-gutters white-bg border rounded">

      <div class="col-6 col-sm-6 col-xl-3 border-bottom border-top-0 border-right border-left-0 border-xl-bottom-0 border-sm-right">

        <b-card no-body class="rounded-0 border-0 m-0">
          <b-card-body class="d-flex justify-content-between align-items-center">
            <div>
              <div class="h5 mb-1">Visitors</div>
              <div>
                <span class="text-xlarge">44,078</span>
                <span class="text-danger font-weight-bold">-1%</span>
              </div>
              <div class="small opacity-75 mt-2">In the last 7 days</div>
            </div>
            <i class="ion ion-ios-people h1 m-0 opacity-25 d-none d-sm-inline"></i>
          </b-card-body>
        </b-card>

      </div>

      <div class="col-6 col-sm-6 col-xl-3 border-bottom border-top-0 border-right-0 border-left-0 border-xl-bottom-0 border-xl-right">

        <b-card no-body class="rounded-0 border-0 m-0">
          <b-card-body class="d-flex justify-content-between align-items-center">
            <div>
              <div class="h5 mb-1">Leads</div>
              <div>
                <span class="text-xlarge">1,698</span>
                <span class="font-weight-bold text-danger">-3%</span>
              </div>
              <div class="small opacity-75 mt-2">In the last 7 days</div>
            </div>
            <i class="ion ion-ios-chatboxes h1 m-0 opacity-25 d-none d-sm-inline"></i>
          </b-card-body>
        </b-card>

      </div>

      <div class="col-6 col-sm-6 col-xl-3 border-bottom border-top-0 border-right border-left-0 border-xl-bottom-0 border-sm-right">

        <b-card no-body class="rounded-0 border-0 m-0">
          <b-card-body class="d-flex justify-content-between align-items-center">
            <div>
              <div class="h5 mb-1">Bounce Rate</div>
              <div>
                  <span class="text-xlarge">3.8%</span>
                  <span class="font-weight-bold text-success">+18%</span>
              </div>
              <div class="small opacity-75 mt-2">In the last 7 days</div>
            </div>
            <i class="ion ion-ios-laptop h1 m-0 opacity-25 d-none d-sm-inline"></i>
          </b-card-body>
        </b-card>

      </div>

      <div class="col-6 col-sm-6 col-xl-3 border-0">

        <b-card no-body class="rounded-0 border-0 m-0">
          <b-card-body class="d-flex justify-content-between align-items-center">
            <div>
              <div class="h5 mb-1">Engagement</div>
              <div>
                  <span class="text-xlarge">0:05:06</span>
                  <span class="font-weight-bold text-success">+88%</span>
              </div>
              <div class="small opacity-75 mt-2">In the last 7 days</div>
            </div>
            <i class="ion ion-ios-time h1 m-0 opacity-25 d-none d-sm-inline"></i>
          </b-card-body>
        </b-card>

      </div>

    </div>

    <div class="row mt-3">

      <div class="col-lg-12 col-xl-7">

        <div class="row">

          <div class="col-12 col-md-6 col-lg-6 col-xl-12">
            <!-- Website Overview -->
            <b-card no-body class="w-100 mb-3">
              <b-card-header header-tag="h5" class="with-elements border-0">
                <div class="card-header-title">Website Overview</div>
                <div class="card-header-elements ml-auto">
                  <b-dropdown right variant="outline-secondary icon-btn btn-round md-btn-flat" size="sm" no-caret>
                    <template slot="button-content">
                      <i class="ion ion-ios-more m-0"></i><span class="sr-only">Actions</span>
                    </template>
                    <b-dropdown-header>Filters</b-dropdown-header>
                    <b-dropdown-item class="d-flex" active>All <i class="ion ion-ios-apps opacity-50 align-self-center ml-auto"></i></b-dropdown-item>
                    <b-dropdown-item class="d-flex">Desktop <i class="ion ion-ios-laptop opacity-50 align-self-center ml-auto"></i></b-dropdown-item>
                    <b-dropdown-item class="d-flex">Mobile <i class="ion ion-ios-phone-portrait opacity-50 align-self-center ml-auto mr-1"></i></b-dropdown-item>
                    <b-dropdown-divider></b-dropdown-divider>
                    <div class="my-2 mx-3">
                      <b-button variant="outline-secondary" size="sm" class="d-block" href="website-overview">Overview Details</b-button>
                    </div>
                  </b-dropdown>
                </div>
              </b-card-header>
              <div>
                <vue-echart :options="lineOptions" :auto-resize="true"></vue-echart>
              </div>
            </b-card>
            <!-- / Website Overview -->
          </div>

          <div class="col-12 col-md-6 col-lg-6 col-xl-12">
            <!-- Traffic Sources -->
            <b-card no-body class="w-100 mb-3 mb-lg-0">
              <b-card-header header-tag="h5" class="with-elements border-0">
                <div class="card-header-title">Traffic Sources</div>
                <div class="card-header-elements ml-auto">
                  <b-button variant="outline-secondary" size="sm" class="d-block" href="traffic-sources">Details <i class="ion ion-ios-arrow-forward"></i></b-button>
                </div>
              </b-card-header>
              <div>
                <vue-echart :options="multiLineOptions" :auto-resize="true"></vue-echart>
              </div>
            </b-card>
            <!-- / Traffic Sources -->
          </div>

        </div>

      </div>

      <div class="col-lg-12 col-xl-5">

        <b-card no-body class="mb-0 mb-xl-3 widget-sessions-device">
          <b-card-header header-tag="h5" class="border-0 pb-0">
              <span class="card-header-title">Sessions by Device</span>
          </b-card-header>
          <div class="row">
            <div class="col-md-6 col-xl-12">
              <div class="d-flex align-items-center position-relative mt-3 h-100">
                <vue-echart :options="pieOptions" :auto-resize="true"></vue-echart>
              </div>
            </div>
          </div>
        </b-card>

      </div>

    </div>

<!-- New Chart Idea Do Not Remove ;)
    <div class="row mt-3">

      <div class="col-sm-6 col-xl-4">

        <b-card no-body class="mb-4 widget04 widget-display-remarketing position-relative">
          <b-card-header header-tag="h5" class="with-elements border-0 position-relative">
              <div class="card-header-title">Display &amp; Remarketing</div>
              <div class="card-header-elements ml-auto">
                <b-button variant="outline-secondary" size="sm" href="display-remarketing"><i class="ion ion-ios-arrow-forward"></i></b-button>
              </div>
          </b-card-header>
          <div class="widget04_visual position-absolute bg-warning w-100" style="height: 250px;">
            <vue-echart :options="multiLineOptions" :auto-resize="true"></vue-echart>
          </div>
          <div class="widget04_stats d-flex mt-5 pb-4 position-relative">
            <div class="col-10 offset-1">
              <div class="row mt-5">
                <div class="col-6">
                  <div class="widget04_item py-3 pl-3 bg-white shadow">
                    <span class="item_icon d-block">
                      <i class="ion ion-ios-wallet h1 m-0 opacity-75"></i>
                    </span>
                    <span class="item_amount d-block h5 mt-5 mb-0">
                      $1,254
                    </span>
                    <span class="item_label d-block small opacity-75">
                      Spend
                    </span>
                  </div>
                  <div class="widget04_item py-3 pl-3 mt-3 bg-white shadow">
                    <span class="item_icon d-block">
                      <i class="ion ion-ios-chatboxes h1 m-0 opacity-75"></i>
                    </span>
                    <span class="item_amount d-block h5 mt-5 mb-0">
                      827
                    </span>
                    <span class="item_label d-block small opacity-75">
                      Leads
                    </span>
                  </div>
                </div>
                <div class="col-6">
                  <div class="widget04_item py-3 pl-3 bg-white shadow">
                    <span class="item_icon d-block">
                      <i class="ion ion-ios-people h1 m-0 opacity-75"></i>
                    </span>
                    <span class="item_amount d-block h5 mt-5 mb-0">
                      44,078
                    </span>
                    <span class="item_label d-block small opacity-75">
                      Visits
                    </span>
                  </div>
                  <div class="widget04_item py-3 pl-3 mt-3 bg-white shadow">
                    <span class="item_icon d-block">
                      <i class="ion ion-ios-cash h1 m-0 opacity-75"></i>
                    </span>
                    <span class="item_amount d-block h5 mt-5 mb-0">
                      $27.84
                    </span>
                    <span class="item_label d-block small opacity-75">
                      Cost Per Lead
                    </span>
                  </div>
                </div>
              </div>
              <div class="widget04_description opacity-75 mt-3">
                Performance and Cost Metrics for your Google Adwords campaigns
              </div>
            </div>
          </div>
        </b-card>

      </div>

    </div>
-->
  </div>

</template>

<style src="@/vendor/styles/pages/analytics.scss" lang="scss"></style>

<script>
import ECharts from 'vue-echarts/components/ECharts.vue'

import 'echarts/lib/chart/line'
import 'echarts/lib/chart/bar'
import 'echarts/lib/chart/pie'
import 'echarts/lib/component/tooltip'
import 'echarts/lib/component/legend'

const colors = ['#dc3545', '#28a745', '#007bff', '#ffc107', '#7751bd', '#a7b61a', '#f3e562', '#ff9800', '#ff5722', '#ff4514', '#647c8a', '#3f51b5', '#2196f3', '#00b862', '#afdf0a']

export default {
  name: 'charts-vue-echarts',
  metaInfo: {
    title: 'Analytics - Dashboard'
  },
  components: {
    'vue-echart': ECharts
  },
  data: () => ({
    selectedQuarter: '0',
    optionsQuarter: [
      { value: '0', disabled: false, text: 'Not Selected' },
      { value: '1', disabled: false, text: 'Quarter #1 (Jan, Feb, Mar)' },
      { value: '2', disabled: false, text: 'Quarter #2 (Apr, May, Jun)' },
      { value: '3', disabled: false, text: 'Quarter #3 (Jul, Aug, Sep)' },
      { value: '4', disabled: true, text: 'Quarter #4 (Oct, Nov, Dec)' }
    ],
    selectedMonth: '9',
    optionsMonth: [
      { value: '0', disabled: false, text: 'Not Selected' },
      { value: '1', disabled: false, text: 'January' },
      { value: '2', disabled: false, text: 'February' },
      { value: '3', disabled: false, text: 'March' },
      { value: '4', disabled: false, text: 'April' },
      { value: '5', disabled: false, text: 'May' },
      { value: '6', disabled: false, text: 'June' },
      { value: '7', disabled: false, text: 'July' },
      { value: '8', disabled: false, text: 'August' },
      { value: '9', disabled: false, text: 'September' },
      { value: '10', disabled: false, text: 'October' },
      { value: '11', disabled: false, text: 'November' },
      { value: '12', disabled: false, text: 'December' }
    ],
    sessionsDesktopOptions: {
      grid: {
        left: '0',
        top: '0',
        right: '0',
        bottom: '0'
      },
      color: '#7751bd',
      tooltip: {
        show: false,
        trigger: 'axis'
      },
      xAxis: {
        data: ['Sept. 17, 2018', 'Sept. 18, 2018', 'Sept. 19, 2018', 'Sept. 20, 2018', 'Sept. 21, 2018', 'Sept. 22, 2018', 'Sept. 23, 2018'],
        show: false,
        boundaryGap: false,
        axisLine: {
          lineStyle: { color: 'rgba(0, 0, 0, .08)' }
        },
        axisLabel: { color: 'rgba(0, 0, 0, .5)' }
      },
      yAxis: {
        show: false,
        boundaryGap: false,
        splitLine: { show: false },
        axisLine: {
          lineStyle: { color: 'rgba(0, 0, 0, .08)' }
        },
        axisLabel: { color: 'rgba(0, 0, 0, .5)' }
      },
      series: [
        {
          name: 'Desktop',
          type: 'line',
          data: [277, 569, 713, 428, 411, 668, 324],
          areaStyle: {},
          smooth: 0.4,
          symbolSize: 7,
          showSymbol: false
        }
      ],
      animationDuration: 2000
    },
    sessionsMobileOptions: {
      grid: {
        left: '0',
        top: '0',
        right: '0',
        bottom: '0'
      },
      color: '#ffc107',
      tooltip: {
        show: false,
        trigger: 'axis'
      },
      xAxis: {
        data: ['Sept. 17, 2018', 'Sept. 18, 2018', 'Sept. 19, 2018', 'Sept. 20, 2018', 'Sept. 21, 2018', 'Sept. 22, 2018', 'Sept. 23, 2018'],
        show: false,
        boundaryGap: false,
        axisLine: {
          lineStyle: { color: 'rgba(0, 0, 0, .08)' }
        },
        axisLabel: { color: 'rgba(0, 0, 0, .5)' }
      },
      yAxis: {
        show: false,
        boundaryGap: false,
        splitLine: { show: false },
        axisLine: {
          lineStyle: { color: 'rgba(0, 0, 0, .08)' }
        },
        axisLabel: { color: 'rgba(0, 0, 0, .5)' }
      },
      series: [
        {
          name: 'Mobile',
          type: 'line',
          data: [257, 369, 513, 228, 311, 868, 924],
          areaStyle: {},
          smooth: 0.4,
          symbolSize: 7,
          showSymbol: false
        }
      ],
      animationDuration: 2000
    },
    lineOptions: {
      grid: {
        left: '0',
        top: '20%',
        right: '0',
        bottom: '0'
      },
      color: colors,
      tooltip: {
        trigger: 'axis',
        textStyle: {
          fontSize: 13
        }
      },
      xAxis: {
        data: ['Sept. 17, 2018', 'Sept. 18, 2018', 'Sept. 19, 2018', 'Sept. 20, 2018', 'Sept. 21, 2018', 'Sept. 22, 2018', 'Sept. 23, 2018'],
        show: true,
        boundaryGap: false,
        axisLine: {
          lineStyle: { color: 'rgba(0, 0, 0, .08)' }
        },
        axisLabel: { color: 'rgba(0, 0, 0, .5)' }
      },
      yAxis: {
        show: true,
        boundaryGap: false,
        splitLine: { show: false },
        axisLine: {
          lineStyle: { color: 'rgba(0, 0, 0, .08)' }
        },
        axisLabel: { color: 'rgba(0, 0, 0, .5)' }
      },
      series: [
        {
          name: 'Desktop',
          type: 'line',
          stack: 'referrals',
          data: [625, 745, 702, 789, 609, 782, 622],
          areaStyle: {},
          smooth: 0.4,
          symbolSize: 7,
          showSymbol: false
        },
        {
          name: 'Mobile',
          type: 'line',
          stack: 'referrals',
          data: [277, 569, 713, 428, 411, 668, 324],
          areaStyle: {},
          smooth: 0.4,
          symbolSize: 7,
          showSymbol: false
        }
      ],
      animationDuration: 2000
    },

    pieOptions: {
      color: colors,
      title: {
        text: 'Pie chart',
        x: 'center'
      },
      tooltip: {
        show: false,
        trigger: 'item',
        formatter: '{b}<br />{c} ({d}%)',
        textStyle: {
          fontSize: 13
        }
      },
      legend: {
        data: ['Desktop', 'Mobile'],
        show: true,
        y: 'top',
        x: 'left',
        left: 20,
        orient: 'vertical',
        textStyle: {
          color: 'rgba(0, 0, 0, .9)'
        }
      },
      series: [{
        name: 'Sessions by Device',
        type: 'pie',
        radius: '75%',
        center: ['50%', '50%'],
        label: {
          normal: {
            show: true,
            position: 'inside',
            formatter: '{b}\n{d}%'
          },
          emphasis: {
            show: true,
            textStyle: {
              fontSize: '18',
              fontWeight: 'bold'
            }
          }
        },
        labelLine: {
          normal: {
            show: false
          },
          emphasis: {
            show: false
          }
        },
        data: [
          { value: 901, name: 'Mobile', itemStyle: { color: '#007bff' } },
          { value: 559, name: 'Desktop', itemStyle: { color: '#dc3545' } }
        ],
        itemStyle: {
          emphasis: {
            shadowBlur: 50,
            shadowColor: 'rgba(0, 0, 0, 0.3)'
          }
        }
      }],
      animationDuration: 2000
    },

    multiLineOptions: {
      tooltip: {
        trigger: 'axis'
      },
      color: colors,
      legend: {
        data: ['Direct', 'Google Organic', 'Google Adwords'],
        show: false,
        top: '10',
        orient: 'vertical'
      },
      grid: {
        left: '0',
        top: '20%',
        right: '0',
        bottom: '0',
        containLabel: false
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: ['Sept. 17', 'Sept. 18', 'Sept. 19', 'Sept. 20', 'Sept. 21', 'Sept. 22', 'Sept. 23'],
        show: false,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        boundaryGap: false,
        show: false
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 50,
          shadowColor: 'rgba(0, 0, 0, 0.3)'
        }
      },
      areaStyle: { },
      series: [
        {
          name: 'Direct',
          type: 'line',
          stack: 'referrals',
          data: [512, 536, 430, 294, 263, 405, 380],
          areaStyle: {},
          smooth: 0.4,
          symbolSize: 7,
          showSymbol: false
        },
        {
          name: 'Google Organic',
          type: 'line',
          stack: 'referrals',
          data: [377, 469, 313, 228, 211, 368, 424],
          areaStyle: {},
          smooth: 0.4,
          symbolSize: 7,
          showSymbol: false
        },
        {
          name: 'Google Adwords',
          type: 'line',
          stack: 'referrals',
          data: [101, 342, 217, 172, 133, 251, 259],
          areaStyle: {},
          smooth: 0.4,
          symbolSize: 7,
          showSymbol: false
        }
      ],
      animationDuration: 2000
    }
  })
}
</script>
