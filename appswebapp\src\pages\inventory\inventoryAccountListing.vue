<template>
  <div>
    <h4>Accounts</h4>
    <b-card>
      <b-form @submit.prevent="applyFilter">
        <b-row>
          <b-col class="my-1" xl="4" lg="4" sm="6">
            <b-form-input placeholder="Search..." v-model="filter.search"></b-form-input>
          </b-col>
          <b-col class="my-1" xl="3" lg="3" sm="6">
            <b-form-select v-model="autoVideoStatusFilterSelectValue" :options="getAutoVideoStatusOptions"></b-form-select>
          </b-col>
          <b-col class="my-1" xl="3" lg="3" sm="6">
            <b-form-select v-model="filter.email_ad_page_enabled" :options="getEmailPageStatusOptions"></b-form-select>
          </b-col>
          <b-col class="my-1" xl="2" lg="2" sm="6">
            <b-btn class="w-100" variant="primary" type="submit">Submit</b-btn>
          </b-col>
        </b-row>
      </b-form>
    </b-card>
    <b-card v-if="isLoaded">
      <b-table
        :items="items"
        :fields="tableFields"
        :sort-by="tableSortBy"
        :sort-desc="tableSortDesc"
        @sort-changed="onSortChanged"
        show-empty
        striped
        hover
        no-local-sorting
        no-sort-reset
        responsive="sm"
      >
        <template #cell(accountId)="data">
          <router-link class="inventory-account-link" :to="{name: 'inventory-description', params: { accountId: data.item.accountId }}">{{data.item.accountId}}</router-link>
        </template>
        <template #cell(accountName)="data">
          <router-link class="inventory-account-link" :to="{name: 'inventory-description', params: { accountId: data.item.accountId }}">{{data.item.accountName}}</router-link>
        </template>
        <template #cell(enableEmailPage)="data">
          <b-icon v-if="data.item.isEmailAdPageEnabled" icon="check" scale="1.5" variant="success"></b-icon>
          <b-icon v-else icon="x" variant="primary" scale="1.5"></b-icon>
        </template>
        <template #cell(actions)="data">
          <b-dropdown variant="outline-secondary icon-btn btn-round" size="sm" right no-caret boundary='viewport'>
            <template slot="button-content">
              <i class="ion ion-ios-more m-0"></i><span class="sr-only">Actions</span>
            </template>
            <write-permission-wrapper v-if="data.item.isVideoMarketingEnabled" variant="hidden">
              <div class="dropdown-toggle no-caret">
                <a class="dropdown-item" style="text-decoration: none;">Change AutoVideo Status</a>
                <div class="dropdown-menu">
                  <b-dropdown-item v-for="(value, key) in getAvailableAutoVideoChangeStatuses(
                  data.item.autoVideoEncodingActivationStatus)" :key="key" @click="changeAutoVideoStatus(data.item.accountId, value.value)">
                    {{value.title}}
                  </b-dropdown-item>
                </div>
              </div>
            </write-permission-wrapper>
            <b-dropdown-text v-else>Video Marketing is not yet activated</b-dropdown-text>
            <b-dropdown-item :to="{name: 'inventory-account-autovideo-settings', params: {accountId: data.item.accountId}}">
              AutoVideo Settings
            </b-dropdown-item>
            <b-dropdown-item @click="disableOrEnableEmailPage(data.item.accountId, !data.item.isEmailAdPageEnabled)">
              {{data.item.isEmailAdPageEnabled ? 'Disable' : 'Enable'}} Access to Email Ad Generator
            </b-dropdown-item>
          </b-dropdown>
        </template>
        <template #cell(autoVideoStatus)="data">
          {{ getAutoVideoStatusText(data.item.autoVideoEncodingActivationStatus) }}
          <template v-if=!data.item.isVideoMarketingEnabled>
            <b-icon variant="secondary" :id="`account-${data.item.accountId}-marketing-status-message`" icon="question-circle"></b-icon>
            <b-popover :target="`account-${data.item.accountId}-marketing-status-message`" triggers="hover click blur">
              <span class="text-secondary">Video Marketing Not Activated</span>
            </b-popover>
          </template>
        </template>
      </b-table>
      <paging
        class="p-0"
        :pageNumber="filter.page"
        :pageSize="filter.pageSize"
        :totalItems="itemsTotalCount"
        titled
        pageSizeSelector
        @numberChanged="onPageChanged"
        @changePageSize="onChangePageSize"
      />
    </b-card>
  </div>
</template>

<script>
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import { mapGetters } from 'vuex'
import permissions from '../../shared/common/permissions'
import videoEncoderTypes from '@/shared/inventory/videoEncoderTypes'
import { accountListingSortTypes } from '@/shared/inventory/inventoryTypes'
import InventoryService from '../../services/inventory/InventoryService'
import VideoEncoderService from '../../services/inventory/VideoEncoderService'

const defaultValues = new ObjectSchema({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  search: { type: String, default: '' },
  auto_video_activation_status: { type: Number, default: null },
  video_marketing_enabled: { type: Boolean, default: null },
  email_ad_page_enabled: { type: Boolean, default: null },
  sort: { type: Number, default: 0 }
})

const queryHelper = new QueryStringHelper(defaultValues)

export default {
  name: 'inventory-account-listing',
  metaInfo: {
    title: 'Accounts'
  },
  data () {
    return {
      items: [],
      itemsTotalCount: 0,
      isLoaded: false,
      filter: defaultValues.getObject()
    }
  },
  created () {
    this.filter = queryHelper.parseQueryStringToObject(this.$router)
    this.loadContent()
  },
  components: {
    'paging': () => import('@/components/_shared/paging'),
    'loader': () => import('@/components/_shared/loader'),
    'write-permission-wrapper': () => import('@/components/_shared/writePermissionWrapper')
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {
        hasPermissions: () => false
      }
    },
    getEmailPageStatusOptions () {
      return [{ value: null, text: 'All Email Ad Generator Statuses' }, { value: true, text: 'Active' }, { value: false, text: 'Inactive' }]
    },
    autoVideoStatuses: {
      get () {
        return [
          videoEncoderTypes.autoVideoStatusTypes.active,
          videoEncoderTypes.autoVideoStatusTypes.inactive
        ]
      }
    },
    autoVideoStatusFilterSelectValue: {
      get () {
        if (this.filter.video_marketing_enabled === false) {
          return -1
        }
        return this.filter.auto_video_activation_status
      },
      set (val) {
        if (val === -1) {
          this.filter.video_marketing_enabled = false
          this.filter.auto_video_activation_status = null
          return
        }
        if (val === videoEncoderTypes.autoVideoStatusTypes.inactive.value) {
          this.filter.auto_video_activation_status = val
          this.filter.video_marketing_enabled = true
          return
        }
        this.filter.auto_video_activation_status = val
        this.filter.video_marketing_enabled = null
      }
    },
    getAutoVideoStatusOptions () {
      let options = [{ value: null, text: 'All AutoVideo Statuses' }].concat(this.autoVideoStatuses).concat([{value: -1, text: 'Inactive(Video Marketing not activated)'}])
      return options
    },
    getVideoMarketingOptions () {
      return [
        {
          value: null,
          text: 'All Accounts'
        },
        {
          value: true,
          text: 'Video Marketing Activated'
        },
        {
          value: false,
          text: 'Video Marketing Not Activated'
        }
      ]
    },
    tableFields () {
      let fields = [
        {
          key: 'accountId',
          label: 'Account Id',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: accountListingSortTypes.accountIdAsc,
          sortTypeDesc: accountListingSortTypes.accountIdDesc
        },
        {
          key: 'accountName',
          label: 'Account Name',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: accountListingSortTypes.accountNameAsc,
          sortTypeDesc: accountListingSortTypes.accountNameDesc
        }
      ]

      if (this.user.hasPermissions(permissions.IMFullAccess)) {
        fields.push({
          key: 'autoVideoStatus',
          label: 'AutoVideo Status',
          tdClass: 'py-2 align-middle',
          sortable: false
        })
        fields.push({
          key: 'enableEmailPage',
          label: 'Email Ad Generator Status',
          tdClass: 'py-2 align-middle',
          sortable: false
        })
        fields.push({
          key: 'actions',
          label: 'Actions',
          tdClass: 'py-2 align-middle',
          sortable: false
        })
      }

      return fields
    },
    sortType () {
      return this.filter.sort
    },
    tableSortBy () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sortType
      } else {
        return false
      }
    }
  },
  methods: {
    applyFilter () {
      this.filter.page = 1
      this.synchronizeUrlAndReload()
    },
    onPageChanged (newPage) {
      this.filter.page = newPage
      this.synchronizeUrlAndReload()
    },
    onChangePageSize (newSize) {
      this.filter.page = 1
      this.filter.pageSize = newSize
      this.synchronizeUrlAndReload()
    },
    onSortChanged (value) {
      const sortingColumn = this.tableFields.find(x => x.key === value.sortBy)
      if (value.sortDesc) {
        this.filter.sort = sortingColumn.sortTypeDesc
      } else {
        this.filter.sort = sortingColumn.sortTypeAsc
      }
      this.synchronizeUrlAndReload()
    },
    resetVideoMarketingFilter () {
      this.filter.video_marketing_enabled = null
    },
    synchronizeUrlAndReload () {
      this.isLoaded = false
      queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.loadContent()
    },
    getAvailableAutoVideoChangeStatuses (currentStatus) {
      let availableAutoVideoStatuses = this.autoVideoStatuses.filter(x => x.value !== currentStatus)
      return availableAutoVideoStatuses.map(x => {
        return {
          value: x.value,
          title: `Change to ${x.text}`
        }
      })
    },
    loadContent () {
      InventoryService.getInventoryAccountsListing(this.filter).then(res => {
        this.items = res.data.accounts
        this.itemsTotalCount = res.data.total
      }).catch(ex => {
        this.$toaster.error('Failed on receiving accounts listing', {timeout: 4000})
      }).finally(() => {
        this.isLoaded = true
      })
    },
    getAutoVideoStatusText (status) {
      return videoEncoderTypes.autoVideoStatusTypes.getActivationStatusText(status)
    },
    changeAutoVideoStatus (accountId, newAutoVideoStatus) {
      let statusDesc = this.getAutoVideoStatusText(newAutoVideoStatus)
      this.$bvModal.msgBoxConfirm(`Are you sure you want to change AutoVideo Status to "${statusDesc}" for account ${accountId}`,
        {
          title: 'Confirmation',
          size: 'md',
          buttonSize: 'sm'
        }
      ).then(value => {
        if (value) {
          this.updateAccountVideoStatus(accountId, newAutoVideoStatus)
        }
      }).catch(err => {
        console.error(err)
      })
    },
    updateAccountVideoStatus (accountId, newAutoVideoStatus) {
      VideoEncoderService.updateAccountPhotoToVideoEncodingStatusInApps(accountId, {activationStatus: newAutoVideoStatus}).then(res => {
        this.$toaster.success('AutoVideo Status Successfully Changed', {timeout: 4000})
      }).catch(ex => {
        this.$toaster.exception(ex, 'Failed to change AutoVideo Status')
      }).finally(() => {
        this.loadContent()
      })
    },
    disableOrEnableEmailPage (accountId, isEmailAdPageEnabled) {
      this.$bvModal.msgBoxConfirm(`Are you sure you want to ${isEmailAdPageEnabled ? 'enable' : 'disable'} access to email ad generator for account ${accountId}?`,
        {
          title: 'Confirmation',
          size: 'md',
          buttonSize: 'sm'
        }
      ).then(value => {
        if (value) {
          this.updateInventoryEmailPageSetting(accountId, isEmailAdPageEnabled)
        }
      }).catch(err => {
        console.error(err)
      })
    },
    updateInventoryEmailPageSetting (accountId, isEmailAdPageEnabled) {
      InventoryService.updateInventoryEmailPageSettings(accountId, {isEmailAdPageEnabled: isEmailAdPageEnabled}).then(res => {
        this.$toaster.success(` Access to email ad generator ${isEmailAdPageEnabled ? 'enabled' : 'disabled'} successfully`)
      }).catch(ex => {
        this.$toaster.exception(ex, `Failed to ${isEmailAdPageEnabled ? 'enable' : 'disable'} access to email ad generator`)
      }).finally(() => {
        this.loadContent()
      })
    }
  }
}
</script>

<style>
.inventory-account-link {
  color: black;
}
</style>
