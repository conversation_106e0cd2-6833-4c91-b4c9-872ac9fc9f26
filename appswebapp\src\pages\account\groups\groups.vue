<template v-if="isReadyToShow">
  <div class="row">
    <h4 class="d-flex justify-content-between align-items-center w-100 font-weight-bold pb-0 mb-4">
      <div>Account Groups</div>
       <div class="d-block btn-group">
          <button id="add-group-button" type="button" @click="openModal()" class="btn btn-primary btn-round btn-md">Add Group</button>
       </div>
    </h4>
    <div v-for="group in groups" :key="group.groupId" class="col-sm-3 mb-3">
      <router-link :to="{ path: `/accounts/groups/${group.groupId}` }">
      <div class="card card-body h-100">
        <button class="btn btn-sm btn-round btn-outline-primary delete-group-button" @click.prevent="tryToDeleteGroup(group.groupId)">×</button>
        <div class="media">
          <div class="media-body text-left">
            <h4 class="font-weight-bold mb-2">{{group.groupName}}</h4>
            <span class="text-uppercase font-size-sm text-muted">{{group.accounts.length}} {{group.accounts.length == 1 ? 'Account' : 'Accounts'}}</span>
          </div>
        </div>
      </div>
      </router-link>
    </div>
    <b-modal id="delete-group-confirmation-modal" v-model="showGroupDeleteConfirmation" title="Delete Group" lazy @ok="deleteGroup()">
      <p class="Delete permission rule">
        Are you sure you want to delete this group?
      </p>
    </b-modal>

    <b-modal id="add-group" title="Add Group"
      v-model="addGroup.show"
      size="lg"
      lazy
      :header-bg-variant="addGroup.headerBgVariant"
      :header-text-variant="addGroup.headerTextVariant">
      <b-form-group
          label="Group Name"
          label-for="modal-group-name"
          label-cols="2"
          horizontal
      >
      <b-form-input id="modal-group-name" v-model.trim="addGroup.groupName" @change="validateGroupName"></b-form-input>
      <small id="group-name-desc" class="form-text', 'text-muted" :style="{color: addGroup.groupNameLabelColor}">
        Must be at least 2 characters long
      </small>
      </b-form-group>

      <account-selector @onAccountSelected="onAccountSelected" @onAccountUnselected="onAccountUnselected" :selectedIds="addGroup.accountIds"></account-selector>

      <div slot="modal-footer" class="w-10 error-message">
         <p class="text-left response-message">{{addGroup.errorMessage}}</p>
      </div>

      <div slot="modal-footer" class="w-10">
         <b-btn variant="secondary" @click="closeModal()">
           Close
         </b-btn>
      </div>

      <div slot="modal-footer" class="w-10">
        <b-btn variant="primary" @click="createGroup()">
          Create
        </b-btn>
      </div>
    </b-modal>
  </div></template>

<script>
import AccountSelector from '@/components/account/groups/accountsselector.vue'
import groupsManagementService from '@/services/accounts/groups/GroupsManagementService'

export default {
  name: 'Groups',
  metaInfo: {
    title: 'Account Groups'
  },
  data () {
    return {
      groups: null,
      addGroup: {
        groupName: '',
        accountIds: [],
        headerBgVariant: '',
        headerTextVariant: 'dark',
        groupNameDesc: '',
        groupNameLabelColor: '',
        errorMessage: '',
        show: false

      },
      isReadyToShow: false,
      groupIdToDelete: null,
      showGroupDeleteConfirmation: false
    }
  },
  mounted () {
    this.populateGroupsInfo()
  },
  methods: {
    populateGroupsInfo () {
      groupsManagementService.getGroups()
        .then(response => {
          this.groups = response.data
          this.isReadyToShow = true
        }).catch(reason => {
          this.$logger.handleError(reason, 'Can\'t populate groups info')
        })
    },
    openModal () {
      this.addGroup.groupName = ''
      this.addGroup.errorMessage = ''
      this.setModalHeaderStyle('', 'dark')
      this.addGroup.groupNameLabelColor = ''
      this.addGroup.show = true
      this.addGroup.accountIds = []
    },
    closeModal () {
      this.addGroup.show = false
    },
    validateGroupName () {
      if (this.addGroup.groupName.length < 2) {
        this.addGroup.groupNameLabelColor = 'red'
      } else {
        this.addGroup.groupNameLabelColor = 'black'
      }
    },
    setModalHeaderStyle (backgroundVarinat, textVarinat) {
      this.addGroup.headerBgVariant = backgroundVarinat
      this.addGroup.headerTextVariant = textVarinat
    },
    createGroup () {
      if (this.addGroup.groupName.length < 2) {
        this.addGroup.groupNameLabelColor = 'red'
      } else {
        let groupItem = {GroupName: this.addGroup.groupName, Accounts: this.addGroup.accountIds}
        groupsManagementService.createGroup(groupItem).then(x => {
          this.populateGroupsInfo()
          this.closeModal()
        }).catch(reason => {
          this.$logger.handleError(reason, 'Can\'t create group info')
        })
      }
    },
    onAccountSelected (accountId) {
      this.addGroup.accountIds.push(accountId)
    },
    onAccountUnselected (accountId) {
      this.addGroup.accountIds.splice(this.addGroup.accountIds.indexOf(accountId), 1)
    },
    tryToDeleteGroup (groupId) {
      this.groupIdToDelete = groupId
      this.showGroupDeleteConfirmation = true
    },
    deleteGroup () {
      groupsManagementService.deleteGroup(this.groupIdToDelete).then(x => {
        this.populateGroupsInfo()
      })
    }
  },
  components: {
    'account-selector': AccountSelector
  }
}
</script>

<style scoped>
  .delete-group-button {
    position: absolute;
    width: 30px;
    height: 26px;
    top: 3px;
    right: 3px;
  }

  #delete-group-confirmation-modal p {
    font-weight: 400;
    font-size: 1.313rem;
    color: #C90F17;
  }
</style>
