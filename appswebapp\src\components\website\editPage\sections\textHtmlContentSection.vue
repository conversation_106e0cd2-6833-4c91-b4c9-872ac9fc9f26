<template>
  <div class="mt-3">
    <div class="border-bottom">
      <b-row>
        <b-col xs="12" sm="6" md="6" lg="6" xl="6" class="m-0"><h6 class="float-left">Text / HTML Content</h6></b-col>
      </b-row>
    </div>
    <ValidationProvider name="Text/ HTML Content" rules="xml" v-slot="{errors}">
      <detail-row titlePosition="start" :extra-payload-width="true" :error="errors[0]" name='Text / HTML Content'>
        <span slot="title">Text / HTML Content:</span>
        <ckeditor v-model="textHtml" slot="payload"/>
      </detail-row>
    </ValidationProvider>
  </div>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import ckeditor from '@/components/_shared/ckeditor/ckeditor'

export default {
  props: {
    value: { type: String }
  },
  data () {
    return {
    }
  },
  components: {
    'detail-row': detailRow,
    'ckeditor': ckeditor
  },
  computed: {
    textHtml: {
      get () {
        if (!this.value) {
          return ''
        }
        return this.value
      },
      set (value) {
        this.$emit('input', value)
      }
    }
  }
}
</script>
