import axios from 'axios'

const actions = {
  getUserActivity (_, parameters) {
    return axios.get('/api/systemtools/useractivity', { params: parameters.filter })
  },
  getUserActivityLogDetails (_, parameters) {
    return axios.get(`/api/systemtools/useractivity/${parameters.id}/details`)
  },
  rijndaelDecrypt (_, parameters) {
    return axios.post(`/api/service/rijndaeldecrypt`, { input: parameters.input })
  },
  rijndaelEncrypt (_, parameters) {
    return axios.post(`/api/service/rijndaelencrypt`, { input: parameters.input })
  },
  tripledesDecrypt (_, parameters) {
    return axios.post(`/api/service/tripledesdecrypt`, { input: parameters.input })
  },
  tripledesEncrypt (_, parameters) {
    return axios.post(`/api/service/tripledesencrypt`, { input: parameters.input })
  },
  generateToken (_, parameters) {
    return axios.post(`/api/auth/generatetoken`, parameters)
  },
  pollProcessingErrorCommand (_, guid) {
    return axios.get(`/api/errorreport/${guid}/poll`)
  },
  generateErrorReport (_, filter) {
    return axios.get(`/api/errorreport/generate`, { params: filter })
  },
  getErrorReportsByDate (_, filter) {
    return axios.get(`/api/errorreport/dailyinfo`, { params: filter })
  },
  getErrorReportSummary (_) {
    return axios.get(`/api/errorreport/summary`)
  },
  getErrorReportRules (_, filter) {
    return axios.get(`/api/errorreport/rules`, { params: filter })
  },
  runMongoQueryString (_, data) {
    return axios.post(`/api/errorreport/mongo/run`, data)
  },
  createErrorRule (_, data) {
    return axios.post(`/api/errorreport/rule/create`, data)
  },
  updateErrorRule (_, data) {
    return axios.post(`/api/errorreport/rule/update`, data)
  },
  deleteErrorRule (_, id) {
    return axios.get(`/api/errorreport/rule/${id}/delete`)
  },
  sendErrorReportToEmail (_, data) {
    return axios.post(`/api/errorreport/email/send`, data)
  },
  updateErrorReport (_, data) {
    return axios.post(`/api/errorreport/update`, data)
  },
  cancelErrorReportTask (_, id) {
    return axios.get(`/api/errorreport/${id}/cancel`)
  },
  getErrorDetails (_, errorId) {
    return axios.get(`/api/errorreport/error/${errorId}`)
  },
  getErrorGroups (_) {
    return axios.get(`/api/errorreport/geterrorgroups`)
  },
  getErrorsApplicationLocations (_, filter) {
    return axios.get(`/api/errorreport/geterrorsapplicationlocations`, { params: filter })
  },
  getErrors (_, filter) {
    return axios.post(`/api/errorreport/geterrors`, filter)
  }
}

export default {
  namespaced: true,
  actions: actions
}
