import permissions from '../shared/common/permissions'
import applicationTypes from '../shared/common/applicationTypes'

const usersDefaultMeta = {
  applicationType: applicationTypes.AppsUsers.Id,
  permissions: [permissions.ManageUserAccount],
  applicationFullAccess: permissions.UsersFullAccess
}

export default [{
  path: '/users',
  meta: {
    ...usersDefaultMeta
  },
  component: () => import('@/layout/Layout2'),
  children: [{
    path: '',
    name: 'users-management',
    meta: {
      ...usersDefaultMeta
    },
    component: () => import('@/pages/users/users')
  }, {
    path: 'roles',
    name: 'user-roles-management',
    meta: {
      ...usersDefaultMeta,
      permissions: [permissions.FullAccess],
      applicationFullAccess: permissions.FullAccess
    },
    component: () => import('@/pages/users/roles')
  }, {
    path: 'announcements',
    name: 'announcements-tool',
    component: () => import('../pages/users/announcements'),
    meta: {
      permissions: [permissions.EbizAutosAdmin],
      applicationFullAccess: permissions.EbizAutosAdmin
    }
  },
  {
    path: 'announcements/create',
    name: 'announcements-create',
    component: () => import('../pages/users/announcementManagement'),
    meta: {
      permissions: [permissions.EbizAutosAdmin],
      applicationFullAccess: permissions.EbizAutosAdmin
    }
  }, {
    path: 'announcements/:id/edit',
    name: 'announcements-edit',
    props: (route) => ({ announcementId: route.params.id }),
    component: () => import('../pages/users/announcementManagement'),
    meta: {
      permissions: [permissions.EbizAutosAdmin],
      applicationFullAccess: permissions.EbizAutosAdmin
    }
  }, {
    path: 'useractivity',
    name: 'users-user-activity',
    meta: {
      ...usersDefaultMeta,
      permissions: permissions.UsersFullAccess
    },
    component: () => import('@/pages/users/userActivity')
  }]
}]
