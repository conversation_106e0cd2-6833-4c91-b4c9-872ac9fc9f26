<template>
    <accountLevelUserActivityListing v-if="userActivityTypes.account.value === type"
      :items="items"
      :sortType="sortType"
      @sortChange="sortChange"
    >
    </accountLevelUserActivityListing>
    <groupLevelUserActivityListing v-else-if="userActivityTypes.group.value === type"
      :items="items"
      :sortType="sortType"
      @sortChange="sortChange"
    >
    </groupLevelUserActivityListing>
    <contactLevelUserActivityListing v-else-if="userActivityTypes.contact.value === type"
      :items="items"
      :sortType="sortType"
      @sortChange="sortChange">
    </contactLevelUserActivityListing>
</template>

<script>
import accountLevelUserActivityListing from './accountLevelUserActivityListing.vue'
import groupLevelUserActivityListing from './groupLevelUserActivityListing.vue'
import contactLevelUserActivityListing from './contactLevelUserActivityListing.vue'
import userActivityConstants from '../../../shared/accounts/userActivityConstants'

export default {
  props: {
    type: {type: Number, required: true},
    items: {type: Array, required: true},
    sortType: {type: Number, required: true}
  },
  data () {
    return {
      userActivityTypes: userActivityConstants.userActivityTypes
    }
  },
  components: {
    accountLevelUserActivityListing,
    groupLevelUserActivityListing,
    contactLevelUserActivityListing
  },
  methods: {
    sortChange (newSortType) {
      this.$emit('sortChange', newSortType)
    }
  }
}
</script>
