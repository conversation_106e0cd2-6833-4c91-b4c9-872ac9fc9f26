<template>
  <div>
    <pricesSection></pricesSection>
    <leaseLoanSection></leaseLoanSection>
  </div>
</template>

<script>
import highLowPricesSection from './highLowPricesSection'
import leaseLoanSection from '@/components/details/sections/pricing/leaseLoanSection'

export default {
  data () {
    return {
      options: [
        'lease',
        'loan'
      ],
      selected: 'lease'
    }
  },
  components: {
    'pricesSection': highLowPricesSection,
    'leaseLoanSection': leaseLoanSection
  }
}
</script>
