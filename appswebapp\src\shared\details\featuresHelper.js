export default {
  getFeatureById (featuresSource, id) {
    return featuresSource.features.find(x => x.id === id)
  },
  getNameValueOptions (nameValueOptions) {
    return nameValueOptions.map(x => ({
      value: x.value,
      text: x.key
    }))
  },
  getSelectedAttributeOption (attribute) {
    if (attribute.nameValueOptions) {
      return getSelectedAttribute(attribute) || {
        key: '-',
        value: attribute.value
      }
    } else {
      return {
        key: attribute.value,
        value: attribute.value
      }
    }
  },
  getSelectedOptionOrSelf (attribute) {
    return getSelectedAttribute(attribute) || {
      key: attribute.value,
      value: attribute.value
    }
  }
}

function getSelectedAttribute (attribute) {
  if (!attribute.nameValueOptions) {
    return null
  }

  return attribute.nameValueOptions.find(x => x.value.toString().toLowerCase() === attribute.value.toString().toLowerCase())
}
