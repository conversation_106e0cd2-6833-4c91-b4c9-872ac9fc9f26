@import "./../variables";

.dark-container {
  .daterangepicker:before {
    top: -7px;
    border-right: 7px solid transparent;
    border-left: 7px solid transparent;
    border-bottom: 7px solid #00000078;;
  }

  .daterangepicker {
    border: 0;
    background-color: rgb(24, 28, 33);
    color: #fff;
  }

  .daterangepicker .calendar-table {
    background-color: rgb(24, 28, 33);
    border: none;
    border-radius: 0;
  }

  .daterangepicker td.off,
  .daterangepicker td.off.in-range,
  .daterangepicker td.off.start-date,
  .daterangepicker td.off.end-date {
    background-color: rgba(0, 0, 0, 0.47);
    border-color: transparent;
    color: #fff;
  }

  .daterangepicker td.available:hover, .daterangepicker th.available:hover {
    background-color: $secondary-color;
    border-color: transparent;
    color: inherit;
  }

  .daterangepicker .calendar-table .next span,
  .daterangepicker .calendar-table .prev span {
    color: #fff;
    border: solid #fffbfb;
    border-width: 0 2px 2px 0;
    border-radius: 0;
    display: inline-block;
    padding: 3px;
  }

  .daterangepicker .ranges ul {
    display: flex;
    flex-direction: column-reverse;
  }

  .daterangepicker .drp-calendar td.in-range {
    color: #fff;
    background-color: $secondary-color;
  }

  .daterangepicker .drp-buttons {
    border: 0;
  }

  .daterangepicker.show-ranges.ltr .drp-calendar.left {
    border: 0;
  }

  .daterangepicker .ranges ul li {
    transform: scale(1);
    transition: 0.15s all ease;
  }

  .daterangepicker .ranges ul li.active {
    background-color: $primary-color;
    color: #fff;
  }

  .daterangepicker .ranges li:not(.active):hover {
    background-color: $primary-color;
    color: #fff;
    transform: scale(1.1);
  }

  .daterangepicker .drp-calendar td.active {
    background-color: $primary-color;
    color: #fff;
  }

  .daterangepicker td.off.in-range:last-child:hover {
    background-color: $primary-color;
  }

  .daterangepicker:after {
    border-bottom: 6px solid #181c21;
  }

  .daterangepicker {
    font-weight: lighter;
  }
}
