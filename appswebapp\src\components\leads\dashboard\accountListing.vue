<template>
  <b-table
  :items='items'
  :fields="getTableFields"
  :sort-by="tableSortBy"
  :sort-desc="tableSortDesc"
  @sort-changed="onSortChanged"
  :bordered="false"
  :no-sort-reset="true"
  :no-local-sorting="true"
  hover
  responsive
  striped
  >
    <template #cell(accountId)="data">
      <router-link class="text-primary" :to="{name: 'leads-dashboard', params: { accountId: data.item.accountId }, query: $route.query} ">{{data.item.accountId}}</router-link>
    </template>
    <template #cell(accountName)="data">
      <router-link class="text-primary" :to="{name: 'leads-dashboard', params: { accountId: data.item.accountId }, query: $route.query} ">{{data.item.accountName}}</router-link>
    </template>
  </b-table>
</template>

<script>
import { accountListingSortType } from '@/shared/leads/common'
import permissions from '@/shared/common/permissions'
import numeral from 'numeral'
import { mapGetters } from 'vuex'

export default {
  name: 'leads-account-listing',
  props: {
    items: { type: Array, required: true },
    sort: { type: Number, required: true }
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {}
    },
    hasLeadsFullAccess () {
      return this.user && this.user.hasPermissions && this.user.hasPermissions(permissions.LeadsFullAccess)
    },
    getTableFields () {
      let tableFields = [
        {
          key: 'accountId',
          label: 'Account ID',
          sortTypeAsc: accountListingSortType.accountIdAsc,
          sortTypeDesc: accountListingSortType.accountIdDesc,
          sortable: true,
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'accountName',
          label: 'Account Name',
          sortTypeAsc: accountListingSortType.accountNameAsc,
          sortTypeDesc: accountListingSortType.accountNameDesc,
          sortable: true,
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'callsCount',
          label: 'Calls',
          sortTypeAsc: accountListingSortType.countCallsAsc,
          sortTypeDesc: accountListingSortType.countCallsDesc,
          sortable: true,
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'smsCount',
          label: 'SMS',
          sortTypeAsc: accountListingSortType.countSmsAsc,
          sortTypeDesc: accountListingSortType.countSmsDesc,
          sortable: true,
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'emailsCount',
          label: 'Emails',
          sortTypeAsc: accountListingSortType.countEmailsAsc,
          sortTypeDesc: accountListingSortType.countEmailsDesc,
          sortable: true,
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'webFormsCount',
          label: 'Web Forms',
          sortTypeAsc: accountListingSortType.countWebFormsAsc,
          sortTypeDesc: accountListingSortType.countWebFormsDesc,
          sortable: true,
          tdClass: 'py-2 align-middle'
        }
      ]
      if (this.hasLeadsFullAccess) {
        tableFields.push({
          key: 'totalCost',
          label: 'Total Cost',
          sortTypeAsc: accountListingSortType.costAsc,
          sortTypeDesc: accountListingSortType.costDesc,
          sortable: true,
          formatter: value => numeral(value).format('$0,0.00'),
          tdClass: 'py-2 align-middle'
        })
      }

      return tableFields
    },
    tableSortBy () {
      const sortedColumn = this.getTableFields.find(x => x.sortTypeAsc === this.sort || x.sortTypeDesc === this.sort)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.getTableFields.find(x => x.sortTypeAsc === this.sort || x.sortTypeDesc === this.sort)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sort
      } else {
        return false
      }
    }
  },
  methods: {
    onSortChanged (value) {
      const sortingColumn = this.getTableFields.find(x => x.key === value.sortBy)
      if (value.sortDesc) {
        this.$emit('sortChange', sortingColumn.sortTypeDesc)
      } else {
        this.$emit('sortChange', sortingColumn.sortTypeAsc)
      }
    }
  }
}
</script>
