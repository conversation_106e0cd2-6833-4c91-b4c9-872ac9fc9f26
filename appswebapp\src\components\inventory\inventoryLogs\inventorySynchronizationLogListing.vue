<template>
  <div class="table-responsive">
    <b-table
      :items="items"
      :fields="tableFields"
      :sort-by="tableSortBy"
      :sort-desc="tableSortDesc"
      @sort-changed="onSortChanged"
      :striped="true"
      :bordered="false"
      :no-sort-reset="true"
      :no-local-sorting="true"
      show-empty
      responsive
      class="products-table card-table"
    >
      <template #cell(vehicle)="data">
        <div class="media align-items-center">
          <img class="d-block float-left ui-w-50 mr-3" style="min-height: 1px" :src="data.item.photoUrl">
          <span>{{data.item.vehicleTitle}}</span>
        </div>
      </template>

      <template #cell(show_details)="data">
        <div class="media align-items-center">
          <b-button size="sm" @click.stop="showDetails(data)" class="text-center">
            {{ data.detailsShowing ? 'Hide' : 'Show' }} Details
          </b-button>
        </div>
      </template>

      <template #row-details="data">
        <b-card>
          <b>Last vehicle processing log</b>
          <b-btn variant="secondary" size="sm" class="refresh-log-info-btn text-nowrap" v-if='data.item.logInfoClass'><font-awesome-icon class="refresh-log-info-processing-mode mr-1" icon="sync-alt" size="sm" /><span class="btn-title">Refresh</span></b-btn>
          <b-btn v-else class="refresh-log-info-btn text-nowrap" size="sm" @click="refreshLogDetails(data.item)"><font-awesome-icon class="mr-1" icon="sync-alt" size="sm" /><span class="btn-title">Refresh</span></b-btn>
          <div :class="data.item.logInfoClass">
            <synchronization-log-detail-item title='Modification request received' isModificationRequest :data='data.item.vehicleState.modificationRequestReceived'/>
            <synchronization-log-detail-item title='Saved in Apps DB' :data='data.item.vehicleState.savedInAppsDb'/>
            <synchronization-log-detail-item title='Vehicle processing queued' :data='data.item.vehicleState.vehicleProcessingQueued'/>
            <synchronization-log-detail-item title='Vehicle processing started' :data='data.item.vehicleState.vehicleProcessingStarted'/>
            <synchronization-log-detail-item title='History report requested' :data='data.item.vehicleState.historyReportRequested'/>
            <synchronization-log-detail-item title='Validity verification result' :data='data.item.vehicleState.validityVerificationResult'/>
            <synchronization-log-detail-item title='Vehicle processing ended' :data='data.item.vehicleState.vehicleProcessingCompleted'/>
            <synchronization-log-detail-item title='Sitebox synchronization queued' :data='data.item.vehicleState.siteboxSynchronizationQueued'/>
            <synchronization-log-detail-item title='Sitebox synchronization started' :data='data.item.vehicleState.siteboxSynchronizationStarted'/>
            <synchronization-log-detail-item title='Sitebox synchronization ended' :data='data.item.vehicleState.siteboxSynchronizationCompleted'/>
            <b-row class="mt-2">
              <b-col :class='getTextVariant(data.item.vehicleState.isSynchronizationCompleted)'><h6>Synchronization completed</h6></b-col>
            </b-row>
          </div>
        </b-card>
      </template>

      <template #cell(actions)="row">
        <template>
          <b-dropdown variant="outline-secondary icon-btn btn-round" size="sm" right no-caret boundary='viewport'>
            <template slot="button-content">
              <i class="ion ion-ios-more m-0"></i><span class="sr-only">Manage</span>
            </template>
            <b-dropdown-item :disabled='isButtonDisabled(row.item)' @click='force(row.item)'>Force Sync</b-dropdown-item>
            <b-dropdown-item v-if="user.isEbizAdmin" :disabled='isButtonDisabled(row.item)' @click="rebuildVehicleData(row.item)">Rebuild Vehicle Data</b-dropdown-item>
            <b-dropdown-item v-if="user.isEbizAdmin" :disabled='isButtonDisabled(row.item)' @click="forceAutoVideoRegeneration(row.item)">Force AutoVideo Regeneration</b-dropdown-item>
          </b-dropdown>
        </template>
      </template>
    </b-table>
  </div>
</template>

<script>
import { vehicleStateSortTypes } from '@/shared/inventory/inventoryTypes'
import permissions from '@/shared/common/permissions'
import signalRInventoryConnection from '@/signalR/inventoryHub'
import moment from 'moment'
import {mapGetters} from 'vuex'
import SyncService from '@/services/inventory/SyncService'
import InventoryLogService from '@/services/logs/InventoryLogService'

export default {
  name: 'inventory-synchronization-log',
  props: {
    items: { type: Array, required: true },
    totalItems: { type: Number, required: true }
  },
  data () {
    return {
      isConnectionManuallyClosed: false,
      isUserHasViewLogsPermission: false,
      interval: null
    }
  },
  created () {
    this.isUserHasViewLogsPermission = this.user.hasPermissions(permissions.ViewLogs)
    this.onVehicleStateItemChanged()
    this.connectToSignalR()
  },
  beforeDestroy () {
    this.disconnectFromSignalR()
    clearInterval(this.interval)
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {}
    },
    tableFields () {
      let fields = [{
        key: 'vehicle',
        label: 'Vehicle',
        tdClass: 'py-2 align-middle'
      }, {
        key: 'show_details',
        label: '',
        tdClass: 'py-2 align-middle'
      }, {
        key: 'stockNumber',
        label: 'Stock#',
        tdClass: 'py-2 align-middle'
      }, {
        key: 'vehicleState.vin',
        label: 'VIN',
        sortable: true,
        sortTypeAsc: vehicleStateSortTypes.vinAsc,
        sortTypeDesc: vehicleStateSortTypes.vinDesc,
        tdClass: 'py-2 align-middle'
      }, {
        key: 'vehicleState.lastProcessedDateTime',
        label: 'Last Processed',
        sortable: true,
        sortTypeAsc: vehicleStateSortTypes.lastProcessedAsc,
        sortTypeDesc: vehicleStateSortTypes.lastProcessedDesc,
        formatter: value => value ? moment(value).format('MM/DD/YYYY hh:mm A') : '-',
        tdClass: 'py-2 align-middle'
      }, {
        key: 'vehicleState.historyReportCheckedDateTime',
        label: 'History Report checked',
        sortable: true,
        sortTypeAsc: vehicleStateSortTypes.historyReportCheckedAsc,
        sortTypeDesc: vehicleStateSortTypes.historyReportCheckedDesc,
        formatter: value => value ? moment(value).format('MM/DD/YYYY hh:mm A') : '-',
        tdClass: 'py-2 align-middle'
      }]

      if (this.isUserHasViewLogsPermission) {
        fields.push({
          key: 'actions',
          label: 'Actions',
          tdClass: 'py-2 align-middle'
        })
      }

      return fields
    },
    tableSortBy () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sortType
      } else {
        return false
      }
    }
  },
  components: {
    'synchronization-log-detail-item': () => import('./synchronizationLogDetailitem')
  },
  methods: {
    onSortChanged (value) {
      const sortingColumn = this.tableFields.find(x => x.key === value.sortBy)
      let sortType = 0
      if (value.sortDesc) {
        sortType = sortingColumn.sortTypeDesc
      } else {
        sortType = sortingColumn.sortTypeAsc
      }
      this.$emit('onSortChanged', sortType)
    },
    getTextVariant (isCompleted) {
      if (isCompleted) {
        return 'text-info'
      }

      return 'text-secondary'
    },
    isButtonDisabled (item) {
      if (item.vehicleState) {
        return item.vehicleState.isLastForceSynchronizationInProgress
      }

      return false
    },
    showDetails (data) {
      data.toggleDetails()

      if (!data.detailsShowing) {
        data.item.isShowingDetails = true
      } else {
        data.item.isShowingDetails = false
      }
    },
    refreshLogDetails (item) {
      this.$set(item, 'logInfoClass', 'log-info-refresh-processing-mode')
      let index = this.items.findIndex(x => x.vehicleState.vin === item.vehicleState.vin)
      InventoryLogService.getSynchronizationLogDetails(item.vehicleState.accountId, item.vehicleState.vin).then(res => {
        if (res.data && index >= 0) {
          this.items[index].vehicleState = res.data
        }
      }).catch(ex => {
        this.$toaster.error('Can\'t refresh log info')
        this.$logger.handleError(ex, `Can't refresh log info for account: ${item.vehicleState.accountId} and vin: ${item.vehicleState.vin}`)
      }).finally(() => {
        this.$set(item, 'logInfoClass', '')
      })
    },
    onVehicleStateItemChanged () {
      this.interval = setInterval(function () {
        for (let index in this.items) {
          if (this.items[index].isShowingDetails) {
            InventoryLogService.getSynchronizationLogDetails(this.items[index].vehicleState.accountId, this.items[index].vehicleState.vin).then(res => {
              if (res.data) {
                this.items[index].vehicleState = res.data
              }
            }).catch(ex => {
              this.$logger.handleError(ex, `Can't automatic refresh log info for account: ${this.items[index].vehicleState.accountId} and vin: ${this.items[index].vehicleState.vin}`)
            })
          }
        }
      }.bind(this), 60000)
    },
    force (item) {
      SyncService.forceSynchronizeVehicle(item.vehicleState.accountId, item.vehicleState.vin).then(res => {
        this.$toaster.success('Sent to Process Successfully', {timeout: 5000})
      }).catch(ex => {
        this.$toaster.exception(ex, 'Failed on forcing vehicle synchronization', {timeout: 5000})
        this.$logger.handleError(ex, 'Failed on forcing vehicle synchronization')
      })
    },
    rebuildVehicleData (item) {
      SyncService.forceRebuildVehicleData(item.vehicleState.accountId, item.vehicleState.vin).then(res => {
        this.$toaster.success('Sent to Process Successfully', {timeout: 5000})
      }).catch(ex => {
        this.$toaster.exception(ex, 'Failed on sending vehicle to rebuild vehicle data', {timeout: 5000})
        this.$logger.handleError(ex, 'Failed on sending vehicle to rebuild vehicle data')
      })
    },
    forceAutoVideoRegeneration (item) {
      SyncService.forceAutoVideoRegeneration(item.vehicleState.accountId, item.vehicleState.vin).then(res => {
        this.$toaster.success('Sent to Process Successfully', {timeout: 5000})
      }).catch(ex => {
        this.$toaster.exception(ex, 'Failed on forcing AutoVideo regeneration', {timeout: 5000})
        this.$logger.handleError(ex, 'Failed on forcing AutoVideo regeneration')
      })
    },
    connectToSignalR () {
      if (this.isUserHasViewLogsPermission) {
        signalRInventoryConnection.on('onForceVehicleSynchronizationStarted', vin => {
          this.forceVehicleSynchronizationStarted(vin)
        })
        signalRInventoryConnection.on('onForceVehicleSynchronizationCompleted', vin => {
          this.forceVehicleSynchronizationCompleted(vin)
        })
        signalRInventoryConnection.on('onForceVehicleSynchronizationTimedOut', vin => {
          this.forceVehicleSynchronizationTimedOut(vin)
        })
        signalRInventoryConnection.onclose(this.onConnectionCloseFromSignalR)
        this.establishConnection()
      }
    },
    onConnectionCloseFromSignalR () {
      if (!this.isConnectionManuallyClosed) {
        this.establishConnection()
      }
    },
    async establishConnection () {
      await signalRInventoryConnection.start().then(() => {
      }).catch(() => {
        return new Promise((resolve, reject) =>
          setTimeout(() => this.connectToSignalR().then(resolve).catch(reject), 5000))
      })
    },
    disconnectFromSignalR () {
      if (this.isUserHasViewLogsPermission) {
        this.isConnectionManuallyClosed = true
        signalRInventoryConnection.stop()
      }
    },
    forceVehicleSynchronizationStarted (vin) {
      let item = this.items.find(x => x.vehicleState.vin === vin)
      if (item) {
        item.vehicleState.isLastForceSynchronizationInProgress = true
      }
    },
    forceVehicleSynchronizationCompleted (vin) {
      let item = this.items.find(x => x.vehicleState.vin === vin)
      if (item) {
        item.vehicleState.isLastForceSynchronizationInProgress = false
      }
    },
    forceVehicleSynchronizationTimedOut (vin) {
      let item = this.items.find(x => x.vehicleState.vin === vin)
      if (item) {
        item.vehicleState.isLastForceSynchronizationInProgress = false
        this.$toaster.error(`Vehicle Synchronization expired timed out for accountId:${item.vehicleState.accountId}, vin:${item.vehicleState.vin}`, { timeout: 8000 })
      }
    }
  }
}
</script>

<style>
.log-info-refresh-processing-mode {
  opacity: 0.5;
}
.refresh-log-info-btn {
  width: 75px;
  height: 25px;
  right: 25%;
  top: 30px;
  position: absolute;
  margin: 0;
  z-index: 100;
}
.refresh-log-info-processing-mode {
  animation: rotate 4s infinite linear;
}
@media (max-width: 1520px) {
  .refresh-log-info-btn {
    right: 22px;
  }
}

@media (max-width: 1200px) {
  .refresh-log-info-btn {
    right: 0;
    top: 90%;
    left: 22px;
    bottom: 5px;
  }
}
@media (max-width: 767px) {
  .refresh-log-info-btn {
    top: 95%;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(359deg);
  }
}
</style>
