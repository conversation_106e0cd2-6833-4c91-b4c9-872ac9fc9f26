<template>
  <div>
    <missed-twilio-campaigns-listing @delete='deleteReport' v-if='type === integrityReportTabTypes.missedTwilioCampaigns.value' :items='items'/>
    <missed-twilio-conversations-listing @delete='deleteReport' v-else-if='type === integrityReportTabTypes.missedTwilioConversations.value' :items='items'/>
    <missed-twilio-phones-listing @delete='deleteReport' v-else-if='type === integrityReportTabTypes.missedTwilioPhones.value' :items='items'/>
    <not-matched-emails-listing  @delete='deleteReport' v-else-if='type === integrityReportTabTypes.notMatchedEmails.value' :items='items'/>
  </div>
</template>

<script>
import { integrityReportTabTypes } from '@/shared/leads/common'

export default {
  name: 'leads-integrity-report-listing',
  props: {
    items: { type: Array, required: true },
    type: { type: Number, required: true }
  },
  data () {
    return {
      integrityReportTabTypes
    }
  },
  components: {
    'missed-twilio-campaigns-listing': () => import('./missedTwilioCampaignsListing'),
    'missed-twilio-conversations-listing': () => import('./missedTwilioConversationsListing'),
    'missed-twilio-phones-listing': () => import('./missedTwilioPhonesListing'),
    'not-matched-emails-listing': () => import('./notMatchedEmailsListing')
  },
  methods: {
    deleteReport (id) {
      this.$emit('delete', id)
    }
  }
}
</script>
