<template>
  <div class="w-100" v-bind:class="{ 'p-3 mb-1 border rounded': !isInnerGroup}">
    <b-form-group class="mb-0">
      <template v-if="!isInnerGroup" slot="label">
        <b-dropdown text="Add" variant="success" size="sm">
          <b-dropdown-item @click="addMatch(matches, '$and')">
            AND
          </b-dropdown-item>
          <b-dropdown-item @click="addMatch(matches, '$or')">
            OR
          </b-dropdown-item>
        </b-dropdown>
        <strong>Query:</strong>
      </template>
      <div v-for="(match, matchIndex) in matches" :key='matchIndex' class="border rounded p-2 my-1">
        <b-input-group prepend="Match all of" class="mb-2">
          <b-form-input v-model="match.conjugation" disabled></b-form-input>
          <b-input-group-append>
            <b-btn variant="outline-success" @click="changeConjugation(match)">
              <font-awesome-icon size="md" icon="sync"/>
            </b-btn>
          </b-input-group-append>
          <b-input-group-append>
            <b-btn variant="outline-primary" @click="removeMatch(matchIndex)">
              <font-awesome-icon size="md" icon="trash"/>
            </b-btn>
          </b-input-group-append>
        </b-input-group>
        <b-input-group class="pl-3 mt-1" v-for="(field, fieldIndex) in match.fields" :key='fieldIndex'>
          <b-form-select class="mw-lte-sm-25" :options="availableFieldOptions" v-model="field.name"></b-form-select>
          <b-form-input :disabled="!field.operator.isInput" v-model="field.input">
          </b-form-input>
          <b-form-select class="mw-lte-sm-25" v-model="field.operator">
            <option
              v-for="option in queryAvailableOperatorOptions"
              :key = "option.text"
              :value="option">
            {{ option.text }}
            </option>
          </b-form-select>
          <b-input-group-append>
            <b-btn @click="removeField(match, fieldIndex, matchIndex)" variant="primary">X</b-btn>
          </b-input-group-append>
        </b-input-group>
        <b-input-group v-if="match.innerMatches" class="pl-3">
          <query-match :matches="match.innerMatches" :isInnerGroup="true" />
        </b-input-group>
        <b-input-group class="pl-3 mt-1 d-flex justify-content-between">
          <b-btn variant="success" @click="addField(match.fields)" size="sm">Add field</b-btn>
          <b-dropdown text="Add group" variant="success" size="sm">
            <b-dropdown-item @click="addMatch(match.innerMatches, '$and')">
              AND
            </b-dropdown-item>
            <b-dropdown-item @click="addMatch(match.innerMatches, '$or')">
              OR
            </b-dropdown-item>
          </b-dropdown>
        </b-input-group>
      </div>
    </b-form-group>
  </div>
</template>

<script>
import { availableFields, mongoQueryOperators } from '@/shared/errorReport/constants'

export default {
  name: 'query-match',
  props: {
    matches: {
      type: Array,
      required: true
    },
    isInnerGroup: {
      type: Boolean,
      required: true
    }
  },
  computed: {
    availableFieldOptions () {
      return availableFields
    },
    queryAvailableOperatorOptions () {
      return mongoQueryOperators
    }
  },
  methods: {
    removeMatch (index) {
      this.matches.splice(index, 1)
    },
    addField (fields) {
      fields.push({
        name: availableFields[2],
        operator: mongoQueryOperators[2],
        input: ''
      })
    },
    removeField (match, fieldIndex, matchIndex) {
      match.fields.splice(fieldIndex, 1)
      if (match.fields.length === 0 && match.innerMatches.length === 0) {
        this.removeMatch(matchIndex)
      }
    },
    addMatch (matches, conjugation) {
      matches.push({
        conjugation: conjugation,
        fields: [{
          name: availableFields[2],
          operator: mongoQueryOperators[2],
          input: ''
        }],
        innerMatches: []
      })
    },
    changeConjugation (match) {
      match.conjugation = match.conjugation === '$and' ? '$or' : '$and'
    }
  }
}
</script>

<style scoped>
@media (max-width: 768px) {
  .mw-lte-sm-25 {
    max-width: 25%;
  }
}
</style>
