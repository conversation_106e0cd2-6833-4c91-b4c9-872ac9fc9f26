<template>
<div>
  <b-table
    :fields="tableFields"
    :items="items"
    :sort-by="tableSortBy"
    :sort-desc="tableSortDesc"
    @sort-changed="onSortChanged"
    :striped="true"
    :bordered="false"
    :no-sort-reset="true"
    :no-local-sorting="true"
    responsive
  >
    <template #cell(manage)="row">
      <b-dropdown variant="outline-secondary icon-btn btn-round" size="sm" right no-caret boundary='viewport'>
        <template slot="button-content">
          <i class="ion ion-ios-more m-0"></i><span class="sr-only">Manage</span>
        </template>
        <b-dropdown-item :to="{ path: `/inventory/${row.item.AccountId}/edit/${row.item.Vin}` }">Edit Vehicle</b-dropdown-item>
        <b-dropdown-item @click="showUnscheduleModal(row.item)">Unschedule Auction</b-dropdown-item>
        <b-dropdown-item :to="{ path: `/ebay/${row.item.AccountId}/posts/${row.item.Vin}/history` }">eBay History</b-dropdown-item>
        <b-dropdown-item :href="getViewTemplateHref(row.item)">View Template</b-dropdown-item>
      </b-dropdown>
    </template>
    <template #cell(title)="row">
      <div class="media align-items-center">
        <img class="d-block float-left ui-w-50 mr-3" style="min-height: 1px" :src="row.item.PresentationPhoto">
        <span>{{row.item | getVehicleTitle}}</span>
      </div>
    </template>
    <template #cell(type)="row">
      {{getTypeDesc(row.item)}}
    </template>
    <template #cell(show_details)="row">
      <div class="media align-items-center">
        <b-button size="sm" @click.stop="row.toggleDetails" class="text-center">
          {{ row.detailsShowing ? 'Hide' : 'Show' }} Details
        </b-button>
      </div>
    </template>
    <template #row-details="row">
      <b-card>
        <b-row>
          <b-col>
            <detail-row :fixed-payload-width="true">
              <span class="font-weight-bold text-dark" slot="title">Listing Title:</span>
              <span slot="payload">{{row.item.ListingTitle}}</span>
            </detail-row>
          </b-col>
          <b-col>
            <detail-row :fixed-payload-width="true">
              <span class="font-weight-bold text-dark" slot="title">Is this a Relist?</span>
              <span slot="payload">{{row.item.IsRelist ? 'Yes' : 'No'}}</span>
            </detail-row>
          </b-col>
        </b-row>
        <b-row>
          <b-col>
            <detail-row :fixed-payload-width="true">
              <span class="font-weight-bold text-dark" slot="title">Parent Category:</span>
              <span slot="payload">{{row.item.EBayCategoryParentName}}</span>
            </detail-row>
          </b-col>
          <b-col>
            <detail-row :fixed-payload-width="true">
              <span class="font-weight-bold text-dark" slot="title">Private Listing?</span>
              <span slot="payload">{{row.item.IsPrivate ? "Yes" : "No"}}</span>
            </detail-row>
          </b-col>
        </b-row>
        <b-row>
          <b-col>
            <detail-row :fixed-payload-width="true">
              <span class="font-weight-bold text-dark" slot="title">eBay Category:</span>
              <span slot="payload">{{row.item.EBayCategoryName}}</span>
            </detail-row>
          </b-col>
          <b-col>
            <detail-row :fixed-payload-width="true">
              <span class="font-weight-bold text-dark" slot="title">VIN:</span>
              <span slot="payload">{{row.item.Vin}}</span>
            </detail-row>
          </b-col>
        </b-row>
        <b-row>
          <b-col>
            <detail-row :fixed-payload-width="true">
              <span class="font-weight-bold text-dark" slot="title">Upgrades:</span>
              <span slot="payload">{{getUpgradesTitle(row.item)}}</span>
            </detail-row>
          </b-col>
          <b-col>
            <detail-row :fixed-payload-width="true">
              <span class="font-weight-bold text-dark" slot="title">Stock#:</span>
              <span slot="payload">{{row.item.StockNumber}}</span>
            </detail-row>
          </b-col>
        </b-row>
        <b-row>
          <b-col>
            <detail-row :fixed-payload-width="true">
              <span class="font-weight-bold text-dark" slot="title">Duration:</span>
              <span slot="payload">{{row.item.AuctionDuration > 30 ? "Good Till Canceled" : row.item.AuctionDuration + " Days"}}</span>
            </detail-row>
          </b-col>
        </b-row>
        <b-row v-if="row.item.IsLocalAuction">
          <b-col>
            <detail-row :fixed-payload-width="true" >
              <span class="font-weight-bold text-dark" slot="title">Advertised Price:</span>
              <span slot="payload">{{getPriceText(row.item.StartPrice)}}</span>
            </detail-row>
          </b-col>
        </b-row>
        <b-row v-if="row.item.IsPriceFixed">
          <b-col>
            <detail-row :fixed-payload-width="true">
              <span class="font-weight-bold text-dark" slot="title">Fixed Price:</span>
              <span slot="payload">{{getPriceText(row.item.StartPrice)}}</span>
            </detail-row>
          </b-col>
        </b-row>
        <b-row v-if="!row.item.IsPriceFixed && !row.item.IsLocalAuction">
          <b-col>
            <detail-row :fixed-payload-width="true" >
              <span class="font-weight-bold text-dark" slot="title">Start Price:</span>
              <span slot="payload">{{getPriceText(row.item.StartPrice)}}</span>
            </detail-row>
          </b-col>
        </b-row>
        <b-row v-if="!row.item.IsPriceFixed && !row.item.IsLocalAuction">
          <b-col>
            <detail-row :fixed-payload-width="true" >
              <span class="font-weight-bold text-dark" slot="title">BIN Price:</span>
              <span slot="payload">{{getPriceText(row.item.BinPrice)}}</span>
            </detail-row>
          </b-col>
        </b-row>
        <b-row v-if="!row.item.IsPriceFixed && !row.item.IsLocalAuction">
          <b-col>
            <detail-row :fixed-payload-width="true" >
              <span class="font-weight-bold text-dark" slot="title">Reserve Amount:</span>
              <span slot="payload">{{getPriceText(row.item.ReservePrice)}}</span>
            </detail-row>
          </b-col>
        </b-row>
      </b-card>
    </template>
  </b-table>
  <b-modal
    v-if="scheduledItem"
    title="Unschedule eBay Listing"
    size="lg"
    :visible="isUnscheduleModalVisible"
    @hide="hideUnscheduleModal"
  >
    <vehicleDescription :vehicle="scheduledItem"/>
    <span class="my-2">Please confirm that you would like to unschedule this eBay listing.</span>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Listing Format:</span>
      <span slot="payload">{{getTypeDesc(scheduledItem)}}</span>
    </detail-row>
    <detail-row title-position="start" :fixed-payload-width="true">
      <span slot="title">Listing Title:</span>
      <span slot="payload">{{scheduledItem.AuctionTitle}}</span>
    </detail-row>
    <detail-row v-if="!scheduledItem.IsPriceFixed" :fixed-payload-width="true">
      <span slot="title">Starting Price:</span>
      <span slot="payload">{{getPriceText(scheduledItem.StartPrice)}}</span>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Fixed Price:</span>
      <span slot="payload">{{getPriceText(scheduledItem.StartPrice)}}</span>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Start Day:</span>
      <span slot="payload">{{getStartDayDesc(scheduledItem.AuctionStartDateTime)}}</span>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Start Time:</span>
      <span slot="payload">{{getStartTimeDesc(scheduledItem.AuctionStartDateTime)}}</span>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Duration:</span>
      <span slot="payload">{{scheduledItem.AuctionDuration > 30 ? "Good Till Canceled" : scheduledItem.AuctionDuration + " Days"}}</span>
    </detail-row>
    <span>What does this do?</span><br/>
    <ol>
      <li>The auction currently scheduled for this vehicle will bt unscheduled.</li>
      <li>Failure to unschedule the auction before its start time may result in eBay fees.</li>
      <li>This cannot be used to cancel an Auction currently "Live" on eBay.</li>
    </ol>
    <template #modal-footer>
      <b-btn variant="primary" @click="onUnscheduleItem">Unschedule eBay Listing</b-btn>
    </template>
  </b-modal>
</div>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import vehicleDescription from '@/components/eBay/helpers/vehicleDescription'
import constants from '@/shared/ebay/constants'
import numeral from 'numeral'
import moment from 'moment'

export default {
  name: 'ebay-posts-scheduled-listing',
  props: {
    sortType: { type: Number, required: true },
    items: { type: Array, required: true }
  },
  data () {
    return {
      scheduledItem: null,
      isUnscheduleModalVisible: false,
      sitesInfo: {}
    }
  },
  components: {
    detailRow,
    vehicleDescription
  },
  computed: {
    tableFields () {
      return [
        {
          key: 'title',
          label: 'Vehicle',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'show_details',
          label: '',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'StartPrice',
          label: 'Pricing',
          tdClass: 'py-2 align-middle',
          sortTypeAsc: constants.postSortTypes.pricingAsc,
          sortTypeDesc: constants.postSortTypes.pricingDesc,
          formatter: value => numeral(value).format('$0,0'),
          sortable: true
        },
        {
          key: 'AuctionStartDateTime',
          label: 'Start Time',
          tdClass: 'py-2 align-middle',
          sortTypeAsc: constants.postSortTypes.startTimeAsc,
          sortTypeDesc: constants.postSortTypes.startTimeDesc,
          formatter: value => moment(value).format('MM/DD/YYYY hh:mm A'),
          sortable: true
        },
        {
          key: 'type',
          label: 'Type',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    tableSortBy () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sortType
      } else {
        return false
      }
    }
  },
  filters: {
    getVehicleTitle: function (item) {
      if (!item) return ''
      let title = ''
      if (item.Year > 0) {
        title = item.Year.toString()
      }

      title = [title, item.Make, item.Model, item.Trim].filter(v => v && v !== '').join(' ')

      return title.trim()
    }
  },
  mounted () {
    this.populateSiteInfo()
  },
  methods: {
    onSortChanged (value) {
      const sortingColumn = this.tableFields.find(x => x.key === value.sortBy)

      this.$emit('onSortChange', sortingColumn
        ? value.sortDesc
          ? sortingColumn.sortTypeDesc
          : sortingColumn.sortTypeAsc
        : 0)
    },
    getPriceText (price) {
      if (price && price > 0) {
        return numeral(price).format('$0,0')
      }
      return 'N/A'
    },
    getTypeDesc (item) {
      if (item.IsLocalAuction) {
        return 'eBay Local'
      }
      if (item.IsPriceFixed) {
        return 'Fixed Price Listing'
      }

      return `Auction (${item.AuctionDuration}d)`
    },
    getStartDayDesc (date) {
      return moment(date).format('MM/DD/YYYY')
    },
    getStartTimeDesc (date) {
      return moment(date).format('hh:mm A')
    },
    hideUnscheduleModal () {
      this.isUnscheduleModalVisible = false
    },
    showUnscheduleModal (item) {
      this.scheduledItem = item
      this.isUnscheduleModalVisible = true
    },
    populateSiteInfo () {
      let siteIds = [+this.$route.params.accountId]
      this.$store.dispatch('website/getSitesBasicInfo', { data: { siteIds: siteIds } }).then(res => {
        this.sitesInfo = res.data.sites
      }).catch(ex => {
        this.$toaster.error('Something went wrong!')
        this.$logger.handleError(ex, 'Cannot populate sites basic info')
      })
    },
    onUnscheduleItem () {
      this.$store.dispatch('eBayScheduling/unscheduleItem', { accountId: this.scheduledItem.AccountId, vin: this.scheduledItem.Vin }).then(res => {
        let index = this.items.findIndex(x => x.Vin === this.scheduledItem.Vin)
        if (index > -1) {
          this.items.splice(index, 1)
        }
        this.$toaster.success('Unscheduled eBay Listing Successfully')
      }).catch(ex => {
        this.$toaster.error('Something went wrong!')
        this.$logger.handleError(ex, 'Cannot unschedule item')
      }).finally(() => {
        this.scheduledItem = null
        this.isUnscheduleModalVisible = false
      })
    },
    getUpgradesTitle (item) {
      let titles = []
      if (item.IsBold) {
        titles.push('Bold Face Title')
      }
      if (item.IsSubTitle) {
        titles.push('Sub Title')
      }
      return titles.join(', ') || 'N/A'
    },
    getViewTemplateHref (item) {
      let res = this.sitesInfo[item.AccountId]
      if (res) {
        return `${res.host}/auction.aspx?vin=${item.Vin}`
      }
      return ''
    }
  }
}
</script>
