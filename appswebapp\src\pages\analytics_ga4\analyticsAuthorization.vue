<template>
  <div>
    <h4>eBizAutos GA4 Authorization</h4>
    <template v-if="!isExceptionOccurred">
      <template v-if="!isLoading">
        <b-row>
          <b-col cols="12"><b>Authorization Status:</b></b-col>
          <b-col cols="12" class="text-muted">{{ getAuthorizationStatusMessage }}</b-col>
          <b-col cols="12">
            <c-button v-if="hasToDisplayedRevokeBtn" :loading="isRevokingProcessed" message="Are you sure you Revoke Authorization? You will not be able to revert this!" variant="primary" @confirm="revoke">Revoke</c-button>
            <b-overlay class="d-inline-block" opacity="0.6" v-else rounded :show="iSignInProcessed">
              <div tabindex="0" @click="signIn" class="google-sign-in-btn"></div>
            </b-overlay>
          </b-col>
        </b-row>
      </template>
      <loader v-else size="md" class="mt-2"/>
    </template>
    <template v-else>
      <error-alert></error-alert>
    </template>
  </div>
</template>

<script>
import {ObjectSchema} from '@/shared/common/objectHelpers'
import oAuth2TokenService from './../../services/analytics/OAuth2TokenService'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import loader from './../../components/_shared/loader.vue'
import detailRow from '@/components/details/helpers/detailRow'

const statusCookieKey = 'ga4-sign-in-status'
const statusCookieExpirationTime = { expires: '30m' }

const defaultFilters = new ObjectSchema({
  is_authorized: {type: Boolean, default: false}
})
const queryStringHelper = new QueryStringHelper(defaultFilters)

export default {
  name: 'analytics-ga4-authorization',
  components: {
    loader,
    detailRow
  },
  data () {
    return {
      isValid: false,
      userEmail: '',
      isRevokingProcessed: false,
      iSignInProcessed: false,
      isLoading: true,
      isExceptionOccurred: false
    }
  },
  created () {
    this.showMessageIfNeeded()
    this.populateAuthorizationStatus()
  },
  computed: {
    getAuthorizationStatusMessage () {
      if (this.isValid) {
        return `Authorized (${this.userEmail}) - Revoke Authorization by clicking Revoke button below.`
      }

      return 'Not Authorized - Update Authorization by signing in with Google Below.'
    },
    hasToDisplayedRevokeBtn () {
      return this.isValid
    }
  },
  methods: {
    populateAuthorizationStatus () {
      oAuth2TokenService.getSignInStatus().then(res => {
        this.userEmail = res.data.model.email
        this.isValid = res.data.model.isValid
      }).catch(ex => {
        this.isExceptionOccurred = true
        this.$toaster.error('Something went wrong', {timeout: 5000})
        this.$logger.handleError(ex, 'Failed on receiving analytic account settings from server')
      }).finally(() => {
        this.isLoading = false
      })
    },
    getCurrentUrlWithoutParams () {
      const currentUrl = window.location.href
      const urlObj = new URL(currentUrl)
      urlObj.search = ''
      return urlObj.toString()
    },
    signIn () {
      this.iSignInProcessed = true
      const url = this.getCurrentUrlWithoutParams()
      oAuth2TokenService.getGoogleSignUrl({
        authorizationSuccessfulRedirectionUrl: url + '?is_authorized=true',
        authorizationFailedRedirectionUrl: url + '?is_authorized=false'
      }).then(res => {
        this.$cookies.set(statusCookieKey, {isProcessed: true}, statusCookieExpirationTime)
        window.open(res.data.model, '_self')
      }).catch(ex => {
        this.$toaster.error('Something went wrong!', {timeout: 5000})
        this.$logger.handleError(ex, 'Failed get google sign in url')
      }).finally(() => {
        this.iSignInProcessed = false
      })
    },
    revoke () {
      this.isRevokingProcessed = true
      oAuth2TokenService.revokeToken().then(res => {
        if (res.data.model) {
          this.$toaster.success('Revoked Successfully', {timeout: 5000})
        } else {
          this.$toaster.error('Something went wrong on revoking', {timeout: 5000})
        }
      }).catch(ex => {
        this.$toaster.error('Something went wrong on revoking', {timeout: 5000})
        this.$logger.handleError(ex, 'Failed on revoking google authorization token')
      }).finally(() => {
        this.isRevokingProcessed = false
        this.populateAuthorizationStatus()
      })
    },
    showMessageIfNeeded () {
      let googleSignStatus = this.$cookies.isKey(statusCookieKey) ? this.$cookies.get(statusCookieKey).isProcessed : false
      if (!googleSignStatus) {
        return
      }

      let filter = queryStringHelper.parseQueryStringToObject(this.$router)
      if (filter.is_authorized) {
        this.$toaster.success('Authorized successfully', {timeout: 10000})
      } else {
        this.$toaster.error('Something went wrong on authorization!', {timeout: 10000})
      }
      this.$cookies.set(statusCookieKey, {isProcessed: false}, statusCookieExpirationTime)
    }
  }
}
</script>

<style>
.google-sign-in-btn {
  width: 190px;
  height: 45px;
  cursor: pointer;
  background: url('/static/img/google/btn_google_signin_dark_normal_web.png') no-repeat;
}

.google-sign-in-btn:hover {
  background: url('/static/img/google/btn_google_signin_dark_focus_web.png') no-repeat;
}

.google-sign-in-btn:focus {
  background: url('/static/img/google/btn_google_signin_dark_focus_web.png') no-repeat;
}
</style>
