<template>
<div>
  <b-card>
    <b-row>
      <b-col>
        <b-form-input placeholder="Vin" v-model="filter.vin"></b-form-input>
      </b-col>
      <b-col>
        <b-input-group class="flex-nowrap">
          <b-input-group-prepend is-text>
            <i class="ion ion-md-calendar" slot="prepend"></i>
          </b-input-group-prepend>
          <date-time-picker
            ref="timeFrom"
            v-model="filter.dateFrom"
            :options="filterTimeOptions"
            format="MM/DD/YYYY HH:mm"
            placeholder="Date From"
            className="form-control"
            @change="onTimeFromInputChange"
          />
          <b-input-group-append
            is-text
            v-show="filter.dateFrom"
            @click="filter.dateFrom = null"
          >
            <i class="ion ion-md-close"></i>
          </b-input-group-append>
        </b-input-group>
      </b-col>
      <b-col>
        <b-input-group class="flex-nowrap">
          <b-input-group-prepend is-text>
            <i class="ion ion-md-calendar" slot="prepend"></i>
          </b-input-group-prepend>
          <date-time-picker
            ref="timeTo"
            v-model="filter.dateTo"
            :options="filterTimeOptions"
            format="MM/DD/YYYY HH:mm"
            placeholder="Date To"
            className="form-control"
            @change="onTimeToInputChange"
          />
          <b-input-group-append
            is-text
            v-show="filter.dateTo"
            @click="filter.dateTo = null"
          >
            <i class="ion ion-md-close"></i>
          </b-input-group-append>
        </b-input-group>
      </b-col>
      <b-col>
        <b-form-select v-model="filter.historyType" :options="getVehicleHistoryReportTypeOptions"></b-form-select>
      </b-col>
      <b-col v-if="filter.historyType === vehicleHistoryReportTypes.carfax.value">
        <b-form-select v-model="filter.type" :options="getApiCallTypeOptions"></b-form-select>
      </b-col>
      <b-col>
        <b-btn class="w-100" variant="primary" @click="applyFilter">Submit</b-btn>
      </b-col>
    </b-row>
  </b-card>
</div>
</template>

<script>
import { carfaxApiCallTypes, vehicleHistoryReportTypes } from '@/shared/inventory/inventoryTypes'

export default {
  name: 'vehicle-history-reports-logs-filter-form',
  props: {
    filter: { type: Object, required: true }
  },
  components: {
    'date-time-picker': () => import('@gravitano/vue-date-range-picker/src/components/DateRangePicker')
  },
  data () {
    return {
      isForceModalVisible: false,
      filterTimeOptions: {
        autoUpdateInput: false,
        startDate: new Date(),
        singleDatePicker: true,
        timePicker: true,
        timePicker24Hour: true,
        maxDate: new Date()
      },
      vehicleHistoryReportTypes
    }
  },
  computed: {
    getApiCallTypeOptions () {
      return Object.values(carfaxApiCallTypes)
    },
    getVehicleHistoryReportTypeOptions () {
      return [{value: 0, text: 'All Report Types'}, vehicleHistoryReportTypes.carfax, vehicleHistoryReportTypes.autoCheck]
    },
    refDateTimeFrom () {
      return (this.$refs.timeFrom || {}).$el || {}
    },
    refDateTimeTo () {
      return (this.$refs.timeTo || {}).$el || {}
    }
  },
  methods: {
    onTimeFromInputChange (newVal) {
      this.refDateTimeFrom.value = newVal || this.filter.dateFrom || null
    },
    onTimeToInputChange (newVal) {
      this.refDateTimeTo.value = newVal || this.filter.dateTo || null
    },
    applyFilter () {
      this.$emit('applyFilter')
    }
  }
}
</script>
