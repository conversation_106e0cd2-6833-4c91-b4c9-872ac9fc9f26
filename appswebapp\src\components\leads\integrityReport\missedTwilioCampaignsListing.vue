<template>
  <b-table
    :fields='getTableFields'
    :items='items'
    hover
    responsive>
    <template #cell(manage)="data">
      <b-btn @click="onDelete(data.item.id)" size="sm">Delete Phone Number</b-btn>
    </template>
  </b-table>
</template>

<script>
import moment from 'moment'
export default {
  name: 'missed-twilio-campaigns-listing',
  props: {
    items: { type: Array, required: true }
  },
  computed: {
    getTableFields () {
      return [
        {
          key: 'twilioPhone.phoneNumber',
          label: 'Phone Number',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'twilioPhone.friendlyName',
          label: 'Friendly Name',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'dateTimeInserted',
          label: 'Report Date Created',
          tdClass: 'py-2 align-middle',
          formatter: value => moment(value).format('MM/DD/YYYY hh:mm:ss A')
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle'
        }
      ]
    }
  },
  methods: {
    onDelete (id) {
      this.$emit('delete', id)
    }
  }
}
</script>
