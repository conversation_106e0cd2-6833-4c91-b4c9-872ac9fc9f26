<template>
  <div class="authentication-wrapper authentication-2 px-4">
    <div class="authentication-inner py-5">

      <!-- Form -->
      <form class="card">
        <div class="p-4 p-sm-5">

          <!-- Logo -->
          <div class="d-flex justify-content-center align-items-center pb-1 mb-2">
            <span class="app-brand-text demo">eBizAutos</span>
          </div>
          <!-- / Logo -->

          <h5 class="text-center text-muted font-weight-normal mb-4">Reset Your Password</h5>

          <hr class="mt-0 mb-4">

          <p>
            Enter your email address and we will send you a link to reset your password.
          </p>

          <b-form-group>
            <b-input v-model="credentials.email" placeholder="Enter your email address" />
          </b-form-group>

          <b-btn variant="primary" :block="true">Send password reset email</b-btn>

        </div>
      </form>
      <!-- / Form -->

    </div>
  </div>
</template>

<!-- Page -->
<style src="@/vendor/styles/pages/authentication.scss" lang="scss"></style>

<script>
export default {
  name: 'account-password',
  metaInfo: {
    title: 'Password Reset'
  },
  data: () => ({
    credentials: {
      email: ''
    }
  })
}
</script>
