<template>
  <div class="account-permission-selector">
    <b-button variant="primary btn-round" size="sm" @click="openModal()">Import Permissions</b-button>

    <b-modal
      :id="id"
      :title="title"
      lazy
      ref="aps-modal"
      size="lg"
      @hidden="resetModal"
      hide-header-close
    >
      <div slot="modal-title" class="d-flex" v-if="selectedAccountId">
        <b-btn @click="resetModal" size="sm"> &lt; Back </b-btn>
        <h5 class="ml-2 mb-0 modal-title">{{title}}</h5>
      </div>

      <account-selector v-if="isAccountSelectionStage" :group-id="groupId" @accountClick="onAccountClick"/>
      <contact-listing v-else :account-id="selectedAccountId" @input="onContactListChanged"></contact-listing>

      <div slot="modal-footer">
        <b-btn variant="danger" @click="handleCancel">Cancel</b-btn>
        <b-btn @click="handleOk">Submit</b-btn>
      </div>
    </b-modal>
  </div>
</template>

<script>
import groupsManagementService from '@/services/accounts/groups/GroupsManagementService'
import accountSelector from './permissionAccountListing'
import contactsListing from './contactsListing'

export default {
  name: 'account-permission-selector',
  props: {
    groupId: {
      type: String,
      required: true
    },
    userId: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      title: 'Import Permissions',
      id: this.$uuid.v4(),
      selectedAccountId: 0,
      selectedContactList: []
    }
  },
  computed: {
    isAccountSelectionStage () {
      return this.selectedAccountId === 0
    }
  },
  methods: {
    openModal () {
      this.$refs['aps-modal'].show()
    },
    closeModal () {
      this.$refs['aps-modal'].hide()
    },
    onAccountClick (accountId) {
      this.selectedAccountId = accountId
    },
    onContactListChanged (contactList) {
      this.selectedContactList = contactList
    },
    resetModal () {
      this.selectedAccountId = 0
    },
    async handleOk () {
      const updateInfo = {
        destinationUserId: this.userId,
        userIdsToCopy: this.selectedContactList
      }

      try {
        await groupsManagementService.sendSelectedUsers(this.groupId, updateInfo)
        this.selectedAccountId = 0
        this.selectedContactList = []
        this.closeModal()
        this.$emit('update')
      } catch (e) {
        this.$toaster.error('Failed to send data to server')
        this.$logger.handleError(e, 'Can\'t send data to server', updateInfo)
      }
    },
    handleCancel () {
      this.selectedAccountId = 0
      this.selectedContactList = []
      this.closeModal()
    }
  },
  components: {
    'account-selector': accountSelector,
    'contact-listing': contactsListing
  }
}
</script>

<style scoped>
  .modal-title {
    line-height: inherit;
  }
</style>
