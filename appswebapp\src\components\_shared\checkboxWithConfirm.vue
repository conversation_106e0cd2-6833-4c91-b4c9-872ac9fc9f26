<template>
  <div class="inline">
    <b-form-checkbox v-model.lazy="checkValue" @change="handleChange" :disabled="disabled" :size="size">{{label}}</b-form-checkbox>
    <b-modal :visible="show" title="Confirm" lazy @ok="handleOk" @hide="show=false">
      <p class="confirmation-message">
       {{message}}
      </p>
    </b-modal>
  </div>
</template>

<script>
export default {
  name: 'button-with-confirm',
  props: {
    value: Boolean,
    label: { type: String, default: '' },
    size: {
      type: String,
      required: false,
      validator: function (value) {
        return ['sm', 'md', '', 'lg'].indexOf(value) !== -1
      }
    },
    message: {
      type: String,
      default: 'Are you sure?'
    },
    disabled: Boolean
  },
  data () {
    return {
      show: false,
      checkValue: this.getCheckboxValue()
    }
  },
  methods: {
    getCheckboxValue () {
      return !!this.value
    },
    handleChange () {
      this.$nextTick(() => {
        this.checkValue = this.getCheckboxValue()
      })
      this.show = true
    },
    handleOk () {
      this.$emit('input', !this.value)
      this.$emit('confirm')
      this.show = false
    }
  },
  watch: {
    value () {
      this.checkValue = this.getCheckboxValue()
    }
  }
}
</script>

<style>
  .inline{
    display: inline-block
  }

  .confirmation-message{
    font-weight: 600;
    margin: 0;
  }
</style>
