<template>
<div>
  <div v-if='!isLoading && !isException'>
    <div class="mb-2">
      <b-row>
        <b-col><h4 class="float-left">Dashboard</h4></b-col>
        <b-col>
          <range-selector
            @input="onRangeChanged"
            :value="filterDateRange"
            class="button-col float-right"
          />
        </b-col>
      </b-row>
    </div>
    <leads-overview v-if='leadsOverview' :model='leadsOverview'/>
    <leads-campaign-filters @refreshCommunication="loadCampaignsModel" v-if='campaignModel' :model='campaignModel'/>
  </div>
  <div v-else-if='isLoading && !isException' class="mt-5">
    <loader size='lg'/>
  </div>
  <div v-else class="mt-5">
    <h4 class="text-muted text-center">Something went wrong! Please reload page</h4>
  </div>
</div>
</template>

<script>
import leadsCampaignFilters from '@/components/leads/dashboard/campaignFilters'
import leadsOverview from '@/components/leads/dashboard/leadsOverview'
import rangeSelector from '@/components/leads/rangeSelector/leadsRangeSelector'
import rangeHelper from '@/components/leads/rangeSelector/rangeHelper'
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import loader from '@/components/_shared/loader'

const defaultRange = rangeHelper.defaultRange.asFormattedStrings()

const defaultValues = new ObjectSchema({
  dateFrom: { type: String, default: defaultRange[0] },
  dateTo: { type: String, default: defaultRange[1] }
})
const queryHelper = new QueryStringHelper(defaultValues)

export default {
  name: 'leads-dashboard-for-account',
  metaInfo: {
    title: 'Leads Dashboard'
  },
  props: {
    accountId: { type: Number, required: true }
  },
  data () {
    return {
      isLoading: true,
      isException: false,
      leadsOverview: null,
      filter: defaultValues.getObject(),
      campaignModel: null
    }
  },
  created () {
    this.filter = queryHelper.parseQueryStringToObject(this.$router)
    this.loadContent()
  },
  components: {
    'leads-campaign-filters': leadsCampaignFilters,
    'leads-overview': leadsOverview,
    'loader': loader,
    'range-selector': rangeSelector
  },
  computed: {
    filterDateRange () {
      if ((this.filter || {}).dateFrom && (this.filter || {}).dateTo) {
        return [this.filter.dateFrom, this.filter.dateTo]
      }

      return null
    }
  },
  methods: {
    async onRangeChanged (rangeInfo) {
      if (this.filter.dateFrom === rangeInfo.range[0] && this.filter.dateTo === rangeInfo.range[1]) {
        return
      }
      this.filter.dateFrom = rangeInfo.range[0]
      this.filter.dateTo = rangeInfo.range[1]
      queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.loadContent()
    },
    async loadContent () {
      await this.loadAcountStatisc()
      await this.loadCampaignsModel()
      this.isLoading = false
    },
    async loadAcountStatisc () {
      this.$store.dispatch('leads/getAccountStatistic', { accountId: this.accountId, filter: this.filter }).then(res => {
        this.leadsOverview = res.data
      }).catch(ex => {
        this.isException = true
        this.$toaster.error(`Cannot get account leads statistic. Message: ${ex.message}`)
        this.$logger.handleError(ex, 'Cannot get account leads statistic', this.filter)
      })
    },
    async loadCampaignsModel () {
      this.$store.dispatch('leads/getAccountCampaignsModel', { accountId: this.accountId, filter: this.filter })
        .then(res => {
          this.campaignModel = res.data
        }).catch(ex => {
          this.isException = true
          this.$toaster.error(`Cannot get account campaigns model. Message: ${ex.message}`)
          this.$logger.handleError(ex, 'Cannot get account campaigns model', this.filter)
        })
    }
  }
}
</script>
