import permissions from './permissions'

const appsApplications = {
  Undefined: {
    Name: 'Undefined',
    Id: 0,
    Label: 'Not Apps Application',
    Permission: [permissions.DefaultAccess]
  },
  AppsHome: {
    Name: 'Home',
    Id: 1,
    Label: 'Home',
    Permission: [permissions.DefaultAccess]
  },
  AppsCraigslist: {
    Name: 'Craigslist',
    Id: 2,
    Label: 'Craigslist',
    Permission: [permissions.ManageCraigslist],
    PermissionFullAccess: permissions.CraigslistFullAccess
  },
  InventoryManagement: {
    Name: 'InventoryManagementApi',
    Id: 3,
    Label: 'Inventory',
    Permission: [permissions.IMManageVehicles],
    PermissionFullAccess: permissions.IMFullAccess
  },
  AppsUsers: {
    Name: 'UserPanel',
    Id: 4,
    Label: 'User Panel',
    Permission: [permissions.UsersFullAccess]
  },
  AppsLeads: {
    Name: 'AppsLeads',
    Id: 5,
    Label: 'Leads',
    Permission: [permissions.LeadsView],
    PermissionFullAccess: permissions.LeadsFullAccess
  },
  AppsLegacyLeads: {
    Name: 'LegacyLeads',
    Id: 6,
    Label: 'Legacy Leads',
    Permission: [permissions.FullAccess]
  },
  AppsAnalytics: {
    Name: 'AppsAnalytics',
    Id: 7,
    Label: 'Analytics',
    Permission: [permissions.ManageGoogleAnalytics],
    PermissionFullAccess: permissions.AnalyticsFullAccess
  },
  SiteBoxManager: {
    Name: 'SiteBoxManager',
    Id: 9,
    Label: 'Sitebox Manager',
    Permission: [permissions.SiteBoxManagerChangeSiteHosting],
    PermissionFullAccess: permissions.SiteBoxManagerFullAccess
  },
  AppsWebApp: {
    Name: 'AppsWebApp',
    Id: 11,
    Label: 'Apps Web App',
    Permission: [permissions.DefaultAccess]
  },
  AccountManagement: {
    Name: 'AccountManagement',
    Id: 12,
    Label: 'Account Management',
    Permission: [permissions.AMFullAccess],
    PermissionFullAccess: permissions.AMFullAccess
  },
  AppsEBay: {
    Name: 'AppsEBay',
    Id: 14,
    Label: 'Apps eBay',
    Permission: [permissions.EBayFullAccess],
    PermissionFullAccess: permissions.EBayFullAccess
  },
  SiteManagement: {
    Name: 'SiteManagement',
    Id: 13,
    Label: 'Site Management',
    Permission: [permissions.SMFullAccess],
    PermissionFullAccess: permissions.SMFullAccess
  }
}

export default {
  ...appsApplications,
  getByName (name) {
    for (let item in appsApplications) {
      let app = appsApplications[item]
      if (app && app.Name.toLowerCase() === name.toLowerCase()) {
        return app
      }
    }
    return null
  },
  getById (id) {
    for (let item in appsApplications) {
      let app = appsApplications[item]
      if (app && app.Id === id) {
        return app
      }
    }
    return null
  }
}
