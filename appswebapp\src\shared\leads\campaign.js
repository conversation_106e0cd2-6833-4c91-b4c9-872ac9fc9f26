const campaignTabTypes = {
  tracking: { key: 0, label: 'Tracking Campaigns', isEnabled: (settings) => true },
  webForm: { key: 1, label: 'Web Forms', isEnabled: (settings) => true },
  ebayLeads: { key: 2, label: 'eBay Leads', isEnabled: (settings) => settings.isEBayEnabled }
}

const getDefaultTabType = function (settings) {
  for (const tabTypeKey in campaignTabTypes) {
    if (campaignTabTypes[tabTypeKey].isEnabled(settings)) {
      return campaignTabTypes[tabTypeKey]
    }
  }
  return campaignTabTypes.tracking
}

const campaignTableFields = {
  tracking: [
    {
      key: 'location',
      label: 'Location',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'campaign',
      label: 'Campaign',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'communicationType',
      label: 'Type',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'proxyPhoneOrEmail',
      label: 'Proxy Phone/Email',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'forwardsTo',
      label: 'Forwards To',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'callsCount',
      label: 'Calls',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'smsCount',
      label: 'SMS',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'emailsCount',
      label: 'Emails',
      tdClass: 'py-2 align-middle'
    }
  ],
  webForm: [
    {
      key: 'location',
      label: 'Location',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'campaign',
      label: 'Campaign',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'accountLeadCommunication.leadTypeName',
      label: 'Lead Type',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'accountLeadCommunication.notificationSettings.hasToSendEmailNotifications',
      label: 'Send Notification Email to Dealership',
      tdClass: 'py-2 align-middle',
      formatter: value => value ? 'Yes' : 'No'
    },
    {
      key: 'accountLeadCommunication.notificationSettings.hasToSendAdfNotification',
      label: 'Send ADF Email to CRM',
      tdClass: 'py-2 align-middle',
      formatter: value => value ? 'Yes' : 'No'
    },
    {
      key: 'accountLeadCommunication.notificationSettings.hasToAutoResponse',
      label: 'Send Auto Response Email to Lead',
      tdClass: 'py-2 align-middle',
      formatter: value => value ? 'Yes' : 'No'
    },
    {
      key: 'webFormsCount',
      label: 'Submissions',
      tdClass: 'py-2 align-middle'
    }
  ],
  ebayLeads: [
    {
      key: 'location',
      label: 'Location',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'accountLeadCommunication.leadTypeName',
      label: 'Lead Type',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'contacts',
      label: 'Contacts',
      tdClass: 'py-2 align-middle'
    },
    {
      key: 'eBayCount',
      label: 'Leads Count',
      tdClass: 'py-2 align-middle'
    }
  ]
}

export {
  campaignTabTypes,
  campaignTableFields,
  getDefaultTabType
}
