<template>
  <b-table
    :fields="tableFields"
    :items="items"
    :sort-by="tableSortBy"
    :sort-desc="tableSortDesc"
    @sort-changed="onSortChanged"
    :striped="true"
    :bordered="false"
    :no-sort-reset="true"
    :no-local-sorting="true"
    responsive
  >
    <template #cell(AuctionId)="row">
      <b-link :href="getAuctionUrl(row.item.AuctionId)"><u>{{row.item.AuctionId}}</u></b-link>
    </template>
    <template #cell(HighBid)="row">
      <span>{{getHighBidDesc(row.item)}}</span>
    </template>
    <template #cell(manage)="row">
      <b-dropdown variant="outline-secondary icon-btn btn-round" size="sm" right no-caret boundary='viewport'>
        <template slot="button-content">
          <i class="ion ion-ios-more m-0"></i><span class="sr-only">Manage</span>
        </template>
        <b-dropdown-item :to="{ path: `/inventory/${row.item.AccountId}/edit/${row.item.Vin}` }">Edit Vehicle</b-dropdown-item>
        <b-dropdown-item :to="{ path: `/ebay/${row.item.AccountId}/inventory/${row.item.Vin}/schedule` }">Relist</b-dropdown-item>
        <b-dropdown-item :to="{ path: `/ebay/${row.item.AccountId}/posts/${row.item.Vin}/history` }">eBay History</b-dropdown-item>
        <b-dropdown-item :href="getViewTemplateHref(row.item)">View Template</b-dropdown-item>
      </b-dropdown>
    </template>
    <template #cell(title)="row">
      <div class="media align-items-center">
        <img class="d-block float-left ui-w-50 mr-3" style="min-height: 1px" :src="row.item.PresentationPhoto">
        <span>{{row.item | getVehicleTitle}}</span>
      </div>
    </template>
    <template #cell(type)="row">
      {{getTypeDesc(row.item)}}
    </template>
    <template #cell(TotalLeads)="row">
      {{getLeadsCount(row.item)}}
    </template>
    <template #cell(show_details)="row">
      <div class="media align-items-center">
        <b-button size="sm" @click.stop="row.toggleDetails" class="text-center">
          {{ row.detailsShowing ? 'Hide' : 'Show' }} Details
        </b-button>
      </div>
    </template>
    <template #row-details="row">
      <b-card>
        <b-row>
          <b-col>
            <detail-row title-position="start" :fixed-payload-width="true">
              <span class="font-weight-bold text-dark" slot="title">Listing Title:</span>
              <span slot="payload">{{row.item.ListingTitle || 'N/A'}}</span>
            </detail-row>
          </b-col>
          <b-col>
            <detail-row :fixed-payload-width="true">
              <span class="font-weight-bold text-dark" slot="title">Is this a Relist?</span>
              <span slot="payload">{{row.item.IsRelist ? 'Yes' : 'No'}}</span>
            </detail-row>
          </b-col>
        </b-row>
        <b-row>
          <b-col>
            <detail-row :fixed-payload-width="true">
              <span class="font-weight-bold text-dark" slot="title">Parent Category:</span>
              <span slot="payload">{{row.item.EBayCategoryParentName || 'N/A'}}</span>
            </detail-row>
          </b-col>
          <b-col>
            <detail-row :fixed-payload-width="true">
              <span class="font-weight-bold text-dark" slot="title">Private Listing?</span>
              <span slot="payload">{{row.item.IsPrivate ? "Yes" : "No"}}</span>
            </detail-row>
          </b-col>
        </b-row>
        <b-row>
          <b-col>
            <detail-row :fixed-payload-width="true">
              <span class="font-weight-bold text-dark" slot="title">eBay Category:</span>
              <span slot="payload">{{row.item.EBayCategoryName || 'N/A'}}</span>
            </detail-row>
          </b-col>
          <b-col>
            <detail-row :fixed-payload-width="true">
              <span class="font-weight-bold text-dark" slot="title">VIN:</span>
              <span slot="payload">{{row.item.Vin}}</span>
            </detail-row>
          </b-col>
        </b-row>
        <b-row>
          <b-col>
            <detail-row :fixed-payload-width="true">
              <span class="font-weight-bold text-dark" slot="title">Upgrades:</span>
              <span slot="payload">{{getUpgradesTitle(row.item)}}</span>
            </detail-row>
          </b-col>
          <b-col>
            <detail-row :fixed-payload-width="true">
              <span class="font-weight-bold text-dark" slot="title">Stock#:</span>
              <span slot="payload">{{row.item.StockNumber}}</span>
            </detail-row>
          </b-col>
        </b-row>
        <b-row>
          <b-col>
            <detail-row :fixed-payload-width="true">
              <span class="font-weight-bold text-dark" slot="title">Duration:</span>
              <span slot="payload">{{row.item.AuctionDuration > 30 ? "Good Till Canceled" : row.item.AuctionDuration + " Days"}}</span>
            </detail-row>
          </b-col>
        </b-row>
        <b-row v-if="row.item.IsLocalAuction">
          <b-col>
            <detail-row :fixed-payload-width="true" >
              <span class="font-weight-bold text-dark" slot="title">Advertised Price:</span>
              <span slot="payload">{{getPriceText(row.item.StartPrice)}}</span>
            </detail-row>
          </b-col>
        </b-row>
        <b-row v-if="row.item.IsPriceFixed">
          <b-col>
            <detail-row :fixed-payload-width="true">
              <span class="font-weight-bold text-dark" slot="title">Fixed Price:</span>
              <span slot="payload">{{getPriceText(row.item.StartPrice)}}</span>
            </detail-row>
          </b-col>
        </b-row>
        <b-row v-if="!row.item.IsPriceFixed && !row.item.IsLocalAuction">
          <b-col>
            <detail-row :fixed-payload-width="true" >
              <span class="font-weight-bold text-dark" slot="title">Start Price:</span>
              <span slot="payload">{{getPriceText(row.item.StartPrice)}}</span>
            </detail-row>
          </b-col>
        </b-row>
        <b-row v-if="!row.item.IsPriceFixed && !row.item.IsLocalAuction">
          <b-col>
            <detail-row :fixed-payload-width="true" >
              <span class="font-weight-bold text-dark" slot="title">BIN Price:</span>
              <span slot="payload">{{getPriceText(row.item.BinPrice)}}</span>
            </detail-row>
          </b-col>
        </b-row>
        <b-row v-if="!row.item.IsPriceFixed && !row.item.IsLocalAuction">
          <b-col>
            <detail-row :fixed-payload-width="true" >
              <span class="font-weight-bold text-dark" slot="title">Reserve Amount:</span>
              <span slot="payload">{{getPriceText(row.item.ReservePrice)}}</span>
            </detail-row>
          </b-col>
        </b-row>
        <b-row>
          <b-col>
            <detail-row :fixed-payload-width="true">
              <span class="font-weight-bold text-dark" slot="title">High Bidder:</span>
              <b-link v-if="row.item.HighBidedUserId" :href="getUserOfferUrl(row.item.HighBidedUserId)" slot="payload"><u>{{row.item.HighBidedUserId}}</u></b-link>
              <span slot="payload" v-else>N/A</span>
            </detail-row>
          </b-col>
        </b-row>
      </b-card>
    </template>
  </b-table>
</template>

<script>
import constants from '@/shared/ebay/constants'
import detailRow from '@/components/details/helpers/detailRow'
import numeral from 'numeral'
import moment from 'moment'

export default {
  name: 'ebay-posts-ended-listing',
  props: {
    sortType: { type: Number, required: true },
    items: { type: Array, required: true }
  },
  components: {
    detailRow
  },
  data () {
    return {
      auctionsLeadsInfo: {},
      sitesInfo: {}
    }
  },
  mounted () {
    this.populateLeadsInfo()
    this.populateSiteInfo()
  },
  computed: {
    tableFields () {
      return [
        {
          key: 'title',
          label: 'Vehicle',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'show_details',
          label: '',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'AuctionId',
          label: 'eBay ID',
          tdClass: 'py-2 align-middle',
          sortTypeAsc: constants.postSortTypes.eBayIdAsc,
          sortTypeDesc: constants.postSortTypes.eBayIdDesc,
          sortable: false
        },
        {
          key: 'HighBid',
          label: 'High Bid ($)',
          tdClass: 'py-2 align-middle',
          sortTypeAsc: constants.postSortTypes.highBidAsc,
          sortTypeDesc: constants.postSortTypes.highBidDesc,
          sortable: true
        },
        {
          key: 'TotalBids',
          label: 'Bids',
          tdClass: 'py-2 align-middle',
          sortTypeAsc: constants.postSortTypes.totalBidsAsc,
          sortTypeDesc: constants.postSortTypes.totalBidsDesc,
          sortable: true
        },
        {
          key: 'TotalViews',
          label: 'Views',
          tdClass: 'py-2 align-middle',
          sortTypeAsc: constants.postSortTypes.totalViewsAsc,
          sortTypeDesc: constants.postSortTypes.totalViewsDesc,
          sortable: true
        },
        {
          key: 'TotalWatchers',
          label: 'Watchers',
          tdClass: 'py-2 align-middle',
          sortTypeAsc: constants.postSortTypes.totalWatchersAsc,
          sortTypeDesc: constants.postSortTypes.totalWatchersDesc,
          sortable: true
        },
        {
          key: 'TotalLeads',
          label: 'Leads',
          tdClass: 'py-2 align-middle',
          sortTypeAsc: constants.postSortTypes.totalLeadsAsc,
          sortTypeDesc: constants.postSortTypes.totalLeadsDesc,
          sortable: true
        },
        {
          key: 'AuctionEndDateTime',
          label: 'End Time',
          tdClass: 'py-2 align-middle',
          sortTypeAsc: constants.postSortTypes.timeLeftAsc,
          sortTypeDesc: constants.postSortTypes.timeLeftDesc,
          formatter: value => moment(value).format('MM/DD/YYYY hh:mm A'),
          sortable: true
        },
        {
          key: 'type',
          label: 'Type',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    tableSortBy () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sortType
      } else {
        return false
      }
    }
  },
  filters: {
    getVehicleTitle: function (item) {
      if (!item) return ''
      let title = ''
      if (item.Year > 0) {
        title = item.Year.toString()
      }

      title = [title, item.Make, item.Model, item.Trim].filter(v => v && v !== '').join(' ')

      return title.trim()
    }
  },
  methods: {
    onSortChanged (value) {
      const sortingColumn = this.tableFields.find(x => x.key === value.sortBy)

      this.$emit('onSortChange', sortingColumn
        ? value.sortDesc
          ? sortingColumn.sortTypeDesc
          : sortingColumn.sortTypeAsc
        : 0)
    },
    getPriceText (price) {
      if (price && price > 0) {
        return numeral(price).format('$0,0')
      }
      return 'N/A'
    },
    getTypeDesc (item) {
      if (item.IsLocalAuction) {
        return 'eBay Local'
      }
      if (item.IsPriceFixed) {
        return 'Fixed Price Listing'
      }

      return `Auction (${item.AuctionDuration}d)`
    },
    getUpgradesTitle (item) {
      let titles = []
      if (item.IsBold) {
        titles.push('Bold Face Title')
      }
      if (item.IsSubTitle) {
        titles.push('Sub Title')
      }
      return titles.join(', ') || 'N/A'
    },
    getUserOfferUrl (userId) {
      return constants.eBayInfoUrls.userFeedBackUrl(userId)
    },
    getAuctionUrl (value) {
      return constants.eBayInfoUrls.ebayItemUrl(value)
    },
    getHighBidDesc (item) {
      if (item.HighBid) {
        let percent = (item.HighBid / item.ReservePrice) * 100

        return `${numeral(item.HighBid).format('$0,0')} (${parseInt(percent)}%)`
      }

      return 'N/A'
    },
    populateLeadsInfo () {
      if (!this.items || this.items.length === 0) {
        return
      }
      let auctionIds = this.items.map(x => { return x.AuctionId || 0 })
      this.$store.dispatch('leads/getEBayAuctionsLeadsInfo', { data: { auctionIds: auctionIds } }).then(res => {
        this.auctionsLeadsInfo = res.data.items
      }).catch(ex => {
        this.$toaster.error('Something went wrong!')
        this.$logger.handleError(ex, 'Cannot get auctions leads info', auctionIds)
      })
    },
    populateSiteInfo () {
      let siteIds = [+this.$route.params.accountId]
      this.$store.dispatch('website/getSitesBasicInfo', { data: { siteIds: siteIds } }).then(res => {
        this.sitesInfo = res.data.sites
      }).catch(ex => {
        this.$toaster.error('Something went wrong!')
        this.$logger.handleError(ex, 'Cannot populate sites basic info')
      })
    },
    getLeadsCount (item) {
      let res = this.auctionsLeadsInfo[item.AuctionId]
      if (res) {
        return res.leadCount
      }

      return 0
    },
    getViewTemplateHref (item) {
      let res = this.sitesInfo[item.AccountId]
      if (res) {
        return `${res.host}/auction.aspx?vin=${item.Vin}`
      }
      return ''
    }
  },
  watch: {
    items: {
      deep: true,
      handler: function () {
        this.populateLeadsInfo()
      }
    }
  }
}
</script>
