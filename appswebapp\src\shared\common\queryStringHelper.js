import { objectKeysToLowerCase } from './objectHelpers'

class QueryStringHelper {
  constructor (defaultFilterValuesParam) {
    let defaultFilterValues = defaultFilterValuesParam.getObject()
    let propertyNameTypeConstructorMapping = defaultFilterValuesParam.getObjectTypeSchema()
    this.defaultFilterMappings = []

    for (let key in defaultFilterValues) {
      this.defaultFilterMappings.push({
        name: key,
        value: defaultFilterValues[key],
        type: propertyNameTypeConstructorMapping[key]
      })
    }
  }

  parseQueryStringToObject (router) {
    if (!router || !router.history || !router.history.current) {
      return {}
    }

    return this.normalizeObject(router.history.current.query)
  };

  /*
    Can be used on query object to set default values for missing parameters
    and parse parameters as arrays
  */
  normalizeObject (source) {
    const result = {}

    this.defaultFilterMappings.forEach(
      defaultFilter => {
        let filterValue = source[defaultFilter.name.toLowerCase()]
        if (filterValue === undefined) {
          filterValue = defaultFilter.value
        }

        if (filterValue != null) {
          if (Array.isArray(defaultFilter.value)) {
            result[defaultFilter.name] = getTypedArray(filterValue, defaultFilter.type)
          } else if (defaultFilter.type === Boolean) {
            result[defaultFilter.name] = convertStringToBoolean(filterValue)
          } else {
            result[defaultFilter.name] = getValueOf(defaultFilter.type(filterValue))
          }
        } else {
          if (defaultFilter.value == null) {
            result[defaultFilter.name] = null
          }
        }
      }
    )

    return result
  }

  rebuildParamsInQueryString (router, qParams) {
    let currentRoute = router.history.current
    let parametersToAdd = {
      ...objectKeysToLowerCase(currentRoute.query),
      ...objectKeysToLowerCase(qParams)
    }

    for (let key in parametersToAdd) {
      let filterMapping = this.defaultFilterMappings.find(x => x.name.toLowerCase() === key)

      if (!filterMapping) {
        continue
      }

      let typeConstructor = this.defaultFilterMappings.find(x => x.name === filterMapping.name).type

      if (Array.isArray(filterMapping.value)) {
        if (filterMapping.value.equals(getTypedArray(parametersToAdd[key], typeConstructor))) {
          delete parametersToAdd[key]
        }
      } else {
        if (filterMapping.value === typeConstructor(parametersToAdd[key]) || parametersToAdd[key] === null) {
          delete parametersToAdd[key]
        }
      }
    }

    router.push({query: {
      ...parametersToAdd
    }}).catch(() => {})
  };
}

let getTypedArray = (str, typeConstructor) => {
  if (Array.isArray(str)) {
    return str
  }
  return str.split(',')
    .map(x => getValueOf(typeConstructor(x)))
}

let getValueOf = (value) => {
  if (value instanceof Date) {
    return value
  } else {
    return value.valueOf()
  }
}

let convertStringToBoolean = (value) => {
  if (value === null || value === undefined) {
    return false
  }

  if (typeof value === 'boolean') {
    return value
  }

  switch (value.toLowerCase().trim()) {
    case 'true':
    case 'yes':
    case '1':
      return true

    case 'false':
    case 'no':
    case '0':
      return false

    default:
      return Boolean(value)
  }
}

export default QueryStringHelper
