<template>
  <div class="position-relative">
    <h4>eBay Posts</h4>
    <paging
      class="custom-paging d-none d-md-block p-0"
      :pageNumber="filters.page"
      :pageSize="filters.pageSize"
      :totalItems="itemsTotalCount"
      @numberChanged="pageChanged"
      @changePageSize="changePageSize"
    />
    <b-tabs v-model="selectedTab" class="nav-tabs-top nav-responsive-sm" no-fade>
      <b-tab v-for="tab in tabs" :key="tab.key" :title="tab.title" class="p-4">
        <b-row>
          <b-col xl="5" lg="6">
            <b-input-group>
              <b-form-input v-model="filters.search" placeholder="Search..."></b-form-input>
              <b-input-group-append>
                <b-btn variant="primary" @click="synchronizeUrlAndReload">Go</b-btn>
              </b-input-group-append>
            </b-input-group>
          </b-col>
          <b-col xl="7" lg="6">
            <b-row class="mt-sm-3 mt-md-3 mt-lg-0">
              <b-col xl="4" lg="4" md="2" sm="12" class="py-2 d-flex justify-content-sm-start justify-content-lg-end">
                <label for="listing-type">Listing Type:</label>
              </b-col>
              <b-col xl="8" lg="8" md="10" sm="12">
                <b-form-select id="listing-type" v-model="filters.listingtype" @input="synchronizeUrlAndReload" :options="listingOptions"/>
              </b-col>
            </b-row>
          </b-col>
        </b-row>
      </b-tab>
    </b-tabs>
    <posts-filter-listing v-if="!isLoading && items && items.length > 0" :filters="filters" @onSortChange="onSortChange" :items="items"></posts-filter-listing>
    <div v-else-if="isLoading" class="mt-3 pt-3">
      <loader size="lg"/>
    </div>
    <span v-else class="text-muted">Not found</span>
  </div>
</template>

<script>
import constants from '@/shared/ebay/constants'
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import loader from '@/components/_shared/loader'
import paging from '@/components/_shared/paging'
import postsFilterListing from '@/components/eBay/posts/postsFilterListing'

const defaultValues = new ObjectSchema({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 10 },
  search: { type: String, default: '' },
  sort: { type: Number, default: 6 },
  tab: { type: Number, default: 0 },
  listingtype: { type: Number, default: 1 }
})

const queryHelper = new QueryStringHelper(defaultValues)

export default {
  name: 'ebay-posts',
  metaInfo: {
    title: 'eBay Posts'
  },
  props: {
    accountId: { type: Number, required: true }
  },
  data () {
    return {
      isLoading: true,
      filters: defaultValues.getObject(),
      tabs: Object.values(constants.postsOptions),
      itemsTotalCount: 0,
      items: [],
      listingOptions: constants.listingTypes
    }
  },
  created () {
    this.filters = queryHelper.parseQueryStringToObject(this.$router)
    this.populateData()
  },
  computed: {
    selectedTab: {
      get () {
        let res = this.tabs.find(x => x.key === this.filters.tab)
        if (res) {
          return this.filters.tab
        }

        return 0
      },
      set (value) {
        this.filters.tab = value
        this.isLoading = true
        this.synchronizeUrlAndReload()
      }
    }
  },
  components: {
    'paging': paging,
    'loader': loader,
    'posts-filter-listing': postsFilterListing
  },
  methods: {
    pageChanged (newPage) {
      this.filters.page = newPage
      this.isLoading = true
      this.synchronizeUrlAndReload()
    },
    onSortChange (sort) {
      this.filters.sort = sort
      this.synchronizeUrlAndReload()
    },
    changePageSize (newPageSize) {
      this.filters.pageSize = newPageSize
      this.filters.page = 1
      this.isLoading = true
      this.synchronizeUrlAndReload()
    },
    synchronizeUrlAndReload () {
      queryHelper.rebuildParamsInQueryString(this.$router, this.filters)
      this.populateData()
    },
    populateData () {
      this.$store.dispatch('eBay/getPostItems', { accountId: this.accountId, filters: this.filters }).then(res => {
        this.itemsTotalCount = res.data.TotalItems
        this.items = res.data.Items
      }).catch(ex => {
        this.$toaster.error('Something went wrong!')
        this.$logger.handleError(ex, 'Cannot populate post items')
      }).finally(() => {
        this.isLoading = false
      })
    }
  }
}
</script>

<style scoped>
.custom-paging {
  position: absolute;
  right: 5px;
  top: 35px;
  z-index: 2;
}
</style>
