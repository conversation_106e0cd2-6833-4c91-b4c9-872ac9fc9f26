<template>
  <b-row class="mobile-adaptive">
    <b-col class="fixed-width">
      <b-form-input ref="input" v-if="isInputMode" :name="name" :value="customSelectValue.inputVal" @input="onInput" class="flex-grow"></b-form-input>
      <b-form-select v-else :value="customSelectValue.selectVal" @input="onInput" :options="options" class="flex-grow">
      </b-form-select>
    </b-col>
    <span class="change-input" @click="changeMode">{{getToggleTitle}}</span>
  </b-row>
</template>

<script>

let searchTimerId = 0

export default {
  props: {
    customSelectValue: {},
    name: String,
    options: Array,
    inputMode: Boolean
  },
  data () {
    return {
      isInputMode: false
    }
  },
  created () {
    this.isInputMode = !this.options.some(x => x.value.toString() === this.customSelectValue.selectVal.toString())
  },
  computed: {
    getToggleTitle () {
      return this.isInputMode ? 'Select From List' : 'Enter Custom'
    }
  },
  methods: {
    changeMode () {
      this.isInputMode = !this.isInputMode
    },
    onInput (newVal) {
      if ((this.isInputMode && this.customSelectValue.inputVal.toString().toLowerCase() === newVal.toString().toLowerCase()) ||
        (!this.isInputMode && this.customSelectValue.selectVal.toString().toLowerCase() === newVal.toString().toLowerCase())) {
        return
      }

      if (searchTimerId !== -1) {
        clearTimeout(searchTimerId)
      }

      searchTimerId = setTimeout(() => {
        let option = this.options.find(
          x => (this.isInputMode && x && x.text.toString().toLowerCase() === newVal.toString().toLowerCase()) ||
          (!this.isInputMode && x && x.value.toString().toLowerCase() === newVal.toString().toLowerCase())) || {
            text: newVal,
            value: null
          }

        this.$emit('input', newVal)
        this.$emit('change', {
          ...option,
          isInputMode: this.isInputMode
        })
      }, 250)
    }
  }
}
</script>

<style scoped lang="scss">
.change-input {
  text-decoration: underline;
  color: #ca1713;
  white-space: nowrap;
  cursor: pointer;
  align-self: center;
}
.flex-grow {
  flex-grow: 1
}
.fixed-width {
  flex: none;
  width: 20.25rem;
}

.mobile-adaptive {
  flex-wrap: nowrap;
}

  @media (max-width:576px) {
    .fixed-width {
      flex-basis: 0;
      flex-grow: 1;
      width: auto;
    }
    .mobile-adaptive {
      width: 100%;
    }
  }
</style>
