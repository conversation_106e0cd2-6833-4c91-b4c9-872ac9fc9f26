<template>
  <div>
    <b-row>
      <b-col>
        <h4>Reports</h4>
      </b-col>
      <b-col v-if="isContactReportTabOpened">
        <b-btn class="float-right" variant="primary"  @click="showFilterManager">Filter Manager</b-btn>
      </b-col>
    </b-row>
    <b-tabs v-model="selectedTab" class="nav-tabs-top nav-responsive-sm" no-fade>
      <b-tab class="p-3" v-for="tab in getTabOptions" :title="tab.text" :key="tab.value">
        <component v-if="isReady" :is="getReportFilterFormComponentName" @generate="generate" :isGenerationPerformed="isGenerationPerformed" :filters="getFilters" :csvData="csvData" :csvFields="getCsvFields" :csvLabels="getCsvLabels"/>
      </b-tab>
    </b-tabs>
    <b-card>
      <component :is="getReportListingComponentName" :items="reports" :fields="getTableFields" :isGenerationFinished="isGenerationFinished"/>
    </b-card>
    <contactReportFilters :isVisible="isVisibleContactReportFIlterManager" @hide="hideContactsReportFilterManager"/>
  </div>
</template>

<script>
import contactsReportFilterForm from '@/components/reports/filterForms/contactsReportFilterForm'
import accountLeadsSettingsReportFilterForm from '@/components/reports/filterForms/accountLeadsSettingsReportFilterForm'
import accountLeadsWebFormReportFilterForm from '@/components/reports/filterForms/accountLeadsWebFormReportFilterForm'
import contactsReportListing from '@/components/reports/listings/contactsReportListing'
import accountLeadsSettingsReportListing from '@/components/reports/listings/accountLeadsSettingsReportListing'
import accountLeadsWebFormReportListing from '@/components/reports/listings/accountLeadsWebFormReportListing'
import contactReportFilters from '@/components/reports/contactReportFilters'
import ReportWrapper from '@/pages/account/reports/ReportWrapper'

import constants from '@/shared/reports/constants'
import globals from '../../../globals'
import { mapGetters } from 'vuex'

export default {
  name: 'reports',
  metaInfo: {
    title: 'Reports'
  },
  data () {
    return {
      isReady: false,
      reports: [],
      csvData: [],
      isGenerationPerformed: false,
      isGenerationFinished: false,
      tabValue: 0,
      isVisibleContactReportFIlterManager: false
    }
  },
  created () {
    this.tabValue = this.$route.query['tab'] ? +this.$route.query['tab'] : constants.reportTypes.contactsReport.value
    this.initFiltersFromUserStorage()
  },
  components: {
    contactsReportFilterForm,
    accountLeadsSettingsReportFilterForm,
    accountLeadsWebFormReportFilterForm,
    contactsReportListing,
    accountLeadsSettingsReportListing,
    accountLeadsWebFormReportListing,
    contactReportFilters
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {getDataFromStorage: key => { return null }}
    },
    selectedTab: {
      get () {
        let tab = this.getTabOptions.find(x => x.value === this.tabValue)
        return tab ? tab.value : constants.reportTypes.contactsReport.value
      },
      set (value) {
        this.setDefaultData()
        this.tabValue = value
        this.initFiltersFromUserStorage()
      }
    },
    isContactReportTabOpened () {
      return constants.reportTypes.contactsReport.value === this.tabValue
    },
    currentReport () {
      switch (this.tabValue) {
        case constants.reportTypes.contactsReport.value:
          return ReportWrapper.contactsReport
        case constants.reportTypes.accountLeadsWebFormsReport.value:
          return ReportWrapper.accountsLeadsWebFormReport
        case constants.reportTypes.accountLeadsSettingsReport.value:
          return ReportWrapper.accountsLeadsSettingsReport
      }
      return {}
    },
    getFilters () {
      return this.currentReport.filters
    },
    getCsvFields () {
      return this.currentReport.csvFields
    },
    getCsvLabels () {
      let labels = Object.create(null)
      this.currentReport.csvLabels.map(x => {
        labels[x.key] = x.label
      })
      return labels
    },
    getTableFields () {
      return this.currentReport.tableFields
    },
    getReportFilterFormComponentName () {
      switch (this.tabValue) {
        case constants.reportTypes.contactsReport.value:
          return 'contactsReportFilterForm'
        case constants.reportTypes.accountLeadsWebFormsReport.value:
          return 'accountLeadsWebFormReportFilterForm'
        case constants.reportTypes.accountLeadsSettingsReport.value:
          return 'accountLeadsSettingsReportFilterForm'
      }
      return ''
    },
    getReportListingComponentName () {
      switch (this.tabValue) {
        case constants.reportTypes.contactsReport.value:
          return 'contactsReportListing'
        case constants.reportTypes.accountLeadsWebFormsReport.value:
          return 'accountLeadsWebFormReportListing'
        case constants.reportTypes.accountLeadsSettingsReport.value:
          return 'accountLeadsSettingsReportListing'
      }
      return ''
    },
    getTabOptions () {
      return Object.values(constants.reportTypes)
    }
  },
  methods: {
    generate (filters) {
      this.isGenerationPerformed = true
      this.currentReport.generate(filters).then(res => {
        this.reports = res.data.items || []
        this.csvData = globals().getClonedValue(res.data.items || [])
        this.isGenerationFinished = true
        this.saveFiltersToUserStorage(filters, this.currentReport.USER_STORAGE_FILTER_KEY)
      }).catch(ex => {
        this.$toaster.exception(ex, 'Failed on generation report', { timeout: 5000 })
      }).finally(() => {
        this.isGenerationPerformed = false
      })
    },
    saveFiltersToUserStorage (filters, key) {
      this.$store.dispatch('users/saveDataInUserStorage', {key: key, data: JSON.stringify(filters)}).then(res => {
      }).catch(ex => {
        this.$logger.handleError(ex, 'Failed save report filters in user storage')
      })
    },
    setDefaultData () {
      this.isGenerationPerformed = false
      this.isGenerationFinished = false
      this.reports = []
      this.csvData = []
    },
    initFiltersFromUserStorage () {
      let dataFromStorage = this.user.getDataFromStorage(this.currentReport.USER_STORAGE_FILTER_KEY)
      if (dataFromStorage) {
        let parsedFilters = null
        try {
          parsedFilters = JSON.parse(dataFromStorage)
        } catch (ex) {
          console.error(ex)
        }
        if (parsedFilters) {
          this.$set(this.currentReport, 'filters', parsedFilters)
        }
      }
      this.isReady = true
    },
    showFilterManager () {
      this.isVisibleContactReportFIlterManager = true
    },
    hideContactsReportFilterManager () {
      this.isVisibleContactReportFIlterManager = false
    }
  }
}
</script>

<style lang="css">
.account-id-fix-column {
  width: 100px;
  position: sticky;
  left: 0;
  z-index: 999;
}
.account-name-fix-column {
  position: sticky;
  left: 75px;
  z-index: 999;
}

.table-striped tbody tr:nth-child(odd) .account-id-fix-column {
  background: #fafafa;
}
.table-striped tbody tr:nth-child(even) .account-id-fix-column {
  background: #ffffff;
}

.table-striped tbody tr:nth-child(odd) .account-name-fix-column {
  background: #fafafa;
}
.table-striped tbody tr:nth-child(even) .account-name-fix-column {
  background: #ffffff;
}

.table-hover tbody tr:hover .account-id-fix-column,
.table-hover tbody tr:hover .account-name-fix-column {
  color: #4E5155;
  background: rgba(245, 245, 245);
}
</style>
