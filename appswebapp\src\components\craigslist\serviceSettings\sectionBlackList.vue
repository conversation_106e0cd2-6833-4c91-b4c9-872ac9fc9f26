<template>
<div v-if='isReady' class='mb-3'>
  <div class="border-bottom">
    <b-row>
      <b-col><h6 class="float-left">Black List Settings</h6></b-col>
      <b-col>
      <b-btn v-if="!isViewMode" size="sm" class="float-right d-none d-sm-block fixed-sizes ml-2" @click="cancel()" :disabled='isDisabled'>Cancel</b-btn>
      <b-btn v-if="isViewMode" variant="secondary" size="sm" class="fixed-sizes d-none d-sm-block mb-2 float-right" @click="setEditMode()" :disabled='isDisabled'><font-awesome-icon icon="pencil-alt" size="sm" /> <span class="btn-title">Edit</span></b-btn>
      <b-btn v-else variant="primary" size="sm" class="fixed-sizes d-none d-sm-block mb-2 float-right " @click="saveSettings()" :disabled='isDisabled'><font-awesome-icon icon="cloud-upload-alt" /> <span class="btn-title">Save</span></b-btn>
      </b-col>
    </b-row>
  </div>
  <div>
    <div v-if='isViewMode' class='mt-2'>
      <multiInput
        title="Black list keywords"
        :data='blackListSettings.blackListKeywords'
        :viewMode='true'
      />
      <multiInput
        title="Response keywords not to send to error handling system"
        :data='blackListSettings.keywordsNotToRaiseError'
        :viewMode='true'
      />
      <multiInput
        title="Emails to notify if skiping vehicle due to black list"
        :data='blackListSettings.emailsToNotifyAboutSkippedVehicles'
        :viewMode='true'
      />
    </div>
    <div v-else class='mt-2'>
      <ValidationObserver ref="validator">
      <multiInput
        title="Black list keywords"
        :data='blackListSettingsToUpdate.blackListKeywords'
        :viewMode='false'
        :input='updateBlackListKeywords'
      />
      <multiInput
        title="Response keywords not to send to error handling system"
        :data='blackListSettingsToUpdate.keywordsNotToRaiseError'
        :viewMode='false'
        :input='updateKeywordsNotToRaiseError'
      />
      <multiInput
        title="Emails to notify if skiping vehicle due to black list"
        :data='blackListSettingsToUpdate.emailsToNotifyAboutSkippedVehicles'
        :viewMode='false'
        :input='updateEmailsToNotifyAboutSkippedVehicles'
      />
      </ValidationObserver>
    </div>
  </div>
  <b-btn v-if="isViewMode" variant="secondary" size="sm" class="float-right w-100 d-block d-sm-none my-2" @click="setEditMode()" :disabled='isDisabled'><font-awesome-icon icon="pencil-alt" size="sm" /> <span class="btn-title">Edit</span></b-btn>
  <b-btn v-else variant="primary" size="sm" class="float-right w-100 d-block d-sm-none my-2" @click="saveSettings()" :disabled='isDisabled'><font-awesome-icon icon="cloud-upload-alt" /> <span class="btn-title">Save</span></b-btn>
  <b-btn v-if="!isViewMode" size="sm" class="w-100 d-block d-sm-none mt-2" @click="cancel()" :disabled='isDisabled'>Cancel</b-btn>
</div>
</template>
<script>
import { mapGetters } from 'vuex'
import globals from '../../../globals'
import multiInput from './helper/multiInput'

export default {
  name: 'section-black-list',
  props: {
    isDisabled: {
      type: Boolean,
      required: true
    }
  },
  data () {
    return {
      isReady: false,
      isViewMode: true,
      blackListSettingsToUpdate: {}
    }
  },
  components: {
    'multiInput': multiInput
  },
  created () {
    this.populateData()
  },
  computed: {
    ...mapGetters('craigslistServiceSettings', ['blackListSettings', 'deactivationSettings'])
  },
  methods: {
    setEditMode () {
      this.isViewMode = false
    },
    saveSettings () {
      this.$refs.validator.validate().then(isValid => {
        if (isValid) {
          this.isViewMode = true
          this.$store.commit('craigslistServiceSettings/setBlackListSettings', this.blackListSettingsToUpdate)
          this.updateSettingsData()
        }
      })
    },
    cancel () {
      this.populateData()
      this.isViewMode = true
    },
    populateData () {
      this.blackListSettingsToUpdate = globals().getClonedValue(this.blackListSettings)
      this.isReady = true
    },
    updateSettingsData () {
      const data = {
        craigslistServiceDeactivationSettings: this.deactivationSettings,
        craigslistBlackListSettings: this.blackListSettingsToUpdate
      }

      this.$emit('updateSettings', data)
    },
    updateBlackListKeywords (data) {
      this.blackListSettingsToUpdate.blackListKeywords = data
    },
    updateKeywordsNotToRaiseError (data) {
      this.blackListSettingsToUpdate.keywordsNotToRaiseError = data
    },
    updateEmailsToNotifyAboutSkippedVehicles (data) {
      this.blackListSettingsToUpdate.emailsToNotifyAboutSkippedVehicles = data
    }
  }
}
</script>
