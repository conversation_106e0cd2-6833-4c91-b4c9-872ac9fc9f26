<template>
  <div class="fullwidth-element bg-dark dark text-white">

    <div class="container-fluid container-p-y">

      <div class="chart-filters text-center">
        <graph-tabs
          :tabsConfiguration="barTabs"
          @tabSelect="onGraphTypeChanged"
        />
      </div>

      <div class="row">
        <div class="col-md-8">
          <vue-echart :options="barOptions" :auto-resize="true"></vue-echart>
        </div>
        <div class="col-md-4">
          <div class="d-flex align-items-center position-relative mt-4" style="height:300px;">
            <vue-echart :options="pieOptions" :auto-resize="true"></vue-echart>
          </div>
        </div>
      </div>

      <div class="row mt-3 widget-metric-higlights">

        <div class="col-6 col-sm-6 col-lg-6 col-xl">
          <summary-card
            label="Sessions"
            :value="summary.sessions"
            :delta="summary.sessionsDelta"
            :rangeLabel="summary.label">
            <i class="ion ion-md-browsers h1 m-0 opacity-25 d-none d-sm-inline"></i>
          </summary-card>
        </div>

        <div class="col-6 col-sm-6 col-lg-6 col-xl">
          <summary-card
            label="Page Views"
            :value="summary.pageViews"
            :delta="summary.pageViewsDelta"
            :rangeLabel="summary.label">
            <i class="ion ion-md-eye h1 m-0 opacity-25 d-none d-sm-inline"></i>
          </summary-card>
        </div>

        <div class="col-6 col-sm-6 col-lg-6 col-xl">
          <summary-card
            label="Avg. Time on Site"
            :value="summary.avgSessionDuration"
            :delta="summary.avgSessionDurationDelta"
            :rangeLabel="summary.label">
            <template slot="value" slot-scope="valueScope">
              {{$locale.getSecondsDurationFormatted(valueScope.value)}}
            </template>
            <i class="ion ion-md-time h1 m-0 opacity-25 d-none d-sm-inline"></i>
          </summary-card>
        </div>

        <div class="col-6 col-sm-6 col-lg-6 col-xl">
          <summary-card
            label="Leads"
            :value="summary.totalLeads"
            :delta="summary.totalLeadsDelta"
            :rangeLabel="summary.label">
            <i class="ion ion-md-chatboxes h1 m-0 opacity-25 d-none d-sm-inline"></i>
          </summary-card>
        </div>

      </div>

    </div>

  </div>
</template>

<script>
import dateHelper from '../../../plugins/locale/date'

import 'echarts/lib/chart/bar'
import 'echarts/lib/chart/pie'
import 'echarts/lib/component/tooltip'
import 'echarts/lib/component/legend'

const colors = ['#dc3545', '#28a745', '#007bff', '#ffc107', '#7751bd', '#1abc9c', '#e67e22', '#3498db', '#ff5722', '#ff4514', '#647c8a', '#3f51b5', '#2196f3', '#00b862', '#afdf0a']

export default {
  name: 'channel-segments-summary',
  props: {
    barItems: { type: Array, required: true },
    pieItems: { type: Array, required: true },
    summary: { type: Object, required: true },
    barTimeFormat: { type: String, required: true }
  },
  components: {
    'vue-echart': () => import('vue-echarts/components/ECharts.vue'),
    'summary-card': () => import('../summaryCard.vue'),
    'graph-tabs': () => import('../graphTabs.vue')
  },
  data () {
    return {
      bar: {
        activeTab: 'sessions'
      }
    }
  },
  computed: {
    barTabs () {
      return {
        defaultTabKey: this.bar.activeTab,
        tabs: [
          {
            key: 'sessions',
            label: 'Sessions',
            iconClass: 'ion-md-browsers'
          }, {
            key: 'pageViews',
            label: 'Views',
            iconClass: 'ion-md-eye'
          }, {
            key: 'sessionDuration',
            label: 'Time',
            iconClass: 'ion-md-time'
          }, {
            key: 'totalLeads',
            label: 'Leads',
            iconClass: 'ion-md-chatboxes'
          }
        ]
      }
    },
    barOptions () {
      return {
        color: '#6b0001',
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            switch (this.bar.activeTab) {
              case 'sessionDuration':
                return `${params[0].name}<br />${this.$locale.getSecondsDurationFormatted(params[0].value)}`
              default:
                return `${params[0].name}<br />${this.$locale.formatNumber(params[0].value)}`
            }
          },
          axisPointer: {
            type: 'line',
            lineStyle: {
              color: '#444'
            }
          },
          textStyle: {
            fontSize: 13
          }
        },
        grid: {
          left: '0',
          right: '0',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: this.barItems.map(x => {
              return dateHelper.getDayFormatted(x.dateFrom, this.barTimeFormat)
            }),
            axisTick: {
              show: true,
              alignWithLabel: true
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: '#444'
              }
            },
            axisLabel: {
              color: 'rgba(255, 255, 255, .9)'
            }
          }
        ],
        yAxis: [
          {
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#444'
              }
            },
            axisLabel: {
              color: 'rgba(255, 255, 255, .9)',
              formatter: (value, index) => {
                if (this.bar.activeTab === 'sessionDuration') {
                  return this.$locale.getSecondsDurationFormatted(value)
                } else {
                  return value
                }
              }
            },
            type: 'value'
          }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 50,
            shadowColor: 'rgba(0, 0, 0, 0.3)'
          }
        },
        series: [
          {
            barWidth: '4',
            type: 'bar',
            data: this.barItems.map(x => x[this.bar.activeTab]),
            itemStyle: {
              normal: {
                color: '#dc3545'
              },
              emphasis: {
                shadowBlur: 50,
                shadowColor: '#dc3545',
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [{
                    offset: 0.5, color: '#dc3545'
                  }, {
                    offset: 1, color: '#ff5a44'
                  }],
                  globalCoord: false
                }
              }
            }
          }],
        animationDuration: 2000
      }
    },
    pieOptions () {
      return {
        color: colors,
        tooltip: {
          show: true,
          trigger: 'item',
          formatter: (params) => {
            return params.name +
              '<br />' +
              this.$locale.formatNumber(params.value) +
              ` (${params.percent}%)`
          },
          textStyle: {
            fontSize: 13
          }
        },
        legend: {
          data: this.pieItems.map(x => x.channelGrouping),
          show: false,
          y: 'top',
          x: 'right',
          orient: 'vertical',
          textStyle: {
            color: 'rgba(255, 255, 255, .9)'
          }
        },
        visualMap: {
          show: true,
          min: 80,
          max: 600,
          inRange: {
            colorLightness: [0, 1]
          }
        },
        series: [{
          type: 'pie',
          radius: ['55%', '80%'],
          center: ['50%', '50%'],
          avoidLabelOverlap: false,
          minAngle: 10,
          label: {
            normal: {
              show: false,
              position: 'center',
              formatter: '{b}\n{d}%'
            },
            emphasis: {
              show: true,
              textStyle: {
                fontSize: '18',
                fontWeight: 'bold'
              }
            }
          },
          labelLine: {
            normal: {
              show: false
            },
            emphasis: {
              show: false
            }
          },
          data: this.pieItems.map(x => ({
            value: x[this.bar.activeTab],
            name: x.channelGrouping
          })),
          itemStyle: {
            normal: {
              shadowBlur: 50,
              shadowColor: 'rgba(0, 0, 0, 0.1)'
            },
            emphasis: {
              shadowBlur: 50,
              shadowColor: 'rgba(0, 0, 0, 0.3)'
            }
          }
        }],
        animationDuration: 2000
      }
    }
  },
  methods: {
    onGraphTypeChanged (newTab) {
      this.bar.activeTab = newTab.key
    }
  }
}
</script>

<style scoped>

</style>
