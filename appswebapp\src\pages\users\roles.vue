<template>
<div v-if="!isManagingMode">
  <b-row>
    <b-col>
      <h4>Roles Management</h4>
    </b-col>
    <b-col>
       <b-btn variant="primary btn-round" class="float-right" size="sm" @click="addUserRole"><span class="ion ion-ios-add"></span><span class="d-none d-sm-inline">&nbsp; Add New Role</span></b-btn>
    </b-col>
  </b-row>
  <b-card v-if="!isLoading && items && items.length > 0">
    <b-table
      :items="items"
      :fields="getTableFields"
      hover
      striped
      responsive
    >
      <template #cell(details)="data">
        <b-btn size="sm" @click="data.toggleDetails">{{ data.detailsShowing ? 'Hide' : 'Show' }} Details</b-btn>
      </template>
      <template #row-details="data">
        <b-card>
          <ValidationObserver v-slot="{valid}">
            <b-row v-if="isEnabledToEdit(data.item.id)">
              <b-col>
                <b-btn v-if="isEnabledToDelete(data.item.id)" class="float-right ml-2" size="sm" @click="deleteUserRole(data.item.id)" variant="dark">Delete</b-btn>
                <b-btn size="sm" class="float-right ml-2" @click="cancelUserRole(data)">Cancel</b-btn>
                <l-button :loading="data.item.isProcessing" :disabled="!valid" size="sm" variant="primary" class="float-right" @click="saveUserRole(data)">Save</l-button>
              </b-col>
            </b-row>
            <ValidationProvider name="Role Name" rules="required" v-slot="{errors}" immediate>
            <b-form-group label="Name:">
              <b-form-input :name="data.item.name" v-model="data.item.itemCopy.name" :disabled="!isEnabledToEdit(data.item.id)"></b-form-input>
              <span class="text-danger">{{errors[0]}}</span>
            </b-form-group>
            </ValidationProvider>
            <ValidationProvider name="Permissions" rule="required" v-slot="{errors}" immediate>
            <b-form-group label="Permissions:">
              <b-form-checkbox-group
                v-model="data.item.itemCopy.permissions"
                :id="`${data.item.name}-permissions`"
              >
                <b-tabs class="nav-tabs-left" no-fade>
                  <b-tab class="p-4" :title="permissionsBySection.title" v-for="permissionsBySection in getPermissionsBySection" :key="permissionsBySection.title">
                    <template #title>
                      <div class="d-flex justify-content-between align-items-center">
                        <span>{{permissionsBySection.title}}</span>
                        <b-badge class="ml-1" pill variant="info">{{permissionsBySection.permissions.filter(x => data.item.itemCopy.permissions && data.item.itemCopy.permissions.includes(x.value)).length}}</b-badge>
                      </div>
                    </template>
                    <b-form-checkbox
                      :name="`role-permissions`"
                      v-for="permission in permissionsBySection.permissions"
                      :key="permission.title"
                      :value="permission.value">
                      {{permission.title}}
                    </b-form-checkbox>
                  </b-tab>
                </b-tabs>
              </b-form-checkbox-group>
              <span class="text-danger">{{errors[0]}}</span>
            </b-form-group>
            </ValidationProvider>
          </ValidationObserver>
        </b-card>
      </template>
      <template #cell(actions)="data">
        <b-btn size="sm" variant="primary" @click="showDetails(data)">
          <font-awesome-icon :icon="data.detailsShowing ? 'caret-up' : 'caret-down'"/>
        </b-btn>
      </template>
    </b-table>
  </b-card>
  <loader v-else-if="isLoading" class="my-4" size="lg"/>
  <span v-else class="text-muted">Not found</span>
</div>
<role-manager v-else :isEditMode="isEditMode" :role="managedRole" @cancel="onCancelManaging" />
</template>

<script>
import globals from '../../globals'
import UserManagementService from '../../services/users/UserManagementService'
import loader from '../../components/_shared/loader'
import roleManager from '../../components/users/roleManager'
import {permissionsBySection, roleTypes} from '@/shared/users/constants'

export default {
  name: 'roles-management',
  metaIfo: {
    title: 'User Roles Management'
  },
  data () {
    return {
      items: [],
      isLoading: true,
      isManagingMode: false,
      isEditMode: false,
      managedRole: {}
    }
  },
  components: {
    loader,
    roleManager
  },
  computed: {
    baseUserRoles () {
      return Object.values(roleTypes)
    },
    getPermissionsBySection () {
      return Object.values(permissionsBySection)
    },
    getTableFields () {
      return [
        {
          key: 'name',
          label: 'Name',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'actions',
          label: '',
          thClass: 'float-right',
          tdClass: 'py-2 align-middle float-right'
        }
      ]
    }
  },
  created () {
    this.populateData()
  },
  methods: {
    populateData () {
      UserManagementService.getUserRoles().then(res => {
        this.items = res.data
      }).catch(ex => {
        this.$toaster.error('Cannot get roles from the server')
      }).finally(() => {
        this.isLoading = false
      })
    },
    deleteUserRole (id) {
      UserManagementService.deleteUserRole(id).then(res => {
        this.$toaster.success('Role Deleted Successfully')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Failed to delete role')
      }).finally(() => {
        this.populateData()
      })
    },
    isEnabledToDelete (id) {
      return !(this.baseUserRoles.some(x => x.value === id) || id === 0)
    },
    isEnabledToEdit (id) {
      return id !== 0 && id !== roleTypes.admin.value && id !== roleTypes.eBizDev.value
    },
    addUserRole () {
      this.isEditMode = false
      this.managedRole = {}
      this.isManagingMode = true
    },
    editUserRole (role) {
      this.isEditMode = true
      this.managedRole = globals().getClonedValue(role)
      this.isManagingMode = true
    },
    onCancelManaging () {
      this.isManagingMode = false
      this.isLoading = true
      this.populateData()
    },
    showDetails (data) {
      this.$set(data.item, 'itemCopy', globals().getClonedValue(data.item))
      data.toggleDetails()
    },
    cancelUserRole (data) {
      this.$set(data.item, 'itemCopy', globals().getClonedValue(data.item))
      data.toggleDetails()
    },
    saveUserRole (data) {
      this.$set(data.item, 'isProcessing', true)
      UserManagementService.updateUserRole(data.item.itemCopy).then(res => {
        this.$toaster.success('Role Updated Successfully')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Failed To Update Role')
        this.$logger.handleError(ex, 'Exception occurred on update role')
      }).finally(() => {
        this.$set(data.item, 'isProcessing', false)
        this.populateData()
      })
    }
  }
}
</script>
