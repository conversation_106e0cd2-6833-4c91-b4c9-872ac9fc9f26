<template>
  <div v-if='isReady'>
    <h4>Craigslist Service Settings</h4>
     <b-card>
      <sectionDeactivation @updateSettings='updateSettings' :isDisabled='isDisabled'/>
      <sectionBlackList @updateSettings='updateSettings' :isDisabled='isDisabled'/>
     </b-card>
  </div>
</template>

<script>
import sectionDeactivation from '@/components/craigslist/serviceSettings/sectionDeactivation'
import sectionBlackList from '@/components/craigslist/serviceSettings/sectionBlackList'

export default {
  name: 'craigslist-service-settings',
  metaInfo: {
    title: 'Craigslist Service settings'
  },
  components: {
    'sectionDeactivation': sectionDeactivation,
    'sectionBlackList': sectionBlackList
  },
  data () {
    return {
      isReady: false,
      isDisabled: false
    }
  },
  created () {
    this.populateData().then(() => { this.isReady = true })
  },
  methods: {
    async populateData () {
      await this.$store.dispatch('craigslistServiceSettings/populateCraigslistServiceSettings')
    },
    refresh () {
      this.isReady = false
      this.populateData().then(() => { this.isReady = true })
    },
    async updateSettings (settingsData) {
      await this.$store.dispatch('craigslistServiceSettings/updateCraigslistServiceSettings', { data: settingsData }).then(x => {
        this.$toaster.success('Craigslist Service Settings Successfully Updated')
      })
        .catch(ex => {
          this.$toaster.error(`Updated Craigslist Service Settings is failed. Error: ${ex}`)
          this.$logger.handleError(ex, `Failed updated craigslist service settings`)
        })
        .finally(() => {
          this.refresh()
          this.isDisabled = false
        })
    }
  }
}
</script>
