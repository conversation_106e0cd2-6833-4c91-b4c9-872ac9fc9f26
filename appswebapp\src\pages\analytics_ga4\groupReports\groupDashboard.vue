<template>
  <div>
    <dashboard-summary-cards
      :summaryCards="summary"
    ></dashboard-summary-cards>

    <div class="row mt-3">

      <div class="col-lg-12 col-xl-7">

        <div class="row">

          <div class="col-12 col-md-6 col-lg-6 col-xl-12">
            <!-- Website Overview -->
            <dashboard-website-overview-graph
              :mobileTimelineItems="sessionsTimelineItems.mobile"
              :desktopTimelineItems="sessionsTimelineItems.desktop"
              :barTimeFormat="barTimeFormat"
              :reportGroupId="reportGroupId"
            ></dashboard-website-overview-graph>
            <!-- / Website Overview -->
          </div>

          <div class="col-12 col-md-6 col-lg-6 col-xl-12">
            <!-- Traffic Sources -->
            <dashboard-traffic-sources-graph
              :trafficSourcesTimelineItems="trafficSourcesTimelineItems"
              :reportGroupId="reportGroupId"
            ></dashboard-traffic-sources-graph>
            <!-- / Traffic Sources -->
          </div>

        </div>

      </div>

      <div class="col-lg-12 col-xl-5">
        <!-- Device sessions -->
        <dashboard-device-sessions-graph
          :mobileCount="summaryDevices.mobile"
          :desktopCount="summaryDevices.desktop"
        ></dashboard-device-sessions-graph>
        <!-- / Device sessions -->

        <!-- Rebuild Analytics -->
        <b-card v-can="permission.AnalyticsFullAccess" no-body class="mb-0 mb-xl-3">
          <b-card-header header-tag="h5" class="border-0 pb-0">
            <span class="card-header-title">Rebuild Analytics</span>
          </b-card-header>
          <div class="row">
            <div class="col-md-12">
              <!-- Rebuild range dropdown -->
              <custom-range-form
                class="p-4"
                :disabled="isRebuildInProgress"
                :enableCustomRangeDisclaimer="false"
                actionText="Rebuild"
                @submitted="onCustomRebuildRequested"
              />
            </div>
          </div>
        </b-card>
        <!-- / Rebuild Analytics -->
      </div>
    </div>

  </div>

</template>

<style src="@/vendor/styles/pages/analytics.scss" lang="scss"></style>

<script>
import dateHelper from '@/plugins/locale/date'
import analyticsBuilders from '../../../shared/analytics/builders'
import permission from '@/shared/common/permissions'
import analyticsHelper from '../helpers.js'
import baseGroupReportPage from './baseGroupReportPage.js'

import DashboardSummaryCards from '../../../components/analytics_ga4/summaries/dashboardSummaryCards'
import DashboardWebsiteOverviewGraph from '../../../components/analytics_ga4/summaries/dashboardWebsiteOverviewGraph'
import DashboardTrafficSourcesGraph from '../../../components/analytics_ga4/summaries/dashboardTrafficSourcesGraph'
import DashboardDeviceSessionsGraph from '../../../components/analytics_ga4/summaries/dashboardDeviceSessionsGraph'
import CustomRangeForm from '../../../components/analytics_ga4/customRangeForm'

const filterManager = analyticsBuilders.getFilterManager({
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' }
})

export default {
  mixins: [baseGroupReportPage],
  name: 'group-dashboard',
  metaInfo: {
    title: 'Analytics - Dashboard'
  },
  components: {
    CustomRangeForm,
    DashboardDeviceSessionsGraph,
    DashboardTrafficSourcesGraph,
    DashboardWebsiteOverviewGraph,
    DashboardSummaryCards
  },
  created () {
    this.$store.commit('analyticsGa4/setAnalyticsName', 'Dashboard')
    this.$store.commit('analyticsGa4/setBlackThemeOn', false)
  },
  data () {
    return {
      page: {
        filter: filterManager.defaultValue
      },
      permission,
      isRebuildInProgress: false,
      summary: {
        sessions: 0,
        sessionsDelta: null,
        totalLeads: 0,
        totalLeadsDelta: null,
        bounceRate: 0,
        bounceRateDelta: null,
        avgSessionDuration: 0,
        avgSessionDurationDelta: null
      },
      summaryDevices: {
        mobile: 0,
        desktop: 0
      },
      sessionsTimelineItems: {
        mobile: [],
        desktop: []
      },
      trafficSourcesTimelineItems: [],
      cache: {}
    }
  },
  computed: {
    filterManager () {
      return filterManager
    }
  },
  methods: {
    async onCustomRebuildRequested (rebuildDates) {
      this.isRebuildInProgress = true

      try {
        await this.$store.dispatch(
          'analyticsGa4/doCustomGroupAnalyticsRebuild',
          {
            reportGroupId: this.reportGroupId,
            dateFrom: dateHelper.getDayFormatted(rebuildDates.dateFrom),
            dateTo: dateHelper.getDayFormatted(rebuildDates.dateTo)
          }
        )
        this.$toaster.success('Rebuild scheduled', { timeout: 8000 })
      } catch (err) {
        this.$toaster.error(
          (err.response || {}).data.executionResultMessage || "An error occurred. Can't rebuild group reports",
          { timeout: 8000 }
        )
        this.$logger.handleError(err, 'Can\'t rebuild reports')
      }

      this.isRebuildInProgress = false
    },
    async updateStatistics () {
      try {
        await Promise.all([
          this.updateSummaryCards(),
          this.updateDeviceSessionsReport(),
          this.updateDeviceSessionsTimeline(),
          this.updateTrafficSourcesTimeline()
        ])
      } catch (err) {
        this.$toaster.error('Failed to get data from server', { timeout: 8000 })
        this.$logger.handleError(err, 'Can\'t update statistics', {filter: this.page.filter, cache: this.cache})
      }
    },
    async updateSummaryCards () {
      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getGroupDashboardSummaryCards',
        {
          reportGroupId: this.reportGroupId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo
        }
      )

      this.summary = {
        ...this.summary,
        ...store.groupWebsiteOverviewSummary.data,
        ...store.groupWebsiteEngagementSummary.data
      }
    },
    async updateDeviceSessionsReport () {
      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getGroupDashboardDeviceSessionsReport',
        {
          reportGroupId: this.reportGroupId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo
        }
      )

      this.summaryDevices.mobile = store.groupMobileOverviewSummary.data.sessions
      this.summaryDevices.desktop = store.groupDesktopOverviewSummary.data.sessions
    },
    async updateDeviceSessionsTimeline () {
      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getGroupDashboardDeviceSessionsTimeline',
        {
          reportGroupId: this.reportGroupId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo
        }
      )

      this.sessionsTimelineItems.mobile = store.groupMobileOverviewTimelineSummary.data.items
      this.sessionsTimelineItems.desktop = store.groupDesktopOverviewTimelineSummary.data.items
    },
    async updateTrafficSourcesTimeline () {
      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getGroupDashboardTrafficSourcesTimeline',
        {
          reportGroupId: this.reportGroupId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo
        }
      )

      this.trafficSourcesTimelineItems = store.groupTrafficSourcesTimeline.data || []
    }
  }
}
</script>
