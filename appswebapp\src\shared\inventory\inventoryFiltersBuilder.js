import ConditionFilter from './filterOption/conditionFilter'
import PriceFilter from './filterOption/priceFilter'
import defaultFiltersScheme from './inventoryFilters'
import { priceTypes } from './inventoryTypes'

const defaultFilters = defaultFiltersScheme.getObject()

class InventoryFiltersBuilder {
  constructor (inventoryFilters) {
    this.inventoryFilters = inventoryFilters
  }

  changePage (page) {
    this.inventoryFilters.pageNumber = page
  };
  changePageSize (pageSize) {
    this.inventoryFilters.pageSize = pageSize
    this.changePage(defaultFilters.pageNumber)
  };
  changeSearch (search) {
    this.inventoryFilters.search = search
    this.changePage(defaultFilters.pageNumber)
  };
  changeSort (sort) {
    this.inventoryFilters.sortType = sort
    this.changePage(defaultFilters.pageNumber)
  };
  changeActiveButton (filterOption) {
    this.changePage(defaultFilters.pageNumber)
    this.inventoryFilters.optionId = filterOption.id
    this.resetPrices()

    if (filterOption instanceof PriceFilter) {
      this.inventoryFilters.priceType = filterOption.priceType
      this.setPriceByPriceType(filterOption.priceType)
      this.inventoryFilters.condition = defaultFilters.condition
    } else if (filterOption instanceof ConditionFilter) {
      this.inventoryFilters.condition = filterOption.conditionType
      this.inventoryFilters.priceType = defaultFilters.priceType
    }
  };
  resetPrices () {
    this.inventoryFilters.priceTo = defaultFilters.priceTo
    this.inventoryFilters.highPriceTo = defaultFilters.highPriceTo
    this.inventoryFilters.lowPriceTo = defaultFilters.lowPriceTo
  };
  setPriceByPriceType (priceType) {
    switch (priceType) {
      case priceTypes.all:
        this.inventoryFilters.priceTo = 0
        break
      case priceTypes.highPrice:
        this.inventoryFilters.highPriceTo = 0
        break
      case priceTypes.lowPrice:
        this.inventoryFilters.lowPriceTo = 0
        break
    }
  };
}

export default InventoryFiltersBuilder
