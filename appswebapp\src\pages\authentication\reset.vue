<template>
  <div class="authentication-wrapper authentication-2 px-4">
    <div class="authentication-inner py-5">

      <!-- Form -->
      <form class="card">
        <div class="p-4 p-sm-5">

          <!-- Logo -->
          <div class="d-flex justify-content-center align-items-center pb-1 mb-2">
            <span class="app-brand-text demo">eBizAutos</span>
          </div>
          <!-- / Logo -->

          <h5 class="text-center text-muted font-weight-normal mb-4">Reset Your Password</h5>

          <hr class="mt-0 mb-4">

          <p>
            Enter your email address and we will send you a link to reset your password.
          </p>

          <template v-if="!!rejectionMessage">
            <hr class="mt-0 mb-4">
            <b-alert show variant="danger">{{rejectionMessage}}</b-alert>
          </template>

          <b-form-group>
            <b-input v-model="credentials.username" placeholder="Enter your username" autofocus />
          </b-form-group>

          <b-form-group>
            <b-input v-model="credentials.email" placeholder="Enter your email address" />
          </b-form-group>

          <b-btn type="submit" @click.prevent="resetPassword" variant="primary" :disabled="isSubmitLocked" :block="true">Send password reset email</b-btn>
          <router-link tag="button" to="/login" class="btn btn-primary btn-block">Back to login screen</router-link>

        </div>
      </form>
      <!-- / Form -->

    </div>
  </div>
</template>

<!-- Page -->
<style src="@/vendor/styles/pages/authentication.scss" lang="scss"></style>

<script>
import axios from 'axios'

export default {
  name: 'account-password',
  metaInfo: {
    title: 'Password Reset'
  },
  data: () => ({
    credentials: {
      username: '',
      email: ''
    },
    isSubmitLocked: false,
    rejectionMessage: ''
  }),
  methods: {
    resetPassword () {
      this.lockForm()

      axios
        .post('/api/auth/resetpassword', {
          'userName': this.credentials.username,
          'email': this.credentials.email
        })
        .then(result => {
          this.unlockForm()
          this.$router.replace({ path: '/reset-done' })
        })
        .catch(reason => {
          this.unlockForm()
          const errorMessage = (((reason || {}).response || {}).data || '')
          this.rejectionMessage = (errorMessage.length > 0 && errorMessage.length < 30) ? errorMessage : 'Reset failed'
        })
    },
    lockForm () {
      this.isSubmitLocked = true
    },
    unlockForm () {
      this.isSubmitLocked = false
    }
  }
}
</script>
