<template>
  <description-listing v-if="inventoryDisplayType === displayTypes.default"
  :inventoryData="inventoryData"
  :siteSettings="siteSettings"
  :sortField="sortBy"
  :sortOrder="sortDesc"
  @sortChanged="sortingChanged"
  @listingChanged="onListingChanged"
  ></description-listing>

  <pricing-listing v-else-if="inventoryDisplayType === displayTypes.pricing"
  :inventoryData="inventoryData"
  :siteSettings="siteSettings"
  :sortField="sortBy"
  :sortOrder="sortDesc"
  @sortChanged="sortingChanged"
  @listingChanged="onListingChanged"
  ></pricing-listing>

  <inventory-merchandising v-else-if="inventoryDisplayType === displayTypes.merchandising"
  :inventoryData="inventoryData"
  :siteSettings="siteSettings"
  :sortField="sortBy"
  :sortOrder="sortDesc"
  @sortChanged="sortingChanged"
  @listingChanged="onListingChanged"
  ></inventory-merchandising>
</template>

<script>
import globals from '@/globals'
import { sortTypes, displayTypes } from '@/shared/inventory/inventoryTypes'
import permission from '@/shared/common/permissions'

import inventoryDescriptions from './inventoryDescriptionsListing'
import inventoryPricing from './inventoryPricingListing'
import inventoryMerchandising from './inventoryMerchandisingListing'

export default {
  name: 'inventory-listing',
  components: {
    'description-listing': inventoryDescriptions,
    'pricing-listing': inventoryPricing,
    'inventory-merchandising': inventoryMerchandising
  },
  props: {
    inventoryData: {
      type: Object,
      default: function () {
        return {
          vehicles: [],
          pageNumber: 1,
          pageSize: 25
        }
      }
    },
    inventoryDisplayType: {
      type: Number,
      required: true
    },
    siteSettings: {
      type: Object,
      required: true
    }
  },
  data: function () {
    return {
      permission,
      fields: [],
      sortMappings: [
        { sortType: sortTypes.stockAsc, sortBy: 'stockNumber', sortDesc: false },
        { sortType: sortTypes.stockDesc, sortBy: 'stockNumber', sortDesc: true },

        { sortType: sortTypes.inStockAsc, sortBy: 'age', sortDesc: true },
        { sortType: sortTypes.inStockDesc, sortBy: 'age', sortDesc: false },

        { sortType: sortTypes.photosCountAsc, sortBy: 'actualPhotosCount', sortDesc: false },
        { sortType: sortTypes.photosCountDesc, sortBy: 'actualPhotosCount', sortDesc: true },

        { sortType: sortTypes.lowPriceAsc, sortBy: 'lowPrice', sortDesc: false },
        { sortType: sortTypes.lowPriceDesc, sortBy: 'lowPrice', sortDesc: true },

        { sortType: sortTypes.highPriceAsc, sortBy: 'highPrice', sortDesc: false },
        { sortType: sortTypes.highPriceDesc, sortBy: 'highPrice', sortDesc: true },

        { sortType: sortTypes.yearDescMakeAsc, sortBy: 'title', sortDesc: true },
        { sortType: sortTypes.yearAscMakeAsc, sortBy: 'title', sortDesc: false },
        { sortType: sortTypes.makeAsc, sortBy: 'title', sortDesc: true },
        { sortType: sortTypes.makeDesc, sortBy: 'title', sortDesc: false },

        { sortType: sortTypes.keywordsAsc, sortBy: 'hasKeywords', sortDesc: true },
        { sortType: sortTypes.keywordsDesc, sortBy: 'hasKeywords', sortDesc: false },

        { sortType: sortTypes.galleryDescriptionAsc, sortBy: 'galleryDescription', sortDesc: true },
        { sortType: sortTypes.galleryDescriptionDesc, sortBy: 'galleryDescription', sortDesc: false },

        { sortType: sortTypes.homepageFeaturedAsc, sortBy: 'isHomepageFeatured', sortDesc: true },
        { sortType: sortTypes.homepageFeaturedDesc, sortBy: 'isHomepageFeatured', sortDesc: false },

        { sortType: sortTypes.promotionalFlagAsc, sortBy: 'hasToIncludePromotionalFlag', sortDesc: true },
        { sortType: sortTypes.promotionalFlagDesc, sortBy: 'hasToIncludePromotionalFlag', sortDesc: false }
      ],
      displayTypes: displayTypes,
      currentTileSortType: sortTypes.yearDescMakeAsc,
      selectedFilters: globals().getClonedValue(this.inventoryData.inventoryFilters)
    }
  },
  methods: {
    getSortType (orderBy, sortDesc) {
      if (orderBy === 'title') {
        return this.currentTileSortType
      } else {
        this.currentTileSortType = undefined
        const sortMapping = this.sortMappings.find(v => v.sortBy === orderBy && v.sortDesc === sortDesc)

        if (sortMapping !== undefined) {
          return sortMapping.sortType
        } else {
          return this.sortMappings.find(x => x.sortType === sortTypes.yearDescMakeAsc).sortType
        }
      }
    },
    sortingChanged (arg) {
      if (arg.sortBy === 'title') {
        switch (this.currentTileSortType) {
          case sortTypes.yearDescMakeAsc:
            this.currentTileSortType = this.sortMappings.find(x => x.sortType === sortTypes.yearAscMakeAsc).sortType
            break
          case sortTypes.yearAscMakeAsc:
            this.currentTileSortType = this.sortMappings.find(x => x.sortType === sortTypes.makeAsc).sortType
            break
          case sortTypes.makeAsc:
            this.currentTileSortType = this.sortMappings.find(x => x.sortType === sortTypes.makeDesc).sortType
            break
          case sortTypes.makeDesc:
            this.currentTileSortType = this.sortMappings.find(x => x.sortType === sortTypes.yearDescMakeAsc).sortType
            break
          default:
            this.currentTileSortType = this.sortMappings.find(x => x.sortType === sortTypes.yearDescMakeAsc).sortType
            break
        }
      } else {
        this.currentTileSortType = this.getSortType(arg.sortBy, arg.sortDesc)
      }

      this.$emit('sortChanged', this.currentTileSortType)
    },
    onListingChanged () {
      this.$emit('listingChanged', this.currentTileSortType)
    }
  },
  computed: {
    sortBy: {
      get () {
        const sortMapping = this.sortMappings.find(v => v.sortType === this.selectedFilters.sortType)
        return (sortMapping !== undefined) ? sortMapping.sortBy : 'title'
      },
      set: function (value) {
      }
    },
    sortDesc: {
      get () {
        const sortMapping = this.sortMappings.find(v => v.sortType === this.selectedFilters.sortType)
        return (sortMapping !== undefined) ? sortMapping.sortDesc : true
      },
      set (value) {
      }
    }
  }
}
</script>

<style lang="scss">
.change-status-dd > button{
  padding: 0 0 1px 0;
  &:hover{
     padding: 0 0 1px 0;
  }
}
.no-border > button{
  border: 0 !important;
  &:hover{
    border: 0 !important;
  }
}
</style>
