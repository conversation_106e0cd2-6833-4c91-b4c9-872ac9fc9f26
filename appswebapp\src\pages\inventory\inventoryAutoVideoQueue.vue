<template>
   <div>
    <div class="d-flex flex-row">
      <h4 class="mt-3">AutoVideo Queue</h4>
      <!-- Pagination -->
      <paging
        class="d-none d-md-block p-0"
        :pageNumber="filters.page"
        :pageSize="filters.pageSize"
        :totalItems="itemsTotalCount"
        @numberChanged="pageChanged"
        @changePageSize="changePageSize"
      />
    </div>
    <b-card>
      <b-form v-on:submit.prevent="applyFilter">
        <div class="form-row">
          <b-col class="mt-2 mb-2" lg="2" md="2" sm="12">
            <b-form-input
              v-model="filters.search"
              placeholder="Search..."
              autocomplete="off"
            >
            </b-form-input>
          </b-col>

          <b-col class="mt-2 mb-2" lg="3" md="5" sm="12">
            <b-input-group class="flex-nowrap">
              <b-input-group-prepend is-text>
                <i class="ion ion-md-calendar" slot="prepend"></i>
              </b-input-group-prepend>
              <date-time-picker
                ref="timeFrom"
                v-model="filters.dateFrom"
                :options="filterTimeOptions"
                format="MM/DD/YYYY HH:mm"
                placeholder="Date From"
                className="form-control"
                @change="onTimeFromInputChange"
              />
              <b-input-group-append
                is-text
                v-show="filters.dateFrom"
                @click="filters.dateFrom = null"
              >
                <i class="ion ion-md-close"></i>
              </b-input-group-append>
            </b-input-group>
          </b-col>

          <b-col class="mt-2 mb-2" lg="3" md="5" sm="12">
            <b-input-group class="flex-nowrap">
              <b-input-group-prepend is-text>
                <i class="ion ion-md-calendar" slot="prepend"></i>
              </b-input-group-prepend>
              <date-time-picker
                ref="timeTo"
                v-model="filters.dateTo"
                :options="filterTimeOptions"
                format="MM/DD/YYYY HH:mm"
                placeholder="Date To"
                className="form-control"
                @change="onTimeToInputChange"
              />
              <b-input-group-append
                is-text
                v-show="filters.dateTo"
                @click="filters.dateTo = null"
              >
                <i class="ion ion-md-close"></i>
              </b-input-group-append>
            </b-input-group>
          </b-col>
          <b-col class="mt-2 mb-2" lg="2" md="6" sm="12">
            <b-form-select v-model="filters.taskStatus" :options="getTaskStatusOptions"></b-form-select>
          </b-col>
          <b-col class="mt-2 mb-2" lg="2" md="6" sm="12">
            <b-btn block  variant="primary" type="submit">Submit</b-btn>
          </b-col>
        </div>
      </b-form>
      <span class="text-primary">
        <b>Tasks in queue: {{totalNotProcessedTasksCount}}</b>
      </span>
    </b-card>
    <b-card v-if="!isLoading">
      <b-table
        :items="items"
        :fields="getTableFields"
        :sort-by="tableSortBy"
        :sort-desc="tableSortDesc"
        @sort-changed="onSortChanged"
        :striped="true"
        :bordered="false"
        :no-sort-reset="true"
        :no-local-sorting="true"
        responsive
        show-empty
        table-class="products-table card-table"
      >
      </b-table>
      <paging
        :pageNumber="filters.page"
        :pageSize="filters.pageSize"
        :totalItems="itemsTotalCount"
        titled
        pageSizeSelector
        @numberChanged="pageChanged"
        @changePageSize="changePageSize"
      />
    </b-card>
    <loader v-else class="mt-4" size="lg"/>
  </div>
</template>

<script>
import videoEncoderTypes from '@/shared/inventory/videoEncoderTypes'
import { getSkipNumber } from '@/shared/common/paginationHelpers'
import paging from '@/components/_shared/paging'
import loader from '../../components/_shared/loader.vue'
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import moment from 'moment'
import VideoEncoderService from '../../services/inventory/VideoEncoderService'

const defaultFilters = new ObjectSchema({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  search: { type: String, default: '' },
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  taskStatus: { type: Number, default: null },
  sort: { type: Number, default: videoEncoderTypes.sortTypes.dateCreatedDesc }
})
const queryHelper = new QueryStringHelper(defaultFilters)

export default {
  name: 'inventory-auto-video-queue',
  metaInfo: {
    title: 'AutoVideo Queue'
  },
  data () {
    return {
      items: [],
      itemsTotalCount: 0,
      totalNotProcessedTasksCount: 0,
      isLoading: true,
      filters: defaultFilters.getObject(),
      filterTimeOptions: {
        autoUpdateInput: false,
        singleDatePicker: true,
        timePicker: true,
        timePicker24Hour: true,
        maxDate: new Date()
      }
    }
  },
  components: {
    paging,
    'date-time-picker': () => import('@gravitano/vue-date-range-picker/src/components/DateRangePicker'),
    loader
  },
  computed: {
    getTaskStatusOptions () {
      return [{value: null, text: 'All Task Statuses'}].concat(
        Object.keys(videoEncoderTypes.taskStatusTypes).map(
          function (namedIndex) {
            return videoEncoderTypes.taskStatusTypes[namedIndex]
          })
      )
    },
    getTableFields () {
      return [
        {
          key: 'accountId',
          label: 'Account Id',
          sortable: true,
          sortTypeAsc: videoEncoderTypes.sortTypes.accountIdAsc,
          sortTypeDesc: videoEncoderTypes.sortTypes.accountIdDesc,
          thStyle: 'min-width: 125px;',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'vin',
          label: 'Vin',
          sortable: true,
          sortTypeAsc: videoEncoderTypes.sortTypes.vinAsc,
          sortTypeDesc: videoEncoderTypes.sortTypes.vinDesc,
          thStyle: 'min-width: 200px;',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'taskStatus',
          label: 'Status',
          sortable: true,
          sortTypeAsc: videoEncoderTypes.sortTypes.taskStatusAsc,
          sortTypeDesc: videoEncoderTypes.sortTypes.taskStatusDesc,
          thStyle: 'min-width: 100px;',
          tdClass: 'py-2 align-middle',
          formatter: val => (Object.values(videoEncoderTypes.taskStatusTypes).find(x => x.value === val) || { text: '-' }).text
        },
        {
          key: 'taskPriority',
          label: 'Priority',
          sortable: true,
          sortTypeAsc: videoEncoderTypes.sortTypes.taskPriorityAsc,
          sortTypeDesc: videoEncoderTypes.sortTypes.taskPriorityDesc,
          thStyle: 'min-width: 100px;',
          tdClass: 'py-2 align-middle',
          formatter: val => (Object.values(videoEncoderTypes.taskPriorityTypes).find(x => x.value === val) || { text: '-' }).text
        },
        {
          key: 'dateTimeCreated',
          label: 'Created',
          sortable: true,
          sortTypeAsc: videoEncoderTypes.sortTypes.dateCreatedAsc,
          sortTypeDesc: videoEncoderTypes.sortTypes.dateCreatedDesc,
          thStyle: 'min-width: 150px;',
          tdClass: 'py-2 align-middle',
          formatter: val => val ? moment(val).format('MM/DD/YYYY hh:mm:ss A') : '-'
        },
        {
          key: 'dateTimeProcessed',
          label: 'Processed',
          sortable: true,
          sortTypeAsc: videoEncoderTypes.sortTypes.dateProcessedAsc,
          sortTypeDesc: videoEncoderTypes.sortTypes.dateProcessedDesc,
          thStyle: 'min-width: 150px;',
          tdClass: 'py-2 align-middle',
          formatter: val => val ? moment(val).format('MM/DD/YYYY hh:mm:ss A') : '-'
        }
      ]
    },
    refDateTimeFrom () {
      return (this.$refs.timeFrom || {}).$el || {}
    },
    refDateTimeTo () {
      return (this.$refs.timeTo || {}).$el || {}
    },
    sortType () {
      return this.filters.sort
    },
    tableSortBy () {
      const sortedColumn = this.getTableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.getTableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sortType
      } else {
        return false
      }
    }
  },
  created () {
    this.filters = queryHelper.parseQueryStringToObject(this.$router)
    this.populateData()
  },
  methods: {
    pageChanged (newPage) {
      this.filters.page = newPage
      this.synchronizeUrlAndReload()
    },
    changePageSize (newSize) {
      this.filters.pageSize = newSize
      this.filters.page = 1
      this.synchronizeUrlAndReload()
    },
    onSortChanged (value) {
      const sortingColumn = this.getTableFields.find(x => x.key === value.sortBy)
      if (value.sortDesc) {
        this.filters.sort = sortingColumn.sortTypeDesc
      } else {
        this.filters.sort = sortingColumn.sortTypeAsc
      }
      this.populateData()
    },
    onTimeFromInputChange (newVal) {
      this.refDateTimeFrom.value = newVal || this.filters.dateFrom || null
    },
    onTimeToInputChange (newVal) {
      this.refDateTimeTo.value = newVal || this.filters.dateTo || null
    },
    applyFilter () {
      this.filters.page = 1
      this.synchronizeUrlAndReload()
    },
    synchronizeUrlAndReload () {
      this.isLoading = false
      queryHelper.rebuildParamsInQueryString(this.$router, this.filters)
      this.populateData()
    },
    populateData () {
      let apiFilters = {
        search: this.filters.search,
        skip: getSkipNumber(this.filters.page, this.filters.pageSize),
        limit: this.filters.pageSize,
        dateFrom: this.filters.dateFrom,
        dateTo: this.filters.dateTo,
        taskStatus: this.filters.taskStatus,
        sort: this.filters.sort
      }

      VideoEncoderService.getPhotoToVideoQueueListing(apiFilters).then(res => {
        this.items = res.data.items
        this.itemsTotalCount = res.data.totalItemsCount
        this.totalNotProcessedTasksCount = res.data.totalNotProcessedTasksCount
      }).catch(ex => {
        this.$toaster.error('Something went wrong!')
      }).finally(() => {
        this.isLoading = false
      })
    }
  }
}
</script>
