<template>
  <details-section title="Miscellaneous" @cancel="onCancel" v-model="mode" v-if="truckAttributes">
    <div class="view" v-if="mode === 'view'">

      <auto-detail-row :title="truckFeatures.sleeperDetails.name" :text="getSelectedAttributeOption(truckFeatures.sleeperDetails).key"/>

      <auto-detail-row :title="truckFeatures.sleeperSize.name" :text="getSelectedAttributeOption(truckFeatures.sleeperSize).key"/>

      <auto-detail-row :title="truckFeatures.sleeperRoof.name" :text="getSelectedAttributeOption(truckFeatures.sleeperRoof).key"/>

      <auto-detail-row :title="truckFeatures.sleeperBunkType.name" :text="getSelectedAttributeOption(truckFeatures.sleeperBunkType).key"/>

      <auto-detail-row title="Number Of Doors" :text="vehicle.doors"/>

      <auto-detail-row :title="truckFeatures.sideFairing.name" :text="getSelectedAttributeOption(truckFeatures.sideFairing).key"/>

      <auto-detail-row :title="truckFeatures.fifthWheel.name" :text="getSelectedAttributeOption(truckFeatures.fifthWheel).key"/>

      <auto-detail-row :title="truckFeatures.roofFairing.name" :text="getSelectedAttributeOption(truckFeatures.roofFairing).key"/>

      <auto-detail-row :title="truckFeatures.wheelFairing.name" :text="getSelectedAttributeOption(truckFeatures.wheelFairing).key"/>

      <auto-detail-row :title="truckFeatures.wheelBase.name" :text="getSelectedAttributeOption(truckFeatures.wheelBase).key"/>

      <auto-detail-row :title="truckFeatures.apuType.name" :text="getSelectedAttributeOption(truckFeatures.apuType).key"/>

    </div>

    <div class="edit" v-else-if="mode === 'edit'">

      <auto-detail-row :title="truckFeatures.sleeperDetails.name" v-model="truckFeatures.sleeperDetails.value" validation-rule="xml"/>

      <auto-detail-row :title="truckFeatures.sleeperSize.name" v-model="truckFeatures.sleeperSize.value" validation-rule="xml"/>

      <auto-detail-row :title="truckFeatures.sleeperRoof.name" v-model="truckFeatures.sleeperRoof.value" :options="getNameValueOptions(truckFeatures.sleeperRoof.nameValueOptions)" />

      <auto-detail-row :title="truckFeatures.sleeperBunkType.name" v-model="truckFeatures.sleeperBunkType.value" :options="getNameValueOptions(truckFeatures.sleeperBunkType.nameValueOptions)" />

      <auto-detail-row title="Number Of Doors" v-model="vehicle.doors" validation-rule="max_value:255|min_value:0"/>

      <auto-detail-row :title="truckFeatures.sideFairing.name" v-model="truckFeatures.sideFairing.value" :options="getNameValueOptions(truckFeatures.sideFairing.nameValueOptions)" />

      <auto-detail-row :title="truckFeatures.fifthWheel.name" v-model="truckFeatures.fifthWheel.value" :options="getNameValueOptions(truckFeatures.fifthWheel.nameValueOptions)" />

      <auto-detail-row :title="truckFeatures.roofFairing.name" v-model="truckFeatures.roofFairing.value" :options="getNameValueOptions(truckFeatures.roofFairing.nameValueOptions)" />

      <auto-detail-row :title="truckFeatures.wheelFairing.name" v-model="truckFeatures.wheelFairing.value" :options="getNameValueOptions(truckFeatures.wheelFairing.nameValueOptions)" />

      <auto-detail-row :title="truckFeatures.wheelBase.name" v-model="truckFeatures.wheelBase.value" validation-rule="xml"/>

      <auto-detail-row :title="truckFeatures.apuType.name" v-model="truckFeatures.apuType.value" :options="getNameValueOptions(truckFeatures.apuType.nameValueOptions)" />

    </div>
  </details-section>
</template>

<script>
import { mapGetters } from 'vuex'
import detailsSection from '@/components/details/detailsSection'
import autoDetailRow from '../helpers/autoDetailRow'
import detailRow from '../helpers/detailRow'
import featuresHelper from '../../../shared/details/featuresHelper'

export default {
  name: 'truck-miscellaneous',
  data () {
    return {
      mode: 'view'
    }
  },
  computed: {
    ...mapGetters('details', ['vehicle', 'truckAttributes']),
    truckFeatures () {
      return {
        sleeperDetails: this.getFeatureById(-10101),
        sleeperSize: this.getFeatureById(-10105),
        sleeperRoof: this.getFeatureById(-10102),
        sleeperBunkType: this.getFeatureById(-10106),
        sideFairing: this.getFeatureById(-10107),
        fifthWheel: this.getFeatureById(-10103),
        roofFairing: this.getFeatureById(-10108),
        wheelFairing: this.getFeatureById(-10109),
        wheelBase: this.getFeatureById(-10104),
        apuType: this.getFeatureById(-10111)
      }
    }
  },
  methods: {
    getFeatureById (id) {
      return featuresHelper.getFeatureById(this.truckAttributes, id)
    },
    getNameValueOptions (nameValueOptions) {
      return featuresHelper.getNameValueOptions(nameValueOptions)
    },
    getSelectedAttributeOption (attribute) {
      return featuresHelper.getSelectedAttributeOption(attribute)
    },
    onCancel () {
      this.$forceUpdate()
    }
  },
  components: {
    'details-section': detailsSection,
    'auto-detail-row': autoDetailRow,
    'detail-row': detailRow
  }
}
</script>
