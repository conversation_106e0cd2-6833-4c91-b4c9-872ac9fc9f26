<template>
  <b-modal
    title='Add New Template'
    :visible='isModalVisible'
    @hide='onHide'
    size="lg"
  >
    <detail-row fixedPayloadWidth>
      <span slot="title">Template Name:</span>
      <b-form-input slot="payload" v-model='model.name'/>
    </detail-row>
    <detail-row fixedPayloadWidth>
      <span slot="title">Email Subject:</span>
      <b-form-input slot="payload" v-model='model.subject'/>
    </detail-row>
    <detail-row fixedPayloadWidth>
      <span slot="title">Include Vehicle Details:</span>
      <b-form-checkbox v-model='model.hasToDisplayVehicleDetails' slot="payload" />
    </detail-row>
    <detail-row bigPayloadWidth>
      <span slot="title">Message:</span>
      <b-form-textarea slot="payload" v-model='model.messageBody' rows='5'/>
    </detail-row>
    <detail-row fixedPayloadWidth>
      <span slot="title">Include Email Signature:</span>
      <b-form-checkbox v-model='model.hasToEnableSignature' slot="payload"/>
    </detail-row>
    <detail-row fixedPayloadWidth>
      <span slot="title"></span>
      <div class='d-flex flex-column' slot="payload">
        <span>Contact Person</span>
        <span class="text-muted">Dealership Name</span>
        <span class="text-muted">City, State</span>
      </div>
    </detail-row>
    <detail-row largePayloadWidth>
      <span slot="title">Used Standard Links:</span>
      <div slot="payload" >
        <b-form-radio v-model='model.hasToUseCustomLink' @change="onChangeChecked" :value='false' name="custom-link">
        <span>Link:</span>
        <a href="#" class="text-primary h6"><u>View Presentation</u></a>
        <span>|</span>
        <a href="#" class="text-primary h6"><u>View Our Other Vehicles</u></a>
        </b-form-radio>
      </div>
    </detail-row>
    <detail-row largePayloadWidth>
      <span slot="title">Used Custom Links:</span>
      <div slot="payload" class="custom-flex-position">
        <b-form-radio v-model='model.hasToUseCustomLink' @change="onChangeChecked" :value='true' name="custom-link" class="mt-2 mr-0">
        </b-form-radio>
        <span class="mt-2 mr-2">Link Text:</span>
        <b-form-input class="custom-input-width" v-model='model.customLinkText' :disabled='!model.hasToUseCustomLink' placeholder="Link Text"></b-form-input>
      </div>
    </detail-row>
    <detail-row largePayloadWidth>
      <span slot="title"></span>
      <div slot="payload" class="custom-flex-position">
        <span class="mt-2 mr-2 custom-margin">Link:</span>
        <b-form-input class="custom-input-width" v-model='model.customLinkHref' :disabled='!model.hasToUseCustomLink' placeholder="www.linkexample.com"></b-form-input>
      </div>
    </detail-row>
    <template #modal-footer>
      <b-button size="sm" @click="onHide" class="text-center">Close</b-button>
      <b-button size="sm" @click="onAdd" variant="primary">Add</b-button>
    </template>
  </b-modal>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'

export default {
  name: 'leads-create-new-template-modal',
  props: {
    model: { type: Object, required: true },
    isModalVisible: { type: Boolean, required: true }
  },
  components: {
    'detail-row': detailRow
  },
  methods: {
    onHide () {
      this.$emit('hide')
    },
    onAdd () {
      this.$emit('add', this.model)
    },
    onChangeChecked (checked) {
      this.model.hasToUseCustomLink = checked
    }
  }
}
</script>

<style scoped>
.custom-flex-position {
  display: flex;
  flex-direction: row;
  flex-shrink: 1;
}
.custom-margin {
  margin-left: 3.5rem;
}

.custom-input-width {
  width: 65%;
}

@media (max-width: 576px) {
  .custom-flex-position {
   flex-direction: column;
  }
  .custom-margin {
    margin-left: 0;
  }
  .custom-input-width {
    width: 100%;
  }
}
</style>
