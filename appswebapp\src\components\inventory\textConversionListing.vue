<template>
  <div>
    <b-card v-if="!isLoading" class="table-responsive">
      <b-table
        :items="items"
        :fields="tableFields"
        :sort-by="tableSortBy"
        :sort-desc="tableSortDesc"
        @sort-changed="onSortChanged"
        :striped="true"
        :bordered="false"
        :no-sort-reset="true"
        :no-local-sorting="true"
        responsive
        show-empty
        table-class="products-table card-table"
      >
        <template #cell(actions)="data">
          <b-dropdown variant="outline-secondary icon-btn btn-round" size="sm" right no-caret boundary='viewport'>
            <template slot="button-content">
              <i class="ion ion-ios-more m-0"></i><span class="sr-only">Actions</span>
            </template>
            <b-dropdown-item @click="onEdit(data.item)">Edit</b-dropdown-item>
            <b-dropdown-item @click="deleteTextConversion(data.item.id)">Remove</b-dropdown-item>
          </b-dropdown>
        </template>
      </b-table>
      <paging
        :totalItems="totalItemsCount"
        :pageNumber="filter.page"
        :pageSize="filter.pageSize"
        titled
        pageSizeSelector
        @numberChanged="onPageChanged"
        @changePageSize="onPageSizeChanged" />
    </b-card>
    <loader v-else size="lg"/>
  </div>
</template>

<script>
import paging from '@/components/_shared/paging'
import loader from '@/components/_shared/loader'
import VideoEncoderService from '../../services/inventory/VideoEncoderService'
import videoEncoderTypes from '../../shared/inventory/videoEncoderTypes'

const textConversionTypes = videoEncoderTypes.textConversionTypes
const textConversionSortTypes = videoEncoderTypes.textConversionSortTypes

export default {
  name: 'inventory-text-conversion-listing',
  props: {
    filter: { type: Object, required: true },
    isLoading: { type: Boolean, required: true },
    items: { type: Array, required: true },
    totalItemsCount: { type: Number, required: true }
  },
  data () {
    return {
    }
  },
  components: {
    paging,
    loader
  },
  computed: {
    tableFields () {
      return [
        {
          key: 'fromValue',
          label: 'From',
          sortable: true,
          sortTypeAsc: textConversionSortTypes.fromAsc,
          sortTypeDesc: textConversionSortTypes.fromDesc,
          thStyle: 'width: 35%; min-width: 200px;',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'toValue',
          label: 'To',
          sortable: true,
          sortTypeAsc: textConversionSortTypes.toAsc,
          sortTypeDesc: textConversionSortTypes.toDesc,
          thStyle: 'width: 35%; min-width: 200px;',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'type',
          label: 'Replace Type',
          sortable: true,
          sortTypeAsc: textConversionSortTypes.typeAsc,
          sortTypeDesc: textConversionSortTypes.typeDesc,
          thStyle: 'min-width: 150px;',
          tdClass: 'py-2 align-middle',
          formatter: val => (Object.values(textConversionTypes).find(x => x.value === val) || { text: '-' }).text
        },
        {
          key: 'actions',
          labe: 'Actions',
          thStyle: 'width: 100px;',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    sortType () {
      return this.filter.sort
    },
    tableSortBy () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sortType
      } else {
        return false
      }
    }
  },
  methods: {
    onPageChanged (newPage) {
      this.filter.page = newPage
      this.refreshData()
    },
    onPageSizeChanged (newSize) {
      this.filter.pageSize = newSize
      this.filter.page = 1
      this.refreshData()
    },
    onSortChanged (value) {
      const sortingColumn = this.tableFields.find(x => x.key === value.sortBy)
      if (value.sortDesc) {
        this.filter.sort = sortingColumn.sortTypeDesc
      } else {
        this.filter.sort = sortingColumn.sortTypeAsc
      }
      this.refreshData()
    },
    refreshData () {
      this.$emit('refresh')
    },
    deleteTextConversion (id) {
      VideoEncoderService.deleteTextConversion(id).then(res => {
        this.$toaster.success('Deleted Successfully')
      }).catch(ex => {
        this.$toaster.error('Failed on deleting')
      }).finally(() => {
        this.refreshData()
      })
    },
    onEdit (item) {
      this.$router.push({name: 'inventory-text-conversion-edit', params: {id: item.id}})
    }
  }
}
</script>

<style>
@media (width < 768px) {
  .crete-new-text-conversion-btn {
    width: 100%;
  }
}
</style>
