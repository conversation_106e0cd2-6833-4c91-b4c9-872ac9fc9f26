<template>
  <div class='mb-4'>
    <editSettingsHelper @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="Other" :isLoading="isUpdatingProcessed" :isDisabled="isDisabled" :isViewMode="isViewMode">
      <div slot="settings-content">
        <detail-row fixedPayloadWidth>
          <span slot="title">Is Active:</span>
          <b-form-checkbox v-model="adminAccountSettingsToUpdate.isActive" slot="payload" :disabled='isViewMode'/>
        </detail-row>
        <detail-row fixedPayloadWidth>
          <span slot="title">Buy caller name:</span>
          <b-form-checkbox v-model="adminAccountSettingsToUpdate.hasToUsePaidUsers" slot="payload" :disabled='isViewMode'/>
        </detail-row>
        <detail-row fixedPayloadWidth>
          <span slot="title">Has custom fees:</span>
          <b-form-checkbox v-model="adminAccountSettingsToUpdate.hasCustomFees" slot="payload" :disabled='isViewMode'/>
        </detail-row>
        <div v-if='adminAccountSettingsToUpdate.hasCustomFees'>
          <detail-row fixedPayloadWidth>
            <span slot="title">Twilio SMS fee:</span>
            <b-form-input type='number' step='0.01' v-model="adminAccountSettingsToUpdate.twilioSMSFee" slot="payload" :disabled='isViewMode'/>
          </detail-row>
          <detail-row fixedPayloadWidth>
            <span slot="title">Twilio call fee:</span>
            <b-form-input type='number' step='0.01' v-model="adminAccountSettingsToUpdate.twilioCallFee" slot="payload" :disabled='isViewMode'/>
          </detail-row>
        </div>
      </div>
    </editSettingsHelper>
  </div>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import { mapGetters } from 'vuex'
import globals from '../../../../globals'

export default {
  name: 'leads-other-settings',
  props: {
    isUpdatingProcessed: {
      type: Boolean,
      required: true
    },
    isDisabled: Boolean
  },
  data () {
    return {
      isViewMode: true,
      adminAccountSettingsToUpdate: {}
    }
  },
  created () {
    this.initData()
  },
  computed: {
    ...mapGetters('leadsAccountSettings', ['adminAccountSettings'])
  },
  components: {
    'detail-row': detailRow,
    editSettingsHelper: () => import('@/components/_shared/editSettingsHelper')
  },
  methods: {
    initData () {
      if (this.adminAccountSettings) {
        this.adminAccountSettingsToUpdate = globals().getClonedValue(this.adminAccountSettings)
      }
    },
    saveSettings () {
      this.$emit('save', this.adminAccountSettingsToUpdate)
      this.isViewMode = true
    },
    changeMode (mode) {
      this.isViewMode = mode
    },
    cancel () {
      this.initData()
      this.changeMode(true)
    }
  }
}
</script>
