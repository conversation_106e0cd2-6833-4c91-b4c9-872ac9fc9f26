import axios from 'axios'

const actions = {
  getCampaignTypes (_, parameters) {
    return axios.get('/api/leads/campaign_types', { params: parameters.filter })
  },
  getGlobalLeadsSettings (_) {
    return axios.get('/api/leads/global/settings')
  },
  updateGlobalLeadsSettings (_, data) {
    return axios.post('/api/leads/global/settings', data)
  },
  getSpamFilters (_, filter) {
    return axios.get('/api/leads/spam/filters', { params: filter })
  },
  getSpamFilterPrototype (_) {
    return axios.get('/api/leads/spam/filters/new')
  },
  getSpamFilterDetails (_, id) {
    return axios.get(`/api/leads/spam/filters/${id}`)
  },
  updateSpamFilter (_, parameters) {
    return axios.post(`/api/leads/spam/filters/${parameters.id}/update`, parameters.data)
  },
  createNewSpamFilter (_, data) {
    return axios.post('/api/leads/spam/filters', data)
  },
  deleteSpamFilter (_, id) {
    return axios.post(`/api/leads/spam/filters/${id}/delete`)
  },
  getSpamMessages (_, filter) {
    return axios.get('/api/leads/spam/messages', { params: filter })
  },
  getSpamMessageDetails (_, id) {
    return axios.get(`/api/leads/spam/messages/${id}`)
  },
  reprocessSpamMessage (_, id) {
    return axios.post(`/api/leads/spam/messages/${id}/reprocess`)
  },
  deleteSpamMessage (_, id) {
    return axios.post(`/api/leads/spam/messages/${id}/delete`)
  },
  getAccountStatistic (_, parameters) {
    return axios.get(`/api/leads/accounts/${parameters.accountId}/statistic/timeline`, { params: parameters.filter })
  },
  async getAccountCampaignsModel (_, parameters) {
    const isEBayEnabledModel = await axios.get(`/api/ebay/${parameters.accountId}/enabled`)
    return axios.get(`/api/leads/accounts/${parameters.accountId}/statistic`, {
      params: {
        ...parameters.filter,
        isEBayEnabled: isEBayEnabledModel.data
      }
    })
  },
  getGlobalStatistic (_, filter) {
    return axios.get('/api/leads/global/statistic/timeline', { params: filter })
  },
  getLeadsAccountListing (_, filter) {
    return axios.get('/api/leads/global/statistic', { params: filter })
  },
  getUserActivityLogs (_, filter) {
    return axios.get('/api/leads/users/activity', { params: filter })
  },
  getUserActivityLogDetails (_, id) {
    return axios.get(`/api/leads/users/activity/${id}/details`)
  },
  getLeadTypes () {
    return axios.get('/api/leads/lead_types')
  },
  getCreditAppPdfFile (_, parameters) {
    return axios.post(`/api/leads/${parameters.accountId}/conversations/${parameters.conversationId}/${parameters.conversationDetailsId}/credit_app`, {password: parameters.password}, {responseType: 'blob'})
  },
  getEBayAuctionsLeadsInfo (_, parameters) {
    return axios.post('/api/leads/ebay/auctions/info', parameters.data)
  },
  getAccessibleLeadAccounts (_) {
    return axios.get('/api/leads/accessible_lead_accounts')
  }
}

export default {
  namespaced: true,
  actions: actions
}
