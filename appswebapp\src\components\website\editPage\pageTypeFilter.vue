<template>
  <div class="mt-2" v-if="pageEditModel.navigationSettings">
    <blog :page-edit-model="pageEditModel" v-if="pageNavigationTypes.blog.value === pageEditModel.navigationSettings.siteNavigationType"/>
    <contactUs :page-edit-model="pageEditModel" v-if="pageNavigationTypes.contactUs.value === pageEditModel.navigationSettings.siteNavigationType"/>
    <customLink :page-edit-model="pageEditModel" v-if="pageNavigationTypes.customLink.value === pageEditModel.navigationSettings.siteNavigationType"/>
    <dealershipInfo :page-edit-model="pageEditModel" v-if="pageNavigationTypes.dealershipInfo.value === pageEditModel.navigationSettings.siteNavigationType"/>
    <financing :page-edit-model="pageEditModel" v-if="pageNavigationTypes.financing.value === pageEditModel.navigationSettings.siteNavigationType"/>
    <freeForm :page-edit-model="pageEditModel" v-if="pageNavigationTypes.freeForm.value === pageEditModel.navigationSettings.siteNavigationType"/>
    <inventory :page-edit-model="pageEditModel" v-if="pageNavigationTypes.inventory.value === pageEditModel.navigationSettings.siteNavigationType"/>
    <newVehicleResearch :page-edit-model="pageEditModel" v-if="pageNavigationTypes.newVehicle.value === pageEditModel.navigationSettings.siteNavigationType"/>
    <partsDepartment :page-edit-model="pageEditModel" v-if="pageNavigationTypes.partsDepartment.value === pageEditModel.navigationSettings.siteNavigationType"/>
    <photoList @refreshData="onRefreshData" :page-edit-model="pageEditModel" v-if="pageNavigationTypes.photoList.value === pageEditModel.navigationSettings.siteNavigationType"/>
    <reviewUs :page-edit-model="pageEditModel" v-if="pageNavigationTypes.reviewUs.value === pageEditModel.navigationSettings.siteNavigationType"/>
    <scheduleTestDrive :page-edit-model="pageEditModel" v-if="pageNavigationTypes.scheduleATestDrive.value === pageEditModel.navigationSettings.siteNavigationType"/>
    <serviceDepartment :page-edit-model="pageEditModel" v-if="pageNavigationTypes.serviceDepartment.value === pageEditModel.navigationSettings.siteNavigationType"/>
    <specials :page-edit-model="pageEditModel" v-if="pageNavigationTypes.specials.value === pageEditModel.navigationSettings.siteNavigationType"/>
    <tradeAppraisal :page-edit-model="pageEditModel" v-if="pageNavigationTypes.tradeAppraisal.value === pageEditModel.navigationSettings.siteNavigationType"/>
    <vehicleFinder :page-edit-model="pageEditModel" v-if="pageNavigationTypes.vehicleFinder.value === pageEditModel.navigationSettings.siteNavigationType"/>
  </div>
</template>

<script>
import {pageNavigationTypes} from '@/shared/website/constants'
import blog from './pages/blog'
import contactUs from './pages/contactUs'
import customLink from './pages/customLink'
import dealershipInfo from './pages/dealershipInfo'
import financing from './pages/financing'
import freeForm from './pages/freeForm'
import inventory from './pages/inventory'
import newVehicleResearch from './pages/newVehicleResearch'
import partsDepartment from './pages/partsDepartment'
import photoList from './pages/photoList'
import reviewUs from './pages/reviewUs'
import scheduleTestDrive from './pages/scheduleTestDrive'
import serviceDepartment from './pages/serviceDepartment'
import specials from './pages/specials'
import tradeAppraisal from './pages/tradeAppraisal'
import vehicleFinder from './pages/vehicleFinder'

export default {
  props: {
    pageEditModel: { type: Object, required: true }
  },
  data () {
    return {
      pageNavigationTypes
    }
  },
  components: {
    blog,
    contactUs,
    customLink,
    dealershipInfo,
    financing,
    freeForm,
    inventory,
    newVehicleResearch,
    partsDepartment,
    photoList,
    reviewUs,
    scheduleTestDrive,
    serviceDepartment,
    specials,
    tradeAppraisal,
    vehicleFinder
  },
  methods: {
    onRefreshData () {
      this.$emit('refreshData')
    }
  }
}
</script>
