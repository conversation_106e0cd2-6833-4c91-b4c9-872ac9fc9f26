<template>
  <div>
    <div v-if="hasToApplyEnvClasses" :class="envClasses">
      {{envDescription}}
    </div>
    <div class="layout-wrapper layout-2">
      <div class="layout-inner">
        <app-layout-sidenav :accountId="accountId" :reportGroupId="reportGroupId" :query="query" />

        <div class="layout-container">
          <app-layout-navbar />
          <app-layout-back-to-admin v-if="canManageMultipleAccount" />

          <!-- Announcements -->
          <b-card class="mb-2" v-for="announcement in getAnnouncements" :key="announcement.announcementId">
            <div v-html="announcement.title"></div>
            <div v-html="announcement.message"></div>
          </b-card>

          <div class="layout-content">
            <div class="router-transitions container-fluid flex-grow-1 container-p-y">
              <transition name="fade" mode="out-in">
                <router-view />
              </transition>
            </div>

            <app-layout-footer />
          </div>
        </div>
      </div>
      <div class="layout-overlay" @click="closeSidenav"></div>
    </div>
  </div>
</template>

<script>
import LayoutNavbar from './LayoutNavbar'
import LayoutSidenav from './LayoutSidenav'
import LayoutFooter from './LayoutFooter'
import LayoutBackToAdmin from './LayoutBackToAdmin'
import {mapGetters} from 'vuex'

export default {
  name: 'app-layout-2',
  props: {
    accountId: {
      type: Number,
      required: false
    },
    reportGroupId: {
      type: String,
      required: false
    },
    query: {
      type: Object,
      required: false
    }
  },
  components: {
    'app-layout-navbar': LayoutNavbar,
    'app-layout-sidenav': LayoutSidenav,
    'app-layout-footer': LayoutFooter,
    'app-layout-back-to-admin': LayoutBackToAdmin
  },
  mounted () {
    this.layoutHelpers.init()
    this.layoutHelpers.update()
    this.layoutHelpers.setAutoUpdate(true)
  },
  beforeDestroy () {
    this.layoutHelpers.destroy()
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    ...mapGetters('userAnnouncements', ['staticAnnouncements']),
    canManageMultipleAccount () {
      return this.userInfo &&
        this.userInfo.user &&
        this.userInfo.user.canManageMultipleAccount
    },
    hasToApplyEnvClasses () {
      return !!this.userInfo && !!this.userInfo.user.isEbizDev
    },
    envClasses () {
      return {
        ['enviroment_' + process.env.NODE_ENV]: this.hasToApplyEnvClasses
      }
    },
    envDescription () {
      return process.env.NODE_ENV + ' Environment (only eBizAutos Developers can see this)'
    },
    getAnnouncements () {
      let announcements = []
      let path = this.$route.path
      if (this.staticAnnouncements && this.staticAnnouncements.length > 0) {
        for (let staticAnnouncement of this.staticAnnouncements) {
          let box = staticAnnouncement.boxes[0]
          if (!box) {
            continue
          }
          if (box.pathAndQuery) {
            let regex = new RegExp(box.pathAndQuery)
            if (regex.test(path)) {
              announcements.push(box)
            }
          } else {
            announcements.push(box)
          }
        }
      }
      return announcements
    }
  },
  methods: {
    closeSidenav () {
      this.layoutHelpers.setCollapsed(true)
    }
  }
}
</script>

<style scoped>
  .layout-content {
    overflow-x: hidden;
    overflow-y: auto;
  }

  .enviroment_development,.enviroment_staging,.enviroment_production {
    text-transform: uppercase;
    color: white;
    text-align: center;
    padding: 0.5rem;
    font-size: 1.2rem;
  }

  .enviroment_development {
    display: none;
  }

  .enviroment_staging {
    background: #cf9326;
  }

  .enviroment_production {
    background: #c90f17;
  }
</style>
