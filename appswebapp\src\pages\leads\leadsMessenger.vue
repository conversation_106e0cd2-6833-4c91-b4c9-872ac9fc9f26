<template>
  <div>
    <b-row class="mb-2">
      <b-col>
        <h4>Messenger</h4>
      </b-col>
      <b-col cols="4">
        <b-overlay v-if="hasAccountsLeadsToDisplay" :show="isAccessibleLeadAccountsLoading">
          <multiSelectWithCheckboxes
            v-model='selectedAccountIds'
            :options="getAccountIdsOptions"
            customMessageOfNoneSelectedItems="Displayed All Accounts"
            name="Accounts"
            @change="selectedAccountIdsChange"
          >
          </multiSelectWithCheckboxes>
        </b-overlay>
      </b-col>
    </b-row>
    <b-card v-if="isLoaded" no-body class='leads-messenger'>
      <messenger ref="messenger" :accountId="accountId" :sitesUrlInfo="sitesUrlInfo" :selectedAccountIds="selectedAccountIds" @applySelectedAccountIds="applySelectedAccountIds"/>
    </b-card>
  </div>
</template>

<script>
import multiSelectWithCheckboxes from '../../components/_shared/multiSelectWithCheckboxes.vue'
import messenger from '@/components/leads/messenger/messenger'
import { mapGetters } from 'vuex'

export default {
  name: 'leads-messenger',
  metaInfo: {
    title: 'Messenger'
  },
  props: {
    accountId: { type: Number, required: true }
  },
  data () {
    return {
      isLoaded: false,
      accessibleLeadAccounts: [],
      selectedAccountIds: [],
      isAccessibleLeadAccountsLoading: true,
      sitesUrlInfo: {}
    }
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {
        canManageAccountApplicationType: () => false,
        getAllowedAccountIdsToDisplayLeads: () => []
      }
    },
    getAccountIdsOptions () {
      let options = []
      let userAccount = this.accessibleLeadAccounts.find(x => x.accountId === this.user.accountId)
      if (userAccount) {
        options.push({
          value: userAccount.accountId,
          text: `${userAccount.accountName || ''}(${userAccount.accountId})`
        })
      }
      this.accessibleLeadAccounts.filter(x => x.accountId !== this.user.accountId)
        .map(x => { options.push({value: x.accountId, text: `${x.accountName || ''}(${x.accountId})`}) })
      return options
    },
    hasAccountsLeadsToDisplay () {
      return this.user.accountId === this.accountId && this.user.getAllowedAccountIdsToDisplayLeads.length > 0
    }
  },
  components: {
    'messenger': messenger,
    multiSelectWithCheckboxes
  },
  created () {
    if (this.hasAccountsLeadsToDisplay) {
      this.populateAccessibleLeadAccounts()
    } else {
      this.populateSitesInfo()
    }
  },
  methods: {
    selectedAccountIdsChange () {
      this.$refs.messenger.selectedAccountIdsChange(this.selectedAccountIds)
    },
    applySelectedAccountIds (accountIds) {
      this.selectedAccountIds = accountIds
    },
    populateAccessibleLeadAccounts () {
      this.$store.dispatch('leads/getAccessibleLeadAccounts').then(res => {
        this.accessibleLeadAccounts = res.data || []
      }).catch(ex => {
        this.$logger.handleError(ex, 'Failed on getting accessible lead accounts')
      }).finally(() => {
        this.isAccessibleLeadAccountsLoading = false
        this.populateSitesInfo()
      })
    },
    async populateSitesInfo () {
      try {
        let siteIds = this.hasAccountsLeadsToDisplay ? this.accessibleLeadAccounts.map(x => x.accountId) : [this.accountId]
        let response = await this.$store.dispatch('website/getSitesBasicInfo', {data: {siteIds: siteIds}})
        this.sitesUrlInfo = response.data.sites
      } catch (ex) {
        this.$logger.handleError(ex, 'Failed to get sites info from server')
      }
      this.isLoaded = true
    }
  }
}
</script>

<style scoped>
.leads-messenger {
  height: auto;
}
@media screen and (max-width: 992px) and (max-height: 992px) {
  body {
    overflow: hidden;
  }
  .leads-messenger {
    height: auto;
  }
}
</style>
