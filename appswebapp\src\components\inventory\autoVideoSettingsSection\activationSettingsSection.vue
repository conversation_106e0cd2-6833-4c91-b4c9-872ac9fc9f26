<template>
  <editSettingsHelper @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="Activation Settings" :isLoading="isUpdatingProcessed" :isViewMode="isViewMode" :readOnlyMode="readOnlyMode">
    <template slot="settings-content">
      <detail-row v-if="hasAccessToAutoVideoStatus" :fixed-payload-width="true" :title-position="'start'">
        <span slot="title">
          AutoVideo Status:
        </span>
        <div slot="payload" class="w-100">
          <b-form-radio-group v-if="!isViewMode" stacked :options="activationStatusOptions" v-model="activationStatus">
          </b-form-radio-group>
          <span v-else>{{ activationStatusText }}</span>
        </div>
      </detail-row>
      <detail-row :fixed-payload-width="true" :title-position="'start'">
        <span slot="title">
          Show Videos On:
        </span>
        <div v-if="!isViewMode" slot="payload" class="w-100 d-flex flex-row flex-sm-column">
          <b-form-checkbox class="mb-2" v-model="updatedSettings.hasToGenerateVideosForNewVehicles">New Vehicles</b-form-checkbox>
          <b-form-checkbox class="mb-2" v-model="updatedSettings.hasToGenerateVideosForUsedVehicles">Pre-Owned Vehicles</b-form-checkbox>
          <b-form-checkbox v-model="updatedSettings.hasToGenerateVideosForCpoVehicles">Certified Vehicles</b-form-checkbox>
        </div>
        <span v-else slot="payload">{{ getShowVideosOnDesc() }}</span>
      </detail-row>
    </template>
  </editSettingsHelper>
</template>

<script>
import editSettingsHelper from '@/components/_shared/editSettingsHelper'
import editSettingsMixin from './editSettingsMixin'
import detailRow from '@/components/details/helpers/detailRow'
import globals from '@/globals'
import VideoEncoderService from '../../../services/inventory/VideoEncoderService'
import videoEncoderTypes from '@/shared/inventory/videoEncoderTypes'
import { mapGetters } from 'vuex'
import permissions from '@/shared/common/permissions'

export default {
  name: 'inventory-account-autovideo-activation-settings-section',
  props: {
    settings: {
      type: Object,
      required: true
    },
    readOnlyMode: {
      type: Boolean
    }
  },
  data () {
    return {
      updatedSettings: globals().getClonedValue(this.settings),
      accountId: +this.$route.params.accountId,
      isViewMode: true,
      isUpdatingProcessed: false
    }
  },
  mixins: [editSettingsMixin],
  components: {
    editSettingsHelper,
    detailRow
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || { hasPermissions: () => false }
    },
    hasAccessToAutoVideoStatus () {
      return this.user.hasPermissions(permissions.IMFullAccess)
    },
    activationStatusOptions: {
      get: function () {
        return [
          videoEncoderTypes.autoVideoStatusTypes.active,
          videoEncoderTypes.autoVideoStatusTypes.inactive
        ]
      }
    },
    activationStatus: {
      get: function () {
        return this.updatedSettings.activationStatus
      },
      set: function (val) {
        switch (val) {
          case videoEncoderTypes.autoVideoStatusTypes.active.value:
            if (!this.updatedSettings.hasToGenerateVideosForNewVehicles &&
              !this.updatedSettings.hasToGenerateVideosForUsedVehicles &&
              !this.updatedSettings.hasToGenerateVideosForCpoVehicles) {
              this.updatedSettings.hasToGenerateVideosForNewVehicles = true
              this.updatedSettings.hasToGenerateVideosForUsedVehicles = true
              this.updatedSettings.hasToGenerateVideosForCpoVehicles = true
            }
            this.updatedSettings.activationStatus = videoEncoderTypes.autoVideoStatusTypes.active.value
            break
          default:
            this.updatedSettings.hasToGenerateVideosForNewVehicles = false
            this.updatedSettings.hasToGenerateVideosForUsedVehicles = false
            this.updatedSettings.hasToGenerateVideosForCpoVehicles = false
            this.updatedSettings.activationStatus = videoEncoderTypes.autoVideoStatusTypes.inactive.value
        }
      }
    },
    activationStatusText: function () {
      return videoEncoderTypes.autoVideoStatusTypes.getActivationStatusText(this.activationStatus)
    }
  },
  methods: {
    updateSettings () {
      this.isUpdatingProcessed = true
      VideoEncoderService.updateAccountPhotoToVideoActivationSettings(this.accountId, this.updatedSettings).then(res => {
        this.$toaster.success('Updated Activation Settings Successfully')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Failed on Updating Activation Settings', {timeout: 5000})
      }).finally(() => {
        this.isUpdatingProcessed = false
        this.isViewMode = true
        this.$emit('refresh')
      })
    },
    getShowVideosOnDesc () {
      let array = []
      if (this.updatedSettings.hasToGenerateVideosForNewVehicles) {
        array.push('New Vehicles')
      }
      if (this.updatedSettings.hasToGenerateVideosForUsedVehicles) {
        array.push('Pre-Owned Vehicles')
      }
      if (this.updatedSettings.hasToGenerateVideosForCpoVehicles) {
        array.push('Certified Vehicles')
      }

      return array.join(', ') || '-'
    }
  }
}
</script>
