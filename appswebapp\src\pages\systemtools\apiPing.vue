<template>
<b-tabs class="nav-tabs-top" no-fade>
  <b-tab title="ICR API Query">
    <ValidationObserver ref="validator">
      <div class="p-3">
        <b-row>
          <b-col>
            <ValidationProvider name="Account Id" rules="required|numeric|min_value:1" v-slot="{errors}">
              <b-form-input name="accountId" v-model="filter.accountId" placeholder="Account Id"></b-form-input>
              <span class="text-danger">{{errors[0]}}</span>
            </ValidationProvider>
          </b-col>
          <b-col>
            <ValidationProvider name="Vin" rules="required" v-slot="{errors}">
              <b-form-input name="vin" v-model="filter.vin" placeholder="VIN"></b-form-input>
              <span class="text-danger">{{errors[0]}}</span>
            </ValidationProvider>
          </b-col>
          <b-col>
            <l-button :loading="isLoading" variant="primary" @click="findReport">Find</l-button>
          </b-col>
        </b-row>
        <b-card v-if="carfaxReport">
          <h6>Query:</h6>
          <br>
          <pre>"query": {{(carfaxReport.query || {query: ''}).query}}</pre>
          <br>
          <pre>"variables": {{(carfaxReport.query || {}).variables}}</pre>
          <br>
          <h6>Response:</h6>
          <br>
          <pre>{{carfaxReport.response}}</pre>
        </b-card>
      </div>
    </ValidationObserver>
  </b-tab>
  <b-tab title="CL Post">
    <div class="p-3">
      <c-button class="mb-2 d-flex justify-content-end" variant="primary" message="Are you sure you want to post test vehicle to Craigslist?" @confirm="pingPostToCraigslist">Post to CL</c-button>
      <b-alert :show="!!clPingResult.manageUrl" variant="warning">
        Please click <b-link :href="clPingResult.manageUrl" class="text-primary">here</b-link> and delete test post from Craigslist
      </b-alert>
      <h6>Request</h6>
      <b-card class="cl-request-body">
        <pre>{{ clPingResult.request }}</pre>
      </b-card>
      <h6 class="mt-2">Response</h6>
      <b-card class="cl-response-body">
        <pre>{{ clPingResult.response }}</pre>
      </b-card>
    </div>
  </b-tab>
</b-tabs>
</template>

<script>
import InventoryService from '@/services/inventory/InventoryService'

export default {
  name: 'api-ping',
  metaInfo: {
    title: 'API Ping'
  },
  data () {
    return {
      isLoading: false,
      filter: {
        accountId: null,
        vin: ''
      },
      carfaxReport: null,
      message: null,
      isPingClPerform: false,
      clPingResult: {
        request: '',
        response: ''
      }
    }
  },
  methods: {
    findReport () {
      this.$refs.validator.validate().then(isValid => {
        if (isValid) {
          this.message = null
          this.carfaxReport = null
          this.isLoading = true
          InventoryService.getVehicleCarfaxReport(this.filter.accountId, this.filter.vin).then(res => {
            this.carfaxReport = res.data
          }).catch(ex => {
            this.$toaster.exception(ex, 'Failed get ICR.')
          }).finally(() => {
            this.isLoading = false
          })
        }
      })
    },
    pingPostToCraigslist () {
      this.$store.dispatch('craigslist/pingPostVehicleToCraigsList').then(res => {
        if (res.data && res.data.model) {
          this.clPingResult.request = res.data.model.request
          this.clPingResult.response = res.data.model.response
          this.clPingResult.manageUrl = res.data.model.manageUrl
        }
      }).catch(ex => {
        this.$toaster.exception(ex, 'Failed on ping posting to cl')
      })
    }
  }
}
</script>

<style>
.cl-request-body {
  min-height: 300px;
  max-height: 700px;
  overflow: auto;
}
.cl-response-body {
  min-height: 300px;
  max-height: 700px;
  overflow: auto;
}
</style>
