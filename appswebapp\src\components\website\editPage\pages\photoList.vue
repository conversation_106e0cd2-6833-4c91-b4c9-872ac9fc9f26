<template>
  <div>
    <page-status-section :page-edit-model="pageEditModel"/>
    <text-html-content-section v-model="pageEditModel.pageSettings.textHtml"/>
    <photo-list-section @refreshData="onRefreshData" :page-settings="pageEditModel.pageSettings"/>
  </div>
</template>

<script>
import pageStatusSection from '../sections/pageStatusSection'
import textHtmlContentSection from '../sections/textHtmlContentSection'
import photoListSection from '../sections/photoListSection'

export default {
  props: {
    pageEditModel: {type: Object, required: true}
  },
  components: {
    pageStatusSection,
    textHtmlContentSection,
    photoListSection
  },
  methods: {
    onRefreshData () {
      this.$emit('refreshData')
    }
  }
}
</script>
