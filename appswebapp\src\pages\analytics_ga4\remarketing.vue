<template>
  <div>
    <remarketing-summary
      :barItems="bar.items"
      :summary="summary"
      :barTimeFormat="barTimeFormat"
    ></remarketing-summary>

    <remarketing-account-level-table
      :tableItems="table.items"
      :totalItems="table.totalItems"
      :pageNumber="page.filter.pageNumber"
      :pageSize="page.filter.pageSize"
      :sortType="page.filter.sortType"
      @pageNumberChanged="onPageNumberChanged"
      @pageSizeChanged="onPageSizeChanged"
      @sortTypeChanged="onSortTypeChanged"
    ></remarketing-account-level-table>
  </div>
</template>

<style src="@/vendor/styles/pages/analytics.scss" lang="scss"></style>

<script>
import analyticsConstants from './../../shared/analytics/constants'
import analyticsBuilders from './../../shared/analytics/builders'
import analyticsHelper from './helpers.js'
import baseReportPage from './baseReportPage.js'

import RemarketingSummary from '../../components/analytics_ga4/summaries/remarketingSummary'
import RemarketingAccountLevelTable from '../../components/analytics_ga4/tables/remarketingAccountLevelTable'

const filterManager = analyticsBuilders.getFilterManager({
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  pageNumber: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  sortType: { type: Number, default: analyticsConstants.remarketingSortTypes.dateDesc }
})

export default {
  mixins: [baseReportPage],
  name: 'remarketing',
  metaInfo: {
    title: 'Analytics - Remarketing Report'
  },
  components: {
    RemarketingAccountLevelTable,
    RemarketingSummary
  },
  created () {
    this.$store.commit('analyticsGa4/setAnalyticsName', 'Remarketing Report')
    this.$store.commit('analyticsGa4/setBlackThemeOn', true)
  },
  data () {
    return {
      page: {
        filter: filterManager.defaultValue
      },
      summary: {
        sessions: 0,
        sessionsDelta: null,
        impressions: 0,
        impressionsDelta: null,
        clicks: 0,
        clicksDelta: null,
        spend: 0,
        spendDelta: null,
        costPerClick: 0,
        costPerClickDelta: null
      }
    }
  },
  computed: {
    filterManager () {
      return filterManager
    }
  },
  methods: {
    async updateStatistics () {
      try {
        await Promise.all([
          this.updateAccountLevelGraphAndSummary(),
          this.updateAccountLevelDetails()
        ])
      } catch (err) {
        this.$toaster.error('Failed to get data from server', { timeout: 8000 })
        this.$logger.handleError(err, 'Can\'t update statistics', { filter: this.page.filter, cache: this.cache })
      }
    },
    async updateAccountLevelGraphAndSummary () {
      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getRemarketingGraphAndSummary',
        {
          accountId: this.accountId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo
        }
      )

      this.summary = {
        ...this.summary,
        ...store.summary.data
      }
      this.bar.items = store.graph.data.items
    },
    async updateAccountLevelDetails () {
      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getRemarketingDetailsPage',
        {
          accountId: this.accountId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo,
          pageNumber: this.page.filter.pageNumber,
          pageSize: this.page.filter.pageSize,
          sortType: this.page.filter.sortType
        }
      )

      this.table.items = store.detailedData.data.items
      this.table.totalItems = store.detailedData.data.totalItems
    }
  }
}
</script>
