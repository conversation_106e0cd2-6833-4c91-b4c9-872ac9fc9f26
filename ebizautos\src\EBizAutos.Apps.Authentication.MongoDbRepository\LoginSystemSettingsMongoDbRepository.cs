﻿using System;
using System.Threading.Tasks;
using EBizAutos.Apps.Authentication.CommonLib.Abstract.Repositories;
using EBizAutos.Apps.Authentication.CommonLib.Models;
using EBizAutos.CommonLib.RepositoryClasses;
using MongoDB.Driver;

namespace EBizAutos.Apps.Authentication.MongoDbRepository {
	public class LoginSystemSettingsMongoDbRepository : BaseMongoDbRepository, ILoginSystemSettingsRepository {
		private readonly IMongoCollection<LoginSystemSettings> _collection;
		private const string DefaultSettingsId = "default";

		public LoginSystemSettingsMongoDbRepository(string connectionString) : base(connectionString ?? throw new ArgumentNullException(nameof(connectionString))) {
			_collection = GetCollection<LoginSystemSettings>(InternalConstants.CollectionNames.ConstLoginSystemSettingsCollection);
		}

		public async Task<LoginSystemSettings> GetAsync() {
			var settings = await _collection.Find(x => x.Id == DefaultSettingsId).SingleOrDefaultAsync();

			// If no settings exist, create default ones
			if (settings == null) {
				settings = CreateDefaultSettings();
				await _collection.InsertOneAsync(settings);
			}

			return settings;
		}

		public async Task<LoginSystemSettings> UpdateAsync(LoginSystemSettings settings) {
			if (settings == null) {
				throw new ArgumentNullException(nameof(settings));
			}

			if (string.IsNullOrEmpty(settings.Id)) {
				settings.Id = DefaultSettingsId;
			}

			settings.UpdatedAtUtc = DateTime.UtcNow;
			await _collection.ReplaceOneAsync(x => x.Id == settings.Id, settings);
			return settings;
		}

		private static LoginSystemSettings CreateDefaultSettings() {
			return new LoginSystemSettings {
				Id = DefaultSettingsId,
				Mfa = new MfaSettings {
					OtpCodeLength = 6,
					OtpCodeTtlInMinutes = 5,
					OtpCodeVerificationAttemptsLimit = 5,
					OtpCodeResendIntervalInSeconds = 60
				},
				Email = new EmailSettings {
					From = "<EMAIL>",
					OtpSubject = "eBizAutos Login Code",
					OtpBodyTemplate = "Your eBizAutos One-Time Passcode is {0}. It will expire in {1} minutes. DO NOT share it with anyone. We will never ask for the code."
				},
				Sms = new SmsSettings {
					From = "8339873249",
					OtpBodyTemplate = "Your eBizAutos One-Time Passcode is {0}. It will expire in {1} minutes. DO NOT share it with anyone. We will never ask for the code."
				},
				UpdatedAtUtc = DateTime.UtcNow
			};
		}
	}
}