import axios from 'axios'

const actions = {
  getMakes (_, parameters) {
    return axios.get(`/api/inventory/categories/makes`, { params: parameters.filter })
  },
  getModels (_, parameters) {
    return axios.get(`/api/inventory/categories/make/model_names`, { params: parameters.filter })
  },
  getTrims (_, parameters) {
    return axios.get(`/api/inventory/categories/trim_labels`, { params: parameters.filter })
  }
}

export default {
  namespaced: true,
  actions: actions
}
