<template>
  <div>
    <page-status-section :page-edit-model="pageEditModel"/>
    <div class="mt-3">
      <div class="border-bottom">
        <b-row>
          <b-col class="m-0"><h6>Payment Calculator Defaults</h6></b-col>
        </b-row>
      </div>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Interest Rate (%):</span>
        <b-form-input slot="payload" type="number" v-model="pageEditModel.pageSettings.defaultInterestRate"></b-form-input>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Loan Term (Mos.):</span>
        <b-form-input slot="payload" type="number" v-model="pageEditModel.pageSettings.defaultLoanTerm"></b-form-input>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Sales Tax (%):</span>
        <b-form-input slot="payload" type="number" v-model="pageEditModel.pageSettings.defaultSalesTax"></b-form-input>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Down Payment:</span>
        <b-form-input slot="payload" type="number" v-model="pageEditModel.pageSettings.defaultDownPayment"></b-form-input>
      </detail-row>
    </div>
  </div>
</template>

<script>
import pageStatusSection from '../sections/pageStatusSection'
import textHtmlContentSection from '../sections/textHtmlContentSection'
import detailRow from '@/components/details/helpers/detailRow'

export default {
  props: {
    pageEditModel: {type: Object, required: true}
  },
  components: {
    pageStatusSection,
    textHtmlContentSection,
    detailRow
  }
}
</script>
