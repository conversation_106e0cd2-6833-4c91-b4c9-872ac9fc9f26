<template>
  <details-section
    ref="controlSection"
    :title="`Videos (${videoCount})`"
    v-model="mode"
    class="video_section"
    sync-save
    @save="onSave"
  >
    <div v-if="mode === 'view'">

      <b-row>
        <b-col sm="6" v-for="(i, key) in vehicleVideo" :key="key" class="pt-3">
          <opacity-component :opacity="!isVideoReady(i)" slot="header">
            <lazy-video :video="buildVideoUrl(i.videoUrl)" :image="i.presentationPhoto.photo640" :image-preload="i.presentationPhoto.photo107" :inactive="!isVideoReady(i)" />
          </opacity-component>
        </b-col>
      </b-row>

      <b-row class="mt-3" v-if="hasToShowToggler">
        <b-col class="view-toggle">
          <b-button class="d-sm-none w-100 view-toggle__mobile-toggler" variant="primary" @click="viewMore = !viewMore">{{toggleText}}</b-button>
          <b-button class="view-toggle__toggler d-none d-sm-block" variant="link" @click="viewMore = !viewMore">{{toggleText}}</b-button>
        </b-col>
      </b-row>

    </div>
    <div v-else-if="mode === 'edit'">

      <b-row>
        <b-col v-if="!hasCustomVideo" xl="3" lg="4" md="6" class="pt-3">
          <dropzone-component
            message="Drop Custom Video here"
            ref="fileUpload"
            :upload-path="videoUploadPath"
            :maxFiles="1"
            :maxFileSizeInBytes="maxVideoFileSizeInBytes"
            acceptedFiles="video/*"
            :previewImage="vehicle.photos.presentationPhoto.photo400"
            @uploadComplete="onUploadComplete"
            @addedFile="onFileAdded"
          />
        </b-col>

        <b-col xl="3" lg="4" md="6" v-for="(i, k) in vehicleVideos" :key="k" class="pt-3">
          <card-component>

            <opacity-component :opacity="!isVideoReady(i)" slot="header">
              <div class="image">
                <div class="image__holder">
                  <lazy-image :src="i.presentationPhoto.photo640" :preload-src="i.presentationPhoto.photo107" />
                </div>
              </div>
            </opacity-component>

            <div slot="body">{{getVideoDesc(i)}}</div>

            <div slot="footer" class="py-1 video-footer">
              <div>
                <b-button v-if="isActualVideo(i)" class="p-0 m-0 video-footer__delete-btn" @click="deleteVideo(i)" variant="link">{{getVideoItemDeleteDescription(i)}}</b-button>
              </div>
              <template v-if="isVideoReady(i)">
                <b-form-checkbox v-model="i.hasToDisplay">
                  Display
                </b-form-checkbox>
              </template>
              <template v-else>
                <div>
                  <span>Status:</span>
                  <span class="font-italic">{{getVideoStatusDesc(i)}}</span>
                </div>
              </template>
            </div>

          </card-component>
        </b-col>

      </b-row>

    </div>
  </details-section>
</template>

<script>
import {mapGetters} from 'vuex'
import lazyVideo from '../../../_shared/lazyVideo'
import cardComponent from '../../helpers/cardComponent'
import lazyImage from '../../../_shared/lazyImage'
import opacityComponent from '../../../_shared/opacityComponent'
import videoTypes from '../../../../shared/common/vehicle/videoTypes'
import videoStatuses from '../../../../shared/common/vehicle/videoStatuses'
import dropzoneComponent from '../../helpers/dropzoneWrapperComponent'
import detailsSection from '../../detailsSection'
import inventoryService from '../../../../services/inventory/InventoryService'
import vehicleConstants from '../../../../shared/details/vehicleConstants'

export default {
  name: 'video-section',
  props: {
    imgPreload: String,
    imgCation: String,
    video: String
  },
  data () {
    return {
      mode: 'view',
      viewMore: false,
      onUploadCompleteAction: null,
      files: []
    }
  },
  computed: {
    ...mapGetters('details', ['vehicle']),
    videoUploadPath () {
      return inventoryService.getVehicleVideoUploadPath(this.vehicle.accountId, this.vehicle.vin)
    },
    videoCount () {
      return this.vehicle.videos.videoItemsCount || 0
    },
    vehicleVideo () {
      return this.viewMore
        ? this.vehicle.videos.videoItems
        : this.vehicle.videos.videoItems.slice(0, 2)
    },
    hasToShowToggler () {
      return this.vehicle.videos.videoItemsCount > 2
    },
    toggleText () {
      return this.viewMore
        ? 'View Less'
        : 'View More'
    },
    vehicleVideos () {
      return this.vehicle.videos.videoItems
    },
    hasCustomVideo () {
      return this.videoCount > 0 && this.vehicleVideos.some(x => x.videoType === videoTypes.actual)
    },
    maxVideoFileSizeInBytes () {
      return vehicleConstants.maxVideoSizeInBytes
    }
  },
  methods: {
    isActualVideo (videoItem) {
      return videoItem.videoType === videoTypes.actual
    },
    async onSave (params) {
      if (this.$refs.fileUpload) {
        this.$refs.controlSection.disableSaveButton()
        this.$refs.fileUpload.sendFiles()

        // done action for synchronously save vehicle after video uploading
        this.onUploadCompleteAction = params.done
      } else {
        await params.done()
      }
    },
    async onUploadComplete (isSuccess) {
      if (isSuccess) {
        this.$nextTick(async () => {
          await this.onUploadCompleteAction()
        })
      }
    },
    getVideoDesc (video) {
      switch (video.videoType) {
        case videoTypes.unknown:
          return 'Unknown Video'
        case videoTypes.actual:
          return 'Custom Video'
        case videoTypes.flash:
          return 'Auto Video'
        case videoTypes.unityWorksMedia:
          return 'Unity Works Media Video'
        case videoTypes.autoTrader:
          return 'Auto Trader Video'
        case videoTypes.sister:
          return 'Sister Video'
        case videoTypes.iDoStream:
          return 'Stream Video'
        case videoTypes.flickFusion:
          return 'Flick Fusion Video'
        case videoTypes.carsComVideo:
          return 'Cars Com Video'
        case videoTypes.dealerSpecialties:
          return 'Dealer Specialties Video'
      }
    },
    isVideoReady (video) {
      return video.videoStatus === videoStatuses.active
    },
    deleteVideo (videoItem) {
      if (videoItem.hasOwnProperty('hasToDelete')) {
        videoItem.hasToDelete = !videoItem.hasToDelete
      } else {
        this.$set(videoItem, 'hasToDelete', true)
      }
    },
    getVideoItemDeleteDescription (videoItem) {
      return videoItem.hasToDelete ? 'Undelete Video' : 'Delete Video'
    },
    getVideoStatusDesc (video) {
      switch (video.videoStatus) {
        case videoStatuses.inactive:
          return 'Inactive'
        case videoStatuses.active:
          return 'Active'
        case videoStatuses.inQueue:
          return 'In Queue...'
        case videoStatuses.inQueueHigh:
          return 'In Queue High...'
        case videoStatuses.inQueueCritical:
          return 'In Queue Critical...'
        case videoStatuses.encodeInProgress:
          return 'Encoding...'
      }
    },
    onFileAdded () {
      this.vehicle.isModified = true
    },
    buildVideoUrl (videoUrl) {
      if (!videoUrl) {
        return videoUrl
      }
      let timeStampParam = `timestamp=${new Date().getTime()}`
      if (videoUrl.includes('?')) {
        return videoUrl + '&' + timeStampParam
      }
      return videoUrl + '?' + timeStampParam
    }
  },
  components: {
    'details-section': detailsSection,
    'lazy-video': lazyVideo,
    'card-component': cardComponent,
    'lazy-image': lazyImage,
    'opacity-component': opacityComponent,
    'dropzone-component': dropzoneComponent
  }
}
</script>

<style lang="scss" scoped>
  @import '../../../../vendor/libs/plyr/plyr.scss';

 .video_section .view-toggle {
    justify-content: center;
  }

  .video_section .view-toggle__toggler {
    text-transform: uppercase;
    color: #C90F17;
    margin: 0 auto;
  }

  .video_section .view-toggle__toggler:hover,
  .video_section .view-toggle__toggler:focus,
  .video_section .view-toggle__toggler:active {
    color: #af0d14;
    text-decoration: underline;
  }

  .video_section .image {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%;
  }

  .video_section .image__holder {
    position: absolute;
    height: 100%;
    width: 100%;
    overflow: hidden;
  }

  .video-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .video-footer__delete-btn {
    font-size: 0.8rem;
    color: #C90F17;
  }

  .video-footer__delete-btn:hover {
    color: #af0d14;
  }
</style>
