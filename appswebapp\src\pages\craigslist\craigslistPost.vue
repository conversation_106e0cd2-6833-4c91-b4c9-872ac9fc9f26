<template>
  <div v-if="isReady">
    <b-row class="mb-2">
      <b-col v-if='filter.postingType !== 3'><h2>Post to Craigslist</h2></b-col>
      <b-col v-else><h2>Edit Post to Craigslist</h2></b-col>
      <b-col cols="8">
        <b-btn class="float-right ml-2" variant="secondary" @click="$router.go(-1)">Close</b-btn>
        <b-btn class="float-right" @click="submitPostData" :variant="submitButton.variant" :disabled="submitButton.disabled">Submit</b-btn>
      </b-col>
    </b-row>
    <ValidationObserver ref="validator">
      <vehicle-post-form :typePost='filter.postingType' />
    </ValidationObserver>
    <div class="table-responce mt-4" v-if='filter.postingType !== 3'>
      <b-row>
        <b-col><h2>Post History - {{craigslistPostHistory ? craigslistPostHistory.length : 0 }} post(s)</h2></b-col>
        <b-col><b-btn v-if="craigslistPostHistory && craigslistPostHistory.length > 0" class="float-right" @click="showHistory" size='sm'>{{isShowPostHistory ? 'hide' : 'show'}}</b-btn></b-col>
      </b-row>
      <b-collapse id="show-post-history" v-model="isShowPostHistory">
        <b-table
          :items="craigslistPostHistory"
          :fields="tableFields"
          :striped="true"
          :bordered="false"
          :outlined="true"
          thead-class="bg-white"
          responsive
        >
        </b-table>
      </b-collapse>
    </div>
    <b-modal :visible='isShowModal' size='md' @hide='hide' :hide-header='true' :hide-footer='true'>
      <b-row class='d-flex justify-content-center'>
        <h1 class='text-success'>Success!</h1>
      </b-row>
      <b-row class='d-flex justify-content-center'>
        <h4>Your request will be processed!</h4>
      </b-row>
      <b-row class='d-flex justify-content-center'>
      <b-button variant="danger" class='mr-1 custom-btn text-center' :to="{ path: getPostsPath() }">View your posts</b-button>
      <b-button @click="hide" class='ml-1 custom-btn text-center'>Close</b-button>
      </b-row>
    </b-modal>
  </div>

</template>

<script>
import vehiclePostForm from './../../components/craigslist/post/vehiclePostForm'
import { mapGetters } from 'vuex'
import moment from 'moment'
import numeral from 'numeral'
import CraigslistDescriptionHelper from '@/shared/craigslist/craigslistDescriptionHelper'
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'

const defaultValues = new ObjectSchema({
  postingType: { type: Number, default: 1 }
})
const queryHelper = new QueryStringHelper(defaultValues)

const craigslistDescriptionHelper = new CraigslistDescriptionHelper()

export default {
  name: 'post-to-craigslist',
  props: {
    accountId: {
      type: Number,
      required: true
    },
    vin: {
      type: String,
      required: true
    }
  },
  metaInfo: {
    title: 'Post To Craigslist'
  },
  components: {
    vehiclePostForm
  },
  data () {
    return {
      isShowModal: false,
      isReady: false,
      isShowPostHistory: false,
      filter: defaultValues.getObject(),
      submitButton: {
        variant: 'primary',
        disabled: false
      }
    }
  },
  computed: {
    ...mapGetters('craigslistPost', ['craigslistPostHistory', 'craigslistPostData', 'vehicle']),
    tableFields () {
      return [
        {
          key: 'dateTimePosted',
          label: 'Post Date/Time',
          sortable: true,
          formatter: value => {
            return moment(value).format('MM/DD/YYYY hh:mm:ss A')
          }
        },
        {
          key: 'craigsListVehiclePrice',
          label: 'Post Price',
          sortable: true,
          formatter: value => numeral(value).format('$0,0')
        },
        {
          key: 'craigsListPostAreaDescription',
          label: 'Area',
          sortable: true,
          formatter: value => value.split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')
        },
        {
          key: 'postingSource',
          label: 'Source',
          sortable: true,
          formatter: value => {
            return this.getSourceLabel(value)
          }
        },
        {
          key: 'craigsListTitle',
          label: 'Post Title',
          sortable: true
        }
      ]
    }
  },
  created () {
    this.filter = queryHelper.parseQueryStringToObject(this.$router)
    this.populateInitialData().then(() => { this.isReady = true })
  },
  methods: {
    async populateInitialData () {
      if (this.filter.postingType !== 3) {
        await this.$store.dispatch('craigslistPost/populateCraigslistPostData', { accountId: this.accountId, vin: this.vin })
      } else {
        await this.$store.dispatch('craigslistPost/populateCraigslistPostDataByJobId', { accountId: this.accountId, id: this.vin })
      }

      await this.$store.dispatch('craigslistPost/populateCraigslistAreas', { accountId: this.accountId })
      await this.$store.commit('craigslistPost/initializeCraigslistPostData', this.$router)
      if (this.filter.postingType === 3) {
        await this.$store.commit('craigslistPost/initializeCraigslistPostDataDelayedJob')
      }
    },
    async submitPostData () {
      let validResult = await this.$refs.validator.validate()
      if (!validResult) {
        return
      }
      this.submitButton.disabled = true
      this.submitButton.variant = 'secondary'
      const postData = {
        accountId: this.accountId,
        iId: this.vehicle.iId,
        price: this.craigslistPostData.price,
        title: this.craigslistPostData.postingTitle,
        specificLocation: this.craigslistPostData.specificLocation,
        area: this.craigslistPostData.areaCode,
        subArea: this.craigslistPostData.subAreaCode,
        postTypeId: this.craigslistPostData.postingType,
        postingDate: this.craigslistPostData.postingDate,
        postingTime: this.craigslistPostData.postingTime,
        postingCategory: this.craigslistPostData.categoryType,
        vin: this.craigslistPostData.vin
      }

      if (this.filter.postingType !== 3) {
        await this.$store.dispatch('craigslistPost/postVehicleToCraigslist', { data: postData })
          .then(() => {
            this.isShowModal = true
          })
          .catch(ex => {
            this.$toaster.exception(ex, 'Failed posting vehicle to craigslist')
            if (ex.response.status !== 400) {
              this.$logger.handleError(ex, `Failed posting vehicle iid:${postData.iId}`)
            }
          }).finally(() => {
            this.submitButton.disabled = false
            this.submitButton.variant = 'primary'
          })
      } else {
        await this.$store.dispatch('craigslistPost/postUpdateDelayedJob',
          { accountId: this.accountId, id: this.vin, data: postData })
          .then(() => {
            this.isShowModal = true
          })
          .catch(ex => {
            this.$toaster.exception(ex, 'Failed posting vehicle to craigslist')
            if (ex.response.status !== 400) {
              this.$logger.handleError(ex, `Failed posting vehicle iid:${postData.iId}`)
            }
          }).finally(() => {
            this.submitButton.disabled = false
            this.submitButton.variant = 'primary'
          })
      }
    },
    showHistory () {
      this.isShowPostHistory = !this.isShowPostHistory
    },
    getSourceLabel (val) {
      return craigslistDescriptionHelper.getCraigslistSourceDescription(val)
    },
    hide () {
      this.isShowModal = false
      this.$router.go(-1)
    },
    getPostsPath () {
      return `/craigslist/${this.accountId}/posts`
    }
  }
}
</script>

<style scoped lang='scss'>
.page-header{
  display: flex;
  justify-content: space-between;
}
.custom-btn {
  width: 45%;
}
.info-row {
  padding: 10px 0;
}

.detail-row {
  display: flex;
  padding: 0.714rem 0;
  box-sizing: border-box;

  .detail-row__title {
    align-self: center;
  }

  .detail-row__title__custom__size {
    flex: none;
    width: 13.37rem;
  }

  .detail-row__fixed-width {
    flex: none;
    width: 20.25rem;
  }

  .detail-row__big-fixed-width {
    flex: none;
    width: 26.3rem;
  }

  &.edit-mode{
    padding: 0.3rem 0;
    min-height: 2.37rem;
  }

  .detail-row__payload {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
  }

  @media (max-width:576px) {
    &.mobile-wrap {
      .detail-row__title {
        flex-basis: 100%;
      }
    }
  }

  @media (max-width:576px) {
    .detail-row__title {
      flex-basis: 49%;
      flex-grow: 0;
    }
    .detail-row__payload {
      flex-grow: 1;
    }
    &.edit-mode {
      .detail-row__title {
        padding-bottom: 0.5rem;
      }
    }
  }
}
</style>
