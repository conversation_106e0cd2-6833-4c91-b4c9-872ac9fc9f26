# SMS MFA Implementation Changes

## Огляд
Додано підтримку SMS як методу багатофакторної автентифікації (MFA) у webapp. Shared API вже мав підтримку SMS MFA, тому зміни стосувалися тільки frontend частини.

## Внесені зміни

### 1. `src/pages/authentication/mfa-method.vue`
- **Додано функцію `getMethodDisplayName()`** для правильного відображення назв методів:
  - `email` → "Email Address"
  - `sms` → "SMS Text Message"
  - інші → капіталізація першої літери
- **Оновлено template** для використання нової функції замість жорстко закодованої логіки

### 2. `src/pages/authentication/mfa-verify.vue`
- **Замінено `maskedEmail` на `maskedDestination`** у template для універсального відображення
- **Додано computed property `maskedDestination`** для отримання замаскованої адреси поточного методу
- **Оновлено метод `resend()`** для використання правильного методу при повторній відправці

### 3. `src/store/modules/mfa.js`
- **Додано `selectedMethod`** до state для відстеження обраного методу
- **Додано mutation `setSelectedMethod`** для збереження обраного методу
- **Додано getters:**
  - `maskedDestination` - повертає замасковану адресу для обраного методу
  - `selectedMethodType` - повертає тип обраного методу
- **Оновлено action `send()`** для збереження обраного методу
- **Оновлено mutation `clear()`** для очищення selectedMethod

### 4. `src/store/modules/authentication.js`
- **Додано очищення MFA стану** при logout через `commit('mfa/clear')`

## Як це працює

### Потік автентифікації з SMS MFA:
1. Користувач вводить логін/пароль
2. Якщо MFA увімкнено, API повертає доступні методи (email та/або SMS)
3. Користувач перенаправляється на `/mfa` і бачить список доступних методів
4. Користувач обирає SMS і бачить "SMS Text Message: ****1234"
5. При натисканні "Send" відправляється запит з `Method: 2` (SMS enum value)
6. Користувач перенаправляється на `/mfa/verify`
7. Сторінка верифікації показує "A one-time passcode was just sent to ****1234"
8. При натисканні "Resend" використовується той самий метод (SMS)

### API Integration:
- **Shared API вже підтримує SMS MFA** через:
  - `MfaMethodEnum.Sms = 2`
  - `MfaChallengeManager.MaskPhone()` для маскування номерів
  - `EBizTwilioSmsClient` для відправки SMS
  - Налаштування в `LoginSystemSettings.Sms`

### Структура даних:
```javascript
// Відповідь від API при login з MFA
{
  "isMfaRequired": true,
  "challengeId": "abc123",
  "availableMethods": [
    {
      "type": 1,  // Email
      "name": "email",
      "maskedDestination": "j***<EMAIL>"
    },
    {
      "type": 2,  // SMS
      "name": "sms",
      "maskedDestination": "****1234"
    }
  ]
}
```

## Тестування
Створено `test-mfa-sms.html` для перевірки функціональності.

## Сумісність
Зміни повністю зворотно сумісні - існуючий email MFA продовжує працювати без змін.
