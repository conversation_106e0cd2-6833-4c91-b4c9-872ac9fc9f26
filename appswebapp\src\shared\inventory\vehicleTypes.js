const warranties = Object.freeze({
  doNotDisplay: { value: 0, text: 'Do Not Display' },
  noWarranty: { value: 1, text: 'No-Warranty' },
  asIs: { value: 2, text: 'As-Is' },
  limited: { value: 3, text: 'Limited' },
  factory: { value: 4, text: 'Factory' },
  fullFactory: { value: 5, text: 'Full-Factory' },
  extended: { value: 6, text: 'Extended' },
  serviceContract: { value: 7, text: 'Service-Contract' }
})

const engineFuels = Object.freeze({
  notDefined: { value: 0, text: 'Not Specified' },
  gasoline: { value: 1, text: 'Gasoline' },
  diesel: { value: 2, text: 'Diesel' },
  flexFuel: { value: 3, text: 'Flex Fuel' },
  hybrid: { value: 4, text: 'Hybrid' },
  naturalGas: { value: 5, text: 'Natural Gas' },
  propaneGas: { value: 6, text: 'Propane Gas' },
  ethanol: { value: 7, text: 'Ethanol' },
  electric: { value: 8, text: 'Electric' },
  methanol: { value: 9, text: 'Methanol' }
})

export {
  warranties,
  engineFuels
}
