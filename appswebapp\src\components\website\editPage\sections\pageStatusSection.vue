<template>
  <div>
    <div class="border-bottom">
      <b-row>
        <b-col xs="12" sm="6" md="6" lg="6" xl="6" class="m-0"><h6 class="float-left">Page Status</h6></b-col>
      </b-row>
    </div>
    <detail-row
      :fixed-payload-width="true"
      v-if="pageTitleFieldVisible"
    >
      <span slot="title">Page Title:</span>
      <b-form-input v-model="pageEditModel.pageSettings.pageTitle" slot="payload"></b-form-input>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Nav Bar Label:</span>
      <b-form-input v-model="pageEditModel.navigationSettings.name" slot="payload"></b-form-input>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Page Type:</span>
      <span slot="payload">{{getNavigationTypeLabel}}</span>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Page Status:</span>
      <label slot="payload" class="switcher">
          <input type="checkbox" v-model="pageEditModel.navigationSettings.isVisible" class="switcher-input">
          <span class="switcher-indicator">
            <span class="switcher-yes"></span>
            <span class="switcher-no"></span>
          </span>
          <span class="switcher-label">Visible</span>
      </label>
    </detail-row>
    <detail-row title-position="start" :fixed-payload-width="true" v-if="pageEditModel.navigationSettings.siteNavigationType !== pageNavigationTypes.customLink.value">
      <span slot="title">Page URL:</span>
      <div
        slot="payload"
        v-if="pageEditModel.navigationSettings.siteNavigationType === pageNavigationTypes.blog.value"
      >
        <b-link
          :href="pageEditModel.navigationSettings.pageUrl + '/'"
        >
          /blog/
        </b-link>
        <span class="text-nowrap">(the URL for this page cannot be changed)</span>
      </div>
      <div
        slot="payload"
        v-else-if="pageEditModel.navigationSettings.siteNavigationType === pageNavigationTypes.newVehicle.value"
      >
        <b-link
          :href="pageEditModel.navigationSettings.pageUrl + '/'"
        >
          /research/
        </b-link>
        <span class="text-nowrap">(the URL for this page cannot be changed)</span>
      </div>
      <b-form-input v-else v-model="pageEditModel.pageSettings.pageName" @keydown.native="handleKeyDown" slot="payload"></b-form-input>
    </detail-row>
  </div>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import { pageNavigationTypes } from '@/shared/website/constants'

export default {
  props: {
    pageEditModel: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      pageNavigationTypes
    }
  },
  computed: {
    getNavigationTypeLabel () {
      let res = Object.values(pageNavigationTypes).find(x => x.value === this.pageEditModel.navigationSettings.siteNavigationType)
      return res ? res.label : ''
    },
    pageTitleFieldVisible () {
      return this.pageEditModel.navigationSettings.siteNavigationType !== pageNavigationTypes.newVehicle.value &&
              this.pageEditModel.navigationSettings.siteNavigationType !== pageNavigationTypes.customLink.value
    }
  },
  components: {
    'detail-row': detailRow
  },
  methods: {
    handleKeyDown (e) {
      if (e.key && e.key === '&') {
        e.preventDefault()
      }
    }
  }
}
</script>
