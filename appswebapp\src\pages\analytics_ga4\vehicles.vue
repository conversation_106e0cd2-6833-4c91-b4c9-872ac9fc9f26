<template>
  <div>
    <vehicles-summary
      :barItems="bar.items"
      :summary="summary"
      :barTimeFormat="barTimeFormat"
    ></vehicles-summary>

    <vehicles-by-vehicle-table
      :tableItems="table.items"
      :totalItems="table.totalItems"
      :pageNumber="page.filter.pageNumber"
      :pageSize="page.filter.pageSize"
      :sortType="page.filter.sortType"
      :showAbsoluteUrl="true"
      @pageNumberChanged="onPageNumberChanged"
      @pageSizeChanged="onPageSizeChanged"
      @sortTypeChanged="onSortTypeChanged"
    ></vehicles-by-vehicle-table>
  </div>
</template>

<style src="@/vendor/styles/pages/analytics.scss" lang="scss"></style>

<script>
import analyticsConstants from './../../shared/analytics/constants'
import analyticsBuilders from './../../shared/analytics/builders'
import analyticsHelper from './helpers.js'
import baseReportPage from './baseReportPage.js'

import VehiclesSummary from '../../components/analytics_ga4/summaries/vehiclesSummary'
import VehiclesByVehicleTable from '../../components/analytics_ga4/tables/vehiclesByVehicleTable'

const filterManager = analyticsBuilders.getFilterManager({
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  pageNumber: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  sortType: { type: Number, default: analyticsConstants.vehicleSortType.pageViewsDesc }
})

export default {
  mixins: [baseReportPage],
  name: 'vehicles',
  props: {
    accountId: {
      type: Number,
      required: true
    }
  },
  metaInfo: {
    title: 'Analytics - Vehicles'
  },
  components: {
    VehiclesByVehicleTable,
    VehiclesSummary
  },
  created () {
    this.$store.commit('analyticsGa4/setAnalyticsName', 'Vehicles')
    this.$store.commit('analyticsGa4/setBlackThemeOn', true)
  },
  data () {
    return {
      page: {
        filter: filterManager.defaultValue
      },
      summary: {
        pageViews: 0,
        pageViewsDelta: null,
        formLeads: 0,
        formLeadsDelta: null
      }
    }
  },
  computed: {
    filterManager () {
      return filterManager
    }
  },
  methods: {
    async updateStatistics () {
      try {
        await Promise.all([
          this.updateAccountLevelGraphAndSummary(),
          this.updateAccountTableByVehicles()
        ])
      } catch (err) {
        this.$toaster.error('Failed to get data from server', { timeout: 8000 })
        this.$logger.handleError(err, 'Can\'t update statistics', { filter: this.page.filter, cache: this.cache })
      }
    },
    async updateAccountLevelGraphAndSummary () {
      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getVehiclesGraphAndSummary',
        {
          accountId: this.accountId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo
        }
      )

      this.summary = {
        ...this.summary,
        ...store.summary.data
      }
      this.bar.items = store.graph.data
    },
    async updateAccountTableByVehicles () {
      const siteSettings = await this.$store.dispatch('siteSettings/getSiteSettings', this.accountId)
      const pageData = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getVehiclesDetailsPage',
        {
          accountId: this.accountId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo,
          pageNumber: this.page.filter.pageNumber,
          pageSize: this.page.filter.pageSize,
          sortType: this.page.filter.sortType
        }
      )
      const siteDefaultUrl = siteSettings && siteSettings.urlWithProtocol
        ? siteSettings.urlWithProtocol
        : ''

      this.table.items = pageData.detailedData.data.items.map(x => {
        x.vehicleLabel = `${x.year} ${x.make} ${x.model} - ${x.vin}`
        x.absoluteUrl = siteDefaultUrl + x.pagePath

        return x
      })
      this.table.totalItems = pageData.detailedData.data.totalItems
    }
  }
}
</script>
