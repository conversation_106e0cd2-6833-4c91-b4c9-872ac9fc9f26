import Vue from 'vue'
import Router from 'vue-router'
import Meta from 'vue-meta'

import globals from '@/globals'
import inventory from './inventory'
import craigslist from './craigslist'
import eBay from './eBay'
import leads from './leads'
import loginRouter from './login'
import notificationsRoutes from './notifications'
import crmRoutes from './crm'
import marketingRoutes from './marketing'
import websiteRoutes from './website'
import analyticsGa4Routes from './analytics_ga4'
import notFoundRoutes from './notfound'
import errorRoutes from './error'
import accountRoutes from './account'
import systemTools from './systemtools'
import siteBoxManager from './siteBoxManager'
import users from './users'
import store from '../store'
import applicationTypes from '../shared/common/applicationTypes'

Vue.use(Router)
Vue.use(Meta)

const ROUTES = [
  // Default route
  {
    path: '',
    redirect (q, w, e) {
      return '/accounts'
    }
  }
]
  .concat(loginRouter.routes)
  .concat(notificationsRoutes)
  .concat(crmRoutes)
  .concat(marketingRoutes)
  .concat(websiteRoutes)
  .concat(analyticsGa4Routes.routes)
  .concat(accountRoutes)
  .concat(inventory)
  .concat(craigslist)
  .concat(eBay)
  .concat(leads)
  .concat(users)
  .concat(systemTools)
  .concat(notFoundRoutes)
  .concat(errorRoutes)
  .concat(siteBoxManager)

const router = new Router({
  base: '/',
  mode: 'history',
  routes: ROUTES
})

loginRouter.bindAuthorizationModules(router)
analyticsGa4Routes.bindExtensionMethods(router)

router.afterEach((to) => {
  if (to.meta.hasOwnProperty('applicationType')) {
    store.commit('users/setLastVisitedApp', +to.meta.applicationType)
  }

  // On small screens collapse sidenav
  if (window.layoutHelpers && window.layoutHelpers.isSmallScreen() && !window.layoutHelpers.isCollapsed()) {
    setTimeout(() => window.layoutHelpers.setCollapsed(true, true), 10)
  }

  // Scroll to top of the page
  globals().scrollTop(0, 0)
})

router.beforeEach(async (to, from, next) => {
  if (to.meta.allowAnonymous) {
    next()
    return
  }
  let infoForUser = store.state.users.informationForUser

  if (!infoForUser) {
    try {
      await store.dispatch('users/getInformationForUser')
      infoForUser = store.state.users.informationForUser
    } catch (ex) {
      router.app.$logger.handleError(ex, `Can't  get user info while going to ${to.fullPath}`)
      next({ path: '/error', query: { back: to.fullPath, hash: (new Date()).getTime() } })
      return
    }
  }

  let isEmptyDynamicAnnouncements = !store.state.userAnnouncements.dynamicAnnouncements || store.state.userAnnouncements.dynamicAnnouncements.length === 0
  if (infoForUser && isEmptyDynamicAnnouncements && !router.app.$announcement.isTourRunning) {
    try {
      await store.dispatch('userAnnouncements/populateUserAnnouncements')
    } catch (ex) {
      router.app.$logger.handleError(ex, `Can't  get user announcements`)
    }
  }

  if (infoForUser && !isEmptyDynamicAnnouncements && !router.app.$announcement.isTourRunning) {
    try {
      router.app.$announcement.tryToStartNewShepherdTour()
    } catch (ex) {
      router.app.$logger.handleError(ex, `Failed on starting announcement`)
    }
  }

  if (to.params && to.params.accountId) {
    const result = await store.dispatch('accountManagement/isAccountExists', { accountId: to.params.accountId })
    if (!result.data) {
      if (infoForUser.user.accountId === to.params.accountId) {
        logOutUser()
      } else {
        setTimeout(() => next(getRedirectionForUser(to, infoForUser.user)), 10)
      }
      return
    }
  }

  let accountId = to.params.accountId || infoForUser.user.accountId
  await store.dispatch('userInventoryGeneralData/initGeneralData', accountId)
  if (to.name === 'inventory-email-ad-generator' && !store.getters['userInventoryGeneralData/isInventoryEmailAdEnabled']) {
    setTimeout(() => next(getRedirectionForUser(to, infoForUser.user)), 10)
    return
  }

  if (to.meta.applicationFullAccess && infoForUser.user.hasPermissions(to.meta.applicationFullAccess)) {
    next()
    return
  }

  if (to.meta.accountGroupAccess && infoForUser.user.canManageAccount((to.params || {}).accountId)) {
    next()
    return
  }

  if (to.meta.manageMultipleAccounts) {
    if (!infoForUser.user.canManageMultipleAccount) {
      setTimeout(() => next(getRedirectionForUser(to, infoForUser.user)), 10)
      return
    }
    if (to.meta.applicationType && !infoForUser.user.canManageApplicationType(to.meta.applicationType, to.meta.applicationFullAccess)) {
      setTimeout(() => next(getRedirectionForUser(to, infoForUser.user)), 10)
      return
    }
  }

  if (to.meta.exactAccountMatchWithUser && !infoForUser.user.hasPermissions(to.meta.applicationFullAccess) &&
      +infoForUser.user.accountId !== +(to.params || {}).accountId) {
    setTimeout(() => next(getRedirectionForUser(to, infoForUser.user)), 10)
    return
  }

  if (to.meta.analyticsReportGroupsAccess &&
      (!infoForUser.user.hasReportGroupsAccess || !infoForUser.user.hasAccessToReportGroup((to.params || {}).reportGroupId))) {
    setTimeout(() => next(getRedirectionForUser(to, infoForUser.user)), 10)
    return
  }

  if (!(to.meta || {}).permissions) {
    if (hasAccessToAID(((to.params || {}).accountId), infoForUser.user, to.meta)) {
      next()
      return
    }
  } else {
    if (infoForUser.user.hasPermissions(to.meta.permissions) &&
        hasAccessToAID(((to.params || {}).accountId), infoForUser.user, to.meta)) {
      next()
      return
    }
  }

  let redirectionPath = getRedirectionForUser(to, infoForUser.user)

  next(redirectionPath)
})

function getRedirectionForUser (to, user) {
  let appType = (to.meta || {}).applicationType || store.state.users.lastVisitedApp
  if (user.accountId && appType) {
    if (user.canManageMultipleAccount) {
      return { name: 'account-listing' }
    }
    switch (appType) {
      case applicationTypes.AppsAnalytics.Id:
        if (user.hasReportGroupsAccess && user.defaultAnalyticsViewId) {
          return { name: 'ga4GroupAnalyticsDashboard', params: { reportGroupId: user.defaultAnalyticsViewId } }
        }
        return { name: 'analyticsGa4Dashboard', params: { accountId: user.accountId } }
      case applicationTypes.AppsCraigslist.Id:
        return { name: 'craigslist-dashboard-for-account', params: { accountId: user.accountId } }
      case applicationTypes.AppsLeads.Id:
        return { name: 'leads-dashboard', params: { accountId: user.accountId } }
      default:
        return { name: 'inventory-description', params: { accountId: user.accountId } }
    }
  } else if (appType) {
    switch (appType) {
      case applicationTypes.AppsAnalytics.Id:
        return { name: 'analytics-ga4-accounts' }
      case applicationTypes.AppsCraigslist.Id:
        return { name: 'craigslist-dashboard-all-accounts' }
      case applicationTypes.AppsLeads.Id:
        return { name: 'leads-accounts' }
      default:
        return { name: 'inventory-accounts' }
    }
  }

  return { name: 'notfound' }
}

function hasAccessToAID (requestedAccountId, user, meta) {
  if (requestedAccountId) {
    if (user.accountId) {
      if (+requestedAccountId === +user.accountId) {
        return true
      }

      if (!user.groupPermissionsByAccountId || !user.groupPermissionsByAccountId[requestedAccountId]) {
        return false
      }

      let userAppPermission = user.groupPermissionsByAccountId[requestedAccountId]

      if (meta.applicationType && userAppPermission.every(x => x.application !== meta.applicationType)) {
        return false
      }
    }
  }

  return true
}

function logOutUser () {
  router.app.$announcement.tryToStopTour()
  router.app.$cacheProvider.clean()
  store.dispatch('authentication/logOut')
    .then(() => {
      router.push('/login')
    }).catch(ex => {
      router.app.$logger.handleError(ex, `Can't  log out user`)
      router.push('/error')
    })
}

export default router
