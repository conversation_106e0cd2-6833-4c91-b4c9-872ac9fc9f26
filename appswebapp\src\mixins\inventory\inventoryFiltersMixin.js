import globals from '@/globals'

export default {
  methods: {
    convertToViewModel (inventoryFilters) {
      inventoryFilters = globals().getClonedValue(inventoryFilters)
      if (!inventoryFilters || !inventoryFilters.vStatus || Array.isArray(inventoryFilters.vStatus)) {
        return inventoryFilters
      }

      inventoryFilters.vStatus = inventoryFilters.vStatus.split(',').map(x => +x)

      return inventoryFilters
    },
    convertToApiModel  (inventoryFilters) {
      inventoryFilters = globals().getClonedValue(inventoryFilters)
      if (!inventoryFilters || !inventoryFilters.vStatus || typeof inventoryFilters.vStatus === 'string') {
        return inventoryFilters
      }

      let filters = inventoryFilters
      filters.vStatus = filters.vStatus.join(',')

      return filters
    }
  }
}
