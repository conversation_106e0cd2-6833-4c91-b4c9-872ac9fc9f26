<template>
  <div class="table-responsive" v-if="totalItems > 0">
    <b-table
      :items="items"
      :fields="getTableFields"
      hover
      striped
    >
      <template #cell(manage)="data">
        <l-button :loading="data.item.isLoading" size="sm" @click="showDetails(data)" class="text-center">
          {{ data.detailsShowing ? 'Hide' : 'Show' }} Details
        </l-button>
        <a class="btn btn-outline-secondary btn-sm" :href='getLogDetailsPath(data.item.id)' target="_blank">
          <i class="ion ion-md-open"></i>
        </a>
      </template>
      <template #row-details="data">
        <b-card>
          <log-node
            :data="data.item.details"
            :isExpandedShallow="true"
            :isExpandedDeep="false"
          />
        </b-card>
      </template>
    </b-table>
  </div>
</template>

<script>
import moment from 'moment'
import InventoryLogService from '../../../../services/logs/InventoryLogService'
import { vehicleHistoryReportTypes } from '@/shared/inventory/inventoryTypes'

export default {
  name: 'vehicle-history-reports-logs',
  props: {
    items: { type: Array, required: true },
    totalItems: { type: Number, required: true }
  },
  data () {
    return {
    }
  },
  computed: {
    getTableFields () {
      return [
        {
          key: 'vin',
          label: 'Vin',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'startProcessingDateTime',
          label: 'Date',
          tdClass: 'py-2 align-middle',
          formatter: val => moment(val).format('MM/DD/YYYY hh:mm:ss A')
        },
        {
          key: 'reportType',
          label: 'Report Type',
          tdClass: 'py-2 align-middle',
          formatter: val => (Object.values(vehicleHistoryReportTypes).find(x => x.value === val) || {text: '-'}).text
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle'
        }
      ]
    }
  },
  components: {
    'log-node': () => import('@/components/_shared/logItemNode.vue')
  },
  methods: {
    getLogDetailsPath (id) {
      return `/inventory/logs/${id}?logtype=4`
    },
    async showDetails (data) {
      if (data.item.details) {
        data.toggleDetails()
      } else {
        this.$set(data.item, 'isLoading', true)

        try {
          const apiResult = await InventoryLogService.getVehiclePostProcessingLogDetails(data.item.id)

          data.item.details = {
            nodes: apiResult.data.properties
          }

          data.toggleDetails()
        } catch (err) {
          this.$toaster.error('Failed to get data from server')
          this.$logger.handleError(err, 'Can\'t load log details', data.item)
        } finally {
          this.$set(data.item, 'isLoading', false)
        }
      }
    }
  }
}
</script>
