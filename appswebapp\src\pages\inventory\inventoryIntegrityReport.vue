<template>
  <div>
    <b-row>
      <b-col>
        <h4>Integrity Report</h4>
      </b-col>
    </b-row>
    <b-tabs v-model="selectedTab" class="nav-tabs-top nav-responsive-sm" no-fade>
      <b-tab v-for="tab in tabOptions" :key="tab.key" :title="tab.title">
      </b-tab>
      <integrity-report-listing :type="selectedTab"></integrity-report-listing>
    </b-tabs>
  </div>
</template>

<script>
import integrityReportListing from '@/components/inventory/integrityReport/integrityReportListing'
import { integrityReportTypes } from '@/shared/inventory/inventoryTypes'

import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'

const defaultValues = new ObjectSchema({
  tab: { type: Number, default: 0 }
})
const queryHelper = new QueryStringHelper(defaultValues)

export default {
  name: 'inventory-integrity-report',
  metaInfo: {
    title: 'Integrity Report'
  },
  data () {
    return {
      filters: defaultValues.getObject(),
      tabOptions: Object.values(integrityReportTypes)
    }
  },
  created () {
    this.filters = queryHelper.parseQueryStringToObject(this.$router)
  },
  computed: {
    selectedTab: {
      get () {
        if (this.filters.tab > this.tabOptions.length) {
          return 0
        }

        return this.filters.tab
      },
      set (val) {
        this.filters.tab = val
        queryHelper.rebuildParamsInQueryString(this.$router, this.filters)
      }
    }
  },
  components: {
    'integrity-report-listing': integrityReportListing
  }
}
</script>
