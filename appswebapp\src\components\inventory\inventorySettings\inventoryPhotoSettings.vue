<template>
  <div>
    <b-row class="border-bottom pb-1">
      <b-col class="align-middle">
          <span>
            <b>
              Inventory Photo Settings
            </b>
          </span>
      </b-col>

      <b-btn variant="secondary" size="sm" class="edit-btn" @click="setEditMode" v-if='!isEditMode' >
        <font-awesome-icon icon="pencil-alt" size="sm" />
        <span class="btn-title">Edit</span>
      </b-btn>
      <template v-else>
        <b-btn variant="primary" size="sm" class="save-btn" @click="onSave">
          <font-awesome-icon icon="cloud-upload-alt" />
          <span class="btn-title">Save</span>
        </b-btn>
        <b-btn size="sm" class="ml-2" @click="onCancel">
          <span class="btn-title">Cancel</span>
        </b-btn>
      </template>

    </b-row>

    <auto-detail-row v-if='!isEditMode' title='Max Photo Size' :text='inventoryPhotoSettings.maxPhotoWidth' />
    <auto-detail-row v-else title='Max Photo Size' v-model='inventoryPhotoSettingsToUpdate.maxPhotoWidth' :options='allowedPhotoSizes'/>
  </div>
</template>

<script>
import { photoSizeTypes } from '@/shared/inventory/inventoryTypes'
import globals from '../../../globals'

export default {
  name: 'inventory-photo-settings',
  components: {
    'auto-detail-row': () => import('@/components/details/helpers/autoDetailRow'),
    'detail-row': () => import('@/components/details/helpers/detailRow')
  },
  props: {
    inventoryPhotoSettings: { type: Object, required: true }
  },
  data () {
    return {
      isEditMode: false,
      inventoryPhotoSettingsToUpdate: {}
    }
  },
  created () {
    this.init()
  },
  methods: {
    init () {
      this.inventoryPhotoSettingsToUpdate = globals().getClonedValue(this.inventoryPhotoSettings)
    },
    onSave () {
      this.$emit('onUpdatePhotoSettings', this.inventoryPhotoSettingsToUpdate)
      this.isEditMode = false
    },
    setEditMode () {
      this.isEditMode = true
    },
    onCancel () {
      this.inventoryPhotoSettingsToUpdate = globals().getClonedValue(this.inventoryPhotoSettings)
      this.isEditMode = false
    }
  },
  computed: {
    allowedPhotoSizes () {
      return Object.values(photoSizeTypes)
    }
  }
}
</script>
