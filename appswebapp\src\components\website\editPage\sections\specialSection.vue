<template>
  <div class="mt-3" v-if="!isLoading">
    <div class="border-bottom">
      <b-row>
        <b-col class="m-0"><h6>Specials Filter Settings</h6></b-col>
      </b-row>
    </div>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Specials Type:</span>
      <b-form-select v-model="specialsFilters.type" @input="onChangeSpecialType" :options="getSpecialTypeOptions" slot="payload"></b-form-select>
    </detail-row>
    <detail-row :fixed-payload-width="true" v-if="specialsFilters.type === specialTypes.inventory.value">
      <span slot="title">Conditions:</span>
      <multiselect size="sm" multiple slot="payload" v-model="specialsFilters.conditions" @remove="onRemoveConditions" @input="onChangeConditions" trackBy="value" label="text" :options="getSpecialConditionOptions"></multiselect>
    </detail-row>
    <detail-row :fixed-payload-width="true" v-if="specialsFilters.type !== specialTypes.allSpecials.value && specialsFilters.type !== specialTypes.other.value">
      <span slot="title">Categories:</span>
      <multiselect size="sm" multiple v-model="specialsFilters.categories" @remove="onRemoveCategories" @input="populateApiSpecialsFilters" trackBy="specialCategoryId" label="name" :options="specialsCategoriesOptions" slot="payload"></multiselect>
    </detail-row>
    <detail-row :fixed-payload-width="true" v-if="specialsFilters.type === specialTypes.inventory.value">
      <span slot="title">Year Range:</span>
      <div class="d-flex flex-row w-100" slot="payload">
        <span class="d-flex align-items-center mr-2">From</span>
        <b-form-select v-model="specialsFilters.yearFrom" @input="populateApiSpecialsFilters" :options="getYearFromOptions"></b-form-select>
        <span class="d-flex align-items-center mx-2">to</span>
        <b-form-select v-model="specialsFilters.yearTo" @input="populateApiSpecialsFilters" :options="getYearToOptions"></b-form-select>
      </div>
    </detail-row><detail-row titlePosition="start" :extra-large-payload-width="true" v-if="specialsFilters.type === specialTypes.inventory.value">
      <span slot="title">Make/Model:</span>
      <div slot="payload" class="w-100">
        <b-row v-for="(make, index) in specialsFilters.makes" :key="index">
          <b-col xl="3" lg="3" md="3" sm="12 mt-1"><b-form-select :value="make" :options="[make]" disabled></b-form-select></b-col>
          <b-col xl="3" lg="3" md="3" sm="12 mt-1"><b-form-select :value="specialsFilters.models[index] || 'Select Model'" :options="[specialsFilters.models[index] || 'Select Model']" disabled></b-form-select></b-col>
          <b-col xl="3" lg="3" md="3" sm="12 mt-1"><b-form-select :value="specialsFilters.trims[index] || 'Select Trim'" :options="[specialsFilters.trims[index] || 'Select Trim']" disabled></b-form-select></b-col>
          <b-col xl="3" lg="3" md="3" sm="12 mt-1" class="d-flex align-items-center"><b-btn @click="onRemoveMakeModelTrim(index)" size="sm">Remove</b-btn></b-col>
        </b-row>
        <b-row class="mt-2">
          <b-col xl="3" lg="3" md="3" sm="12 mt-1"><b-form-select v-model="selectedMake" @input="onChangeMake" :options="specialsMakes"></b-form-select></b-col>
          <b-col xl="3" lg="3" md="3" sm="12 mt-1"><b-form-select v-model="selectedModel" @input="onChangeModel" :options="specialsModels"></b-form-select></b-col>
          <b-col xl="3" lg="3" md="3" sm="12 mt-1"><b-form-select v-model="selectedTrim" :options="specialsTrims"></b-form-select></b-col>
          <b-col xl="3" lg="3" md="3" sm="12 mt-1" class="d-flex align-items-center"><b-btn @click="onAddMakeModelTrim()" size="sm" :disabled="!selectedMake">Add</b-btn></b-col>
        </b-row>
      </div>
    </detail-row>
  </div>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import multiselect from 'vue-multiselect'
import { specialTypes, specialConditionTypes } from '@/shared/website/constants'
import SpecialsFilterBuilder from '@/components/website/editPage/helpers/specialsFilterBuilder'

export default {
  props: {
    pageSettings: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      isLoading: true,
      accountId: +this.$route.params.accountId,
      specialTypes,
      selectedMake: null,
      selectedModel: null,
      selectedTrim: null,
      specialsFilters: {
        type: 0,
        makes: [],
        models: [],
        trims: [],
        conditions: [],
        categories: [],
        yearFrom: null,
        yearTo: null
      },
      specialsCategoriesOptions: [],
      specialsYears: [],
      specialsTrims: [{value: null, text: 'Select Trim'}],
      specialsMakes: [{value: null, text: 'Select Makes'}],
      specialsModels: [{value: null, text: 'Select Models'}]
    }
  },
  async created () {
    this.populateSpecialsFilters()
    await this.populateSpecialsCategories()
    await this.populateYears()
    await this.populateMakes()
    this.populateSpecialsFilters()
    this.isLoading = false
  },
  computed: {
    getSpecialTypeOptions () {
      return Object.values(specialTypes)
    },
    getSpecialConditionOptions () {
      return Object.values(specialConditionTypes)
    },
    getYearFromOptions () {
      let years = [...this.specialsYears]
      if (this.specialsFilters.yearTo) {
        years = years.splice(0, years.findIndex(x => x === +this.specialsFilters.yearTo))
      }
      let options = [{value: null, text: 'Any Year'}]
      return options.concat(years)
    },
    getYearToOptions () {
      let years = [...this.specialsYears]
      years.reverse()
      if (this.specialsFilters.yearFrom) {
        years = years.splice(0, years.findIndex(x => x === +this.specialsFilters.yearFrom))
      }
      let options = [{value: null, text: 'Any Year'}]

      return options.concat(years)
    }
  },
  components: {
    detailRow,
    multiselect
  },
  methods: {
    onAddMakeModelTrim () {
      this.specialsFilters.makes.push(this.selectedMake)
      if (this.selectedModel) {
        this.specialsFilters.models.push(this.selectedModel)
      }
      if (this.selectedTrim) {
        this.specialsFilters.trims.push(this.selectedTrim)
      }
      this.populateApiSpecialsFilters()
    },
    onRemoveMakeModelTrim (index) {
      this.specialsFilters.makes.splice(index, 1)
      this.specialsFilters.models.splice(index, 1)
      this.specialsFilters.trims.splice(index, 1)
      this.populateApiSpecialsFilters()
    },
    onChangeConditions () {
      this.specialsFilters.yearFrom = null
      this.specialsFilters.yearTo = null
      this.selectedMake = null
      this.selectedModel = null
      this.selectedTrim = null
      this.populateYears()
      this.populateMakes()
      this.populateModels()
      this.populateTrims()
      this.populateApiSpecialsFilters()
    },
    onRemoveConditions (option) {
      let index = this.specialsFilters.conditions.findIndex(x => x.value === option.value)
      this.specialsFilters.conditions.splice(index, 1)
      this.onChangeConditions()
    },
    onRemoveCategories (option) {
      let index = this.specialsFilters.categories.findIndex(x => x.specialCategoryId === option.specialCategoryId)
      this.specialsFilters.categories.splice(index, 1)
      this.populateApiSpecialsFilters()
    },
    onChangeMake () {
      this.populateModels()
    },
    onChangeModel () {
      this.populateTrims()
    },
    async onChangeSpecialType () {
      this.setDefaultSpecialsFiltersData()
      await this.populateSpecialsCategories()
      this.populateApiSpecialsFilters()
    },
    setDefaultSpecialsFiltersData () {
      this.specialsFilters.categories = []
      this.specialsFilters.conditions = []
      this.specialsFilters.makes = []
      this.specialsFilters.models = []
      this.specialsFilters.trims = []
      this.specialsFilters.yearFrom = null
      this.specialsFilters.yearTo = null
    },
    async populateSpecialsCategories () {
      if (this.specialsFilters.type !== specialTypes.allSpecials.value && this.specialsFilters.type !== specialTypes.other.value) {
        try {
          let res = await this.$store.dispatch('specials/getSpecialsCategories', {params: {specialType: this.specialsFilters.type}})
          this.specialsCategoriesOptions = res.data
        } catch (ex) {
          this.$toaster.error('Something went wrong! Please reload page')
          this.$logger.handleError(ex, 'Cannot get specials categories')
        }
      } else {
        this.specialsCategoriesOptions = []
      }
    },
    async populateYears () {
      if (this.specialsFilters.conditions && this.specialsFilters.conditions.length > 0) {
        let apiParams = {
          accountId: this.accountId,
          conditions: this.specialsFilters.conditions.map(x => x.value).join()
        }
        try {
          let res = await this.$store.dispatch('specials/getSpecialsYears', {params: apiParams})
          this.specialsYears = res.data
        } catch (ex) {
          this.$toaster.error('Something went wrong! Please reload page')
          this.$logger.handleError(ex, 'Cannot get specials years')
        }
      } else {
        this.specialsYears = []
      }
    },
    populateMakes () {
      if (this.specialsFilters.conditions && this.specialsFilters.conditions.length > 0) {
        let apiParams = {
          accountId: this.accountId,
          conditions: this.specialsFilters.conditions.map(x => x.value).join()
        }
        this.$store.dispatch('specials/getSpecialsMakes', {params: apiParams}).then(res => {
          this.specialsMakes = [{value: null, text: 'Select Make'}].concat(res.data || [])
        }).catch(ex => {
          this.$toaster.error('Something went wrong! Please reload page')
          this.$logger.handleError(ex, 'Cannot get specials makes')
        })
      } else {
        this.specialsMakes = [{value: null, text: 'Select Makes'}]
      }
    },
    populateModels () {
      if (this.specialsFilters.conditions && this.specialsFilters.conditions.length > 0 && this.selectedMake) {
        let apiParams = {
          accountId: this.accountId,
          conditions: this.specialsFilters.conditions.map(x => x.value).join(),
          make: this.selectedMake
        }
        this.$store.dispatch('specials/getSpecialsModels', {params: apiParams}).then(res => {
          this.specialsModels = [{value: null, text: 'Select Models'}].concat(res.data || [])
        }).catch(ex => {
          this.$toaster.error('Something went wrong! Please reload page')
          this.$logger.handleError(ex, 'Cannot get specials makes')
        })
      } else {
        this.specialsModels = [{value: null, text: 'Select Models'}]
      }
    },
    populateTrims () {
      if (this.specialsFilters.conditions && this.specialsFilters.conditions.length > 0 && this.selectedMake && this.selectedModel) {
        let apiParams = {
          accountId: this.accountId,
          conditions: this.specialsFilters.conditions.map(x => x.value).join(),
          make: this.selectedMake,
          model: this.selectedModel
        }
        this.$store.dispatch('specials/getSpecialsTrims', {params: apiParams}).then(res => {
          this.specialsTrims = [{value: null, text: 'Select Trims'}].concat(res.data || [])
        }).catch(ex => {
          this.$toaster.error('Something went wrong! Please reload page')
          this.$logger.handleError(ex, 'Cannot get specials makes')
        })
      } else {
        this.specialsTrims = [{value: null, text: 'Select Trims'}]
      }
    },
    populateSpecialsFilters () {
      let specialsFilterBuilder = new SpecialsFilterBuilder(this.specialsCategoriesOptions)
      specialsFilterBuilder.BuildSpecialsFilters(this.pageSettings.pageFilters, this.specialsFilters)
    },
    populateApiSpecialsFilters () {
      let specialsFilterBuilder = new SpecialsFilterBuilder(this.specialsCategoriesOptions)
      this.pageSettings.pageFilters = specialsFilterBuilder.BuildApiSpecialsFilters(this.specialsFilters)
    }
  }
}
</script>
