export default {
  taskStatusTypes: Object.freeze({
    waiting: { value: 0, text: 'Waiting' },
    inQueue: { value: 1, text: 'In Queue' },
    inProcess: { value: 2, text: 'In Process' },
    completed: { value: 3, text: 'Completed' },
    failed: { value: 4, text: 'Failed' }
  }),
  taskPriorityTypes: Object.freeze({
    low: { value: 0, text: 'Low' },
    normal: { value: 1, text: 'Normal' },
    high: { value: 2, text: 'High' },
    highest: { value: 255, text: 'Highest' }
  }),
  sortTypes: {
    accountIdAsc: 0,
    accountIdDesc: 1,
    vinAsc: 2,
    vinDesc: 3,
    taskStatusAsc: 4,
    taskStatusDesc: 5,
    taskPriorityAsc: 6,
    taskPriorityDesc: 7,
    dateCreatedAsc: 8,
    dateCreatedDesc: 9,
    dateProcessedAsc: 10,
    dateProcessedDesc: 11
  },
  textConversionSortTypes: {
    fromAsc: 1,
    fromDesc: 2,
    toAsc: 3,
    toDesc: 4,
    typeAsc: 5,
    typeDesc: 6
  },
  textConversionTypes: Object.freeze({
    caseSensitive: { value: 1, text: 'Case Sensitive' },
    caseInsensitive: { value: 2, text: 'Not Case Sensitive' },
    regularExpression: { value: 3, text: 'Regular Expression' }
  }),
  descriptionTextSections: Object.freeze({
    intro: { value: 1, text: 'Intro' },
    vehicleHistory: { value: 2, text: 'Vehicle History' },
    mileage: { value: 3, text: 'Mileage' },
    warranty: { value: 4, text: 'Warranty' },
    fuelEconomy: { value: 5, text: 'Economy' },
    engine: { value: 6, text: 'Engine' },
    fuelType: { value: 7, text: 'Fuel' },
    features: { value: 8, text: 'Features' },
    outro: { value: 2147483647, text: 'Outro' }// value equals to c# int.MaxValue
  }),
  voiceTypes: Object.freeze({
    none: {value: 0, text: 'No Voice, only music will play'},
    random: {value: 1, text: 'Random'},
    joanna: {value: 2, text: 'Female Voice, US English Accent'},
    matthew: {value: 3, text: 'Male Voice, US English Accent'}
  }),
  musicTypes: Object.freeze({
    none: 0,
    random: 1,
    custom: 2,
    genreRandom: 3,
    genreSpecific: 4
  }),
  genreTypes: Object.freeze({
    undefined: {value: 0, text: 'Undefined'},
    blues: {value: 1, text: 'Blues'},
    contemporaryClassical: {value: 2, text: 'Contemporary-Classical'},
    country: {value: 3, text: 'Country'},
    electronic: {value: 4, text: 'Electronic'},
    funk: {value: 5, text: 'Funk'},
    rAndB: {value: 6, text: 'R&B'},
    rock: {value: 7, text: 'Rock'}
  }),
  photoToVideoLogTypes: Object.freeze({
    processing: 'processing',
    queueManager: 'queuemanager'
  }),
  photoToVideoLogSortTypes: Object.freeze({
    dateAsc: 1,
    dateDesc: 2
  }),
  autoVideoStatusTypes: {
    active: { value: 1, text: 'Active' },
    inactive: { value: 0, text: 'Inactive' },
    getActivationStatusText: function (value) {
      return (Object.values(this)
        .find(x => x.value === value) || { text: '-' }
      ).text
    }
  }
}
