<template>
  <div>
    <h4>User Activity Details</h4>
    <b-card v-if="!isLoading">
      <log-node
        v-if="log"
        :data="log"
        :isExpandedShallow="true"
        :isExpandedDeep="false"
      />
      <span v-else>Not Found</span>
    </b-card>
    <div v-else class="my-3 py-3">
      <loader size="lg"/>
    </div>
  </div>
</template>

<script>
export default {
  name: 'sitebox-log-details',
  metaInfo: {
    title: 'Log Details'
  },
  props: {
    logId: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      isLoading: true,
      log: null
    }
  },
  components: {
    'log-node': () => import('@/components/_shared/logItemNode.vue'),
    'loader': () => import('@/components/_shared/loader')
  },
  mounted () {
    this.populateData()
  },
  methods: {
    async populateData () {
      try {
        const apiResult = await this.$store.dispatch('siteBoxManager/getLogDetails', {id: this.logId})

        this.log = {
          nodes: apiResult.data.properties
        }
      } catch (err) {
        this.$toaster.exception(err, 'Failed to get data from server')
      } finally {
        this.isLoading = false
      }
    }
  }
}
</script>
