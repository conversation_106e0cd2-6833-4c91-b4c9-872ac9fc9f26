<template>
  <div>
    <h6>Brand Exports</h6>
    <hr>
    <b-table
      :items="items"
      :fields="getTableFields"
      striped
      outlined
      responsive>
      <template #cell(reports)="data">
        <span v-if="data.item.reports && data.item.reports.length > 0" @click.stop="data.toggleDetails" class="custom-link mr-1">
          {{ data.detailsShowing ? 'Hide' : 'Show' }}({{data.item.reports.length}})
        </span>
        <span v-if="data.detailsShowing"><i class="ion ion-ios-arrow-up opacity-75 text-primary"></i></span>
        <span v-else><i class="ion ion-ios-arrow-down opacity-75 text-primary"></i></span>
      </template>
      <template #row-details="data">
        <b-table
          :items="data.item.reports"
          :fields="getDetailsTableFields"
          striped
          outlined
          responsive>
          <template #cell(report)="report">
            <span>"{{report.item.rowDelimiter}}" <strong>/</strong> "{{report.item.columnDelimiter}}"</span>
          </template>
          <template #cell(manage)="row">
            <div class="d-flex flex-row">
              <b-btn class="mr-1 text-nowrap" size="sm" @click="onEditReport(data.item, row.item)"><font-awesome-icon  icon="pencil-alt" size="sm"/> Edit</b-btn>
              <b-btn v-if="data.item.isActive" class="text-nowrap" size="sm" @click="rebuild(data.item, row.item.type, row.item.reportRangeType)" variant="primary"><font-awesome-icon  icon="tasks" size="sm"/> Rebuild</b-btn>
            </div>
          </template>
        </b-table>
      </template>
      <template #cell(manage)="data">
        <b-btn size="sm" @click="onEditExport(data.item)"><font-awesome-icon  icon="pencil-alt" size="sm"/> Edit</b-btn>
        <b-btn size="sm" v-if="data.item.isActive" @click="rebuild(data.item, 0, 0)" variant="primary"><font-awesome-icon  icon="tasks" size="sm"/> Rebuild</b-btn>
      </template>
    </b-table>
    <export-rebuild-modal
      :isShowModal='isShowRebuildModal'
      :reportInfo='exportReportRebuildInfo'
      @hide="hideRebuild"
      @rebuildExportSettings="rebuildExportSettings"
    >
    </export-rebuild-modal>
    <export-edit-modal
      :isShowModal='isShowExportEditModal'
      :model='exportModalModel'
      @hide="hideExportModal"
      @save="saveExportModel">
    </export-edit-modal>
    <report-edit-modal
      :isShowModal='isShowReportEditModal'
      :model='reportModalModel'
      @hide="hideReportModal"
      @save="saveReportModel">
    </report-edit-modal>
  </div>
</template>

<script>
import brandExportRebuildModal from '@/components/analytics_ga4/brandExport/brandExportRebuildModal'
import brandExportReportEditModal from '@/components/analytics_ga4/brandExport/brandExportReportEditModal'
import brandExportEditModal from '@/components/analytics_ga4/brandExport/brandExportEditModal'
import BrandExportService from '@/services/analytics/BrandExportService'
import constants from '@/shared/analytics/constants'
import moment from 'moment'

export default {
  name: 'analytics-brand-export-listing',
  props: {
    items: { type: Array, required: true }
  },
  data () {
    return {
      exportReportRebuildInfo: {
        exportId: '',
        exportName: '',
        reportType: 0,
        reportRangeType: 0
      },
      reportModalModel: null,
      exportModalModel: null,
      isShowRebuildModal: false,
      isShowExportEditModal: false,
      isShowReportEditModal: false
    }
  },
  components: {
    'export-rebuild-modal': brandExportRebuildModal,
    'export-edit-modal': brandExportEditModal,
    'report-edit-modal': brandExportReportEditModal
  },
  computed: {
    getTableFields () {
      return [
        {
          key: 'name',
          label: 'Export Name',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'brandType',
          label: 'Export Brand Type',
          tdClass: 'py-2 align-middle',
          formatter: value => {
            let res = constants.brandTypes.find(x => x.value === value)
            return res ? res.text : 'Undefined'
          }
        },
        {
          key: 'makeId',
          label: 'Make ID',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'isActive',
          label: 'Status',
          tdClass: 'py-2 align-middle',
          formatter: value => value ? 'Active' : 'Inactive'
        },
        {
          key: 'reports',
          label: 'Reports',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    getDetailsTableFields () {
      return [
        {
          key: 'type',
          label: 'Type',
          tdClass: 'py-2 align-middle text-truncate',
          formatter: value => {
            let res = constants.reportTypes.find(x => x.value === value)
            return res ? res.text : 'Undefined'
          }
        },
        {
          key: 'fileName',
          label: 'File Name',
          tdClass: 'py-2 align-middle text-truncate'
        },
        {
          key: 'fileNameDateFormat',
          label: 'File Name Date Format',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'fileNameDateShiftInDays',
          label: 'Offset day(s) in file name',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'delimiter',
          label: 'Row/Col Delimiter',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'hasToIncludeColumnNames',
          label: 'Include Columns Names',
          tdClass: 'py-2 align-middle',
          formatter: value => value ? 'Yes' : 'No'
        },
        {
          key: 'reportDateShiftInDays',
          label: 'Offset day(s) in report\'s data',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'ftpSubfolder',
          label: 'Ftp Subfolder',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'runTime',
          label: 'Run Time',
          tdClass: 'py-2 align-middle',
          formatter: value => value ? moment(value, 'hh:mm:ss').format('hh:mm') : '-'
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle'
        }
      ]
    }
  },
  methods: {
    rebuild (item, reportType, reportRangeType) {
      this.exportReportRebuildInfo.exportId = item.id
      this.exportReportRebuildInfo.exportName = item.name
      this.exportReportRebuildInfo.reportType = reportType
      this.exportReportRebuildInfo.reportRangeType = reportRangeType
      this.isShowRebuildModal = true
    },
    hideRebuild () {
      this.isShowRebuildModal = false
    },
    rebuildExportSettings (model) {
      BrandExportService.rebuildBrandExportSettings(model).then(res => {
        if (model.reportType === 0) {
          this.$toaster.success(`All Reports for Export ${model.exportName} Successfully Rebuild`)
        } else {
          this.$toaster.success(`Report Successfully Rebuild`)
        }
      }).catch(ex => {
        this.$toaster.error('Something went wrong')
        this.$logger.handleError(ex, 'Cannot rebuild brand export settings', model)
      }).finally(() => {
        this.isShowRebuildModal = false
      })
    },
    saveReportModel (model) {
      BrandExportService.updateBrandExportReportSettings(model).then(res => {
        this.$toaster.success(`Report Successfully Updated`)
      }).catch(ex => {
        this.$toaster.error('Something went wrong')
        this.$logger.handleError(ex, 'Cannot update brand export report settings', model)
      }).finally(() => {
        this.isShowReportEditModal = false
      })
    },
    onEditReport (brandExport, report) {
      this.reportModalModel = report
      this.reportModalModel.exportId = brandExport.id
      this.reportModalModel.exportName = brandExport.name
      this.isShowReportEditModal = true
    },
    hideReportModal () {
      this.isShowReportEditModal = false
    },
    onEditExport (item) {
      this.exportModalModel = item
      this.isShowExportEditModal = true
    },
    saveExportModel (model) {
      BrandExportService.updateBrandExportSettings(model).then(res => {
        this.$toaster.success(`Export ${model.name} Successfully Updated`)
      }).catch(ex => {
        this.$toaster.error('Something went wrong')
        this.$logger.handleError(ex, 'Cannot update brand export settings', model)
      }).finally(() => {
        this.isShowExportEditModal = false
      })
    },
    hideExportModal () {
      this.isShowExportEditModal = false
    }
  }
}
</script>

<style scoped>
.custom-link{
  border-bottom: 0.01rem solid #9a0b12;
  color: #9a0b12;
  cursor: pointer;
}
</style>
