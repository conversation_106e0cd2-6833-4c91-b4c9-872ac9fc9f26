const conditionType = Object.freeze({
  equal: { value: 0, label: 'Equal' },
  contains: { value: 1, label: 'Contains' },
  regExp: { value: 2, label: 'RegExp' }
})

const fieldType = Object.freeze({
  undefined: { value: 0, label: 'All' },
  from: { value: 1, label: 'From' },
  to: { value: 2, label: 'To' },
  subject: { value: 3, label: 'Subject' },
  body: { value: 4, label: 'Body' },
  ip: { value: 5, label: 'Ip' },
  firstName: { value: 6, label: 'First Name' },
  lastName: { value: 7, label: 'Last Name' },
  phone: {value: 10, label: 'Phone Number'},
  jobTitle: { value: 9, label: 'Job Title' },
  employerName: { value: 8, label: 'Employer Name' }
})

const applicationType = Object.freeze({
  undefined: {
    value: 0,
    label: 'All',
    fields: []
  },
  creditApplication: {
    value: 1,
    label: 'Credit Application',
    fields: [fieldType.ip, fieldType.firstName, fieldType.lastName, fieldType.phone, fieldType.jobTitle, fieldType.employerName, fieldType.body]
  },
  galleryLeadForm: {
    value: 2,
    label: 'Gallery Lead Form',
    fields: [fieldType.ip, fieldType.firstName, fieldType.lastName, fieldType.phone, fieldType.body]
  },
  email: {
    value: 3,
    label: 'Email',
    fields: [fieldType.from, fieldType.to, fieldType.subject, fieldType.body]
  }
})

const spamFilterSortType = Object.freeze({
  undefined: 0,
  applicationTypeAsc: 1,
  applicationTypeDesc: 2
})

const spamMessageSortType = Object.freeze({
  accountIdAsc: 1,
  accountIdDesc: 2,
  emailAsc: 3,
  emailDesc: 4,
  createdDateAsc: 5,
  createdDateDesc: 6,
  sourceIpAsc: 7,
  sourceIpDesc: 8
})

const spamReasonType = Object.freeze({
  undefined: { value: 0, label: 'All reasons' },
  spamFilter: { value: 1, label: 'Spam filter' },
  ipRateLimit: { value: 2, label: 'IP rate limit' },
  turnstileValidation: { value: 3, label: 'Turnstile validation' }
})

export {
  conditionType,
  fieldType,
  spamFilterSortType,
  spamMessageSortType,
  applicationType,
  spamReasonType
}
