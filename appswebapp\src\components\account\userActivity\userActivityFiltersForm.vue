<template>
  <div class="p-3">
    <b-form @submit.prevent="applyFilters">
      <b-row>
        <b-col class="my-1" sm="4" lg="4" xl="2">
          <b-form-input placeholder="Search..." v-model="filters.search"></b-form-input>
        </b-col>
        <b-col class="my-1" sm="4" lg="4" xl="2">
          <b-input-group class="flex-nowrap">
            <b-input-group-prepend is-text>
              <i class="ion ion-md-calendar" slot="prepend"></i>
            </b-input-group-prepend>
            <date-time-picker
              ref="timeFrom"
              v-model="filters.dateFrom"
              :options="filterTimeOptions"
              format="MM/DD/YYYY HH:mm"
              placeholder="Date From"
              className="form-control"
              @change="onTimeFromInputChange"
            />
            <b-input-group-append
              is-text
              v-show="filters.dateFrom"
              @click="filters.dateFrom = null"
            >
              <i class="ion ion-md-close"></i>
            </b-input-group-append>
          </b-input-group>
        </b-col>

        <b-col class="my-1" sm="4" lg="4" xl="2">
          <b-input-group class="flex-nowrap">
            <b-input-group-prepend is-text>
              <i class="ion ion-md-calendar" slot="prepend"></i>
            </b-input-group-prepend>
            <date-time-picker
              ref="timeTo"
              v-model="filters.dateTo"
              :options="filterTimeOptions"
              format="MM/DD/YYYY HH:mm"
              placeholder="Date To"
              className="form-control"
              @change="onTimeToInputChange"
            />
            <b-input-group-append
              is-text
              v-show="filters.dateTo"
              @click="filters.dateTo = null"
            >
              <i class="ion ion-md-close"></i>
            </b-input-group-append>
          </b-input-group>
        </b-col>
        <b-col class="my-1" sm="4" lg="4" xl="2">
          <b-form-select v-model="filters.action" :options="getActionOptions"></b-form-select>
        </b-col>
        <b-col class="my-1" sm="4" lg="4" xl="2">
          <multiSelectWithCheckboxes
            v-model='selectedUserTypes'
            :options='getUserTypeOptions'
            name="User Types"
            customMessageOfNoneSelectedItems="All User Types"
          ></multiSelectWithCheckboxes>
        </b-col>
        <b-col class="my-1" sm="4" lg="4" xl="2">
          <b-btn block variant="primary" type="submit">Submit</b-btn>
        </b-col>
      </b-row>
    </b-form>
  </div>
</template>

<script>
import multiSelectWithCheckboxes from '../../_shared/multiSelectWithCheckboxes.vue'
import userActivityConstants from '../../../shared/accounts/userActivityConstants'
import {userTypes} from '@/shared/users/constants'
import DateRangePicker from '@gravitano/vue-date-range-picker/src/components/DateRangePicker'

export default {
  name: 'account-user-activity-filters-form',
  props: {
    filters: { type: Object, required: true },
    type: { type: Number, required: true }
  },
  data () {
    return {
      filterTimeOptions: {
        autoUpdateInput: false,
        singleDatePicker: true,
        timePicker: true,
        timePicker24Hour: true,
        maxDate: new Date()
      }
    }
  },
  components: {
    'date-time-picker': DateRangePicker,
    multiSelectWithCheckboxes
  },
  computed: {
    selectedUserTypes: {
      get () {
        let array = this.getIntegerArrayFromString(this.filters.userTypes)
        return array || []
      },
      set (value) {
        this.filters.userTypes = (value || []).join()
      }
    },
    refDateTimeFrom () {
      return (this.$refs.timeFrom || {}).$el || {}
    },
    refDateTimeTo () {
      return (this.$refs.timeTo || {}).$el || {}
    },
    getActionOptions () {
      let applyOptions = (array, options) => {
        array.forEach(x => {
          if (x.value !== 0) {
            options.push(x)
          }
        })
      }
      let options = [{ value: 0, text: 'All Actions' }]
      switch (this.type) {
        case userActivityConstants.userActivityTypes.account.value:
          applyOptions(Object.values(userActivityConstants.accountActionTypes), options)
          break
        case userActivityConstants.userActivityTypes.group.value:
          applyOptions(Object.values(userActivityConstants.groupActionTypes), options)
          break
        case userActivityConstants.userActivityTypes.contact.value:
          applyOptions(Object.values(userActivityConstants.contactActionTypes), options)
          break
      }
      return options.sort((left, right) => left.text.localeCompare(right.text))
    },
    getUserTypeOptions () {
      let options = []
      Object.values(userTypes).forEach(x => {
        options.push(x)
      })
      return options
    }
  },
  methods: {
    onTimeFromInputChange (newVal) {
      this.refDateTimeFrom.value = newVal || this.filters.dateFrom || null
    },
    onTimeToInputChange (newVal) {
      this.refDateTimeTo.value = newVal || this.filters.dateTo || null
    },
    applyFilters () {
      this.$emit('filtersChange')
    },
    getIntegerArrayFromString (str) {
      if (!str || !str.trim()) {
        return []
      }
      let array = []
      for (let numberStr of str.split(',')) {
        let number = parseInt(numberStr)
        if (!isNaN(number)) {
          array.push(number)
        }
      }
      return array
    }
  }
}
</script>
