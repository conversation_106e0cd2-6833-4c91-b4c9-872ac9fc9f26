<template>
  <div>
    <template v-if="isLoaded">
      <h4>Inventory Log Details</h4>
      <b-card>
        <log-node
          v-if="log"
          :data="log"
          :isExpandedShallow="true"
          :isExpandedDeep="false"
        />
        <span v-else class="text-muted">Not Found</span>
      </b-card>
    </template>
    <template v-else>
      <loader size="lg"/>
    </template>
  </div>
</template>

<script>
import InventoryLogService from '@/services/logs/InventoryLogService'
import loader from '@/components/_shared/loader'
import { logTypes } from '@/shared/inventory/inventoryTypes'

export default {
  name: 'inventory-log-details',
  metaInfo: {
    title: 'Inventory Log Details'
  },
  props: {
    logId: {
      type: String,
      required: true
    }
  },
  components: {
    'log-node': () => import('@/components/_shared/logItemNode.vue'),
    loader
  },
  data () {
    return {
      isLoaded: false,
      log: null,
      logType: 1
    }
  },
  methods: {
    async getLogData () {
      let logItem
      switch (this.logType) {
        case logTypes.mobile.value:
        case logTypes.desktop.value:
          logItem = await this.getInventoryApiLogDetails()
          return logItem
        case logTypes.settings.value:
          logItem = await this.getInventorySettingsLogDetails()
          return logItem
        case logTypes.vehiclePostProcessing.value:
        case logTypes.vehicleHistoryReport.value:
          logItem = await this.getVehiclePostProcessingLogDetails()
          return logItem
      }
    },
    async getInventoryApiLogDetails () {
      return this.processRequest((id) => InventoryLogService.getInventoryApiLogDetails(id))
    },
    async getInventorySettingsLogDetails () {
      return this.processRequest((id) => InventoryLogService.getSettingsLogDetails(id))
    },
    async getVehiclePostProcessingLogDetails () {
      return this.processRequest((id) => InventoryLogService.getVehiclePostProcessingLogDetails(id))
    },
    async processRequest (getFunc) {
      try {
        const apiResult = await getFunc(this.logId)
        return {
          nodes: apiResult.data.properties
        }
      } catch (err) {
        console.error(err)
        this.$toaster.exception(err, 'Failed on getting details of the log', {timeout: 5000})
      }
    }
  },
  mounted: async function () {
    this.logType = +this.$route.query.logtype || 0
    this.log = await this.getLogData()
    this.isLoaded = true
  }
}
</script>
