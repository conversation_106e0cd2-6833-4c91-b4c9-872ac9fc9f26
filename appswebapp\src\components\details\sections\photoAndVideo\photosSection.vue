<template>
  <details-photo-section
    ref="controlSection"
    :title="`Photos (${photoCount})`"
    v-model="mode"
    :delete-all="isAllPhotosMarkedForDelete"
    :hasSelectedToDeletePhotos="hasSelectedToDeleteItems"
    :multiple-file-mode="true"
    :disable-add-button="hasToDisableAddButton"
    :disable-delete-button="hasToDisableDeleteButton"
    :disable-rearrange-button="hasToDisableRearrangeButton"
    @modifyPhotos="onModifyPhotos"
    @deleteAll="onToggleDeleteAll"
    @saveAddedFiles="onSavePhotos"
    class="photo_section"
  >
    <div v-if="mode === 'view'">
      <b-row>
        <b-col cols="6" sm="4" lg="3" v-for="(i, imageIndex) in vehiclePhotos" :key="imageIndex" class="pt-3">
          <div class="photo-fit-container">
            <div class="photo-wrapper" @click="imageGalleryIndex = imageIndex">
              <image-lazy :src="i.photo640" :preload-src="i.photo107" :query="actualPhotos.dateTimeEdited"/>
            </div>
          </div>
        </b-col>
      </b-row>

      <b-row class="mt-3" v-if="hasToShowToggler">
        <b-col class="view-toggle">
          <b-btn class="d-sm-none w-100 view-toggle__mobile-toggler" variant="primary" @click="viewMore = !viewMore">{{toggleText}}</b-btn>
          <b-btn class="view-toggle__toggler d-none d-sm-block" variant="link" @click="viewMore = !viewMore">{{toggleText}}</b-btn>
        </b-col>
      </b-row>

      <gallery :images="galleryPhotos" :index="imageGalleryIndex" @close="imageGalleryIndex = null"></gallery>
    </div>

    <div v-else-if="isRearrangeMode">
      <draggable
        class="row"
        v-bind="draggableOptions"
        v-model="actualPhotos.photoItems"
        @start="isDragging = true"
        @end="isDragging = false"
        :class="{on: isDragging, off: !isDragging}"
        handle=".photo-fit-container"
      >
        <b-col cols="6" sm="4" lg="3" v-for="(i, imageIndex) in vehiclePhotos" :key="imageIndex" class="mt-3">
          <card-component>

            <div class="photo-fit-container" slot="header">
              <div class="draggable-overflow">
                <div>Drag this photo</div>
                <div>to rearrange</div>
                <font-awesome-icon icon="arrows-alt" size="2x" class="mt-2" />
              </div>
              <div class="photo-wrapper" @click="imageGalleryIndex = imageIndex">
                <image-lazy :src="i.photo640" :preload-src="i.photo107" :query="actualPhotos.dateTimeEdited"/>
              </div>
            </div>

            <div slot="body">
              <div class="rearrange-group">
                <div>Photo {{i.index}}</div>
                <input type="text" :value="imageIndex+1" @change="onIndexChange($event, imageIndex + 1)">
              </div>
            </div>

          </card-component>
        </b-col>
      </draggable>
    </div>

    <div v-else-if="isDeleteMode">
      <b-row>
        <b-col cols="6" sm="4" lg="3" v-for="(i, imageIndex) in vehiclePhotos" :key="imageIndex" class="mt-3">
          <card-component>

            <div class="photo-fit-container" slot="header">
              <div class="photo-wrapper" @click="imageGalleryIndex = imageIndex">
                <image-lazy :src="i.photo640" :preload-src="i.photo107" :query="actualPhotos.dateTimeEdited" @click="toggleDelete($event, i.index)"/>
              </div>
            </div>

            <div slot="body">
              <div>Photo {{i.index}}</div>
            </div>

            <div slot="footer">
              <div class="delete-photo">
                <b-btn variant="link" class="delete-photo__button" @click="toggleDelete($event, i.index)">Delete Photo</b-btn>
                <b-form-checkbox class="mr-0" @change="toggleDelete($event, i.index)" :checked="photoIndexesToRemove[i.index]"/>
              </div>
            </div>

          </card-component>
        </b-col>
      </b-row>
    </div>

    <div v-else-if="isAddPhotosMode">
      <file-upload
        message="Drop vehicle photos here"
        ref="fileUpload"
        :upload-path="photoUploadPath"
        :maxFiles="maxPhotosAllowed - (actualPhotos.photoItemsCount || 0)"
        :maxPhotoWidth="maxPhotoWidth"
        :maxFileSizeInBytes="maxPhotoSizeInBytes"
        acceptedFiles="image/*"
        :multipleFileMode="true"
        @uploadComplete="onUploadFinish"
        @addedFile="onFileAdded"
      />
    </div>

  </details-photo-section>
</template>

<script>
import {mapGetters} from 'vuex'
import vueGallery from 'vue-gallery'
import imageLazy from '../../../_shared/lazyImage'
import detailsPhotoSection from './detailsPhotoSection'
import cardComponent from '../../helpers/cardComponent'
import draggable from 'vuedraggable'
import dropzone from '../../helpers/dropzoneWrapperComponent'
import vehicleSaveMixin from '../../../../mixins/vehicle/vehicleSaveMixin'
import inventoryService from '../../../../services/inventory/InventoryService'
import vehicleConstants from '../../../../shared/details/vehicleConstants'

export default {
  name: 'photos-section',
  mixins: [vehicleSaveMixin],
  data () {
    return {
      defaultPhotosCount: 8,
      isDragging: false,
      mode: 'view',
      viewMore: false,
      imageGalleryIndex: null,
      photoIndexesOrder: null,
      photoIndexesToRemove: {},
      draggableOptions: {
        animation: 150
      }
    }
  },
  computed: {
    ...mapGetters('details', ['vehicle', 'vehicleOriginal', 'vehiclePhotoSettings']),
    photoUploadPath () {
      return inventoryService.getVehiclePhotoUploadPath(this.vehicle.accountId, this.vehicle.vin)
    },
    hasToDisableAddButton () {
      return this.photoCount === this.maxPhotosAllowed
    },
    hasToDisableDeleteButton () {
      return this.photoCount === 0
    },
    hasToDisableRearrangeButton () {
      return this.photoCount < 2
    },
    isRearrangeMode () {
      return this.mode === 'rearrange'
    },
    isDeleteMode () {
      return this.mode === 'delete'
    },
    isAddPhotosMode () {
      return this.mode === 'add'
    },
    actualPhotos () {
      return this.vehicle.photos.actualPhotos || {}
    },
    photoCount () {
      return this.actualPhotos.photoItemsCount || 0
    },
    photoSettings () {
      return this.vehiclePhotoSettings || {
        maxPhotosCount: vehicleConstants.defaultMaxPhotosCount,
        maxPhotoWidth: vehicleConstants.defaultMaxPhotoWidth,
        maxPhotoSizeInBytes: vehicleConstants.defaultMaxPhotoSizeInBytes
      }
    },
    maxPhotosAllowed () {
      return this.photoSettings.maxPhotosCount
    },
    maxPhotoWidth () {
      return this.photoSettings.maxPhotoWidth
    },
    maxPhotoSizeInBytes () {
      return this.photoSettings.maxPhotoSizeInBytes
    },
    hasToShowToggler () {
      return this.photoCount > this.defaultPhotosCount
    },
    isAllPhotosMarkedForDelete () {
      return !this.vehiclePhotos.some(x => !this.photoIndexesToRemove[x.index])
    },
    hasSelectedToDeleteItems () {
      return this.vehiclePhotos.some(x => !!this.photoIndexesToRemove[x.index])
    },
    vehiclePhotos () {
      return (this.viewMore || this.isRearrangeMode || this.isDeleteMode)
        ? this.actualPhotos.photoItems || []
        : (this.actualPhotos.photoItems || []).slice(0, this.defaultPhotosCount)
    },
    galleryPhotos () {
      return (this.actualPhotos.photoItems || [])
        .map(x => this.getImageLinkWithQuery(x.photo1024 || x.photo800 || x.photo640 || x.photo400))
    },
    toggleText () {
      return this.viewMore
        ? 'View Less'
        : 'View More'
    }
  },
  methods: {
    async onModifyPhotos () {
      this.$refs.controlSection.disableSaveButton()

      let photos = this.vehiclePhotos
        .filter(x => this.photoIndexesToRemove[x.index] !== true)

      this.vehicle.photos.actualPhotos.photoItems = photos
      await this.updateVehiclePhotos(this.vehicleOriginal, this.vehicle)

      this.photoIndexesToRemove = {}

      if (this.$refs.controlSection) {
        this.$refs.controlSection.enableSaveButton()
        this.$refs.controlSection.setViewMode()
      }
    },
    onSavePhotos () {
      this.$refs.controlSection.disableSaveButton()
      this.$refs.fileUpload.sendFiles()
    },
    async onUploadFinish (isSuccess) {
      if (isSuccess) {
        try {
          await this.$store.dispatch('details/populateBaseVehicleData', {accountId: this.vehicle.accountId, vin: this.vehicle.vin})
        } catch (e) {
          this.$toaster.error("Can't get vehicle updated information.")
          this.$logger.handleError(e, "Can't get vehicle updated information. Reload the page.")
        }

        this.$refs.controlSection.setViewMode()
      }
      this.$refs.controlSection.enableSaveButton()
    },
    onToggleDeleteAll (newVal) {
      this.vehiclePhotos.forEach(x => this.$set(this.photoIndexesToRemove, x.index, newVal))
    },
    onIndexChange (newVal, oldVal) {
      if (!Number.isInteger(+newVal.target.value) || !Number.isInteger(+newVal.target.value)) {
        return
      }

      let newIndex = (+newVal.target.value) - 1
      let oldIndex = oldVal - 1

      let arrayItem = this.actualPhotos.photoItems[oldIndex]

      this.actualPhotos.photoItems.splice(oldIndex, 1)

      this.actualPhotos.photoItems.splice(newIndex, 0, arrayItem)
    },
    toggleDelete (e, index) {
      this.$set(this.photoIndexesToRemove, index, !this.photoIndexesToRemove[index])
    },
    getImageLinkWithQuery (imageLink) {
      return imageLink + '?q=' + this.actualPhotos.dateTimeEdited
    },
    onFileAdded () {
      this.vehicle.isModified = true
    }
  },
  components: {
    'image-lazy': imageLazy,
    'details-photo-section': detailsPhotoSection,
    'gallery': vueGallery,
    'draggable': draggable,
    'card-component': cardComponent,
    'file-upload': dropzone
  }
}
</script>

<style lang="scss">
  @import '../../../../vendor/libs/vue-gallery/vue-gallery.scss';

  .photo_section .view-toggle {
    justify-content: center;
  }

  .photo_section .view-toggle__toggler {
    text-transform: uppercase;
    color: #C90F17;
    margin: 0 auto;
  }

  .photo_section .view-toggle__toggler:hover,
  .photo_section .view-toggle__toggler:focus,
  .photo_section .view-toggle__toggler:active {
    color: #af0d14;
    text-decoration: underline;
    border-color: none;
    box-shadow: none;
  }

  .photo_section .photo-fit-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%;
    cursor: pointer;
  }

  .photo_section .photo-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .sortable-ghost {
    opacity: 0.2;
  }

  .photo_section .draggable-overflow {
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex-wrap: wrap;
    align-items: center;
    color: #fff;
    font-size: 0.8rem;
    transition: background-color .3s;
  }

  .photo_section .draggable-overflow>* {
    width: 100%;
    text-align: center;
  }

  .photo_section .off .photo-fit-container:hover .draggable-overflow {
    z-index: 10;
    background-color: rgba(0,0,0, 0.7);
  }

  .photo_section .rearrange-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    input {
      height: 1.5rem;
      width: 1.5rem;
      background-color: #f1f1f2;
      border-radius: 5px;
      border:1px solid #eee;
      box-shadow: none;
      text-align: center;
      color: #4E5155;
    }
  }

  .delete-photo {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .delete-photo__button {
    padding: 0;
    color: #C90F17;
    font-size: 0.7rem;
    &:hover {
      color: #af0d14;
    }
  }
</style>
