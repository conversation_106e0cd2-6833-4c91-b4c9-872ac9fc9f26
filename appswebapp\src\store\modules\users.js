import axios from 'axios'
import bitFlagHelper from '../../shared/common/bitFlagHelper'
import permission from '../../shared/common/permissions'
import applicationTypes from '../../shared/common/applicationTypes'
import accountGroupPermissionTypes from '../../shared/accounts/groups/accountGroupPermissionTypes'
import analyticsConstants from '../../shared/analytics/constants'

const lastAppTypeVisitedKey = 'lastAppTypeVisited'

export default {
  namespaced: true,
  state: {
    informationForUser: null,
    lastVisitedApp: +(localStorage.getItem(lastAppTypeVisitedKey) || applicationTypes.InventoryManagement.Id),
    analyticsInformationForUser: null
  },
  getters: {
    userInfo: state => {
      return state.informationForUser
    },
    analyticsUserInfo: state => state.analyticsInformationForUser,
    lastVisitedApp: state => state.lastVisitedApp
  },
  mutations: {
    setInfoForUser (state, info) {
      state.informationForUser = info.data
    },
    setAnalyticsInfoForUser (state, info) {
      state.analyticsInformationForUser = info.data.model
    },
    clearInfoForUser (state) {
      state.informationForUser = null
      state.analyticsInformationForUser = null
    },
    setLastVisitedApp (state, appTypeId) {
      state.lastVisitedApp = +appTypeId
      localStorage.setItem(lastAppTypeVisitedKey, state.lastVisitedApp)
    },
    updateUserStorage (state, storageData) {
      if (state.informationForUser && state.informationForUser.user) {
        if (!state.informationForUser.user.storage) {
          state.informationForUser.user.storage = {}
        }
        state.informationForUser.user.storage[storageData.key] = storageData.data
      }
    }
  },
  actions: {
    async getInformationForUser ({ commit, state }) {
      const result = await Promise.all([
        axios.get('/api/users/infoforuser'),
        axios.get(`${analyticsConstants.googleAnalyticsDefaultApiEndpointsBasePath}users/infoforuser`)
      ])

      const userResult = result[0]
      const analyticsUserResult = result[1]

      if (userResult.status === 200 && analyticsUserResult.status === 200) {
        commit('setAnalyticsInfoForUser', analyticsUserResult)
        applyUserHelperMethods(userResult.data.user)
        applyUserStorageHelperMethods(userResult.data.user)
        applyAnalyticsUserHelperMethods(userResult.data.user, state)
        commit('setInfoForUser', userResult)
      }
      return userResult.data
    },
    async fillAnalyticsInformationForUser ({ commit }) {
      const result = await axios.get(`${analyticsConstants.googleAnalyticsDefaultApiEndpointsBasePath}users/infoforuser`)
      if (result.status === 200) {
        commit('setAnalyticsInfoForUser', result)
      }
    },
    saveDataInUserStorage ({commit}, userStorageData) {
      commit('updateUserStorage', userStorageData)
      return axios.post('/api/users/userstorage/save', userStorageData)
    }
  }
}

function applyUserHelperMethods (user) {
  applyGroupHelpers(user)
  applyUserPermissionHelper(user)
}

function applyUserStorageHelperMethods (user) {
  user.getDataFromStorage = function (key) {
    if (!key) {
      return null
    }
    if (this.storage) {
      return this.storage[key]
    }

    return null
  }
}

function applyUserPermissionHelper (user) {
  Object.defineProperty(user, 'isEbizDev', {
    get: function () {
      return bitFlagHelper.hasFlag(permission.FullAccess, user.bitPermissions)
    }
  })

  Object.defineProperty(user, 'isEbizAdmin', {
    get: function () {
      return bitFlagHelper.hasFlag(permission.EbizAutosAdmin, user.bitPermissions)
    }
  })

  user.hasPermissions = function (requestedPermissions) {
    if (!requestedPermissions) {
      return true
    }

    if (Array.isArray(requestedPermissions)) {
      return bitFlagHelper.hasFlagIntersect(requestedPermissions, this.bitPermissions)
    }

    return bitFlagHelper.hasFlag(requestedPermissions, this.bitPermissions)
  }

  user.hasViewPermissions = function (requestedAccountId, viewPermissions, fullAccessPermission) {
    if (fullAccessPermission && this.hasPermissions(fullAccessPermission)) {
      return true
    }

    if (!requestedAccountId) {
      return false
    }

    if (this.accountId > 0) {
      return (requestedAccountId === this.accountId || this.canManageAccount(requestedAccountId)) && this.hasPermissions(viewPermissions)
    } else {
      return this.hasPermissions(viewPermissions)
    }
  }

  user.hasWritePermissions = function (requestedAccountId, writePermissions, appType, fullAccessPermission) {
    if (fullAccessPermission && this.hasPermissions(fullAccessPermission)) {
      return true
    }

    if (!requestedAccountId) {
      return false
    }

    if (this.accountId > 0) {
      return (requestedAccountId === this.accountId || this.hasWriteAccountApplicationTypePermission(requestedAccountId, appType, fullAccessPermission)) && this.hasPermissions(writePermissions)
    } else {
      return this.hasPermissions(writePermissions)
    }
  }
}

function applyGroupHelpers (user) {
  Object.defineProperty(user, 'canManageMultipleAccount', {
    get: function () {
      return !this.accountId || Object.keys(this.groupPermissionsByAccountId || []).length > 0
    }
  })

  Object.defineProperty(user, 'canManageLeadsMultipleAccount', {
    get: function () {
      if (!this.accountId || Object.keys(this.groupPermissionsByAccountId || []).length < 1) {
        return false
      }
      let accessibleAccounts = []
      Object.keys(this.groupPermissionsByAccountId).map(x => {
        if (user.canManageAccountApplicationType(x, applicationTypes.AppsLeads.Id)) {
          accessibleAccounts.push(x)
        }
      })
      return accessibleAccounts.length > 0
    }
  })

  Object.defineProperty(user, 'getAllowedAccountIdsToDisplayLeads', {
    get: function () {
      if (!this.accountId || Object.keys(this.groupPermissionsByAccountId || []).length < 1) {
        return []
      }
      let accountIds = []
      Object.keys(this.groupPermissionsByAccountId).map(groupAccountId => {
        if (this.groupPermissionsByAccountId[groupAccountId].some(x => x.application === applicationTypes.AppsLeads.Id && x.hasToIncludeLeadsToLeadsManager)) {
          accountIds.push(parseInt(groupAccountId))
        }
      })
      return accountIds
    }
  })

  user.canManageApplicationType = function (requestedApplicationType, fullAccessPermission) {
    return !this.accountId ||
      (fullAccessPermission && bitFlagHelper.hasFlag(fullAccessPermission, this.bitPermissions)) ||
      Object.values(this.groupPermissionsByAccountId).some(x => x.some(y => y.application === requestedApplicationType))
  }

  user.canManageAccount = function (accountId) {
    return !this.accountId || !accountId || !!this.groupPermissionsByAccountId[accountId] || this.accountId === accountId
  }

  user.canManageAccountApplicationType = function (accountId, appType, fullAccessPermission) {
    return !this.accountId ||
      this.accountId === accountId ||
      (fullAccessPermission && bitFlagHelper.hasFlag(fullAccessPermission, this.bitPermissions)) ||
      (this.groupPermissionsByAccountId[accountId] && this.groupPermissionsByAccountId[accountId].some(x => x.application === appType))
  }

  user.hasWriteAccountApplicationTypePermission = function (accountId, appType, fullAccessPermission) {
    return (this.accountId && this.accountId === accountId) ||
      (fullAccessPermission && bitFlagHelper.hasFlag(fullAccessPermission, this.bitPermissions)) ||
      (
        this.groupPermissionsByAccountId[accountId] &&
        this.groupPermissionsByAccountId[accountId].some(x => x.application === appType && x.permissionType === accountGroupPermissionTypes.FullAccess.Id)
      )
  }
}

function applyAnalyticsUserHelperMethods (user, state) {
  Object.defineProperty(user, 'hasReportGroupsAccess', {
    get: function () {
      return !!(state.analyticsInformationForUser && state.analyticsInformationForUser.accessibleReportGroups && state.analyticsInformationForUser.accessibleReportGroups.length)
    }
  })

  Object.defineProperty(user, 'defaultAnalyticsViewId', {
    get: function () {
      return state.analyticsInformationForUser ? state.analyticsInformationForUser.defaultAnalyticsViewId : null
    }
  })

  Object.defineProperty(user, 'isGa4EnabledForUser', {
    get: function () {
      return state.analyticsInformationForUser ? state.analyticsInformationForUser.isGa4EnabledForUser : false
    }
  })

  user.hasAccessToReportGroup = function (reportGroupId) {
    return !reportGroupId || (state.analyticsInformationForUser && state.analyticsInformationForUser.accessibleReportGroups.some(x => x.id === reportGroupId))
  }
}
