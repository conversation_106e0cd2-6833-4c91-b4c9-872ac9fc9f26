<template>
  <div>
    <page-status-section :page-edit-model="pageEditModel"/>
    <text-html-content-section v-model="pageEditModel.pageSettings.welcomeMessage" />
    <div class="mt-3">
      <div class="border-bottom">
        <b-row>
          <b-col class="m-0"><h6>Blog Settings</h6></b-col>
        </b-row>
      </div>
      <detail-row titlePosition="start" :fixed-payload-width="true">
        <span slot="title">Blog RSS URL:</span>
        <b-form-group
          slot="payload"
          description="<span class='custom-nowrap'>Examples: www.dealershipblog.com/feed/, dealership.wordpress.com/feed/. <br> Note: Wordpress Blogs are highly recommended for this functionality.</span>"
        >
          <b-form-input v-model="pageEditModel.pageSettings.rssSourceUrl"></b-form-input>
        </b-form-group>
      </detail-row>
    </div>
  </div>
</template>

<script>
import pageStatusSection from '../sections/pageStatusSection'
import textHtmlContentSection from '../sections/textHtmlContentSection'
import detailRow from '@/components/details/helpers/detailRow'

export default {
  props: {
    pageEditModel: {type: Object, required: true}
  },
  components: {
    pageStatusSection,
    textHtmlContentSection,
    detailRow
  },
  methods: {
    refreshBlog () {
    }
  }
}
</script>
