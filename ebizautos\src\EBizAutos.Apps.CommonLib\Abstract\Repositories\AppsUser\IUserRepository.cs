﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EBizAutos.Apps.CommonLib.Models.AppsUser;

namespace EBizAutos.Apps.CommonLib.Abstract.Repositories.AppsUser {
	public interface IUserRepository {
		User GetUser(string id);
		User GetUser(string userName, string password, string email = "");
		User GetUserByContactId(int contactId);
		List<User> GetUsersByIds(List<string> ids);
		Task<User> GetUserAsync(string id);
		Task<User> GetUserAsync(string userName, string password, string email = "");
		Task<bool> IsUserNameReservedAsync(string userName);
		List<User> GetUsersByUserName(string userName);
		Task<List<User>> GetUsersBySearchPhraseAsync(string searchPhrase);
		Task<User> GetUserWithSessionByUserIdAndSessionIdAsync(string authToken, string userId);
		void AddUserSession(string userId, UserSession userSessionInfo);
		void CleanupUserSessions(string userId, int allowedInactivityInDays);
		void UpdateUserSession(string authToken, bool isUserOnline, DateTime? lastActivityDate, DateTime? closedDateTime);
		Task UpdateUserSessionAsync(string authToken, bool isUserOnline, DateTime? lastActivityDate, DateTime? closedDateTime);
		Task<List<User>> GetUsersByAccountIdsAsync(int accountId);
		Task InsertUserAsync(User user);
		void InsertUser(User user);
		Task UpdateUserAsync(User user);
		Task UpdateUserDisplaySettingsAsync(string userId, UserDisplaySettings displaySettings);
		void UpdateUser(User user);
		void UpdateUsersInactiveStatus(int accountId, bool isInactive);
		Task UpdateUsersInactiveStatusAsync(int accountId, bool isInactive);
		Task DeleteUserAsync(string id);
		Task CleanupUserRoleAsync(int userRole);
		void DeleteUser(string id);
		Task DeleteUsersByAccountIdAsync(int accountId);
		void DeleteUsers(int accountId);
	}
}