<template>
   <details-section :mode="mode" title="Status" v-model="mode" :visible="isDisplayed" @visibilityChange='onVisibilityChange' @cancel="onCancel">
    <div class="view" v-if="mode === 'view'">
      <auto-detail-row title="Vehicle Status" :text="getVehicleStatusDescription"/>

      <auto-detail-row title="Days in Stock" :text="[`${getDaysInStock} Days (${getDateInStock})`,isIncomingInventory ? 'Incoming Inventory' : 'In Stock']"/>

      <auto-detail-row title="Import Source" :text="`${getImportSource} (${getDateInStock})`"/>

      <auto-detail-row title="Sales Contact" :text="`${getSalesContact.firstName} ${getSalesContact.lastName}`"/>

      <auto-detail-row title="Status Code" :text="getStatusCode"/>

    </div>
    <div class="edit" v-else-if="mode === 'edit'">
      <auto-detail-row title="Vehicle Status"  v-model="vehicle.vehicleStatus" :options="getVehicleStatusOptionsForEdit"/>

      <detail-row fixedPayloadWidth editMode>
        <span slot="title">Days in Stock:</span>
        <div slot="payload" class="d-flex w-100">
          <b-input-group class="flex-nowrap flex-inline-sized status-dp">
            <b-input-group-prepend is-text>
              <i class="ion ion-md-calendar" slot="prepend"></i>
            </b-input-group-prepend>
            <datepicker v-model="vehicle.miscellaneousVehicleDetails.inStockDate" format="MM/dd/yyyy" input-class="form-control"></datepicker>
          </b-input-group>

          <b-form-checkbox class="flex-inline-sized status-incoming-inv ml-3" :checked="isIncomingInventory">
            Display As Incoming
          </b-form-checkbox>
        </div>
      </detail-row>

      <auto-detail-row title="Import Source" :text="`${getImportSource} (${getDateInStock})`"/>

      <auto-detail-row title="Sales Contact" v-model="vehicle.miscellaneousVehicleDetails.contactId" :options="getSalesContactOptions"/>

      <auto-detail-row title="Status Code" v-model="vehicle.miscellaneousVehicleDetails.importStatusCode" validation-rule="required|between:0,255"/>

    </div>
  </details-section>
</template>

<script>
import { mapGetters } from 'vuex'
import detailRow from '../helpers/detailRow'
import splitHelper from '../helpers/spliterHelper'
import detailsSection from '@/components/details/detailsSection'
import vehicleStatuses from '@/shared/common/vehicle/vehicleStatuses'
import moment from 'moment'

import datepicker from 'vuejs-datepicker'
import autoDetailRow from '../helpers/autoDetailRow'

export default {
  name: 'status-section',
  data () {
    return {
      mode: 'view',
      isDisplayed: true
    }
  },
  methods: {
    onVisibilityChange (val) {
      this.isDisplayed = val
    },
    onCancel () {
      this.$forceUpdate()
    }
  },
  computed: {
    ...mapGetters('details', ['vehicle', 'metadata']),
    getVehicleStatusDescription () {
      for (let key in vehicleStatuses) {
        if (vehicleStatuses[key].value === this.vehicle.vehicleStatus) {
          return vehicleStatuses[key].title
        }
      }
      return 'Unknown'
    },
    getDaysInStock () {
      let daysInStock = Date.daysBetween(new Date(Date.now()), new Date(this.getDateInStock))
      return daysInStock
    },
    getImportSource () {
      return this.vehicle.miscellaneousVehicleDetails.importSource
    },
    getDateInStock () {
      return moment(this.vehicle.miscellaneousVehicleDetails.inStockDate).format('MM/DD/YYYY')
    },
    getSalesContact () {
      let res = this.metadata.contactOptions.find(x => x.contactId === this.vehicle.miscellaneousVehicleDetails.contactId)
      if (res) {
        return res
      }

      return {
        'firstName': 'Undefined',
        'lastName': ''
      }
    },
    getStatusCode () {
      return this.vehicle.miscellaneousVehicleDetails.importStatusCode
    },
    isIncomingInventory () {
      if (typeof this.vehicle.miscellaneousVehicleDetails.inStockDate === 'string') {
        return new Date(this.vehicle.miscellaneousVehicleDetails.inStockDate) > Date.now()
      }
      return this.vehicle.miscellaneousVehicleDetails.inStockDate > Date.now()
    },

    // -------------------- EDIT --------------------
    getVehicleStatusOptions () {
      return Object.getOwnPropertyNames(vehicleStatuses).map(x => ({
        value: vehicleStatuses[x].value,
        text: vehicleStatuses[x].title
      }))
    },
    getVehicleStatusOptionsForEdit () {
      return Object.getOwnPropertyNames(vehicleStatuses).reduce(function (result, x) {
        if (vehicleStatuses[x].title !== 'Archive') {
          result.push(
            {
              value: vehicleStatuses[x].value,
              text: vehicleStatuses[x].title
            })
        }
        return result
      }, [])
    },
    getSalesContactOptions () {
      return this.metadata.contactOptions.map(x => ({
        value: x.contactId,
        text: `${x.firstName} ${x.lastName}`
      }))
    }
  },
  components: {
    'details-section': detailsSection,
    'datepicker': datepicker,
    'detail-row': detailRow,
    'split-helper': splitHelper,
    'auto-detail-row': autoDetailRow
  }
}
</script>

<style scoped>
  .flex-inline-sized {
    flex: 1
  }
  .status-dp {
    min-width: 150px;
    -ms-flex: 60;
  }
  .status-incoming-inv {
    white-space: nowrap;
    align-self: center;
  }
</style>
