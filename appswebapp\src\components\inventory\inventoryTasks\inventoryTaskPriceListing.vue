<template>
  <div>
    <b-table
    class="products-table card-table"
    :fields="tableFields"
    :items="items"
    :striped="true"
    :bordered="false"
    :no-sort-reset="true"
    :no-local-sorting="true"
    :sort-by.sync="sortBy"
    :sort-desc.sync="sortDesc"
    @sort-changed="onSortChanged"
    responsive
    >
      <template #cell(title)="data">
        <div class="media align-items-center">
          <router-link class="media-body text-dark d-flex align-items-center" :to="{ path: getDetailsPath(data.item) }">
            <img class="d-block float-left ui-w-50 mr-3" style="min-height: 1px" :src="getVehiclePhotoSrc(data.item)">
            <span>{{data.item | getVehicleTitle}}</span>
          </router-link>
        </div>
      </template>

      <template #cell(highPrice)="{ item, value }">
        <span v-if="!item.editing">
            {{ getPriceFormat(value) }}
          </span>
          <b-input type="number" size="sm" v-else v-model="item.temp['highPrice']" @keydown.enter.exact="doSave(item)"></b-input>
      </template>
      <template #cell(lowPrice)="{ item, value }">
        <span v-if="!item.editing">
            {{ getPriceFormat(value) }}
          </span>
          <b-input type="number" size="sm" v-else v-model="item.temp['lowPrice']" @keydown.enter.exact="doSave(item)"></b-input>
      </template>
      <template #cell(actions)="{ item }">
        <b-btn v-if="!item.editing" @click="doEdit(item)" size='sm' variant="secondary">
          Add Pricing
        </b-btn>
        <b-btn v-if="item.editing" @click="doSave(item)" size='sm' variant="primary">
          Save
        </b-btn>
        <b-btn v-if="item.editing" @click="doCancel(item)" size='sm' variant="secondary">
          Cancel
        </b-btn>
      </template>
    </b-table>
  </div>
</template>

<script>
import numeral from 'numeral'
import vehicleManagementService from '@/services/inventory/VehicleManagementService'

export default {
  name: 'inventory-task-price-listing',
  props: {
    sortField: String,
    sortOrderDesc: Boolean,
    items: Array
  },
  data () {
    return {

    }
  },
  filters: {
    getVehicleTitle: function (value) {
      if (!value) return ''
      let title = ''
      if (value.year > 0) {
        title = value.year.toString()
      }

      title = [title, value.make, value.model, value.trim].filter(v => v && v !== '').join(' ')

      return title.trim()
    }
  },
  computed: {
    tableFields () {
      return [
        {
          key: 'title',
          label: 'Vehicle',
          tdClass: 'py-2 align-middle',
          sortable: true
        },
        {
          key: 'stockNumber',
          label: 'Stock #',
          tdClass: 'py-2 align-middle',
          sortable: true
        },
        {
          key: 'age',
          label: 'Age',
          tdClass: 'py-2 align-middle',
          sortable: true
        },
        {
          key: 'highPrice',
          label: 'High Price',
          tdClass: 'py-2 align-middle',
          sortable: true
        },
        {
          key: 'lowPrice',
          label: 'Low Price',
          tdClass: 'py-2 align-middle',
          sortable: true
        },
        {
          key: 'actions',
          label: 'Actions',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    sortBy: {
      get () {
        return this.sortField
      },
      set () {}
    },
    sortDesc: {
      get () {
        return this.sortOrderDesc
      },
      set () {}
    }
  },
  methods: {
    onSortChanged (value) {
      this.$emit('sortChanged', value)
    },
    getVehiclePhotoSrc (item) {
      return (item.presentationPhoto || {}).photo107 + '?q=' + item.actualPhotosDateTimeEdited
    },
    getDetailsPath (item, hasToAppendTab) {
      return `/inventory/${item.accountId}/edit/${item.vin}?tabindex=1`
    },
    doEdit (item) {
      this.$set(item, 'temp', JSON.parse(JSON.stringify(item)))
      this.$set(item, 'editing', true)
    },
    doSave (item) {
      this.$set(item, 'editing', false)
      for (let key in item.temp) {
        if (item[key] !== item.temp[key]) {
          item[key] = item.temp[key]
        }
      }
      this.updateVehiclePrice(item)
    },
    doCancel (item) {
      this.$set(item, 'editing', false)
      this.$delete(item, 'temp')
    },
    getPriceFormat (value) {
      return numeral(value).format('$0,0')
    },
    updateVehiclePrice (item) {
      let postModel = {
        vin: item.vin,
        pricing: {
          highPrice: item.highPrice,
          lowPrice: item.lowPrice
        }
      }
      vehicleManagementService.updateVehicle(item.accountId, postModel).then(result => {
        this.$router.go()
      }).catch(ex => {
        this.$toaster.error(`Can't update Vehicle Prices for accountId: ${item.accountId}, vin:${item.vin}. Error: ${ex.message}`)
        this.$logger.handleError(ex, `Can't update Vehicle Prices for accountId: ${item.accountId}, vin:${item.vin}.`)
      })
    }
  }
}
</script>
