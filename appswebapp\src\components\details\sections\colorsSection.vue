<template>
  <details-section title="Colors" v-model="mode" @cancel="onCancel" :visible="isDisplayed" @visibilityChange='onVisibilityChange'>
    <div class="view" v-if="mode === 'view'">

      <detail-row>
        <span slot="title">{{getTitlesByVehicleType.firstColorTitle}}:</span>
        <span slot="payload" style="white-space: nowrap;">
          <span class="ui-product-color align-middle" v-bind:style="{ 'background-color': '#' + getExteriorColorOption.hexCode }"></span>
          <span class="align-middle ml-1">{{getExteriorColorOption.text}}</span>
        </span>
      </detail-row>

      <detail-row v-if="hasSecondaryColor">
        <span slot="title">{{getTitlesByVehicleType.secondColorTitle}}:</span>
        <span slot="payload" style="white-space: nowrap;">
          <span class="ui-product-color align-middle" v-bind:style="{ 'background-color': '#' + getInteriorColorOption.hexCode }"></span>
          <span class="align-middle ml-1">{{getInteriorColorOption.text}}</span>
        </span>
      </detail-row>

      <detail-row v-if="hasInteriorSurface">
        <span slot="title">Interior Surface:</span>
        <span slot="payload">{{getInteriorSurfaceDesc}}</span>
      </detail-row>

    </div>
    <div class="edit" v-else-if="mode === 'edit'">
      <ValidationProvider immediate rules="min:0|xml" :name="getTitlesByVehicleType.firstColorTitle" v-slot="{errors}">
      <detail-row mobileWrap :error="errors[0]">
        <span slot="title">{{getTitlesByVehicleType.firstColorTitle}}:</span>
        <color-picker slot="payload"
            v-model="getExteriorColorOption.text"
            :colorValue="getExteriorColorOption"
            :options="getExteriorColorOptions"
            :name="getTitlesByVehicleType.firstColorTitle"
            @colorNameChanged="updateExtColorName"
            @colorChanged="updateExtColorInfo">
        </color-picker>
      </detail-row>
      </ValidationProvider>

      <ValidationProvider immediate rules="min:0|xml" :name="getTitlesByVehicleType.secondColorTitle" v-slot="{errors}">
      <detail-row editMode mobileWrap v-if="hasSecondaryColor" :error='errors[0]'>
        <span slot="title">{{getTitlesByVehicleType.secondColorTitle}}:</span>
        <color-picker slot="payload"
            v-model="getInteriorColorOption.text"
            :colorValue="getInteriorColorOption"
            :options="getInteriorColorOptions"
            :name="getTitlesByVehicleType.secondColorTitle"
            @colorNameChanged="updateIntColorName"
            @colorChanged="updateIntColorInfo">
        </color-picker>
      </detail-row>
      </ValidationProvider>

      <detail-row fixedPayloadWidth editMode v-if="hasInteriorSurface">
        <span slot="title">Interior Surface:</span>
        <b-form-select slot="payload" v-model="vehicle.colors.interiorSurface" :options="this.metadata.interiorSurfaceOptions"></b-form-select>
      </detail-row>

    </div>

  </details-section>
</template>

<script>
import { mapGetters } from 'vuex'
import detailRow from '../helpers/detailRow'
import colorPicker from '../helpers/colorPicker'
import detailsSection from '@/components/details/detailsSection'
import vehicleTypes from '../../../shared/common/vehicle/vehicleTypes'

export default {
  name: 'colors-section',
  data () {
    return {
      mode: 'view',
      isDisplayed: true
    }
  },
  computed: {
    ...mapGetters('details', ['vehicle', 'metadata']),
    ...mapGetters('categoryData', ['exteriorColors', 'interiorColors']),
    hasSecondaryColor () {
      return this.vehicle.colors.intBasicColorId !== undefined
    },
    hasInteriorSurface () {
      return this.vehicle.colors.interiorSurface !== undefined
    },
    getTitlesByVehicleType () {
      switch (this.vehicle.vehicleType) {
        case vehicleTypes.Passenger.value:
        case vehicleTypes.Truck.value:
        case vehicleTypes.Rv.value:
        case vehicleTypes.Bus.value:
          return {
            firstColorTitle: 'Exterior Color',
            secondColorTitle: 'Interior Color'
          }
        default:
          return {
            firstColorTitle: 'Primary Color',
            secondColorTitle: 'Secondary Color'
          }
      }
    },
    getExteriorColorOption () {
      let colorId = this.vehicle.colors.extBasicColorId

      let option = this.getExteriorColorOptions.standardColors.find(x => x.colorId === colorId) || {}

      let hex = this.vehicle.colors.extColorHex || option.hexCode
      let name = this.vehicle.colors.extColorName || option.text

      return {
        hexCode: hex,
        text: name,
        colorId: colorId,
        value: name
      }
    },
    getInteriorColorOption () {
      let colorId = this.vehicle.colors.intBasicColorId

      let option = this.getInteriorColorOptions.standardColors.find(x => x.colorId === colorId) || {}

      let hex = this.vehicle.colors.intColorHex || option.hexCode
      let name = this.vehicle.colors.intColorName || option.text

      return {
        hexCode: hex,
        text: name,
        colorId: colorId,
        value: name
      }
    },
    getInteriorSurfaceDesc () {
      return this.vehicle.colors.interiorSurface || '—'
    },
    getExteriorColorOptions () {
      let options = {
        styleColors: [],
        standardColors: []
      }
      let colors = this.metadata.standardExteriorColorOptions
      let extColors = this.exteriorColors

      if (extColors && this.exteriorColors.length > 0) {
        extColors.forEach(x => {
          options.styleColors.push({
            hexCode: x.hexCode,
            text: x.name,
            colorId: x.basicColorId,
            value: x.name
          })
        })
      }

      colors.forEach(x => {
        options.standardColors.push({
          hexCode: x.hexCode,
          text: x.name,
          colorId: x.basicColorId,
          value: x.name
        })
      })

      return options
    },
    getInteriorColorOptions () {
      let options = {
        styleColors: [],
        standardColors: []
      }
      let colors = this.metadata.standardInteriorColorOptions
      let intColors = this.interiorColors

      if (this.interiorColors && this.interiorColors.length > 0) {
        intColors.forEach(x => {
          options.styleColors.push({
            hexCode: x.hexCode,
            text: x.name,
            colorId: x.basicColorId,
            value: x.name
          })
        })
      }

      colors.forEach(x => {
        options.standardColors.push({
          hexCode: x.hexCode,
          text: x.name,
          colorId: x.basicColorId,
          value: x.name
        })
      })

      return options
    }
  },
  methods: {
    onVisibilityChange (val) {
      this.isDisplayed = val
    },
    updateExtColorInfo (val) {
      this.vehicle.colors.extColorHex = val.hexCode
      this.vehicle.colors.extColorName = val.text
      this.vehicle.colors.extBasicColorId = val.colorId
    },
    updateExtColorName (val) {
      this.vehicle.colors.extColorName = val
    },
    updateIntColorInfo (val) {
      this.vehicle.colors.intColorHex = val.hexCode
      this.vehicle.colors.intColorName = val.text
      this.vehicle.colors.intBasicColorId = val.colorId
    },
    updateIntColorName (val) {
      this.vehicle.colors.intColorName = val
    },
    onCancel () {
      this.$forceUpdate()
    }
  },
  components: {
    'details-section': detailsSection,
    'color-picker': colorPicker,
    'detail-row': detailRow
  }
}
</script>
