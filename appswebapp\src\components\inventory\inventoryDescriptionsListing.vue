<template>
  <div>

    <b-modal v-model="showModal" title="Archive vehicle" lazy @ok="handleArchive">
      <p class="confirmation-message">
        {{modalMessage}}
      </p>
    </b-modal>

    <b-table
    class="products-table card-table"
    :items="inventoryData.vehicles"
    :current-page="inventoryData.pageNumber"
    :per-page="inventoryData.pageSize"
    :sort-by.sync="sortBy"
    :sort-desc.sync="sortDesc"
    @sort-changed="onSortChanged"
    :fields="fields"
    :striped="true"
    :bordered="false"
    :no-sort-reset="true"
    :no-local-sorting="true"
    responsive
    >
      <template #cell(title)="data">
        <div class="media align-items-center">
            <router-link class="media-body text-dark d-flex align-items-center" :to="{ path: getDetailsPath(data.item) }">
              <img class="d-block float-left ui-w-50 mr-3" style="min-height: 1px" :src="getVehiclePhotoSrc(data.item)">
              <span>{{data.item | getVehicleTitle}}</span>
            </router-link>
        </div>
      </template>

    <template #cell(actions)="data">
      <template>
        <b-dropdown variant="outline-secondary icon-btn btn-round" size="sm" right no-caret boundary='viewport'>
          <template slot="button-content">
            <i class="ion ion-ios-more m-0"></i><span class="sr-only">Actions</span>
          </template>
          <b-dropdown-item :href="`${siteSettings.siteDefaultUrl}${data.item.links.vehicleDetailsLink}`" target="_blank">View VDP</b-dropdown-item>
          <write-permission-wrapper variant="hidden">
            <div class="dropdown-toggle no-caret">
              <a class="dropdown-item" style="text-decoration: none;">Change Status</a>
              <div class="dropdown-menu">
                <b-dropdown-item v-for="(value, key) in getAvailableChangeStatuses(
                data.item.vehicleStatus,
                data.item.actualPhotosCount,
                data.item.make,
                data.item.model,
                data.item.year)" :key="key" v-on:click="changeStatus(data.item, value.value)">
                  {{value.title}}
                </b-dropdown-item>
              </div>
            </div>
          </write-permission-wrapper>
          <b-dropdown-item :to="{ path: getDetailsPath(data.item) }">Edit Description</b-dropdown-item>
          <b-dropdown-item v-can="permission.IMFullAccess" @click="syncVehicle(data.item, data.item.accountId, data.item.vin)">Sync Vehicle</b-dropdown-item>
        </b-dropdown>
      </template>
    </template>

    </b-table>
  </div>
</template>

<script>

import vehicleStatuses from '@/shared/common/vehicle/vehicleStatuses'
import vehicleManagementService from '@/services/inventory/VehicleManagementService'
import permission from '@/shared/common/permissions'
import syncService from '@/services/inventory/SyncService'
import numeral from 'numeral'
import writePermissionWrapper from '../_shared/writePermissionWrapper'

export default {
  props: {
    inventoryData: {
      type: Object,
      default: function () {
        return {
          vehicles: [],
          pageNumber: 1,
          pageSize: 25
        }
      }
    },
    siteSettings: {
      type: Object,
      required: true
    },
    sortField: String,
    sortOrderDesc: Boolean
  },
  data () {
    return {
      showModal: false,
      itemToArchive: null,
      permission,
      fields: [{
        key: 'title',
        label: 'Vehicle',
        sortable: true,
        tdClass: 'py-2 align-middle',
        thStyle: 'min-width: 300px'
      }, {
        key: 'stockNumber',
        label: 'Stock #',
        sortable: true,
        tdClass: 'py-2 align-middle'
      }, {
        key: 'age',
        label: 'Age',
        sortable: true,
        tdClass: 'py-2 align-middle'
      }, {
        key: 'actualPhotosCount',
        sortable: true,
        tdClass: 'py-2 align-middle'
      }, {
        key: 'lowPrice',
        label: 'Price',
        sortable: true,
        tdClass: 'py-2 align-middle',
        formatter: value => numeral(value).format('$0,0')
      }, {
        key: 'actions',
        label: 'Actions',
        sortable: false,
        tdClass: 'py-2 align-middle text-nowrap'
      }]
    }
  },
  methods: {
    onSortChanged (value) {
      this.$emit('sortChanged', value)
    },
    getAvailableChangeStatuses (status, photos, make, model, year) {
      let availableStatuses = []
      if (status !== vehicleStatuses.inProgress.value && year) {
        availableStatuses.push({
          ...vehicleStatuses.inProgress,
          title: 'Move To In Progress'
        })
      }

      if (status !== vehicleStatuses.archive.value) {
        availableStatuses.push({
          ...vehicleStatuses.archive,
          title: 'Archive Vehicle'
        })
      }

      if (status !== vehicleStatuses.sold.value && (status === vehicleStatuses.inProgress.value || status === vehicleStatuses.live.value)) {
        availableStatuses.push({
          ...vehicleStatuses.sold,
          title: 'Sell Vehicle'
        })
      }

      if (status !== vehicleStatuses.live.value && (photos && make && model && year)) {
        availableStatuses.push({
          ...vehicleStatuses.live,
          title: 'Save To Live'
        })
      }

      return availableStatuses
    },
    changeStatus (item, status) {
      this.$set(item, '_rowVariant', 'info')

      const updateData = {
        Vin: item.vin,
        VehicleStatus: status
      }
      if (status === 0) {
        this.itemToArchive = item
        this.showModal = true
        return
      }

      return vehicleManagementService.updateVehicle(item.accountId, updateData)
        .then(() => {
          this.$set(item, '_rowVariant', 'success')
          this.$emit('listingChanged', '')
        }).catch(reason => {
          this.$set(item, '_rowVariant', 'danger')
          this.$logger.handleError(reason, 'Can\'t update vehicle status', updateData)
        })
    },
    handleArchive () {
      this.showModal = false
      const updateData = {
        Vin: this.itemToArchive.vin,
        VehicleStatus: 'Archive'
      }

      return vehicleManagementService.deleteVehicle(this.itemToArchive.accountId, this.itemToArchive.vin)
        .then(() => {
          this.$toaster.success('Vehicle ' + this.itemToArchive.stockNumber + ' archived')
          this.$set(this.itemToArchive, '_rowVariant', 'success')
          this.$emit('listingChanged', '')
        }).catch(reason => {
          this.$toaster.error('Something went wrong')
          this.$set(this.itemToArchive, '_rowVariant', 'danger')
          this.$logger.handleError(reason, 'Can\'t update vehicle status', updateData)
        })
    },
    syncVehicle (item, accountId, vin) {
      this.$set(item, '_rowVariant', 'info')

      syncService.synchronizeAccountVehicle(accountId, vin)
        .then(() => {
          this.$set(item, '_rowVariant', 'success')
        }).catch(reason => {
          this.$set(item, '_rowVariant', 'danger')
          this.$logger.handleError(reason, 'Can\'t sync account vehicle')
        })
    },
    getVehiclePhotoSrc (item) {
      return (item.presentationPhoto || {}).photo107 + '?q=' + item.actualPhotosDateTimeEdited
    },
    getDetailsPath (item) {
      return `/inventory/${item.accountId}/edit/${item.vin}/`
    }
  },
  filters: {
    getVehicleTitle: function (value) {
      if (!value) return ''
      let title = ''
      if (value.year > 0) {
        title = value.year.toString()
      }

      title = [title, value.make, value.model, value.trim].filter(v => v && v !== '').join(' ')

      return title.trim()
    }
  },
  computed: {
    modalMessage () {
      if (this.itemToArchive) {
        return 'Are you sure you want to Archive vehicle ' + this.itemToArchive.stockNumber + '?'
      }
    },
    sortBy: {
      get () {
        return this.sortField
      },
      set () {}
    },
    sortDesc: {
      get () {
        return this.sortOrderDesc
      },
      set () {}
    }
  },
  components: {
    'write-permission-wrapper': writePermissionWrapper
  }
}
</script>

<style>

  .confirmation-message{
    font-weight: 600;
    margin: 0;
  }
</style>
