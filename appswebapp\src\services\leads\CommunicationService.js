import BaseService from '@/services/BaseService'

class CommunicationService extends BaseService {
  getCommunicationPrototype (accountId, type) {
    return this.axios.get(`/api/leads/${accountId}/communications/new?communicationtype=${type}`)
  };
  getCommunicationSettings (accountId, communicationId) {
    return this.axios.get(`/api/leads/${accountId}/communications/${communicationId}`)
  };
  getPhoneInfo (phoneNumber) {
    return this.axios.get(`/api/leads/communications/phone_info?number=${phoneNumber}`)
  };
  getAvailableContacts (accountId, filter) {
    return this.axios.get(`/api/leads/${accountId}/communications/available_contacts`, { params: filter })
  };
  getAvailableDepartmentTypes (accountId, filter) {
    return this.axios.get(`/api/leads/${accountId}/communications/available_department_types`, { params: filter })
  };
  getLegacyCampaignIds (campaignIds) {
    return this.axios.get(`/api/leads/campaign_types/legacy_campaign_ids?campaigntypeids=${campaignIds}`)
  };
  getAvailableCampaigns (campaignType, filter) {
    return this.axios.get(`/api/leads/campaign_types/${campaignType}/listing`, { params: filter })
  }
  createNewCommunication (accountId, data) {
    return this.axios.post(`/api/leads/${accountId}/communications`, data)
  };
  updateCommunication (accountId, data) {
    return this.axios.post(`/api/leads/${accountId}/communications/${data.communicationId}/update`, data)
  };
  deleteCommunication (accountId, communicationId) {
    return this.axios.post(`/api/leads/${accountId}/communications/${communicationId}/delete`)
  };
}

export default new CommunicationService()
