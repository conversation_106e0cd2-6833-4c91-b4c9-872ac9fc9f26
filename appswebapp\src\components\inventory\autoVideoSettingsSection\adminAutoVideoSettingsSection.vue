<template>
  <div v-if="isLoaded">
    <introSection
      ref="intro"
      :settings="originalSettings.introSettings"
      v-model="updateSettings.introSettings"
      :isUpdatingProcessed="sections.intro.isUpdatingProcessed"
      :isDisabled="sections.intro.isDisabled"
      @saveChanges="updateIntroSettings"
    />
    <vehicleHistorySection
      ref="vehicleHistory"
      :settings="originalSettings.vehicleHistorySettings"
      v-model="updateSettings.vehicleHistorySettings"
      :isUpdatingProcessed="sections.vehicleHistory.isUpdatingProcessed"
      :isDisabled="sections.vehicleHistory.isDisabled"
      @saveChanges="updateVehicleHistorySettings"/>
    <vehicleMileageSection
      ref="vehicleMileage"
      :settings="originalSettings.mileageSettings"
      v-model="updateSettings.mileageSettings"
      :isUpdatingProcessed="sections.vehicleMileage.isUpdatingProcessed"
      :isDisabled="sections.vehicleMileage.isDisabled"
      @saveChanges="updateVehicleMileageSettings"/>
    <warrantySection
      ref="warranty"
      :settings="originalSettings.warrantySettings"
      v-model="updateSettings.warrantySettings"
      :isUpdatingProcessed="sections.warranty.isUpdatingProcessed"
      :isDisabled="sections.warranty.isDisabled"
      @saveChanges="updateWarrantySettings"/>
    <economySection
      ref="economy"
      :settings="originalSettings.economySettings"
      v-model="updateSettings.economySettings"
      :isUpdatingProcessed="sections.economy.isUpdatingProcessed"
      :isDisabled="sections.economy.isDisabled"
      @saveChanges="updateEconomySettings"/>
    <engineSection
      ref="engine"
      :settings="originalSettings.engineSettings"
      v-model="updateSettings.engineSettings"
      :isUpdatingProcessed="sections.engine.isUpdatingProcessed"
      :isDisabled="sections.engine.isDisabled"
      @saveChanges="updateEngineSettings"/>
    <fuelTypesSection
      ref="fuelTypes"
      :settings="originalSettings.fuelTypeSettings"
      v-model="updateSettings.fuelTypeSettings"
      :isUpdatingProcessed="sections.fuelTypes.isUpdatingProcessed"
      :isDisabled="sections.fuelTypes.isDisabled"
      @saveChanges="updateFuelTypesSettings"/>
    <featuresSection
      ref="features"
      :settings="originalSettings.featureSettings"
      v-model="updateSettings.featureSettings"
      :isUpdatingProcessed="sections.features.isUpdatingProcessed"
      :isDisabled="sections.features.isDisabled"
      @saveChanges="updateFeaturesSettings"/>
    <outroSection
      ref="outro"
      :settings="originalSettings.outroSettings"
      v-model="updateSettings.outroSettings"
      :isUpdatingProcessed="sections.outro.isUpdatingProcessed"
      :isDisabled="sections.outro.isDisabled"
      @saveChanges="updateOutroSettings"/>
    <summarySection
      ref="summary"
      :settings="originalSettings.descriptionTextSections"
      v-model="updateSettings.descriptionTextSections"
      :isUpdatingProcessed="sections.summary.isUpdatingProcessed"
      :isDisabled="sections.summary.isDisabled"
      @saveChanges="updateDescriptionTextSettings"
    />
    <detail-row :extra-large-payload-width="true" :title-position="'start'">
      <span slot="title">Final Example:</span>
      <span slot="payload">{{ exampleText }}</span>
    </detail-row>
  </div>
  <loader v-else size="lg"/>
</template>

<script>
import introSection from './ttsSettingsSections/introSection.vue'
import vehicleHistorySection from './ttsSettingsSections/vehicleHistorySection.vue'
import vehicleMileageSection from './ttsSettingsSections/vehicleMileageSection.vue'
import economySection from './ttsSettingsSections/economySection.vue'
import engineSection from './ttsSettingsSections/engineSection.vue'
import featuresSection from './ttsSettingsSections/featuresSection.vue'
import fuelTypesSection from './ttsSettingsSections/fuelTypesSection.vue'
import outroSection from './ttsSettingsSections/outroSection.vue'
import warrantySection from './ttsSettingsSections/warrantySection.vue'
import summarySection from './ttsSettingsSections/summarySection.vue'
import VideoEncoderService from '../../../services/inventory/VideoEncoderService'
import detailRow from '@/components/details/helpers/detailRow'
import loader from '../../_shared/loader.vue'
import globals from '../../../globals'
import videoEncoderTypes from '@/shared/inventory/videoEncoderTypes'

const descriptionTextSections = videoEncoderTypes.descriptionTextSections

export default {
  name: 'admin-autovideo-settings',
  data () {
    return {
      originalSettings: {},
      updateSettings: {},
      isLoaded: false,
      exampleText: '',
      sections: {
        intro: {isDisabled: false, isUpdatingProcessed: false},
        vehicleHistory: {isDisabled: false, isUpdatingProcessed: false},
        vehicleMileage: {isDisabled: false, isUpdatingProcessed: false},
        warranty: {isDisabled: false, isUpdatingProcessed: false},
        economy: {isDisabled: false, isUpdatingProcessed: false},
        engine: {isDisabled: false, isUpdatingProcessed: false},
        fuelTypes: {isDisabled: false, isUpdatingProcessed: false},
        features: {isDisabled: false, isUpdatingProcessed: false},
        outro: {isDisabled: false, isUpdatingProcessed: false},
        summary: {isDisabled: false, isUpdatingProcessed: false}
      }
    }
  },
  created () {
    this.initData()
  },
  components: {
    introSection,
    vehicleHistorySection,
    vehicleMileageSection,
    economySection,
    engineSection,
    featuresSection,
    fuelTypesSection,
    outroSection,
    warrantySection,
    summarySection,
    detailRow,
    loader
  },
  methods: {
    initData () {
      VideoEncoderService.getPhotoToVideoSettings().then(res => {
        this.originalSettings = res.data.descriptionTextSettings
        this.updateSettings = globals().getClonedValue(this.originalSettings)
      }).catch(ex => {
        this.$toaster.error('Something went wrong!')
      }).finally(() => {
        this.generateExampleText()
        this.isLoaded = true
      })
    },
    saveChanges (sectionKey) {
      VideoEncoderService.updatePhotoToVideoSettings({ descriptionTextSettings: this.updateSettings }).then(res => {
        this.$toaster.success('Updated Successfully')
        this.initData()
        this.$refs[sectionKey].changeMode(true)
      }).catch(ex => {
        this.$toaster.exception(ex, 'Failed on updating.')
      }).finally(() => {
        this.setSectionsToDefault()
      })
    },
    updateIntroSettings (introSettings) {
      this.sections.intro.isUpdatingProcessed = true
      this.makeOtherSectionsDisabled('intro')
      this.$set(this.updateSettings, 'introSettings', introSettings)
      this.saveChanges('intro')
    },
    updateVehicleHistorySettings (vehicleHistorySettings) {
      this.sections.vehicleHistory.isUpdatingProcessed = true
      this.makeOtherSectionsDisabled('vehicleHistory')
      this.$set(this.updateSettings, 'vehicleHistorySettings', vehicleHistorySettings)
      this.saveChanges('vehicleHistory')
    },
    updateVehicleMileageSettings (mileageSettings) {
      this.sections.vehicleMileage.isUpdatingProcessed = true
      this.makeOtherSectionsDisabled('vehicleMileage')
      this.$set(this.updateSettings, 'mileageSettings', mileageSettings)
      this.saveChanges('vehicleMileage')
    },
    updateWarrantySettings (warrantySettings) {
      this.sections.warranty.isUpdatingProcessed = true
      this.makeOtherSectionsDisabled('warranty')
      this.$set(this.updateSettings, 'warrantySettings', warrantySettings)
      this.saveChanges('warranty')
    },
    updateEconomySettings (economySettings) {
      this.sections.economy.isUpdatingProcessed = true
      this.makeOtherSectionsDisabled('economy')
      this.$set(this.updateSettings, 'economySettings', economySettings)
      this.saveChanges('economy')
    },
    updateDescriptionTextSettings (descriptionTextSections) {
      this.sections.summary.isUpdatingProcessed = true
      this.makeOtherSectionsDisabled('summary')
      this.$set(this.updateSettings, 'descriptionTextSections', descriptionTextSections)
      this.saveChanges('summary')
    },
    updateEngineSettings (engineSettings) {
      this.sections.engine.isUpdatingProcessed = true
      this.makeOtherSectionsDisabled('engine')
      this.$set(this.updateSettings, 'engineSettings', engineSettings)
      this.saveChanges('engine')
    },
    updateOutroSettings (outroSettings) {
      this.sections.outro.isUpdatingProcessed = true
      this.makeOtherSectionsDisabled('outro')
      this.$set(this.updateSettings, 'outroSettings', outroSettings)
      this.saveChanges('outro')
    },
    updateFeaturesSettings (featureSettings) {
      this.sections.features.isUpdatingProcessed = true
      this.makeOtherSectionsDisabled('features')
      this.$set(this.updateSettings, 'featureSettings', featureSettings)
      this.saveChanges('features')
    },
    updateFuelTypesSettings (fuelTypeSettings) {
      this.sections.fuelTypes.isUpdatingProcessed = true
      this.makeOtherSectionsDisabled('fuelTypes')
      this.$set(this.updateSettings, 'fuelTypeSettings', fuelTypeSettings)
      this.saveChanges('fuelTypes')
    },
    makeOtherSectionsDisabled (excludeSectionKey) {
      Object.keys(this.sections).filter(key => key !== excludeSectionKey).forEach(key => {
        this.sections[key].isDisabled = true
      })
    },
    setSectionsToDefault () {
      Object.values(this.sections).forEach(section => {
        section.isDisabled = false
        section.isUpdatingProcessed = false
      })
    },
    generateExampleText () {
      let exampleText = this.getIntroText()
      exampleText = exampleText + ' ' + '2019 Audi A4'
      let excludeSections = [descriptionTextSections.intro.value, descriptionTextSections.outro.value]
      let sections = this.updateSettings.descriptionTextSections.orderedSectionItems.filter(x => x.isEnabled && !excludeSections.includes(x.section)).map(x => x.section)
      for (let section of sections) {
        switch (section) {
          case descriptionTextSections.vehicleHistory.value:
            exampleText = this.appendText(exampleText, this.getVehicleHistoryText())
            break
          case descriptionTextSections.mileage.value:
            exampleText = this.appendText(exampleText, this.getVehicleMileageText())
            break
          case descriptionTextSections.warranty.value:
            exampleText = this.appendText(exampleText, this.getWarrantyText())
            break
          case descriptionTextSections.fuelEconomy.value:
            exampleText = this.appendText(exampleText, this.getEconomyText())
            break
          case descriptionTextSections.engine.value:
            exampleText = this.appendText(exampleText, this.getEngineText())
            break
          case descriptionTextSections.fuelType.value:
            exampleText = this.appendText(exampleText, this.getFuelTypeText())
            break
          case descriptionTextSections.features.value:
            exampleText = this.appendText(exampleText, this.getFeaturesText())
            break
        }
      }
      exampleText = this.appendText(exampleText, this.getOutroText())
      this.exampleText = exampleText.trim()
    },
    appendText (text, append) {
      if (append) {
        text = text + '. ' + append
      }
      return text
    },
    getIntroText () {
      return (this.updateSettings.introSettings.introItems.findLast(x => x.isEnabled) || { introText: '' }).introText
    },
    getVehicleHistoryText () {
      return (this.updateSettings.vehicleHistorySettings.carfaxOneOwnerDescriptionText || '').replaceAll('{VehicleModel}', 'A4')
    },
    getVehicleMileageText () {
      let text = this.updateSettings.mileageSettings.mileageTextTemplate || ''
      let replaceOptions = [{ from: '{MileageDescription}', to: '40k miles' }, { from: '{VehicleModel}', to: 'A4' }]
      replaceOptions.forEach(x => {
        text = text.replaceAll(x.from, x.to)
      })
      return text
    },
    getWarrantyText () {
      let warranties = Object.values(this.updateSettings.warrantySettings.warrantyItems)
      let randomIndex = Math.floor(Math.random() * warranties.length)
      return warranties[randomIndex].descriptionText
    },
    getEconomyText () {
      let text = this.updateSettings.economySettings.fuelEconomyTextTemplate || ''
      let replaceOptions = [
        { from: '{CityMileage}', to: '15' },
        { from: '{HighwayMileage}', to: '8' },
        { from: '{RunUnits}', to: 'miles' }
      ]
      replaceOptions.forEach(x => {
        text = text.replaceAll(x.from, x.to)
      })
      return text
    },
    getEngineText () {
      let engineText = this.updateSettings.engineSettings.engineDescriptionTextTemplate || ''
      engineText = engineText.replaceAll('{EngineDescription}', '3.5L Engine').replaceAll('{VehicleModel}', 'A4')
      let transmissionText = this.updateSettings.engineSettings.transmissionDescriptionTextTemplate || ''
      transmissionText = transmissionText.replaceAll('{TransmissionDescription}', 'Automatic Transmission')
      return engineText + ', ' + transmissionText
    },
    getFuelTypeText () {
      let fuelTypes = Object.values(this.updateSettings.fuelTypeSettings.fuelTypeItems)
      let randomIndex = Math.floor(Math.random() * fuelTypes.length)
      return fuelTypes[randomIndex].descriptionText
    },
    getFeaturesText () {
      let text = this.updateSettings.featureSettings.featuresIntro || ''
      let enabledFeatures = this.updateSettings.featureSettings.featureItems.filter(x => x.isEnabled)
      let featuresText = []
      for (let index = 0; index < Math.min(this.updateSettings.featureSettings.keyFeaturesLimitInDescription, enabledFeatures.length); index++) {
        if (enabledFeatures[index].featureCustomText) {
          featuresText.push(enabledFeatures[index].featureCustomText)
        } else {
          featuresText.push(enabledFeatures[index].featureName)
        }
      }
      return text + ' ' + featuresText.join(', ')
    },
    getOutroText () {
      return (this.updateSettings.outroSettings.outroItems.findLast(x => x.isEnabled) || { outroText: '' }).outroText
    }
  },
  watch: {
    'updateSettings': {
      deep: true,
      handler: function () {
        this.generateExampleText()
      }
    }
  }
}
</script>
