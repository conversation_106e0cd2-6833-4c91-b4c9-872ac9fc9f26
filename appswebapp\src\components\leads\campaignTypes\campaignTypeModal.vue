<template>
  <b-modal
  :title="title"
  :visible='isShowModal'
  size='lg'
  @hide='hide'
  >
    <div v-if='model'>
      <ValidationObserver ref="validator">
      <detail-row largePayloadWidth v-if="!model.isAllOtherCampaign">
        <span slot="title">Campaign Type:</span>
        <b-form-select v-model="model.type" slot="payload" :options="campaignTypesOptions" :disabled="model.isEditModal"/>
      </detail-row>
      <ValidationProvider name="Campaign Type Name" rules="required" v-slot="{errors}">
      <detail-row largePayloadWidth :error="errors[0]">
        <span slot="title">Campaign Type Name:</span>
        <b-form-input name='campaign_type_name' v-model="model.campaignName" slot="payload" placeholder="Name new campaign type"></b-form-input>
      </detail-row>
      </ValidationProvider>
      <detail-row largePayloadWidth>
        <span slot="title">Chanel group:</span>
        <b-form-select v-model="model.channelGroupType" slot="payload" :options="gaChannelGroupTypes"></b-form-select>
      </detail-row>
      <ValidationProvider v-if="hasShowSourceField" name="Leads Campaign Source" rules="required" v-slot="{errors}">
      <detail-row largePayloadWidth :error="errors[0]">
        <span slot="title">Source:</span>
        <b-form-input name='leads_campaign_source' v-model="model.source" slot="payload"></b-form-input>
      </detail-row>
      </ValidationProvider>
      <detail-row largePayloadWidth>
        <span slot="title">Legacy campaign Types:</span>
        <multi-input slot="payload" placeholder='ex 42' type='number' v-model='model.legacyCampaignIds' :values='model.legacyCampaignIds'></multi-input>
      </detail-row>
      <div v-if="!model.isAllOtherCampaign">
        <detail-row largePayloadWidth v-if="model.type === campaignTypes.onSite.value">
          <span slot="title">Referrer Hosts:</span>
          <multi-input slot="payload" placeholder='ex: ebizautos.com' v-model='model.hosts' :values='model.hosts'></multi-input>
        </detail-row>
        <detail-row largePayloadWidth v-if="model.type === campaignTypes.onSite.value">
          <span slot="title">Query Params:</span>
          <multi-input  slot="payload" placeholder='ex: key=value' v-model='model.queryParams' :values='model.queryParams'></multi-input>
        </detail-row>
        <detail-row largePayloadWidth v-if="model.type === campaignTypes.offSite.value">
          <span slot="title">Details view types:</span>
          <multi-input slot="payload" :values='model.detailsViewTypes' v-model='model.detailsViewTypes' :options='detailsPageSubTypes'></multi-input>
        </detail-row>
      </div>
      <detail-row largePayloadWidth v-if="model.type === campaignTypes.offSiteParent.value">
        <span slot="title">Account Ids:</span>
        <multi-input slot="payload" placeholder='ex: 42' type='number' v-model='model.accountIds' :values='model.accountIds'></multi-input>
      </detail-row>
      </ValidationObserver>
    </div>
    <template #modal-footer>
      <div class="py-1 d-flex bd-highlight">
        <b-btn class="ml-auto mt-1 bd-highlight" @click="hide">Close</b-btn>
        <b-btn class="ml-1 mt-1 bd-highlight" variant="primary" @click='submit'>Submit</b-btn>
      </div>
    </template>
  </b-modal>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import multiInput from '@/components/_shared/multiInput'
import { campaignTypes, gaChannelGroupTypes, detailsPageSubTypes } from '@/shared/leads/campaignTypes'
import { mapGetters } from 'vuex'

export default {
  name: 'leads-create-edit-campaign-type',
  props: {
    title: { type: String, required: true },
    mode: { type: Number, required: true },
    isShowModal: { type: Boolean, required: true }
  },
  data () {
    return {
      campaignTypes,
      gaChannelGroupTypes,
      detailsPageSubTypes,
      modeOptions: {
        create: { value: 0 },
        edit: { value: 1 },
        move: { value: 2 }
      }
    }
  },
  components: {
    'detail-row': detailRow,
    'multi-input': multiInput
  },
  computed: {
    ...mapGetters('leadsCampaignTypes', ['campaignType']),
    model () {
      return this.campaignType
    },
    hasShowSourceField () {
      if (this.model.type !== campaignTypes.onSite.value) {
        return false
      }

      let res = this.model.chanelGroups.find(x => x.value === this.model.channelGroupType)
      if (res) {
        return res.isSourceRequired
      }

      return false
    },
    hasQueryParamsRequired () {
      if (this.model.type !== campaignTypes.onSite.value) {
        return false
      }

      let res = this.model.chanelGroups.find(x => x.value === this.model.channelGroupType)
      if (res) {
        return res.isQueryParamsRequired
      }

      return false
    },
    hasHostsRequired () {
      if (this.model.type !== campaignTypes.onSite.value) {
        return false
      }

      let res = this.model.chanelGroups.find(x => x.value === this.model.channelGroupType)
      if (res) {
        return res.isHostsRequired
      }

      return false
    },
    campaignTypesOptions () {
      if (this.campaignTypes) {
        return Object.values(this.campaignTypes)
      }

      return []
    }
  },
  methods: {
    hide () {
      this.$emit('hide')
    },
    async submit () {
      let isValid = await this.validate()
      if (isValid) {
        try {
          switch (this.mode) {
            case this.modeOptions.create.value:
              await this.$store.dispatch('leadsCampaignTypes/createNewCampaignType', { data: this.model })
              break
            case this.modeOptions.edit.value:
              await this.$store.dispatch('leadsCampaignTypes/updateCampaignType', { type: this.model.type, id: this.model.campaignTypeId, data: this.model })
              break
            case this.modeOptions.move.value:
              await this.$store.dispatch('leadsCampaignTypes/moveUnmatchedCampaignTypeToOnSite', { id: this.model.campaignTypeId, data: this.model })
              break
          }

          this.$toaster.success('Operation Successfully Completed')
        } catch (ex) {
          if (ex.response) {
            this.$toaster.error(`Something has gone wrong. Message: ${ex.response.data}`, { timeOut: 10000 })
          } else {
            this.$toaster.error(`Something has gone wrong. Message: ${ex.message}`, { timeOut: 10000 })
          }
          this.$logger.handleError(ex, `Something wen wrong`, this.model)
        } finally {
          this.$emit('hide')
        }
      }
    },
    async validate () {
      if (this.model.legacyCampaignIds.length === 0) {
        this.$toaster.error('Legacy Campaign Types cannot be empty')
        return false
      }
      if (this.hasHostsRequired && this.model.hosts.length === 0) {
        this.$toaster.error('Referrer Hosts field cannot be empty for this chanel group')
        return false
      }
      if (this.hasQueryParamsRequired && this.model.queryParams.length === 0) {
        this.$toaster.error('Query Params field cannot be empty for this chanel group')
        return false
      }

      let res = await this.$refs.validator.validate()

      return res
    }
  }
}
</script>
