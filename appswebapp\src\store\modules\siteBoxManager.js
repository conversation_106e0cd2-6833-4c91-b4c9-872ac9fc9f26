import axios from 'axios'

const actions = {
  getSiteHostings (_, parameters) {
    return axios.get('/api/siteboxmanager/sitehostings', { params: parameters.filter })
  },
  getSiteBoxes (_) {
    return axios.get('/api/siteboxmanager/siteboxes')
  },
  getConnections (_) {
    return axios.get('/api/siteboxmanager/connections')
  },
  getSiteBoxSiteListing (_, parameters) {
    return axios.get(`/api/siteboxmanager/siteboxes/${parameters.siteBoxId}/sites`, { params: parameters.filter })
  },
  getSiteHostingMovingTaskStatus (_, parameters) {
    return axios.get(`/api/siteboxmanager/sitehostings/move/${parameters.taskId}/status`)
  },
  getLogs (_, parameters) {
    return axios.get(`/api/siteboxmanager/useractivity`, { params: parameters.filter })
  },
  getLogDetails (_, parameters) {
    return axios.get(`/api/siteboxmanager/useractivity/${parameters.id}/details`)
  },
  startMovingSiteHosting (_, parameters) {
    return axios.post('/api/siteboxmanager/sitehostings/move/start', parameters.data)
  },
  finishMovingSiteHosting (_, parameters) {
    return axios.post(`/api/siteboxmanager/sitehostings/move/${parameters.siteId}/finish`)
  },
  createSiteBox (_, parameters) {
    return axios.post('/api/siteboxmanager/siteboxes/create', parameters.data)
  },
  updateSiteBox (_, parameters) {
    return axios.post('/api/siteboxmanager/siteboxes/update', parameters.data)
  },
  deleteSiteBox (_, id) {
    return axios.post(`/api/siteboxmanager/siteboxes/${id}/delete`)
  },
  createConnection (_, parameters) {
    return axios.post('/api/siteboxmanager/connections/create', parameters.data)
  },
  updateConnection (_, parameters) {
    return axios.post(`/api/siteboxmanager/connections/${parameters.id}/update`, parameters.data)
  },
  deleteConnection (_, parameters) {
    return axios.post(`/api/siteboxmanager/connections/${parameters.id}/delete`)
  },
  fetcherCheckStatus (_, parameters) {
    return axios.post(`/api/siteboxmanager/fetcher/checkstatus`, parameters.data)
  },
  fetcherStartProcessing (_, parameters) {
    return axios.post(`/api/siteboxmanager/fetcher/start`, parameters.data)
  },
  fetcherCancelProcessing (_, parameters) {
    return axios.post(`/api/siteboxmanager/fetcher/cancel`, parameters.data)
  }
}

export default {
  namespaced: true,
  actions: actions
}
