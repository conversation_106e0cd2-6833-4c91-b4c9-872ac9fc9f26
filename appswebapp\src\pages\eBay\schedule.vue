<template>
  <div v-if="!isLoading && isScheduledEnabled && !isExceptionOccurred">
    <b-row>
      <b-col><h4>Schedule eBay Listing</h4></b-col>
      <b-col>
        <c-button class="float-right ml-2" message="Are you sure you want to unschedule?" v-if="isScheduledItemExisted" @confirm="onConfirmUnSchedule" variant="secondary" :disabled="isDisabled">Unschedule eBay Listing</c-button>
        <c-button :disabled="isDisabled" class="float-right" message="Are you sure you want to schedule?" @confirm="onConfirmSchedule" variant="primary">Schedule eBay Listing</c-button>
      </b-col>
    </b-row>
    <vehicle-description :vehicle="vehicle"></vehicle-description>
    <b-alert v-if="isDisabled" class="text-center text-dark" variant="warning" show>
      eBay Local does not allow vehicles with "Photos Coming Soon" images.
      <router-link class="text-info" :to="{name: 'details', params: { accountId: accountId, vin: vehicle.Vin }, query: {tabindex: 2}}"><u>Click here to upload photos of this vehicle</u></router-link>.
    </b-alert>
    <ValidationObserver ref="validator">
    <b-card class="mt-2">
      <div class="border-bottom">
        <h6>eBay Motors Category</h6>
      </div>
      <div>
        <detail-row :fixed-payload-width="true">
          <span slot="title">Top Level Category:</span>
          <b-form-select v-if="!isLoadingTopCategories" slot="payload" v-model="scheduledItem.TopLevelEBayCategory" @input="onChangeTopCategory" :options="getEBayTopCategoryOptions"></b-form-select>
          <loader v-else slot="payload" size="sm"/>
        </detail-row>
        <ValidationProvider name="eBay Category" rules="required" v-slot="{errors}">
        <detail-row title-position="start" :fixed-payload-width="true" :error="errors[0]">
          <span slot="title">eBay Category:</span>
          <b-form-select name="eBay_Category" v-if="!isLoadingCategories" slot="payload" v-model="scheduledItem.EBayCategory" @input="onChangeCategory" :options="getEBayCategoryOptions" :disabled="isDisabled"></b-form-select>
          <loader v-else slot="payload" size="sm"/>
        </detail-row>
        </ValidationProvider>
        <detail-row :fixed-payload-width="true">
          <span slot="title">eBay Sub Model:</span>
          <b-form-input slot="payload" v-model="scheduledItem.EBaySubModel" :disabled="isDisabled"></b-form-input>
        </detail-row>
      </div>
      <div class="border-bottom mt-3">
        <h6>Required vehicle Information</h6>
      </div>
      <ValidationProvider name="No. Of Cylinders" rules="excluded: 0" v-slot="{errors}">
      <detail-row title-position="start" :fixed-payload-width="true" :error="errors[0]">
        <span slot="title">No. Of Cylinders:</span>
        <b-form-select name="Cylinders" slot="payload" v-model="scheduledItem.Cylinders" :options="getCylinderOptions" :disabled="isDisabled"></b-form-select>
      </detail-row>
      </ValidationProvider>
      <ValidationProvider name="No. Of Doors" rules="excluded: 0" v-slot="{errors}">
      <detail-row title-position="start" :fixed-payload-width="true" :error="errors[0]">
        <span slot="title">No. Of Doors:</span>
        <b-form-select name="Doors" slot="payload" v-model="scheduledItem.Doors" :options="getDoorOptions" :disabled="isDisabled"></b-form-select>
      </detail-row>
      </ValidationProvider>
      <div class="border-bottom mt-3">
        <h6>Listing Details</h6>
      </div>
      <div>
        <detail-row :fixed-payload-width="true">
          <span slot="title">Listing Format:</span>
          <b-form-select v-model="scheduledItem.ListingType" :options="getListingFormatTypeOptions" slot="payload"></b-form-select>
        </detail-row>
        <ValidationProvider name="Listing Title" rules="required|max:80" v-slot="{errors}">
        <detail-row title-position="start" :extra-large-payload-width="true" :error="errors[0]">
          <span slot="title">Listing Title:</span>
          <b-row class="w-100" slot="payload">
            <b-col class="pr-0" sm="12" md="8" lg="8">
              <b-form-input name="Listing_Title" v-model="scheduledItem.ListingTitle" :disabled="isDisabled"></b-form-input>
            </b-col>
            <b-col sm="12" md="4" lg="4" class="py-2">
              <span>80 Characters Remaining</span>
            </b-col>
          </b-row>
        </detail-row>
        </ValidationProvider>
        <ValidationProvider v-if="scheduledItem.IsSubTitle" name="Sub Title" rules="required|max:55" v-slot="{errors}">
        <detail-row title-position="start" :extra-large-payload-width="true" v-if="scheduledItem.IsSubTitle" :error="errors[0]">
          <span slot="title">Sub Title (Paid Upgrade):</span>
          <b-row class="w-100" slot="payload">
            <b-col class="pr-0" sm="12" md="8" lg="8">
              <b-form-input name="Sub_Title" v-model="scheduledItem.SubTitle" :disabled="isDisabled"></b-form-input>
            </b-col>
            <b-col sm="12" md="4" lg="4" class="py-2">
              <span>55 Characters Remaining</span>
            </b-col>
          </b-row>
        </detail-row>
        </ValidationProvider>
        <ValidationProvider name="Starting Price" rules="required|min_value: 1" v-slot="{errors}">
        <detail-row title-position="start" :error="errors[0]" :fixed-payload-width="true" v-if="listingFormatTypes.auction.value === scheduledItem.ListingType">
          <span slot="title">Starting Price:</span>
          <price-input slot="payload" name="Starting_Price" active v-model="scheduledItem.StartPrice" :disabled="isDisabled"></price-input>
        </detail-row>
        </ValidationProvider>
        <ValidationProvider name="Reserve Price" :rules="getReservePriceValidationRule" v-slot="{errors}">
        <detail-row :fixed-payload-width="true" v-if="listingFormatTypes.auction.value === scheduledItem.ListingType" :error="errors[0]">
          <span slot="title">Reserve Price:</span>
          <price-input slot="payload" name="Reserve_Price" active v-model="scheduledItem.ReservePrice" :disabled="isDisabled"></price-input>
        </detail-row>
        </ValidationProvider>
        <ValidationProvider v-if="listingFormatTypes.local.value !== scheduledItem.ListingType" name="Buy it Now Price" rules="required|min_value: 1" v-slot="{errors}">
        <detail-row titlePosition="start" :error="errors[0]" :fixed-payload-width="true">
          <span slot="title">Buy it Now Price *:</span>
          <b-form-group
            slot="payload"
            class="w-100"
          >
            <price-input
              v-if="listingFormatTypes.fixedPriceListing.value === scheduledItem.ListingType"
              active
              name="Buy_It_Now_Price"
              v-model="scheduledItem.StartPrice"
              :disabled="isDisabled"
            >
            </price-input>
            <price-input
              v-else
              active
              name="Buy_It_Now_Price"
              v-model="scheduledItem.BinPrice"
              :disabled="isDisabled"
            >
            </price-input>
            <b-form-text class="text-dark custom-text-nowrap">
              * Requires feedback of 10 or higher and aBay ID Verification.
            </b-form-text>
          </b-form-group>
        </detail-row>
        </ValidationProvider>
        <ValidationProvider name="Advertised Price" rules="required|min_value: 1" v-slot="{errors}" v-else>
          <detail-row :fixed-payload-width="true" :error="errors[0]">
            <span slot="title">Advertised Price:</span>
            <price-input active slot="payload" name="Advertised_Price" v-model="scheduledItem.StartPrice" :disabled="isDisabled"></price-input>
          </detail-row>
        </ValidationProvider>
        <detail-row :fixed-payload-width="true" v-if="listingFormatTypes.auction.value !== scheduledItem.ListingType">
          <span slot="title">Allow Best Offers:</span>
          <b-form-select slot="payload" v-model="scheduledItem.IsAllowedBestOffer" :options="getYesNoOptions" :disabled="isDisabled"></b-form-select>
        </detail-row>
        <detail-row :fixed-payload-width="true" v-if="listingFormatTypes.auction.value !== scheduledItem.ListingType && scheduledItem.IsAllowedBestOffer">
          <span slot="title">Auto Decline Amount:</span>
          <b-form-input slot="payload" type="number" v-model="scheduledItem.AutoDeclineAmount" :disabled="isDisabled"></b-form-input>
        </detail-row>
        <detail-row :fixed-payload-width="true" v-if="isDepositEnabled">
          <span slot="title">Deposit:</span>
          <price-input active slot="payload" v-if="isDepositEnabledToChange" v-model="scheduledItem.DepositAmount" :disabled="isDisabled"></price-input>
          <span slot="payload" v-else title="Deposit can be changed in Settings">{{getPriceText(scheduledItem.DepositAmount)}}
            <span class="ml-2" style="cursor:pointer;" @click="onDepositEdit"><font-awesome-icon icon="pencil-alt" size="sm" /></span>
          </span>
        </detail-row>
        <detail-row :large-payload-width="true" v-if="listingFormatTypes.local.value !== scheduledItem.ListingType">
          <span slot="title">Boldface Title:</span>
          <b-form-checkbox slot="payload" v-model="scheduledItem.IsBold" :disabled="isDisabled">$5 will be added to your aBay bill/invoice.</b-form-checkbox>
        </detail-row>
        <detail-row :large-payload-width="true" v-if="listingFormatTypes.local.value !== scheduledItem.ListingType">
          <span slot="title">Sub Title:</span>
          <b-form-checkbox slot="payload" @input="onInputSubTitle" v-model="scheduledItem.IsSubTitle" :disabled="isDisabled">$5 will be added to your aBay bill/invoice.</b-form-checkbox>
        </detail-row>
      </div>
      <div class="border-bottom mt-3">
        <h6>Listing Date &amp; Time</h6>
      </div>
      <div>
        <detail-row :fixed-payload-width="true">
          <span slot="title">Start Day:</span>
          <b-form-select slot="payload" v-model="scheduledItem.StartDay" :options="getStartDayOptions" :disabled="isDisabled"></b-form-select>
        </detail-row>
        <detail-row :fixed-payload-width="true">
          <span slot="title">Start Time:</span>
          <div slot="payload" class="d-flex flex-row">
          <b-form-select class="select-time-item" v-model="scheduledItem.StartHour" :options="getHourOptions" :disabled="isDisabled"></b-form-select>
          <span class="mt-2 mr-2">:</span>
          <b-form-select class="select-time-item" v-model="scheduledItem.StartMinute" :options="getMinuteOptions" :disabled="isDisabled"></b-form-select>
          <b-form-select class="select-time-item" v-model="scheduledItem.TimeFormat" :options="getAmPmOptions" :disabled="isDisabled"></b-form-select>
          <span class="mt-2 d-none d-md-block text-nowrap">(Pacific Time)</span>
        </div>
        </detail-row>
        <ValidationProvider name="Duration" rules="required" v-slot="{errors}">
        <detail-row title-position="start" :fixed-payload-width="true" :error="errors[0]">
          <span slot="title">Duration:</span>
          <b-form-select name="Duration" :options="getDurationOptions" v-model="scheduledItem.AuctionDuration" slot="payload" :disabled="isDisabled"></b-form-select>
        </detail-row>
        </ValidationProvider>
        <detail-row :fixed-payload-width="true">
          <span slot="title">Private Listing?:</span>
          <b-form-select slot="payload" v-model="scheduledItem.IsPrivateListing" :options="getYesNoOptions" :disabled="isDisabled"></b-form-select>
        </detail-row>
      </div>
      <div class="text-dark mt-2 pt-2 border-top">
        <span>
          eBay Motors offers a credit for re-lists, however certain restrictions apply.
          <a :href="eBayInfoUrls.feesCreditsInvoices" target="blank" class="text-info"><u>Click here for details</u></a>.
        </span>
        <br>
        <span>Your reserve price must be the same or lower than the reserve price used in the previous listing.</span>
        <br>
        <span>eBizAutos will automatically launch this auction using the RelistItem API call.</span>
        <br>
        <span>If this auction meets ALL of eBay's requirements, you will automatically receive a credit for this auction if it sells on eBay.</span>
      </div>
    </b-card>
    </ValidationObserver>
    <c-button class="float-right mt-3 ml-2" message="Are you sure you want to unschedule?" v-if="isScheduledItemExisted" @confirm="onConfirmUnSchedule" variant="secondary" :disabled="isDisabled">Unschedule eBay Listing</c-button>
    <c-button class="float-right mt-3" message="Are you sure you want to schedule?" @confirm="onConfirmSchedule" variant="primary" :disabled="isDisabled">Schedule eBay Listing</c-button>
  </div>
  <div v-else-if="!isScheduledEnabled && !isExceptionOccurred">
    <b-alert class="text-center" variant="warning" show>
      Invalid or Expired eBay Authorization.<br/>
      Click here to
      <router-link class="text-info" :to="{name: 'ebay-settings', params: { accountId: accountId }}">
        <u>Update your eBay Motors Authorization</u>
      </router-link>
      now.
    </b-alert>
  </div>
  <div v-else-if="isExceptionOccurred" class="text-center">
    <error-alert/>
  </div>
  <div v-else>
    <loader size="lg"/>
  </div>
</template>

<script>
import vehicleDescription from '@/components/eBay/helpers/vehicleDescription'
import priceInput from '@/components/_shared/priceInput'
import detailRow from '@/components/details/helpers/detailRow'
import constants from '@/shared/ebay/constants'
import moment from 'moment'
import numeral from 'numeral'
import scheduledHelper from '@/shared/ebay/scheduledHelper'
import loader from '@/components/_shared/loader'
import photoTypes from '@/shared/common/vehicle/photoTypes'

export default {
  name: 'ebay-schedule-listing',
  metaInfo: {
    title: 'eBay Schedule'
  },
  props: {
    accountId: { type: Number, required: true },
    vin: { type: String, required: true }
  },
  data () {
    return {
      isLoading: true,
      isScheduledEnabled: true,
      isScheduledItemExisted: false,
      isExceptionOccurred: false,
      isReservePriceRequired: false,
      isDepositEnabled: false,
      isDepositEnabledToChange: false,
      isLoadingTopCategories: false,
      isLoadingCategories: false,
      vehicle: {},
      eBayInfoUrls: constants.eBayInfoUrls,
      listingFormatTypes: constants.listingFormatTypes,
      ebayTopCategories: [],
      ebayTopCategoryOptions: [],
      ebayCategories: [],
      ebayCategoryOptions: [],
      scheduledItem: {}
    }
  },
  created () {
    this.initData().then(() => { this.isLoading = false })
  },
  computed: {
    getReservePriceValidationRule () {
      if (this.isReservePriceRequired) {
        return 'required|min_value: 1'
      }

      return ''
    },
    getDurationOptions () {
      let res = Object.values(constants.durationOptions).find(x => x.value === this.scheduledItem.ListingType)

      if (res) {
        return [{ value: null, text: 'Select Duration' }].concat(res.options)
      }

      return [{ value: null, text: 'Select Duration' }]
    },
    isDisabled () {
      return (this.vehicle.PhotoType === photoTypes.customComingSoon || this.vehicle.PhotoType === photoTypes.standardComingSoon) &&
        this.scheduledItem.ListingType === this.listingFormatTypes.local.value
    },
    getListingFormatTypeOptions () {
      return Object.values(constants.listingFormatTypes)
    },
    getEBayCategoryOptions () {
      return this.ebayCategoryOptions
    },
    getEBayTopCategoryOptions () {
      return this.ebayTopCategoryOptions
    },
    getYesNoOptions () {
      return [
        {
          value: false,
          text: 'No'
        },
        {
          value: true,
          text: 'Yes'
        }
      ]
    },
    getStartDayOptions () {
      let options = [{ value: -1, text: 'Now' }]
      Array(7).fill().map((_, i) => {
        if (i === 0) {
          options.push({
            value: 0,
            text: 'Today'
          })
          return
        }
        options.push({
          value: i,
          text: moment().add(i, 'd').format('dddd M/D')
        })
      })

      return options
    },
    getAmPmOptions () {
      return ['AM', 'PM']
    },
    getHourOptions () {
      return constants.hourOptions
    },
    getMinuteOptions () {
      return Array(12).fill().map((_, i) => {
        if (i === 0) {
          return {
            value: 0,
            text: '00'
          }
        }
        if (i === 1) {
          return {
            value: 5,
            text: '05'
          }
        }
        return {
          value: i * 5,
          text: `${i * 5}`
        }
      })
    },
    getCylinderOptions () {
      let options = Array(5).fill().map((_, i) => {
        if (i === 0) {
          return {
            value: 0,
            text: 'N/A'
          }
        }
        return {
          value: i + 1,
          text: `${i + 1}`
        }
      })
      for (let i = 0; i <= 5; i++) {
        options.push({
          value: 6 + i * 2,
          text: `${6 + i * 2}`
        })
      }
      options.push({ value: 'ro', text: 'Rotary' })
      return options
    },
    getDoorOptions () {
      return Array(5).fill().map((_, i) => {
        if (i === 0) {
          return {
            value: 0,
            text: 'N/A'
          }
        }
        if (i === 4) {
          return {
            value: 5,
            text: '5 or More'
          }
        }
        return {
          value: i + 1,
          text: `${i + 1} Doors`
        }
      })
    }
  },
  components: {
    'vehicle-description': vehicleDescription,
    'detail-row': detailRow,
    'price-input': priceInput,
    'loader': loader
  },
  methods: {
    async initData () {
      try {
        let scheduledEnabledRes = await this.$store.dispatch('eBayScheduling/checkScheduledEnabled', { accountId: this.accountId })
        this.isScheduledEnabled = scheduledEnabledRes.data
        if (!this.isScheduledEnabled) {
          return
        }

        let vehicleApiRes = await this.$store.dispatch('eBayScheduling/getScheduledVehicle', { accountId: this.accountId, vin: this.vin })
        let scheduledItemApiRes = await this.$store.dispatch('eBayScheduling/getScheduledItem', { accountId: this.accountId, vin: this.vin })
        this.isScheduledItemExisted = scheduledItemApiRes.data
        let accountSettingsApiRes = await this.$store.dispatch('eBay/getAccountSettings', this.accountId)

        this.isReservePriceRequired = ((accountSettingsApiRes.data || {}).GlobalDefaultSettings || {}).HasReservePricePrompt || false
        this.isDepositEnabled = ((accountSettingsApiRes.data || {}).DepositSettings || {}).IsRequiredDeposit || false
        this.isDepositEnabledToChange = ((accountSettingsApiRes.data || {}).DepositSettings || {}).IsAllowedToChangeDepositPerListing || false

        this.scheduledItem = scheduledHelper.calculateScheduledModel(vehicleApiRes.data, accountSettingsApiRes.data, scheduledItemApiRes.data)
        this.vehicle = vehicleApiRes.data
        await this.populateEBayTopCategoryOptions()
        await this.populateEBayCategoryOptions()
      } catch (ex) {
        this.isExceptionOccurred = true
        this.$toaster.error('Something went wrong!')
        this.$logger.handleError(ex, 'Cannot populate scheduled data')
      }
    },
    async populateEBayCategoryOptions () {
      this.$store.dispatch('eBayScheduling/getEBayCategories', { accountId: this.accountId, filter: { parentCategoryId: this.scheduledItem.TopLevelEBayCategory } }).then(res => {
        this.ebayCategories = res.data
        this.ebayCategoryOptions = [{value: null, text: 'Select eBay Category'}]
        if (res.data) {
          res.data.map(x => {
            this.ebayCategoryOptions.push({
              text: x.Name,
              value: x.EBayCategoryId
            })
          })
        }
      }).catch(ex => {
        this.isExceptionOccurred = true
        this.$toaster.error('Something went wrong!')
        this.$logger.handleError(ex, 'Cannot populate ebay categories')
      }).finally(() => {
        this.isLoadingCategories = false
      })
    },
    async populateEBayTopCategoryOptions () {
      this.$store.dispatch('eBayScheduling/getEBayTopCategories', { accountId: this.accountId, filter: { categoryId: this.scheduledItem.TopLevelEBayCategory } }).then(res => {
        this.ebayTopCategories = res.data
        this.ebayTopCategoryOptions = []
        if (res.data) {
          res.data.map(x => {
            this.ebayTopCategoryOptions.push({
              text: x.Name,
              value: x.EBayCategoryId
            })
          })
        }
      }).catch(ex => {
        this.isExceptionOccurred = true
        this.$toaster.error('Something went wrong!')
        this.$logger.handleError(ex, 'Cannot populate ebay top categories')
      }).finally(() => {
        this.isLoadingTopCategories = false
      })
    },
    onConfirmSchedule () {
      this.$refs.validator.validate().then(res => {
        if (res) {
          this.scheduleItem()
        }
      })
    },
    onConfirmUnSchedule () {
      this.$store.dispatch('eBayScheduling/unscheduleItem', { accountId: this.accountId, vin: this.vehicle.Vin }).then(res => {
        this.$toaster.success('Unscheduled Successfully')
      }).catch(ex => {
        this.$toaster.error('Something went wrong! Please try again later')
        this.$logger.handleError(ex, 'Cannot unschedule item')
      }).finally(() => {
        this.$router.push({name: 'ebay-posts', params: {accountId: this.accountId}})
      })
    },
    scheduleItem () {
      this.$store.dispatch('eBayScheduling/scheduleItem', { accountId: this.accountId, vin: this.vehicle.Vin, data: this.scheduledItem }).then(res => {
        this.$toaster.success('Scheduled Successfully')
      }).catch(ex => {
        this.$toaster.error('Something went wrong! Please try again later')
        this.$logger.handleError(ex, 'Cannot schedule item')
      }).finally(() => {
        this.$router.push({name: 'ebay-posts', params: {accountId: this.accountId}})
      })
    },
    getPriceText (value) {
      return numeral(value).format('$0,0')
    },
    onDepositEdit () {
      this.$router.push(`/ebay/${this.accountId}/settings/#deposit-settings`)
    },
    onInputSubTitle () {
      if (!this.scheduledItem.IsSubTitle) {
        this.scheduledItem.SubTitle = ''
      }
    },
    onChangeTopCategory () {
      this.scheduledItem.EBayCategory = null
      this.refreshEBayCategories()
    },
    onChangeCategory (category) {
      let res = this.ebayCategories.find(x => x.EBayCategoryId === category)

      if (res && !res.IsLeaf) {
        this.scheduledItem.TopLevelEBayCategory = category
        this.scheduledItem.EBayCategory = null
        this.refreshEBayCategories()
      }
    },
    refreshEBayCategories () {
      this.isLoadingCategories = true
      this.isLoadingTopCategories = true
      this.populateEBayTopCategoryOptions()
      this.populateEBayCategoryOptions()
    }
  }
}
</script>

<style scoped>
@media (min-width: 1320px) {
  .custom-text-nowrap {
    white-space: nowrap;
  }
}

@media (max-width: 380px) {
  .select-time-item {
    width: 40px;
    margin-left: 0;
    margin-right: 0.5rem;
  }
}

.select-time-item {
  width: 80px;
  margin-right: 0.5rem;
}
</style>
