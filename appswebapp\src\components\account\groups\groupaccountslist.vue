<template>
  <div v-if="readyToShow">
    <b-card>
      <b-row>
        <b-col xl="6">
          <b-form @submit.prevent="applySearch" class="mb-2">
            <b-card no-body>
              <b-card-body>
                <div class="form-row">
                  <div class="col-12">
                    <b-input-group class="search-group">
                      <b-form-input ref="search" placeholder="Search..." class="d-inline-block float-sm-right"></b-form-input>
                      <b-input-group-append>
                        <b-btn type="submit">Go</b-btn>
                      </b-input-group-append>
                    </b-input-group>
                  </div>
                </div>
              </b-card-body>
            </b-card>
          </b-form>
        </b-col>
      </b-row>
      <div class="row">
        <div class="col">
          <div class="table-responsive selected-accounts-table">
            <b-table
              striped
              hover
              responsive="true"
              :items="listing.items"
              :fields="fields">
              <template #cell(accountId)="data">
                <router-link class="media-body d-block text-dark ml-3"
                  :to="{ path: `${groupId}/accountpermissions/${data.item.accountId}` }">
                  {{data.item.accountId}}
                </router-link>
              </template>
              <template #cell(dealershipName)="data">
                <router-link class="media-body d-block text-dark ml-3"
                  :to="{ path: `${groupId}/accountpermissions/${data.item.accountId}` }">
                  {{data.item.dealershipName}}
                </router-link>
              </template>
            </b-table>
          </div>
          <paging
            :pageNumber="filters.pageNumber"
            :pageSize="filters.pageSize"
            :totalItems="listing.totalItemsCount"
            titled
            pageSizeSelector
            @numberChanged="pageChanged"
            @changePageSize="changePageSize">
          </paging>
        </div>
      </div>
    </b-card>
  </div>
</template>

<script>
import globals from '@/globals'
import { ObjectSchema } from '@/shared/common/objectHelpers'
import paging from '@/components/_shared/paging.vue'

const defaultValues = new ObjectSchema({
  pageNumber: { type: Number, default: 1 },
  pageSize: { type: Number, default: 10 },
  search: { type: String, default: '' }
})

export default {
  name: 'AccountsList',
  props: {
    groupId: {
      type: String,
      required: true
    },
    accounts: {
      type: Array,
      required: true
    }
  },
  data () {
    return {
      filters: null,
      selectedcount: 0,
      readyToShow: false,
      fields: ['accountId', 'dealershipName']
    }
  },
  computed: {
    listing: function () {
      let filteredAccounts = Array.from(this.accounts)
      let result = {}
      result.totalItemsCount = this.accounts.length
      if (this.filters.search && this.filters.search.trim) {
        let exp = new RegExp(this.filters.search, 'i')
        let searchIntVal = null
        if (!isNaN(this.filters.search)) {
          searchIntVal = parseInt(this.filters.search)
        }

        if (searchIntVal !== null) {
          filteredAccounts = filteredAccounts.filter(x => x.dealershipName.search(exp) !== -1 || x.accountId === searchIntVal)
        } else {
          filteredAccounts = filteredAccounts.filter(x => x.dealershipName.search(exp) !== -1)
        }

        result.totalItemsCount = filteredAccounts.length
      }
      result.items = filteredAccounts.sort((x, y) => x.accountId - y.accountId).slice((this.filters.pageNumber - 1) * this.filters.pageSize, ((this.filters.pageNumber - 1) * this.filters.pageSize) + this.filters.pageSize)
      return result
    }
  },
  mounted () {
    this.filters = globals().getClonedValue(defaultValues.getObject())
    this.readyToShow = true
  },
  methods: {
    pageChanged (pageNumber) {
      if (pageNumber !== this.filters.pageNumber) {
        this.filters.pageNumber = pageNumber
      }
    },

    applySearch () {
      let searchValue = this.$refs.search.$el.value
      this.filters.search = searchValue
      this.filters.pageNumber = 1
    },

    changePageSize (newSize) {
      if (newSize !== this.filters.pageSize) {
        this.filters.pageSize = newSize
        this.filters.pageNumber = 1
      }
    }
  },
  components: {
    'paging': paging
  }
}
</script>

<style scoped>
@media (max-width: 425px) {
 .table-responsive {
   font-size: 12px;
  }
}
</style>
