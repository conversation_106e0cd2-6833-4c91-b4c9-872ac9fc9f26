import constants from './constants'
import moment from 'moment'

class ScheduledHelper {
  calculateScheduledModel (vehicle, accountSettings, scheduledItem) {
    let modelToSchedule = {}
    if (scheduledItem) {
      modelToSchedule = this.calculateScheduledModelWithExitScheduledItem(scheduledItem)
    } else {
      modelToSchedule = this.calculateNewScheduledModel(vehicle, accountSettings)
    }

    return modelToSchedule
  };
  calculateScheduledModelWithExitScheduledItem (scheduledItem) {
    let modelToSchedule = {}
    if (scheduledItem.IsLocalAuction) {
      modelToSchedule.ListingType = constants.listingFormatTypes.local.value
    } else if (scheduledItem.IsPriceFixed) {
      modelToSchedule.ListingType = constants.listingFormatTypes.fixedPriceListing.value
    } else {
      modelToSchedule.ListingType = constants.listingFormatTypes.auction.value
    }
    modelToSchedule.Vin = scheduledItem.Vin
    modelToSchedule.IsPrivateListing = scheduledItem.IsPrivate
    modelToSchedule.EBaySubModel = scheduledItem.EBaySubModel
    modelToSchedule.StartPrice = scheduledItem.StartPrice
    modelToSchedule.ReservePrice = scheduledItem.ReservePrice
    modelToSchedule.BinPrice = scheduledItem.BinPrice
    modelToSchedule.ListingTitle = scheduledItem.ListingTitle
    modelToSchedule.Cylinders = scheduledItem.Cylinders
    modelToSchedule.Doors = scheduledItem.Doors
    modelToSchedule.AuctionDuration = scheduledItem.AuctionDuration
    modelToSchedule.IsBold = scheduledItem.IsBold
    modelToSchedule.IsAllowedBestOffer = scheduledItem.IsBestOfferAllowed
    modelToSchedule.AutoDeclineAmount = scheduledItem.BestOfferAutoDeclinePrice
    modelToSchedule.DepositAmount = scheduledItem.Deposit
    modelToSchedule.TopLevelEBayCategory = scheduledItem.EBayCategoryParentId
    modelToSchedule.EBayCategory = scheduledItem.EBayCategoryId
    modelToSchedule.EBayCategoryParentName = scheduledItem.EBayCategoryParentName
    if (scheduledItem.SubTitle) {
      modelToSchedule.SubTitle = scheduledItem.SubTitle
      modelToSchedule.IsSubTitle = true
    }
    if (moment(scheduledItem.AuctionScheduledDateTime) < moment()) {
      modelToSchedule.StartDay = -1 // Now
    } else {
      let nowDate = moment().set({'hour': 0, 'minute': 0}).format()
      modelToSchedule.StartDay = moment(scheduledItem.AuctionScheduledDateTime).diff(moment(nowDate), 'days')
    }
    let launchHour = moment(scheduledItem.AuctionScheduledDateTime).hour()
    modelToSchedule.StartHour = launchHour === 0 ? 12 : launchHour > 12 ? launchHour - 12 : launchHour
    modelToSchedule.StartMinute = moment(scheduledItem.AuctionScheduledDateTime).minutes()
    modelToSchedule.TimeFormat = launchHour > 12 ? 'PM' : 'AM'

    return modelToSchedule
  };
  calculateNewScheduledModel (vehicle, accountSettings) {
    let modelToSchedule = {}
    modelToSchedule.Vin = vehicle.Vin
    modelToSchedule.ListingType = accountSettings.GlobalDefaultSettings.ListingType
    modelToSchedule.IsPrivateListing = accountSettings.GlobalDefaultSettings.IsPrivateAuctions
    modelToSchedule.EBaySubModel = this.getEBaySubModel(vehicle)
    modelToSchedule.StartPrice = 0
    modelToSchedule.ReservePrice = 0
    modelToSchedule.BinPrice = 0
    modelToSchedule.Cylinders = vehicle.Cylinders || 0
    modelToSchedule.Doors = vehicle.Doors
    modelToSchedule.ListingTitle = ''
    modelToSchedule.IsAllowedBestOffer = accountSettings.GlobalDefaultSettings.IsAllowedBestOffers
    modelToSchedule.AutoDeclineAmount = 0
    modelToSchedule.DepositAmount = accountSettings.DepositSettings.IsRequiredDeposit ? accountSettings.DepositSettings.DepositAmount : 0
    modelToSchedule.IsBold = false
    modelToSchedule.StartDay = 0
    modelToSchedule.StartHour = 12
    modelToSchedule.StartMinute = 0
    modelToSchedule.TimeFormat = 'PM'
    modelToSchedule.AuctionDuration = null
    if (vehicle.EBayCategoryParentId) {
      modelToSchedule.TopLevelEBayCategory = vehicle.EBayCategoryParentId
      modelToSchedule.EBayCategory = vehicle.EBayCategoryId === vehicle.EBayCategoryParentId ? null : vehicle.EBayCategoryId
      modelToSchedule.EBayCategoryParentName = vehicle.EBayCategoryParentName
    } else {
      let ebayCategory = this.getEBayCategoryByVehicleType(vehicle.VehicleType)
      modelToSchedule.TopLevelEBayCategory = ebayCategory.ebayCategoryId
      modelToSchedule.EBayCategory = null
      modelToSchedule.EBayCategoryParentName = ebayCategory.ebayCategoryName
    }

    return modelToSchedule
  };
  getEBaySubModel (vehicle) {
    if (vehicle.Trim) {
      return vehicle.Trim.substring(0, 12)
    }
    if (vehicle.PromoText) {
      return vehicle.PromoText.substring(0, 12)
    }

    return ''
  }
  getEBayCategoryByVehicleType (vehicleType) {
    let res = Object.values(constants.ebayCategoriesByVehicleType).find(x => x.value === vehicleType)
    if (res) {
      return res
    }

    return constants.ebayCategoriesByVehicleType.Default
  }
}

export default new ScheduledHelper()
