<!DOCTYPE html>
<html>
<head>
    <title>SMS MFA UI Example</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; padding: 20px; }
        .authentication-wrapper { max-width: 400px; margin: 0 auto; }
        .authentication-inner { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        #login-logo { height: 2.25rem; margin: 0 auto; display: block; }
        .policy-links a { color: #4E5155 !important; text-decoration: underline !important; font-weight: bold !important; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">SMS MFA Implementation - UI Examples</h1>
        
        <!-- MFA Method Selection Page -->
        <div class="row mb-5">
            <div class="col-12">
                <h3>1. MFA Method Selection (/mfa)</h3>
                <div class="authentication-wrapper authentication-1 px-1">
                    <div class="authentication-inner py-4">
                        <img id="login-logo" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjQwIiB2aWV3Qm94PSIwIDAgMTAwIDQwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8dGV4dCB4PSI1MCIgeT0iMjUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzMzMzMzMyIgdGV4dC1hbmNob3I9Im1pZGRsZSI+ZUJpekF1dG9zPC90ZXh0Pgo8L3N2Zz4K" alt="eBizAutos Login Logo">
                        <form class="my-4">
                            <h2 class="text-center mb-2">OTP Verification Method</h2>
                            <div class="text-center text-muted mb-3">
                                A one-time passcode will be sent to you as an additional verification step.
                            </div>
                            <div class="mb-3">
                                <div class="mb-2">How do you want to get the code?</div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="method" id="email" value="email" checked>
                                    <label class="form-check-label" for="email">
                                        Email Address: <strong>j***<EMAIL></strong>
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="method" id="sms" value="sms">
                                    <label class="form-check-label" for="sms">
                                        SMS Text Message: <strong>****1234</strong>
                                    </label>
                                </div>
                            </div>
                            <div class="text-muted small mb-3 policy-links">
                                Message and data rates may apply. One message per request.<br/>
                                View our <a href="https://www.ebizautos.com/terms" target="_blank">Terms of Service</a> and <a href="https://www.ebizautos.com/privacy-policy" target="_blank">Privacy Policy</a>.
                            </div>
                            <div>
                                <button type="button" class="btn btn-primary btn-block btn-lg">Send</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- MFA Verification Page -->
        <div class="row mb-5">
            <div class="col-12">
                <h3>2. MFA Code Verification (/mfa/verify)</h3>
                
                <!-- Email verification example -->
                <div class="mb-4">
                    <h5>When Email is selected:</h5>
                    <div class="authentication-wrapper authentication-1 px-1">
                        <div class="authentication-inner py-4">
                            <img id="login-logo" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjQwIiB2aWV3Qm94PSIwIDAgMTAwIDQwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8dGV4dCB4PSI1MCIgeT0iMjUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzMzMzMzMyIgdGV4dC1hbmNob3I9Im1pZGRsZSI+ZUJpekF1dG9zPC90ZXh0Pgo8L3N2Zz4K" alt="eBizAutos Login Logo">
                            <form class="my-4">
                                <h2 class="text-center mb-2">Enter One-Time Passcode</h2>
                                <div class="text-center text-muted mb-3">
                                    A one-time passcode was just sent to <strong>j***<EMAIL></strong>.
                                </div>
                                <input type="text" class="form-control form-control-lg" placeholder="Enter the code">
                                <div class="mt-2">
                                    <small class="text-muted policy-links">Didn't get it? <a href="#">Resend</a>.</small>
                                </div>
                                <div class="mt-3">
                                    <button type="button" class="btn btn-primary btn-block btn-lg">Continue Sign In</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- SMS verification example -->
                <div class="mb-4">
                    <h5>When SMS is selected:</h5>
                    <div class="authentication-wrapper authentication-1 px-1">
                        <div class="authentication-inner py-4">
                            <img id="login-logo" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjQwIiB2aWV3Qm94PSIwIDAgMTAwIDQwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8dGV4dCB4PSI1MCIgeT0iMjUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzMzMzMzMyIgdGV4dC1hbmNob3I9Im1pZGRsZSI+ZUJpekF1dG9zPC90ZXh0Pgo8L3N2Zz4K" alt="eBizAutos Login Logo">
                            <form class="my-4">
                                <h2 class="text-center mb-2">Enter One-Time Passcode</h2>
                                <div class="text-center text-muted mb-3">
                                    A one-time passcode was just sent to <strong>****1234</strong>.
                                </div>
                                <input type="text" class="form-control form-control-lg" placeholder="Enter the code">
                                <div class="mt-2">
                                    <small class="text-muted policy-links">Didn't get it? <a href="#">Resend</a>.</small>
                                </div>
                                <div class="mt-3">
                                    <button type="button" class="btn btn-primary btn-block btn-lg">Continue Sign In</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Technical Details -->
        <div class="row">
            <div class="col-12">
                <h3>3. Technical Implementation Details</h3>
                <div class="card">
                    <div class="card-body">
                        <h5>Key Changes Made:</h5>
                        <ul>
                            <li><strong>mfa-method.vue:</strong> Added getMethodDisplayName() function and updated template</li>
                            <li><strong>mfa-verify.vue:</strong> Changed from maskedEmail to maskedDestination for universal support</li>
                            <li><strong>mfa.js store:</strong> Added selectedMethod tracking and universal getters</li>
                            <li><strong>authentication.js store:</strong> Added MFA state cleanup on logout</li>
                        </ul>
                        
                        <h5>API Integration:</h5>
                        <ul>
                            <li><strong>Method Types:</strong> Email = 1, SMS = 2</li>
                            <li><strong>Endpoints:</strong> POST /api/auth/mfa/send, POST /api/auth/mfa/verify</li>
                            <li><strong>Backend:</strong> Already supports SMS via Twilio integration</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simulate radio button interaction
        document.querySelectorAll('input[name="method"]').forEach(radio => {
            radio.addEventListener('change', function() {
                console.log('Selected method:', this.value);
            });
        });
    </script>
</body>
</html>
