<template>
  <editSettingsHelper @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="Default View" :isLoading="isUpdatingProcessing" :isViewMode="isViewMode">
    <template slot="settings-content">
      <template v-if="!isLoading">
        <b-table
          hover
          class="analytics-settings-table"
          :items="settings"
          :fields="fields"
        >
            <template #cell(value)="data">

              <b-form-radio
                v-model="selectedDefaultView"
                :value='data.value'
                :text='data.text'
                name="setting-radios"
                button-variant='secondary'
                @change='changeSettings(data)'
                :disabled="isViewMode"
              >
              </b-form-radio>

            </template>

        </b-table>
      </template>
      <loader v-else size="md" class="mt-3"/>
    </template>
  </editSettingsHelper>
</template>

<script>
import { mapGetters } from 'vuex'
import globals from '../../../globals'
import editSettingsHelper from '../../_shared/editSettingsHelper.vue'
import loader from '@/components/_shared/loader'

export default {
  name: 'analytics-default-view-setting-section',
  props: {
    accountId: {
      type: Number,
      required: false
    }
  },
  created () {
    this.fillSettings()
  },
  data () {
    return {
      originalDefaultView: '',
      selectedDefaultView: '',
      settings: [],
      isViewMode: true,
      isUpdatingProcessing: false,
      isLoading: true
    }
  },
  components: {
    editSettingsHelper,
    loader
  },
  computed:
  {
    ...mapGetters('users', ['userInfo']),
    ...mapGetters('analyticsGa4', ['dateRangeQuery']),
    fields () {
      return [
        { key: 'text', label: 'View / GroupName', thStyle: { width: '80%' } },
        { key: 'value', label: 'Select Default', thStyle: { width: '20%', 'text-align': 'center' }, tdClass: 'text-center' }
      ]
    }
  },
  methods: {
    async saveSettings () {
      try {
        this.isUpdatingProcessing = true
        await this.$store.dispatch('analyticsGa4/updateDefaultViewId', {accountId: this.accountId, defaultAnalyticsReportGroupId: this.selectedDefaultView})
        this.originalDefaultView = globals().getClonedValue(this.selectedDefaultView)
        this.$toaster.success('Default view was changed')
      } catch (err) {
        this.$toaster.error('Default view wasn\'t changed')
        this.$logger.handleError(err, 'Default view wasn\'t changed')
      } finally {
        this.isUpdatingProcessing = false
        this.changeMode(true)
      }
    },
    async changeSettings (data) {
      this.selectedDefaultView = data.value
    },
    async fillSettings () {
      try {
        const apiResults = await Promise.all([
          this.$store.dispatch('accountSettings/getAccountSettings', this.accountId),
          this.$store.dispatch('analyticsGa4/getAccessibleReportGroups', { accountId: this.accountId }),
          this.$store.dispatch('analyticsGa4/getDefaultViewId', {accountId: this.accountId})
        ])

        this.settings = [
          { value: '', text: `${apiResults[0].dealerInformation.companyName} (${apiResults[0].accountId})` },
          ...apiResults[1].data.model.map(x => ({ value: x.id, text: x.groupName }))
        ]

        if (apiResults[2].data.model) {
          this.originalDefaultView = apiResults[2].data.model
          this.selectedDefaultView = globals().getClonedValue(this.originalDefaultView)
        }
      } catch (err) {
        this.$toaster.error('Failed to get data from server', { timeout: 8000 })
        this.$logger.handleError(err, 'Can\'t get settings table', this.page.filter)
      } finally {
        this.isLoading = false
      }
    },
    changeMode (newMode) {
      this.isViewMode = newMode
    },
    cancel () {
      this.selectedDefaultView = globals().getClonedValue(this.originalDefaultView)
      this.changeMode(true)
    }
  }
}
</script>

<style>
.analytics-settings-table {
  margin-bottom: 0;
}
.analytics-settings-table > thead > tr > th {
  border-top: none
}
</style>
