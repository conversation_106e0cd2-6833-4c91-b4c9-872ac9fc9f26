import axios from 'axios'
import globals from '@/globals'
import { sectionHelper } from '@/shared/details/vehicleMappingHelpers'
import UIModificationDirector from '../../shared/details/UIModificationBuilders/Director'
import MotorcycleModifier from '../../shared/details/UIModificationBuilders/MotorcycleModifier'
import BoatModifier from '../../shared/details/UIModificationBuilders/BoatModifier'

export default {
  namespaced: true,
  state: {
    vehicle: null,
    vehicleOriginal: null, // returned by api, to compare
    metadata: null,
    accountSettings: null,
    siteSettings: null,
    vehiclePhotoSettings: null,
    featuresMetadata: null,
    inventoryAccountSettings: null,
    dataOriginal: null
  },
  getters: {
    vehicle: state => state.vehicle,
    metadata: state => state.metadata,
    accountSettings: state => state.accountSettings,
    siteSettings: state => state.siteSettings,
    vehiclePhotoSettings: state => state.vehiclePhotoSettings,
    vehicleOriginal: state => state.vehicleOriginal,
    featuresMetadata: state => (state.metadata || {}).vehicleFeatureSections,
    featureChecklistMetadata: state => ((state.metadata || {}).vehicleFeatureSections || {}).standartFeatureChecklist,
    tiresAndWheelsMetadata: state => ((state.metadata || {}).vehicleFeatureSections || {}).tiresAndWheels,
    vehicleHistoryMetadata: state => ((state.metadata || {}).vehicleFeatureSections || {}).vehicleHistory,
    conditionMetadata: state => ((state.metadata || {}).vehicleFeatureSections || {}).condition,
    truckAttributes: state => ((state.metadata || {}).vehicleFeatureSections || {}).truckAttributes,
    rvDetails: state => ((state.metadata || {}).vehicleFeatureSections || {}).RVDetails,
    atvDetails: state => ((state.metadata || {}).vehicleFeatureSections || {}).ATVDetails,
    miscDetails: state => ((state.metadata || {}).vehicleFeatureSections || {}).miscDetails,
    motorcycleUpgrades: state => ((state.metadata || {}).vehicleFeatureSections || {}).motorcycleUpgrades,
    boatDetails: state => ((state.metadata || {}).vehicleFeatureSections || {}).boatDetails,
    snowDetails: state => ((state.metadata || {}).vehicleFeatureSections || {}).snowDetails,
    inventoryAccountSettings: state => state.inventoryAccountSettings
  },
  mutations: {
    setVehicleData (state, data) {
      state.vehicle = data.vehicle
      state.vehicle.isModified = false // For photos and videos which don't make any changes in vehicle model
      state.vehicleOriginal = globals().getClonedValue(data.vehicle)
      state.metadata = getPreparedMetadata(data)
      state.accountSettings = data.accountSettings
      state.siteSettings = data.siteSettings
      state.vehiclePhotoSettings = data.accountInventorySettings.photoSettings
      state.dataOriginal = globals().getClonedValue(data) // need to rollback changes
    },
    setInventoryAccountSettings (state, data) {
      state.inventoryAccountSettings = data
    },
    reset (state) {
      state.vehicle = state.dataOriginal.vehicle
      state.vehicle.isModified = false
      state.vehicleOriginal = globals().getClonedValue(state.dataOriginal.vehicle)
      state.metadata = getPreparedMetadata(state.dataOriginal)
      state.accountSettings = state.dataOriginal.accountSettings
      state.siteSettings = state.dataOriginal.siteSettings
      state.vehiclePhotoSettings = state.dataOriginal.vehiclePhotoSettings
      state.dataOriginal = globals().getClonedValue(state.dataOriginal)
    }
  },
  actions: {
    async populateBaseVehicleData ({ commit }, parameters) {
      const result = await axios.get(`/api/inventory/${parameters.accountId}/${parameters.vin}`)
      commit('setVehicleData', result.data)
      return result.data
    },
    async populateInventoryAccountSettings ({ commit }, parameters) {
      const result = await axios.get(`/api/inventory/${parameters.accountId}/settings`)
      commit('setInventoryAccountSettings', result.data)
      return result.data
    }
  }
}

function getPreparedMetadata (detailBuilderModel) {
  let featuresMetadata = {}
  let vehicleTypesSectionIdMapping = sectionHelper.getVehicleTypesSectionIdMapping()

  let metadata = prepareStandardFeaturesMetadata(detailBuilderModel.metadata, detailBuilderModel.vehicle.vehicleType)

  for (let propName in vehicleTypesSectionIdMapping) {
    featuresMetadata[propName] = getMappedVehicleAndMetadataFeatureSection(
      detailBuilderModel.vehicle,
      metadata,
      vehicleTypesSectionIdMapping[propName][detailBuilderModel.vehicle.vehicleType]
    )
  }

  metadata.vehicleFeatureSections = featuresMetadata

  return metadata
}

function applyUIModifications (feature) {
  const director = new UIModificationDirector([
    new MotorcycleModifier(),
    new BoatModifier()
  ])

  director.construct(feature)

  return feature
}

function getMappedVehicleAndMetadataFeatureSection (vehicle, metadata, featureCategoryId) {
  if (!metadata.vehicleFeatureSections || metadata.vehicleFeatureSections.length === 0 || featureCategoryId === undefined) {
    return null
  }

  let section = findSection(metadata.vehicleFeatureSections, featureCategoryId)

  if (!section) {
    return null
  }

  let features = vehicle.features.filter(x => x.featureCategory === section.featuresCategory)

  let featureCategory = (vehicle.featureCategories || []).find(x => x.featureCategoryType === section.featuresCategory)
  let featuresWithMetadata = features.map(x => applyUIModifications(Object.assign(x, metadata.vehicleFeatureMetadata[x.id])))

  let res = {
    ...section,
    features: featuresWithMetadata,
    featureCategory: featureCategory
  }

  if (res.subsections && res.subsections.length > 0) {
    for (let i = 0; i < res.subsections.length; i++) {
      res.subsections[i] = getMappedVehicleAndMetadataFeatureSection(vehicle, metadata, res.subsections[i].featuresCategory)
    }
  }

  return res
}

function findSection (sections, categoryId) {
  let res = null
  for (let i = 0; i < sections.length; i++) {
    if (sections[i].featuresCategory === categoryId) {
      return sections[i]
    }
    if (sections[i].subsections && sections[i].subsections.length > 0) {
      res = findSection(sections[i].subsections, categoryId)

      if (res) {
        return res
      }
    }
  }
}

function prepareStandardFeaturesMetadata (metadata, vehicleType) {
  let newMetadata = JSON.parse(JSON.stringify(metadata))

  // The passenger vehicle type has the 'Standard Features Checklist' section, we form this section for other vehicle types.

  if (newMetadata.vehicleFeatureSections.some(x => x.featuresCategory === 0)) {
    return newMetadata
  }

  let standardFeatureChecklist = {
    featuresCategory: 0,
    name: 'Standard Features Checklist',
    subsections: []
  }

  for (let key in newMetadata.vehicleFeatureSections) {
    let featureSection = newMetadata.vehicleFeatureSections[key]
    if ([502, 708, 1008].includes(featureSection.featuresCategory)) {
      standardFeatureChecklist.subsections.push({
        featuresCategory: featureSection.featuresCategory,
        name: featureSection.name,
        subsections: []
      })
    } else if ([503, 703, 1001].includes(featureSection.featuresCategory)) {
      standardFeatureChecklist.subsections.push({
        featuresCategory: featureSection.featuresCategory,
        name: featureSection.name,
        subsections: []
      })
    } else if ([504, 704, 1002].includes(featureSection.featuresCategory)) {
      standardFeatureChecklist.subsections.push({
        featuresCategory: featureSection.featuresCategory,
        name: featureSection.name,
        subsections: []
      })
    } else if ([400, 505, 1003].includes(featureSection.featuresCategory)) {
      standardFeatureChecklist.subsections.push({
        featuresCategory: featureSection.featuresCategory,
        name: featureSection.name,
        subsections: []
      })
    } else if ([506, 705, 1005].includes(featureSection.featuresCategory)) {
      standardFeatureChecklist.subsections.push({
        featuresCategory: featureSection.featuresCategory,
        name: featureSection.name,
        subsections: []
      })
    } else if ([402, 507, 709, 1004].includes(featureSection.featuresCategory)) {
      standardFeatureChecklist.subsections.push({
        featuresCategory: featureSection.featuresCategory,
        name: featureSection.name,
        subsections: []
      })
    } else if ([509, 701].includes(featureSection.featuresCategory)) {
      standardFeatureChecklist.subsections.push({
        featuresCategory: featureSection.featuresCategory,
        name: featureSection.name,
        subsections: []
      })
    } else if ([403].includes(featureSection.featuresCategory)) {
      standardFeatureChecklist.subsections.push({
        featuresCategory: featureSection.featuresCategory,
        name: featureSection.name,
        subsections: []
      })
    }
  }

  newMetadata.vehicleFeatureSections.push(standardFeatureChecklist)

  return newMetadata
}
