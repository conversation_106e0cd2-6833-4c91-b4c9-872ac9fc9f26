<template>
  <details-section :title="tiresAndWheelsMetadata.name" @cancel="onCancel" v-model="mode" v-if="tiresAndWheelsMetadata" canBeHidden :visible="tiresAndWheelsMetadata.featureCategory.isDisplayed" @visibilityChange='onVisibilityChange'>
    <div class="view" v-if="mode === 'view'">

      <auto-detail-row :title="getTireBrand.name" :text="getTireBrand.value"/>

      <detail-row>
        <span slot="title">{{GetTireWidthFOL.name}}</span>
        <span slot="payload"> {{GetTireWidthFOL.value || '-'}} / {{GetTireRatioFOL.value || '-'}} / {{GetTireSpeedFOL.value}}{{GetTireDiameterFOL.value || '-'}}</span>
      </detail-row>

       <detail-row>
        <span slot="title">{{GetTireWidthRear.name}}</span>
        <span slot="payload"> {{GetTireWidthRear.value || '-'}} / {{GetTireRatioRear.value || '-'}} / {{GetTireSpeedRear.value}}{{GetTireDiameterRear.value || '-'}}</span>
      </detail-row>

      <auto-detail-row :title="GetTireTreadRemaining.name" :text="GetTireTreadRemaining.value"/>

      <auto-detail-row :title="GetWheelType.name" :text="GetWheelType.value"/>

      <auto-detail-row :title="GetSpareCondition.name" :text="GetSpareCondition.value"/>

      <auto-detail-row :title="GetAdditionalWheelInfo.name" :text="GetAdditionalWheelInfo.value"/>

    </div>
    <div class="tire-and-wheels edit" v-else-if="mode === 'edit'">
      <ValidationProvider rules="max:50|xml" name="Tire brand" v-slot="{errors}">
        <detail-row editMode mobileWrap :error="errors[0]">
          <span slot="title">{{getTireBrand.name}}</span>
          <custom-select slot="payload"
                        v-model="getTireBrand.value"
                        :customSelectValue="{inputVal: getTireBrand.value,selectVal: getTireBrand.value}"
                        :options="getNameValueOptions(getTireBrand.nameValueOptions)"
                        @change="changeTireBrand"
                        name="Tire brand"/>
        </detail-row>
      </ValidationProvider>
      <detail-row fixedPayloadWidth editMode>
        <span slot="title">{{GetTireWidthFOL.name}}</span>
        <div slot="payload" class="d-flex tire-select-block">

          <b-form-select class="custom-size" v-model="GetTireWidthFOL.value" :options="getNameValueOptions(GetTireWidthFOL.nameValueOptions)"></b-form-select>

          <b-form-select class="custom-size" v-model="GetTireRatioFOL.value" :options="getNameValueOptions(GetTireRatioFOL.nameValueOptions)"></b-form-select>

          <b-form-select class="custom-size" v-model="GetTireSpeedFOL.value" :options="getNameValueOptions(GetTireSpeedFOL.nameValueOptions)"></b-form-select>

          <b-form-select class="custom-size" v-model="GetTireDiameterFOL.value" :options="getNameValueOptions(GetTireDiameterFOL.nameValueOptions)"></b-form-select>

        </div>
      </detail-row>

      <detail-row fixedPayloadWidth editMode>
        <span slot="title">{{GetTireWidthRear.name}}</span>
        <div slot="payload" class="d-flex tire-select-block">

          <b-form-select class="custom-size" v-model="GetTireWidthRear.value" :options="getNameValueOptions(GetTireWidthRear.nameValueOptions)"></b-form-select>

          <b-form-select class="custom-size" v-model="GetTireRatioRear.value" :options="getNameValueOptions(GetTireRatioRear.nameValueOptions)"></b-form-select>

          <b-form-select class="custom-size" v-model="GetTireSpeedRear.value" :options="getNameValueOptions(GetTireSpeedRear.nameValueOptions)"></b-form-select>

          <b-form-select class="custom-size" v-model="GetTireDiameterRear.value" :options="getNameValueOptions(GetTireDiameterRear.nameValueOptions)"></b-form-select>

        </div>
      </detail-row>

      <auto-detail-row :title="GetTireTreadRemaining.name" v-model="GetTireTreadRemaining.value" :options="getNameValueOptions(GetTireTreadRemaining.nameValueOptions)"/>

      <auto-detail-row :title="GetWheelType.name" v-model="GetWheelType.value" :options="getNameValueOptions(GetWheelType.nameValueOptions)"/>

      <auto-detail-row :title="GetSpareCondition.name" v-model="GetSpareCondition.value" :options="getNameValueOptions(GetSpareCondition.nameValueOptions)"/>

      <ValidationProvider rules="max:50|xml" name="Additional wheel info" v-slot="{errors}">
       <detail-row fixedPayloadWidth editMode :error="errors[0]">
          <span slot="title">{{GetAdditionalWheelInfo.name}}</span>
          <b-form-textarea
            slot="payload"
            v-model="GetAdditionalWheelInfo.value"
            :rows="3"
            :max-rows="6"
            name="Additional wheel info"
          />
        </detail-row>
      </ValidationProvider>
    </div>
  </details-section>
</template>

<script>
import {mapGetters} from 'vuex'
import detailsSection from '@/components/details/detailsSection'
import selectWithCustomValue from '../helpers/selectWithCustomValue'
import detailRow from '../helpers/detailRow'
import {tireHelper} from '@/shared/details/vehicleMappingHelpers'
import autoDetailRow from '../helpers/autoDetailRow'

export default {
  name: 'vehicle-history-section',
  data () {
    return {
      mode: 'view'
    }
  },
  computed: {
    ...mapGetters('details', ['vehicle', 'tiresAndWheelsMetadata']),
    getTireBrand () {
      return this.tiresAndWheelsMetadata.features.find(x => x.id === tireHelper.GetTireBrandKey(this.vehicle.vehicleType))
    },
    GetTireWidthFOL () {
      return this.tiresAndWheelsMetadata.features.find(x => x.id === tireHelper.GetTireWidthFOLKey(this.vehicle.vehicleType))
    },
    GetTireRatioFOL () {
      return this.tiresAndWheelsMetadata.features.find(x => x.id === tireHelper.GetTireRatioFOLKey(this.vehicle.vehicleType))
    },
    GetTireSpeedFOL () {
      return this.tiresAndWheelsMetadata.features.find(x => x.id === tireHelper.GetTireSpeedFOLKey(this.vehicle.vehicleType))
    },
    GetTireDiameterFOL () {
      return this.tiresAndWheelsMetadata.features.find(x => x.id === tireHelper.GetTireDiameterFOLKey(this.vehicle.vehicleType))
    },
    GetTireTreadRemaining () {
      return this.tiresAndWheelsMetadata.features.find(x => x.id === tireHelper.GetTireTreadRemainingKey(this.vehicle.vehicleType))
    },
    GetSpareCondition () {
      return this.tiresAndWheelsMetadata.features.find(x => x.id === tireHelper.GetSpareConditionKey(this.vehicle.vehicleType))
    },
    GetWheelType () {
      return this.tiresAndWheelsMetadata.features.find(x => x.id === tireHelper.GetWheelTypeKey(this.vehicle.vehicleType))
    },
    GetAdditionalWheelInfo () {
      return this.tiresAndWheelsMetadata.features.find(x => x.id === tireHelper.GetAdditionalWheelInfoKey(this.vehicle.vehicleType))
    },
    GetTireWidthRear () {
      return this.tiresAndWheelsMetadata.features.find(x => x.id === tireHelper.GetTireWidthRearKey(this.vehicle.vehicleType))
    },
    GetTireRatioRear () {
      return this.tiresAndWheelsMetadata.features.find(x => x.id === tireHelper.GetTireRatioRearKey(this.vehicle.vehicleType))
    },
    GetTireSpeedRear () {
      return this.tiresAndWheelsMetadata.features.find(x => x.id === tireHelper.GetTireSpeedRearKey(this.vehicle.vehicleType))
    },
    GetTireDiameterRear () {
      return this.tiresAndWheelsMetadata.features.find(x => x.id === tireHelper.GetTireDiameterRearKey(this.vehicle.vehicleType))
    }
  },
  methods: {
    onVisibilityChange (val) {
      this.tiresAndWheelsMetadata.featureCategory.isDisplayed = val
    },
    getNameValueOptions (nameValueOptions) {
      return nameValueOptions.map(x => ({
        value: x.value,
        text: x.key
      }))
    },
    changeTireBrand (newTireBrand) {
      this.getTireBrand.value = newTireBrand.text
    },
    onCancel () {
      this.$forceUpdate()
    }
  },
  components: {
    'details-section': detailsSection,
    'custom-select': selectWithCustomValue,
    'detail-row': detailRow,
    'auto-detail-row': autoDetailRow
  }
}
</script>

<style>
 .tire-and-wheels.edit select.custom-size {
   padding: 0.138rem 0.625rem 0.138rem 0.625rem;
 }

 .tire-select-block {
   justify-content: space-between;
   width: 100%;
 }

 .tire-select-block > select {
   max-width: 67px;
 }
</style>
