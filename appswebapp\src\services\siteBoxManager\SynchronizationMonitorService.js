import BaseService from './../BaseService'

class SynchronizationMonitorService extends BaseService {
  getSynchronizationLogInfo () {
    return this.axios.get('/api/siteboxmanager/synchronizationmonitor/synclogs')
  };
  getAppsSynchronizerProcessorsInfo () {
    return this.axios.get('/api/siteboxmanager/synchronizationmonitor/appssyncprocessors/statistics')
  };
  getVehicleAccountSynchronizationListing (filter) {
    return this.axios.get('/api/siteboxmanager/synchronizationmonitor/appsaccountvehiclesync/statistics', { params: filter })
  };
  getFoundationSettingsFetcherListingInfo () {
    return this.axios.get('/api/siteboxmanager/synchronizationmonitor/foundationsettingsfetcher/statistics')
  };
  getFoundationSettingsFetchingStatistic (filter) {
    return this.axios.get('/api/siteboxmanager/synchronizationmonitor/foundationsettingsfetcher/statistics/details', { params: filter })
  };
  getSiteBoxVehicleFetcherListingInfo () {
    return this.axios.get('/api/siteboxmanager/synchronizationmonitor/siteboxvehiclefetcher/statistics')
  };
  getSiteBoxSettingsFetcherListingInfo () {
    return this.axios.get('/api/siteboxmanager/synchronizationmonitor/siteboxsettingsfetcher/statistics')
  };
  getVehicleFetchingStatistic (filter) {
    return this.axios.get('/api/siteboxmanager/synchronizationmonitor/siteboxvehiclefetcher/statistics/details', { params: filter })
  };
  getGallerySettingsFetchingStatistic (filter) {
    return this.axios.get('/api/siteboxmanager/synchronizationmonitor/siteboxsettingsfetcher/statistics/details', { params: filter })
  };
}

export default new SynchronizationMonitorService()
