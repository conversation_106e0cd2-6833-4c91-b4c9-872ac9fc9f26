<template>
  <div class="ebiz-audio-player" v-if="!loadSourceOnPlay">
    <audio ref="player" :autoplay="autoplay" style="display: none">
      <source :src="getSource">
    </audio>
    <div>
      <b-btn v-if="isToggleButtonEnabled" :id="id" variant="light" :class="paginationClass" class="text-dark" @click="toggleAudio" :size="getSize">
        <b-icon v-if="!isPlaying" icon="play-fill" :scale="getIconScale"></b-icon>
        <b-icon v-else icon="stop-circle-fill" :scale="getIconScale-0.3"></b-icon>
      </b-btn>
      <template v-else>
        <span v-if="customTextNotFileLoaded"> {{ customTextNotFileLoaded }}</span>
        <b-icon v-else icon="file-earmark-x-fill" :scale="getIconScale" title="Audio is Unavailable"></b-icon>
      </template>

      <b-popover v-if="canplay" custom-class="custom-popover" :target="id" :show="isPlaying" :triggers="['hover']" boundary-padding="0">
        <b-input-group :size="getSize">
          <b-input-group-prepend>
            <b-input-group-text class="bg-light">
              {{ elapsedTime() }}
            </b-input-group-text>
          </b-input-group-prepend>
          <b-form-input v-model="playbackTime" step="0.01" type="range" min="0" :max="audioDuration"></b-form-input>
          <b-input-group-append>
            <b-input-group-text class="bg-light">
              {{ totalTime() }}
            </b-input-group-text>
          </b-input-group-append>
        </b-input-group>
      </b-popover>
    </div>
  </div>
  <div v-else>
    <b-btn v-if="!isAudioSourceLoaded" :id="id" variant="light" :class="paginationClass" class="text-dark" @click="loadSource" :size="getSize">
      <b-icon v-if="!isAudioSourceLoadPerforming" icon="play-fill" :scale="getIconScale"></b-icon>
      <b-icon v-else icon="circle-fill" animation="throb" :scale="getIconScale"></b-icon>
    </b-btn>
    <audio-player v-else :source="getSource" autoplay :type="type" />
  </div>
</template>

<script>
const audioTypeMapper = {
  mp3: 'audio/mpeg',
  ogg: 'audio/ogg',
  wav: 'audio/wav'
}

const sizeOptions = ['sm', 'md', 'lg', '']

const sizeScaleMapper = {
  sm: 0.9,
  md: 1.4,
  lg: 1.9
}

const sizePaginationMapper = {
  sm: 'px-1 py-1',
  md: 'px-2 py-2',
  lg: 'px-3 py-3'
}
export default {
  name: 'audio-player',
  props: {
    source: String,
    type: { type: String, validator (val) { return !!audioTypeMapper[val] } },
    size: { type: String, validator (val) { return sizeOptions.includes(val) } },
    customTextNotFileLoaded: String,
    loadSourceFunc: Function,
    loadSourceOnPlay: Boolean,
    autoplay: Boolean
  },
  data () {
    return {
      id: this.$uuid.v4(),
      playbackTime: 0,
      audioDuration: 100,
      audioSource: '',
      canplay: false,
      isPlaying: false,
      isAudioSourceLoaded: false,
      isAudioSourceLoadPerforming: false
    }
  },
  created () {
    if (this.source) {
      this.audioSource = this.source
    }
  },
  computed: {
    getSize () {
      return this.size || 'sm'
    },
    paginationClass () {
      return sizePaginationMapper[this.getSize]
    },
    getIconScale () {
      return sizeScaleMapper[this.getSize]
    },
    getType () {
      return audioTypeMapper[this.type]
    },
    getSource () {
      return this.audioSource
    },
    isToggleButtonEnabled () {
      return this.canplay
    }
  },
  methods: {
    initSlider () {
      let audio = this.$refs.player
      if (audio) {
        this.audioDuration = Math.round(audio.duration)
      }
    },
    convertTime (seconds) {
      const format = val => `0${Math.floor(val)}`.slice(-2)
      let minutes = (seconds % 3600) / 60
      return [minutes, seconds % 60].map(format).join(':')
    },
    totalTime () {
      let audio = this.$refs.player
      if (audio) {
        let seconds = audio.duration
        return this.convertTime(seconds)
      } else {
        return '00:00'
      }
    },
    elapsedTime () {
      let audio = this.$refs.player
      if (audio) {
        let seconds = audio.currentTime
        return this.convertTime(seconds)
      } else {
        return '00:00'
      }
    },
    playbackListener (e) {
      let audio = this.$refs.player
      this.playbackTime = audio.currentTime
      audio.addEventListener('ended', this.endListener)
      audio.addEventListener('pause', this.pauseListener)
    },
    pauseListener () {
      this.isPlaying = false
      this.listenerActive = false
      this.cleanupListeners()
    },
    endListener () {
      this.isPlaying = false
      this.listenerActive = false
      this.cleanupListeners()
    },
    cleanupListeners () {
      let audio = this.$refs.player
      audio.removeEventListener('timeupdate', this.playbackListener)
      audio.removeEventListener('ended', this.endListener)
      audio.removeEventListener('pause', this.pauseListener)
    },
    async loadSource () {
      try {
        this.isAudioSourceLoadPerforming = true
        this.audioSource = await this.loadSourceFunc()
      } catch (ex) {
        this.$toaster.error('Failed load track from server')
      } finally {
        this.isAudioSourceLoaded = true
        this.isAudioSourceLoadPerforming = false
      }
    },
    toggleAudio () {
      let audio = this.$refs.player

      if (audio.paused) {
        audio.play()
        this.isPlaying = true
      } else {
        audio.pause()
        this.isPlaying = false
      }
    },
    stopPlayingAudioEvenHandler (e) {
      if (e.playerId !== this.id) {
        if (this.isPlaying) {
          this.toggleAudio()
        }
      }
    },
    sendStopPlayingAudioEvent () {
      var event = new CustomEvent('stop-playing-audio', { 'playerId': this.id })
      document.dispatchEvent(event)
    }
  },
  mounted: function () {
    if (!this.source) {
      return
    }
    this.$nextTick(function () {
      let audio = this.$refs.player
      audio.addEventListener(
        'loadedmetadata',
        function (e) {
          this.initSlider()
        }.bind(this)
      )
      audio.addEventListener(
        'canplay',
        function (e) {
          this.canplay = true
        }.bind(this)
      )

      this.$watch('isPlaying', function () {
        if (this.isPlaying) {
          let audio = this.$refs.player
          this.initSlider()
          if (!this.listenerActive) {
            this.listenerActive = true
            audio.addEventListener('timeupdate', this.playbackListener)
          }
          this.sendStopPlayingAudioEvent()
        }
      })

      this.$watch('playbackTime', function () {
        let diff = Math.abs(this.playbackTime - this.$refs.player.currentTime)
        if (diff > 0.01) {
          this.$refs.player.currentTime = this.playbackTime
        }
      })

      if (this.autoplay) {
        this.isPlaying = true
      }

      document.addEventListener('stop-playing-audio', this.stopPlayingAudioEvenHandler)
    })
  }
}
</script>

<style>
.ebiz-audio-player .custom-popover {
  padding: 0;
}
</style>
