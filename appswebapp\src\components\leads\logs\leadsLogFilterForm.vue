<template>
<div>
  <div>
    <div class="leads-logs-filter">
      <b-form-input class="leads-log-filter-item leads-log-filter-account-id" :value="accountIdComputed" @input="accountIdComputed = $event" placeholder="Account ID"></b-form-input>
      <b-form-input class="leads-log-filter-item" v-if='logTypes.statistic.key !== logType' :placeholder='getPlaceholder' v-model='filter.search'/>
      <b-form-select class="leads-log-filter-item leads-log-filter-dropdown" v-if='logTypes.gallery.key === logType'
        v-model='filter.spamscanstatus' :options='spamScanStatusTypes'
        text-field='label'
        value-field='key'>
      </b-form-select>
      <b-form-select class="leads-log-filter-item leads-log-filter-dropdown" v-if='logTypes.gallery.key === logType'
        v-model='filter.turnstilevalidationstatus' :options='turnstileValidationStatusTypes'
        text-field='label'
        value-field='key'>
      </b-form-select>
      <b-form-select class="leads-log-filter-item leads-log-filter-dropdown" v-if='logTypes.gallery.key === logType'
        v-model='filter.bodyscantype' :options='bodyScanTypes'
        text-field='label'
        value-field='key'>
      </b-form-select>
      <b-form-select class="leads-log-filter-item leads-log-filter-dropdown" v-if='logTypes.statistic.key === logType'
        v-model='filter.processingstatus' :options='processingStatusType'
        text-field='label'
        value-field='key'>
      </b-form-select>
      <b-form-select class="leads-log-filter-item leads-log-filter-dropdown" v-if='logTypes.statistic.key === logType'
        v-model='filter.statistictasktype' :options='statisticTypes'
        text-field='label'
        value-field='key'>
      </b-form-select>
      <b-form-select class="leads-log-filter-item leads-log-filter-dropdown" v-if='logTypes.leadsNotification.key === logType'
        v-model='filter.resultstatus' :options='leadsNotificationStatus'
        text-field='label'
        value-field='key'>
      </b-form-select>
      <b-form-select class="leads-log-filter-item leads-log-filter-dropdown" v-if='logTypes.leadsNotification.key === logType'
        v-model='filter.communicationtype' :options='communicationTypes'
        text-field='label'
        value-field='key'>
      </b-form-select>
      <b-form-select class="leads-log-filter-item leads-log-filter-dropdown" v-if='logTypes.synchronization.key === logType'
        v-model='filter.operationtype' :options='synchronizationOperationTypes'
        text-field='label'
        value-field='key'>>
      </b-form-select>
      <b-input-group class="flex-nowrap leads-log-filter-item">
        <b-input-group-prepend is-text>
          <i class="ion ion-md-calendar" slot="prepend"></i>
        </b-input-group-prepend>
        <date-time-picker
          ref="timeFrom"
          v-model="filter.dateFrom"
          :options="filterTimeOptions"
          format="MM/DD/YYYY HH:mm"
          placeholder="Date From"
          className="form-control"
          @change="onTimeFromInputChange" />
        <b-input-group-append
          is-text
          v-show="filter.dateFrom"
          @click="filter.dateFrom = null" >
          <i class="ion ion-md-close"></i>
        </b-input-group-append>
      </b-input-group>
      <b-input-group class="flex-nowrap leads-log-filter-item">
        <b-input-group-prepend is-text>
          <i class="ion ion-md-calendar" slot="prepend"></i>
        </b-input-group-prepend>
        <date-time-picker
          ref="timeTo"
          v-model="filter.dateTo"
          :options="filterTimeOptions"
          format="MM/DD/YYYY HH:mm"
          placeholder="Date To"
          className="form-control"
          @change="onTimeToInputChange" />
        <b-input-group-append
          is-text
          v-show="filter.dateTo"
          @click="filter.dateTo = null" >
          <i class="ion ion-md-close"></i>
        </b-input-group-append>
      </b-input-group>
      <b-btn block class="leads-log-filter-item" variant="primary" type="submit" @click="applyFilter">Submit</b-btn>
    </div>
  </div>
</div>
</template>

<script>
import { logTypes, processingStatusType } from '@/shared/leads/common'
import { statisticTypes, communicationTypes, synchronizationOperationTypes, leadsNotificationStatus, bodyScanTypes, spamScanStatusTypes, turnstileValidationStatusTypes } from '@/shared/leads/logs/logsCommon'

export default {
  name: 'leads-log-filter-form',
  props: {
    filter: { type: Object, required: true },
    logType: { type: Number, required: true }
  },
  data () {
    return {
      logTypes,
      processingStatusType,
      statisticTypes,
      leadsNotificationStatus,
      bodyScanTypes,
      spamScanStatusTypes,
      turnstileValidationStatusTypes,
      communicationTypes,
      synchronizationOperationTypes,
      filterTimeOptions: {
        startDate: new Date(),
        autoUpdateInput: false,
        singleDatePicker: true,
        timePicker: true,
        timePicker24Hour: true,
        maxDate: new Date()
      }
    }
  },
  components: {
    'date-time-picker': () => import('@gravitano/vue-date-range-picker/src/components/DateRangePicker')
  },
  computed: {
    getPlaceholder () {
      let placeholder = ''

      switch (this.logType) {
        case this.logTypes.voice.key:
        case this.logTypes.sms.key:
        case this.logTypes.serviceLog.key:
          placeholder = 'Phone Number'
          break
        case this.logTypes.email.key:
          placeholder = 'Email'
          break
        case this.logTypes.gallery.key:
          placeholder = 'Phone / Email / IP / Subnet / URL'
          break
        case this.logTypes.leadsNotification.key:
          placeholder = 'Phone Number or Email'
          break
        case this.logTypes.eBay.key:
          placeholder = 'Email or User Id'
          break
        case this.logTypes.synchronization.key:
          placeholder = 'Lead Thread Id or Source Id'
          break
      }

      return placeholder
    },
    refDateTimeFrom () {
      return (this.$refs.timeFrom || {}).$el || {}
    },
    refDateTimeTo () {
      return (this.$refs.timeTo || {}).$el || {}
    },
    accountIdComputed: {
      get () {
        return this.filter.accountId === 0 ? null : this.filter.accountId
      },
      set (value) {
        this.filter.accountId = value === null || value === '' ? 0 : value
      }
    }
  },
  methods: {
    onTimeFromInputChange (newVal) {
      this.refDateTimeFrom.value = newVal || this.filter.dateFrom || null
    },
    onTimeToInputChange (newVal) {
      this.refDateTimeTo.value = newVal || this.filter.dateTo || null
    },
    applyFilter () {
      this.$emit('applyFilter')
    }
  }
}
</script>

<style>
.leads-logs-filter {
  margin: 20px 10px;
  display: flex;
  flex-shrink: 1;
  flex-direction: row;
}
.leads-log-filter-item {
  margin: 3px 3px;
}
.leads-log-filter-dropdown {
  max-width: 200px;
  min-width: 180px;
}
.leads-log-filter-account-id {
  max-width: 140px;
  min-width: 120px;
}
@media (max-width: 1200px) {
  .leads-logs-filter {
    display: flex;
    flex-shrink: 1;
    flex-direction: column;
  }
  .leads-log-filter-dropdown {
    max-width: none;
    min-width: auto;
  }
  .leads-log-filter-account-id {
    max-width: none;
    min-width: auto;
  }
}
</style>
