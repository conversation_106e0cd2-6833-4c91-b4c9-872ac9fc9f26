<template>
  <b-row class="mt-1">
    <b-col xl="3" lg="4" md="4 mt-1" :class='textVariant'><b>{{text}}</b></b-col>
    <b-col xl="3" lg="4" md="4 mt-1" :class='textVariant'>{{description}}</b-col>
    <b-col xl="3" lg="4" md="4 mt-1" :class='textVariant'>{{dateTime}}</b-col>
  </b-row>
</template>

<script>
import { vehicleModificationSourceTypes, vehicleProcessingStepStatusTypes } from '@/shared/inventory/inventoryTypes'
import moment from 'moment'

export default {
  props: {
    title: { type: String, required: true },
    data: { type: Object },
    isModificationRequest: { type: Boolean }
  },
  data () {
    return {
      description: 'Unprocessed',
      textVariant: 'text-secondary',
      dateTime: '',
      text: this.title + ':'
    }
  },
  mounted () {
    this.initDate()
  },
  methods: {
    initDate () {
      if (this.data) {
        if (this.isModificationRequest) {
          this.initModificationSourceDetail(this.data)
        } else {
          this.initOperationResultDetail(this.data)
        }
      } else {
        this.description = 'Unprocessed'
        this.textVariant = 'text-secondary'
        this.dateTime = ''
      }
    },
    initModificationSourceDetail (value) {
      if (value.operationResult.dateTimeOccured) {
        this.dateTime = moment(value.operationResult.dateTimeOccured).format('MM/DD/YYYY hh:mm:ss A')
      }

      switch (value.operationResult.modificationSource) {
        case vehicleModificationSourceTypes.webApp.value:
          this.description = `${vehicleModificationSourceTypes.webApp.text} (${value.ipAddress})`
          break
        case vehicleModificationSourceTypes.iOSApp.value:
          this.description = vehicleModificationSourceTypes.iOSApp.text
          break
        case vehicleModificationSourceTypes.androidApp.value:
          this.description = vehicleModificationSourceTypes.androidApp.text
          break
        case vehicleModificationSourceTypes.vehicleSynchronizer.value:
          this.text = 'Picked up by Apps:'
          this.description = 'Success'
          break
        case vehicleModificationSourceTypes.historyReportChecker.value:
          this.text = ''
          this.description = ''
          this.dateTime = ''
          return
      }

      this.textVariant = this.getTextVariant(value.operationResult.processingResult)
    },
    initOperationResultDetail (value) {
      if (value.dateTimeOccured) {
        this.dateTime = moment(value.dateTimeOccured).format('MM/DD/YYYY hh:mm:ss A')
      }

      switch (value.processingResult) {
        case vehicleProcessingStepStatusTypes.success.value:
          this.description = vehicleProcessingStepStatusTypes.success.text
          break
        case vehicleProcessingStepStatusTypes.failed.value:
          this.description = vehicleProcessingStepStatusTypes.failed.text
          break
        case vehicleProcessingStepStatusTypes.notNeeded.value:
          this.description = vehicleProcessingStepStatusTypes.notNeeded.text
          this.dateTime = ''
          break
        case vehicleProcessingStepStatusTypes.skipped.value:
          this.description = vehicleProcessingStepStatusTypes.skipped.text
          this.dateTime = ''
          break
        case vehicleProcessingStepStatusTypes.carfaxRequested.value:
          this.description = vehicleProcessingStepStatusTypes.carfaxRequested.text
          break
        case vehicleProcessingStepStatusTypes.autocheckRequested.value:
          this.description = vehicleProcessingStepStatusTypes.autocheckRequested.text
          break
        case vehicleProcessingStepStatusTypes.notExist.value:
          this.description = vehicleProcessingStepStatusTypes.notExist.text
          break
        case vehicleProcessingStepStatusTypes.validForGallery.value:
          this.description = vehicleProcessingStepStatusTypes.validForGallery.text
          break
        case vehicleProcessingStepStatusTypes.validForApps.value:
          this.description = vehicleProcessingStepStatusTypes.validForApps.text
          break
        case vehicleProcessingStepStatusTypes.notValid.value:
          this.description = vehicleProcessingStepStatusTypes.notValid.text
          break
      }

      this.textVariant = this.getTextVariant(value.processingResult)
    },
    getTextVariant (status) {
      switch (status) {
        case vehicleProcessingStepStatusTypes.success.value:
        case vehicleProcessingStepStatusTypes.carfaxRequested.value:
        case vehicleProcessingStepStatusTypes.autocheckRequested.value:
        case vehicleProcessingStepStatusTypes.validForGallery.value:
        case vehicleProcessingStepStatusTypes.validForApps.value:
          return 'text-success'
        case vehicleProcessingStepStatusTypes.failed.value:
        case vehicleProcessingStepStatusTypes.notValid.value:
        case vehicleProcessingStepStatusTypes.notExist.value:
          return 'text-danger'
        default:
          return 'text-secondary'
      }
    }
  },
  watch: {
    data: {
      deep: true,
      handler () {
        this.initDate()
      }
    }
  }
}
</script>
