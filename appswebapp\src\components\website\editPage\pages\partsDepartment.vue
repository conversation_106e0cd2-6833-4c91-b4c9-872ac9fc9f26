<template>
  <div>
    <page-status-section :page-edit-model="pageEditModel"/>
    <text-html-content-section v-model="pageEditModel.pageSettings.description"/>
  </div>
</template>

<script>
import pageStatusSection from '../sections/pageStatusSection'
import textHtmlContentSection from '../sections/textHtmlContentSection'

export default {
  props: {
    pageEditModel: {type: Object, required: true}
  },
  components: {
    pageStatusSection,
    textHtmlContentSection
  }
}
</script>
