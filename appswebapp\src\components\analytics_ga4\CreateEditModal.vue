<template>

  <ValidationObserver v-slot="{ invalid }">
    <b-modal
        :visible="show"
        :title="modalTittle"
        @hide="hideModal"
        @shown="fillData"
        body-class="create-edit-modal"
    >
        <template #modal-footer>
          <b-btn variant="secondary" @click="hideModal">Cancel</b-btn>
          <b-btn variant="primary" @click="sendReportGroup" :disabled="invalid">{{okButtonTittle}}</b-btn>
        </template>
        <ValidationProvider immediate name="Group Name" rules="required" v-slot="{errors}">
          <b-row class="mt-2">
            <b-col md="4" class="edit-row-caption">
              Group Name
            </b-col>
            <b-col md="8" class="position-relative">
              <b-form-input
                v-model="currentReportGroup.groupName"
                placeholder="Enter group name"
                :state="errors[0] ? false : null"
                name="Group Name"
              >
              </b-form-input>

              <b-btn variant="link" class="input-helper-link" v-if="!isEdit" @click="useGroupName">Use selected group name</b-btn>
            </b-col>
          </b-row>

          <b-row>
            <b-col class="d-flex justify-content-end text-error">
              <span>{{ errors[0] }}</span>
            </b-col>
          </b-row>
        </ValidationProvider>

        <ValidationProvider immediate name="Select Account Group" rules="required" v-slot="{errors}">
          <b-row class="mt-4" v-show="!isEdit">
            <b-col md="4" class="edit-row-caption">Select Account Group</b-col>
            <b-col md="8">
              <b-form-select
                v-model="currentReportGroup.accountsGroupId"
                :options="groupsListingForSelect"
                @input="changeGroup"
                name="Select Account Group"
                :state="errors[0] ? false : null"
              ></b-form-select>
            </b-col>
          </b-row>

          <b-row v-show="!isEdit">
            <b-col class="d-flex justify-content-end text-error">
              <span>{{ errors[0] }}</span>
            </b-col>
          </b-row>
        </ValidationProvider>
        <ValidationProvider immediate name="Report Account(s)" rules="required|min_args:2" v-slot="{errors}">
          <b-row class="mt-4">
            <b-col md="4" class="edit-row-caption">Report Account(s)</b-col>
            <b-col md="8">
              <multiselect
                v-model="currentReportGroup.reportAccounts"
                :options="accountsListing || []"
                :multiple="true"
                :preserve-search="true"
                :close-on-select="false"
                :clear-on-select="false"
                placeholder="Search..."
                :show-labels="false"
                track-by="accountId"
                :class="errors[0] ? 'input-error': ''"
                name="Report Account(s)"
                >
                <!-- needed to force select to not use tags -->
                <template slot="tag">{{''}}</template>
                <template slot="selection" slot-scope="{ values, isOpen }">
                  <span class="multiselect__single" v-if="!isOpen && values.length">{{multiselectTitle(values.length)}}</span>
                </template>
                <template slot="option" slot-scope="props">
                  <div class="option__desc"><font-awesome-icon icon="check" size="sm" hidden/> <span>({{ props.option.accountId }}) - {{ props.option. accountName }}</span></div>
                </template>
              </multiselect>

              <b-btn variant="link" class="input-helper-link" @click="selectUnselectAllReportAccounts">{{isAllReportAccountsSelected? 'Unselect All' : 'Select All' }}</b-btn>
            </b-col>
          </b-row>

          <b-row>
            <b-col class="d-flex justify-content-end text-error">
              <span>{{ errors[0] }}</span>
            </b-col>
          </b-row>
        </ValidationProvider>
        <b-row class="mt-4">
          <b-col md="4" class="edit-row-caption">Access Account(s)</b-col>
          <b-col md="8">
            <multiselect
              v-model="currentReportGroup.accessAccounts"
              :options="accountsListing || []"
              :multiple="true"
              :close-on-select="false"
              :clear-on-select="false"
              :preserve-search="true"
              placeholder="Search..."
              :show-labels="false"
              track-by="accountId">
              <!-- needed to force select to not use tags -->
              <template slot="tag">{{''}}</template>
              <template slot="selection" slot-scope="{ values, isOpen }">
                <span class="multiselect__single" v-if=" !isOpen  && values.length">{{multiselectTitle(values.length)}}</span>
              </template>
              <template slot="option" slot-scope="props">
                <div class="option__desc"><font-awesome-icon icon="check" size="sm" hidden/> <span>({{ props.option.accountId }}) - {{ props.option. accountName }}</span></div>
              </template>
            </multiselect>
          </b-col>
        </b-row>

        <div class="selected-accounts-box d-none d-xl-block">
          <div v-for="account in currentReportGroup.accessAccounts" :key="account.accountId" class="selected-account-item">
            {{account.accountId}}
            <button type="button" @click="unselectAccessAccountById(account.accountId)" aria-label="Remove" class="close ml-1 text-dark remove-selected-account">×</button>
          </div>
        </div>
      </b-modal>
  </ValidationObserver>

</template>

<script>
import Multiselect from 'vue-multiselect'
import globals from '@/globals'

export default {
  name: 'create-edit-modal',
  props: {
    show: { type: Boolean, required: true },
    reportGroup: { type: Object, required: true },
    accountsListing: { type: Array, required: true },
    groupsListing: { type: Array, required: true }
  },
  components: {
    multiselect: Multiselect
  },
  data () {
    return {
      currentReportGroup: {
        id: '',
        groupName: '',
        accountsGroupId: null,
        reportAccounts: [],
        accessAccounts: []
      }
    }
  },
  computed: {
    okButtonTittle () {
      return this.isEdit ? 'Save' : 'Create'
    },
    modalTittle () {
      return this.isEdit ? this.currentReportGroup.groupName : 'Create a Group'
    },
    isAllReportAccountsSelected () {
      if (this.currentReportGroup.accountsGroupId) {
        return this.currentReportGroup.reportAccounts.length === this.accountsListing.length
      }
      return false
    },
    isEdit () {
      return !!this.currentReportGroup.id
    },
    groupsListingForSelect () {
      const toReturn = this.groupsListing.slice(0)
      toReturn.unshift({value: null, text: 'Select an Account Group'})
      return toReturn
    }
  },
  methods: {
    fillData () {
      if (this.reportGroup.id) {
        this.currentReportGroup = globals().getClonedValue(this.reportGroup)
      } else {
        this.currentReportGroup = {
          id: '',
          groupName: '',
          accountsGroupId: null,
          reportAccounts: [],
          accessAccounts: []
        }
      }
    },
    changeGroup (value) {
      if (this.isEdit || value == null) {
        return
      }
      this.$emit('changeGroup', value)
    },
    selectUnselectAllReportAccounts () {
      if (this.currentReportGroup.reportAccounts.length < this.accountsListing.length) {
        this.currentReportGroup.reportAccounts = this.accountsListing
      } else {
        this.currentReportGroup.reportAccounts = []
      }
    },
    unselectAccessAccountById (accountId) {
      this.currentReportGroup.accessAccounts = this.currentReportGroup.accessAccounts.filter(x => x.accountId !== accountId)
    },
    hideModal () {
      this.$emit('hide')
    },
    useGroupName () {
      if (this.currentReportGroup.accountsGroupId) {
        const group = this.groupsListing.filter(x => x.value === this.currentReportGroup.accountsGroupId)[0]
        this.currentReportGroup.groupName = group ? group.text : ''
      }
    },
    sendReportGroup () {
      this.$emit('sendReportGroup', this.currentReportGroup)
    },
    multiselectTitle (selectedCount) {
      if (selectedCount) {
        return (selectedCount.toString() + (selectedCount > 1 ? ' accounts' : ' account') + ' selected')
      }
    }
  }
}

</script>

<style lang="scss">
.create-edit-modal {

  .edit-row-caption {
    display: flex;
    align-items: center;
  }

  .input-error .multiselect__tags{
    border-color : #d9534f;
  }

  .text-error{
    color: red;
  }

 .input-helper-link {
    position: absolute;
    right: 0;
    top: -30px;
    color: #C90F17;
  }

  .input-helper-link:hover {
    color: #af0d14;
    text-decoration: underline;
  }

  .input-helper-link:focus {
    border-color: none;
    box-shadow: none;
  }

 .selected-account-item {
    display: inline-block;
    margin-top: 10px;
    margin-left: 3px;
    background: #8897aa;
    border-radius: 10px;
    font-weight: 700;
    padding: 5px 10px;
    color: white;
    min-width: 82px;
    text-align: center;
  }

 .remove-selected-account {
    height: 100%;
    text-align: right;
    margin-top: -1px;
    border-radius: 50px;
    font-weight: bold;
  }

 .remove-selected-account:hover {
    color: white !important;
  }

  .multiselect__option--selected svg {
    display: inline-block !important;
  }

  .multiselect__option--highlight, .multiselect__option--selected.multiselect__option--highlight {
    background: #C90F17 !important;
    color: #fff !important;
  }

  .multiselect, .multiselect__input, .multiselect__single {
    font-family: inherit;
    font-size: inherit;
  }
}
</style>

<style lang="scss">
  @import '~node_modules/vue-multiselect/dist/vue-multiselect.min';
</style>
