<template>
  <div>
    <div>
      <h4>User Activity Details Log</h4>
    </div>
    <b-card>
      <log-node
        v-if="logDetails"
        :data="logDetails"
        :isExpandedShallow="true"
        :isExpandedDeep="false"
      />
    </b-card>
  </div>
</template>

<script>
export default {
  name: 'system-tools-user-activity-log-details',
  metaInfo: {
    title: 'User Activity Details'
  },
  props: {
    logId: { type: String, required: true }
  },
  data () {
    return {
      logDetails: null
    }
  },
  components: {
    'log-node': () => import('@/components/_shared/logItemNode.vue')
  },
  created () {
    this.loadContent()
  },
  methods: {
    loadContent () {
      this.$store.dispatch('systemTools/getUserActivityLogDetails', {id: this.logId}).then(x => {
        this.logDetails = {
          nodes: x.data.details
        }
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
      })
    }
  }
}
</script>
