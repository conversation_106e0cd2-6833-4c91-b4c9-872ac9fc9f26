<template>
  <b-form inline @submit.prevent="onSubmit">
    <b-card-body>
      <div class="form-row">
        <b-col lg="5">
          <b-input-group class="inventory-filter__search">
              <b-form-input placeholder="Search..." v-model.trim="searchString"></b-form-input>
              <b-input-group-append>
                  <b-btn type="submit" variant="primary">Go</b-btn>
              </b-input-group-append>
          </b-input-group>
        </b-col>
        <div class="col-12 col-lg-6 col-xl-5 offset-lg-1 offset-xl-2 text-right">
          <b-button-group v-if="variant === 'buttons'" class="d-flex mt-4 mt-lg-0 inventory-filter__buttons">
            <b-btn v-for="button in buttons" :key="button.id" :variant="button.isActive ? 'primary' : 'default'" @click="changeActiveButton(button.id)" class="flex-fill px-1">{{button.title}} (<number :value="button.total"></number>)</b-btn>
          </b-button-group>
          <b-row v-else-if="variant === 'select'" class="mt-4 mt-lg-0">
            <b-col cols="2" md="1" lg="2" class="m-auto text-sm-left">
              <label class="mb-0 justify-content-start" for="display-type">Display:</label></b-col>
            <b-col cols>
              <b-form-select  :value="getActiveTabId" class="w-100" id="display-type" @change="changeActiveButton">
                <option v-for="button in buttons" :value="button.id" :key="button.id">{{button.title}}</option>
              </b-form-select>
            </b-col>
          </b-row>
        </div>
      </div>
    </b-card-body>
  </b-form>
</template>

<script>
import number from '@/components/_shared/number.vue'
let searchTimerId = 0

export default {
  name: 'inventory-filters-form',
  components: {
    'number': number
  },
  props: {
    buttons: {
      type: Array,
      validator (val) {
        return Array.isArray(val) &&
        val.reduce((res, current) => {
          return res &&
          current.hasOwnProperty('id') &&
          current.hasOwnProperty('title') &&
          current.hasOwnProperty('total') &&
          current.hasOwnProperty('isActive')
        }, true)
      }
    },
    search: String,
    variant: {
      required: true,
      type: String,
      validator: function (value) {
        return ['buttons', 'select'].indexOf(value) !== -1
      }
    }
  },
  data () {
    return {
      searchPhrase: this.search
    }
  },
  methods: {
    changeActiveButton (activeId) {
      this.$emit('changeActive', activeId)
    },
    onSubmit () {
      this.searchSetter(this.searchPhrase)
    },
    searchSetter (newValue) {
      if (searchTimerId !== -1) {
        clearTimeout(searchTimerId)
      }

      if (newValue === this.search) {
        return
      }

      this.searchPhrase = newValue
      searchTimerId = setTimeout(() => {
        this.$emit('searchChanged', newValue)
      }, 250)
    }
  },
  computed: {
    searchString: {
      get: function () {
        return this.search
      },
      set: function (newValue) {
        this.searchSetter(newValue)
      }
    },
    getActiveTabId () {
      return this.buttons.find(x => x.isActive).id
    }
  }
}
</script>

<style scoped lang="scss">
@media (max-width: 479px) {
  .inventory-filter__buttons {
    .btn {
      font-size: .75rem;
    }
  }
}
</style>
