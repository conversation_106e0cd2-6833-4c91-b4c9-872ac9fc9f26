<template>
  <b-table
    :fields="getTableFields"
    :items='items'
    :bordered="false"
    :no-sort-reset="true"
    :no-local-sorting="true"
    hover
    striped
    responsive
    :sort-by="tableSortBy"
    :sort-desc="tableSortDesc"
    @sort-changed="onSortChanged"
    >
    <template #cell(show_details)="row">
      <b-button size="sm" @click.stop="row.toggleDetails" class="text-center">
        {{ row.detailsShowing ? 'Hide' : 'Show' }} Details
      </b-button>
      <b-btn size="sm" variant="primary" v-if="row.item.isDeleteAllowed" @click="onDelete(row.item.id)">Delete</b-btn>
    </template>
    <template #row-details="row">
      <b-card>
        <b-row class="mb-2">
          <b-col sm="12" md="4" lg="3" xl="2"><b>Rebuild Type:</b></b-col>
          <b-col col>{{ row.item.rebuildType }}</b-col>
        </b-row>
        <b-row class="mb-2">
          <b-col sm="12" md="4" lg="3" xl="2"><b>Statistic:</b></b-col>
          <b-col col>{{ row.item.statisticType }}</b-col>
        </b-row>
        <b-row class="mb-2">
          <b-col sm="12" md="4" lg="3" xl="2"><b>Is Account Closed:</b></b-col>
          <b-col col>{{ row.item.hasToCloseAccount }}</b-col>
        </b-row>
        <b-row class="mb-2">
          <b-col sm="12" md="4" lg="3" xl="2"><b>Priority:</b></b-col>
          <b-col col>{{ row.item.priority }}</b-col>
        </b-row>
        <b-row class="mb-2">
          <b-col sm="12" md="4" lg="3" xl="2"><b>Scheduled At:</b></b-col>
          <b-col>{{ row.item.schedulingDateTime }}</b-col>
        </b-row>
        <b-row class="mb-2">
          <b-col sm="12" md="4" lg="3" xl="2"><b>Task Id:</b></b-col>
          <b-col col>{{ row.item.id }}</b-col>
        </b-row>
        <b-button size="sm" @click="row.toggleDetails">Hide Details</b-button>
      </b-card>
    </template>
  </b-table>
</template>

<script>
import baseLogsFilterListing from './baseLogsFilterListing'
import analyticsConstants from '@/shared/analytics/constants'

export default {
  mixins: [baseLogsFilterListing],
  name: 'analytics-account-logs-listing',
  computed: {
    getTableFields () {
      return [
        {
          key: 'accountId',
          sortable: false,
          label: 'Account Id'
        },
        {
          key: 'userName',
          sortable: false,
          label: 'User'
        },
        {
          key: 'dateFrom',
          sortable: false,
          label: 'From'
        },
        {
          key: 'dateTo',
          sortable: false,
          label: 'To'
        },
        {
          key: 'processStatus',
          sortable: false,
          label: 'Status'
        },
        {
          key: 'createdDateTime',
          sortable: true,
          label: 'Created At',
          sortTypeAsc: analyticsConstants.accountLogsSortTypes.createdAtAsc,
          sortTypeDesc: analyticsConstants.accountLogsSortTypes.createdAtDesc
        },
        {
          key: 'lastProcessDateTime',
          sortable: true,
          label: 'Processed At',
          sortTypeAsc: analyticsConstants.accountLogsSortTypes.processedAtAsc,
          sortTypeDesc: analyticsConstants.accountLogsSortTypes.processedAtDesc
        },
        {
          key: 'show_details',
          label: 'Manage'
        }
      ]
    }
  }
}
</script>
