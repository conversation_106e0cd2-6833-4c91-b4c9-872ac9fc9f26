<template>
  <div class="back-to-admin-component" v-if="accountId">
    <span class="back-to-admin-component__title" v-if="siteSettings">{{title}} ({{accountId}})</span>
    <router-link  class="back-to-admin-component__exit exit" :to="{name: 'account-listing'}">
      <span class="exit__title pr-2">Back To Admin</span>
      <i class="ion ion-ios-close back-to-admin-close-icon"></i>
    </router-link>
  </div>
</template>

<script>

export default {
  name: 'backToAdmin',
  async created () {
    if (this.accountId) {
      this.siteSettings = await this.$store.dispatch('accountSettings/getAccountSettings', this.accountId)
    }
  },
  data () {
    return {
      siteSettings: null
    }
  },
  computed: {
    title () {
      return (this.siteSettings.dealerInformation || {companyName: ''}).companyName
    },
    accountId () {
      return +this.$route.params.accountId
    }
  }
}
</script>

<style scoped>
  .back-to-admin-component {
    font-weight: bold;
    color: white !important;
    background: #C90F17;
    height: 2rem;
    text-align: center;
  }

  .back-to-admin-component__exit {
    color: white;
    background: #bf0e16;
    padding: 0 10px;
    display: flex;
    align-items: center;
    float: right;
    height: 100%;
  }

  .back-to-admin-component__title {
    display: inline-flex;
    align-items: center;
    height: 100%;
  }

  .back-to-admin-close-icon {
    font-size: 1.5rem;
  }

  @media (max-width:576px) {
    .exit__title {
      display: none;
    }
  }
</style>
