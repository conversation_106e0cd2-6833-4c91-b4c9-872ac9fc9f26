import {mapGetters} from 'vuex'
import applicationTypes from '../../../shared/common/applicationTypes'
import permission from '../../../shared/common/permissions'
import analyticsHelper from '../helpers.js'
import globals from '@/globals'

export default {
  props: {
    reportGroupId: {
      type: String,
      required: true
    }
  },
  created () {
    this.setFilters()
    this.page.hasToUpdateStatisticsWhenRangeInfoComes = true
    if (this.rangeInfo) {
      this.onRangeChanged(this.rangeInfo)
    }
    this.fillReportAccounts()
  },
  data () {
    return {
      accountNameStr: '',
      reportAccounts: {},
      page: {
        hasToUpdateStatisticsWhenRangeInfoComes: false,
        deviceFilter: 'all',
        groupLevelState: null
      },
      bar: {
        items: []
      },
      summary: {
        label: ''
      },
      table: {
        items: [],
        totalItems: 0
      },
      cache: {}
    }
  },
  computed: {
    ...mapGetters('analyticsGa4', ['rangeInfo']),
    ...mapGetters('users', ['userInfo']),
    accountName () {
      if (!this.accountNameStr) {
        this.fillAccountNameStr()
      }
      return this.accountNameStr
    },
    isAccountLevel () {
      return this.page.filter.accountId > 0
    },
    barTimeFormat () {
      if (this.rangeInfo && Date.daysBetween(new Date(this.rangeInfo.range[1]), new Date(this.rangeInfo.range[0]), true) > 365) {
        return 'MMM D YYYY'
      }

      return 'MMM.D'
    }
  },
  methods: {
    onRangeChanged (rangeInfo) {
      this.summary.label = rangeInfo.label
      this.page.filter.dateFrom = rangeInfo.range[0]
      this.page.filter.dateTo = rangeInfo.range[1]

      if (this.page.hasToUpdateStatisticsWhenRangeInfoComes) {
        this.updateStatistics()
      } else {
        if (this.page.filter.pageNumber) {
          this.page.filter.pageNumber = 1
        }
        this.synchronizeUrl()
      }
      this.page.hasToUpdateStatisticsWhenRangeInfoComes = false
    },
    onPageNumberChanged (pageNumber) {
      this.page.filter.pageNumber = pageNumber
      this.synchronizeUrl()
    },
    onPageSizeChanged (pageSize) {
      this.page.filter.pageSize = pageSize
      this.page.filter.pageNumber = 1
      this.synchronizeUrl()
    },
    onSortTypeChanged (sortType) {
      this.page.filter.sortType = sortType
      if (this.page.filter.pageNumber) {
        this.page.filter.pageNumber = 1
      }
      this.synchronizeUrl()
    },
    onDeviceFilterChanged (tab) {
      if (this.page.filter.pageNumber) {
        this.page.filter.pageNumber = 1
      }
      this.page.deviceFilter = tab.key
      this.updateStatistics()
    },
    onReportTablesSelectorChanged (isSegmentedByAccountSelected) {
      this.page.filter.segmentedByAccount = isSegmentedByAccountSelected
      if (this.page.filter.pageNumber) {
        this.page.filter.pageNumber = 1
      }
      if (this.getReportTablesSelectorSortTypeCondition()) {
        this.page.filter.sortType = this.filterManager.defaultValue.sortType
      }
      this.table.items = []
      this.synchronizeUrl()
    },
    backupGroupLevelState () {
      this.page.groupLevelState = {
        filter: globals().getClonedValue(this.page.filter),
        deviceFilter: this.page.deviceFilter
      }
    },
    restoreGroupLevelState () {
      let restored = false
      if (this.page.groupLevelState && this.page.groupLevelState.filter &&
        this.page.groupLevelState.filter.accountId === this.page.filter.accountId &&
        this.page.groupLevelState.filter.segmentedByAccount === this.page.filter.segmentedByAccount &&
        this.page.groupLevelState.filter.dateFrom === this.page.filter.dateFrom &&
        this.page.groupLevelState.filter.dateTo === this.page.filter.dateTo &&
        this.page.groupLevelState.deviceFilter === this.page.deviceFilter) {
        this.page.filter = this.page.groupLevelState.filter
        restored = true
      }
      this.page.groupLevelState = null
      return restored
    },
    onAccountNameClicked ({ accountName, accountId }) {
      this.backupGroupLevelState()
      if (this.getAccountNameSortTypeCondition()) {
        this.page.filter.sortType = this.defaultAccountLevelSortType
      }
      if (this.page.filter.pageNumber) {
        this.page.filter.pageNumber = 1
      }
      this.page.filter.accountId = accountId
      this.accountNameStr = accountName
      this.table.items = []
      this.synchronizeUrl()
    },
    async onBackToGroup () {
      this.page.filter.accountId = 0
      if (!this.restoreGroupLevelState()) {
        this.page.filter.pageNumber = 1
        this.page.filter.sortType = this.filterManager.defaultValue.sortType
      }
      this.table.items = []
      this.synchronizeUrl()
    },
    setFilters () {
      this.page.filter = analyticsHelper.getFilterWithDefaultDateRange(this.$router, this.filterManager)
    },
    synchronizeUrl () {
      const filterForRoute = analyticsHelper.getFilterForUrlQueryWithDefaultDateRange(this.page.filter, this.filterManager)
      this.filterManager.urlHelper.rebuildParamsInQueryString(this.$router, filterForRoute)
    },
    async fillAccountNameStr () {
      try {
        let result = await this.$store.dispatch(
          'analyticsGa4/getAccountNameFromReportGroup',
          { reportGroupId: this.reportGroupId, accountId: this.page.filter.accountId })
        this.accountNameStr = result.data.model
      } catch (err) {
        this.$toaster.error('Failed to get data from server', { timeout: 8000 })
        this.$logger.handleError(err, 'Can\'t get account name from report group', { reportGroupId: this.reportGroupId, accountId: this.page.filter.accountId })
      }
    },
    async fillReportAccounts () {
      try {
        let result = await this.$store.dispatch(
          'analyticsGa4/getReportAccountsFromReportGroup',
          { reportGroupId: this.reportGroupId })
        this.reportAccounts = result.data.model.reduce((reportAccountsObject, current) => {
          reportAccountsObject[current.accountId] = current
          return reportAccountsObject
        }, {})

        if (this.table.items.length) {
          this.table.items = this.table.items.map(x => {
            if (x.account) {
              x.account.isAccessAllowed = this.isAccessToAccountAllowed(x.account.accountId)
            }
            return x
          })
        }
      } catch (err) {
        this.$toaster.error('Failed to get data from server', { timeout: 8000 })
        this.$logger.handleError(err, 'Can\'t get report accounts from report group', { reportGroupId: this.reportGroupId })
      }
    },
    isAccessToAccountAllowed (accountId) {
      return this.userInfo.user.canManageAccountApplicationType(accountId, applicationTypes.AppsAnalytics.Id, permission.AnalyticsFullAccess) &&
        this.reportAccounts[accountId] && this.reportAccounts[accountId].isActive
    }
  },
  watch: {
    $route (to, from) {
      const fromQuery = this.filterManager.urlHelper.normalizeObject(from.query)
      const toQuery = this.filterManager.urlHelper.normalizeObject(to.query)
      if (fromQuery.accountId !== toQuery.accountId || fromQuery.segmentedByAccount !== toQuery.segmentedByAccount) {
        this.table.items = []
      }
      this.setFilters()
      this.updateStatistics()
    },
    rangeInfo (value) {
      this.onRangeChanged(value)
    }
  }
}
