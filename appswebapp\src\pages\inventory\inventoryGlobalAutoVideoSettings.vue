<template>
  <div class="position-relative">
    <h4>AutoVideo Settings</h4>
    <paging
      v-if="tabsOptions[selectedTab].value === tabs.ttsTrainingSettings.value"
      class="custom-autovideo-paging d-none d-md-block"
      :totalItems="totalTextConversionItemsItemsCount"
      :pageNumber="textConversionFilter.page"
      :pageSize="textConversionFilter.pageSize"
      @numberChanged="onPageChanged"
      @changePageSize="onPageSizeChanged" />
    <b-tabs class="nav-tabs-top nav-responsive-sm" v-model="selectedTab" no-fade>
      <b-tab class="p-3" v-for="tab in tabsOptions" :title="tab.title" :key="tab.value">
        <adminAutoVideoSettingsSection v-if="tabsOptions[selectedTab].value === tabs.ttsSettings.value"/>
        <div v-if="tabsOptions[selectedTab].value === tabs.ttsTrainingSettings.value">
          <b-row>
            <b-col class="py-2" sm="12" md="6" lg="5">
              <b-input-group>
                <b-form-input v-model="textConversionFilter.search" @keydown.enter.native="populateTextConversionData" placeholder="Search by keyword..."></b-form-input>
                <b-input-group-append>
                  <b-btn variant="primary" @click="populateTextConversionData">Go</b-btn>
                </b-input-group-append>
              </b-input-group>
            </b-col>
            <b-col class="py-2" sm="12" md="6" lg="7">
              <b-btn class="float-right crete-new-text-conversion-btn" variant="primary" @click="createNewTextConversion">Create New Setting</b-btn>
            </b-col>
          </b-row>
        </div>
      </b-tab>
    </b-tabs>
    <textConversionListing v-if="tabsOptions[selectedTab].value === tabs.ttsTrainingSettings.value"
      :isLoading="isLoading"
      :filter="textConversionFilter"
      :items="textConversionItems"
      :totalItemsCount="totalTextConversionItemsItemsCount"
      @refresh="populateTextConversionData"
    />
  </div>
</template>

<script>
import paging from '@/components/_shared/paging'
import {ObjectSchema} from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import adminAutoVideoSettingsSection from '@/components/inventory/autoVideoSettingsSection/adminAutoVideoSettingsSection.vue'
import textConversionListing from '@/components/inventory/textConversionListing'
import VideoEncoderService from '../../services/inventory/VideoEncoderService'
import videoEncoderTypes from '../../shared/inventory/videoEncoderTypes'

const textConversionSortTypes = videoEncoderTypes.textConversionSortTypes

const defaultTextConversionValues = new ObjectSchema({
  search: { type: String, default: '' },
  pageSize: { type: Number, default: 25 },
  page: { type: Number, default: 1 },
  sort: { type: Number, default: textConversionSortTypes.fromAsc }
})

const tabs = {
  ttsTrainingSettings: { value: 'ttstrainingsettings', title: 'TTS Training Settings' },
  ttsSettings: { value: 'ttssettings', title: 'TTS Settings' }
}

const defaultValues = new ObjectSchema({
  tab: { type: String, default: tabs.ttsTrainingSettings.value }
})
let queryHelper = new QueryStringHelper(defaultValues)

export default {
  name: 'inventory-global-autovideo-settings',
  metaInfo: {
    title: 'Global AutoVideo Settings'
  },
  data () {
    return {
      tabFilter: defaultValues.getObject(),
      textConversionFilter: defaultTextConversionValues.getObject(),
      textConversionItems: [],
      totalTextConversionItemsItemsCount: 0,
      isLoading: true,
      tabs: tabs
    }
  },
  created () {
    this.tabFilter = queryHelper.parseQueryStringToObject(this.$router)
    if (this.tabsOptions[this.selectedTab].value === this.tabs.ttsTrainingSettings.value) {
      this.populateTextConversionData()
    }
  },
  computed: {
    tabsOptions () {
      return Object.values(this.tabs)
    },
    selectedTab: {
      get () {
        let index = this.tabsOptions.findIndex(x => x.value === this.tabFilter.tab)
        if (index < 0) {
          return 0
        }
        return index
      },
      set (index) {
        let tab = this.tabsOptions[index]
        this.tabFilter.tab = tab.value
        queryHelper.rebuildParamsInQueryString(this.$router, this.tabFilter)
        if (tab.value === this.tabs.ttsTrainingSettings.value) {
          this.populateTextConversionData()
        }
      }
    }
  },
  components: {
    adminAutoVideoSettingsSection,
    textConversionListing,
    paging
  },
  methods: {
    onPageChanged (newPage) {
      this.textConversionFilter.page = newPage
      this.populateTextConversionData()
    },
    onPageSizeChanged (pageSize) {
      this.textConversionFilter.pageSize = pageSize
      this.textConversionFilter.page = 1
      this.populateTextConversionData()
    },
    populateTextConversionData () {
      this.isLoading = true
      let apiFilters = {
        search: this.textConversionFilter.search,
        skip: (this.textConversionFilter.page - 1) * this.textConversionFilter.pageSize,
        limit: this.textConversionFilter.pageSize,
        sort: this.textConversionFilter.sort
      }
      VideoEncoderService.getTextConversionListing(apiFilters).then(res => {
        this.textConversionItems = res.data.items
        this.totalTextConversionItemsItemsCount = res.data.totalItemsCount
      }).catch(ex => {
        this.$toaster.error('Failed on receiving data from server')
      }).finally(() => {
        this.isLoading = false
      })
    },
    createNewTextConversion () {
      this.$router.push({name: 'inventory-text-conversion-create'})
    }
  }
}
</script>

<style lang="scss">
  .custom-autovideo-paging {
  position: absolute;
  right: -10px;
  top: 30px;
  z-index: 2;
}
</style>
