<template>
  <div>
    <b-row>
      <b-col>
        <h4>Announcements</h4>
      </b-col>
      <b-dropdown variant="primary btn-round" class="float-right" size="sm">
        <template slot="button-content">
          <span class="ion ion-ios-add"></span><span class="d-none d-md-inline">&nbsp; Add New Announcement</span>
        </template>
        <b-dropdown-item @click="onAddNewAnnouncement">Create</b-dropdown-item>
        <b-dropdown-item @click="openImportAnnouncementModal">Import</b-dropdown-item>
      </b-dropdown>
    </b-row>
    <b-card class="mt-2">
      <b-form @submit.prevent="applyFilters">
        <div class="form-row">
          <b-col xl="6" lg="6" md="6" sm="12">
            <b-input-group>
              <b-form-input v-model="filters.search"></b-form-input>
              <b-input-group-append>
                <b-btn variant="primary" type="submit">Submit</b-btn>
              </b-input-group-append>
            </b-input-group>
          </b-col>
        </div>
      </b-form>
    </b-card>
    <b-card class="mt-2">
      <b-table
        :items="items"
        :fields="tableFields"
        hover
        striped
        responsive="sm"
      >
      <template #cell(manage)="data">
        <b-btn size="sm" @click="onEdit(data.item)">Edit</b-btn>
        <b-btn size="sm" variant="primary" @click="onExport(data.item)">Export</b-btn>
        <b-btn size="sm" variant="dark" @click="onDelete(data.item)">Delete</b-btn>
      </template>
      </b-table>
      <!-- Pagination -->
      <paging
        :pageNumber="filters.page"
        :pageSize="filters.pageSize"
        :totalItems="totalItemsCount"
        titled
        pageSizeSelector
        @numberChanged="pageChanged"
        @changePageSize="changePageSize"
      />
    </b-card>
    <b-modal
      :title="isImportMode ? 'Import' : 'Export'"
      :visible="isImportExportModalVisible"
      @hide="hideImportExportModal"
    >
      <b-textarea ref="importOrExportAnnouncement" v-model="importOrExportText" :placeholder="isImportMode ? 'Paste the announcement here' : ''" rows="10">
      </b-textarea>
      <template #modal-footer>
        <b-btn size="sm" @click="hideImportExportModal">Cancel</b-btn>
        <b-btn v-if="isImportMode" size="sm" variant="primary" @click="onImportAnnouncement">Import</b-btn>
        <b-btn v-else size="sm" variant="primary" @click="copyAnnouncement">Copy</b-btn>
      </template>
    </b-modal>
  </div>
</template>

<script>
import loader from '@/components/_shared/loader'
import moment from 'moment'
import detailRow from '@/components/details/helpers/detailRow'
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import paging from '@/components/_shared/paging'
import AnnouncementService from '@/services/announcements/AnnouncementService'
import { announcementTypes } from '@/shared/announcements/constants'

const defaultValues = new ObjectSchema({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  search: { type: String, default: '' }
})

const queryHelper = new QueryStringHelper(defaultValues)
const unnecessaryExportAnnouncementFields = ['dateTimeCreated', 'createdByUserName', 'dateTimeUpdated', 'updatedByUserName']

export default {
  name: 'announcements',
  metaInfo: {
    title: 'Announcements'
  },
  data () {
    return {
      items: [],
      totalItemsCount: 0,
      isLoading: true,
      filters: defaultValues.getObject(),
      isImportMode: false,
      isImportExportModalVisible: false,
      importOrExportText: ''
    }
  },
  created () {
    this.filters = queryHelper.parseQueryStringToObject(this.$router)
    this.loadContent()
  },
  components: {
    loader,
    detailRow,
    paging
  },
  computed: {
    tableFields () {
      return [
        {
          key: 'title',
          label: 'Title',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'announcementType',
          label: 'Type',
          tdClass: 'py-2 align-middle',
          formatter: val => (Object.values(announcementTypes).find(x => x.value === val) || {text: '-'}).text
        },
        {
          key: 'dateTimeCreated',
          label: 'Create Date',
          tdClass: 'py-2 align-middle',
          formatter: val => val ? moment(val).format('MM/DD/YYYY hh:mm:ss A') : '-'
        },
        {
          key: 'startDateTime',
          label: 'Start Date',
          tdClass: 'py-2 align-middle',
          formatter: val => val ? moment(val).format('MM/DD/YYYY hh:mm:ss A') : '-'
        },
        {
          key: 'endDateTime',
          label: 'End Date',
          tdClass: 'py-2 align-middle',
          formatter: val => val ? moment(val).format('MM/DD/YYYY hh:mm:ss A') : '-'
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle'
        }
      ]
    }
  },
  methods: {
    onAddNewAnnouncement () {
      this.$router.push({name: 'announcements-create'})
    },
    applyFilters () {
      this.filters.page = 1
      this.synchronizeUrlAndReload()
    },
    pageChanged (newPage) {
      this.filters.page = newPage
      this.synchronizeUrlAndReload()
    },
    changePageSize (newSize) {
      this.filters.pageSize = newSize
      this.filters.page = 1
      this.synchronizeUrlAndReload()
    },
    synchronizeUrlAndReload () {
      this.isLoaded = false
      queryHelper.rebuildParamsInQueryString(this.$router, this.filters)
      this.loadContent()
    },
    loadContent () {
      let apiFilters = {
        skip: this.filters.page - 1,
        limit: this.filters.pageSize,
        search: this.filters.search
      }
      AnnouncementService.getAnnouncements(apiFilters).then(res => {
        this.items = res.data.items
        this.totalItemsCount = res.data.total
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong on getting list of announcements', {timeout: 5000})
      })
    },
    onEdit (announcement) {
      this.$router.push({name: 'announcements-edit', params: {id: announcement.announcementId}})
    },
    onDelete (announcement) {
      AnnouncementService.deleteAnnouncement(announcement.announcementId).then(res => {
        this.$toaster.success('Announcement Deleted Successfully', {timeout: 5000})
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong on deleting announcement', {timeout: 5000})
      }).finally(() => {
        this.loadContent()
      })
    },
    onExport (item) {
      AnnouncementService.getAnnouncement(item.announcementId).then(res => {
        let announcement = this.removeUnnecessaryAnnouncementFieldToExport(res.data)
        this.isImportMode = false
        this.importOrExportText = JSON.stringify(announcement)
        this.isImportExportModalVisible = true
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong on exporting the announcement', {timeout: 5000})
      })
    },
    openImportAnnouncementModal () {
      this.isImportMode = true
      this.importOrExportText = ''
      this.isImportExportModalVisible = true
    },
    hideImportExportModal () {
      this.importOrExportText = ''
      this.isImportExportModalVisible = false
    },
    copyAnnouncement () {
      const element = this.$refs.importOrExportAnnouncement
      element.select()
      if (document.execCommand('copy')) {
        this.$toaster.success('Copied Successfully')
        this.hideImportExportModal()
      } else {
        this.$toaster.error('Copied Failed')
      }
    },
    async onImportAnnouncement () {
      try {
        let announcement = JSON.parse(this.importOrExportText)
        await AnnouncementService.createAnnouncement(announcement)
        this.$toaster.success('Imported Successfully')
        this.loadContent()
        this.hideImportExportModal()
      } catch (ex) {
        this.$toaster.exception(ex, 'Failed on importing announcement', {timeout: 5000})
      }
    },
    removeUnnecessaryAnnouncementFieldToExport (announcement) {
      delete announcement['announcementId']
      unnecessaryExportAnnouncementFields.forEach(key => {
        delete announcement[key]
      })
      announcement.boxes.forEach(box => {
        delete box['announcementBoxId']
        unnecessaryExportAnnouncementFields.forEach(key => {
          delete box[key]
        })
      })
      return announcement
    }
  }
}
</script>
