<template>
  <div v-if="isLoaded">
    <b-row class="p-3">
      <b-col class="my-2" xs="12" sm="12" md="6" lg="7" xl="7">
        <datepicker
          v-if="errorReportSummary"
          format="d MMMM yyyy"
          v-model="date"
          :day-cell-content="customDate"
          :bootstrap-styling="true"
          :disabled-dates="disabledDates"
          @input="onInputDate"
          inline
        >
        </datepicker>
        <loader v-else size="sm" />
      </b-col>
      <b-col class="my-2" xs="12" sm="12" md="6" lg="5" xl="5">
        <b-row>
          <b-col cols="auto my-auto" v-if="hasUserFullAccess">
            <b-btn size="md" variant="primary" @click="onOpenGenerateErrorReportModal">Generate Error Report</b-btn>
          </b-col>
          <b-col>
            <b-input-group>
              <b-form-input v-model="email" placeholder="<EMAIL>"></b-form-input>
              <b-input-group-append>
                <b-btn @click="sendEmail" variant="primary" :disabled="isDisabledEmailSendBtn">Send</b-btn>
              </b-input-group-append>
            </b-input-group>
          </b-col>
        </b-row>
      </b-col>
    </b-row>
    <b-tabs v-if="errorReportsByDate" class="nav-tabs-top nav-responsive-sm mt-3" v-model="selectedTab" no-fade>
      <b-tab class="p-4" v-for="errorReportTab in errorReportTabs"
        :key="errorReportTab.value"
        :title="errorReportTab.text"
        :disabled="changeTabDisabled(errorReportTab.value)"
      >
        <error-report-item
          :hasUserFullAccess="hasUserFullAccess"
          v-if="errorReportsByDate[errorReportTab.value]"
          @editReport="onEditReport"
          @saveReport="onSaveReport"
          @viewExampleErrorClicked="onViewExampleErrorClicked"
          @viewExampleErrorMiddleClicked="onViewExampleErrorMiddleClicked"
          v-model="errorReportsByDate[errorReportTab.value]">
        </error-report-item>
        <span v-else>{{`${errorReportTab.text} Error Report was not generated for this day!!!`}}</span>
      </b-tab>
    </b-tabs>
    <b-modal
      v-if="hasUserFullAccess"
      name="generate-error-report"
      title="Generate Error Report"
      no-close-on-backdrop
      static
      v-model="isGenerateErrorReportModalOpen"
    >
      <b-form-radio-group
        id="error-categories"
        v-model="generateErrorReportModel.errorreporttype"
        :options="errorReportTypeOptions"
        name="error-categories"
      >
      </b-form-radio-group>
      <b-input-group class="flex-nowrap mt-2">
        <b-input-group-prepend is-text>
          <i class="ion ion-md-calendar" slot="prepend"></i>
        </b-input-group-prepend>
        <date-time-picker
          ref="timeFrom"
          v-model="generateErrorReportModel.dateFrom"
          :options="filterTimeOptions"
          format="MM/DD/YYYY"
          className="form-control"
          @change="onTimeFromInputChange"
        />
        <b-input-group-append
          is-text
          v-show="generateErrorReportModel.dateFrom"
          @click="generateErrorReportModel.dateFrom = null"
        >
          <i class="ion ion-md-close"></i>
        </b-input-group-append>
      </b-input-group>
      <b-input-group class="flex-nowrap mt-2">
        <b-input-group-prepend is-text>
          <i class="ion ion-md-calendar" slot="prepend"></i>
        </b-input-group-prepend>
        <date-time-picker
          ref="timeTo"
          v-model="generateErrorReportModel.dateTo"
          :options="filterTimeOptions"
          format="MM/DD/YYYY"
          className="form-control"
          @change="onTimeToInputChange"
        />
        <b-input-group-append
          is-text
          v-show="generateErrorReportModel.dateTo"
          @click="generateErrorReportModel.dateTo = null"
        >
          <i class="ion ion-md-close"></i>
        </b-input-group-append>
      </b-input-group>
      <template #modal-footer>
        <b-btn @click="onGenerateErrorReport" size="sm" variant="primary">Submit</b-btn>
        <b-btn @click="onHideGenerateErrorReportModal" size="sm">Close</b-btn>
      </template>
    </b-modal>
    <b-modal
      v-if="hasUserFullAccess"
      no-close-on-backdrop
      title="Processing"
      name='processing'
      v-model="isProcessingModalOpen"
      @hide="onHideProcessingModal"
    >
      <loader size="lg" />
      <template #modal-footer>
        <b-btn @click="onHideProcessingModal">Cancel</b-btn>
      </template>
    </b-modal>
    <b-modal
      id="error-details-modal"
      :visible="isErrorDetailsModalOpen"
      size="xl"
      lazy
      ok-title="Details"
      @ok="onModalDetailsOk"
      cancel-title="Close"
      @hide="onHideErrorDetailsModal"
      body-class="p-0"
      header-class="p-3"
      scrollable
    >
      <template #modal-header>
        <div class="mx-auto text-center" v-if="errorIsFetched">
          <router-link @click.native="onHideErrorDetailsModal()" class="error-block" :to="{name: 'error-details', params: {errorId: detailsError.id}}" target='_blank'>
            <h4 class="mb-0">Error body View for ErrorID {{detailsError.id}}
              <br>Category - {{detailsError.errorCategoryLabel}}
              <br>Error level - {{detailsError.errorLevelLabel}}
            </h4>
          </router-link>
        </div>
        <b-btn size="sm" @click="onHideErrorDetailsModal()" variant="primary">X</b-btn>
      </template>
      <div class="p-3">
        <loader v-if="!errorIsFetched" size="sm" />
        <div v-if="errorIsFetched">
          <b-table
            table-variant="secondary"
            :items="[detailsError]"
            :fields="getDetailsErrorField"
            bordered
            responsive
          >
          </b-table>
          <div v-html="detailsError.body"/>
        </div>
      </div>
    </b-modal>
  </div>
</template>

<script>

import Datepicker from 'vuejs-datepicker'
import globals from '@/globals'
import moment from 'moment-timezone'
import { mapGetters } from 'vuex'
import constants from '@/shared/common/constants'
import { errorReportTypes, taskStatus, errorMonitorTimezone } from '@/shared/errorReport/constants'
import errorReportItem from '@/components/errorReport/errorReportItem'
import loader from '@/components/_shared/loader'

export default {
  name: 'error-report-viewer',
  props: {
    hasUserFullAccess: Boolean
  },
  data () {
    return {
      options: {
        modules: {
          toolbar: [
          ]
        }
      },
      date: moment().tz(errorMonitorTimezone).toDate(),
      disabledDates: {
        from: moment().tz(errorMonitorTimezone).toDate()
      },
      errorReportSummary: null,
      errorReportsByDate: null,
      email: '',
      selectedTab: 0,
      isReportEdited: false,
      isDisabledEmailSendBtn: false,
      isLoaded: false,
      taskStatus,
      filterTimeOptions: {
        autoUpdateInput: false,
        singleDatePicker: true,
        maxDate: moment().endOf('day')
      },
      generateErrorReportModel: {
        errorreporttype: 1,
        dateFrom: '',
        dateTo: ''
      },
      isGenerateErrorReportModalOpen: false,
      isProcessingModalOpen: false,
      errorReportTaskId: null,
      isErrorDetailsModalOpen: false,
      detailsErrorId: '',
      detailsError: {}
    }
  },
  components: {
    'date-time-picker': () => import('@gravitano/vue-date-range-picker/src/components/DateRangePicker'),
    'error-report-item': errorReportItem,
    'datepicker': Datepicker,
    'loader': loader
  },
  created () {
    this.populateData(true).then(() => { this.isLoaded = true })
  },
  beforeDestroy () {
    clearInterval(this.interval)
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {}
    },
    userEmail () {
      return this.user ? this.user.email : ''
    },
    errorReportTabs () {
      return errorReportTypes
    },
    errorReportTypeOptions () {
      return Object.values(errorReportTypes)
    },
    refDateTimeFrom () {
      return (this.$refs.timeFrom || {}).$el || {}
    },
    refDateTimeTo () {
      return (this.$refs.timeTo || {}).$el || {}
    },
    errorIsFetched () {
      return this.detailsError.body !== undefined
    },
    getDetailsErrorField () {
      return [
        {
          key: 'applicationName',
          label: 'Application Name'
        },
        {
          key: 'errorTypeLabel',
          label: 'Error Type'
        },
        {
          key: 'machineName',
          label: 'Machine Name'
        },
        {
          key: 'errorLocalDateTime',
          label: 'Error DateTime',
          formatter: value => this.formatDate(value)
        },
        {
          key: 'requestInsertedDateTime',
          label: 'Date Inserted',
          formatter: value => this.formatDate(value)
        }
      ]
    }
  },
  methods: {
    changeTabDisabled (key) {
      return this.isReportEdited && this.selectedTab + 1 !== key
    },
    customDate (dateToFormat) {
      if (this.errorReportSummary) {
        for (const [date, summary] of Object.entries(this.errorReportSummary)) {
          if (moment(date).tz(errorMonitorTimezone).format('MM/DD/YYYY') === moment(dateToFormat.timestamp).tz(errorMonitorTimezone).format('MM/DD/YYYY')) {
            return this.getErrorReportsInfoText(date, summary)
          }
        }
      }
      return `<span>${moment(dateToFormat.timestamp).tz(errorMonitorTimezone).format('DD')}</span>`
    },
    formatDate (str) {
      return moment(str).tz(errorMonitorTimezone).format('MM/DD/YYYY hh:mm:ss A')
    },
    getErrorReportsInfoText (date, summary) {
      let title = []
      summary.map(x => {
        let res = Object.values(errorReportTypes).find(ec => ec.value === x)
        if (res) {
          title.push(res.text)
        }
      })
      let isFullErrorReportGenerated = summary && summary.length === Object.values(errorReportTypes).length
      return `<span title="${title.join(',')}" class="${isFullErrorReportGenerated ? 'text-success' : 'text-warning'}">${moment(date).tz(errorMonitorTimezone).format('DD')}</span>`
    },
    onInputDate () {
      if (!this.isReportEdited) {
        this.fetchErrorReportsByDate()
      }
    },
    onEditReport (isEdited) {
      if (isEdited) {
        this.isReportEdited = true
        this.disabledDates.from = moment('1970-01-01').tz(errorMonitorTimezone).toDate()
        this.$toaster.success('You can\'t select another report until save or cancel editing this one')
      } else {
        this.isReportEdited = false
        this.disabledDates.from = moment().tz(errorMonitorTimezone).toDate()
      }
    },
    onSaveReport (obj) {
      this.errorReportsByDate[this.selectedTab + 1].applicationReports = globals().getClonedValue(obj.applicationReports)
      this.isReportEdited = false
      this.disabledDates.from = moment().tz(errorMonitorTimezone).toDate()
    },
    async populateData (isFull) {
      this.email = this.userEmail
      await this.fetchErrorReportSummary()
      await this.fetchErrorReportsByDate()
      if (isFull) {
        this.populateFilterDate()
      }
    },
    async fetchErrorReportSummary () {
      this.$store.dispatch('systemTools/getErrorReportSummary').then(res => {
        this.errorReportSummary = res.data.model
      }).catch(ex => {
        this.$toaster.error(`Error occurred while api called. Message: ${ex.message}`)
        this.$logger.handleError(ex, 'Error occurred while api called.')
      })
    },
    async fetchErrorReportsByDate () {
      let date = moment(this.date).tz(errorMonitorTimezone)
      this.$store.dispatch('systemTools/getErrorReportsByDate', { date: moment().tz(errorMonitorTimezone).set({'year': date.year(), 'month': date.month(), 'date': date.date(), 'hour': 6, 'minute': 30, 'seconds': 0, 'milliseconds': 0}).format() }).then(res => {
        this.errorReportsByDate = res.data.model ? res.data.model : {}
      }).catch(ex => {
        this.$toaster.error(`Error occurred while api called. Message: ${ex.message}`)
        this.$logger.handleError(ex, 'Error occurred while api called.')
      })
    },
    sendEmail () {
      this.isDisabledEmailSendBtn = true
      let date = moment(this.date).tz(errorMonitorTimezone)
      let requestModel = {
        date: moment().tz(errorMonitorTimezone).set({'year': date.year(), 'month': date.month(), 'date': date.date(), 'hour': 6, 'minute': 30, 'seconds': 0, 'milliseconds': 0}).format(),
        errorReportType: this.selectedTab + 1,
        emailTo: this.email
      }
      this.$store.dispatch('systemTools/sendErrorReportToEmail', requestModel).then(res => {
        this.$toaster.success(`Email Send Successfully`)
      }).catch(ex => {
        if (ex.response && ex.response.status === 400 && ex.response.data && ex.response.data.executionResultMessage.includes('is not completed')) {
          this.$toaster.error(ex.response.data.executionResultMessage)
        } else {
          let message = ex.response ? ex.response.data : ex.message
          this.$toaster.error(`Cannot send email. Message: ${message}`)
          this.$logger.handleError(ex, 'Cannot send Email', requestModel)
        }
      }).finally(() => {
        this.isDisabledEmailSendBtn = false
      })
    },
    onSaveChanges (report) {
      let date = moment(this.date).tz(errorMonitorTimezone)
      let apiData = {
        type: this.selectedTab + 1,
        date: moment().tz(errorMonitorTimezone).set({'year': date.year(), 'month': date.month(), 'date': date.date(), 'hour': 6, 'minute': 30, 'seconds': 0, 'milliseconds': 0}).format(),
        reportBody: report
      }
      this.$store.dispatch('systemTools/updateErrorReport', apiData).then(res => {
        this.$toaster.success('Error Report Updated Successfully')
      }).catch(ex => {
        this.$toaster.error('Something went wrong!')
        this.$logger.handleError(ex, 'Error occurred on update error report', apiData)
      })
    },
    onTimeFromInputChange (newVal) {
      this.refDateTimeFrom.value = newVal || this.generateErrorReportModel.dateFrom || null
    },
    onTimeToInputChange (newVal) {
      this.refDateTimeTo.value = newVal || this.generateErrorReportModel.dateTo || null
    },
    onOpenGenerateErrorReportModal () {
      this.isGenerateErrorReportModalOpen = true
    },
    onHideGenerateErrorReportModal () {
      this.isGenerateErrorReportModalOpen = false
    },
    populateFilterDate () {
      let dateFrom
      let dateTo
      switch (moment().tz(errorMonitorTimezone).weekday()) {
        case constants.dayOfWeek.monday:
          dateFrom = moment().tz(errorMonitorTimezone).add(-2, 'd')
          dateTo = moment().tz(errorMonitorTimezone)
          this.generateErrorReportModel.dateFrom = dateFrom.format('MM/DD/YYYY')
          break
        default:
          dateFrom = moment().tz(errorMonitorTimezone).add(-1, 'd')
          dateTo = moment().tz(errorMonitorTimezone)
          this.generateErrorReportModel.dateFrom = dateTo.format('MM/DD/YYYY')
      }
      this.generateErrorReportModel.dateTo = dateTo.format('MM/DD/YYYY')
    },
    onGenerateErrorReport () {
      if (!moment(this.generateErrorReportModel.dateFrom).isValid()) {
        this.$toaster.error(`Invalid date: ${this.generateErrorReportModel.dateFrom}`)
        return
      }
      if (!moment(this.generateErrorReportModel.dateTo).isValid()) {
        this.$toaster.error(`Invalid date: ${this.generateErrorReportModel.dateTo}`)
        return
      }
      let dateFrom = moment(this.generateErrorReportModel.dateFrom)
      let dateTo = moment(this.generateErrorReportModel.dateTo)
      let apiModel = {
        dateFrom: moment().tz(errorMonitorTimezone).set({'year': dateFrom.year(), 'month': dateFrom.month(), 'date': dateFrom.date(), 'hour': 6, 'minute': 30, 'seconds': 0, 'milliseconds': 0}).format(),
        dateTo: moment().tz(errorMonitorTimezone).set({'year': dateTo.year(), 'month': dateTo.month(), 'date': dateTo.date(), 'hour': 6, 'minute': 30, 'seconds': 0, 'milliseconds': 0}).format(),
        errorreporttype: this.generateErrorReportModel.errorreporttype
      }
      this.$store.dispatch('systemTools/generateErrorReport', apiModel).then(res => {
        this.isProcessingModalOpen = true
        this.isGenerateErrorReportModalOpen = false
        this.pollProcessingCommand(res.data.model)
      }).catch(ex => {
        this.$toaster.error(`Error Occurred on call api. Message: ${ex.message}`)
        this.$logger.handleError(ex, 'Error Report Exception. Method: onGenerateErrorReport')
      })
    },
    pollProcessingCommand (id) {
      this.cleanResults()
      this.errorReportTaskId = id
      this.interval = setInterval(function () {
        this.$store.dispatch('systemTools/pollProcessingErrorCommand', id).then(res => {
          if (res.data.model && res.data.model.errorReportTask.status !== taskStatus.inProgress) {
            if (res.data.model.errorReportTask.status === taskStatus.completed) {
              this.isLoaded = false
              this.populateData(false).then(() => { this.isLoaded = true })
            } else {
              this.errorPollingException = `Result exception message:\n ${JSON.stringify(res.data.model.errorReportTask.exception)}`
            }
            this.hideModal()
          }
        }).catch(ex => {
          this.$toaster.error(`Cannot poll processing command. Ex: ${ex.message}`)
          this.$logger.handleError(ex, 'Error Report Exception. Method: pollProcessingCommand')
          clearInterval(this.interval)
          this.onHideProcessingModal()
        })
      }.bind(this), 10000)
    },
    cleanResults () {
      this.mongoErrorItems = null
      this.errorPollingException = null
    },
    hideModal () {
      this.isProcessingModalOpen = false
      this.errorReportTaskId = null
      clearInterval(this.interval)
    },
    onHideProcessingModal () {
      if (this.errorReportTaskId) {
        this.$store.dispatch('systemTools/cancelErrorReportTask', this.errorReportTaskId).then(res => {
        }).catch(ex => {
          this.$logger.handleError(ex, 'Error occurred on cancel error report task')
        }).finally(() => {
          this.hideModal()
        })
      }
    },
    onViewExampleErrorClicked (exampleId) {
      this.detailsErrorId = exampleId
      this.getErrorDetails(exampleId)
      this.isErrorDetailsModalOpen = true
    },
    onViewExampleErrorMiddleClicked (exampleId) {
      window.open(this.$router.resolve({name: 'error-details', params: { errorId: exampleId }}).href, '_blank')
    },
    onHideErrorDetailsModal () {
      this.detailsErrorId = ''
      this.isErrorDetailsModalOpen = false
    },
    onModalDetailsOk () {
      window.open(this.$router.resolve({name: 'error-details', params: { errorId: this.detailsErrorId }}).href, '_blank')
    },
    getErrorDetails (errorId) {
      return this.$store.dispatch('systemTools/getErrorDetails', errorId)
        .then(x => {
          this.detailsError = x.data.model
        })
        .catch(ex => {
          if (ex.response && ex.response.status && ex.response.status === 400) {
            this.$toaster.error(ex.response.data.executionResultMessage + ' ' + errorId, { timeout: 8000 })
          } else {
            this.$toaster.error(`Error Occurred on call api.`)
            this.$logger.handleError(ex, 'Can\'t get error', errorId)
          }
          this.isErrorDetailsModalOpen = false
        })
    },
    attachMiddleMouseButtonEventHandler () {
      document.body.onmousedown = (event) => {
        if (event.button === 1) {
          return false
        }
      }
    }
  },
  mounted: function () {
    this.attachMiddleMouseButtonEventHandler()
  }
}
</script>

<style>
p a {
  margin-left: 150px;
  cursor: pointer;
  pointer-events: auto;
}
.ql-toolbar {
  display: none;
}
.daterangepicker.single.show-calendar {
  max-width: 250px;
  min-width: 250px;
}
#error-details-modal pre {
  white-space: pre-wrap;
  margin: 0px;
  border: #a0a0c0 1px solid;
  background: #e8faff;
}
@media (min-width: 800px) {
  #error-details-modal .modal-xl {
    max-width: 75%;
  }
}
@media (min-width: 1200px) {
  #error-details-modal .modal-xl {
    max-width: 75%;
  }
  #error-details-modal .modal-dialog {
    transform: translateX(12%);
  }
}
@media (min-width: 1600px) {
  #error-details-modal .modal-xl {
    max-width: 81%;
  }
  #error-details-modal .modal-dialog {
    transform: translateX(8%);
  }
}
</style>

<style scoped>
pre {
  font-size: 15px;
}
</style>
