<template>
<ValidationObserver ref="validator">
  <div class='mb-5 edit-settings-helper'>
    <div class="border-bottom">
      <b-row class='mr-1 ml-1 title-section'>
        <h5 class="font-weight-bold">{{title}}</h5>
        <template v-if="!readOnlyMode">
          <b-btn v-if="isViewMode" variant="secondary" size="sm" class="float-right ml-auto d-none d-sm-block fixed-sizes mb-2" @click="setEditMode()" style="z-index: 999;" :disabled='disabled'><font-awesome-icon icon="pencil-alt" size="sm" /> <span class="btn-title">Edit</span></b-btn>
          <b-btn v-else variant="primary" size="sm" class="float-right mr-1 ml-auto d-none d-sm-block fixed-sizes mb-2" @click="saveSettings()" style="z-index: 999;" :disabled='disabled'><font-awesome-icon icon="cloud-upload-alt" /> <span class="btn-title">Save</span></b-btn>
          <b-btn v-if="!isViewMode" size="sm" class="float-right d-none d-sm-block fixed-sizes mb-2" @click="cancel()" style="z-index: 999;" :disabled='disabled'>Cancel</b-btn>
        </template>
      </b-row>
    </div>
    <b-overlay :show="isLoading" rounded="sm">
      <slot name="settings-content"></slot>
      <template #overlay>
        <div class="text-center text-primary">
          <b-icon icon="stopwatch" font-scale="3" animation="cylon"></b-icon>
          <p id="cancel-label">Please wait...</p>
        </div>
      </template>
    </b-overlay>
    <template v-if="!readOnlyMode">
      <b-btn v-if="isViewMode" variant="secondary" size="sm" class="w-100 d-block d-sm-none mt-2" @click="setEditMode()" :disabled='disabled'><font-awesome-icon icon="pencil-alt" size="sm" /> <span class="btn-title">Edit</span></b-btn>
      <b-btn v-else variant="primary" size="sm" class="w-100 d-block d-sm-none mt-2" @click="saveSettings()" :disabled='disabled'><font-awesome-icon icon="cloud-upload-alt" /> <span class="btn-title">Save</span></b-btn>
      <b-btn v-if="!isViewMode" size="sm" class="w-100 d-block d-sm-none mt-2" @click="cancel()" :disabled='disabled'>Cancel</b-btn>
    </template>
  </div>
</ValidationObserver>
</template>

<script>
export default {
  name: 'edit-settings-helper',
  props: {
    isLoading: {
      type: Boolean,
      required: true
    },
    isDisabled: {
      type: Boolean
    },
    title: {
      type: String,
      required: true
    },
    isViewMode: {
      type: Boolean,
      required: true
    },
    readOnlyMode: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
    }
  },
  computed: {
    disabled () {
      return this.isDisabled || this.isLoading
    }
  },
  methods: {
    saveSettings () {
      this.$refs.validator.validate().then(isValid => {
        if (isValid) {
          this.$emit('save')
        }
      })
    },
    setEditMode () {
      this.$emit('changeMode', false)
    },
    cancel () {
      this.$refs.validator.reset()
      this.$emit('cancel')
    }
  }
}
</script>
