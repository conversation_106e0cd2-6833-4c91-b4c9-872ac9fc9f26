<template>
  <div class="date-range-picker" :id="id">
    <date-range-picker ref="picker" v-model="dateRange" :options="options" format="MM/DD/YYYY" className="date-range-picker_component" :key="key" />
    <button class="date-range-picker-input-arrow btn btn-primary btn-round btn-md" @click="openRangePicker">{{customLabel || label}}</button>
  </div>
</template>

<script>
import DateRangePicker from '@gravitano/vue-date-range-picker/src/components/DateRangePicker'
import {DateRange} from './rangeModels'

export default {
  name: 'rangeSelector',
  props: {
    value: Array,
    ranges: Object,
    maxRange: Object,
    customLabel: String
  },
  mounted () {
    this.$nextTick(x => {
      this.updateLabel()
      this.$refs.picker.$el.setAttribute('readonly', true)
    })
  },
  data () {
    let startRange = new DateRange({range: this.value})
    const id = this.$uuid.v4()
    return {
      id: id,
      label: '',
      range: startRange.asFormattedStrings(),
      options: {
        startDate: startRange.from,
        endDate: startRange.to,
        minDate: this.maxRange.from,
        maxDate: this.maxRange.to,
        buttonClasses: 'btn',
        applyButtonClasses: 'btn-primary font-weight-normal',
        cancelButtonClasses: 'btn-secondary font-weight-normal',
        ranges: this.ranges,
        opens: 'left',
        parentEl: '#' + id,
        locale: {
          format: 'LL'
        }
      },
      key: 0
    }
  },
  computed: {
    dateRange: {
      get () {
        return this.range
      },
      set (val) {
        if (val.length === 0) {
          return
        }

        this.range = val

        this.$nextTick(() => {
          this.updateLabel()
          this.$emit('input', {
            range: this.range,
            label: this.label
          })
        })
      }
    }
  },
  methods: {
    openRangePicker () {
      this.$refs.picker.$el.focus()
    },
    updateLabel () {
      this.label = this.$refs.picker.$el.value
    }
  },
  components: {
    DateRangePicker
  },
  watch: {
    value () {
      let newRange = new DateRange({range: this.value}).asFormattedStrings()
      if (!(newRange[0] === this.range[0] && newRange[1] === this.range[1])) {
        this.range = newRange
        this.key++
      }
    }
  }
}
</script>

<style scoped>
  .date-range-picker_component {
    height: 0;
    width: 0;
    padding: 0;
    border: 0;

    position: absolute;
    bottom: 0;
    right: 0;
  }

  .date-range-picker {
    position: relative;
  }

  .date-range-picker-input-arrow:after {
    display: inline-block;
    margin-left: 0.457em;
    content: "";
    margin-top: -0.28em;
    width: 0.42em;
    height: 0.42em;
    border: 1px solid;
    border-top: 0;
    border-left: 0;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
    vertical-align: middle;
  }
</style>

<style>
  .daterangepicker.show-calendar .drp-buttons{
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  @media (min-width: 564px) {
    .daterangepicker .ranges ul {
      width: auto;
    }

    .daterangepicker.show-ranges.show-calendar {
      min-width: 667px;
    }
  }

  @media (max-width: 564px) {
    .daterangepicker.show-calendar .drp-buttons {
      flex-wrap: wrap;
      flex-direction: column-reverse;
    }
    .daterangepicker.show-calendar .drp-buttons button{
      width: 100%;
      margin: 0 0 1rem 0;
    }
  }

</style>
