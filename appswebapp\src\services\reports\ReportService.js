import BaseService from '@/services/BaseService'

class ReportService extends BaseService {
  generateContactsReport (filters) {
    return this.axios.get(`/api/leads/report/contacts/generate`, { params: filters })
  };
  generateLeadsSettingsReport (filters) {
    return this.axios.get(`/api/leads/report/leads_settings/generate`, { params: filters })
  };
  generateLeadsWebFormReport (filters) {
    return this.axios.get(`/api/leads/report/leads_webform/generate`, { params: filters })
  };
  getContactReportFilterListing (filters) {
    return this.axios.get(`/api/leads/report/contacts/filters`, { params: filters })
  };
  createContactReportFilter (contactReportFilter) {
    return this.axios.post(`/api/leads/report/contacts/filters/create`, contactReportFilter)
  };
  updateContactReportFilter (contactReportFilter) {
    return this.axios.post(`/api/leads/report/contacts/filters/update`, contactReportFilter)
  };
  deleteContactReportFilter (id) {
    return this.axios.post(`/api/leads/report/contacts/filters/${id}/delete`)
  };
  getAllContactReportFilters () {
    return this.axios.get(`/api/leads/report/contacts/filters/all`)
  };
  getSearchSuggestions (search) {
    return this.axios.get('/api/leads/report/search/suggestions', { params: { searchPhrase: search } })
  };
}

export default new ReportService()
