$rtl-support: true;

@import "../_appwork/include";
@import "../_theme/common";
@import "../_theme/libs";
@import "../_theme/uikit";

$primary-color: #17b3a3;
$body-bg: #f8f8f8;

body {
  background: $body-bg;
}

@include appwork-common-theme($primary-color);
@include appwork-libs-theme($primary-color);
@include appwork-uikit-theme($primary-color);

// Navbar

@include appwork-navbar-variant('.bg-navbar-theme', #fff, $color: $text-muted, $active-color: $body-color);

.layout-navbar {
  box-shadow: 0 0 0 1px rgba($black, 0.04);
}

// Sidenav

@include appwork-sidenav-variant('.bg-sidenav-theme', #fff, $color: $text-muted, $active-color: $body-color, $menu-bg: darken($body-bg, 2.5%));

.bg-sidenav-theme {
  .sidenav-inner > .sidenav-item > .sidenav-link .sidenav-icon,
  .sidenav-inner > .sidenav-item > .sidenav-link:hover .sidenav-icon,
  .sidenav-inner > .sidenav-item > .sidenav-link:focus .sidenav-icon {
    color: $gray-300;
  }
  .sidenav-inner > .sidenav-item.active > .sidenav-link .sidenav-icon {
    color: $primary-color !important;
  }
  .sidenav-item.active > .sidenav-link:not(.sidenav-toggle) {
    color: $primary-color !important;
    background: none !important;
  }
}

.layout-sidenav {
  box-shadow: 0 0 0 1px rgba($black, 0.04);
}
.layout-sidenav-horizontal {
  box-shadow: 0 -1px 0 rgba($black, 0.04) inset;
}

// Footer

@include appwork-footer-variant('.bg-footer-theme', #f3f3f3, $color: $text-muted, $active-color: $body-color);
