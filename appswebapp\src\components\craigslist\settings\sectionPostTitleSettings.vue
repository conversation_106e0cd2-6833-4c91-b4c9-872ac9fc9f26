<template>
<editSettingsHelper title="Post Title Settings" @save="saveSettings" @cancel="cancel" @changeMode="changeMode" :isDisabled="isDisabled" :isLoading="isUpdatingProcessing" :isViewMode="isViewMode">
  <div slot="settings-content">
    <b-form-checkbox class="flex-inline-sized status-incoming-inv my-2" v-model="data.hasToIncludeExteriorColor" :disabled="isViewMode">
      Include Exterior Color
    </b-form-checkbox>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Custom Text:</span>
      <span v-if="isViewMode" slot="payload">{{data.addedTitleText}}</span>
      <b-form-input v-else max='200' size="sm" slot="payload" v-model='data.addedTitleText'></b-form-input>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Display Price:</span>
      <span v-if="isViewMode" slot="payload" size="sm">{{getAccountPriceDescription()}}</span>
      <multiselect  v-else size="sm" slot="payload" :allowEmpty='false' v-model='accountPriceTypeSelected' :options='accountPriceOptions' label='text' :multiple="false" :preselect-first="true"></multiselect>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Specific Location:</span>
      <span v-if="isViewMode" slot="payload">{{data.craigslistSpecificLocation}}</span>
      <b-form-input v-else slot="payload"  size="sm" v-model='data.craigslistSpecificLocation'></b-form-input>
    </detail-row>
  </div>
</editSettingsHelper>
</template>

<script>
import { mapGetters } from 'vuex'
import Multiselect from 'vue-multiselect'
import detailRow from '@/components/details/helpers/detailRow'
import CraigslistDescriptionHelper from '@/shared/craigslist/craigslistDescriptionHelper'
import globals from '../../../globals'
import editSettingsHelper from '../../_shared/editSettingsHelper.vue'

const craigslistDescriptionHelper = new CraigslistDescriptionHelper()

export default {
  name: 'section-exterior-color',
  props: {
    isDisabled: {
      type: Boolean,
      required: true
    },
    isUpdatingProcessing: {
      type: Boolean,
      required: true
    }
  },
  created () {
    this.populateData()
  },
  data () {
    return {
      isViewMode: true,
      accountPriceOptions: craigslistDescriptionHelper.getAccountPriceOptions(),
      accountPriceTypeSelected: null,
      data: {
        addedTitleText: '',
        craigslistSpecificLocation: '',
        hasToIncludeExteriorColor: false,
        accountPriceType: 0
      }
    }
  },
  components: {
    'multiselect': Multiselect,
    'detail-row': detailRow,
    editSettingsHelper
  },
  computed: {
    ...mapGetters('craigslistSettings', ['settingsPutData'])
  },
  methods: {
    changeMode (newMode) {
      this.isViewMode = newMode
    },
    saveSettings () {
      this.isViewMode = true
      this.data.accountPriceType = this.accountPriceTypeSelected.value
      this.$store.commit('craigslistSettings/setPostTitleSettings', this.data)
      this.putSettingsData()
    },
    cancel () {
      this.isViewMode = true
      this.populateData()
    },
    putSettingsData () {
      this.$emit('putSettingsData', this.settingsPutData)
    },
    populateData () {
      this.data.hasToIncludeExteriorColor = globals().getClonedValue(this.settingsPutData.hasToIncludeExteriorColor)
      this.data.craigslistSpecificLocation = globals().getClonedValue(this.settingsPutData.craigslistSpecificLocation)
      this.data.accountPriceType = globals().getClonedValue(this.settingsPutData.accountPriceType)
      this.data.addedTitleText = globals().getClonedValue(this.settingsPutData.addedTitleText)
      this.accountPriceTypeSelected = this.accountPriceOptions[this.data.accountPriceType]
    },
    getAccountPriceDescription () {
      return craigslistDescriptionHelper.getAccountPriceDescription(this.data.accountPriceType)
    }
  }
}
</script>

<style scoped>
 @media(min-width: 1600px) {
    .custom-settings-craigslist {
      width: 25%;
    }
 }

 @media(max-width: 1600px) {
    .custom-settings-craigslist {
      width: 30%;
    }
 }

 @media(max-width: 1200px) {
    .custom-settings-craigslist {
      width: 40%;
    }
 }

 @media(max-width: 800px) {
    .custom-settings-craigslist {
      width: 50%;
    }
 }
 @media(max-width: 400px) {
    .custom-settings-craigslist {
      width: 100%;
    }
 }
</style>
