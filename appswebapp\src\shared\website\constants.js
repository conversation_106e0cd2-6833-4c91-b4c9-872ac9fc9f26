const pageNavigationTypes = Object.freeze({
  home: {value: 1, label: 'Home', isEnabledToManage: true},
  inventory: {value: 2, label: 'Inventory', isEnabledToManage: true},
  financing: {value: 3, label: 'Financing', isEnabledToManage: true},
  warranty: {value: 4, label: 'Warranty', isEnabledToManage: false},
  shipping: {value: 5, label: 'Shipping', isEnabledToManage: false},
  dealershipInfo: {value: 6, label: 'Dealership Info', isEnabledToManage: true},
  termsOfSale: {value: 7, label: 'Terms Of Sale', isEnabledToManage: false},
  termsOfUse: {value: 8, label: 'Terms Of Use', isEnabledToManage: false},
  privacyPolicy: {value: 9, label: 'Privacy Policy', isEnabledToManage: false},
  genericHome: {value: 10, label: 'Generic Home', isEnabledToManage: false},
  dynamicIFrame: {value: 11, label: 'Dynamic IFrame', isEnabledToManage: false},
  customLink: {value: 12, label: 'Custom Link', isEnabledToManage: true},
  freeForm: {value: 13, label: 'Free Form', isEnabledToManage: true},
  tradeAppraisal: {value: 14, label: 'Trade Appraisal', isEnabledToManage: true},
  serviceDepartment: {value: 15, label: 'Service Department', isEnabledToManage: true},
  vehicleFinder: {value: 16, label: 'Vehicle Finder', isEnabledToManage: true},
  photoList: {value: 17, label: 'Photo List', isEnabledToManage: true},
  newVehicle: {value: 18, label: 'New Vehicle Research', isEnabledToManage: true},
  partsDepartment: {value: 20, label: 'Parts Department', isEnabledToManage: true},
  genericDepartment: {value: 21, label: 'Generic Department', isEnabledToManage: false},
  contactUs: {value: 22, label: 'Contact Us', isEnabledToManage: true},
  testimonials: {value: 23, label: 'Testimonials', isEnabledToManage: false},
  graphics: {value: 24, label: 'Graphics', isEnabledToManage: false},
  specials: {value: 28, label: 'Specials', isEnabledToManage: true},
  specialsDetail: {value: 30, label: 'Specials Detail', isEnabledToManage: false},
  compare: {value: 31, label: 'Compare', isEnabledToManage: false},
  customForm: {value: 33, label: 'Custom Form', isEnabledToManage: false},
  mapAndHours: {value: 34, label: 'Map and Hours', isEnabledToManage: false},
  selectAStore: {value: 35, label: 'Select a Store', isEnabledToManage: false},
  directions: {value: 37, label: 'Directions', isEnabledToManage: false},
  blog: {value: 38, label: 'Blog', isEnabledToManage: true},
  blogDetail: {value: 39, label: 'Blog Detail', isEnabledToManage: false},
  scheduleATestDrive: {value: 41, label: 'Schedule a Test Drive', isEnabledToManage: true},
  reviewUs: {value: 42, label: 'Review Us', isEnabledToManage: true},
  newModelOverview: {value: 43, label: 'New Model Overview', isEnabledToManage: false},
  manufacturerSpecialOffers: {value: 44, label: 'Manufacturer Special Offers', isEnabledToManage: false},
  vdp: {value: 80, label: 'Vehicle Detail Page', isEnabledToManage: false},
  siteMap: {value: 85, label: 'Site Map', isEnabledToManage: true},
  robots: {value: 86, label: 'Robots', isEnabledToManage: false},
  creditApplication: {value: 95, label: 'Credit Application', isEnabledToManage: false},
  pageNotFound: {value: 100, label: 'Page Not Found', isEnabledToManage: false},
  vwAccessories: {value: 112, label: 'Vw Accessories', isEnabledToManage: false},
  craigslist: {value: 801, label: 'Craigslist', isEnabledToManage: false},
  ebayNational: {value: 802, label: 'eBay National', isEnabledToManage: false},
  ebayLocal: {value: 803, label: 'eBay Local', isEnabledToManage: false},
  windowsSticker: {value: 804, label: 'Window Sticker', isEnabledToManage: false}
})

const inventoryTypeFilters = Object.freeze({
  vehicleStatus: 1,
  newUsedCarList: 2,
  specialList: 3,
  certifiedPreOwned: 4,
  make: 5,
  makeF: 28,
  model: 6,
  ebayList: 7,
  location: 8,
  cpoDealer: 9,
  yearFrom: 11,
  yearTo: 12,
  bodyStyle: 13,
  priceFrom: 14,
  priceTo: 15,
  milesFrom: 16,
  milesTo: 17,
  vehicleType: 18,
  daysInStockFrom: 19,
  daysInStockTo: 20,
  featured: 21,
  ebayLocalList: 22,
  extColor: 23,
  intColor: 24,
  transmissionClass: 25,
  transmissionType: 26,
  transmissionName: 27,
  sortOrder: 50,
  searchName: 51,
  historyCarfax: 52,
  historyCarfaxOneOwner: 53,
  historyAutocheck: 54,
  specialTypeList: 55,
  specialCategoryList: 56,
  specialItemList: 57,
  dmsStatus: 58,
  promoText: 59,
  fuelType: 60,
  cityMilesPerGallonFrom: 61,
  cityMilesPerGallonTo: 62,
  highwayMilesPerGallonFrom: 63,
  highwayMilesPerGallonTo: 64,
  drivetrain: 65,
  rvClass: 66,
  vehicleTrim: 67,
  offerId: 68,
  dealerGroupCPO: 70,
  showFilter: 71,
  bedStyle: 72,
  cabStyle: 73,
  truckType: 74,
  truckClass: 75,
  truckEngineMake: 76,
  truckEngineHpFrom: 77,
  truckEngineHpTo: 78,
  truckTransmissionMake: 79,
  truckTransmissionSpeed: 80,
  truckSleeperSizeFrom: 81,
  truckSleeperSizeTo: 82,
  truckSleeperRoof: 83,
  truckSleeperBunk: 84,
  truckSuspension: 85,
  refreshMemoryCache: 86,
  ignoreMemoryCache: 87,
  photoType: 88,
  minPhotos: 89,
  minPhotoSize: 90,
  featureIdsList: 91,
  distance: 92,
  latitude: 93,
  longitude: 94,
  truckAPUType: 95
})

const specialTypes = Object.freeze({
  inventory: {value: 1, apiValue: 'inventory', text: 'Inventory'},
  parts: {value: 2, apiValue: 'parts', text: 'Parts'},
  service: {value: 3, apiValue: 'services', text: 'Services'},
  other: {value: 4, apiValue: 'services', text: 'Other'},
  allSpecials: {value: 0, apiValue: null, text: 'All Specials'}
})

const specialConditionTypes = Object.freeze({
  new: { value: 1, text: 'New' },
  used: { value: 2, text: 'Used' },
  cpo: { value: 3, text: 'CPO' }
})

const specialFilterTypes = Object.freeze({
  specialTypes: 0,
  specialCategories: 1,
  conditions: 2,
  yearFrom: 3,
  yearTo: 4,
  makes: 5,
  models: 6,
  trims: 7,
  startEffectiveDate: 8,
  endEffectiveDate: 9,
  showPending: 10,
  siteId: 11
})

const userActivitySortTypes = Object.freeze({
  siteIdAsc: 1,
  siteIdDesc: 2,
  siteNameAsc: 3,
  siteNameDesc: 4,
  userNameAsc: 5,
  userNameDesc: 6,
  dateTimeAsc: 7,
  dateTimeDesc: 8,
  actionAsc: 9,
  actionDesc: 10,
  userTypeAsc: 11,
  userTypeDesc: 12
})

const userActivityActionTypes = [
  {value: 0, text: 'All Actions'},
  {value: 4, text: 'Create Page'},
  {value: 7, text: 'Delete Navigation Menu Item'},
  {value: 9, text: 'Delete Site Navigation Settings'},
  {value: 3, text: 'Delete Site Settings'},
  {value: 1, text: 'Insert Site Settings'},
  {value: 6, text: 'Update Navigation Menu Item'},
  {value: 5, text: 'Update Page Settings'},
  {value: 8, text: 'Update Site Navigation Settings'},
  {value: 2, text: 'Update Site Settings'},
  {value: 10, text: 'Run Enabling Website Process'},
  {value: 11, text: 'Run Disabling Website Process'},
  {value: 12, text: 'Run Enabling PAS Process'}
]

const accountListingSortTypes = Object.freeze({
  accountIdAsc: 1,
  accountIdDesc: 2,
  accountNameAsc: 3,
  accountNameDesc: 4
})

export {
  pageNavigationTypes,
  specialTypes,
  specialFilterTypes,
  specialConditionTypes,
  inventoryTypeFilters,
  userActivitySortTypes,
  userActivityActionTypes,
  accountListingSortTypes
}
