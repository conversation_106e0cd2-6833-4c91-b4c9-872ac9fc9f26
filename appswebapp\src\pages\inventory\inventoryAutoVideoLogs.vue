<template>
  <div class="position-relative">
    <h4>AutoVideo Logs</h4>
    <paging
      class="d-none d-sm-block p-0 custom-autovideo-logs-pagination"
      :pageNumber="filters.page"
      :pageSize="filters.pageSize"
      :totalItems="totalItemsCount"
      @numberChanged="pageChanged"
      @changePageSize="changePageSize"
    />
    <b-tabs v-model="selectedTab" class="nav-tabs-top nav-responsive-sm">
      <b-tab v-for="tab in tabOptions" :title="tab.title" :key="tab.value">
      </b-tab>
      <b-form @submit.prevent="applyFilter" class="m-4">
        <b-row>
          <b-col>
            <b-form-input v-model="filters.search" placeholder="Search..."></b-form-input>
          </b-col>
          <b-col>
            <b-input-group class="flex-nowrap">
              <b-input-group-prepend is-text>
                <i class="ion ion-md-calendar" slot="prepend"></i>
              </b-input-group-prepend>
              <date-time-picker
                ref="timeFrom"
                v-model="filters.dateFrom"
                :options="filterTimeOptions"
                format="MM/DD/YYYY HH:mm"
                placeholder="Date From"
                className="form-control"
                @change="onTimeFromInputChange"
              />
              <b-input-group-append
                is-text
                v-show="filters.dateFrom"
                @click="filters.dateFrom = null"
              >
                <i class="ion ion-md-close"></i>
              </b-input-group-append>
            </b-input-group>
          </b-col>
          <b-col>
            <b-input-group class="flex-nowrap">
              <b-input-group-prepend is-text>
                <i class="ion ion-md-calendar" slot="prepend"></i>
              </b-input-group-prepend>
              <date-time-picker
                ref="timeTo"
                v-model="filters.dateTo"
                :options="filterTimeOptions"
                format="MM/DD/YYYY HH:mm"
                placeholder="Date To"
                className="form-control"
                @change="onTimeToInputChange"
              />
              <b-input-group-append
                is-text
                v-show="filters.dateTo"
                @click="filters.dateTo = null"
              >
                <i class="ion ion-md-close"></i>
              </b-input-group-append>
            </b-input-group>
          </b-col>
          <b-col>
            <b-btn type="submit" variant="primary">Apply</b-btn>
          </b-col>
        </b-row>
      </b-form>
    </b-tabs>
    <b-card v-if="!isLoading">
      <b-table
        :items="items"
        :fields="getTableFields"
        :sort-by="tableSortBy"
        :sort-desc="tableSortDesc"
        @sort-changed="onSortChanged"
        show-empty
        no-local-sorting
        no-sort-reset
        hover
        striped
        responsive="sm"
        class="products-table card-table"
      >
        <template #cell(details)="data">
          <b-btn size="sm" @click="toggleDetails(data)">{{ data.detailsShowing ? "Hide" : "Show" }} Details</b-btn>
          <a class="btn btn-outline-secondary btn-sm" :href='getLogDetailsPath(data.item)' target="_blank">
            <i class="ion ion-md-open"></i>
          </a>
        </template>
        <template #row-details="data">
          <b-card>
            <b-table
              :items="data.item.details"
              :fields="getDetailsTableFields"
              striped
              hover
              responsive="sm"
            >
              <template #cell(renderedMessage)="detailsItem">
                <div class="rendered-message-box" v-html="getMessageDetails(detailsItem.item)"></div>
              </template>
            </b-table>
          </b-card>
        </template>
        <template #cell(actions)="data">
          <b-dropdown variant="outline-secondary icon-btn btn-round" size="sm" right no-caret boundary='viewport'>
            <template slot="button-content">
              <i class="ion ion-ios-more m-0"></i><span class="sr-only">Actions</span>
            </template>
            <b-dropdown-item @click="getVDPLink(data.item)">View VDP</b-dropdown-item>
          </b-dropdown>
        </template>
      </b-table>
      <!-- Pagination -->
      <paging
        :pageNumber="filters.page"
        :pageSize="filters.pageSize"
        :totalItems="totalItemsCount"
        titled
        pageSizeSelector
        @numberChanged="pageChanged"
        @changePageSize="changePageSize"
      />
    </b-card>
    <loader v-else class="mt-4" size="lg"></loader>
  </div>
</template>

<script>
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import VideoEncoderService from '@/services/inventory/VideoEncoderService'
import videoEncoderTypes from '../../shared/inventory/videoEncoderTypes'
import { getSkipNumber } from '@/shared/common/paginationHelpers'
import moment from 'moment'

const tabTypes = Object.freeze({
  processing: {
    value: videoEncoderTypes.photoToVideoLogTypes.processing,
    title: 'Processing Logs',
    getDataFromServer: (filters) => VideoEncoderService.getPhotoToVideoProcessingLogs(filters),
    getLogDetails: (id) => VideoEncoderService.getPhotoToVideoProcessingLogDetails(id)
  },
  queueManager: {
    value: videoEncoderTypes.photoToVideoLogTypes.queueManager,
    title: 'Queue Manager Logs',
    getDataFromServer: (filters) => VideoEncoderService.getPhotoToVideoQueueMangerLogs(filters),
    getLogDetails: (id) => VideoEncoderService.getPhotoToVideoQueueMangerLogDetails(id)
  }
})

const defaultValues = new ObjectSchema({
  tab: { type: String, default: tabTypes.processing.value },
  search: { type: String, default: '' },
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  sort: { type: Number, default: videoEncoderTypes.photoToVideoLogSortTypes.dateDesc }
})
const queryHelper = new QueryStringHelper(defaultValues)

export default {
  name: 'inventory-autovideo-logs',
  metaInfo: {
    title: 'AutoVideo Logs'
  },
  data () {
    return {
      isLoading: true,
      items: [],
      totalItemsCount: 0,
      filters: defaultValues.getObject(),
      filterTimeOptions: {
        autoUpdateInput: false,
        singleDatePicker: true,
        timePicker: true,
        timePicker24Hour: true,
        maxDate: new Date()
      },
      sitesInfoCache: {}
    }
  },
  created () {
    this.filters = queryHelper.parseQueryStringToObject(this.$router)
    this.synchronizeUrlAndReload()
  },
  components: {
    'paging': () => import('@/components/_shared/paging'),
    'date-time-picker': () => import('@gravitano/vue-date-range-picker/src/components/DateRangePicker'),
    'loader': () => import('@/components/_shared/loader.vue'),
    'log-node': () => import('@/components/_shared/logItemNode.vue')
  },
  computed: {
    selectedTab: {
      get () {
        let tabTypeIndex = this.tabOptions.findIndex(x => x.value === this.filters.tab)
        return tabTypeIndex >= 0 ? tabTypeIndex : 0
      },
      set (value) {
        let tabType = this.tabOptions[value]
        if (tabType) {
          this.filters.tab = tabType.value
        }
        this.filters.page = 1
        this.synchronizeUrlAndReload()
      }
    },
    getDetailsTableFields () {
      return [
        {
          key: 'renderedMessage',
          label: 'Message',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'timestamp',
          label: 'Date',
          tdClass: 'py-2 align-middle',
          formatter: value => {
            return moment(value).format('MM/DD/YYYY hh:mm:ss A')
          }
        }
      ]
    },
    getTableFields () {
      return [
        {
          key: 'properties.accountId',
          label: 'Account Id',
          tdClass: 'py-2 align-middle',
          sortable: false
        },
        {
          key: 'properties.vin',
          label: 'Vin',
          tdClass: 'py-2 align-middle',
          sortable: false
        },
        {
          key: 'timestamp',
          label: 'Date',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: videoEncoderTypes.photoToVideoLogSortTypes.dateAsc,
          sortTypeDesc: videoEncoderTypes.photoToVideoLogSortTypes.dateDesc,
          formatter: value => {
            return moment(value).format('MM/DD/YYYY hh:mm:ss A')
          }
        },
        {
          key: 'details',
          label: 'Details',
          tdClass: 'py-2 align-middle',
          thStyle: 'width: 200px',
          sortable: false
        },
        {
          key: 'actions',
          label: 'Actions',
          tdClass: 'py-2 align-middle',
          thStyle: 'width: 100px;',
          sortable: false
        }
      ]
    },
    tabOptions () {
      return Object.values(tabTypes)
    },
    refDateTimeFrom () {
      return (this.$refs.timeFrom || {}).$el || {}
    },
    refDateTimeTo () {
      return (this.$refs.timeTo || {}).$el || {}
    },
    sortType () {
      return this.filters.sort
    },
    tableSortBy () {
      const sortedColumn = this.getTableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.getTableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sortType
      } else {
        return false
      }
    }
  },
  methods: {
    pageChanged (newPage) {
      this.filters.page = newPage
      this.synchronizeUrlAndReload()
    },
    changePageSize (newSize) {
      this.filters.page = 1
      this.filters.pageSize = newSize
      this.synchronizeUrlAndReload()
    },
    onTimeFromInputChange (newVal) {
      this.refDateTimeFrom.value = newVal || this.filters.dateFrom || null
    },
    onTimeToInputChange (newVal) {
      this.refDateTimeTo.value = newVal || this.filters.dateTo || null
    },
    onSortChanged (value) {
      const sortingColumn = this.getTableFields.find(x => x.key === value.sortBy)
      if (value.sortDesc) {
        this.filters.sort = sortingColumn.sortTypeDesc
      } else {
        this.filters.sort = sortingColumn.sortTypeAsc
      }
      this.synchronizeUrlAndReload()
    },
    applyFilter () {
      this.filters.page = 1
      this.synchronizeUrlAndReload()
    },
    synchronizeUrlAndReload () {
      queryHelper.rebuildParamsInQueryString(this.$router, this.filters)
      this.isLoading = true
      this.populateData()
    },
    populateData () {
      let apiFilters = {
        searchPhrase: this.filters.search,
        dateFrom: this.filters.dateFrom,
        dateTo: this.filters.dateTo,
        sort: this.filters.sort,
        skip: getSkipNumber(this.filters.page, this.filters.pageSize),
        limit: this.filters.pageSize
      }
      this.tabOptions[this.selectedTab].getDataFromServer(apiFilters).then(res => {
        this.items = res.data.items
        this.totalItemsCount = res.data.totalCount
      }).catch(ex => {
        this.$toaster.error('Failed on receiving data from server')
      }).finally(() => {
        this.isLoading = false
      })
    },
    toggleDetails (data) {
      if (data.item.details) {
        data.toggleDetails()
      } else {
        this.tabOptions[this.selectedTab].getLogDetails(data.item.properties.batchId).then(res => {
          this.$set(data.item, 'details', res.data)
          data.toggleDetails()
        }).catch(ex => {
          this.$toaster.error('Failed on getting log details from server')
          console.error(ex)
        })
      }
    },
    getLogDetailsPath (item) {
      return this.$router.resolve({name: 'inventory-autovideo-log-details', params: { batchId: item.properties.batchId, logType: this.tabOptions[this.selectedTab].value }}).href
    },
    getMessageDetails (logItem) {
      let details = logItem.renderedMessage
      if (logItem.exception) {
        details = details.concat(': ', logItem.exception)
      }

      if (details.includes('Video url')) {
        let expression = /(https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|www\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9]+\.[^\s]{2,}|www\.[a-zA-Z0-9]+\.[^\s]{2,})/gi
        let regex = new RegExp(expression)
        details = details.replaceAll('"', '').replace(regex, `<a class="text-primary" href="$1?timestamp=${new Date().getTime()}" target="_blank" >$1</a>`)
      }

      return details
    },
    getVDPLink (item) {
      this.getSiteInfo(item.properties.accountId).then(res => {
        if (!res) {
          this.$toaster.error('Something went wrong!')
        } else {
          let url = res.urlWithProtocol + `/details.aspx?vin=${item.properties.vin}`
          window.open(url, '_blank')
        }
      })
    },
    async getSiteInfo (siteId) {
      if (this.sitesInfoCache[siteId]) {
        return this.sitesInfoCache[siteId]
      }

      try {
        const siteSettings = await this.$store.dispatch('siteSettings/getSiteSettings', siteId)
        this.sitesInfoCache[siteId] = siteSettings
        return siteSettings
      } catch (ex) {
        console.error(ex)
        return null
      }
    }
  }
}
</script>

<style>
.custom-autovideo-logs-pagination {
  position: absolute;
  right: 5px;
  top: 30px;
  z-index: 2;
}

.rendered-message-box {
  word-break: break-word;
}
</style>
