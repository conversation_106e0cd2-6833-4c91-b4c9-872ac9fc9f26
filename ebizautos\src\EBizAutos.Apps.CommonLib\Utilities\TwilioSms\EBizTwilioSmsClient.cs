﻿using EBizAutos.Apps.CommonLib.Abstract.TwilioSms;
using EBizAutos.Apps.CommonLib.Models.TwilioSms;
using System;
using System.Threading.Tasks;
using Twilio;
using Twilio.Exceptions;
using Twilio.Rest.Api.V2010.Account;
using Twilio.Types;
using static EBizAutos.Apps.CommonLib.Enums.TwilioMessagingEnums;

namespace EBizAutos.Apps.CommonLib.Utilities.TwilioSms {
	public class EBizTwilioSmsClient : ITwilioSmsClient {
		public EBizTwilioSmsClient(TwilioSettings twilioSettings) {
			Settings = twilioSettings;
			TwilioClient.Init(Settings.AccountSid, Settings.AuthToken);
		}

		#region Settings
		public TwilioSettings Settings { get; }
		#endregion

		public async Task<TwilioMessage> SendMessageAsync(string phoneNumberFrom, string phoneNumberTo, string body) {
			var messageOptions = new CreateMessageOptions(new PhoneNumber(phoneNumberTo)) {
				From = new PhoneNumber(phoneNumberFrom),
				Body = body
			};

			try {
				MessageResource message = await MessageResource.CreateAsync(messageOptions);

				return new TwilioMessage() {
					MessageFrom = phoneNumberFrom,
					MessageTo = phoneNumberTo,
					Body = body,
					MessageSid = message.Sid,
					DateCreated = DateTime.Now,
					MessageStatus = GetMessageStatusByStrValue(message.Status.ToString().ToLower())
				};
			} catch (ApiException exception) {
				if ((TwilioErrorCodeEnum)exception.Code != TwilioErrorCodeEnum.Undefined) {
					return new TwilioMessage() {
						MessageFrom = phoneNumberFrom,
						MessageTo = phoneNumberTo,
						Body = body,
						MessageSid = "",
						DateCreated = DateTime.Now,
						MessageStatus = MessageStatusEnum.Failed,
						ErrorCode = (TwilioErrorCodeEnum)exception.Code
					};
				} else {
					throw;
				}
			}
		}
	}
}