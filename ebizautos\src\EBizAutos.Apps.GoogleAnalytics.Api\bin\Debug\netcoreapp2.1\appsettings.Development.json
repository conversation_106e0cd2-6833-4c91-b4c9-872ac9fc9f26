{"Kestrel": {"EndPoints": {"Http": {"Url": "http://0.0.0.0:9002"}}}, "AppSettings": {"ApplicationName": "Apps Google Analytics API (dev)", "DataAccessEncryptKey": "fpABRddPOOg0hbm1PUHIjw==", "DataAccessEncryptIV": "AAAAAAAAAAAAAAAAAAAAAA==", "PAGGroupID": "5e209cb3b2ca3f10cc905c92"}, "AnalyticsTaskLimitSettings": {"TimeframeToCheckInHours": 1, "MaxTasksCountPerTimeframe": 5}, "DbSettings": {"UsersMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/cp?retryWrites=true&w=majority", "GoogleAnalyticsMongoDbConnectionString": "mongodb+srv://ebizdev:<EMAIL>/googleanalytics?retryWrites=true&w=majority", "GoogleAnalyticsMongoDbConnectionStringWithReadPreference": "mongodb+srv://ebizdev:<EMAIL>/googleanalytics?retryWrites=true&w=majority&readPreference=secondaryPreferred", "AdsCampaignUrlSettingsMongoDbConnectionString": "mongodb+srv://ebizdev:<EMAIL>/googleanalytics?retryWrites=true&w=majority&readPreference=secondaryPreferred", "AppsAccountsMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/accounts?retryWrites=true&w=majority", "AppsServiceBusLogsMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/appsservicebuslogs?retryWrites=true&w=majority", "GoogleAnalyticsEventProcessingLogsMongoDbConnectionString": "mongodb+srv://ebizdev:<EMAIL>/appsgoogleanalyticslogs?retryWrites=true&w=majority", "BrandExportsMongoDbRepositoryConnectionStrings": "mongodb+srv://ebizdev:<EMAIL>/googleanalytics?retryWrites=true&w=majority"}, "LogSettings": {"IsEnabled": true, "ConnectionString": "mongodb+srv://ebizdev:<EMAIL>/appsgoogleanalyticslogs?retryWrites=true&w=majority"}, "AppsApiSettings": {"AppsBaseUrl": "http://apps.dev.ebizautos/", "RequestTimeoutInMs": 300000, "AppsAuthorizationToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VyU2Vzc2lvbklkIjoiYzVkYzNkNmYtZGZlZC00YjYxLThjNGItNWNiMDU2OTMyYzVkIiwiVXNlcklkIjoiNWUxYzM0OWY3ZDMzN2NmMDJlZTAwOWY2IiwiaXNzIjoiQXBwc0FwaVNlcnZlciIsImF1ZCI6IkFwcHNBcGlBdWRpZW5jZSJ9.ReuQRU5ps573doZ-R8_GiadyN9x804ug7GdWzC7hUYU", "AccountManagementUpdatePermissionForAccountUsersUrlTemplate": "api/accounts/groups/{0}/permissions/update/accountuserspermissions"}, "GoogleAuthorizationSettings": {"ProjectName": "fresh-bloom-366121", "ClientName": "Google Analytics Web Service", "ClientId": "************-7lbk9rlidnduob8h5uc9ehp547gg062s.apps.googleusercontent.com", "ClientSecret": "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", "StateCacheItemExpirationTimeInHr": 6, "AccountLevelRedirectionUrl": "http://localhost:9002/api/ebizanalytics/authorization/retrieve_token", "ApplicationLevelRedirectionUrl": "http://localhost:9002/api/ebizanalytics/oauth2/retrieve_token"}, "ServiceBusSettings": {"Host": "b-14921ad0-337a-4458-84ee-c042b1327094-1.mq.us-east-1.amazonaws.com", "Port": 61617, "Username": "ebizeventbus", "Password": "z$32s0d-nd8G62!js83aPuhpUh", "FailoverHosts": ["b-14921ad0-337a-4458-84ee-c042b1327094-1.mq.us-east-1.amazonaws.com"], "HasToUseSsl": true, "PublishSettings": {"RetryAttempts": 3, "MinRetryDelayInMs": 5000, "MaxRetryDelayInMs": 10000}, "ReceiveEnpointSettings": {"LeadInsertedReceiveEnpointSettings": {"IsEnpointEnabled": true, "RetryCount": 3, "RetryTimeoutInSeconds": 5, "IsEnpointEventsRedeliveryEnabled": true, "EnpointEventsRedeliveryCount": 3, "EnpointEventsRedeliveryTimeoutInMinutes": 30}, "AccountReceiveEndpointSettings": {"IsReceiveEnpointEnabled": true, "ReceiveEnpointRetryCount": 3, "ReceiveEnpointRetryTimeoutInSeconds": 20, "IsReceiveEnpointRedeliveryEnabled": true, "ReceiveEnpointRedeliveryCount": 3, "ReceiveEnpointRedeliveryTimeoutInMinutes": 2}}}, "ExceptionSettings": {"ErrorWebServiceLocation": "http://errors.aws.ebizautos.com/queuedservice.asmx", "ApplicationCategoryId": 9, "MailServer": "l2ms04.ebizautos.colo", "ErrorEmailFrom": "<EMAIL>", "ErrorEmailTo": ["alex.i<PERSON><PERSON><PERSON>@ebizautos.com"], "EmailCc": [], "HasToUseServiceBus": false}}