<template>
  <editSettingsHelper :readOnlyMode="readOnlyMode" @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="Intro" :isLoading="isUpdatingProcessed" :isDisabled="isDisabled" :isViewMode="isViewMode">
    <template slot="settings-content">
      <detail-row v-if="accountLevel" :fixed-payload-width="true" :title-position="'start'">
        <span slot="title">Use Custom Intro Settings:</span>
        <b-form-checkbox v-if="!isViewMode" slot="payload" v-model="updatedSettings.hasToUseCustomIntroSettings" @change="onChangeUseCustom"></b-form-checkbox>
        <span v-else slot="payload">
          {{ updatedSettings.hasToUseCustomIntroSettings ? 'Yes' : 'No' }}
        </span>
      </detail-row>
      <b-overlay :show="!(!accountLevel || updatedSettings.hasToUseCustomIntroSettings)" opacity="0.7" blur="1.2px">
        <template #overlay>
          <span></span>
        </template>
        <ValidationProvider name="Intro Versions" ref="introVersionsValidator" v-slot="{errors}" :detectInput="false">
          <detail-row :fixed-payload-width="true" :title-position="'start'" :error="errors[0]">
            <span slot="title">Intro Versions</span>
            <div slot="payload" class="w-100">
              <b-form-group v-for="(intro, index) in updatedSettings.introItems" :key="index"
                :state="intro.introText ? null : false" :invalid-feedback="intro.introText ? '' : 'Intro text is required'">
                <template #label>
                  <b-form-checkbox v-model="intro.isEnabled" :disabled="isViewMode">
                    Enabled
                  </b-form-checkbox>
                </template>
                <b-input-group>
                  <b-form-input v-model="intro.introText" style="border-right:none;" :disabled="isViewMode"></b-form-input>
                  <b-input-group-append v-if="!isViewMode" @click="removeIntro(index)" style="cursor: pointer;" is-text>
                    <b-icon icon="x" aria-hidden="true" scale="1.2"/>
                  </b-input-group-append>
                </b-input-group>
              </b-form-group>
              <b-form-group v-if="!isViewMode">
                <template #label>
                  <b-form-checkbox v-model="intro.isEnabled">
                    Enabled
                  </b-form-checkbox>
                </template>
                <b-input-group>
                  <b-form-input v-model="intro.introText"></b-form-input>
                  <b-input-group-append @click="addIntro()" style="cursor: pointer;">
                    <b-input-group-text class="bg-primary text-white">
                      <b-icon icon="check" aria-hidden="true" scale="1.2"/>
                    </b-input-group-text>
                  </b-input-group-append>
                </b-input-group>
              </b-form-group>
            </div>
          </detail-row>
        </ValidationProvider>
      </b-overlay>
    </template>
  </editSettingsHelper>
</template>

<script>
import editSettingsHelper from '@/components/_shared/editSettingsHelper'
import detailRow from '@/components/details/helpers/detailRow'
import editSettingsMixin from '../editSettingsMixin'
import globals from '../../../../globals'

export default {
  name: 'tts-intro-settings-section',
  props: {
    settings: { type: Object, required: true },
    isUpdatingProcessed: { type: Boolean, required: true },
    isDisabled: Boolean,
    accountLevel: Boolean,
    readOnlyMode: Boolean
  },
  data () {
    return {
      updatedSettings: globals().getClonedValue(this.settings),
      isViewMode: true,
      intro: {
        introText: '',
        isEnabled: true
      }
    }
  },
  mixins: [editSettingsMixin],
  components: {
    detailRow,
    editSettingsHelper
  },
  methods: {
    addIntro () {
      if (!this.intro.introText) {
        this.$toaster.error('Intro Text is required')
        return
      }
      if (!this.updatedSettings.introItems) {
        this.updatedSettings.introItems = []
      }
      this.updatedSettings.introItems.push(this.intro)
      this.intro = {
        introText: '',
        isEnabled: true
      }
      this.validateIntroVersions()
    },
    removeIntro (index) {
      this.updatedSettings.introItems.splice(index, 1)
      this.validateIntroVersions()
    },
    updateSettings () {
      if (this.validateIntroVersions()) {
        this.$emit('saveChanges', this.updatedSettings)
      }
    },
    onChangeUseCustom () {
      this.validateIntroVersions()
    },
    validateIntroVersions () {
      this.$refs.introVersionsValidator.reset() // clean previously validation results
      if (this.accountLevel && !this.updatedSettings.hasToUseCustomIntroSettings) {
        return true
      }
      if (!this.updatedSettings.introItems || this.updatedSettings.introItems.length === 0) {
        this.$refs.introVersionsValidator.applyResult({
          errors: ['The Intro Versions field must have at least one elements'],
          valid: false,
          failedRules: {}
        })
        return false
      }
      if (this.updatedSettings.introItems && this.updatedSettings.introItems.length > 0) {
        return !this.updatedSettings.introItems.some(x => !x.introText)
      }
      return true
    }
  },
  watch: {
    'updatedSettings': {
      deep: true,
      handler: function () {
        this.$emit('input', this.updatedSettings)
      }
    }
  }
}
</script>
