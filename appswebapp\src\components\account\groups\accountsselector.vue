<template>
  <div v-if="readyToShow">
    <b-card>
      <div class="row">
        <div class="col">

          <div class="selected-accounts-box d-none d-xl-block">
            <div v-for="id in selectedIds" :key="id" class="selected-account-item mr-1">
              {{id}}
              <button type="button" @click="unselectAccountById(id)" aria-label="Close" class="close ml-1 text-dark remove-selected-account">×</button>
            </div>
          </div>

          <b-row>
            <b-col xl="12">
              <b-form @submit.prevent="applySearch" class="mb-2">
                <b-card no-body>
                  <b-card-body>
                    <div class="form-row">
                      <div class="col-12">
                        <b-input-group class="search-group">
                          <b-form-input ref="search" :value="filters.search" placeholder="Search..." class="d-inline-block float-sm-right"></b-form-input>
                          <b-input-group-append>
                            <b-btn type="submit">Go</b-btn>
                          </b-input-group-append>
                        </b-input-group>
                      </div>
                    </div>
                  </b-card-body>
                </b-card>
              </b-form>
            </b-col>
          </b-row>
          <div class="table-responsive ac-selector-table">
            <b-table
              striped
              hover
              :items="listing"
              :fields="fields">
              <template #cell(accountId)="data">
                  <div class="media align-items-center">
                    <div class="media-body d-block text-dark">{{data.item.accountId}}</div>
                  </div>
              </template>
              <template #cell(accountName)="data">
                  <div class="media align-items-center add-acc-name">
                    <div class="media-body d-block text-dark">{{data.item.accountName}}</div>
                    <button @click="selectAccount(data.item)" v-show="!isAccountSelected(data.item.accountId)" class="add-account-button btn btn-info btn-sm ml-2">+</button>
                    <button @click="unselectAccount(data.item)" v-show="isAccountSelected(data.item.accountId)" class=" btn btn-primary btn-sm ml-2">×</button>
                  </div>
              </template>
            </b-table>
          </div>
          <!-- Pagination -->
          <paging
            :pageNumber="filters.page"
            :pageSize="filters.pageSize"
            :totalItems="count"
            titled
            pageSizeSelector
            @numberChanged="pageChanged"
            @changePageSize="changePageSize">
          </paging>
        </div>
      </div>
    </b-card>
  </div>
</template>

<script>
import axios from 'axios'
import { ObjectSchema } from '@/shared/common/objectHelpers'
import paging from '@/components/_shared/paging.vue'

const defaultValues = new ObjectSchema({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 10 },
  sort: { type: Number, default: 0 },
  search: { type: String, default: '' }
})

export default {
  name: 'AccountsSelector',
  props: {
    selectedIds: {
      type: Array,
      default: function () {
        return []
      }
    }
  },
  data () {
    return {
      filters: null,
      listing: null,
      count: 0,
      readyToShow: false,
      fields: ['accountId', 'accountName']
    }
  },
  mounted () {
    let defaultFilters = defaultValues.getObject()
    this.filters = {page: defaultFilters.page, pageSize: defaultFilters.pageSize, search: defaultFilters.search}
    this.populateAccountListing()
      .then(x => {
        this.$watch('filters', (newVal, old) => { this.populateAccountListing() }, {deep: true})
        this.readyToShow = true
      })
  },
  methods: {
    populateAccountListing () {
      return axios
        .get('/api/accounts/list/0', { params: this.filters })
        .then(x => {
          let result = x.data
          this.filters.page = result.pageNumber
          this.filters.pageSize = result.pageSize
          this.filters.sort = result.sortType
          this.filters.search = result.search
          this.listing = result.accountsSummaries
          this.count = result.accountsCount
        }).catch(reason => {
          this.$logger.handleError(reason, 'Can\'t populate account listing', this.filters)
        })
    },
    isAccountSelected (accountId) {
      return this.selectedIds.indexOf(accountId) !== -1
    },
    applySearch () {
      let searchValue = this.$refs.search.$el.value
      this.filters.search = searchValue
      this.filters.page = 1
    },
    selectAccount (item) {
      this.$emit('onAccountSelected', item.accountId)
    },
    unselectAccount (item) {
      this.$emit('onAccountUnselected', item.accountId)
    },
    unselectAccountById (accountId) {
      this.$emit('onAccountUnselected', accountId)
    },
    pageChanged (newPage) {
      this.filters.page = newPage
    },
    changePageSize (newSize) {
      this.filters.pageSize = newSize
      this.filters.page = 1
    }
  },
  components: {
    'paging': paging
  }
}
</script>

<style>
@media (max-width: 425px) {
 .ac-selector-table {
   font-size: 12px;
  }
}

.add-account-button {
  background-color: #02cb77;
}

.add-account-button:hover {
  background-color: #02bc77;
}

.add-acc-name {
  word-break: break-word;
}

.search-group {
  width: 100%;
}

.selected-account-item {
  display: inline-block;
  background: transparent;
  margin-bottom: 5px;
  display: inline-block;
  background-color: #8897aa;
  border-radius: 10px;
  font-weight: 700;
  padding: 5px 10px;
  color: white;
  min-width: 82px;
  text-align: center;
}

.remove-selected-account {
    height: 100%;
    text-align: right;
    margin-top: -1px;
    border-radius: 50px;
    font-weight: bold;
}

.remove-selected-account:hover {
    color: white !important;
}
</style>
