<template>
  <b-table
    :fields="getTableFields"
    :items='items'
    :sort-by="tableSortBy"
    :sort-desc="tableSortDesc"
    @sort-changed="onSortChanged"
    :bordered="false"
    :no-sort-reset="true"
    :no-local-sorting="true"
    hover
    striped
    responsive>

    <template #cell(manage)="data">
      <div class='d-flex flex-column'>
        <b-btn @click="onEdit(data.item.id)" size="sm">Edit</b-btn>
        <b-btn @click="onDelete(data.item.id)" variant="primary" class='mt-1' size="sm">Delete</b-btn>
      </div>
    </template>
  </b-table>
</template>

<script>
import { conditionType, fieldType, spamFilterSortType, applicationType } from '@/shared/leads/spam'
import moment from 'moment'

export default {
  name: 'leads-spam-filter-listing',
  props: {
    items: { type: Array, required: true },
    sort: { type: Number, required: true }
  },
  computed: {
    getTableFields () {
      return [
        {
          key: 'applicationType',
          label: 'Application',
          tdClass: 'py-2 align-middle',
          formatter: value => {
            let type = Object.values(applicationType).find(x => x.value === value)

            if (type) {
              return type.label
            }
            return ''
          }
        },
        {
          key: 'fieldType',
          label: 'Field',
          tdClass: 'py-2 align-middle',
          formatter: value => {
            let type = Object.values(fieldType).find(x => x.value === value)

            if (type) {
              return type.label
            }
            return ''
          }
        },
        {
          key: 'conditionType',
          label: 'Condition',
          tdClass: 'py-2 align-middle',
          formatter: value => {
            let type = Object.values(conditionType).find(x => x.value === value)

            if (type) {
              return type.label
            }
            return ''
          }
        },
        {
          key: 'hasToIgnoreCase',
          label: 'Ignore Case',
          tdClass: 'py-2 align-middle',
          formatter: value => {
            return value ? 'True' : 'False'
          }
        },
        {
          key: 'value',
          label: 'Value',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'lastModifiedBy',
          label: 'Last Modified By',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'lastModifiedDateTime',
          sortable: true,
          sortTypeAsc: spamFilterSortType.createdDateAsc,
          sortTypeDesc: spamFilterSortType.createdDateDesc,
          label: 'Last Modified Date',
          tdClass: 'py-2 align-middle',
          formatter: value => moment(value).format('MM/DD/YYYY hh:mm A')
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    tableSortBy () {
      const sortedColumn = this.getTableFields.find(x => x.sortTypeAsc === this.sort || x.sortTypeDesc === this.sort)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.getTableFields.find(x => x.sortTypeAsc === this.sort || x.sortTypeDesc === this.sort)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sort
      } else {
        return false
      }
    }
  },
  methods: {
    onEdit (id) {
      this.$emit('edit', id)
    },
    onDelete (id) {
      this.$emit('delete', id)
    },
    onSortChanged (value) {
      const sortingColumn = this.getTableFields.find(x => x.key === value.sortBy)
      if (value.sortDesc) {
        this.$emit('sortChange', sortingColumn.sortTypeDesc)
      } else {
        this.$emit('sortChange', sortingColumn.sortTypeAsc)
      }
    }
  }
}
</script>
