<template>
  <details-section title="Boat Details" v-model="mode" v-if="boatDetails" @cancel="onCancel">

    <div class="view" v-if="mode === 'view'">
      <auto-detail-row :title="detailsFeatures.engineManufacturer.name" :text="getSelectedOptionOrSelf(detailsFeatures.engineManufacturer).key"/>

      <auto-detail-row title="Engine Model" :text="vehicle.engine"/>

      <auto-detail-row :title="detailsFeatures.usageHistory.name" :text="getSelectedAttributeOption(detailsFeatures.usageHistory).key"/>

      <auto-detail-row :title="detailsFeatures.driveType.name" :text="getSelectedOptionOrSelf(detailsFeatures.driveType).key"/>

      <auto-detail-row :title="detailsFeatures.boatLength.name" :text="getSelectedAttributeOption(detailsFeatures.boatLength).key"/>

      <auto-detail-row :title="detailsFeatures.beamLength.name" :text="getSelectedAttributeOption(detailsFeatures.beamLength).key"/>

      <auto-detail-row title="Fuel Type" :text="getFuelType"/>

      <auto-detail-row :title="detailsFeatures.fuelCapacity.name" :text="getSelectedAttributeOption(detailsFeatures.fuelCapacity).key"/>

      <auto-detail-row :title="detailsFeatures.hullMaterial.name" :text="getSelectedOptionOrSelf(detailsFeatures.hullMaterial).key"/>

      <auto-detail-row :title="detailsFeatures.trailer.name" :text="getSelectedAttributeOption(detailsFeatures.trailer).key"/>
    </div>

    <div class="edit" v-else-if="mode === 'edit'">
      <auto-detail-row
        v-model="detailsFeatures.engineManufacturer.value"
        :title="detailsFeatures.engineManufacturer.name"
        :options="getNameValueOptions(detailsFeatures.engineManufacturer.nameValueOptions)"
        validation-rule="xml"
        enableCustom
      />

      <auto-detail-row title="Engine Model" v-model="vehicle.engine" validation-rule="max:50|xml"/>

      <auto-detail-row :title="detailsFeatures.usageHistory.name" v-model="detailsFeatures.usageHistory.value" :options="getNameValueOptions(detailsFeatures.usageHistory.nameValueOptions)" />

      <auto-detail-row
        v-model="detailsFeatures.driveType.value"
        :title="detailsFeatures.driveType.name"
        :options="getNameValueOptions(detailsFeatures.driveType.nameValueOptions)"
        validation-rule="xml"
        enableCustom
      />

      <auto-detail-row :title="detailsFeatures.boatLength.name" v-model="detailsFeatures.boatLength.value" validation-rule="xml"/>

      <auto-detail-row :title="detailsFeatures.beamLength.name" v-model="detailsFeatures.beamLength.value" validation-rule="xml"/>

      <auto-detail-row title="Fuel Type" v-model="vehicle.engineFuelId" :options="metadata.engineFuelOptions"/>

      <auto-detail-row :title="detailsFeatures.fuelCapacity.name" v-model="detailsFeatures.fuelCapacity.value" :options="getNameValueOptions(detailsFeatures.fuelCapacity.nameValueOptions)" />

      <auto-detail-row
        v-model="detailsFeatures.hullMaterial.value"
        :title="detailsFeatures.hullMaterial.name"
        :options="getNameValueOptions(detailsFeatures.hullMaterial.nameValueOptions)"
        validation-rule="xml"
        enableCustom
      />

      <auto-detail-row :title="detailsFeatures.trailer.name" v-model="detailsFeatures.trailer.value" :options="getNameValueOptions(detailsFeatures.trailer.nameValueOptions)" />
    </div>

  </details-section>
</template>

<script>
import { mapGetters } from 'vuex'
import detailsSection from '@/components/details/detailsSection'
import autoDetailRow from '../helpers/autoDetailRow'
import featuresHelper from '../../../shared/details/featuresHelper'

export default {
  name: 'boat-details',
  data () {
    return {
      mode: 'view'
    }
  },
  computed: {
    ...mapGetters('details', ['vehicle', 'metadata', 'boatDetails']),
    detailsFeatures () {
      return {
        engineManufacturer: this.getFeatureById(-2002),
        usageHistory: this.getFeatureById(-2007),
        driveType: this.getFeatureById(-2001),
        boatLength: this.getFeatureById(-2006),
        beamLength: this.getFeatureById(-2000),
        fuelCapacity: this.getFeatureById(-2003),
        hullMaterial: this.getFeatureById(-2005),
        trailer: this.getFeatureById(-2004)
      }
    },
    getFuelType () {
      return this.metadata.engineFuelOptions[this.vehicle.engineFuelId] || '-'
    }
  },
  methods: {
    getFeatureById (id) {
      return featuresHelper.getFeatureById(this.boatDetails, id)
    },
    getNameValueOptions (nameValueOptions) {
      return featuresHelper.getNameValueOptions(nameValueOptions)
    },
    getSelectedAttributeOption (attribute) {
      return featuresHelper.getSelectedAttributeOption(attribute, true)
    },
    getSelectedOptionOrSelf (attribute) {
      return featuresHelper.getSelectedOptionOrSelf(attribute)
    },
    onCancel () {
      this.$forceUpdate()
    }
  },
  components: {
    'details-section': detailsSection,
    'auto-detail-row': autoDetailRow
  }
}
</script>
