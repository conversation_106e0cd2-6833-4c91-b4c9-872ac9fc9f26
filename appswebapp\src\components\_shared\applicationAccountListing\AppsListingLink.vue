<template>
    <div class="apps-link">
      <router-link v-if="forceLinkName" :to="{name: forceLinkName, params: {accountId: accountId}, query: queryProp }"><slot></slot></router-link>
      <router-link v-else-if="CanManageInventory" :to="{name: 'inventory-description', params: {accountId: accountId}, query: queryProp }"><slot></slot></router-link>
      <router-link v-else-if="CanManageAnalytics" :to="{name: 'analyticsGa4Dashboard',  params: {accountId: accountId}, query: queryProp }"><slot></slot></router-link>
      <router-link v-else-if="CanManageCraigslist" :to="{name: 'craigslist-dashboard-for-account',  params: {accountId: accountId}, query: queryProp }"><slot></slot></router-link>
      <router-link v-else-if="CanManageLeads" :to="{name: 'leads-dashboard',  params: {accountId: accountId}, query: queryProp }"><slot></slot></router-link>
    </div>
</template>

<script>
import applicationTypes from '../../../shared/common/applicationTypes'
import ExternalLinkItem from '../ExternalLinkItem'
import {mapGetters} from 'vuex'

export default {
  name: 'apps-listing-link',
  props: {
    accountId: Number,
    forceLinkName: String,
    query: Object
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {
        canManageAccountApplicationType: () => false
      }
    },
    queryProp () {
      if (this.query) {
        return this.query
      }
      return null
    },
    CanManageCraigslist () {
      return this.user.canManageAccountApplicationType(this.accountId, applicationTypes.AppsCraigslist.Id, applicationTypes.AppsCraigslist.PermissionFullAccess)
    },
    CanManageLeads () {
      return this.user.canManageAccountApplicationType(this.accountId, applicationTypes.AppsLeads.Id, applicationTypes.AppsLeads.PermissionFullAccess)
    },
    CanManageAnalytics () {
      return this.user.canManageAccountApplicationType(this.accountId, applicationTypes.AppsAnalytics.Id, applicationTypes.AppsAnalytics.PermissionFullAccess)
    },
    CanManageInventory () {
      return this.user.canManageAccountApplicationType(this.accountId, applicationTypes.InventoryManagement.Id, applicationTypes.InventoryManagement.PermissionFullAccess)
    }
  },
  components: {
    'external-link-item': ExternalLinkItem
  }
}
</script>

<style>
  .apps-link  a {
    color: black;
    text-align: center;
  }

  .apps-link a:hover {
    color: #3c8ae2;
  }
</style>
