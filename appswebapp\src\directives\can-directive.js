export default {
  bind: (domElement, binding, vnode) => {
    let hasBitPermission = false
    let hasLinkPermission = false
    let hasGroupPermission = false

    try {
      const user = (vnode.context.$store.state.users.informationForUser || {}).user

      const { value } = binding

      if (!user) {
        // nothing to do -_-
      } else if (binding.arg === 'group') {
        hasGroupPermission = checkGroupPermission(vnode.context.$router.resolve({path: value}).route, user)
      } else if (binding.arg === 'link') {
        hasLinkPermission = checkLinkPermission(vnode.context.$router.resolve({path: value}).route, user)
      } else if (binding.arg === 'to') {
        hasLinkPermission = checkLinkPermission(vnode.context.$router.resolve({...value}).route, user)
      } else {
        hasBitPermission = user.hasPermissions(value)
      }
    } catch (ex) {
      hasBitPermission = false
      hasLinkPermission = false
      hasGroupPermission = false
      vnode.context.$logger.handleError(ex, 'An error occurred on checking permissions')
    }

    if (!hasGroupPermission && !hasLinkPermission && !hasBitPermission) {
      domElement.innerHTML = ''
      domElement.style.display = 'none'
    }
  }
}

function checkGroupPermission (route, user) {
  let requestedApplicationType = route.meta.applicationType

  return user.canManageApplicationType(requestedApplicationType)
}

function checkLinkPermission (route, user) {
  if (route.fullPath === '/') {
    return false
  }

  const requestedAccountId = +route.params.accountId
  const requestedReportGroupId = route.params.reportGroupId
  const requestedApplicationType = +route.meta.applicationType
  const requestedPermissions = route.meta.permissions
  const requestedPermissionsFullAccess = route.meta.applicationFullAccess
  const isExactAccountMatchWithUserRequired = route.meta.exactAccountMatchWithUser
  const isAnalyticsReportGroupsAccessRequired = route.meta.analyticsReportGroupsAccess

  if (requestedPermissionsFullAccess && user.hasPermissions(requestedPermissionsFullAccess)) {
    return true
  }

  if (requestedPermissions && !user.hasPermissions(requestedPermissions)) {
    return false
  }

  if (isExactAccountMatchWithUserRequired && !user.hasPermissions(requestedPermissionsFullAccess) &&
      +user.accountId !== +requestedAccountId) {
    return false
  }

  if (isAnalyticsReportGroupsAccessRequired &&
      (!user.hasReportGroupsAccess || !user.hasAccessToReportGroup(requestedReportGroupId))) {
    return false
  }

  if (requestedAccountId) {
    return user.canManageAccountApplicationType(requestedAccountId, requestedApplicationType)
  }

  if (requestedPermissions && user.hasPermissions(requestedPermissions)) {
    return true
  }

  console.warn('No rules for:', route, user)
  return false
}
