<template>
  <common-analytics-table
    :tableItems="tableItems"
    :totalItems="totalItems"
    :tableFields="tableFields"
    :isPaginated="true"
    :sortType.sync="sortTypeProp"
    :pageNumber.sync="pageNumberProp"
    :pageSize.sync="pageSizeProp"
  >
    <template slot="row-details" slot-scope="{ item }">
      <b-card>
        <b-row class="mb-2">
          <b-col sm="12" md="4" lg="3" xl="2"><b>Form Leads:</b></b-col>
          <b-col>{{ $locale.formatNumber(item.item.formLeads) }}</b-col>
        </b-row>
        <b-row class="mb-2">
          <b-col sm="12" md="4" lg="3" xl="2"><b>SMS Leads:</b></b-col>
          <b-col>{{ $locale.formatNumber(item.item.smsLeads) }}</b-col>
        </b-row>
        <b-row class="mb-2">
          <b-col sm="12" md="4" lg="3" xl="2"><b>Phone Leads:</b></b-col>
          <b-col>{{ $locale.formatNumber(item.item.phoneLeads) }}</b-col>
        </b-row>
        <b-button size="sm" @click="item.toggleDetails">Hide Details</b-button>
      </b-card>
    </template>
  </common-analytics-table>
</template>

<script>
import analyticsConstants from '../../../shared/analytics/constants'

export default {
  name: 'display-account-level-table',
  props: {
    tableItems: { type: Array, required: true },
    totalItems: { type: Number, require: true },
    pageNumber: { type: Number, required: true },
    pageSize: { type: Number, required: true },
    sortType: { type: Number, required: true }
  },
  components: {
    'common-analytics-table': () => import('./commonAnalyticsTable.vue')
  },
  computed: {
    tableFields () {
      return [
        {
          key: 'dateFrom',
          label: 'Date',
          sortable: true,
          sortTypeAsc: analyticsConstants.displaySortTypes.dateAsc,
          sortTypeDesc: analyticsConstants.displaySortTypes.dateDesc
        },
        {
          key: 'spend',
          label: 'Spend',
          sortable: true,
          sortTypeAsc: analyticsConstants.displaySortTypes.spendAsc,
          sortTypeDesc: analyticsConstants.displaySortTypes.spendDesc,
          formatter: val => this.$locale.formatNumber(val)
        },
        {
          key: 'impressions',
          label: 'Impressions',
          sortable: true,
          sortTypeAsc: analyticsConstants.displaySortTypes.impressionsAsc,
          sortTypeDesc: analyticsConstants.displaySortTypes.impressionsDesc,
          formatter: val => this.$locale.formatNumber(val)
        },
        {
          key: 'clicks',
          label: 'Clicks',
          sortable: true,
          sortTypeAsc: analyticsConstants.displaySortTypes.clicksAsc,
          sortTypeDesc: analyticsConstants.displaySortTypes.clicksDesc,
          formatter: val => this.$locale.formatNumber(val)
        },
        {
          key: 'costPerClick',
          label: 'Cost Per Click',
          sortable: true,
          sortTypeAsc: analyticsConstants.displaySortTypes.CPCAsc,
          sortTypeDesc: analyticsConstants.displaySortTypes.CPCDesc,
          formatter: val => this.$locale.formatCurrency(val)
        },
        {
          key: 'clickThroughRate',
          label: 'Click Through Rate',
          sortable: true,
          sortTypeAsc: analyticsConstants.displaySortTypes.CTRAsc,
          sortTypeDesc: analyticsConstants.displaySortTypes.CTRDesc,
          formatter: val => `${val}%`
        },
        {
          key: 'sessions',
          label: 'Sessions',
          sortable: true,
          sortTypeAsc: analyticsConstants.displaySortTypes.sessionsAsc,
          sortTypeDesc: analyticsConstants.displaySortTypes.sessionsDesc,
          formatter: val => this.$locale.formatNumber(val)
        },
        {
          key: 'totalLeads',
          label: 'Total Leads',
          sortable: true,
          sortTypeAsc: analyticsConstants.displaySortTypes.totalLeadsAsc,
          sortTypeDesc: analyticsConstants.displaySortTypes.totalLeadsDesc,
          formatter: val => this.$locale.formatNumber(val)
        },
        {
          key: 'conversionRate',
          label: 'Conversion Rate',
          sortable: true,
          sortTypeAsc: analyticsConstants.displaySortTypes.convRateAsc,
          sortTypeDesc: analyticsConstants.displaySortTypes.convRateDesc,
          formatter: val => `${val}%`
        },
        {
          key: 'costPerLead',
          label: 'Cost Per Lead',
          sortable: true,
          sortTypeAsc: analyticsConstants.displaySortTypes.CPLAsc,
          sortTypeDesc: analyticsConstants.displaySortTypes.CPLDesc,
          formatter: val => this.$locale.formatCurrency(val)
        },
        {
          key: 'show_details'
        }
      ]
    },
    sortTypeProp: {
      get () {
        return this.sortType
      },
      set (newVal) {
        this.$emit('sortTypeChanged', newVal)
      }
    },
    pageNumberProp: {
      get () {
        return this.pageNumber
      },
      set (newVal) {
        this.$emit('pageNumberChanged', newVal)
      }
    },
    pageSizeProp: {
      get () {
        return this.pageSize
      },
      set (newVal) {
        this.$emit('pageSizeChanged', newVal)
      }
    }
  }
}
</script>

<style scoped>

</style>
