<template>
  <div class="position-relative">
    <b-row>
      <b-col>
        <h4>Campaign Types</h4>
      </b-col>
      <b-col>
        <b-btn variant="primary" @click='onCreateNewCampaignType' class="float-right">Create New Campaign Type</b-btn>
      </b-col>
    </b-row>
     <paging
        class="custom-leads-campaign-paging d-none d-md-block p-0"
        :pageNumber="filter.page"
        :pageSize="filter.pageSize"
        :totalItems="itemsTotalCount"
        @numberChanged="pageChanged"
        @changePageSize="changePageSize"
      />
      <b-tabs v-model='selectedTab' class="nav-tabs-top nav-responsive-sm" no-fade>
        <b-tab v-for='tab in tabOptions' :key='tab.key' :title='tab.title'>
          <div>
            <b-input-group class="mx-4 my-3 flex-nowrap w-50">
              <b-form-input placeholder="Search..." v-model="filter.search"></b-form-input>
              <b-input-group-append>
                <b-btn variant="primary" @click="applySearch()">Go</b-btn>
              </b-input-group-append>
            </b-input-group>
          </div>
        </b-tab>
      </b-tabs>
      <b-card
      v-if="!isLoading && itemsTotalCount > 0"
      >
        <b-table
          :fields="getTableFields"
          :items='items'
          striped
          responsive
          @sort-changed="onSortChange"
        >
          <template #cell(manage)="data">
            <div class="d-flex flex-column" v-if='filter.tabType !== campaignTabTypes.unmatched.value'>
              <b-btn size="sm" class="mb-1" @click="onEditCampaignType(data.item.campaignTypeId)"><font-awesome-icon icon="pencil-alt" size="sm" /> <span class="btn-title">Edit</span></b-btn>
              <!-- Campaign type with id: 0 cannot be deleted -->
              <b-btn size="sm" v-if="data.item.campaignTypeId > 0 && hasFullAccess" variant="primary" @click="onDeleteCampaignType(data.item.campaignTypeId)">Delete</b-btn>
            </div>
            <div class="d-flex flex-row" v-else>
              <b-btn size="sm" variant="primary" @click="onMoveToOnSite(data.item.campaignTypeId)">Move to on-site</b-btn>
            </div>
          </template>
        </b-table>
      <paging
        class="p-0"
        :pageNumber="filter.page"
        :pageSize="filter.pageSize"
        :totalItems="itemsTotalCount"
        titled
        pageSizeSelector
        @numberChanged="pageChanged"
        @changePageSize="changePageSize"
      />
      </b-card>
      <div v-else-if="isLoading" class="mt-5">
        <loader size="lg"/>
      </div>
      <div v-else class="text-muted">Not Found</div>
      <!-- Modal -->
      <campaign-type-modal ref="campaignTypeModal" :isShowModal="isShowModal" :title='modalOption.title' :mode="modalOption.mode"  @hide="hide"/>
  </div>
</template>

<script>
import paging from '@/components/_shared/paging'
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import campaignTypeModal from '@/components/leads/campaignTypes/campaignTypeModal'
import { campaignTabTypes } from '@/shared/leads/campaignTypes'
import loader from '@/components/_shared/loader'
import { mapGetters } from 'vuex'

const defaultFilters = new ObjectSchema({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  search: { type: String, default: '' },
  sort: { type: Number, default: 1 },
  tabType: { type: Number, default: 1 }
})

const queryStringHelper = new QueryStringHelper(defaultFilters)

export default {
  name: 'leads-campaign-types',
  metaInfo: {
    title: 'Campaign Types'
  },
  data () {
    return {
      filter: defaultFilters.getObject(),
      isLoading: true,
      isShowModal: false,
      campaignTabTypes,
      modalOption: {
        mode: 0, // default create mode
        title: 'Create New Campaign Type'
      },
      itemsTotalCount: 0,
      items: [],
      tabOptions: [{key: 1, title: 'On Site'}, {key: 2, title: 'Off Site'}, {key: 3, title: 'On Site Parent'}, {key: 4, title: 'Unmatched'}]
    }
  },
  components: {
    'paging': paging,
    'campaign-type-modal': campaignTypeModal,
    'loader': loader
  },
  created () {
    this.filter = queryStringHelper.parseQueryStringToObject(this.$router)
    this.loadContent()
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {
        canManageAccountApplicationType: () => false
      }
    },
    hasFullAccess () {
      return this.user && this.user.isEbizAdmin
    },
    getTableFields () {
      return [
        {
          key: 'campaignName',
          label: 'Campaign Type',
          tdClass: 'py-2 align-middle',
          sortable: true,
          thClass: 'col-lg-10 col-md-9 col-6'
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle',
          thClass: 'col-lg-2 col-md-3 col-6'
        }
      ]
    },
    selectedTab: {
      get: function () {
        if (this.filter.tabType > 4 || this.filter.tabType < 1) {
          return 1
        }

        let res = this.tabOptions.find(x => x.key === this.filter.tabType)
        if (res) {
          return res.key - 1
        }

        return 1
      },
      set: function (index) {
        this.filter.tabType = index + 1
        this.itemsTotalCount = 0
        this.filter.page = 1
        this.synchronizeUrlAndReload()
      }
    }
  },
  methods: {
    pageChanged (newPage) {
      this.filter.page = newPage
      this.synchronizeUrlAndReload()
    },
    changePageSize (newSize) {
      this.filter.pageSize = newSize
      this.filter.page = 1
      this.synchronizeUrlAndReload()
    },
    applySearch () {
      this.filter.page = 1
      this.synchronizeUrlAndReload()
    },
    onCreateNewCampaignType () {
      this.modalOption.mode = 0
      this.modalOption.title = 'Create New Campaign Type'
      this.populateCampaignTypeData('leadsCampaignTypes/getCampaignTypeNewPrototype')
    },
    onEditCampaignType (id) {
      this.modalOption.mode = 1 // set edit campaign type mode
      this.modalOption.title = 'Edit Campaign Type'
      this.populateCampaignTypeData('leadsCampaignTypes/populateCampaignType', { id: id, type: this.filter.tabType })
    },
    onMoveToOnSite (id) {
      this.modalOption.mode = 2 // set move unmatched campaign type to on site mode
      this.modalOption.title = 'Move Unmatched Campaign Type on Site'
      this.populateCampaignTypeData('leadsCampaignTypes/populateCampaignType', { id: id, type: this.filter.tabType })
    },
    async populateCampaignTypeData (methodName, params) {
      try {
        if (params) {
          await this.$store.dispatch(methodName, params)
        } else {
          await this.$store.dispatch(methodName)
        }
      } catch (ex) {
        if (ex.response && ex.response.data) {
          this.$toaster.error(`Cannot run ${methodName}, message: ${ex.response.data}`)
        } else {
          this.$toaster.error('Something went wrong!', {timeout: 5000})
        }
        this.$logger.handleError(ex, `Cannot run ${methodName}`)
        return
      }

      this.isShowModal = true
    },
    onDeleteCampaignType (id) {
      this.$store.dispatch('leadsCampaignTypes/deleteCampaignType', { id: id }).then(res => {
        this.$toaster.success('Campaign Type Successfully Deleted')
      }).catch(ex => {
        if (ex.response && ex.response.data) {
          this.$toaster.error(`Cannot delete Campaign Type with id ${id}. Message: ${ex.response.data}`, { timeout: 10000 })
        }
        this.$logger.handleError(ex, `Cannot delete Campaign Type with id ${id}`)
      }).finally(() => {
        this.synchronizeUrlAndReload()
      })
    },
    hide () {
      this.isShowModal = false
    },
    synchronizeUrlAndReload () {
      queryStringHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.isLoading = true
      this.loadContent()
    },
    onSortChange (ctx) {
      if (ctx.sortDesc) {
        this.filter.sort = 2
      } else {
        this.filter.sort = 1
      }
      queryStringHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.loadContent()
    },
    loadContent () {
      this.$store.dispatch('leads/getCampaignTypes', { filter: this.filter }).then(res => {
        this.items = res.data.campaigns
        this.itemsTotalCount = res.data.count
      }).catch(ex => {
        if (ex.response && ex.response.data) {
          this.$toaster.error(`Cannot get Campaign Type listing. Message: ${ex.response.data}`, { timeout: 10000 })
        }
        this.$logger.handleError(ex, `Cannot get Campaign Type listing. filter: ${this.filter}`)
      }).finally(() => {
        this.isLoading = false
      })
    }
  }
}
</script>

<style>
.custom-leads-campaign-paging {
  position: absolute;
  right: 5px;
  top: 35px;
  z-index: 2;
}
</style>
