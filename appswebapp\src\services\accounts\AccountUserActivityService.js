import axios from 'axios'
import userActivityConstants from '../../shared/accounts/userActivityConstants'

class AccountUserActivityService {
  getUserActivityGetterMethodByType (type) {
    switch (type) {
      case userActivityConstants.userActivityTypes.account.value:
        return this.getAccountLevelUserActivities
      case userActivityConstants.userActivityTypes.group.value:
        return this.getGroupLevelUserActivities
      case userActivityConstants.userActivityTypes.contact.value:
        return this.getContactLevelUserActivities
      default:
        return null
    }
  };
  getAccountLevelUserActivities (filters) {
    return axios.get('/api/accounts/useractivity/account', {params: filters})
  };
  getGroupLevelUserActivities (filters) {
    return axios.get('/api/accounts/useractivity/group', {params: filters})
  };
  getContactLevelUserActivities (filters) {
    return axios.get('/api/accounts/useractivity/contact', {params: filters})
  };
  getUserActivityDetails (id, type) {
    return axios.get(`/api/accounts/useractivity/${id}/details`, {params: {userActivityType: type}})
  };
}

export default new AccountUserActivityService()
