{"Kestrel": {"EndPoints": {"Http": {"Url": "http://0.0.0.0:9002"}}}, "AppSettings": {"ApplicationName": "Apps Google Analytics API (sandbox)", "DataAccessEncryptKey": "fpABRddPOOg0hbm1PUHIjw==", "DataAccessEncryptIV": "AAAAAAAAAAAAAAAAAAAAAA==", "PAGGroupID": "5e209cb3b2ca3f10cc905c92"}, "AnalyticsTaskLimitSettings": {"TimeframeToCheckInHours": 1, "MaxTasksCountPerTimeframe": 5}, "DbSettings": {"UsersMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/cp?retryWrites=true&w=majority", "GoogleAnalyticsMongoDbConnectionString": "mongodb+srv://ebizdev:<EMAIL>/googleanalytics?retryWrites=true&w=majority", "GoogleAnalyticsMongoDbConnectionStringWithReadPreference": "mongodb+srv://ebizdev:<EMAIL>/googleanalytics?retryWrites=true&w=majority&readPreference=secondaryPreferred", "AdsCampaignUrlSettingsMongoDbConnectionString": "mongodb+srv://ebizdev:<EMAIL>/googleanalytics?retryWrites=true&w=majority&readPreference=secondaryPreferred", "AppsAccountsMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/accounts?retryWrites=true&w=majority", "AppsServiceBusLogsMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/servicebuslogs?retryWrites=true&w=majority", "GoogleAnalyticsEventProcessingLogsMongoDbConnectionString": "mongodb+srv://ebizdev:<EMAIL>/appsgoogleanalyticslogs?retryWrites=true&w=majority", "BrandExportsMongoDbRepositoryConnectionStrings": "mongodb+srv://ebizdev:<EMAIL>/googleanalytics?retryWrites=true&w=majority"}, "LogSettings": {"IsEnabled": true, "ConnectionString": "mongodb+srv://ebizdev:<EMAIL>/appsgoogleanalyticslogs?retryWrites=true&w=majority"}, "AppsApiSettings": {"AppsBaseUrl": "http://sandbox-gateway.internal.aws.ebizautos.com/", "RequestTimeoutInMs": 300000, "AppsAuthorizationToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VyU2Vzc2lvbklkIjoiYTFiNDRiMzUtNDM2ZS00ZjcwLWFkZDItMmVlODQwMjZmYTg4IiwiVXNlcklkIjoiNWRmY2JjZDEwZjk4YjEyNDcwMzViZmJkIiwiaXNzIjoiQXBwc0FwaVNlcnZlciIsImF1ZCI6IkFwcHNBcGlBdWRpZW5jZSJ9.Ztq3-rrGfL3Y793d3GYeE4h-0MqlI3-Aa56bTzNY1K0", "AccountManagementUpdatePermissionForAccountUsersUrlTemplate": "api/accounts/groups/{0}/permissions/update/accountuserspermissions"}, "GoogleAuthorizationSettings": {"ProjectName": "fresh-bloom-366121", "ClientName": "Google Analytics Web Service", "ClientId": "************-7lbk9rlidnduob8h5uc9ehp547gg062s.apps.googleusercontent.com", "ClientSecret": "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", "StateCacheItemExpirationTimeInHr": 6, "AccountLevelRedirectionUrl": "https://apps.sandbox.ebizautos.com/api/ebizanalytics/authorization/retrieve_token", "ApplicationLevelRedirectionUrl": "https://apps.sandbox.ebizautos.com/api/ebizanalytics/oauth2/retrieve_token"}, "ServiceBusSettings": {"Host": "b-14ab2686-6644-4584-b08a-5a4d86f5fad1-1.mq.us-east-1.amazonaws.com", "Port": 61617, "Username": "ebizeventbus", "Password": "z$32s0d-nd8G62!js83aPuhpUh", "FailoverHosts": ["b-14ab2686-6644-4584-b08a-5a4d86f5fad1-1.mq.us-east-1.amazonaws.com"], "HasToUseSsl": true, "PublishSettings": {"RetryAttempts": 3, "MinRetryDelayInMs": 5000, "MaxRetryDelayInMs": 10000}, "ReceiveEnpointSettings": {"LeadInsertedReceiveEnpointSettings": {"IsEnpointEnabled": true, "RetryCount": 3, "RetryTimeoutInSeconds": 5, "IsEnpointEventsRedeliveryEnabled": true, "EnpointEventsRedeliveryCount": 3, "EnpointEventsRedeliveryTimeoutInMinutes": 30}, "AccountReceiveEndpointSettings": {"IsReceiveEnpointEnabled": true, "ReceiveEnpointRetryCount": 3, "ReceiveEnpointRetryTimeoutInSeconds": 20, "IsReceiveEnpointRedeliveryEnabled": true, "ReceiveEnpointRedeliveryCount": 3, "ReceiveEnpointRedeliveryTimeoutInMinutes": 20}}}, "ExceptionSettings": {"ErrorWebServiceLocation": "http://errors.internal.aws.ebizautos.com/queuedservice.asmx", "ApplicationCategoryId": 1, "MailServer": "email-01.internal.aws.ebizautos.com", "MailPort": 25, "MailUserName": "", "MailPassword": "", "MailEnableSsl": false, "ErrorEmailFrom": "<EMAIL>", "ErrorEmailTo": ["alex.i<PERSON><PERSON><PERSON>@ebizautos.com"], "EmailCc": [], "HasToUseServiceBus": true}}