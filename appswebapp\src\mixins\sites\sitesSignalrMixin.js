
import accountLevelFeatureStatuses from '@/shared/accounts/accountLevelFeatureStatuses'
import signalRSitesConnection from '@/signalR/sitesHub'

export default {
  methods: {
    connectToSignalR () {
      if (this.user.isEbizAdmin) {
        signalRSitesConnection.on('onWebsiteEnablingStarted', data => {
          this.onStartEnableWebsiteSignalRMessage(data)
        })
        signalRSitesConnection.on('onWebsiteEnablingFinished', data => {
          this.onFinishEnableWebsiteSignalRMessage(data)
        })
        signalRSitesConnection.on('onWebsiteDisablingStarted', data => {
          this.onStartDisableWebsiteSignalRMessage(data)
        })
        signalRSitesConnection.on('onWebsiteDisablingFinished', data => {
          this.onFinishDisableWebsiteSignalRMessage(data)
        })

        signalRSitesConnection.on('onPASEnablingStarted', data => {
          this.onStartEnablePASSignalRMessage(data)
        })
        signalRSitesConnection.on('onPASEnablingFinished', data => {
          this.onFinishEnablePASSignalRMessage(data)
        })
        signalRSitesConnection.on('onPASDisablingStarted', data => {
          this.onStartDisablePASSignalRMessage(data)
        })
        signalRSitesConnection.on('onPASDisablingFinished', data => {
          this.onFinishDisablePASSignalRMessage(data)
        })

        signalRSitesConnection.onclose(this.onConnectionCloseFromSignalR)
        this.establishConnection()
      }
    },
    async establishConnection () {
      await signalRSitesConnection.start().then(() => {
      }).catch(() => {
        return new Promise((resolve, reject) =>
          setTimeout(() => this.connectToSignalR().then(resolve).catch(reject), 5000))
      })
    },
    onConnectionCloseFromSignalR () {
      if (!this.isConnectionManuallyClosed) {
        this.establishConnection()
      }
    },
    disconnectFromSignalR () {
      if (this.user.isEbizAdmin) {
        this.isConnectionManuallyClosed = true
        signalRSitesConnection.stop()
      }
    },
    onStartEnableWebsiteSignalRMessage (accountId) {
      const account = this.items.find(x => x.accountId === accountId)
      if (account) {
        account.accountWebsiteStatus = accountLevelFeatureStatuses.ProcessEnabling.Value
      }

      this.$toaster.success(`Started enabling website for ${accountId}`, { timeout: 8000 })
    },
    onFinishEnableWebsiteSignalRMessage (accountId) {
      const account = this.items.find(x => x.accountId === accountId)
      if (account) {
        account.accountWebsiteStatus = accountLevelFeatureStatuses.Enabled.Value
      }

      this.$toaster.success(`Finished enabling website for ${accountId}`, { timeout: 8000 })
    },
    onStartDisableWebsiteSignalRMessage (accountId) {
      const account = this.items.find(x => x.accountId === accountId)
      if (account) {
        account.accountWebsiteStatus = accountLevelFeatureStatuses.ProcessDisabling.Value
      }

      this.$toaster.success(`Started disabling website for ${accountId}`, { timeout: 8000 })
    },
    onFinishDisableWebsiteSignalRMessage (accountId) {
      const account = this.items.find(x => x.accountId === accountId)
      if (account) {
        account.accountWebsiteStatus = accountLevelFeatureStatuses.Disabled.Value
      }

      this.$toaster.success(`Finished disabling website for ${accountId}`, { timeout: 8000 })
    },

    onStartEnablePASSignalRMessage (accountId) {
      const account = this.items.find(x => x.accountId === accountId)
      if (account) {
        account.accountPASStatus = accountLevelFeatureStatuses.ProcessEnabling.Value
      }

      this.$toaster.success(`Started enabling PAS for ${accountId}`, { timeout: 8000 })
    },
    onFinishEnablePASSignalRMessage (accountId) {
      const account = this.items.find(x => x.accountId === accountId)
      if (account) {
        account.accountPASStatus = accountLevelFeatureStatuses.Enabled.Value
      }

      this.$toaster.success(`Finished enabling PAS for ${accountId}`, { timeout: 8000 })
    },
    onStartDisablePASSignalRMessage (accountId) {
      const account = this.items.find(x => x.accountId === accountId)
      if (account) {
        account.accountPASStatus = accountLevelFeatureStatuses.ProcessDisabling.Value
      }

      this.$toaster.success(`Started disabling PAS for ${accountId}`, { timeout: 8000 })
    },
    onFinishDisablePASSignalRMessage (accountId) {
      const account = this.items.find(x => x.accountId === accountId)
      if (account) {
        account.accountPASStatus = accountLevelFeatureStatuses.Disabled.Value
      }

      this.$toaster.success(`Finished disabling PAS for ${accountId}`, { timeout: 8000 })
    }
  }
}
