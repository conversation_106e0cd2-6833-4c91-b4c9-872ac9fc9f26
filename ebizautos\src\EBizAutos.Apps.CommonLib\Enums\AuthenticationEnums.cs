using System;
using System.Collections.Generic;

namespace EBizAutos.Apps.CommonLib.Enums {
	public static class AuthenticationEnums {
		public enum UserTypeEnum {
			Undefined = 0,
			CPUser = 1,
			AdminUser = 2,
			eBizServiceUser = 3 //Fetcher, Import, Export, etc.
		}

		public enum PasswordEmailStatusEnum {
			Undefined,
			Sent,
			RecipientNotExisted,
			ExceptionOnSending
		}

		//[Description("Admin")] using "System.ComponentModel.Primitives": "4.1.0"
		public enum UserRoleEnum {
			Anonym = 0,
			Admin = 1,
			eBizDev = 5,

			// admin roles till 9
			eBizUserStandard = 10,
			eBizReps = 20,
			IT = 30,
			eBizService = 40,

			// eBizRoles till 99
			CPUser = 100
		}

		public enum MfaMethodEnum {
			Email = 1,
			Sms = 2
			// AuthenticatorApp = 3
		}

		[Flags]
		public enum UserPermissionEnum : ulong {
			DefaultAccess = 0,                           //0
			ManageUserAccount = 1 << 0,                  //1
			ManageCraigslist = 1 << 1,                   //2
			RepostOnCraigslist = 1 << 2,                 //4
			ViewLogs = 1 << 3,                           //8
			CraigslistFullAccess = 1 << 4,               //16 (eBizUserStandard & Admin)
			UsersFullAccess = ManageMultipleUserAccounts |
				ManageUserAccount |
				1 << 5,                                  //********** for manage users app (Admin)
			LeadsView = 1 << 6,                          //64 for new leads system
			LeadsManageCommunications = 1 << 7,          //128 for new leads system
			LeadsSendMessages = 1 << 8,                  //256 for new leads system
			LeadsLogView = 1 << 14,                      //16384 for new leads system
			LeadsManageDealerSettings = 1 << 19,         //524288 for leads system
			LeadsFullAccess =
				LeadsSendMessages |
				LeadsManageCommunications |
				LeadsView |
				LeadsLogView |
				LeadsManageDealerSettings |
				1 << 13,                                 //64 + 128 + 256 + 16384 + 524288 + 8192 = 549312 has all permissions for leads
			ManageLegacyLeads = 1 << 9,                  //512 for legacy leads
			ManageGoogleAnalytics = 1 << 10,             //1024
			GoogleAnalyticsFullAccess = 1 << 11,         //2048 (eBizUserStandard & Admin)
			GoogleAnalytics4View = 1 << 12,              //4096
			SiteBoxmanagerChangeSiteHosting = 1 << 15,   //32768 IT role
			SiteBoxManagerFullAccess =                   //98304
				SiteBoxmanagerChangeSiteHosting |
				1 << 16,                                 //65536 (eBizUserStandard & Admin)

			ViewErrors = 1 << 17,                        //131072
			ViewEncryptDecrypt = 1 << 18,                //262144

			IMManageVehicles = 1 << 20,                  //1048576
			IMFullAccess =
				IMManageVehicles |
				1 << 21,                                 //1048576 + 2097152 = 3145728

			AMViewSettings = 1 << 23,                    //8388608
			AMManageSettings = 1 << 24,                  //********
			AMFullAccess = AMViewSettings |
				AMManageSettings |
				1 << 25,                                 //8388608 + ******** + ******** = ********

			SMViewSettings = 1 << 27,                    //*********
			SMManageSettings = 1 << 28,                  //*********
			SMFullAccess = SMViewSettings |
				SMManageSettings |
				1 << 29,                                 //********* + ********* + ********* = *********

			ManageMultipleUserAccounts = ManageUserAccount |
				1 << 30,                                 // ********** for manage users app (ebizUserStandard and Admin)

			EBayViewSettings = 1ul << 32,                //**********
			EBayManageSettings = 1ul << 33,              //**********
			EBayFullAccess = EBayViewSettings |
							EBayManageSettings |
							1ul << 34,                   //********** + ********** + *********** = ***********

			ErrorsViewErrors = 1ul << 35,                //***********
			ErrorsViewErrorReports = 1ul << 36,          //***********

			ViewReports = 1ul << 37,                     //************

			EbizAutosAdmin = ulong.MaxValue >> 1,        // to separate admin and ebizDev

			FullAccess = ulong.MaxValue,                 //-1 in MongoDB
		}

		public static List<UserPermissionEnum> AccountListingAccessPermissions = new List<UserPermissionEnum>() {
			UserPermissionEnum.CraigslistFullAccess,
			UserPermissionEnum.LeadsFullAccess,
			UserPermissionEnum.GoogleAnalyticsFullAccess,
			UserPermissionEnum.SiteBoxManagerFullAccess,
			UserPermissionEnum.IMFullAccess,
			UserPermissionEnum.EbizAutosAdmin,
			UserPermissionEnum.FullAccess
		};

		public enum ApplicationTypeEnum : byte {
			Undefined = 0,
			Web = 1,
			Mobile = 2
		}
	}
}