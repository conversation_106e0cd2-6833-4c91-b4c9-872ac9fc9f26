import axios from 'axios'

export default {
  namespaced: true,
  state: {
    makes: null,
    models: null,
    years: null,
    styles: null,
    engines: null,
    exteriorColors: null,
    interiorColors: null
  },
  getters: {
    makes: s => s.makes || [],
    models: s => s.models || [],
    years: s => s.years || [],
    styles: s => s.styles || [],
    engines: s => s.engines || [],
    exteriorColors: s => s.exteriorColors || [],
    interiorColors: s => s.interiorColors || []
  },
  mutations: {
    setMakes (state, info) {
      state.makes = info.data
    },
    setModels (state, info) {
      state.models = info.data
    },
    setYears (state, info) {
      state.years = info.data
    },
    setStyles (state, info) {
      state.styles = info.data
    },
    setEngines (state, info) {
      state.engines = info.data
    },
    setExteriorColors (state, data) {
      state.exteriorColors = data
    },
    setInteriorColors (state, data) {
      state.interiorColors = data
    },
    resetStore (state) {
      state.makes = null
      state.models = null
      state.years = null
      state.styles = null
      state.engines = null
      state.exteriorColors = null
      state.interiorColors = null
    },
    resetColors (state) {
      state.exteriorColors = null
      state.interiorColors = null
    }
  },
  actions: {
    async populateCategoryMakes ({ commit }, vehicleType) {
      const result = await axios.get(`/api/inventory/categories/makes/${vehicleType}`)

      if (Array.isArray(result.data)) {
        result.data.sort((a, b) => (a || '').toString().toLowerCase().localeCompare((b || '').toString().toLowerCase()))
      }

      commit('setMakes', result)
      return result.data
    },

    async populateYears ({commit}) {
      const result = await axios.get(`/api/inventory/categories/years`)
      commit('setYears', result)
      return result.data
    },

    async populateCategoryModelsForMake ({commit}, parameters) {
      let params = {
        make: parameters.make,
        year: parameters.year
      }

      const result = await axios.get(`/api/inventory/categories/make/models`, { params })
      commit('setModels', result)
      return result.data
    },

    async populateStyles ({commit}, parameters) {
      let params = {
        year: parameters.year,
        make: parameters.make,
        model: parameters.model
      }

      const result = await axios.get(`/api/inventory/categories/styles`, { params })
      commit('setStyles', result)
      return result.data
    },

    async populateEngines ({commit}, parameters) {
      let params = {
        vehicleStyleId: parameters.vehicleStyleId
      }

      const result = await axios.get(`/api/inventory/categories/engines`, { params })
      commit('setEngines', result)
      return result.data
    },

    async populateExteriorColors ({commit}, parameters) {
      let params = {
        vehicleYearModelId: parameters.vehicleYearModelId
      }

      const result = await axios.get(`/api/inventory/categories/exteriorcolors`, { params })
      commit('setExteriorColors', result.data)
      return result.data
    },

    async populateInteriorColors ({commit}, parameters) {
      let params = {
        vehicleYearModelId: parameters.vehicleYearModelId
      }

      const result = await axios.get(`/api/inventory/categories/interiorcolors`, { params })
      commit('setInteriorColors', result.data)
      return result.data
    },
    async resetColors ({commit}) {
      commit('resetColors')
    }
  }
}
