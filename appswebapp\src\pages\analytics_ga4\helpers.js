import rangeHelper from '../../components/analytics_ga4/rangeSelector/rangeHelper'
import globals from '@/globals'
import store from '@/store/index'

export default {
  getFilterWithDefaultDateRange (router, filterManager) {
    let filter = filterManager.urlHelper.parseQueryStringToObject(router)
    if (filter.dateFrom === filterManager.defaultValue.dateFrom || filter.dateTo === filterManager.defaultValue.dateTo) {
      let defaultRange = rangeHelper.defaultRange.asFormattedStrings()
      filter.dateFrom = defaultRange[0]
      filter.dateTo = defaultRange[1]
    }
    return filter
  },

  getFilterForUrlQueryWithDefaultDateRange (filter, filterManager) {
    let defaultRange = rangeHelper.defaultRange.asFormattedStrings()
    if (filter.dateFrom === defaultRange[0] && filter.dateTo === defaultRange[1]) {
      let filterForRoute = globals().getClonedValue(filter)
      filterForRoute.dateFrom = filterManager.defaultValue.dateFrom
      filterForRoute.dateTo = filterManager.defaultValue.dateTo
      return filterForRoute
    }
    return filter
  },

  async fetchOrGetCachedApiResult (cache, actionName, parameters) {
    let resultPropName = actionName + 'result'
    let paramsPropName = actionName + 'params'
    if (cache[resultPropName] && cache[paramsPropName] && JSON.stringify(cache[paramsPropName]) === JSON.stringify(parameters)) {
      return cache[resultPropName]
    }

    const result = await store.dispatch(actionName, parameters)
    cache[resultPropName] = result
    cache[paramsPropName] = parameters
    return result
  }
}
