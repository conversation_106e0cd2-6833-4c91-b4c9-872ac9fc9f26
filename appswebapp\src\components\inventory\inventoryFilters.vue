<template>
  <div class="position-relative">

      <paging
      class="custom-tab-paging d-none d-md-block"
      :pageNumber="inventoryData.inventoryFilters.pageNumber"
      :pageSize="inventoryData.inventoryFilters.pageSize"
      :totalItems="inventoryData.vehiclesTotalCount"
      @numberChanged="onPageChanged"
      @changePageSize="onPageSizeChanged" />

      <b-tabs v-model="selectedTabIndex" class="nav-tabs-top nav-responsive-sm" no-fade>

        <b-tab v-for="(tab, index) in tabs" :key="index" :title="`${tab.title} (${tab.vehiclesTotal})`">
          <inventory-filters-form
          :buttons="getFilterOptions"
          :search="inventoryFilters.search"
          :variant="getFiltersFormVariant"
          @changeActive="onChangeActiveButton"
          @searchChanged="onChangeSearch" />
        </b-tab>

      </b-tabs>

    <b-card no-body>

      <inventory-listing
      v-if="siteSettings"
      :inventoryData="inventoryData"
      :inventoryDisplayType="inventoryDisplayType"
      :siteSettings="siteSettings"
      @sortChanged="onSortChanged"
      @listingChanged="onListingChanged"
      />

      <paging
      :pageNumber="inventoryData.inventoryFilters.pageNumber"
      :pageSize="inventoryData.inventoryFilters.pageSize"
      :totalItems="inventoryData.vehiclesTotalCount"
      titled
      pageSizeSelector
      @numberChanged="onPageChanged"
      @changePageSize="onPageSizeChanged" />

    </b-card>

  </div>
</template>

<script>
import globals from '@/globals'
import inventoryFiltersForm from './inventoryFiltersForm'
import inventoryListing from '@/components/inventory/inventoryListing.vue'
import vehicleStatuses from '@/shared/common/vehicle/vehicleStatuses'
import defaultInventoryFilters from './../../shared/inventory/inventoryFilters'
import inventoryService from '@/services/inventory/InventoryService'
import vehicleStatisticsMixin from '@/mixins/inventory/vehicleStatisticsMixin'
import inventoryFiltersMixin from '../../mixins/inventory/inventoryFiltersMixin'
import QueryStringHelper from '../../shared/common/queryStringHelper'
import paging from '@/components/_shared/paging.vue'
import conditions from '@/shared/common/vehicle/vehicleConditions'
import {displayTypes, priceTypes} from '@/shared/inventory/inventoryTypes'
import ConditionFilter from '@/shared/inventory/filterOption/conditionFilter' // id, conditionType, isActive, title, total
import PriceFilter from '@/shared/inventory/filterOption/priceFilter' // id, priceType, isActive, title, total
import InventoryFiltersBuilder from '@/shared/inventory/inventoryFiltersBuilder'

const queryStringHelper = new QueryStringHelper(defaultInventoryFilters)
let filtersBuilder = null

export default {
  name: 'inventory-filters',
  mixins: [vehicleStatisticsMixin, inventoryFiltersMixin],
  components: {
    'inventory-filters-form': inventoryFiltersForm,
    'inventory-listing': inventoryListing,
    'paging': paging
  },
  props: {
    inventoryDisplayType: {
      type: Number,
      required: true
    }
  },
  data: function () {
    return {
      siteSettings: null,
      inventoryData: {
        accountId: +this.$route.params.accountId,
        vehiclesTotalCount: null,
        vehicles: [],
        inventoryFilters: this.convertToViewModel(queryStringHelper.parseQueryStringToObject(this.$router))
      },
      tabs: [
        { vStatusKey: [vehicleStatuses.live.key], vStatus: [vehicleStatuses.live.value], title: vehicleStatuses.live.title, vehiclesTotal: 0 },
        { vStatusKey: [vehicleStatuses.inProgress.key], vStatus: [vehicleStatuses.inProgress.value], title: vehicleStatuses.inProgress.title, vehiclesTotal: 0 },
        { vStatusKey: [vehicleStatuses.sold.key, vehicleStatuses.pendingSell.key], vStatus: [vehicleStatuses.sold.value, vehicleStatuses.pendingSell.value], title: 'Sold & Pending', vehiclesTotal: 0 }
      ],
      filterOptions: [
        new ConditionFilter(0, conditions.all, false, 'Total', 0),
        new ConditionFilter(1, conditions.new, false, 'New', 0),
        new ConditionFilter(2, conditions.used, false, 'Used', 0),
        new ConditionFilter(3, conditions.cpo, false, 'CPO', 0),
        new PriceFilter(4, priceTypes.highPrice, false, 'Vehicles without a High Price', 0),
        new PriceFilter(5, priceTypes.lowPrice, false, 'Vehicles without a Low Price', 0)
      ]
    }
  },
  beforeMount () {
    this.filterOptions.find(x => x.id === this.inventoryFilters.optionId).isActive = true
  },
  mounted: function () {
    filtersBuilder = new InventoryFiltersBuilder(this.inventoryFilters)
    this.fetchInventoryBuilderModel()
    this.fetchTabStatistic()
    this.loadVehicleConditionsStatistic()
  },
  methods: {
    fetchTabStatistic () {
      return inventoryService
        .fetchVehicleStatuses(this.inventoryData.accountId)
        .then(result => {
          let vehicleStatusesStatistic = this.getStatusesStatisticByVehicles(result.data)
          this.tabs.forEach(function (element) {
            element.vehiclesTotal = 0
            for (let i = 0; i < element.vStatusKey.length; i++) {
              element.vehiclesTotal += vehicleStatusesStatistic[element.vStatusKey[i]].vehiclesTotal
            }
          })
        })
        .catch(reason => {
          this.$logger.handleError(reason, 'Can\'t fetch tab statistic', {accountId: this.inventoryData.accountId})
        })
    },
    getTabIndex (vStatus) {
      let index = this.tabs.findIndex(x => x.vStatus.equals(vStatus, true))
      if (index >= 0) {
        return index
      }

      return 0
    },
    loadVehicleConditionsStatistic (vStatus, search) {
      let filters = this.convertToApiModel(this.inventoryFilters)
      let requestData = {
        vStatus: filters.vStatus,
        search: filters.search
      }
      let responseResult = {}
      return inventoryService.fetchVehicleConditionsStatistic(
        this.inventoryData.accountId,
        vStatus || requestData.vStatus,
        search || requestData.search
      ).then(result => {
        let allCount = 0
        let newCount = 0
        let usedCount = 0
        let cpoCount = 0
        responseResult = result
        let responseData = result.data || []
        responseData.forEach((element) => {
          switch (element.condition) {
            case conditions.all:
              allCount = element.count
              break
            case conditions.new:
              newCount = element.count
              break
            case conditions.used:
              usedCount = element.count
              break
            case conditions.cpo:
              cpoCount = element.count
              break
          }
        })
        this.filterOptions.find(x => x.conditionType === conditions.all).total = allCount
        this.filterOptions.find(x => x.conditionType === conditions.new).total = newCount
        this.filterOptions.find(x => x.conditionType === conditions.used).total = usedCount
        this.filterOptions.find(x => x.conditionType === conditions.cpo).total = cpoCount
      }).catch(reason => {
        this.$toaster.error('Something went wrong. Please reload page')
        this.$logger.handleError(reason, 'Can\'t fetch vehicle condition statistics', { request: requestData, response: responseResult })
      })
    },
    onPageChanged (newVal) {
      filtersBuilder.changePage(newVal)
      queryStringHelper.rebuildParamsInQueryString(this.$router, this.convertToApiModel(this.inventoryFilters))
    },
    onPageSizeChanged (newPageSize) {
      filtersBuilder.changePageSize(newPageSize)
      queryStringHelper.rebuildParamsInQueryString(this.$router, this.convertToApiModel(this.inventoryFilters))
    },
    onChangeActiveButton (newValue) {
      let filterOption = this.filterOptions.find(x => x.id === newValue)
      filtersBuilder.changeActiveButton(filterOption)

      this.setActiveById(filterOption.id)
      queryStringHelper.rebuildParamsInQueryString(this.$router, this.convertToApiModel(this.inventoryFilters))
    },
    onChangeSearch (newVal) {
      filtersBuilder.changeSearch(newVal)
      this.loadVehicleConditionsStatistic(null, newVal)
      queryStringHelper.rebuildParamsInQueryString(this.$router, this.convertToApiModel(this.inventoryFilters))
    },
    onSortChanged (sortType) {
      filtersBuilder.changeSort(sortType)
      queryStringHelper.rebuildParamsInQueryString(this.$router, this.convertToApiModel(this.inventoryFilters))
    },
    setActiveById (id) {
      let filterOption = this.filterOptions.find(x => x.id === id)
      this.filterOptions.forEach(x => { x.isActive = false })
      filterOption.isActive = true
    },
    fetchInventoryBuilderModel () {
      let filters = this.convertToApiModel(this.inventoryFilters)
      let responseResult = {}
      return inventoryService.fetchInventoryBuilderModel(this.inventoryData.accountId, filters)
        .then(result => {
          responseResult = result
          let responseData = result.data || {}
          this.inventoryData.vehiclesTotalCount = responseData.vehiclesTotalCount || 0
          this.inventoryData.vehicles = responseData.vehicles || []
          this.siteSettings = responseData.siteSettings
        })
        .catch(reason => {
          this.$toaster.error('Something went wrong. Please reload page')
          this.$logger.handleError(reason, 'Can\'t fetch inventory builder model', {request: filters, response: responseResult})
        })
    },
    onListingChanged () {
      this.fetchInventoryBuilderModel()
    },
    isAlertAvailable () {
      let currentVStatus = this.inventoryFilters.vStatus
      return this.inventoryDisplayType === displayTypes.pricing && (
        currentVStatus.equals([vehicleStatuses.live.value]) ||
        currentVStatus.equals([vehicleStatuses.inProgress.value])
      )
    }
  },
  computed: {
    getFiltersFormVariant () {
      return this.inventoryDisplayType === displayTypes.pricing ? 'select' : 'buttons'
    },
    inventoryFilters: {
      get () {
        return this.inventoryData.inventoryFilters
      },
      set (value) {
        this.inventoryData.inventoryFilters = value
      }
    },
    selectedTabIndex: {
      get: function () {
        return this.getTabIndex(this.inventoryFilters.vStatus)
      },
      set: function (index) {
        this.setActiveById(this.filterOptions.find(x => x.conditionType === conditions.all).id)

        let updatedInventoryFilters = globals().getClonedValue(defaultInventoryFilters.getObject())
        updatedInventoryFilters.vStatus = this.tabs[index].vStatus

        const apiFilters = this.convertToApiModel(updatedInventoryFilters)
        queryStringHelper.rebuildParamsInQueryString(this.$router, apiFilters)
        this.loadVehicleConditionsStatistic(apiFilters.vStatus, null)
      }
    },
    getFilterOptions () {
      let filterOptions = []
      this.filterOptions.forEach(x => {
        if (x.hasOwnProperty('priceType')) {
          if (!this.isAlertAvailable()) {
            return void 0
          }
        }

        filterOptions.push(x)
      })

      return filterOptions
    }
  },
  watch: {
    $route: {
      handler () {
        this.inventoryFilters = this.convertToViewModel(queryStringHelper.parseQueryStringToObject(this.$router))
        filtersBuilder = new InventoryFiltersBuilder(this.inventoryFilters)
        this.fetchInventoryBuilderModel()
      },
      deep: true
    }
  }
}
</script>

<style lang="scss">
.custom-tab-paging {
  position: absolute;
  right: -10px;
  top: -15px;
  z-index: 2;
}
@media (max-width: 575px) {
  .nav-responsive-sm > .nav, .nav-responsive-sm > div > .nav {
      flex-wrap: nowrap!important;
      -webkit-box-orient: horizontal;
      -webkit-box-direction: normal;
      -ms-flex-direction: row;
      flex-direction: row;
      border: 0;
      overflow-x: scroll;
      white-space: nowrap;
      -webkit-overflow-scrolling: touch;
  }
  .nav-responsive-sm > div > .nav-tabs .nav-item {
    -webkit-box-flex: 1 !important;
    -ms-flex: 1 1 auto !important;
    flex: 1 1 auto !important;
  }
}
</style>
