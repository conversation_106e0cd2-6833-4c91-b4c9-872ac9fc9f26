<template>
  <div class="p-3">
    <div class="d-flex justify-content-end mb-1">
      <l-button :loading="isLoading" class="btn-round" size="sm" variant="outline-secondary" @click="populateData">Refresh</l-button>
    </div>
    <div v-if="!isLoading && reports.length > 0">
      <b-card no-body class="mb-1" v-for="report in reports" :key="report.ipAddress">
        <b-card-header header-tag="header" class="p-3" role="tab">
          <div class="float-left" v-b-toggle="report.ipAddress">
            <span class="text-muted text-left">{{report.ipAddress}}</span>
          </div>
          <b-icon class="float-right" font-scale="1.5" icon="files" @click="copyIp(report.ipAddress)" style="cursor: pointer;"></b-icon>
        </b-card-header>
        <b-collapse :id="report.ipAddress" visible role="tabpanel">
          <b-card-body>
            <b-table
              :items="report.items"
              :fields="fields"
              hover
              striped
              responsive
            >
              <template #cell(user)="data">
              {{ data.item.firstName }} {{ data.item.lastName }} ({{ data.item.userName }})
              </template>
              <template #cell(account)="data">
                {{ getAccountTitle(data.item) }}
              </template>
            </b-table>
          </b-card-body>
        </b-collapse>
      </b-card>
    </div>
    <div v-else-if="!isLoading">
      <span class="text-muted">No reports</span>
    </div>
  </div>
</template>

<script>
import SystemToolsService from '../../services/systemTools/SystemToolsService'

export default {
  name: 'user-auth-log-report',
  data () {
    return {
      reports: [],
      isLoading: true
    }
  },
  created () {
    this.populateData()
  },
  computed: {
    fields () {
      return [
        {
          key: 'user',
          label: 'User',
          tdClass: 'py-2 align-middle',
          thStyle: 'min-width: 300px;'
        },
        {
          key: 'account',
          label: 'Account',
          tdClass: 'py-2 align-middle',
          thStyle: 'width: 50%; min-width: 300px;'
        }
      ]
    }
  },
  methods: {
    populateData () {
      this.isLoading = true
      SystemToolsService.getUserAuthorizationLogsGroupByIpAddress().then(res => {
        this.reports = res.data
      }).catch(ex => {
        this.$toaster.error('Something went wrong!')
      }).finally(() => {
        this.isLoading = false
      })
    },
    getAccountTitle (item) {
      if (item.accountName) {
        return `${item.accountName} (${item.accountId})`
      }
      if (item.accountId > 0) {
        return item.accountId
      }
      return '-'
    },
    copyIp (ip) {
      this.$copyProvider.copyTextToClipboard(ip).then(() => {
        this.$toaster.success(`Copied the ${ip}`, { timeout: 4000 })
      }).catch(err => {
        console.error(err)
      })
    }
  }
}
</script>
