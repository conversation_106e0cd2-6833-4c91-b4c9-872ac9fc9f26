<template>
  <b-modal
    :visible="isVisible"
    @hide="hideModal"
    centered
    title="Send Email"
  >
    <div v-if="isDataLoaded">
      <detail-row largePayloadWidth>
        <span slot="title">Send Reply As:</span>
        <b-form-select v-model="selectedContact" :options="availableContacts" text-field="contactNameWithDepartments" value-field='contactId' slot="payload"></b-form-select>
      </detail-row>
      <detail-row largePayloadWidth>
        <span slot="title">Email Template:</span>
        <b-form-select v-model="selectedEmailTemplate" @change="emailTemplateChange" :options="availableEmailTemplates" text-field="name" value-field='id' slot="payload"></b-form-select>
      </detail-row>
      <detail-row largePayloadWidth>
        <span slot="title">Subject:</span>
        <b-form-input v-model="sendEmailOptions.subject" slot="payload"></b-form-input>
      </detail-row>
      <detail-row largePayloadWidth>
        <span slot="title">Message:</span>
        <b-form-textarea rows="4" no-resize v-model="sendEmailOptions.emailMessage" slot="payload"></b-form-textarea>
      </detail-row>
    </div>
    <loader v-else size="md"></loader>
    <template #modal-footer>
      <l-button :loading="isProcessingEmailSending" @click="sendEmail" variant="primary">Send Email</l-button>
      <b-btn @click="hideModal">Cancel</b-btn>
    </template>
    </b-modal>
</template>

<script>
import InventoryService from '@/services/inventory/InventoryService'
import LeadsEmailBuilder from '@/shared/leads/email/leadsEmailBuilder'
import CommunicationService from '@/services/leads/CommunicationService'
import CustomEmailTemplateService from '@/services/leads/CustomEmailTemplateService'
import { communicationTypes } from '@/shared/leads/common'
import ConversationService from '@/services/leads/ConversationService'
import detailRow from '../../details/helpers/detailRow.vue'
import loader from '../../_shared/loader.vue'

export default {
  name: 'email-reply',
  data () {
    return {
      isDataLoaded: false,
      isVisible: false,
      isProcessingEmailSending: false,
      availableContacts: [],
      availableEmailTemplates: [],
      sendEmailOptions: {
        subject: '',
        emailMessage: ''
      },
      selectedContact: null,
      selectedEmailTemplate: null,
      dealerInfo: null,
      siteUrlWithProtocol: '',
      conversationId: '',
      accountId: 0,
      vin: '',
      userConversation: null
    }
  },
  components: {
    detailRow,
    loader
  },
  methods: {
    async showEmailReplyModal (accountId, conversationId, vin, userConversation) {
      this.accountId = accountId
      this.conversationId = conversationId
      this.vin = vin
      this.userConversation = userConversation
      await this.populateExtraOptions(accountId)
      this.isVisible = true
    },
    hideModal () {
      this.resetData()
    },
    populateExtraOptions (accountId) {
      const availableContactsPromise = CommunicationService.getAvailableContacts(accountId, { communicationtype: communicationTypes.email.value, includeexistingcontacts: true }).then(res => {
        this.availableContacts = res.data
        this.selectedContact = res.data[0] ? res.data[0].contactId : 0
      }).catch(ex => {
        this.$logger.handleError(ex, `Cannot get available contact for accountId ${accountId}`)
      })

      const customEmailTemplatesPromise = CustomEmailTemplateService.getCustomEmailTemplates(accountId).then(res => {
        this.availableEmailTemplates = res.data.templates
        this.selectedEmailTemplate = res.data.templates[0] ? res.data.templates[0].id : 0
        this.sendEmailOptions.subject = res.data.templates[0] ? res.data.templates[0].subject : ''
        this.sendEmailOptions.emailMessage = res.data.templates[0] ? res.data.templates[0].messageBody : ''
      }).catch(ex => {
        this.$logger.handleError(ex, `Cannot get email templates for accountId ${accountId}`)
      })

      const siteSettingsPromise = this.$store.dispatch('siteSettings/getSiteSettings', accountId).then(res => {
        this.siteUrlWithProtocol = res.urlWithProtocol
      }).catch(ex => {
        this.$logger.handleError(ex, `Cannot get site settings for siteId ${accountId}`)
      })

      const dealerInfoPromise = this.$store.dispatch('accountManagement/getDealerInfo', { accountId: accountId }).then(res => {
        this.dealerInfo = res.data
      }).catch(ex => {
        this.$logger.handleError(ex, `Cannot get dealer info for accountId ${accountId}`)
      })

      Promise.all([availableContactsPromise, customEmailTemplatesPromise, siteSettingsPromise, dealerInfoPromise]).then(() => { this.isDataLoaded = true })
    },
    async sendEmail () {
      this.isProcessingEmailSending = true
      let templateSettings = this.availableEmailTemplates.find(x => x.id === this.selectedEmailTemplate)
      let contactInfo = this.availableContacts.find(x => x.contactId === this.selectedContact)
      let vehicle = await this.getVehicle()

      const emailBuilder = new LeadsEmailBuilder(vehicle, this.dealerInfo, contactInfo, this.userConversation, templateSettings, this.sendEmailOptions, this.siteUrlWithProtocol)
      let email = await emailBuilder.build()
      email.accountId = this.accountId
      email.conversationId = this.conversationId
      email.dealerContactId = contactInfo ? contactInfo.contactId : 0
      ConversationService.sendEmailFromDealer(this.accountId, this.conversationId, email).then(res => {
        this.$toaster.success('Email Sent Successfully')
      }).catch(ex => {
        this.$toaster.error('Cannot send email. Something went wrong')
        this.$logger.handleError(ex, 'Cannot send email', email)
      }).finally(() => {
        this.isProcessingEmailSending = false
        this.hideModal()
      })
    },
    emailTemplateChange (id) {
      let templateSettings = this.availableEmailTemplates.find(x => x.id === id)
      if (templateSettings) {
        this.sendEmailOptions.subject = templateSettings.subject
        this.sendEmailOptions.emailMessage = templateSettings.messageBody
      }
    },
    async getVehicle () {
      let vehicle = null
      if (this.vin.length > 0) {
        try {
          let res = await InventoryService.getVehicleBaseDetailed(this.accountId, this.vin)
          if (res) {
            vehicle = res.data
          }
        } catch (ex) {
          if (!ex.response || !ex.response.data || !ex.response.data.includes(`Vin ${this.vin.toUpperCase()} does not exist`)) {
            this.$logger.handleError(ex, 'Exception occurred on get vehicle base detail')
          }
        }
      }

      return vehicle
    },
    resetData () {
      this.isVisible = false
      this.isDataLoaded = false
      this.accountId = 0
      this.conversationId = ''
      this.userConversation = null
      this.selectedContact = null
      this.selectedEmailTemplate = null
      this.sendEmailOptions = {subject: '', emailMessage: ''}
      this.availableContacts = []
      this.availableEmailTemplates = []
      this.dealerInfo = null
      this.siteUrlWithProtocol = ''
    }
  }
}
</script>
