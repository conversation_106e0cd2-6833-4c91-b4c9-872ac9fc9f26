import ErrorHandler from './ErrorHandler'
import ErrorHelpers from './ErrorHelpers'

export default {
  install (Vue) {
    let errorHandler = new ErrorHandler()
    Vue.prototype.$logger = errorHandler
    Vue.logger = errorHandler
    Vue.config.errorHandler = function (error, vm, info) {
      errorHandler.handleError(error, info + ' error', ErrorHelpers.getErrorInfoFromVueInstance(vm))
    }
  }
}
