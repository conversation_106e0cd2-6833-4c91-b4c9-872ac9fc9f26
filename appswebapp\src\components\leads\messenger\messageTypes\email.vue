<template>
  <b-card class="overflow-auto">
    <div v-if="model.adfPresentation">
      <div><strong>Email Adf</strong></div>
      <detail-row fixedPayloadWidth>
        <span slot="title">Name:</span>
        <span slot="payload" v-html="model.adfPresentation.userName"></span>
      </detail-row>
      <detail-row fixedPayloadWidth v-if="model.adfPresentation.userEmail">
        <span slot="title">Email:</span>
        <span slot="payload">{{model.adfPresentation.userEmail}}</span>
      </detail-row>
      <detail-row fixedPayloadWidth v-if="model.adfPresentation.userPhone">
        <span slot="title">Phone:</span>
        <span slot="payload">{{model.adfPresentation.userPhone}}</span>
      </detail-row>
      <detail-row fixedPayloadWidth v-if="model.adfPresentation.purchaseTimeframe">
        <span slot="title">Timeframe:</span>
        <span slot="payload">{{model.adfPresentation.purchaseTimeframe}}</span>
      </detail-row>
      <detail-row fixedPayloadWidth v-if="model.adfPresentation.vehicleInformation">
        <span slot="title">Vehicle:</span>
        <span slot="payload">{{model.adfPresentation.vehicleInformation}}</span>
      </detail-row>
      <detail-row fixedPayloadWidth v-if="model.adfPresentation.comments">
        <span slot="title">Comments:</span>
        <b-form-textarea v-html="model.adfPresentation.comments" rows="3" slot="payload" trim no-resize plaintext readonly></b-form-textarea>
      </detail-row>
      <b-btn @click="showModal" size="sm">View XML</b-btn>
      <b-modal
        :visible="isVisibleModal"
        size="lg"
        :title="model.adfPresentation.userEmail"
        @hide="hide"
      >
        {{model.adfPresentation.xmlBody}}
      </b-modal>
    </div>
    <div v-else>
      <div class="mb-2"><strong v-html="model.emailSubject"></strong></div>
      <div v-if='!model.isEmailHtml' v-html="model.emailBody"></div>
      <div v-else class="overflow-auto" v-html="model.emailBody"></div>
    </div>
  </b-card>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'

export default {
  name: 'leads-email-type-message',
  props: {
    model: { type: Object, required: true }
  },
  data () {
    return {
      isVisibleModal: false
    }
  },
  components: {
    'detail-row': detailRow
  },
  methods: {
    showModal () {
      this.isVisibleModal = true
    },
    hide () {
      this.isVisibleModal = false
    }
  }
}
</script>
