<template>
  <common-analytics-table
    :tableItems="tableItems"
    :totalItems="totalItems"
    :tableFields="tableFields"
    :isPaginated="true"
    :sortType.sync="sortTypeProp"
    :pageNumber.sync="pageNumberProp"
    :pageSize.sync="pageSizeProp"
    @accountNameClicked="onAccountNameClicked"
  ></common-analytics-table>
</template>

<script>
import analyticsConstants from '../../../shared/analytics/constants'

export default {
  name: 'group-vehicles-by-account-table',
  props: {
    tableItems: { type: Array, required: true },
    totalItems: { type: Number, require: true },
    pageNumber: { type: Number, required: true },
    pageSize: { type: Number, required: true },
    sortType: { type: Number, required: true }
  },
  components: {
    'common-analytics-table': () => import('./commonAnalyticsTable.vue')
  },
  computed: {
    tableFields () {
      return [
        {
          key: 'account',
          label: 'Account Name',
          sortable: true,
          sortTypeAsc: analyticsConstants.vehicleSortType.accountNameAsc,
          sortTypeDesc: analyticsConstants.vehicleSortType.accountNameDesc
        },
        {
          key: 'pageViews',
          label: 'Page Views',
          sortable: true,
          sortTypeAsc: analyticsConstants.vehicleSortType.pageViewsAsc,
          sortTypeDesc: analyticsConstants.vehicleSortType.pageViewsDesc,
          formatter: val => this.$locale.formatNumber(val)
        },
        {
          key: 'avgTimeOnPage',
          label: 'Avg. Time on Page',
          sortable: true,
          sortTypeAsc: analyticsConstants.vehicleSortType.avgTimeOnPageAsc,
          sortTypeDesc: analyticsConstants.vehicleSortType.avgTimeOnPageDesc,
          formatter: val => this.$locale.getSecondsDurationFormatted(val)
        },
        {
          key: 'timeOnPage',
          label: 'Total Time on Page',
          sortable: true,
          sortTypeAsc: analyticsConstants.vehicleSortType.totalTimeOnPageAsc,
          sortTypeDesc: analyticsConstants.vehicleSortType.totalTimeOnPageDesc,
          formatter: val => this.$locale.getSecondsDurationFormatted(val)
        },
        {
          key: 'formLeads',
          label: 'Form Leads',
          sortable: true,
          sortTypeAsc: analyticsConstants.vehicleSortType.fromLeadsAsc,
          sortTypeDesc: analyticsConstants.vehicleSortType.fromLeadsDesc,
          formatter: val => this.$locale.formatNumber(val)
        }
      ]
    },
    sortTypeProp: {
      get () {
        return this.sortType
      },
      set (newVal) {
        this.$emit('sortTypeChanged', newVal)
      }
    },
    pageNumberProp: {
      get () {
        return this.pageNumber
      },
      set (newVal) {
        this.$emit('pageNumberChanged', newVal)
      }
    },
    pageSizeProp: {
      get () {
        return this.pageSize
      },
      set (newVal) {
        this.$emit('pageSizeChanged', newVal)
      }
    }
  },
  methods: {
    onAccountNameClicked (account) {
      this.$emit('accountNameClicked', account)
    }
  }
}
</script>

<style scoped>

</style>
