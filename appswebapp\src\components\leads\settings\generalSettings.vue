<template>
  <div>
    <div class="mb-3">
      <daily-leads-summary-report v-if="hasPermissionsToManageDealerSettings"
        :isUpdatingProcessed="sections.dailyLeadsSummaryReport.isUpdatingProcessed"
        :isDisabled="sections.dailyLeadsSummaryReport.isDisabled"
        @save="saveDailyLeadsSummaryReportSettings"/>
      <leads-management v-if="hasPermissionsToManageDealerSettings"
        :isUpdatingProcessed="sections.leadsManagement.isUpdatingProcessed"
        :isDisabled="sections.leadsManagement.isDisabled"
        @save="saveLeadsManagementSettings"/>
      <crm-integrations v-if="hasPermissionsToManageAdminSettings"
        :isUpdatingProcessed="sections.crmIntegrations.isUpdatingProcessed"
        :isDisabled="sections.crmIntegrations.isDisabled"
        @save="saveCrmIntegrationsSettings"/>
      <other v-if="hasPermissionsToManageAdminSettings"
        :isUpdatingProcessed="sections.other.isUpdatingProcessed"
        :isDisabled="sections.other.isDisabled"
        @save="saveOtherSettings"/>
      <ebay-bids-notification-send v-if="hasPermissionsToManageAdminSettings"
        :isUpdatingProcessed="sections.ebayBidsNotificationSend.isUpdatingProcessed"
        :isDisabled="sections.ebayBidsNotificationSend.isDisabled"
        @save="saveEbayBidsNotificationSendSettings"/>
    </div>
  </div>
</template>

<script>
import crmIntegrations from './generalSettingsSections/crmIntegrations'
import other from './generalSettingsSections/other'
import ebayBidsNotificationSend from './generalSettingsSections/ebayBidsNotificationSend'
import dailyLeadsSummaryReport from './generalSettingsSections/dailyLeadsSummaryReport'
import leadManagement from './generalSettingsSections/leadManagement'
import permissions from '@/shared/common/permissions'
import { mapGetters } from 'vuex'

export default {
  name: 'leads-general-settings',
  data () {
    return {
      isDisabled: false,
      accountId: +this.$route.params.accountId,
      sections: {
        dailyLeadsSummaryReport: { isDisabled: false, isUpdatingProcessed: false },
        leadsManagement: { isDisabled: false, isUpdatingProcessed: false },
        crmIntegrations: { isDisabled: false, isUpdatingProcessed: false },
        other: { isDisabled: false, isUpdatingProcessed: false },
        ebayBidsNotificationSend: { isDisabled: false, isUpdatingProcessed: false }
      }
    }
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {}
    },
    hasPermissionsToManageAdminSettings () {
      return this.user && this.user.hasPermissions && this.user.hasPermissions(permissions.LeadsManageCommunications)
    },
    hasPermissionsToManageDealerSettings () {
      return this.user && this.user.hasPermissions && this.user.hasPermissions(permissions.LeadsManageDealerSettings)
    }
  },
  components: {
    'crm-integrations': crmIntegrations,
    'other': other,
    'ebay-bids-notification-send': ebayBidsNotificationSend,
    'leads-management': leadManagement,
    'daily-leads-summary-report': dailyLeadsSummaryReport
  },
  methods: {
    saveCrmIntegrationsSettings (settings) {
      this.sections.crmIntegrations.isUpdatingProcessed = true
      this.makeOtherSectionsDisabled('crmIntegrations')
      this.saveAdminSettings(settings)
    },
    saveOtherSettings (settings) {
      this.sections.other.isUpdatingProcessed = true
      this.makeOtherSectionsDisabled('other')
      this.saveAdminSettings(settings)
    },
    saveEbayBidsNotificationSendSettings (settings) {
      this.sections.ebayBidsNotificationSend.isUpdatingProcessed = true
      this.makeOtherSectionsDisabled('ebayBidsNotificationSend')
      this.saveAdminSettings(settings)
    },
    saveAdminSettings (data) {
      this.$store.dispatch('leadsAccountSettings/updateAdminAccountSettings', { accountId: this.accountId, data: data }).then(() => {
        this.$toaster.success('Leads Account Settings Successfully Updated')
      }).catch(ex => {
        if (ex.response) {
          this.$toaster.error(`Cannot update leads account settings. Message ${ex.response.data}`)
        } else {
          this.$toaster.error(`Cannot update leads account settings. Message ${ex.message}`)
        }
        this.$logger.handleError(ex, `Cannot update leads account settings for accountId: ${this.accountId}`)
      }).finally(() => {
        this.setSectionsToDefault()
        this.$emit('refreshSettings')
      })
    },
    saveDailyLeadsSummaryReportSettings (settings) {
      this.sections.dailyLeadsSummaryReport.isUpdatingProcessed = true
      this.makeOtherSectionsDisabled('dailyLeadsSummaryReport')
      this.saveDealerSettings(settings)
    },
    saveLeadsManagementSettings (settings) {
      this.sections.leadsManagement.isUpdatingProcessed = true
      this.makeOtherSectionsDisabled('leadsManagement')
      this.saveDealerSettings(settings)
    },
    saveDealerSettings (data) {
      this.$store.dispatch('leadsAccountSettings/updateDealerAccountSettings', { accountId: this.accountId, data: data }).then(() => {
        this.$toaster.success('Leads Account Settings Successfully Updated')
      }).catch(ex => {
        if (ex.response) {
          this.$toaster.error(`Cannot update leads account settings. Message ${ex.response.data}`)
        } else {
          this.$toaster.error(`Cannot update leads account settings. Message ${ex.message}`)
        }
        this.$logger.handleError(ex, `Cannot update leads account settings for accountId: ${this.accountId}`)
      }).finally(() => {
        this.setSectionsToDefault()
        this.$emit('refreshSettings')
      })
    },
    makeOtherSectionsDisabled (excludeSectionKey) {
      Object.keys(this.sections).filter(key => key !== excludeSectionKey).forEach(key => {
        this.sections[key].isDisabled = true
      })
    },
    setSectionsToDefault () {
      Object.values(this.sections).forEach(section => {
        section.isDisabled = false
        section.isUpdatingProcessed = false
      })
    }
  }
}
</script>
