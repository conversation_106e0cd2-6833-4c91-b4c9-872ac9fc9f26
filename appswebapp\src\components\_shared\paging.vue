<template>
  <b-card-body class="pt-0 pb-3" id="paging">
    <div class="flex-container">
      <div class="d-flex justify-content-between flex-grow full-width-xs">
        <span class="text-muted mt-3" v-if="titled && perPage">Page {{ pageNumber }} of {{ totalPages }}</span>
        <div v-if="pageSizeSelector" class="mt-3">
          Per page:&nbsp;<b-select size="sm" v-model="perPage" :options="[10, 25, 50, 100]" class="d-inline-block w-auto" />
        </div>
      </div>
      <b-pagination class="ml-sm-4 justify-content-center justify-content-sm-end m-0 white-li mt-3"
      v-if="totalItems"
      :value="pageNumber"
      :total-rows="totalItems"
      :per-page="perPage"
      size="sm"
      v-on:change="pageChanged" />
    </div>
  </b-card-body>
</template>

<script>
export default {
  name: 'paging',
  props: {
    pageNumber: Number,
    pageSize: Number,
    totalItems: Number,
    titled: Bo<PERSON>an,
    pageSizeSelector: Boolean
  },
  data () {
    return {

    }
  },
  computed: {
    totalPages () {
      return Math.max(Math.ceil(this.totalItems / this.pageSize), 1)
    },
    perPage: {
      get () {
        return this.pageSize
      },
      set (value) {
        this.changePageSize(value)
      }
    }
  },
  methods: {
    pageChanged (number) {
      this.$emit('numberChanged', number)
    },
    changePageSize (newSize) {
      this.$emit('changePageSize', newSize)
    }
  }
}
</script>

<style lang="scss">
@media (max-width:576px){
  .full-width-xs {
    flex: 1 0 100%;
    flex-wrap: wrap;
  }
}

#paging{
  .flex-container {
    display: flex;
    flex-wrap: wrap;
  }

  .flex-grow {
    flex-grow: 1;
  }
 .white-li {
   li {
     a, span, button {
       background-color: white;
     }
   }
 }
}
</style>
