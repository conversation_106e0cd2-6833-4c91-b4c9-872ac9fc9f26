<template>
  <div class="inline">
    <b-check :disabled="isDisabled" v-model="checkboxValue" @change="onChangeCheckbox">{{statusMessage}}</b-check>
    <b-modal v-model="showModal" title="CONFIRM" lazy @ok="handleOk">
      <p class="confirmation-message">
        {{modalMessage}}
      </p>
    </b-modal>
  </div>
</template>

<script>
import accountLevelFeatureStatuses from '../../../shared/accounts/accountLevelFeatureStatuses'
import accountStatuses from '@/shared/accounts/accountStatuses'

export default {
  name: 'checkbox-with-confirm',
  props: {
    value: {
      type: Number,
      required: true
    },
    accountStatus: {
      type: Number,
      required: true
    },
    canManaged: {
      type: Boolean,
      required: true
    }
  },
  data () {
    return {
      showModal: false,
      checkboxValue: this.getCheckboxValue()
    }
  },
  computed: {
    isDisabled () {
      if (!this.canManaged) {
        return true
      }
      return (this.value !== accountLevelFeatureStatuses.Disabled.Value && this.value !== accountLevelFeatureStatuses.Enabled.Value) ||
        this.accountStatus === accountStatuses.Pending.Value || this.accountStatus === accountStatuses.Closed.Value
    },
    modalMessage () {
      return this.checkboxValue
        ? "Are you sure? It will completely remove Gallery for this account. Please be aware you won't be able to roll it back!"
        : 'Are you sure? It will enable Gallery for this account. You will have to set up all settings to get it work.'
    },
    statusMessage () {
      return this.value === accountLevelFeatureStatuses.ProcessDisabling.Value
        ? 'Disabling Website is in progress'
        : this.value === accountLevelFeatureStatuses.ProcessEnabling.Value
          ? 'Enabling Website is in progress'
          : ''
    }
  },
  methods: {
    getCheckboxValue () {
      return this.value === accountLevelFeatureStatuses.ProcessEnabling.Value || this.value === accountLevelFeatureStatuses.Enabled.Value
    },
    onChangeCheckbox () {
      this.$nextTick(() => {
        this.checkboxValue = this.getCheckboxValue()
      })
      this.showModal = true
    },
    handleOk () {
      const val = this.value === accountLevelFeatureStatuses.Enabled.Value
        ? accountLevelFeatureStatuses.ProcessDisabling.Value
        : this.value === accountLevelFeatureStatuses.Disabled.Value
          ? accountLevelFeatureStatuses.ProcessEnabling.Value
          : 0

      this.$emit('input', val)
      this.showModal = false
    }
  },
  watch: {
    value () {
      this.checkboxValue = this.getCheckboxValue()
    }
  }
}
</script>

<style scoped>
  .inline{
    display: inline-block
  }

  .confirmation-message{
    font-weight: 600;
    margin: 0;
  }
</style>
