<template>
  <div>
    <h4 class="d-flex justify-content-between align-items-center w-100 font-weight-bold pb-0 mb-4">
      Update Legacy Google Ads (In SQL)
    </h4>

    <b-form v-on:submit.prevent="onRebuild" name="rebuild-form">
      <b-row class="mb-2">
        <b-col cols="12" sm="6" md="4" lg="3" xl="2">
        <b-form-group id="account-group" label="AccountId:" description="*AccountId: 0, will rebuild all accounts" >
          <b-form-input
            id="account-input"
            v-model="accountId"
            type="number"
            required
            placeholder="Enter accountId"/>
        </b-form-group>
        </b-col>

        <b-col cols="12" sm="6" md="4" lg="3" xl="2">
         <b-form-group id="date-from-group" label="Date from:" >
            <datepicker
              v-model="dateFrom"
              format="MM/dd/yyyy"
              placeholder="from"
              :bootstrap-styling="true"
              :disabledDates="dateFromDisabledDates"
            />
          </b-form-group>
        </b-col>
        <b-col cols="12" sm="6" md="4" lg="3" xl="2">
          <b-form-group id="date-to-group" label="Date to:" >
            <datepicker
              v-model="dateTo"
              format="MM/dd/yyyy"
              placeholder="from"
              :bootstrap-styling="true"
              :disabledDates="dateToDisabledDates"
            />
          </b-form-group>
        </b-col>
      </b-row>
      <b-button type="submit" :disabled="isRebuildInProgress" variant="primary btn-round">Rebuild</b-button>
    </b-form>
  </div>
</template>

<script>
import Datepicker from 'vuejs-datepicker'
import dateHelper from '@/plugins/locale/date'

const componentDefaults = {
  minDate: new Date(2000, 1, 1)
}

export default {
  name: 'googleAdsRebuild',
  components: {
    'datepicker': Datepicker
  },
  data: () => ({
    isRebuildInProgress: false,
    accountId: '',
    dateFrom: new Date(),
    dateTo: new Date()
  }),
  computed: {
    dateFromDisabledDates () {
      return {
        to: componentDefaults.minDate,
        from: new Date(),
        customPredictor: (date) => {
          return date.getTime() > this.dateTo.getTime()
        }
      }
    },
    dateToDisabledDates () {
      return {
        to: componentDefaults.minDate,
        from: new Date(),
        customPredictor: (date) => {
          return date.getTime() < this.dateFrom.getTime()
        }
      }
    }
  },
  methods: {
    async onRebuild () {
      this.isRebuildInProgress = true

      let requestData = {
        dateFrom: dateHelper.getDayFormatted(this.dateFrom),
        dateTo: dateHelper.getDayFormatted(this.dateTo)
      }

      try {
        if (Number(this.accountId) !== 0) {
          requestData.accountId = Number(this.accountId)
          await this.$store.dispatch(
            'analyticsGa4/doCustomAccountGoogleAdsRebuild', requestData
          )
        } else {
          await this.$store.dispatch(
            'analyticsGa4/doAllAccountsGoogleAdsRebuild', requestData
          )
        }
        this.$toaster.success('Rebuild scheduled')
      } catch (err) {
        this.$toaster.error(
          (err.response || {}).data || "An error occurred. Can't rebuild reports"
        )
        this.$logger.handleError(
          err,
          'Can\'t rebuild Google Ads Report',
          requestData
        )
      }

      this.isRebuildInProgress = false
    }
  }
}
</script>
