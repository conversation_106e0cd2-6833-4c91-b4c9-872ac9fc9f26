import InventoryService from '@/services/inventory/InventoryService'
import Cache<PERSON><PERSON>ider from '@/plugins/cache/CacheProvider'

const cacheProvider = new CacheProvider()

const USER_INVENTORY_DATA_CACHE_KEY = 'user_inventory_data'
const USER_INVENTORY_DATA_CACHE_EXPIRATION_IN_SEC = 30

export default {
  namespaced: true,
  state: {
    inventoryGeneralData: {accessData: {}, alertsData: {}}
  },
  getters: {
    isInventoryEmailAdEnabled: state => state.inventoryGeneralData.accessData.hasAccessToEmailAdPage || false,
    alertsTotal: state => state.inventoryGeneralData.alertsData.totalAlertsCount || 0
  },
  mutations: {
    setData (state, data) {
      state.inventoryGeneralData.accessData = data.accessData || {}
      state.inventoryGeneralData.alertsData = data.alertsData || {}
    }
  },
  actions: {
    async initGeneralData ({ commit }, accountId) {
      let data = cacheProvider.get(USER_INVENTORY_DATA_CACHE_KEY + accountId)
      if (data) {
        commit('setData', data)
      }
      await InventoryService.getUserInventoryGeneralData(accountId).then(res => {
        let data = (res.data || {alertsData: {}, accessData: {}})
        commit('setData', data)
        cacheProvider.add(USER_INVENTORY_DATA_CACHE_KEY + accountId, data, USER_INVENTORY_DATA_CACHE_EXPIRATION_IN_SEC)
      }).catch(ex => {
        console.error(ex)
      })
    }
  }
}
