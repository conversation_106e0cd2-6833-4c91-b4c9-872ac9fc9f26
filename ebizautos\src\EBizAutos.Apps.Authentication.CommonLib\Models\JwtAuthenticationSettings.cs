﻿using System.Text;
using Microsoft.IdentityModel.Tokens;

namespace EBizAutos.Apps.Authentication.CommonLib.Models {
	/// <summary>
	/// Jwt (token based) authentication settings class is using for Admin/CP
	/// </summary>
	public class JwtAuthenticationSettings {
		public const string ConstAuthenticationScheme = "JwtBearer";

		public string JwtIssuer { get; set; }
		public string JwtAudience { get; set; }

		private string _secretKey = "";
		public string SecretKey {
			get {
				return _secretKey;
			}
			set {
				_secretKey = value;

				SymmetricSecurityKey signingKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(_secretKey));
				SigningCredentials = new SigningCredentials(signingKey, SecurityAlgorithms.HmacSha256);
			}
		}

		public string AuthenticationScheme { get; set; }
		public SigningCredentials SigningCredentials { get; private set; }
	}
}