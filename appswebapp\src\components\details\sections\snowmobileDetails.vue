<template>
  <details-section title="Snowmobile Details" v-model="mode" v-if="snowDetails" @cancel="onCancel">
    <div class="view" v-if="mode === 'view'">

      <auto-detail-row title="Engine" :text="vehicle.engine"/>

      <auto-detail-row title="Engine Size (cc)" :text="vehicle.eBayEngineSize" validation-rule="max:50"/>

      <auto-detail-row :title="snowmobileDetails.engineType.name" :text="getSelectedAttributeOption(snowmobileDetails.engineType).key"/>

      <auto-detail-row :title="snowmobileDetails.reverse.name" :text="getSelectedAttributeOption(snowmobileDetails.reverse).key"/>

      <auto-detail-row :title="snowmobileDetails.electricStart.name" :text="getSelectedAttributeOption(snowmobileDetails.electricStart).key"/>

    </div>

    <div class="edit" v-else-if="mode === 'edit'">

      <auto-detail-row title="Engine" v-model="vehicle.engine" validation-rule="max:50|xml"/>

      <auto-detail-row title="Engine Size (cc)" v-model="vehicle.eBayEngineSize" validation-rule="max:50|xml"/>

      <auto-detail-row :title="snowmobileDetails.engineType.name" v-model="snowmobileDetails.engineType.value" :options="getNameValueOptions(snowmobileDetails.engineType.nameValueOptions)" />

      <auto-detail-row :title="snowmobileDetails.reverse.name" v-model="snowmobileDetails.reverse.value" :options="getNameValueOptions(snowmobileDetails.reverse.nameValueOptions)" />

      <auto-detail-row :title="snowmobileDetails.electricStart.name" v-model="snowmobileDetails.electricStart.value" :options="getNameValueOptions(snowmobileDetails.electricStart.nameValueOptions)" />

    </div>
  </details-section>
</template>

<script>
import { mapGetters } from 'vuex'
import detailsSection from '@/components/details/detailsSection'
import autoDetailRow from '../helpers/autoDetailRow'
import featuresHelper from '../../../shared/details/featuresHelper'

export default {
  name: 'truck-miscellaneous',
  data () {
    return {
      mode: 'view'
    }
  },
  computed: {
    ...mapGetters('details', ['vehicle', 'snowDetails']),
    snowmobileDetails () {
      return {
        reverse: this.getFeatureById(-9002),
        electricStart: this.getFeatureById(-9001),
        engineType: this.getFeatureById(-9003)
      }
    }
  },
  methods: {
    getFeatureById (id) {
      return featuresHelper.getFeatureById(this.snowDetails, id)
    },
    getNameValueOptions (nameValueOptions) {
      return featuresHelper.getNameValueOptions(nameValueOptions)
    },
    getSelectedAttributeOption (attribute) {
      return featuresHelper.getSelectedAttributeOption(attribute)
    },
    onCancel () {
      this.$forceUpdate()
    }
  },
  components: {
    'details-section': detailsSection,
    'auto-detail-row': autoDetailRow
  }
}
</script>
