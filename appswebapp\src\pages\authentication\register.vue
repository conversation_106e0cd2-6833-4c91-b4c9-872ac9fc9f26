<template>
  <div class="authentication-wrapper authentication-1 px-4">
    <div class="authentication-inner py-5">

      <!-- Logo -->
      <div class="d-flex justify-content-center align-items-center">
        <span class="app-brand-text demo">eBizAutos</span>
      </div>
      <!-- / Logo -->

      <!-- Form -->
      <form class="my-4">
        <b-form-group label="Your name">
          <b-input v-model="credentials.name" />
        </b-form-group>
        <b-form-group label="Your email">
          <b-input v-model="credentials.email" />
        </b-form-group>
        <b-form-group label="Password">
          <b-input type="password" v-model="credentials.password" />
        </b-form-group>
        <b-btn variant="primary" :block="true" class="mt-4">Sign Up</b-btn>
        <div class="bg-lightest text-muted small p-2 mt-4">
          By clicking "Sign Up", you agree to our
          <a href="javascript:void(0)">terms of service and privacy policy</a>.
          We’ll occasionally send you account related emails.
        </div>
      </form>
      <!-- / Form -->

      <div class="text-center text-muted">
        Already have an account? <router-link :to="{ path: '/account/login' }" class="p-1 my-1">Sign In</router-link>
      </div>

    </div>
  </div>
</template>

<!-- Page -->
<style src="@/vendor/styles/pages/authentication.scss" lang="scss"></style>

<script>
export default {
  name: 'account-register',
  metaInfo: {
    title: 'Register'
  },
  data: () => ({
    credentials: {
      name: '',
      email: '',
      password: ''
    }
  })
}
</script>
