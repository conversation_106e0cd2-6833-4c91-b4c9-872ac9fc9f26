﻿using EBizAutos.Apps.ServiceBus.Events.User;
using System;

namespace EBizAutos.Apps.UsersManagement.Api.ServiceBus.Events {
	public class UserUpdatedEvent : IUserUpdatedEvent {
		public string MessageId { get; set; }
		public string CorrelationId { get; set; }
		public DateTime OccurredOn { get; set; } = DateTime.Now;
		public int ContactId { get; set; }
		public int AccountId { get; set; }
		public string FirstName { get; set; }
		public string LastName { get; set; }
		public string Email { get; set; }

		public UserUpdatedEvent(string correlationId, DateTime occurredOn, int contactId, int accountId, string firstName, string lastName, string email) {
			CorrelationId = correlationId;
			OccurredOn = occurredOn;
			ContactId = contactId;
			AccountId = accountId;
			FirstName = firstName;
			LastName = lastName;
			Email = email;
		}
	}
}