<template>
  <b-modal
    size="lg"
    :title="getTitle"
    :visible="isVisibleModal"
    @hide="onHide"
  >
    <b-input-group class="mb-4">
      <b-form-input v-model="search" placeholder="Name, Account Id"></b-form-input>
      <b-input-group-append>
        <b-btn variant="primary" @click="onSearch">Submit</b-btn>
      </b-input-group-append>
    </b-input-group>
    <div v-if="!isLoading">
      <b-table
        :items="items"
        :fields="getTableFields"
        striped
        bordered
        responsive
      >
        <template #cell(isSSL)="data">
          <i class="ion ion-ios-checkmark text-success zoomeds" v-if="data.item.isSSL"></i>
          <i class="ion ion-ios-close text-danger zoomeds" v-else></i>
        </template>
        <template #cell(isWWW)="data">
          <i class="ion ion-ios-checkmark text-success zoomeds" v-if="data.item.isWWW"></i>
          <i class="ion ion-ios-close text-danger zoomeds" v-else></i>
        </template>
        <template #cell(url)="data">
          <b-link :href="data.item.url" class="text-primary">{{data.item.url}}</b-link>
        </template>
      </b-table>
    </div>
    <div v-else class="m-3 p-3">
      <loader />
    </div>
    <paging
      class="p-0"
      :pageNumber="page"
      :pageSize="pageSize"
      :totalItems="totalCount"
      titled
      @numberChanged="pageChanged"
    />
    <template #modal-footer>
      <b-btn size="sm" @click="onHide">Close</b-btn>
    </template>
  </b-modal>
</template>

<script>
import paging from '@/components/_shared/paging'
import loader from '@/components/_shared/loader'
import detailRow from '@/components/details/helpers/detailRow'

export default {
  props: {
    isVisibleModal: { type: Boolean, required: true },
    siteBox: { type: Object }
  },
  data () {
    return {
      isLoading: true,
      search: '',
      items: [],
      totalCount: 0,
      pageSize: 10,
      page: 1
    }
  },
  components: {
    paging,
    loader,
    detailRow
  },
  computed: {
    getTitle () {
      return this.siteBox.name + ' Sites'
    },
    getTableFields () {
      return [
        {
          key: 'siteId',
          label: 'SiteId:',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'dealershipName',
          label: 'Account Name:',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'isSSL',
          label: 'IsSSL:',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'isWWW',
          label: 'IsWWW:',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'host',
          label: 'Host:',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'url',
          label: 'Assembled URL:',
          tdClass: 'py-2 align-middle'
        }
      ]
    }
  },
  mounted () {
    this.populateData()
  },
  methods: {
    onHide () {
      this.$emit('hide')
    },
    populateData () {
      if (!this.isVisibleModal) {
        return
      }
      this.isLoading = true
      this.$store.dispatch('siteBoxManager/getSiteBoxSiteListing', { siteBoxId: this.siteBox.id, filter: {search: this.search, page: this.page} }).then(res => {
        this.items = res.data.items
        this.totalCount = res.data.totalCount
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
      }).finally(() => {
        this.isLoading = false
      })
    },
    onSearch () {
      this.page = 1
      this.populateData()
    },
    pageChanged (newPage) {
      this.page = newPage
      this.populateData()
    }
  }
}
</script>
