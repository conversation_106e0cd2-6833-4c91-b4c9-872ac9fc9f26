import axios from 'axios'

export default {
  namespaced: true,
  state: {
    siteSettings: null
  },
  mutations: {
    setSiteSettings (state, siteSettings) {
      state.siteSettings = siteSettings
    }
  },
  actions: {
    async getSiteSettings ({ state, commit }, siteId) {
      if (state.siteSettings && state.siteSettings.siteId === siteId) {
        return state.siteSettings
      }

      const result = await axios.get(`/api/sites/${siteId}/basicsettings`)

      commit('setSiteSettings', result.data)

      return result.data
    }
  }
}
