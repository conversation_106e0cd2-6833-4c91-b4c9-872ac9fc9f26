<template>
  <div v-if="!isErrorOccurred">
    <b-row>
      <b-col xl="8" lg="6" md="6" sm="5" xs="5"><h2 class='mt-1'>{{createEdit}} Campaign</h2></b-col>
      <b-col xl="4" lg="6" md="6" sm="7" xs="7" class="custom-btn-position">
        <b-btn variant="primary" class="ml-1 mt-1 mb-2" v-if="isAllowedToManage" @click='onSubmit' :disabled='onSubmitDisabled'>Submit</b-btn>
        <b-btn variant="secondary" class="ml-1 mt-1 mb-2" @click='$router.go(-1)'>Close</b-btn>
        <b-btn v-if='campaignId && isAllowedToManage' variant="dark" class="ml-1 mt-1 mb-2" @click='deleteCampaign'>Delete</b-btn>
      </b-col>
    </b-row>
    <b-card class="p-3" no-body>
      <div>
        <auto-detail-row v-if="isAllowedToManage && !campaignId" title='Name Your Campaign' v-model='selectedModel.campaignName'/>
        <detail-row v-else>
          <span slot="title">Name Your Campaign:</span>
          <span slot="payload">{{selectedModel.campaignName || '-'}}</span>
        </detail-row>
        <detail-row :fixed-payload-width="true">
          <span slot="title">
            Status:
          </span>
          <multiselect
            slot="payload"
            v-model='selectedModel.status'
            placeholder='Select or leave for all body styles'
            :options='editCampaignConstants.campaignStatusOptions.map(x => x.text)'
            :multiple='false'
            :allowEmpty='false'
            :disabled="!isAllowedToManage"
            >
          </multiselect>
        </detail-row>
      </div>

      <div class='mt-5'>
        <b>Vehicle Types</b>
        <hr>

        <detail-row :fixed-payload-width="true">
          <span slot="title">
            Select body styles:
          </span>
          <multiselect
            slot="payload"
            v-model='selectedModel.bodyStylesFilter'
            placeholder='Select or leave for all body styles'
            :options='editCampaignConstants.bodyStyleEnum.map(x => x.text)'
            :multiple='true'
            v-if="isAllowedToManage"
            >
          </multiselect>
          <span slot="payload" v-else>{{(selectedModel.bodyStylesFilter || []).join(', ') || '-'}}</span>
        </detail-row>

        <detail-row :fixed-payload-width="true">
          <span slot="title">
            Select makes:
          </span>
          <multiselect
            slot="payload"
            v-model='selectedModel.makes'
            placeholder='Select or leave for all makes'
            :options='makesOptions'
            :multiple='true'
            v-if="isAllowedToManage"
          >
          </multiselect>
          <span slot="payload" v-else>{{(selectedModel.makes || []).join(', ') || '-'}}</span>
        </detail-row>

        <detail-row :fixed-payload-width="true">
          <span slot="title">
            Select models:
          </span>
          <multiselect
            slot="payload"
            v-model='selectedModel.models'
            placeholder='Select or leave for all models'
            :options='currentModels'
            :disabled='modelsDisabled'
            :multiple='true'
            v-if="isAllowedToManage"
            >
          </multiselect>
          <span slot="payload" v-else>{{(selectedModel.models || []).join(', ') || '-'}}</span>
        </detail-row>

        <detail-row :fixed-payload-width="true">
          <span slot="title">
            Select vehicle types:
          </span>
          <multiselect
            slot="payload"
            v-model='selectedModel.vehicleTypesFilter'
            placeholder='Select or leave for all types'
            :options='editCampaignConstants.vehicleTypeEnum.map(x => x.text)'
            :multiple='true'
            v-if="isAllowedToManage"
            >
          </multiselect>
          <span slot="payload" v-else>{{(selectedModel.vehicleTypesFilter || []).join(', ') || '-'}}</span>
        </detail-row>

        <detail-row :fixed-payload-width="true">
          <span slot="title">
            Craigslist Posting Category:
          </span>
          <multiselect
            slot="payload"
            v-model='selectedModel.ebizAutosVehicleTypes'
            placeholder='Select or leave for all types'
            :options='editCampaignConstants.craigslistCategoryType.map(x => x.text)'
            :multiple='false'
            :allowEmpty='false'
            :disabled="!isAllowedToManage"
            >
          </multiselect>
        </detail-row>
      </div>

      <div class='mt-5'>
        <b>Location</b>
        <hr>

        <detail-row :fixed-payload-width="true">
          <span slot="title">
            Area:
          </span>
          <multiselect
            slot="payload"
            v-model='selectedModel.area'
            @input="onAreaUpdated"
            placeholder='Select or leave for all types'
            :options='areasOptions'
            :multiple='false'
            :allowEmpty='false'
            :disabled="!isAllowedToManage"
            >
          </multiselect>
        </detail-row>
        <detail-row :fixed-payload-width="true">
          <span slot="title">
            Subarea:
          </span>
          <multiselect
            slot="payload"
            v-model='selectedModel.subArea'
            placeholder='Select or leave for all types'
            :options='currentSubAreas'
            :disabled='subAreaDisabled || !isAllowedToManage'
            :multiple='false'
            :allowEmpty='false'
            >
          </multiselect>
        </detail-row>

      </div>

      <div class='mt-5'>
        <b>Schedule</b>
      <hr>

        <div class="container-fluid p-0">
          <b-row class="mt-4" align-v="start" v-for='item in selectedModel.craigslistSchedulerRules' :key='item.dayOfWeek' id="schedule-item">

            <b-col xl="2" lg="2" md="2" sm='12' xs="12">
              <span>{{item.dayOfWeek}}:</span>
            </b-col>
              <b-col xl="5" lg="6" md="5" sm="7" xs="12">
                <div class="d-flex flex-column">
                  <span class="text-muted">Start Time:</span>
                  <div class="scheduled-time-options">
                    <b-form-select class="fixed-time-width" v-model='item.timeFromHour' :disabled="!isAllowedToManage" :options='editCampaignConstants.hoursOptions'>
                    </b-form-select>
                    <b-form-select class="fixed-time-width" v-model='item.timeFromMinutes' :disabled="!isAllowedToManage" :options='editCampaignConstants.minutesOptions'>
                    </b-form-select>
                    <b-form-select class="fixed-time-width" v-model='item.timeFromSuffix' :disabled="!isAllowedToManage" :options='editCampaignConstants.timeSuffixOptions'>
                    </b-form-select>
                  </div>
                  <span class="text-muted mt-2">End Time:</span>
                  <div class="scheduled-time-options">
                    <b-form-select class="fixed-time-width" v-model='item.timeToHour' :disabled="!isAllowedToManage" :options='editCampaignConstants.hoursOptions'>
                    </b-form-select>
                    <b-form-select class="fixed-time-width" v-model='item.timeToMinutes' :disabled="!isAllowedToManage" :options='editCampaignConstants.minutesOptions'>
                    </b-form-select>
                    <b-form-select class="fixed-time-width" v-model='item.timeToSuffix' :disabled="!isAllowedToManage" :options='editCampaignConstants.timeSuffixOptions'>
                    </b-form-select>
                  </div>
                </div>
              </b-col>
              <b-col xl="5" lg="4" md="5" sm='5' xs="12">
                <div class="d-flex flex-column">
                  <span class='text-muted'>Number of vehicles to post:</span>
                  <b-form-input class="fixed-input-width" type="number" min='0' :disabled="!isAllowedToManage"  max='999' v-model="item.postsPerDay"></b-form-input>
                  <span class="text-muted mt-2">In stock longer than this many days:</span>
                  <b-form-input class="fixed-input-width" type="number" min='0' max='999' :disabled="!isAllowedToManage" v-model="item.olderThanDaysInStock"></b-form-input>
                </div>
              </b-col>
          </b-row>
        </div>

      </div>

      <div class='mt-5'>
        <b>Advanced Options</b>
        <hr>

        <detail-row fixedPayloadWidth>
          <span slot="title">
            Stock Search Type:
          </span>
          <multiselect
            slot="payload"
            v-model='selectedModel.stockSearchType'
            placeholder='Select or leave for all types'
            :options='editCampaignConstants.craigsListStockSearchType.map(x => x.text)'
            :multiple='false'
            :allowEmpty='false'
            :disabled="!isAllowedToManage"
            >
          </multiselect>
        </detail-row>
        <auto-detail-row title='Stock Number Character' v-if="isAllowedToManage" v-model='selectedModel.stock'/>
        <detail-row :fixed-payload-width="true" v-else>
          <span slot="title">Stock Number Character:</span>
          <span slot="payload">{{selectedModel.stock || '-'}}</span>
        </detail-row>
        <auto-detail-row title='Specific Location' v-if="isAllowedToManage" v-model='selectedModel.specificLocation'/>
        <detail-row :fixed-payload-width="true" v-else>
          <span slot="title">Specific Location:</span>
          <span slot="payload">{{selectedModel.specificLocation || '-'}}</span>
        </detail-row>
        <auto-detail-row title='Ignore vehicles where price ends with' v-if="isAllowedToManage" v-model='selectedModel.endOfPrice'/>
        <detail-row :fixed-payload-width="true" v-else>
          <span slot="title">Ignore vehicles where price ends with:</span>
          <span slot="payload">{{selectedModel.endOfPrice || '-'}}</span>
        </detail-row>
        <detail-row fixedPayloadWidth>
          <span slot="title">Post Vehicles with Price From:</span>
          <price-input slot="payload" active v-model='selectedModel.priceFrom' :disabled="!isAllowedToManage" placeholder=""></price-input>
        </detail-row>
        <detail-row fixedPayloadWidth>
          <span slot="title">Post Vehicles with Price To:</span>
          <price-input slot="payload" active v-model='selectedModel.priceTo' :disabled="!isAllowedToManage" placeholder=""></price-input>
        </detail-row>
        <detail-row fixedPayloadWidth>
          <span slot="title">
            Post Strategy:
          </span>>
          <multiselect
            slot="payload"
            v-model='selectedModel.postStrategy'
            placeholder='Select or leave for all types'
            :options='editCampaignConstants.craigslistPostStrategyEnum.map(x => x.text)'
            :multiple='false'
            :disabled="!isAllowedToManage"
            :allowEmpty='false'
            >
          </multiselect>>
        </detail-row>
        <detail-row fixedPayloadWidth>
          <span slot="title">
            Photos count:
          </span>
          <b-form-input slot="payload" type='number' max='100' min='1' :disabled="!isAllowedToManage" v-model='selectedModel.minRequiredPhotoCount'></b-form-input>
        </detail-row>
      </div>

    </b-card>

  </div>
  <div v-else class="text-muted text-center">
    <span>Something went wrong! <br/>
      Please try again later or contact our Support at <a href="tel:************" class="text-primary"><u>************</u></a> or
      <a href="mailto:<EMAIL>" class="text-primary"><u><EMAIL></u></a>
    </span>
  </div>
</template>

<script>
import {mapGetters} from 'vuex'

import detailRow from '@/components/details/helpers/detailRow'
import autoDetailRow from '@/components/details/helpers/autoDetailRow'
import priceInput from '@/components/_shared/priceInput'

import Multiselect from 'vue-multiselect'

import dateModule from '@/plugins/locale/date'
import editCampaignConstants from '@/shared/craigslist/editCampaignConstants'
import CampaignDataBuilder from '@/shared/craigslist/campaignDataBuilder'

const campaignDataBuilder = new CampaignDataBuilder()

export default {
  name: 'create-edit-campaign',
  props: {
    accountId: {
      type: Number,
      required: true
    },
    campaignId: {
      type: String,
      required: false
    }
  },
  metaInfo: {
    title: 'Campaign'
  },
  components: {
    'auto-detail-row': autoDetailRow,
    'detail-row': detailRow,
    'multiselect': Multiselect,
    'price-input': priceInput
  },
  created () {
    this.getCampaignsCategories()
  },
  data () {
    return {
      selectedModel: campaignDataBuilder.getDefaultSelectedData(),
      postOrPutModel: null,
      isErrorOccurred: false,
      dateMonday: '01',
      areas: [],
      areasOptions: [],
      makes: [],
      makesOptions: [],
      editCampaignConstants: editCampaignConstants,
      onSubmitDisabled: false
    }
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {}
    },
    isAllowedToManage () {
      return this.user.isEbizAdmin
    },
    currentModels () {
      if (this.makes && this.selectedModel.makes && this.selectedModel.makes.length === 1 && this.makes.find(x => x.make === this.selectedModel.makes[0])) {
        return this.makes.find(x => x.make === this.selectedModel.makes[0]).models
      }
      return []
    },
    currentSubAreas () {
      if (this.areas.length > 0 && this.selectedModel.area) {
        let area = this.areas.find(x => x.areaDescription === this.selectedModel.area)
        if (area && area.subAreas) {
          return area.subAreas.map(x => x.value)
        }
      }
      return []
    },
    modelsDisabled () {
      return !(this.selectedModel.makes.length === 1)
    },
    subAreaDisabled () {
      return !this.areas.length || !this.selectedModel.area || !this.areas.find(x => x.areaDescription === this.selectedModel.area).subAreas.length
    },
    createEdit () {
      if (this.campaignId) {
        return 'Edit'
      }
      return 'Create'
    },
    timeDict () {
      let dict = []
      for (let i = 0; i < 24; i++) {
        let timeValue = (i % 12 === 0 ? 12 : i % 12) + (i / 12 < 1 ? ' AM' : ' PM')
        dict.push(timeValue)
      }
      return dict
    },
    hourTime (date) {
      return dateModule.formatAHours(date)
    }
  },
  methods: {
    onAreaUpdated (area) {
      this.selectedModel.subArea = this.currentSubAreas.length > 0 ? this.currentSubAreas[0] : ''
    },
    fetchCampaign () {
      this.$store.dispatch('craigslist/fetchCampaign', {accountId: this.accountId, campaignId: this.campaignId}).then(x => {
        this.buildSelectedFields(x.data.model)
      }).catch(ex => {
        this.isErrorOccurred = true
        this.$toaster.error('Something went wrong!')
        this.$logger.handleError(ex, 'Exception occurred on fetching campaign')
      })
    },
    getCampaignsCategories () {
      this.$store.dispatch('craigslist/getCampaignsCategories', {accountId: this.accountId}).then(x => {
        this.areas = x.data.areas || []
        this.makes = x.data.makes || []
        this.makesOptions = this.makes.map(x => x.make)
        this.areasOptions = this.areas.map(x => x.areaDescription)
        this.initDefaultValues()
        if (this.campaignId) {
          this.fetchCampaign()
        }
      }).catch(ex => {
        this.isErrorOccurred = true
        this.$toaster.error('Something went wrong!')
        this.$logger.handleError(ex, 'Exception occurred on get campaigns categories')
      })
    },
    onSubmit () {
      if (this.validate()) {
        this.prepareDataForPost()
        if (this.campaignId && this.postOrPutModel) {
          this.$store.dispatch('craigslist/updateCampaign', {accountId: this.accountId, campaignId: this.campaignId, item: this.postOrPutModel}).then(x => {
            this.$toaster.success('Campaign Successfully Updated')
            this.$router.push({name: 'craigslist-dashboard-for-account', params: {accountId: this.accountId}})
          })
            .catch(err => {
              this.$toaster.exception(err, `Failed on updating`, {timeout: 8000})
              this.$logger.handleError(err, `Can't update craigslist campaign for accountId:${this.accountId} and campaignId:${this.campaignId}`)
            })
            .finally(() => {
              this.onSubmitDisabled = false
            })
        } else if (this.postOrPutModel) {
          this.$store.dispatch('craigslist/createCampaign', {accountId: this.accountId, item: this.postOrPutModel}).then(x => {
            this.$toaster.success('Campaign Successfully Created')
            this.$router.push({name: 'craigslist-dashboard-for-account', params: {accountId: this.accountId}})
          })
            .catch(err => {
              this.$toaster.exception(err, `Failed on creating`, {timeout: 8000})
              this.$logger.handleError(err, `Can't create craigslist campaign for accountId:${this.accountId}`)
            })
            .finally(() => {
              this.onSubmitDisabled = false
            })
        }
      }
    },
    deleteCampaign () {
      this.onSubmitDisabled = true
      this.$store.dispatch('craigslist/deleteCampaign', { accountId: this.accountId, id: this.campaignId }).then(x => {
        this.$toaster.success('Campaign Successfully Deleted')
        this.$router.push({name: 'craigslist-dashboard-for-account', params: {accountId: this.accountId}})
      })
        .catch(err => {
          this.$toaster.exception(err, `Failed on deleting`, {timeout: 8000})
          this.$logger.handleError(err, `Can't delete craigslist campaign for accountId:${this.accountId}`)
        })
        .finally(() => {
          this.onSubmitDisabled = false
        })
    },
    initDefaultValues () {
      this.selectedModel.area = this.areasOptions[0]
      this.selectedModel.subArea = this.currentSubAreas.length > 0 ? this.currentSubAreas[0] : ''
      this.selectedModel.ebizAutosVehicleTypes = editCampaignConstants.craigslistCategoryType[0].text
      this.selectedModel.stockSearchType = editCampaignConstants.craigsListStockSearchType[0].text
      this.selectedModel.postStrategy = editCampaignConstants.craigslistPostStrategyEnum[0].text
    },
    prepareDataForPost () {
      this.onSubmitDisabled = true
      this.selectedModel.accountId = this.accountId
      this.postOrPutModel = campaignDataBuilder.buildDataForPostOrPut(this.selectedModel)
      if (this.selectedModel.area) {
        let area = this.areas.find(x => x.areaDescription === this.selectedModel.area)
        if (area) {
          this.postOrPutModel.area = area.area
          if (area.subAreas && this.selectedModel.subArea) {
            let subArea = area.subAreas.find(x => x.value === this.selectedModel.subArea)
            if (subArea) {
              this.postOrPutModel.subArea = subArea.key
            }
          }
        }
      }
    },
    buildSelectedFields (model) {
      this.selectedModel = campaignDataBuilder.buildSelectedData(model)
      if (model.area) {
        let currentArea = this.areas.find(x => x.area === model.area)
        if (currentArea) {
          this.selectedModel.area = currentArea.areaDescription
        }
        if (model.subArea) {
          let area = this.areas.find(x => x.area === model.area)
          if (area && area.subAreas) {
            this.selectedModel.subArea = (area.subAreas.find(x => x.key === model.subArea) || {}).value
          }
        }
      }
    },
    validate () {
      if (this.selectedModel.campaignName.trim().length === 0) {
        this.$toaster.error('Campaign Name cannot be empty', {timeout: 5000})
        this.onSubmitDisabled = false
        return false
      }
      if (this.selectedModel.specificLocation && this.selectedModel.specificLocation.trim().length > 55) {
        this.$toaster.error('The Specific Location field may not be greater than 55 characters', {timeout: 5000})
        this.onSubmitDisabled = false
        return false
      }
      for (var index in this.selectedModel.craigslistSchedulerRules) {
        if (this.selectedModel.craigslistSchedulerRules[index].postsPerDay > 0) {
          return true
        }
      }
      this.$toaster.error('At least one post should be scheduled')
      this.onSubmitDisabled = false
      return false
    }
  }
}
</script>

<style scoped>

#schedule-item{
  display: flex;
  align-items: center;
}

.fixed-time-width {
  width: 100px;
}

.fixed-input-width {
  width: 200px;
}

.row {
  margin: 0;
}

input[type=number] {
  padding-right: 2px;
}

.scheduled-time-options {
  display: flex;
  flex-direction: row;
  flex-shrink: 1;
}

.scheduled-time-options .fixed-time-width:nth-child(2), .fixed-time-width:nth-child(3) {
  margin-left: 1rem;
}

@media (min-width: 1200px) {
  .fixed-input-width {
    width: 250px;
  }
}

@media (max-width:576px) {
  .fixed-time-width {
    width: 100%;
  }
  .fixed-input-width {
    width: 100%;
  }
}

@media (max-width: 348px) {
  .scheduled-time-options {
    display: flex;
    flex-direction: column;
    flex-shrink: 1;
  }
    .scheduled-time-options .fixed-time-width:nth-child(n) {
    margin-left: 0;
    margin-top: 0.5rem;
  }
}

@media (min-width: 576px) {
  .custom-btn-position {
    display: flex;
    justify-content: flex-end
  }
}

@media (max-width: 308px) {
  .custom-btn-position {
    margin: 0 !important;
    display: flex;
    flex-direction: column;
    flex-shrink: 1;
  }
}
</style>
