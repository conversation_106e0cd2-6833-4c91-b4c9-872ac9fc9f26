import editCampaignConstants from './editCampaignConstants'

class CampaignHelper {
  getStatusValueByDescription (description) {
    if (description && description !== '') {
      return editCampaignConstants.campaignStatusOptions.find(x => x.text === description).value
    }
    return 0
  }

  getEbizAutosVehicleTypesValueByDescription (description) {
    if (description && description !== '') {
      return editCampaignConstants.craigslistCategoryType.find(x => x.text === description).value
    }
    return 0
  }

  getStockSerachTypeValueByDescription (description) {
    if (description && description !== '') {
      return editCampaignConstants.craigsListStockSearchType.find(x => x.text === description).value
    }
    return 0
  }

  getPostStrategyValueByDescription (description) {
    if (description && description !== '') {
      return editCampaignConstants.craigslistPostStrategyEnum.find(x => x.text === description).value
    }
    return 0
  }

  getBodyStyleFiltersByDescriptions (descriptions) {
    const results = []
    if (descriptions && descriptions.length > 0) {
      descriptions.map(x => {
        results.push(editCampaignConstants.bodyStyleEnum.find(bs => bs.text === x).value)
      })
    }
    return results
  }

  getVehicleTypeFiltersByDescriptions (descriptions) {
    const results = []
    if (descriptions && descriptions.length > 0) {
      descriptions.map(x => {
        results.push(editCampaignConstants.vehicleTypeEnum.find(vt => vt.text === x).value)
      })
    }
    return results
  }

  getStatusDescriptionByValue (value) {
    return editCampaignConstants.campaignStatusOptions.find(x => x.value === value).text
  }

  getEbizAutosVehicleTypesDescriptionByValue (value) {
    if (value && value !== 0) {
      return editCampaignConstants.craigslistCategoryType.find(x => x.value === value).text
    }
    return ''
  }

  getStockSerachTypeDescriptionByValue (value) {
    return editCampaignConstants.craigsListStockSearchType.find(x => x.value === value).text
  }

  getPostStrategyDescriptionByValue (value) {
    return editCampaignConstants.craigslistPostStrategyEnum.find(x => x.value === value).text
  }

  getBodyStyleFiltersByValues (values) {
    const results = []
    if (values && values.length > 0) {
      values.map(x => {
        results.push(editCampaignConstants.bodyStyleEnum.find(bs => bs.value === x).text)
      })
    }
    return results
  }

  getVehicleTypeFiltersByValues (values) {
    const results = []
    if (values && values.length > 0) {
      values.map(x => {
        results.push(editCampaignConstants.vehicleTypeEnum.find(vt => vt.value === x).text)
      })
    }
    return results
  }
}

export default CampaignHelper
