<template>
  <b-card-body class="pt-0 pb-3">
    <div class="flex-container">
      <div class="d-flex justify-content-between flex-grow full-width-xs">
        <span class="text-muted mt-3" v-if="hasLabel && totalItems > 0" >Total: {{ totalItems }}</span>
        <span v-else></span>

        <div v-if="hasPageSizeSelector" class="mt-3">
          Per page:&nbsp;
          <b-select
            size="sm"
            v-model="size"
            @change="onPageSizeChanged"
            :options="[10, 25, 50, 100]"
            class="d-inline-block w-auto"
          />
        </div>
      </div>

      <div class="flex-container ml-sm-4 mt-3">
        <b-button :disabled="!canMoveBackward" variant="light" size="sm" @click="onPageChanged(fluentShiftTypes.first)">&laquo;</b-button>
        <b-button :disabled="!canMoveBackward" variant="light" size="sm" @click="onPageChanged(fluentShiftTypes.previous)">&lsaquo;</b-button>
        <b-button :disabled="!canMoveForward" variant="light" size="sm" @click="onPageChanged(fluentShiftTypes.next)">&rsaquo;</b-button>
        <b-button :disabled="!canMoveForward" variant="light" size="sm" @click="onPageChanged(fluentShiftTypes.last)">&raquo;</b-button>
      </div>
    </div>
  </b-card-body>
</template>

<script>
import fluentShiftTypes from './fluentShiftTypes'

export default {
  name: 'fluent-pagination',
  props: {
    canMoveForward: {
      type: Boolean,
      default: false
    },
    canMoveBackward: {
      type: Boolean,
      default: false
    },
    hasLabel: {
      type: Boolean,
      default: true
    },
    totalItems: Number,
    hasPageSizeSelector: {
      type: Boolean,
      default: true
    },
    pageSize: Number
  },
  data () {
    return {
      size: this.pageSize,
      fluentShiftTypes: fluentShiftTypes
    }
  },
  methods: {
    onPageChanged (shift) {
      this.$emit('pageChanged', shift)
    },
    onPageSizeChanged (newSize) {
      this.$emit('pageSizeChanged', newSize)
    }
  },
  watch: {
    pageSize (newVal, oldVal) {
      this.size = newVal
    }
  }
}
</script>

<style lang="scss" scoped>
@media (max-width: 576px) {
  .full-width-xs {
    flex: 1 0 100%;
    flex-wrap: wrap;
  }
}

.btn {
  margin-left: 3px;
}

.flex-container {
  display: flex;
  flex-wrap: wrap;
}

.flex-grow {
  flex-grow: 1;
}
.white-li {
  li {
    a, span {
      background-color: white;
    }
  }
}
</style>
