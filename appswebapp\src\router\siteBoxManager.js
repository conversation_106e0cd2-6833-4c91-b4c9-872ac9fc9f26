import permissions from '../shared/common/permissions'
import applicationTypes from '../shared/common/applicationTypes'

let siteBoxManagerDefaultMeta = {
  applicationType: applicationTypes.SiteBoxManager.Id,
  permissions: [permissions.SiteBoxManagerChangeSiteHosting],
  applicationFullAccess: permissions.SiteBoxManagerFullAccess
}

export default [{
  path: '/siteboxmanager',
  meta: {
    ...siteBoxManagerDefaultMeta
  },
  component: () => import('@/layout/Layout2'),
  redirect: {
    name: 'site-hostings'
  },
  children: [{
    path: 'sitehostings',
    name: 'site-hostings',
    meta: {
      ...siteBoxManagerDefaultMeta,
      permissions: permissions.SiteBoxManagerChangeSiteHosting
    },
    component: () => import('@/pages/siteBoxManager/siteHostings')
  }, {
    path: 'siteboxes',
    name: 'siteboxes',
    meta: {
      ...siteBoxManagerDefaultMeta,
      permissions: permissions.SiteBoxManagerFullAccess
    },
    component: () => import('@/pages/siteBoxManager/siteBoxes')
  }, {
    path: 'databases',
    name: 'databases',
    meta: {
      ...siteBoxManagerDefaultMeta,
      permissions: permissions.SiteBoxManagerFullAccess
    },
    component: () => import('@/pages/siteBoxManager/databases')
  }, {
    path: 'useractivity',
    name: 'sitebox-manager-user-activity',
    meta: {
      ...siteBoxManagerDefaultMeta,
      permissions: permissions.SiteBoxManagerFullAccess
    },
    component: () => import('@/pages/siteBoxManager/userActivity')
  }, {
    path: 'useractivity/:logId/details',
    name: 'sitebox-manager-useractivity-details',
    meta: {
      ...siteBoxManagerDefaultMeta,
      permissions: permissions.SiteBoxManagerFullAccess
    },
    props: (route) => ({logId: route.params.logId}),
    component: () => import('@/pages/siteBoxManager/userActivityDetails')
  }, {
    path: 'fetcher',
    name: 'sitebox-manager-fetcher',
    meta: {
      ...siteBoxManagerDefaultMeta,
      permissions: permissions.SiteBoxManagerFullAccess
    },
    component: () => import('@/pages/siteBoxManager/fetcher')
  }, {
    path: 'synchronizationmonitor',
    name: 'sitebox-synchronization-monitor',
    meta: {
      ...siteBoxManagerDefaultMeta,
      permissions: permissions.SiteBoxManagerFullAccess
    },
    component: () => import('@/pages/siteBoxManager/synchronizationMonitor')
  }]
}]
