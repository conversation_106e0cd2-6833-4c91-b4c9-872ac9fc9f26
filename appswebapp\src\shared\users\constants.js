const permissions = {
  DefaultAccess: 'DefaultAccess',
  ManageUserAccount: 'ManageUserAccount',
  ManageCraigslist: 'ManageCraigslist',
  RepostOnCraigslist: 'RepostOnCraigslist',
  ViewLogs: 'ViewLogs',
  CraigslistFullAccess: 'CraigslistFullAccess',
  UsersFullAccess: 'UsersFullAccess',
  LeadsView: 'LeadsView',
  LeadsManageCommunications: 'LeadsManageCommunications',
  LeadsSendMessages: 'LeadsSendMessages',
  LeadsLogView: 'LeadsLogView',
  LeadsManageDealerSettings: 'LeadsManageDealerSettings',
  LeadsFullAccess: 'LeadsFullAccess',
  ManageLegacyLeads: 'ManageLegacyLeads',
  ManageGoogleAnalytics: 'ManageGoogleAnalytics',
  GoogleAnalyticsFullAccess: 'GoogleAnalyticsFullAccess',
  GoogleAnalytics4View: 'GoogleAnalytics4View',
  SiteBoxManagerChangeSiteHosting: 'SiteBoxmanagerChangeSiteHosting',
  SiteBoxManagerFullAccess: 'SiteBoxManagerFullAccess',
  ViewErrors: 'ViewErrors',
  ViewEncryptDecrypt: 'ViewEncryptDecrypt',
  IMManageVehicles: 'IMManageVehicles',
  IMFullAccess: 'IMFullAccess',
  AMViewSettings: 'AMViewSettings',
  AMManageSettings: 'AMManageSettings',
  AMFullAccess: 'AMFullAccess',
  SMViewSettings: 'SMViewSettings',
  SMManageSettings: 'SMManageSettings',
  SMFullAccess: 'SMFullAccess',
  ManageMultipleUserAccounts: 'ManageMultipleUserAccounts',
  EBayViewSettings: 'EBayViewSettings',
  EBayManageSettings: 'EBayManageSettings',
  EBayFullAccess: 'EBayFullAccess',
  EbizAutosAdmin: 'EbizAutosAdmin',
  FullAccess: 'FullAccess'
}

const userTypes = Object.freeze({
  cpUser: {value: 1, text: 'Dealer'},
  adminUser: {value: 2, text: 'eBizAutos User'},
  ebizServiceUser: {value: 3, text: 'eBizAutos Services'}
})

const appTypes = Object.freeze({
  craigslist: {value: 2, text: 'Craigslist'},
  inventory: {value: 3, text: 'Inventory'},
  users: {value: 4, text: 'User Panel'},
  leads: {value: 5, text: 'Leads'},
  analytics: {value: 7, text: 'Analytics'},
  siteBoxManager: {value: 9, text: 'SiteBox Manager'},
  accountManagement: {value: 12, text: 'Account Management'},
  siteManagement: {value: 13, text: 'Site Management'},
  ebay: {value: 14, text: 'eBay'}
})

const roleTypes = Object.freeze({
  admin: {value: 1, text: 'Admin'},
  eBizDev: {value: 5, text: 'eBizDev'},
  eBizUserStandard: {value: 10, text: 'eBizUserStandard'},
  eBizReps: {value: 20, text: 'eBizReps'},
  it: {value: 30, text: 'It'},
  eBizService: {value: 40, text: 'eBizService'},
  cpUser: {value: 100, text: 'CPUser'}
})

const syncUserStatus = Object.freeze({
  notFound: 0,
  inProgress: 1,
  completed: 3,
  failed: 4
})

const permissionsBySection = {
  AccountManagement: {
    title: 'Account Management',
    permissions: [
      { value: permissions.AMViewSettings, title: 'View Account Management Settings' },
      { value: permissions.AMManageSettings, title: 'Manage Account Management Settings' },
      { value: permissions.AMFullAccess, title: 'Account Management Full Access' }
    ]
  },
  InventoryManagement: {
    title: 'Inventory Management',
    permissions: [
      { value: permissions.IMManageVehicles, title: 'Manage Vehicles' },
      { value: permissions.IMFullAccess, title: 'Inventory Management Full Access' }
    ]
  },
  Craigslist: {
    title: 'Craigslist',
    permissions: [
      { value: permissions.ManageCraigslist, title: 'Manage Craigslist' },
      { value: permissions.CraigslistFullAccess, title: 'Craigslist Full Access' },
      { value: permissions.RepostOnCraigslist, title: 'Repost on Craigslist' }
    ]
  },
  eBay: {
    title: 'eBay',
    permissions: [
      {value: permissions.EBayViewSettings, title: 'View eBay Settings'},
      {value: permissions.EBayManageSettings, title: 'Manage eBay Settings'},
      {value: permissions.EBayFullAccess, title: 'eBay Full Access'}
    ]
  },
  Analytics: {
    title: 'Analytics',
    permissions: [
      {value: permissions.ManageGoogleAnalytics, title: 'Manage Google Analytics'},
      {value: permissions.GoogleAnalytics4View, title: 'Google Analytics GA4 View'},
      {value: permissions.GoogleAnalyticsFullAccess, title: 'Analytics Full Access'}
    ]
  },
  Leads: {
    title: 'Leads',
    permissions: [
      {value: permissions.LeadsLogView, title: 'Leads Log View'},
      {value: permissions.LeadsManageCommunications, title: 'Leads Manage Communications'},
      {value: permissions.LeadsManageDealerSettings, title: 'Leads Manage Dealer Settings'},
      {value: permissions.LeadsSendMessages, title: 'Leads Send Messages'},
      {value: permissions.LeadsView, title: 'Leads View'},
      {value: permissions.LeadsFullAccess, title: 'Leads Full Access'},
      {value: permissions.ManageLegacyLeads, title: 'Manage Legacy Leads'}
    ]
  },
  UserPanel: {
    title: 'User Panel',
    permissions: [
      {value: permissions.ManageUserAccount, title: 'Manage User Account'},
      {value: permissions.UsersFullAccess, title: 'Users Full Access'}
    ]
  },
  SiteBoxManager: {
    title: 'SiteBox Manager',
    permissions: [
      {value: permissions.SiteBoxManagerChangeSiteHosting, title: 'SiteBox Manager Change Site Hosting'},
      {value: permissions.SiteBoxManagerFullAccess, title: 'SiteBox Manager Full Access'}
    ]
  },
  SitesManagement: {
    title: 'Sites Management',
    permissions: [
      {value: permissions.SMViewSettings, title: 'Sites Management View Settings'},
      {value: permissions.SMManageSettings, title: 'Sites Management Manage Settings'},
      {value: permissions.SMFullAccess, title: 'Sites Management Full Access'}
    ]
  },
  Others: {
    title: 'Others',
    permissions: [
      {value: permissions.FullAccess, title: 'Full Access(dev)'},
      {value: permissions.ViewLogs, title: 'View Logs'},
      {value: permissions.DefaultAccess, title: 'Default Access'},
      {value: permissions.EbizAutosAdmin, title: 'eBizAutos Admin'},
      {value: permissions.ViewEncryptDecrypt, title: 'View Encrypt/Decrypt'},
      {value: permissions.ViewErrors, title: 'View Errors'}
    ]
  }
}

const userActivitySortTypes = Object.freeze({
  userNameAsc: 1,
  userNameDesc: 2,
  userActionTypeAsc: 3,
  userActionTypeDesc: 4,
  dateAsc: 5,
  dateDesc: 6
})

const userActivityActionTypes = [
  {value: 0, text: 'All Actions'},
  {value: 1, text: 'Create User'},
  {value: 2, text: 'Update User'},
  {value: 3, text: 'Delete User'},
  {value: 4, text: 'Change Account Users Status'},
  {value: 5, text: 'Delete Account Users'},
  {value: 6, text: 'Synchronize Users'},
  {value: 7, text: 'Create User Role'},
  {value: 8, text: 'Update User Role'},
  {value: 9, text: 'Delete User Role'}
]

export {
  userTypes,
  appTypes,
  roleTypes,
  syncUserStatus,
  permissionsBySection,
  userActivitySortTypes,
  userActivityActionTypes
}
