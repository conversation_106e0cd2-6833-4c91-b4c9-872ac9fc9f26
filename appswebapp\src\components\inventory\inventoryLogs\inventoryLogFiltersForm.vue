<template>
  <b-form v-if="logType !== logTypes.vehicleHistoryReport.value" v-on:submit.prevent="onFilterApply" class='m-4'>
    <div class="inventory-logs-filter-form">
      <div class="inventory-logs-filter-form-input">
        <b-form-input v-if='logType === logTypes.mobile.value || logType === logTypes.desktop.value' v-model="filter.vin" placeholder="Vin" autocomplete="off"></b-form-input>
        <b-form-input v-else v-model="filter.search" placeholder="Search..." autocomplete="off"></b-form-input>
      </div>

      <div class="inventory-logs-filter-form-input">
        <b-input-group class="flex-nowrap">
          <b-input-group-prepend is-text>
            <i class="ion ion-md-calendar" slot="prepend"></i>
          </b-input-group-prepend>
          <date-time-picker
            ref="timeFrom"
            v-model="filter.dateFrom"
            :options="filterTimeOptions"
            format="MM/DD/YYYY HH:mm"
            placeholder="Date From"
            className="form-control"
            @change="onTimeFromInputChange"
          />
          <b-input-group-append
            is-text
            v-show="filter.dateFrom"
            @click="filter.dateFrom = null"
          >
            <i class="ion ion-md-close"></i>
          </b-input-group-append>
        </b-input-group>
      </div>

      <div class="inventory-logs-filter-form-input">
        <b-input-group class="flex-nowrap">
          <b-input-group-prepend is-text>
            <i class="ion ion-md-calendar" slot="prepend"></i>
          </b-input-group-prepend>
          <date-time-picker
            ref="timeTo"
            v-model="filter.dateTo"
            :options="filterTimeOptions"
            format="MM/DD/YYYY HH:mm"
            placeholder="Date To"
            className="form-control"
            @change="onTimeToInputChange"
          />
          <b-input-group-append
            is-text
            v-show="filter.dateTo"
            @click="filter.dateTo = null"
          >
            <i class="ion ion-md-close"></i>
          </b-input-group-append>
        </b-input-group>
      </div>
      <div v-if="logType === logTypes.vehiclePostProcessing.value" class="inventory-logs-filter-form-input">
        <b-form-select v-model="filter.task_type" :value="filter.task_type" text-field="description" :options="getVehiclePostProcessingTaskTypes"></b-form-select>
      </div>
      <div class="inventory-logs-filter-form-btn">
        <b-btn variant="primary" class="mr-2 mb-2 inventory-log-btn" type="submit">Apply</b-btn>
        <b-btn variant="secondary" type="reset" class="mr-2 mb-2 inventory-log-btn" @click="onFilterClear">Clear</b-btn>
      </div>
    </div>
  </b-form>
  <vehicle-history-reports-logs-filter-form v-else :filter="filter" @applyFilter="onFilterApply" />
</template>

<script>
import { logTypes } from '@/shared/inventory/inventoryTypes'
import InventoryService from '../../../services/inventory/InventoryService'
import logsFilterForm from './vehicleHistoryReports/historyReportsLogsFilterForm.vue'

export default {
  props: {
    logType: { type: Number, required: true },
    filter: { type: Object, required: true }
  },
  data () {
    return {
      logTypes,
      totalVehicles: 0,
      accountId: +this.$route.params.accountId,
      filterTimeOptions: {
        autoUpdateInput: false,
        startDate: new Date(),
        singleDatePicker: true,
        timePicker: true,
        timePicker24Hour: true,
        maxDate: new Date()
      },
      vehiclePostProcessingTaskTypes: []
    }
  },
  created () {
    this.populateVehiclePostProcessingTaskTypes()
  },
  components: {
    'date-time-picker': () => import('@gravitano/vue-date-range-picker/src/components/DateRangePicker'),
    'vehicle-history-reports-logs-filter-form': logsFilterForm
  },
  computed: {
    refDateTimeFrom () {
      return (this.$refs.timeFrom || {}).$el || {}
    },
    refDateTimeTo () {
      return (this.$refs.timeTo || {}).$el || {}
    },
    getVehiclePostProcessingTaskTypes () {
      let res = [{value: null, description: 'All'}].concat(this.vehiclePostProcessingTaskTypes || [])
      return res
    }
  },
  methods: {
    onFilterClear () {
      if (this.logType === logTypes.mobile.value || this.logType === logTypes.desktop.value) {
        this.filter.vin = null
      } else {
        this.filter.search = null
      }
      this.filter.dateFrom = null
      this.filter.dateTo = null
    },
    onFilterApply () {
      this.filter.page = 1

      this.$emit('filterApply')
    },
    onTimeFromInputChange (newVal) {
      this.refDateTimeFrom.value = newVal || this.filter.dateFrom || null
    },
    onTimeToInputChange (newVal) {
      this.refDateTimeTo.value = newVal || this.filter.dateTo || null
    },
    populateVehiclePostProcessingTaskTypes () {
      InventoryService.getVehiclePostProcessingTaskTypes().then(res => {
        this.vehiclePostProcessingTaskTypes = res.data
      }).catch(ex => {
        console.error(ex)
      })
    }
  }
}
</script>

<style>
.inventory-logs-filter-form {
  display: flex;
  flex-direction: row;
  flex-shrink: 1;
  padding: 10px;
}
.force-account-notice {
    float: right;
}
.inventory-logs-filter-form-input {
  width: 25em;
  margin-right: 15px;
}
.inventory-logs-filter-form-btn {
  width: 430px;
  display: flex;
}
@media (max-width: 1520px) {
  .inventory-logs-filter-form {
    flex-direction: column;
    padding-top: 0;
  }
  .inventory-logs-filter-form-btn {
    width: 100%;
    margin-top: 10px;
  }
  .inventory-logs-filter-form-input {
    width: 100%;
    margin-top: 10px;
  }
  .force-account-notice {
    float: left;
  }
}
@media (max-width: 505px) {
  .inventory-logs-filter-form-btn {
    display: flex;
    flex-shrink: 1;
    flex-direction: column;
  }
  .inventory-log-btn {
    width: 100px;
  }
}
</style>
