const userActivityTypes = Object.freeze({
  undefined: {value: 0, text: 'Undefined'},
  account: {value: 1, text: 'Accounts'},
  group: {value: 2, text: 'Account Groups'},
  contact: {value: 3, text: 'Contacts'}
})

const sortTypes = Object.freeze({
  accountIdAsc: 1,
  accountIdDesc: 2,
  accountNameAsc: 3,
  accountNameDesc: 4,
  dateTimeAsc: 5,
  dateTimeDesc: 6,
  operationResultAsc: 7,
  operationResultDesc: 8,
  userNameAsc: 9,
  userNameDesc: 10,
  groupNameAsc: 11,
  groupNameDesc: 12,
  userTypeAsc: 13,
  userTypeDesc: 14
})

const accountActionTypes = Object.freeze({
  undefined: {value: 0, text: 'Undefined'},
  insertAccountSettings: {value: 1, text: 'Insert Account Settings'},
  updateAccountSettings: {value: 2, text: 'Update Account Settings'},
  runClosingAccountProcess: {value: 3, text: 'Run Closing Account Process'}
})

const groupActionTypes = Object.freeze({
  undefined: {value: 0, text: 'Undefined'},
  insertGroup: {value: 1, text: 'Insert Group'},
  insertPermission: {value: 4, text: 'Insert Permission'},
  insertMultiplePermissions: {value: 5, text: 'Insert Multiple Permissions'},
  updateGroup: {value: 2, text: 'Update Group'},
  updatePermission: {value: 6, text: 'Update Permission'},
  updateMultiplePermissions: {value: 7, text: 'Update Multiple Permissions'},
  copyPermissions: {value: 8, text: 'Copy Permissions'},
  deleteGroup: {value: 3, text: 'Delete Group'},
  deletePermission: {value: 9, text: 'Delete Permission'}
})

const contactActionTypes = Object.freeze({
  undefined: {value: 0, text: 'Undefined'},
  insertContact: {value: 1, text: 'Insert Contact'},
  insertContactMapping: {value: 5, text: 'Insert Contact Mapping'},
  setAccountContacts: {value: 3, text: 'Set Account Contacts'},
  setAccountContactsMapping: {value: 7, text: 'Set Account Contacts Mapping'},
  updateContact: {value: 2, text: 'Update Contact'},
  updateContactMapping: {value: 6, text: 'Update Contact Mapping'},
  deleteContact: {value: 4, text: 'Delete Contact'},
  deleteContactMapping: {value: 8, text: 'Delete Contact Mapping'}
})

export default {
  userActivityTypes,
  sortTypes,
  accountActionTypes,
  groupActionTypes,
  contactActionTypes
}
