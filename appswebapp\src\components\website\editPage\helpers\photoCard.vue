<template>
  <b-card no-body class="bg-light">
    <div class="border-bottom d-flex flex-row">
      <div class="bg-light custom-size rounded">
        <i
          class='ion ion-ios-menu m-0 h5 opacity-100'
        ></i>
      </div>
      <div class="custom-size ml-auto">
        <font-awesome-icon @click="onDelete" style="cursor: pointer;" icon="trash" size="md" />
      </div>
    </div>
    <div class="p-3">
      <detail-row :bootstrap-mode="true">
        <span slot="title">Photo:</span>
        <b-img :src="getPhotoUrl" class="photo-size" slot="payload"></b-img>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Add/Replace Photo:</span>
        <b-form-file slot="payload" v-model="photoItem.file" class="mt-3" accept="image/*" plain></b-form-file>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Title 1:</span>
        <b-form-input v-model="photoItem.title1" slot="payload" :required="true"></b-form-input>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Title 2:</span>
        <b-form-input v-model="photoItem.title2" slot="payload"></b-form-input>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Photo Description:</span>
        <b-form-textarea rows="5" v-model="photoItem.textHtml" slot="payload"></b-form-textarea>
      </detail-row>
      <b-btn variant="primary" @click="saveChanges" class="float-right" size="sm"><font-awesome-icon icon="cloud-upload-alt" /> Save Changes</b-btn>
    </div>
  </b-card>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'

export default {
  props: {
    photoItem: { type: Object, required: true },
    pageId: { type: Number, required: true },
    index: { type: Number, required: true },
    imageHost: { type: String }
  },
  data () {
    return {
      accountId: this.$route.params.accountId
    }
  },
  components: {
    detailRow
  },
  computed: {
    getPhotoUrl () {
      if (this.photoItem && this.photoItem.file) {
        return URL.createObjectURL(this.photoItem.file)
      }
      return `${this.imageHost}sites/${this.accountId}/images/photo_list_page_${this.pageId}/${this.photoItem.photoItemId}_640.jpg`
    }
  },
  methods: {
    saveChanges () {
      if (this.photoItem.title1) {
        let formData = new FormData()
        formData.append('file', this.photoItem.file)
        formData.append('updateModel', JSON.stringify(this.photoItem))
        this.$store.dispatch('website/updatePhotoItem', { accountId: this.accountId, pageId: this.pageId, data: formData }).then(res => {
          this.$toaster.success('Photo Item Successfully Updated')
        }).catch(ex => {
          this.$toaster.error('Something went wrong. Please try again later.')
          this.$logger.handleError(ex, 'Cannot update photo item')
        })
      } else {
        this.$toaster.error('Title 1 is required')
      }
    },
    onDelete () {
      this.$store.dispatch('website/deletePhotoItem', { accountId: this.accountId, pageId: this.pageId, photoItemId: this.photoItem.photoItemId }).then(res => {
        this.$toaster.success('Photo Item Successfully Deleted')
        this.$emit('photoItemDelete')
      }).catch(ex => {
        this.$toaster.error('Something went wrong. Please try again later.')
        this.$logger.handleError(ex, 'Cannot delete photo item')
      })
    }
  }
}
</script>

<style scoped>
.custom-size {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.photo-size {
  width: 100px;
}
</style>
