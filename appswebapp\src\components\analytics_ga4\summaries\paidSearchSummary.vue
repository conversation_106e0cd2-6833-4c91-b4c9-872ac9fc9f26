<template>
  <div class="fullwidth-element bg-dark dark text-white">

    <div class="container-fluid container-p-y">

      <div class="chart-filters text-center">
        <graph-tabs
          :tabsConfiguration="barTabs"
          @tabSelect="onGraphTypeChanged"
        />
      </div>

      <div>
        <vue-echart :options="barOptions" :auto-resize="true"></vue-echart>
      </div>

      <!-- Summary cards -->
      <div class="row mt-3 widget-metric-higlights">
        <div class="col-6 col-sm-6 col-xl">
          <summary-card
            label="Sessions"
            :value="summary.sessions"
            :delta="summary.sessionsDelta"
            :rangeLabel="summary.label">
            <i class="ion ion-md-browsers h1 m-0 opacity-25 d-none d-sm-inline"></i>
          </summary-card>
        </div>

        <div class="col-6 col-sm-6 col-xl">
          <summary-card
            label="Leads"
            :value="summary.totalLeads"
            :delta="summary.totalLeadsDelta"
            :rangeLabel="summary.label">
            <i class="ion ion-md-chatboxes h1 m-0 opacity-25 d-none d-sm-inline"></i>
          </summary-card>
        </div>

        <div class="col-6 col-sm-6 col-xl">
          <summary-card
            label="Conv. Rate"
            :value="summary.conversionRate"
            :delta="summary.conversionRateDelta"
            :rangeLabel="summary.label">
            <template slot="value" slot-scope="valueScope">
              {{valueScope.localizedValue}}%
            </template>
            <i class="ion ion-md-pie h1 m-0 opacity-25 d-none d-sm-inline"></i>
          </summary-card>
        </div>

        <div class="col-6 col-sm-6 col-xl">
          <summary-card
            label="Spend"
            :value="summary.spend"
            :delta="summary.spendDelta"
            :rangeLabel="summary.label">
            <template slot="value" slot-scope="valueScope">
              ${{valueScope.localizedValue}}
            </template>
            <i class="ion ion-md-wallet h1 m-0 opacity-25 d-none d-sm-inline"></i>
          </summary-card>
        </div>

        <div class="col-6 col-sm-6 col-xl">
          <summary-card
            label="Cost Per Lead"
            :value="summary.costPerLead"
            :delta="summary.costPerLeadDelta"
            :rangeLabel="summary.label">
            <template slot="value" slot-scope="valueScope">
              ${{valueScope.localizedValue}}
            </template>
            <i class="ion ion-md-cash h1 m-0 opacity-25 d-none d-sm-inline"></i>
          </summary-card>
        </div>

      </div>

    </div>

  </div>
</template>

<script>
import dateHelper from '../../../plugins/locale/date'

import 'echarts/lib/chart/line'
import 'echarts/lib/component/tooltip'
import 'echarts/lib/component/legend'

export default {
  name: 'paid-search-summary',
  props: {
    barItems: { type: Array, required: true },
    summary: { type: Object, required: true },
    barTimeFormat: { type: String, required: true }
  },
  components: {
    'vue-echart': () => import('vue-echarts/components/ECharts.vue'),
    'summary-card': () => import('../summaryCard.vue'),
    'graph-tabs': () => import('../graphTabs.vue')
  },
  data () {
    return {
      bar: {
        activeTab: 'sessions'
      }
    }
  },
  computed: {
    barTabs () {
      return {
        defaultTabKey: this.bar.activeTab,
        tabs: [
          {
            key: 'sessions',
            label: 'Sessions',
            iconClass: 'ion-md-browsers'
          }, {
            key: 'totalLeads',
            label: 'Leads',
            iconClass: 'ion-md-chatboxes'
          }, {
            key: 'conversionRate',
            label: 'Conv. Rate',
            iconClass: 'ion-md-pie'
          }, {
            key: 'spend',
            label: 'Spend',
            iconClass: 'ion-md-wallet'
          }, {
            key: 'costPerLead',
            label: 'Cost Per. Lead',
            iconClass: 'ion-md-cash'
          }
        ]
      }
    },
    barOptions () {
      return {
        color: '#6b0001',
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            return `${params[0].name}<br />${this.$locale.formatNumber(params[0].value)}`
          },
          axisPointer: {
            type: 'line',
            lineStyle: {
              color: '#444'
            }
          },
          textStyle: {
            fontSize: 13
          }
        },
        grid: {
          left: '0',
          right: '0',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: this.barItems.map(x => {
              return dateHelper.getDayFormatted(x.dateFrom, this.barTimeFormat)
            }),
            boundaryGap: false,
            axisTick: {
              show: true,
              alignWithLabel: true
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: '#444'
              }
            },
            axisLabel: {
              color: 'rgba(255, 255, 255, .9)'
            }
          }
        ],
        yAxis: [
          {
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#444'
              }
            },
            axisLabel: {
              formatter: '{value}',
              color: 'rgba(255, 255, 255, .9)'
            },
            type: 'value'
          }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 50,
            shadowColor: 'rgba(0, 0, 0, 0.3)'
          }
        },
        series: [
          {
            type: 'line',
            data: this.barItems.map(x => x[this.bar.activeTab]),
            areaStyle: {},
            smooth: 0.4,
            symbolSize: 7,
            showSymbol: false,
            itemStyle: {
              normal: {
                color: '#dc3545'
              }
            }
          }],
        animationDuration: 2000
      }
    }
  },
  methods: {
    onGraphTypeChanged (newTab) {
      this.bar.activeTab = newTab.key
    }
  }
}
</script>

<style scoped>

</style>
