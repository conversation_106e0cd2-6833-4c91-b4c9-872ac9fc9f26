@import '../_custom-variables/pages';

.echarts {
  height: 250px !important;
  width: 100% !important;
}

.widget-sessions-device-2 .echarts {
  height: 80px !important;
}

.fullwidth-element {
  margin: -1.5rem -1rem 0 -1rem;
}

.dark {
  .nav-tabs .nav-link:not(.active),
  .nav-pills .nav-link:not(.active) {
    color: #cccccc;
  }
  .nav-tabs .nav-link:not(.active):hover,
  .nav-tabs .nav-link:not(.active):focus,
  .nav-pills .nav-link:not(.active):hover,
  .nav-pills .nav-link:not(.active):focus {
    color: #ffffff;
  }
}

@media (min-width: 992px) {
  .fullwidth-element {
    margin: -1.5rem -2rem 0 -2rem;
    .metric-amount {
      font-size: 1.313rem !important;
    }
  }
}

@media (max-width: 768px) {
  .chart-filters {
    .nav-link {
      font-size: .8rem;
      padding: .5rem .5rem;
    }
  }
}
