using System;
using System.Threading.Tasks;
using EBizAutos.Apps.CommonLib.Models.Web;
using EBizAutos.Apps.Shared.Api.Models.Mfa;
using EBizAutos.Apps.Shared.Api.Utilities.Managers;
using EBizAutos.Apps.Shared.Api.Utilities.Helpers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using EBizAutos.Apps.Shared.Api.Models;
using static EBizAutos.Apps.CommonLib.Enums.AuthenticationEnums;
using EBizAutos.Apps.Authentication.CommonLib.Utilities.Attributes;
using Microsoft.Extensions.DependencyInjection;
using EBizAutos.Apps.CommonLib.Abstract.Repositories.AppsAccountSettings;

namespace EBizAutos.Apps.Shared.Api.Controllers {
	[Route("auth/mfa")]
	public class MfaController : BaseApiController {
		private readonly UserAuthenticationManager _authenticationManager;
		private readonly IAppsAccountSettingsRepository _accountSettingsRepository;
		private readonly MfaChallengeManager _mfaManager;

		public MfaController(IServiceProvider serviceProvider) : base(serviceProvider) {
			_authenticationManager = serviceProvider.GetRequiredService<UserAuthenticationManager>();
			_accountSettingsRepository = serviceProvider.GetRequiredService<IAppsAccountSettingsRepository>();
			_mfaManager = serviceProvider.GetRequiredService<MfaChallengeManager>();
		}

		[HttpPost("create")]
		[AllowAnonymous]
		public async Task<IActionResult> Create([FromBody] MfaCreateChallengeRequestModel model) {
			if (model == null || string.IsNullOrWhiteSpace(model.UserName) || string.IsNullOrWhiteSpace(model.Password)) {
				return BadRequest("Invalid request.");
			}

			var validationResult = await _authenticationManager.ValidateUserCredentialsAsync(new LoginRequestModel {
				UserName = model.UserName,
				Password = model.Password,
				ApplicationName = model.ApplicationName ?? string.Empty,
				DeviceId = model.DeviceId ?? string.Empty,
				DeviceModel = model.DeviceModel ?? string.Empty
			});
			if (validationResult.IsRejected) return ApiResult(validationResult.AsPromise(x => false));

			var user = validationResult.Model;
			bool isMfaRequired = false;
			if (user != null && user.AccountId > 0) {
				var acc = _accountSettingsRepository.GetBasicAccountSettings(user.AccountId);
				isMfaRequired = acc != null && acc.IsMfaEnabled;
			}

			if (!isMfaRequired) {
				return ApiResult(PromiseResultModel<MfaCreateChallengeResponseModel>.SuccessResult(new MfaCreateChallengeResponseModel {
					IsMfaRequired = false,
					AvailableMethods = Array.Empty<MfaMethodModel>()
				}));
			}

			var availableMethods = new System.Collections.Generic.List<MfaMethodModel>();

			// Add Email method if available and verified
			if (!string.IsNullOrEmpty(user.PersonalInformation?.Email) &&
				user.PersonalInformation?.IsEmailVerified == true) {
				availableMethods.Add(new MfaMethodModel {
					Type = MfaMethodEnum.Email,
					Name = MfaMethodHelper.GetMethodName(MfaMethodEnum.Email),
					MaskedDestination = MfaChallengeManager.MaskEmail(user.PersonalInformation.Email)
				});
			}

			// Add SMS method if available and verified
			if (!string.IsNullOrEmpty(user.PersonalInformation?.MfaPhoneNumber) &&
				user.PersonalInformation?.IsMfaPhoneNumberVerified == true) {
				availableMethods.Add(new MfaMethodModel {
					Type = MfaMethodEnum.Sms,
					Name = MfaMethodHelper.GetMethodName(MfaMethodEnum.Sms),
					MaskedDestination = MfaChallengeManager.MaskPhone(user.PersonalInformation.MfaPhoneNumber)
				});
			}

			// Check if any verified MFA methods are available
			if (availableMethods.Count == 0) {
				return ApiResult(PromiseResultModel<MfaCreateChallengeResponseModel>.CanceledResult(
					"No verified MFA methods available. Please set up and verify your email or phone number first."));
			}

			var challengeResult = await _mfaManager.CreateChallengeAsync(
				user: user,
				applicationName: model.ApplicationName ?? string.Empty,
				deviceId: model.DeviceId ?? string.Empty,
				deviceModel: model.DeviceModel ?? string.Empty,
				ipAddress: HttpContext?.Connection?.RemoteIpAddress?.ToString() ?? string.Empty
			);

			if (challengeResult.IsRejected) {
				return ApiResult(challengeResult.AsPromise(x => false));
			}

			var challenge = challengeResult.Model;

			var response = new MfaCreateChallengeResponseModel {
				IsMfaRequired = true,
				ChallengeId = challenge.Id,
				AvailableMethods = availableMethods.ToArray()
			};
			return ApiResult(PromiseResultModel<MfaCreateChallengeResponseModel>.SuccessResult(response));
		}

		[HttpPost("send")]
		[AllowAnonymous]
		public async Task<IActionResult> Send([FromBody] MfaSendCodeRequestModel model) {
			if (model == null || string.IsNullOrWhiteSpace(model.ChallengeId) || !Enum.IsDefined(typeof(MfaMethodEnum), model.Method)) {
				return BadRequest("Invalid request.");
			}

			var sendResult = await _mfaManager.GenerateAndSendCodeAsync(
				challengeId: model.ChallengeId,
				method: model.Method
			);

			if (sendResult.IsRejected) {
				return ApiResult(sendResult.AsPromise(x => false));
			}

			return ApiResult(PromiseResultModel<MfaSendCodeResponseModel>.SuccessResult(new MfaSendCodeResponseModel { IsSent = true }));
		}

		[HttpPost("verify")]
		[AllowAnonymous]
		public async Task<IActionResult> Verify([FromBody] MfaVerifyCodeRequestModel model) {
			if (model == null || string.IsNullOrWhiteSpace(model.ChallengeId) || string.IsNullOrWhiteSpace(model.Code)) {
				return BadRequest("Invalid request.");
			}

			var verifyResult = await _mfaManager.VerifyAsync(model.ChallengeId, model.Code);
			if (verifyResult.IsRejected) {
				return ApiResult(verifyResult.AsPromise(x => false));
			}

			var (user, applicationName, deviceId, deviceModel) = verifyResult.Model;

			if (model.HasToCompleteSignIn) {
				var authResult = await _authenticationManager.CreateSessionAndCompleteCookieSignInAsync(user, new LoginRequestModel {
					UserName = user.PersonalInformation?.UserName,
					Password = string.Empty,
					DeviceId = deviceId,
					DeviceModel = deviceModel,
					ApplicationName = applicationName
				});
				if (authResult.IsRejected) {
					return ApiResult(authResult.AsPromise(x => false));
				}
			}

			return ApiResult(PromiseResultModel<MfaVerifyCodeResponseModel>.SuccessResult(new MfaVerifyCodeResponseModel { IsValid = true }));
		}

		[HttpPost("setup_start")]
		[UserAuthorize]
		public async Task<IActionResult> StartMfaMethodSetup([FromBody] MfaStartMethodSetupRequestModel model) {
			if (model == null || !Enum.IsDefined(typeof(MfaMethodEnum), model.Method)) {
				return BadRequest("Invalid request.");
			}
			var challengeResult = await _mfaManager.CreateChallengeAndSendCodeAsync(
				contactId: Identity.ContactId,
				method: model.Method,
				applicationName: model.ApplicationName ?? string.Empty,
				deviceId: model.DeviceId ?? string.Empty,
				deviceModel: model.DeviceModel ?? string.Empty,
				ipAddress: HttpContext?.Connection?.RemoteIpAddress?.ToString() ?? string.Empty
			);

			if (challengeResult.IsRejected) {
				return ApiResult(challengeResult.AsPromise(x => false));
			}

			return ApiResult(PromiseResultModel<MfaStartMethodSetupResponseModel>.SuccessResult(
				new MfaStartMethodSetupResponseModel {
					ChallengeId = challengeResult.Model.Id
				}
			));
		}

		[HttpPost("setup_finish")]
		[UserAuthorize]
		public async Task<IActionResult> FinishMfaMethodSetup([FromBody] MfaFinishMethodSetupRequestModel model) {
			if (model == null || string.IsNullOrWhiteSpace(model.ChallengeId) || string.IsNullOrWhiteSpace(model.Code)) {
				return BadRequest("Invalid request.");
			}

			var verifyResult = await _mfaManager.VerifyMfaMethodAsync(model.ChallengeId, model.Code);
			if (verifyResult.IsRejected) {
				return ApiResult(verifyResult.AsPromise(x => false));
			}

			return ApiResult(PromiseResultModel<MfaVerifyCodeResponseModel>.SuccessResult(
				new MfaVerifyCodeResponseModel { IsValid = true }
			));
		}
	}
}