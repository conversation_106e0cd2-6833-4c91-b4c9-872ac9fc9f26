<template>
  <div v-if="isReadyToShow">
    <div class='grpmgmt-group-name mb-5'>
      <h2 class="d-flex justify-content-between align-items-center w-100 pb-0">
        <div v-show="!isEditingGroupName">
          {{group.groupName}}
            <font-awesome-icon icon="pencil-alt" id="grpmgmt-group-name-edit-icon" class="ml-3" color="#C90F17" @click="onEditGroupName"/>
        </div>
        <div ref="editgroupname" class='grpmgmt-group-name-edit w-100' v-show="isEditingGroupName">
          <input ref="groupnameinput" type="text" v-model="group.groupName" @focusout="onGroupNameChange">
          <small id="group-name-desc" class="form-text" v-show="!isGroupNameValid">
            Must be at least 2 characters long
          </small>
        </div>
      </h2>
    </div>
    <div class='group-accounts mb-5'>
      <h4 class="d-flex justify-content-between align-items-center w-100 pb-0 mb-4">
        <div>Accounts</div>
        <div v-show="!isEditingAccountListing">
          <div class="d-block btn-group">
            <button id="editGroupNameButton" type="button" class="btn btn-primary btn-round btn-sm" @click="editAccountsList">
              Edit
            </button>
          </div>
        </div>
        <div v-show="isEditingAccountListing">
          <div class="d-block btn-group">
            <c-button @confirm="discardEditAccountListing" message="Discard all changes?" variant="primary btn-round" size="sm">
              <span>Discard</span>
            </c-button>
            <button id="discardSaveAccountListingButton"  type="button" class="btn btn-primary btn-round btn-sm" @click="saveEditAccountListing">
              Save
            </button>
          </div>
        </div>
      </h4>
      <div class="accounts">
        <account-list v-if="!isEditingAccountListing" :groupId="group.groupId" :accounts="group.accounts"></account-list>
        <account-selector
          :selectedIds="accountIdsToSelect"
          @onAccountSelected="onAccountSelected"
          @onAccountUnselected="onAccountUnselected"
          v-show="isEditingAccountListing">
        </account-selector>
      </div>
    </div>
  </div>
</template>

<script>
import accountSelector from '@/components/account/groups/accountsselector.vue'
import accountList from '@/components/account/groups/groupaccountslist.vue'
import groupsManagementService from '@/services/accounts/groups/GroupsManagementService'

export default {
  name: 'Group-Managament',
  metaInfo: {
    title: 'Account Group'
  },
  props: {
    groupId: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      group: null,
      originalGroupName: null,
      accountIdsToSelect: [],
      isEditingGroupName: false,
      isEditingAccountListing: false,
      isReadyToShow: false
    }
  },
  mounted () {
    this.populateGroupInfo()
  },
  methods: {
    populateGroupInfo () {
      groupsManagementService.getGroup(this.groupId).then(x => {
        if (x.data) {
          this.group = x.data
          this.group.selectedAccountIds = this.group.accounts.map(x => x.accountId)
          this.accountIdsToSelect = Array.from(this.group.selectedAccountIds)
          this.originalGroupName = this.group.groupName
          this.isReadyToShow = true
        }
      }).catch(reason => {
        console.error(reason)
      })
    },
    editAccountsList () {
      this.isEditingAccountListing = true
    },
    discardEditAccountListing () {
      this.isEditingAccountListing = false
      this.accountIdsToSelect = Array.from(this.group.selectedAccountIds)
    },
    saveEditAccountListing () {
      groupsManagementService.updateGroup({groupId: this.group.groupId, groupName: this.group.groupName, accounts: this.accountIdsToSelect})
        .then(x => {
          this.isEditingAccountListing = false
          this.populateGroupInfo()
        }).catch(reason => {
          console.error(reason)
        })
    },
    onAccountSelected (accountId) {
      if (!this.accountIdsToSelect.includes(accountId)) {
        this.accountIdsToSelect.push(accountId)
      }
    },
    onAccountUnselected (accountId) {
      var rmIndex = this.accountIdsToSelect.indexOf(accountId)
      if (rmIndex !== -1) {
        this.accountIdsToSelect.splice(rmIndex, 1)
      }
    },
    onEditGroupName () {
      this.isEditingGroupName = true
      this.$nextTick(() => {
        this.$refs.groupnameinput.focus()
      })
    },
    onGroupNameChange () {
      if (this.isGroupNameValid) {
        this.isEditingGroupName = false
        if (this.group.groupName !== this.originalGroupName) {
          groupsManagementService.updateGroup({groupId: this.group.groupId, groupName: this.group.groupName, accounts: this.group.selectedAccountIds}).then(x => {
            this.originalGroupName = this.group.groupName
          }).catch(reason => {
            console.error(reason)
          })
        }
      }
    }
  },
  computed: {
    isGroupNameValid () {
      return this.group.groupName.length > 2
    }
  },
  components: {
    'account-selector': accountSelector,
    'account-list': accountList
  }
}
</script>

<style lang="scss">
  .grpmgmt-group-name-edit {
    input {
      border: none;
      width: 100%;
      padding: 8px;
      border-radius: 8px;

      :focus {
        outline: none;
      }
    }

    small {
        color: red;
        font-size: 0.85rem;
      }
  }

  #grpmgmt-group-name-edit-icon {
    display: none;
    font-size: smaller;
  }

  .grpmgmt-group-name:hover {
    #grpmgmt-group-name-edit-icon {
      display: inline-block;
    }
  }
</style>
