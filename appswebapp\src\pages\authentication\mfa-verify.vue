<template>
  <div>
    <div class="authentication-wrapper authentication-1 px-1">
      <div class="authentication-inner py-4">
        <img id="login-logo" src="/static/img/logo-ebizautos-login.svg" alt="eBizAutos Login Logo">
        <form class="my-4">
          <b-alert show :variant="messageType" v-if="!!message">{{message}}</b-alert>
          <h2 class="text-center mb-2">Enter One-Time Passcode</h2>
          <div class="text-center text-muted mb-3">
            A one-time passcode was just sent to <strong>{{ maskedDestination }}</strong>.
          </div>
          <b-input v-model="code" placeholder="Enter the code" size="lg" />
          <div class="mt-2">
            <small class="text-muted policy-links">Didn't get it? <a href="#" @click.prevent="resend">Resend</a>.</small>
          </div>
          <div class="mt-3">
            <b-btn type="submit" @click.prevent="verify" variant="primary" :disabled="isLocked" class="btn-block btn-lg">Continue Sign In</b-btn>
          </div>
        </form>
      </div>
    </div>
    <app-layout-footer />
  </div>
</template>

<script>
import LayoutFooter from '../../layout/LayoutFooter.vue'

export default {
  name: 'mfa-verify',
  components: { 'app-layout-footer': LayoutFooter },
  data: () => ({ code: '', isLocked: false, message: '', messageType: 'danger' }),
  computed: {
    maskedEmail () { return this.$store.getters['mfa/maskedEmail'] },
    maskedDestination () { return this.$store.getters['mfa/maskedDestination'] }
  },
  methods: {
    verify () {
      this.message = ''
      if (!this.code.trim()) { this.message = 'Please enter the code'; return }
      this.isLocked = true
      this.$store.dispatch('mfa/verify', this.code)
        .then(() => {
          this.$store.commit('authentication/setIsAuthenticated', true)
          this.$router.replace(decodeURIComponent(this.$route.query.returnurl || this.$route.query.redirect || '/'))
        })
        .catch(err => {
          const msg = (((err || {}).response || {}).data || '')
          this.message = (msg && msg.length < 120) ? msg : 'Verification failed'
        })
        .finally(() => { this.isLocked = false })
    },
    resend () {
      this.message = ''
      const selectedMethodType = this.$store.getters['mfa/selectedMethodType']
      this.$store.dispatch('mfa/send', selectedMethodType)
        .then(() => {
          this.messageType = 'success'
          this.message = 'Verification code sent'
        })
        .catch(err => {
          const msg = (((err || {}).response || {}).data || '')
          this.messageType = 'danger'
          this.message = (msg && msg.length < 120) ? msg : 'Failed to send code'
        })
    }
  }
}
</script>

<style>
  #login-logo { height: 2.25rem; margin: 0 auto; display: block; }
  .policy-links a {
    color: #4E5155 !important;
    text-decoration: underline !important;
    font-weight: bold !important;
  }
</style>
