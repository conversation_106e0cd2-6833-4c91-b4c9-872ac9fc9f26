import permissions from '../shared/common/permissions'

export default [
  {
    path: '/system',
    component: () => import('@/layout/Layout2'),
    meta: {
      permissions: [permissions.FullAccess],
      applicationFullAccess: permissions.FullAccess
    },
    children: [
      {
        path: 'encryptor',
        name: 'encryptor',
        meta: {
          permissions: [permissions.FullAccess],
          applicationFullAccess: permissions.FullAccess
        },
        component: () => import('../pages/systemtools/encryptor')
      },
      {
        path: 'generatetoken',
        name: 'generatetoken',
        meta: {
          permissions: [permissions.FullAccess],
          applicationFullAccess: permissions.FullAccess
        },
        component: () => import('../pages/systemtools/generatetoken')
      },
      {
        path: 'errorreport',
        name: 'error-report',
        meta: {
          permissions: [permissions.FullAccess],
          applicationFullAccess: permissions.FullAccess
        },
        component: () => import('../pages/systemtools/errorReport')
      },
      {
        path: 'error/:errorId',
        name: 'error-details',
        component: () => import('../pages/systemtools/errorDetails'),
        props: (route) => ({ errorId: route.params.errorId }),
        meta: {
          permissions: [permissions.ErrorsViewErrors],
          applicationFullAccess: permissions.EbizAutosAdmin
        }
      },
      {
        path: 'errors',
        name: 'error-monitor',
        component: () => import('../pages/systemtools/errorMonitor'),
        meta: {
          permissions: [permissions.ErrorsViewErrors],
          applicationFullAccess: permissions.EbizAutosAdmin
        }
      },
      {
        path: 'apiping',
        name: 'api-ping',
        component: () => import('../pages/systemtools/apiPing'),
        meta: {
          permissions: [permissions.FullAccess],
          applicationFullAccess: permissions.FullAccess
        }
      },
      {
        path: 'useractivity',
        name: 'system-tools-user-activity',
        component: () => import('../pages/systemtools/userActivity'),
        meta: {
          permissions: [permissions.FullAccess],
          applicationFullAccess: permissions.FullAccess
        }
      },
      {
        path: 'useractivity/:id/details',
        name: 'system-tools-user-activity-details',
        props: (route) => ({ logId: route.params.id }),
        component: () => import('../pages/systemtools/userActivityDetails'),
        meta: {
          permissions: [permissions.FullAccess],
          applicationFullAccess: permissions.FullAccess
        }
      },
      {
        path: 'userauthlogs',
        name: 'system-tools-user-auth-logs',
        component: () => import('../pages/systemtools/userAuthorizationLogs'),
        meta: {
          permissions: [permissions.FullAccess],
          applicationFullAccess: permissions.FullAccess
        }
      },
      {
        path: 'userauthlogs/:id/details',
        name: 'system-tools-user-auth-log-details',
        props: (route) => ({ logId: route.params.id }),
        component: () => import('../pages/systemtools/userAuthorizationLogDetails'),
        meta: {
          permissions: [permissions.FullAccess],
          applicationFullAccess: permissions.FullAccess
        }
      }
    ]
  }
]
