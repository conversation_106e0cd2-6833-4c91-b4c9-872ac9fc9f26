<template>
  <editSettingsHelper :readOnlyMode="readOnlyMode" @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="Engine" :isLoading="isUpdatingProcessed" :isDisabled="isDisabled" :isViewMode="isViewMode">
    <template slot="settings-content">
      <detail-row v-if="accountLevel" :fixed-payload-width="true" :title-position="'start'">
        <span slot="title">Use Custom Engine Settings:</span>
        <b-form-checkbox v-if="!isViewMode" slot="payload" v-model="updatedSettings.hasToUseCustomEngineSettings"></b-form-checkbox>
        <span slot="payload" v-else>
          {{ updatedSettings.hasToUseCustomEngineSettings ? 'Yes' : 'No' }}
        </span>
      </detail-row>
      <b-overlay :show="!(!accountLevel || updatedSettings.hasToUseCustomEngineSettings)" opacity="0.7" blur="1.2px">
        <template #overlay>
          <span></span>
        </template>
        <ValidationProvider name="Engine Description" :rules="getAccountEngineRules" v-slot="{errors}">
          <detail-row :fixed-payload-width="true" :title-position="'start'" :error="errors[0]">
            <span slot="title">Engine Description
              <b-icon variant="secondary" :id="`engine-description-popover-${id}`" icon="question-circle"></b-icon>:
              <b-popover :target="`engine-description-popover-${id}`" triggers="hover click blur">
                <template #title>Available Placeholders</template>
                <b-list-group>
                  <b-list-group-item @click="copyText('{VehicleModel}')">{VehicleModel} <b-icon icon="files"></b-icon></b-list-group-item>
                  <b-list-group-item @click="copyText('{EngineDescription}')">{EngineDescription} <b-icon icon="files"></b-icon></b-list-group-item>
                </b-list-group>
              </b-popover>
            </span>
            <span slot="payload" v-if="isViewMode">{{ settings.engineDescriptionTextTemplate || '-' }}</span>
            <b-form-input name="Engine Description" slot="payload" v-else v-model="updatedSettings.engineDescriptionTextTemplate"></b-form-input>
          </detail-row>
        </ValidationProvider>
        <ValidationProvider name="Transmission" :rules="getAccountTransmissionRules" v-slot="{errors}">
          <detail-row :fixed-payload-width="true" :title-position="'start'" :error="errors[0]">
            <span slot="title">Transmission
              <b-icon variant="secondary" :id="`transmission-description-popover-${id}`" icon="question-circle"></b-icon>:
              <b-popover :target="`transmission-description-popover-${id}`" triggers="hover click blur">
                <template #title>Available Placeholders</template>
                <b-list-group>
                  <b-list-group-item @click="copyText('{TransmissionDescription}')">{TransmissionDescription} <b-icon icon="files"></b-icon></b-list-group-item>
                </b-list-group>
              </b-popover>
            </span>
            <span slot="payload" v-if="isViewMode">{{ settings.transmissionDescriptionTextTemplate || '-' }}</span>
            <b-form-input name="Transmission" slot="payload" v-else v-model="updatedSettings.transmissionDescriptionTextTemplate"></b-form-input>
          </detail-row>
        </ValidationProvider>
      </b-overlay>
    </template>
  </editSettingsHelper>
</template>

<script>
import editSettingsHelper from '@/components/_shared/editSettingsHelper'
import detailRow from '@/components/details/helpers/detailRow'
import editSettingsMixin from '../editSettingsMixin'
import globals from '../../../../globals'

export default {
  name: 'tts-engine-settings-section',
  props: {
    settings: { type: Object, required: true },
    isUpdatingProcessed: { type: Boolean, required: true },
    isDisabled: Boolean,
    accountLevel: Boolean,
    readOnlyMode: Boolean
  },
  data () {
    return {
      id: this.$uuid.v4(),
      updatedSettings: globals().getClonedValue(this.settings),
      isViewMode: true
    }
  },
  mixins: [editSettingsMixin],
  components: {
    detailRow,
    editSettingsHelper
  },
  computed: {
    hasToApplyValidationRules () {
      return this.accountLevel && this.updatedSettings.hasToUseCustomEngineSettings
    },
    getAccountEngineRules () {
      return this.hasToApplyValidationRules ? 'required' : ''
    },
    getAccountTransmissionRules () {
      return this.hasToApplyValidationRules ? 'required' : ''
    }
  },
  methods: {
    updateSettings () {
      this.$emit('saveChanges', this.updatedSettings)
    },
    copyText (text) {
      this.$copyProvider.copyTextToClipboard(text).then(() => {
        this.$toaster.success(`Copied the text: ${text}`, { timeout: 4000 })
      }).catch(err => {
        console.error(err)
      })
    }
  },
  watch: {
    'updatedSettings': {
      deep: true,
      handler: function () {
        this.$emit('input', this.updatedSettings)
      }
    }
  }
}
</script>
