<template>
  <b-card v-if="!model.isAutoNotification" class="overflow-auto">
    <h4 v-html="model.eBayTitle"></h4>
    <detail-row titlePosition="start" fixedPayloadWidth v-if="model.eBayMessage || model.eBayMessageBody">
      <span slot="title">Message:</span>
      <div slot="payload">
        <strong v-html="model.eBayMessage"></strong>
        <br>
        <span v-html="model.eBayMessageBody"></span>
      </div>
    </detail-row>
    <detail-row titlePosition="start" fixedPayloadWidth>
      <span slot="title">Other Information:</span>
      <div slot="payload" class="d-flex flex-column">
        <div v-if="model.status"><b>Status: </b>{{model.status}}</div>
        <div><b>Price Amount: </b>{{model.eBayBidAmount}}</div>
        <div v-if="model.autoDeclinePrice"><b>$ Auto-Decline: </b>{{model.autoDeclinePrice}}</div>
        <a :href="model.eBayAuctionUrl">Auction</a>
      </div>
    </detail-row>
    <detail-row titlePosition="start" fixedPayloadWidth>
      <span slot="title">Vehicle of Interest:</span>
      <b-link slot="payload" class="d-flex flex-column vdp-link" target="_blank" rel="noopener noreferrer" :href="model.vdpLink || '#'">
        <b-img class="leads-vehicle-img" v-if="model.vehiclePresentationPhotoUrl" :src="model.vehiclePresentationPhotoUrl"/>
        <span>{{model.vehicleTitle}}</span>
        <span v-if="model.vehicleStockNumber">Stock #: {{model.vehicleStockNumber}}</span>
      </b-link>
    </detail-row>
  </b-card>
  <div v-else>
    {{model.autoNotificationMessage}}
  </div>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'

export default {
  name: 'leads-ebay-type-message',
  props: {
    model: { type: Object, required: true }
  },
  components: {
    'detail-row': detailRow
  }
}
</script>

<style scoped>
  .leads-vehicle-img {
    width: 107px;
    height: auto;
  }
  .vdp-link {
    color: rgba(24, 28, 33, 0.9);
  }
  .vdp-link:hover {
    color: #bf0e16;
  }
</style>
