<template>
  <div>
    <div class="authentication-wrapper authentication-1 px-1">
      <div class="authentication-inner py-4">
        <img id="login-logo" src="/static/img/logo-ebizautos-login.svg" alt="eBizAutos Login Logo">
        <form class="my-4">
          <b-alert show :variant="messageType" v-if="!!message">{{message}}</b-alert>
          <h2 class="text-center mb-2">OTP Verification Method</h2>
          <div class="text-center text-muted mb-3">
            A one-time passcode will be sent to you as an additional verification step.
          </div>
          <div class="mb-3">
            <div class="mb-2">How do you want to get the code?</div>
            <b-form-radio
              v-for="method in availableMethods"
              :key="method.name"
              v-model="selectedMethod"
              :value="method.name">
              {{ getMethodDisplayName(method.name) }}: <strong>{{ method.maskedDestination }}</strong>
            </b-form-radio>
          </div>
          <div class="text-muted small mb-3 policy-links">
            Message and data rates may apply. One message per request.<br/>
            View our <a href="https://www.ebizautos.com/terms" target="_blank">Terms of Service</a> and <a href="https://www.ebizautos.com/privacy-policy" target="_blank">Privacy Policy</a>.
          </div>
          <div>
            <b-btn type="submit" @click.prevent="send" variant="primary" :disabled="isLocked || !selectedMethod" class="btn-block btn-lg">Send</b-btn>
          </div>
        </form>
      </div>
    </div>
    <app-layout-footer />
  </div>
</template>

<script>
import LayoutFooter from '../../layout/LayoutFooter.vue'

export default {
  name: 'mfa-method',
  components: { 'app-layout-footer': LayoutFooter },
  data: () => ({
    isLocked: false,
    selectedMethod: null,
    message: '',
    messageType: 'danger'
  }),
  watch: {
    availableMethods: {
      immediate: true,
      handler (methods) {
        if (methods.length > 0 && !this.selectedMethod) {
          this.selectedMethod = methods[0].name
        }
      }
    }
  },
  computed: {
    availableMethods () { return this.$store.state.mfa.availableMethods || [] },
  },
  methods: {
    getMethodDisplayName (methodName) {
      switch (methodName) {
        case 'email':
          return 'Email Address'
        case 'sms':
          return 'Text Message via Mobile Phone'
        default:
          return methodName.charAt(0).toUpperCase() + methodName.slice(1)
      }
    },
    send () {
      this.message = ''
      this.isLocked = true
      this.$store.dispatch('mfa/send', this.selectedMethod)
        .then(() => {
          this.$router.push('/mfa/verify')
        })
        .catch(err => {
          const msg = (((err || {}).response || {}).data || '')
          this.messageType = 'danger'
          this.message = (msg && msg.length < 160) ? msg : 'Failed to start verification'
        })
        .finally(() => { this.isLocked = false })
    }
  }
}
</script>

<style>
  #login-logo { height: 2.25rem; margin: 0 auto; display: block; }
  .policy-links a {
    color: #4E5155 !important;
    text-decoration: underline !important;
    font-weight: bold !important;
  }
</style>
