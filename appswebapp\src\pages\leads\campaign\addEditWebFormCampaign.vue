<template>
  <div v-if='!isLoading && !isException'>
    <div class="py-2">
      <b-row>
        <b-col class="d-flex align-items-center">
          <h4 class="m-0">{{isEditMode ? 'Edit' : 'Add'}} Web Form Campaign</h4>
        </b-col>
        <b-col class="d-flex justify-content-end">
          <b-btn variant="primary" size="sm" class="mr-2 custom-btn-height" @click="onSubmit" :disabled="isSubmitButtonDisabled">Submit</b-btn>
          <b-btn variant="secondary" size="sm" class="custom-btn-height" @click="$router.go(-1)">Close</b-btn>
        </b-col>
      </b-row>
    </div>
    <b-card>
      <!-- Web Form Settings Section -->
      <div class="mt-1">
        <div class="border-bottom">
          <b-row>
            <b-col><h6 class="float-left">Web Form Settings</h6></b-col>
          </b-row>
        </div>
        <div>
          <detail-row fixedPayloadWidth>
            <span slot="title">Lead Form Type:</span>
            <b-form-select v-model="selectedLeadType" slot="payload" text-field='label' :value='getLeadTypeOptions[0].value' :options='getLeadTypeOptions' :disabled='isEditMode'/>
          </detail-row>
        </div>
      </div>
      <!-- Web Form Settings Section End -->
      <!-- Campaign Settings Section -->
      <div class="mt-4">
        <div class="border-bottom">
          <b-row>
            <b-col><h6 class="float-left">Campaign Settings</h6></b-col>
          </b-row>
        </div>
        <div>
          <detail-row fixedPayloadWidth>
            <span slot="title">Location Type:</span>
            <b-form-select v-model='selectedCampaignsType' slot="payload" :value='getLocationOptions[0].value' :options='getLocationOptions' :disabled="isEditMode" />
          </detail-row>
          <detail-row fixedPayloadWidth>
            <span slot="title">Campaign(s):</span>
            <multiselect slot="payload"
              v-model='selectedCampaigns'
              track-by="campaignTypeId"
              :options='availableCampaigns'
              label='campaignName'
              :multiple="true"
              :disabled='isEditMode'
              :closeOnSelect='false'
              :showLabels='false'
              :showPointer='false'
              :limit='3'
            />
          </detail-row>
          <detail-row fixedPayloadWidth>
            <span slot="title">Legacy Campaign Id:</span>
            <b-form-select v-model='webFormCommunication.legacyCampaignId' slot="payload" :options='legacyCampaignIds' />
          </detail-row>
          <detail-row fixedPayloadWidth v-if='webFormCommunication.campaignsType === campaignTypes.offSiteParent.value'>
            <span slot="title">Departments:</span>
            <b-form-select v-model='webFormCommunication.departmentType' text-field='name' value-field='id' slot="payload" :options='availableDepartmentTypes' />
          </detail-row>
        </div>
      </div>
      <!-- Campaign Settings Section End -->
      <!-- Tracking & Notification Settings Section -->
      <div class="mt-4">
        <div class="border-bottom">
          <b-row>
            <b-col><h6 class="float-left">Tracking & Notification Settings</h6></b-col>
          </b-row>
        </div>
        <div>
          <div class="d-flex flex-column mt-2">
            <b-form-checkbox v-model='webFormCommunication.notificationSettings.hasToSendEmailToFormContact' class="mt-3">Send Email to Displayed Contact</b-form-checkbox>
            <b-form-checkbox v-model='webFormCommunication.notificationSettings.hasToSendUserEmail' class="mt-3">Send Auto Response Email to Lead</b-form-checkbox>
            <detail-row fixedPayloadWidth v-if='webFormCommunication.notificationSettings.userEmailTemplates && webFormCommunication.notificationSettings.hasToSendUserEmail'>
              <span slot="title">User Email Template:</span>
              <b-form-select v-model='webFormCommunication.notificationSettings.userEmailTemplateId' slot="payload" :options='webFormCommunication.notificationSettings.userEmailTemplates' />
            </detail-row>
            <b-form-checkbox v-if="webFormCommunication.notificationSettings.hasToShowDealerSocketSettings" v-model='webFormCommunication.notificationSettings.hasToSendDealerSocketLead' class="mt-3">Send Dealer Socket Lead</b-form-checkbox>
            <b-form-checkbox v-if="webFormCommunication.notificationSettings.hasToShowShiftDigitalSettings" v-model='webFormCommunication.notificationSettings.hasToSendShiftDigitalLead' class="mt-3">Send Shift Digital Lead</b-form-checkbox>
            <b-form-checkbox v-if="webFormCommunication.notificationSettings.hasToShowPremierTruckSettings" v-model='webFormCommunication.notificationSettings.hasToSendPremierTruckLead' class="mt-3">Send Premier Truck Lead</b-form-checkbox>
            <b-form-checkbox class="mt-3" v-model='webFormCommunication.notificationSettings.hasToSendDealerEmail'>Send Notification Email to Dealership</b-form-checkbox>
            <div class="mt-3 d-flex flex-row ml-4" v-if='webFormCommunication.notificationSettings.hasToSendDealerEmail'>
              <div>
                <label class="text-muted" for="DealershipEmailTo">Dealership Emails (To):</label>
                <multi-input
                  type="email"
                  validateRules='email'
                  :values='webFormCommunication.notificationSettings.dealerEmailAddresses'
                  placeholder="<EMAIL>"
                  v-model='webFormCommunication.notificationSettings.dealerEmailAddresses'
                />
              </div>
              <div class="ml-4">
                <label class="text-muted" for="DealershipEmailCC">Dealership Emails (CC):</label>
                <multi-input
                  type="email"
                  validateRules='email'
                  :values='webFormCommunication.notificationSettings.dealerEmailAddressesCc'
                  placeholder="<EMAIL>"
                  v-model="webFormCommunication.notificationSettings.dealerEmailAddressesCc"
                />
              </div>
            </div>
            <b-form-checkbox v-model='webFormCommunication.notificationSettings.hasToSendAdfEmail' class="mt-3">Send ADF Email to CRM</b-form-checkbox>
            <div v-if='webFormCommunication.notificationSettings.hasToSendAdfEmail'>
              <detail-row fixedPayloadWidth>
                <span slot="title">ADF Lead Source Notes:</span>
                <b-form-input placeholder="Leave empty for default" v-model='webFormCommunication.adfLeadSourceId' slot="payload" />
              </detail-row>
              <div class="mt-3 d-flex flex-row ml-4">
                <div>
                  <label class="text-muted" for="ADFEmailTo">ADF Emails (To):</label>
                  <multi-input
                    type="email"
                    validateRules='email'
                    :values='webFormCommunication.notificationSettings.adfEmailAddresses'
                    placeholder="<EMAIL>"
                    v-model="webFormCommunication.notificationSettings.adfEmailAddresses"
                  />
                </div>
                <div class="ml-4">
                  <label class="text-muted" for="ADFEmailCC">ADF Emails (CC):</label>
                  <multi-input
                    type="email"
                    validateRules='email'
                    :values='webFormCommunication.notificationSettings.adfEmailAddressesCc'
                    placeholder="<EMAIL>"
                    v-model="webFormCommunication.notificationSettings.adfEmailAddressesCc"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- Tracking & Notification Settings Section End -->
    </b-card>
  </div>
  <div v-else-if='isLoading && !isException' class="mt-5">
    <loader size="lg"/>
  </div>
  <div v-else class="mt-5">
    <h4 class="text-center text-muted">Something went wrong! Please reload page</h4>
  </div>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import CommunicationService from '@/services/leads/CommunicationService'
import { communicationTypes, leadType } from '@/shared/leads/common'
import { campaignTypes } from '@/shared/leads/campaignTypes'
import Multiselect from 'vue-multiselect'
import multiInput from '@/components/_shared/multiInput'
import loader from '@/components/_shared/loader'

export default {
  name: 'leads-add-edit-web-form-campaign',
  props: {
    accountId: { type: Number, required: true },
    communicationId: { type: String }
  },
  data () {
    return {
      campaignTypes,
      isLoading: true,
      isException: false,
      isSubmitButtonDisabled: false,
      selectedCampaigns: [],
      legacyCampaignIds: [],
      webFormCommunication: null,
      availableCampaigns: [],
      availableDepartmentTypes: []
    }
  },
  components: {
    'detail-row': detailRow,
    'multiselect': Multiselect,
    'multi-input': multiInput,
    'loader': loader
  },
  computed: {
    isEditMode () {
      if (this.communicationId) {
        return true
      }

      return false
    },
    selectedLeadType: {
      get () {
        if (this.webFormCommunication && this.webFormCommunication.leadType !== 0) {
          return this.webFormCommunication.leadType
        }

        return 0
      },
      set (value) {
        this.webFormCommunication.leadType = value
        this.selectedCampaigns = []
        this.populateAvailableCampaigns()
      }
    },
    selectedCampaignsType: {
      get () {
        if (this.webFormCommunication && this.webFormCommunication.campaignsType !== 0) {
          return this.webFormCommunication.campaignsType
        }

        return 1
      },
      set (value) {
        this.webFormCommunication.campaignsType = value
        this.selectedCampaigns = []
        this.populateAvailableCampaigns()
        this.populateAvailableDepartmentTypes()
      }
    },
    getLeadTypeOptions () {
      let options = []
      Object.values(leadType).forEach(function (x) {
        if (x.value !== leadType.bid.value &&
            x.value !== leadType.buyItNow.value) {
          return options.push(x)
        }
      })

      return options
    },
    getLocationOptions () {
      return Object.values(campaignTypes)
    }
  },
  created () {
    if (this.isEditMode) {
      this.populateCommunicationModelById(this.communicationId)
    } else {
      this.populateCommunicationPrototypeModel()
    }
  },
  methods: {
    finalize () {
      this.$router.push({name: 'leads-dashboard', params: {accountId: this.accountId}})
    },
    populateAvailableCampaigns () {
      let filter = this.webFormCommunication.leadType === 0 ? null : { accountId: this.accountId, leadType: this.webFormCommunication.leadType }
      CommunicationService.getAvailableCampaigns(this.webFormCommunication.campaignsType, filter).then(res => {
        this.availableCampaigns = res.data
      }).catch(ex => {
        this.$toaster.error('Cannot get available campaigns')
        this.$logger.handleError(ex, `cannot get available campaigns for campaign type: ${this.webFormCommunication.campaignsType}`, filter)
      })
    },
    populateCommunicationModelById () {
      CommunicationService.getCommunicationSettings(this.accountId, this.communicationId).then(res => {
        this.availableDepartmentTypes = res.data.availableDepartmentTypes
        this.webFormCommunication = res.data.communication
        this.legacyCampaignIds = res.data.availableLegacyCampaignIds && res.data.availableLegacyCampaignIds.length > 0 ? res.data.availableLegacyCampaignIds : [res.data.communication.legacyCampaignId]
        this.selectedCampaigns = this.webFormCommunication.campaignTypes
      }).catch(ex => {
        this.$toaster.exception(ex, `Cannot get Communication Details Model`, {timeout: 5000})
        this.$logger.handleError(ex, `Cannot get Communication Details Model with id:${this.communicationId} for accountId:${this.accountId}`)
        this.isException = true
      }).finally(() => {
        this.isLoading = false
      })
    },
    populateCommunicationPrototypeModel () {
      CommunicationService.getCommunicationPrototype(this.accountId, communicationTypes.webForm.value).then(res => {
        this.webFormCommunication = res.data.communication
        this.availableCampaigns = res.data.availableCampaigns
      }).catch(ex => {
        this.$toaster.error(`Cannot get Communication Details Model`)
        this.$logger.handleError(ex, `Cannot get Communication Details Model with id:${this.communicationId} for accountId:${this.accountId}`)
        this.isException = true
      }).finally(() => {
        this.isLoading = false
      })
    },
    populateAvailableDepartmentTypes () {
      if (this.webFormCommunication.campaignsType === this.campaignTypes.offSiteParent.value && !this.isEditMode) {
        let campaigns = this.selectedCampaigns.map(x => x.campaignTypeId).toString()
        if (campaigns && campaigns.length > 0) {
          let filter = {
            communicationType: communicationTypes.webForm.value,
            campaignTypeIds: campaigns
          }
          CommunicationService.getAvailableDepartmentTypes(this.accountId, filter).then(res => {
            this.availableDepartmentTypes = res.data
          }).catch(ex => {
            this.availableDepartmentTypes = []
            this.$toaster.error('Cannot get available department types')
            this.$logger.handleError(ex, 'Cannot get available department types')
          })
        } else {
          this.availableDepartmentTypes = []
          this.webFormCommunication.departmentType = null
        }
      }
    },
    setAvailableLegacyCampaignIds () {
      if (!this.isEditMode) {
        let campaigns = this.selectedCampaigns.map(x => x.campaignTypeId).toString()
        if (campaigns && campaigns.length > 0) {
          CommunicationService.getLegacyCampaignIds(campaigns).then(res => {
            this.legacyCampaignIds = res.data
          }).catch(ex => {
            this.legacyCampaignIds = []
            this.$toaster.error('Cannot get available legacy campaign ids')
            this.$logger.handleError(ex, 'Cannot get available legacy campaign ids')
          })
        } else {
          this.legacyCampaignIds = []
          this.webFormCommunication.legacyCampaignId = null
        }
      }
    },
    onSubmit () {
      this.isSubmitButtonDisabled = true
      if (this.validate()) {
        if (this.isEditMode) {
          this.updateCommunication()
        } else {
          this.createNewCommunication()
        }
      } else {
        this.isSubmitButtonDisabled = false
      }
    },
    updateCommunication () {
      CommunicationService.updateCommunication(this.accountId, this.webFormCommunication).then(res => {
        this.$toaster.success('Web Form Campaign Successfully Updated')
      }).catch(ex => {
        this.$toaster.exception(ex, `Cannot updated Web Form Campaign.`, {timeout: 5000})
        this.$logger.handleError(ex, 'Cannot updated Web Form Campaign', this.webFormCommunicationModel)
      }).finally(() => {
        this.finalize()
      })
    },
    createNewCommunication () {
      this.webFormCommunication.campaignTypes = this.selectedCampaigns
      CommunicationService.createNewCommunication(this.accountId, this.webFormCommunication).then(res => {
        this.$toaster.success('New Web Form Campaign Successfully Created')
      }).catch(ex => {
        this.$toaster.exception(ex, `Cannot create Web Form Campaign`, {timeout: 5000})
        this.$logger.handleError(ex, 'Cannot create new Web Form Campaign', this.webFormCommunicationModel)
      }).finally(() => {
        this.finalize()
      })
    },
    validate () {
      if (this.selectedCampaigns.length === 0) {
        this.$toaster.error('Campaigns field cannot be empty')
        return false
      }
      if (!this.webFormCommunication.legacyCampaignId || this.webFormCommunication.legacyCampaignId === '') {
        this.$toaster.error('Legacy Campaign Id field cannot be empty')
        return false
      }
      if (this.webFormCommunication.notificationSettings.hasToSendDealerEmail &&
          (!this.webFormCommunication.notificationSettings.dealerEmailAddresses ||
          this.webFormCommunication.notificationSettings.dealerEmailAddresses.length === 0)) {
        this.$toaster.error('Dealership Emails (To) field cannot be empty')
        return false
      }
      if (this.webFormCommunication.notificationSettings.hasToSendAdfEmail &&
          (!this.webFormCommunication.notificationSettings.adfEmailAddresses ||
          this.webFormCommunication.notificationSettings.adfEmailAddresses.length === 0)) {
        this.$toaster.error('ADF Emails (To) field cannot be empty')
        return false
      }
      if (this.webFormCommunication.campaignsType === this.campaignTypes.offSiteParent.value && !this.webFormCommunication.departmentType) {
        this.$toaster.error('Departments: field cannot be empty')
        return false
      }

      return true
    }
  },
  watch: {
    'selectedCampaigns': {
      deep: true,
      handler: function () {
        if (!this.webFormCommunication) {
          return
        }
        this.setAvailableLegacyCampaignIds()
        this.populateAvailableDepartmentTypes()
      }
    }
  }
}
</script>

<style>
.custom-btn-height {
  height: 35px;
}

.multiselect__option--group {
  font-size: 12px !important;
  padding-top: 12px !important;
  padding-left: 4px !important;
}
</style>
