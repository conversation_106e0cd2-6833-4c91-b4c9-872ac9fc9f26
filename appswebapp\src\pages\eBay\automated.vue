<template>
  <div v-if="!isLoading">
    <h4>Automated eBay</h4>
    <b-card v-if="automatedSettings">
      <activateSection :settings="automatedSettings" @refresh="populateAutomatedSettings"/>
      <classifiedListingsSection :settings="automatedSettings.ClassifiedListingSettings" @refresh="populateAutomatedSettings"/>
      <nationalListingPreferencesSection :settings="automatedSettings.NationalListingPreferenceSettings" @refresh="populateAutomatedSettings"/>
      <auctionListingsSection @calculateListingQuantity="calculateAuctionListingQuantity"  @update="updateAuctionSettings" :settings="automatedSettings.AuctionListingSettings" @refresh="populateAutomatedSettings"/>
      <fixedPriceListingsSection @calculateListingQuantity="calculateFixedListingQuantity" @update="updateFixedPriceSettings" :settings="automatedSettings.FixedPriceListingSettings" @refresh="populateAutomatedSettings"/>
      <depositSection :settings="automatedSettings.DepositSettings" @refresh="populateAutomatedSettings"/>
      <paymentSection :settings="automatedSettings.PaymentSettings" @refresh="populateAutomatedSettings"/>
    </b-card>
  </div>
  <div v-else>
    <loader size="lg"/>
  </div>
</template>

<script>
import activateSection from '@/components/eBay/automated/activateSection'
import classifiedListingsSection from '@/components/eBay/automated/classifiedListingsSection'
import nationalListingPreferencesSection from '@/components/eBay/automated/nationalListingPreferencesSection'
import auctionListingsSection from '@/components/eBay/automated/auctionListingsSection'
import fixedPriceListingsSection from '@/components/eBay/automated/fixedPriceListingsSection'
import depositSection from '@/components/eBay/automated/depositSection'
import paymentSection from '@/components/eBay/automated/paymentSection'
import loader from '@/components/_shared/loader'

export default {
  name: 'ebay-automated',
  metaInfo: {
    title: 'eBay Automated'
  },
  props: {
    accountId: { type: Number, required: true }
  },
  data () {
    return {
      automatedSettings: null,
      isLoading: true
    }
  },
  components: {
    activateSection,
    classifiedListingsSection,
    nationalListingPreferencesSection,
    auctionListingsSection,
    fixedPriceListingsSection,
    depositSection,
    paymentSection,
    loader
  },
  created () {
    this.populateAutomatedSettings()
  },
  methods: {
    populateAutomatedSettings () {
      this.$store.dispatch('eBay/getAutomatedSettings', this.accountId).then(res => {
        this.automatedSettings = res.data
      }).catch(ex => {
        this.$toaster.error('Something went wrong! Please try again later')
        this.$logger.handleError(ex, 'Cannot get eBay automated settings')
      }).finally(() => {
        this.isLoading = false
      })
    },
    calculateAuctionListingQuantity (auctionQuantity) {
      let quantity = 0
      if (+auctionQuantity > 100 || auctionQuantity < 0) {
        this.$set(this.automatedSettings.AuctionListingSettings, 'ListingQuantityInPercent', 0)
        quantity = 100 - this.automatedSettings.FixedPriceListingSettings.ListingQuantityInPercent
      } else if (auctionQuantity + this.automatedSettings.FixedPriceListingSettings.ListingQuantityInPercent > 100) {
        this.$set(this.automatedSettings.AuctionListingSettings, 'ListingQuantityInPercent', 0)
        quantity = 100 - this.automatedSettings.FixedPriceListingSettings.ListingQuantityInPercent
      } else {
        quantity = 100 - (auctionQuantity + this.automatedSettings.FixedPriceListingSettings.ListingQuantityInPercent)
        this.$set(this.automatedSettings.AuctionListingSettings, 'ListingQuantityInPercent', auctionQuantity)
      }

      this.$set(this.automatedSettings.ClassifiedListingSettings, 'ListingPercentageClassified', quantity)
    },
    calculateFixedListingQuantity (fixedQuantity) {
      let quantity = 0
      if (fixedQuantity > 100 || fixedQuantity < 0) {
        this.$set(this.automatedSettings.FixedPriceListingSettings, 'ListingQuantityInPercent', 0)
        quantity = 100 - this.automatedSettings.AuctionListingSettings.ListingQuantityInPercent
      } else if (fixedQuantity + this.automatedSettings.AuctionListingSettings.ListingQuantityInPercent > 100) {
        this.$set(this.automatedSettings.AuctionListingSettings, 'ListingQuantityInPercent', 0)
        this.$set(this.automatedSettings.FixedPriceListingSettings, 'ListingQuantityInPercent', fixedQuantity)
        quantity = 100 - fixedQuantity
      } else {
        quantity = 100 - (fixedQuantity + this.automatedSettings.AuctionListingSettings.ListingQuantityInPercent)
        this.$set(this.automatedSettings.FixedPriceListingSettings, 'ListingQuantityInPercent', fixedQuantity)
      }

      this.$set(this.automatedSettings.ClassifiedListingSettings, 'ListingPercentageClassified', quantity)
    },
    updateFixedPriceSettings (settings) {
      this.$set(settings, 'AuctionListingQuantityInPercent', this.automatedSettings.AuctionListingSettings.ListingQuantityInPercent)
      this.$set(settings, 'ListingPercentageClassified', this.automatedSettings.ClassifiedListingSettings.ListingPercentageClassified)
      this.$store.dispatch('eBay/updateAutomatedFixedPriceListingsSettings', { accountId: this.accountId, data: settings }).then(res => {
        this.$toaster.success('Fixed Price Listing Settings Updated Successfully')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
        if (ex.response && ex.response.status !== 400) {
          this.$logger.handleError(ex, 'Cannot update eBay automated fixed price listing settings', this.settingsToUpdate)
        }
      }).finally(() => {
        this.populateAutomatedSettings()
      })
    },
    updateAuctionSettings (settings) {
      this.$set(settings, 'FixedListingQuantityInPercent', this.automatedSettings.FixedPriceListingSettings.ListingQuantityInPercent)
      this.$set(settings, 'ListingPercentageClassified', this.automatedSettings.ClassifiedListingSettings.ListingPercentageClassified)
      this.$store.dispatch('eBay/updateAutomatedAuctionListingSettings', { accountId: this.accountId, data: settings }).then(res => {
        this.$toaster.success('Auction Listings Settings Updated Successfully')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
        if (ex.response && ex.response.status !== 400) {
          this.$logger.handleError(ex, 'Cannot update eBay automated auction listings settings', settings)
        }
      }).finally(() => {
        this.populateAutomatedSettings()
      })
    }
  }
}
</script>
