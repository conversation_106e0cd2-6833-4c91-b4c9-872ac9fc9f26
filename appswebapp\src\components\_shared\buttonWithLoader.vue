<template>
  <b-button :size="size" :variant="variant" @click="onClick" :disabled="loading">
    <font-awesome-icon v-show="loading" icon="spinner" spin/>
    <slot>
    </slot>
  </b-button>
</template>

<script>
export default {
  name: 'button-with-loader',
  props: {
    variant: String,
    size: {
      type: String,
      required: false,
      validator: function (value) {
        return ['sm', 'md', '', 'lg'].indexOf(value) !== -1
      }
    },
    loading: Boolean
  },
  methods: {
    onClick () {
      this.$emit('click')
    }
  }
}
</script>
