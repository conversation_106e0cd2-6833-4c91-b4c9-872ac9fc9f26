const statisticTypes = [
  { key: '', label: 'All' },
  { key: 1, label: 'Phone price task' },
  { key: 2, label: 'SMS price task' },
  { key: 3, label: 'Call price task' },
  { key: 4, label: 'User info price task' },
  { key: 5, label: 'Recording price task' },
  { key: 6, label: 'Rebuild price task' },
  { key: 7, label: 'Email task' },
  { key: 8, label: 'Web form task' },
  { key: 9, label: 'EBay Task' },
  { key: 10, label: 'User Auto Notification Task' }
]

const communicationTypes = [
  { key: '', label: 'All' },
  { key: 2, label: 'Message' },
  { key: 3, label: 'Voice' },
  { key: 4, label: 'Email' },
  { key: 5, label: 'Gallery' },
  { key: 6, label: 'EBay' }
]

const synchronizationOperationTypes = [
  { key: '', label: 'All' },
  { key: 1, label: 'Communication insert' },
  { key: 2, label: 'Communication update' },
  { key: 3, label: 'Communication delete' },
  { key: 4, label: 'Campaign type insert' },
  { key: 5, label: 'Campaign type delete' },
  { key: 6, label: 'Conversation details notification upsert' },
  { key: 7, label: 'Conversation upsert' },
  { key: 8, label: 'Conversation delete' },
  { key: 9, label: 'Conversation details upsert' },
  { key: 10, label: 'Communication statistic upsert' },
  { key: 11, label: 'Account statistic upsert' },
  { key: 12, label: 'Campaign type update' },
  { key: 13, label: 'Phone History Upsert' },
  { key: 14, label: 'CreditApp Notification upsert' },
  { key: 15, label: 'Conversation details delete' },
  { key: 16, label: 'User email template create' },
  { key: 17, label: 'User email template update' },
  { key: 18, label: 'User email template delete' },
  { key: 19, label: 'Account lead settings update' }
]

const logsSortType = Object.freeze({
  dateAsc: 1,
  dateDesc: 2,
  sourceIpAsc: 3,
  sourceIpDesc: 4
})

const leadsNotificationStatus = [
  { key: '', label: 'All' },
  { key: 0, label: 'Waiting' },
  { key: 1, label: 'In Queue' },
  { key: 2, label: 'In Processing' },
  { key: 3, label: 'Completed' },
  { key: 4, label: 'Failed' },
  { key: 5, label: 'Canceled' }
]

const bodyScanTypes = [
  { key: '', label: 'All leads' },
  { key: 1, label: 'Contains URL in lead body' },
  { key: 2, label: 'Contains email in lead body' }
]

const spamScanStatus = {
  undefined: 0,
  caught: 1,
  passed: 2
}

const spamScanStatusTypes = [
  { key: '', label: 'All spam statuses' },
  { key: spamScanStatus.undefined, label: 'Not checked yet' },
  { key: spamScanStatus.caught, label: 'Caught in spam filter' },
  { key: spamScanStatus.passed, label: 'Passed spam filter' }
]

const turnstileValidationStatus = {
  validationDisabled: 0,
  successful: 1,
  failed: 2,
  internalError: 3,
  bypassedByHeaders: 4
}

const turnstileValidationStatusTypes = [
  { key: '', label: 'All Turnstile statuses' },
  { key: turnstileValidationStatus.validationDisabled, label: 'Validation disabled' },
  { key: turnstileValidationStatus.successful, label: 'Successful' },
  { key: turnstileValidationStatus.failed, label: 'Failed' },
  { key: turnstileValidationStatus.internalError, label: 'Internal error' },
  { key: turnstileValidationStatus.bypassedByHeaders, label: 'Bypassed by headers' }
]

export {
  statisticTypes,
  communicationTypes,
  synchronizationOperationTypes,
  logsSortType,
  leadsNotificationStatus,
  bodyScanTypes,
  spamScanStatus,
  spamScanStatusTypes,
  turnstileValidationStatus,
  turnstileValidationStatusTypes
}
