<template>
  <b-row class="p-3">
    <b-col cols="4" class="my-1">
      <b-input-group class="m-0">
        <b-form-input v-model="filter.search" placeholder="Search Name, Email, Phone">
        </b-form-input>
        <b-input-group-append>
          <b-btn variant="primary" @click="applySearch">Submit</b-btn>
        </b-input-group-append>
      </b-input-group>
    </b-col>
    <b-col cols="4" class="my-1">
      <multiSelectWithCheckboxes
        v-model='selectedCampaigns'
        :options='filterOptions.campaignTypes'
        name="Campaigns"
        customMessageOfNoneSelectedItems="Displayed All Campaigns"
        labelField="campaignTypeName"
        valueField="campaignTypeId"
        @change="selectedCampaignsChange"
      ></multiSelectWithCheckboxes>
    </b-col>
    <b-col cols="4" class="my-1">
      <multiSelectWithCheckboxes
        v-model='selectedCommunicationTypes'
        name="Lead Types"
        customMessageOfNoneSelectedItems="Displayed All Lead Types"
        :options="communicationTypeOptions"
        labelField="label"
        valueField="value"
        @change="selectedCommunicationTypesChange"
      >
      </multiSelectWithCheckboxes>
    </b-col>
    <b-col cols="4" class="my-1">
      <multiSelectWithCheckboxes
        v-model='selectedLeadTypes'
        name="Form Types"
        customMessageOfNoneSelectedItems="Displayed All Form Types"
        :options='filterOptions.leadTypes'
        valueField="leadTypeId"
        labelField="leadTypeName"
        @change="selectedLeadTypesChange"
      >
      </multiSelectWithCheckboxes>
    </b-col>
    <b-col cols="4" class="my-1">
      <multiSelectWithCheckboxes
        v-model='selectedDepartmentTypes'
        name="Departments"
        customMessageOfNoneSelectedItems="Displayed All Departments"
        :options="contactTypes"
        labelField="label"
        valueField="value"
        @change="selectedDepartmentTypesChange"
      >
      </multiSelectWithCheckboxes>
    </b-col>
    <b-col cols="4" v-if="hasAccountsLeadsToDisplay" class="my-1">
      <multiSelectWithCheckboxes
        v-model='selectedAccountIds'
        :options="getAccountIdsOptions"
        name="Accounts"
        @change="selectedAccountIdsChange"
        customMessageOfNoneSelectedItems="Displayed All Accounts"
      >
      </multiSelectWithCheckboxes>
    </b-col>
  </b-row>
</template>

<script>
import multiSelectWithCheckboxes from '../../_shared/multiSelectWithCheckboxes.vue'
import { communicationTypes, contactTypes } from '@/shared/leads/common'
import globals from '@/globals'
import { mapGetters } from 'vuex'

export default {
  name: 'leads-manager-filter',
  props: {
    hasAccountsLeadsToDisplay: Boolean,
    accessibleLeadAccounts: { type: Array, default: () => [] },
    filteredAccountIds: Array,
    filterOptions: { type: Object, required: true },
    filter: { type: Object, required: true }
  },
  data () {
    return {
      contactTypes,
      clonedFilter: {},
      selectedAccountIds: [],
      selectedCampaigns: [],
      selectedLeadTypes: [],
      selectedCommunicationTypes: [],
      selectedDepartmentTypes: []
    }
  },
  mounted () {
    this.clonedFilter = globals().getClonedValue(this.filter)
    this.initData()
  },
  components: {
    multiSelectWithCheckboxes
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {getAllowedAccountIdsToDisplayLeads: () => []}
    },
    getAccountIdsOptions () {
      let options = []
      let userAccount = this.accessibleLeadAccounts.find(x => x.accountId === this.user.accountId)
      if (userAccount) {
        options.push({
          value: userAccount.accountId,
          text: `${userAccount.accountName || ''}(${userAccount.accountId})`
        })
      }
      this.accessibleLeadAccounts.filter(x => x.accountId !== this.user.accountId)
        .map(x => { options.push({value: x.accountId, text: `${x.accountName || ''}(${x.accountId})`}) })
      return options
    },
    communicationTypeOptions () {
      let options = []
      Object.values(communicationTypes).forEach(x => {
        if (x.value !== 0) {
          options.push(x)
        }
      })

      return options
    }
  },
  methods: {
    initData () {
      this.selectedAccountIds = globals().getClonedValue(this.filteredAccountIds)
      this.selectedCommunicationTypes = this.getIntegerArrayFromString(this.clonedFilter.communicationtypes)
      this.selectedLeadTypes = this.getIntegerArrayFromString(this.clonedFilter.leadtypes)
      this.selectedCampaigns = this.getIntegerArrayFromString(this.clonedFilter.campaigntypes)
      this.selectedDepartmentTypes = this.getIntegerArrayFromString(this.clonedFilter.contacttypes)
    },
    applySearch () {
      this.$emit('applySearch', this.filter.search)
    },
    selectedDepartmentTypesChange () {
      if (this.selectedDepartmentTypes.length > 0) {
        this.clonedFilter.contacttypes = this.selectedDepartmentTypes.toString()
      } else {
        this.clonedFilter.contacttypes = ''
      }
      this.$emit('filterChange', this.clonedFilter)
    },
    selectedLeadTypesChange () {
      if (this.selectedLeadTypes.length > 0) {
        this.clonedFilter.leadtypes = this.selectedLeadTypes.toString()
      } else {
        this.clonedFilter.leadtypes = ''
      }
      this.$emit('filterChange', this.clonedFilter)
    },
    selectedCampaignsChange () {
      if (this.selectedCampaigns.length > 0) {
        this.clonedFilter.campaigntypes = this.selectedCampaigns.toString()
      } else {
        this.clonedFilter.campaigntypes = ''
      }
      this.$emit('filterChange', this.clonedFilter)
    },
    selectedCommunicationTypesChange () {
      if (this.selectedCommunicationTypes.length > 0) {
        this.clonedFilter.communicationtypes = this.selectedCommunicationTypes.toString()
      } else {
        this.clonedFilter.communicationtypes = ''
      }
      this.$emit('filterChange', this.clonedFilter)
    },
    selectedAccountIdsChange () {
      this.$emit('changeSelectedAccounts', this.selectedAccountIds)
    },
    getIntegerArrayFromString (str) {
      if (!str || !str.trim()) {
        return []
      }
      let array = []
      for (let numberStr of str.split(',')) {
        let number = parseInt(numberStr)
        if (!isNaN(number)) {
          array.push(number)
        }
      }
      return array
    }
  }
}
</script>
