<template>
  <div>
    <b-row>
      <b-col>
        <h4>SiteBoxes</h4>
      </b-col>
      <b-col>
        <b-btn variant="primary btn-round" class="float-right" size="sm" @click="addSiteBox"><span class="ion ion-ios-add"></span><span class="d-none d-sm-inline">&nbsp; Add New SiteBox</span></b-btn>
      </b-col>
    </b-row>
    <b-card no-body v-if="!isLoading && items && items.length > 0">
      <b-table
        :items="items"
        :fields="getTableFields"
        hover
        responsive
        striped
      >
        <template #cell(manage)="data">
          <b-dropdown variant="outline-secondary icon-btn btn-round" size="sm" right no-caret boundary='viewport'>
            <template slot="button-content">
              <i class="ion ion-ios-more"></i><span class="sr-only">Manage</span>
            </template>
            <b-dropdown-item @click="onViewSites(data.item)">View Sites</b-dropdown-item>
            <b-dropdown-item @click="onEdit(data.item)">Edit</b-dropdown-item>
            <b-dropdown-item @click="onDelete(data.item.id)">Delete</b-dropdown-item>
          </b-dropdown>
        </template>
      </b-table>
    </b-card>
    <div v-else-if="isLoading" class="py-3 my-3">
      <loader size="lg"/>
    </div>
    <div v-else>
      <span class="text-muted">Not Found</span>
    </div>
    <b-modal
      size="lg"
      :title="isEditMode ? 'Edit SiteBox' : 'Add New SiteBox'"
      :visible="isVisibleModal"
      @hide="onHideModal"
    >
      <ValidationObserver ref="validator">
        <ValidationProvider name="Name" rules="required" v-slot="{errors}">
        <detail-row :title-position="'start'" :large-payload-width="true" :error="errors[0]">
          <span slot="title">Name:</span>
          <b-form-input slot="payload" name="Name" v-model="itemModal.name" placeholder="Name for new SiteBox"></b-form-input>
        </detail-row>
        </ValidationProvider>
        <detail-row :large-payload-width="true">
          <span slot="title">Is Default:</span>
          <b-form-checkbox slot="payload" v-model="itemModal.isDefault"></b-form-checkbox>
        </detail-row>
        <detail-row :large-payload-width="true">
          <span slot="title">Is Secure:</span>
          <b-form-checkbox slot="payload" v-model="itemModal.isSecure"></b-form-checkbox>
        </detail-row>
        <ValidationProvider name="url" rules="required" v-slot="{errors}">
          <detail-row :large-payload-width="true" :title-position="'start'" :error="errors[0]">
            <span slot="title">Aws Gallery Internal Url:</span>
            <b-input-group slot="payload">
              <b-input-group-prepend>
                <b-dropdown :text="itemModal.awsGalleryInternalUrlProtocol" variant="outline-secondary">
                  <b-dropdown-item v-for="urlProtocol in getUrlProtocolOptions" :key="urlProtocol" @click="itemModal.awsGalleryInternalUrlProtocol = urlProtocol">
                    {{urlProtocol}}
                  </b-dropdown-item>
                </b-dropdown>
              </b-input-group-prepend>
              <b-form-input v-model="itemModal.awsGalleryInternalUrl"></b-form-input>
            </b-input-group>
          </detail-row>
        </ValidationProvider>
        <detail-row :large-payload-width="true">
          <span slot="title">Foundation Settings Connection:</span>
          <b-form-select slot="payload" v-model="itemModal.mainDropboxConnections.foundationSettingsConnectionId" :options="getDbConnectionOptions"></b-form-select>
        </detail-row>
        <detail-row :large-payload-width="true">
          <span slot="title">Gallery Settings Connection:</span>
          <b-form-select slot="payload" v-model="itemModal.mainDropboxConnections.gallerySettingsConnectionId" :options="getDbConnectionOptions"></b-form-select>
        </detail-row>
        <detail-row :large-payload-width="true">
          <span slot="title">Vehicle Data System Connection:</span>
          <b-form-select slot="payload" v-model="itemModal.mainDropboxConnections.vehicleDataSystemConnectionId" :options="getDbConnectionOptions"></b-form-select>
        </detail-row>
        <detail-row :large-payload-width="true">
          <span slot="title">Vehicle Listing Connection:</span>
          <b-form-select slot="payload" v-model="itemModal.mainDropboxConnections.vehicleListingConnectionId" :options="getDbConnectionOptions"></b-form-select>
        </detail-row>
        <detail-row :large-payload-width="true">
          <span slot="title">Export Settings Connection:</span>
          <b-form-select slot="payload" v-model="itemModal.mainDropboxConnections.exportSettingsConnectionId" :options="getDbConnectionOptions"></b-form-select>
        </detail-row>
        <ValidationProvider name="Gallery Cache Cleaner Url" :rules="{url: true, required: true}" v-slot="{errors}">
        <detail-row :title-position="'start'" :large-payload-width="true" :error="errors[0]">
          <span slot="title">Gallery cache cleaner url:</span>
          <b-form-input slot="payload" name="Gallery_Cache_Cleaner_Url" v-model="itemModal.cacheCleanerUrl"></b-form-input>
        </detail-row>
        </ValidationProvider>
        <ValidationProvider name="Web Forms Cache Cleaner Url" :rules="{url: true, required: true}" v-slot="{errors}">
        <detail-row :title-position="'start'" :large-payload-width="true" :error="errors[0]">
          <span slot="title">Web Forms cache cleaner url:</span>
          <b-form-input slot="payload" name="Web_Forms_Cache_Cleaner_Url" v-model="itemModal.leadsWebFormsCacheCleanerUrl"></b-form-input>
        </detail-row>
        </ValidationProvider>
      </ValidationObserver>
      <template #modal-footer>
        <b-btn size="sm" @click="onHideModal">Cancel</b-btn>
        <b-btn size="sm" variant="primary" @click="onSubmit">Submit</b-btn>
      </template>
    </b-modal>

    <sitesViewModal v-if="sitesViewSiteBox" :siteBox="sitesViewSiteBox" :isVisibleModal="isSitesViewModalVisible" @hide="onHideSitesViewModal"/>
  </div>
</template>

<script>
import loader from '@/components/_shared/loader'
import detailRow from '@/components/details/helpers/detailRow'
import globals from '../../globals'
import sitesViewModal from '@/components/siteBoxManager/sitesViewModal'

export default {
  metaInfo: {
    title: 'SiteBoxes'
  },
  data () {
    return {
      items: [],
      isSitesViewModalVisible: false,
      sitesViewSiteBox: null,
      isVisibleModal: false,
      itemModal: {mainDropboxConnections: {}},
      isEditMode: false,
      isLoading: true,
      connections: []
    }
  },
  components: {
    loader,
    detailRow,
    sitesViewModal
  },
  computed: {
    getTableFields () {
      return [
        {
          key: 'id',
          label: 'Id',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'name',
          label: 'Name',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'sitesCount',
          label: 'Sites',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    getDbConnectionOptions () {
      return this.connections.map(x => ({
        value: x.id,
        text: x.name
      }))
    },
    getUrlProtocolOptions () {
      return ['http://', 'https://']
    }
  },
  created () {
    this.populateData()
    this.populateConnections()
  },
  methods: {
    populateData () {
      this.$store.dispatch('siteBoxManager/getSiteBoxes').then(res => {
        this.items = res.data
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
      }).finally(() => {
        this.isLoading = false
      })
    },
    populateConnections () {
      this.$store.dispatch('siteBoxManager/getConnections').then(res => {
        this.connections = res.data.items
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
      })
    },
    onHideModal () {
      this.isVisibleModal = false
      this.itemModal = {mainDropboxConnections: {}}
    },
    addSiteBox () {
      this.itemModal = {
        awsGalleryInternalUrlProtocol: 'http://',
        mainDropboxConnections: {
          foundationSettingsConnectionId: (this.getDbConnectionOptions[0] || {value: 0}).value,
          gallerySettingsConnectionId: (this.getDbConnectionOptions[0] || {value: 0}).value,
          vehicleDataSystemConnectionId: (this.getDbConnectionOptions[0] || {value: 0}).value,
          vehicleListingConnectionId: (this.getDbConnectionOptions[0] || {value: 0}).value,
          exportSettingsConnectionId: (this.getDbConnectionOptions[0] || {value: 0}).value
        }
      }
      this.isEditMode = false
      this.isVisibleModal = true
    },
    onSubmit () {
      this.$refs.validator.validate().then(isValid => {
        if (isValid) {
          if (this.isEditMode) {
            this.updateSiteBox()
          } else {
            this.createSiteBox()
          }
        }
      })
    },
    createSiteBox () {
      this.$store.dispatch('siteBoxManager/createSiteBox', {data: this.itemModal}).then(res => {
        this.$toaster.success('Created SiteBox Successfully')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
      }).finally(() => {
        this.onHideModal()
        this.populateData()
      })
    },
    updateSiteBox () {
      this.$store.dispatch('siteBoxManager/updateSiteBox', {data: this.itemModal}).then(res => {
        this.$toaster.success('Updated SiteBox Successfully')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
      }).finally(() => {
        this.onHideModal()
        this.populateData()
      })
    },
    onDelete (id) {
      this.$store.dispatch('siteBoxManager/deleteSiteBox', id).then(res => {
        this.$toaster.success('Deleted SiteBox Successfully')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
      }).finally(() => {
        this.populateData()
      })
    },
    onEdit (item) {
      this.itemModal = globals().getClonedValue(item)
      this.isEditMode = true
      this.isVisibleModal = true
    },
    onViewSites (item) {
      this.sitesViewSiteBox = globals().getClonedValue(item)
      this.isSitesViewModalVisible = true
    },
    onHideSitesViewModal () {
      this.isSitesViewModalVisible = false
      this.sitesViewSiteBox = null
    }
  }
}
</script>
