<template>
  <div>
    <!-- Filters -->
    <b-form v-on:submit.prevent="onFilterApply">
      <div class="form-row">
        <b-col lg="2 mt-1">
          <b-form-input
            v-model="filter.accountId"
            type="number"
            placeholder="Account Id"
            autocomplete="off"
          ></b-form-input>
        </b-col>

        <b-col lg="2 mt-1">
          <b-form-input v-model="filter.vin" placeholder="Vin" autocomplete="off"></b-form-input>
        </b-col>

        <b-col lg="2 mt-1">
          <b-input-group class="flex-nowrap">
            <b-input-group-prepend is-text>
              <i class="ion ion-md-calendar" slot="prepend"></i>
            </b-input-group-prepend>
            <date-time-picker
              ref="timeFrom"
              v-model="filter.dateFrom"
              :options="filterTimeOptions"
              format="MM/DD/YYYY HH:mm"
              placeholder="Date From"
              className="form-control"
              @change="onTimeFromInputChange"
            />
            <b-input-group-append
              is-text
              v-show="filter.dateFrom"
              @click="filter.dateFrom = null"
            >
              <i class="ion ion-md-close"></i>
            </b-input-group-append>
          </b-input-group>
        </b-col>

        <b-col lg="2 mt-1">
          <b-input-group class="flex-nowrap">
            <b-input-group-prepend is-text>
              <i class="ion ion-md-calendar" slot="prepend"></i>
            </b-input-group-prepend>
            <date-time-picker
              ref="timeTo"
              v-model="filter.dateTo"
              :options="filterTimeOptions"
              format="MM/DD/YYYY HH:mm"
              placeholder="Date To"
              className="form-control"
              @change="onTimeToInputChange"
            />
            <b-input-group-append
              is-text
              v-show="filter.dateTo"
              @click="filter.dateTo = null"
            >
              <i class="ion ion-md-close"></i>
            </b-input-group-append>
          </b-input-group>
        </b-col>

        <b-col lg="4 mt-1">
          <b-btn variant="primary" type="submit">Apply</b-btn>
          <b-btn variant="secondary" type="reset" @click="onFilterClear">Clear</b-btn>
        </b-col>
      </div>
    </b-form>

    <!-- Listing -->
    <div class="row mt-3">

      <div class="col">
        <b-card no-body>
          <!-- / Pagination -->
          <paging
            :totalItems="itemsTotalCount"
            :pageNumber="filter.page"
            :pageSize="filter.pageSize"
            @numberChanged="onPageChanged"
            @changePageSize="onPageSizeChanged" />
          <!-- Table -->
          <div class="table-responsive" v-if="isLoaded && itemsTotalCount > 0">
            <b-table
              striped
              hover
              :items="items"
              :fields="tableFields"
              class="card-table"
            >
              <template #cell(show_details)="row">
                <l-button :loading="row.item.isLoading" size="sm" @click="showDetails(row)" class="text-center">
                  {{ row.detailsShowing ? 'Hide' : 'Show' }} Details
                </l-button>
                <a class="btn btn-outline-secondary btn-sm" :href="row.item.id" target="_blank">
                  <i class="ion ion-md-open"></i>
                </a>
              </template>
              <template #row-details="row">
                  <b-card>
                    <log-node
                      :data="row.item.details"
                      :isExpandedShallow="true"
                      :isExpandedDeep="false"
                    />
                  </b-card>
              </template>
            </b-table>
          </div>
          <div class="ml-4 font-weight-bolder" v-else-if="!isLoaded">
            Loading...
          </div>
          <div class="ml-4 font-weight-bolder" v-else>
            No logs found
          </div>

          <!-- / Pagination -->
          <paging
            :totalItems="itemsTotalCount"
            :pageNumber="filter.page"
            :pageSize="filter.pageSize"
            titled
            pageSizeSelector
            @numberChanged="onPageChanged"
            @changePageSize="onPageSizeChanged" />
        </b-card>
      </div>
    </div>
  </div>
</template>

<script>
import commonConstants from '@/shared/common/constants'
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import InventoryLogService from '@/services/logs/InventoryLogService'

const filterSchema = new ObjectSchema({
  pageSize: { type: Number, default: 25 },
  page: { type: Number, default: 1 },
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  accountId: { type: String, default: '' },
  vin: { type: String, default: '' }
})
const defaultFilter = filterSchema.getObject()
const queryHelper = new QueryStringHelper(filterSchema)

export default {
  name: 'inventory-log-listing',
  metaInfo: {
    title: 'Inventory Log'
  },
  props: {
    clientType: {
      type: Number,
      required: true
    }
  },
  components: {
    'paging': () => import('@/components/_shared/paging.vue'),
    'log-node': () => import('../_shared/logItemNode.vue'),
    'date-time-picker': () => import('@gravitano/vue-date-range-picker/src/components/DateRangePicker')
  },
  data () {
    return {
      isLoaded: false,
      filter: defaultFilter,
      filterTimeOptions: {
        startDate: new Date(),
        singleDatePicker: true,
        timePicker: true,
        timePicker24Hour: true,
        maxDate: new Date()
      },
      itemsTotalCount: 0,
      items: []
    }
  },
  computed: {
    tableFields () {
      return [
        {
          key: 'accountId',
          label: 'AccountId'
        },
        {
          key: 'vin',
          label: 'Vin'
        },
        {
          key: 'userName',
          label: 'UserName'
        },
        {
          key: 'startProcessingDateTime',
          label: 'Date'
        },
        {
          key: 'description',
          label: 'Description'
        },
        {
          key: 'show_details'
        }
      ]
    },
    refDateTimeFrom () {
      return (this.$refs.timeFrom || {}).$el || {}
    },
    refDateTimeTo () {
      return (this.$refs.timeTo || {}).$el || {}
    }
  },
  methods: {
    onPageChanged (newPage) {
      this.filter.page = newPage
      this.synchronizeUrl()
    },
    onPageSizeChanged (newSize) {
      this.filter.pageSize = newSize
      this.filter.page = 1
      this.synchronizeUrl()
    },
    onTimeFromInputChange (newVal) {
      this.refDateTimeFrom.value = newVal || this.filter.dateFrom || null
    },
    onTimeToInputChange (newVal) {
      this.refDateTimeTo.value = newVal || this.filter.dateTo || null
    },
    async onFilterApply () {
      this.filter.page = 1

      await this.synchronizeUrlAndReload()
    },
    onFilterClear () {
      this.filter.accountId = null
      this.filter.vin = null
      this.filter.dateFrom = null
      this.filter.dateTo = null
    },
    synchronizeUrl () {
      queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
    },
    async synchronizeUrlAndReload () {
      const queryBefore = JSON.stringify(this.$route.query)
      this.synchronizeUrl()
      const queryAfter = JSON.stringify(this.$route.query)

      if (queryBefore === queryAfter) {
        await this.loadContent()
      }
    },
    async showDetails (row) {
      if (row.item.details) {
        row.toggleDetails()
      } else {
        this.$set(row.item, 'isLoading', true)

        try {
          const apiResult = await InventoryLogService.getInventoryApiLogDetails(row.item.id)

          row.item.details = {
            nodes: apiResult.data.properties
          }

          row.toggleDetails()
        } catch (err) {
          this.$toaster.error('Failed to get data from server')
          this.$logger.handleError(err, 'Can\'t load log details', row.item)
        } finally {
          this.$set(row.item, 'isLoading', false)
        }
      }
    },
    async loadContent () {
      this.isLoaded = false
      this.itemsTotalCount = 0

      this.filter = queryHelper.parseQueryStringToObject(this.$router)

      const apiFilter = {
        ...this.filter,
        type: this.clientType
      }

      try {
        const apiResult = await InventoryLogService.getInventoryApiLogs(apiFilter)

        if (apiResult.data.items.length > 0) {
          // keep latest result if new page has no items
          if (this.filter.direction === commonConstants.paginationDirectionTypes.toFuture) {
            // reverse items to show from new to old
            this.items = apiResult.data.items.reverse()
          } else {
            this.items = apiResult.data.items
          }
        }

        this.itemsTotalCount = apiResult.data.itemsTotalCount
      } catch (err) {
        if (err.response && err.response.data) {
          this.$toaster.error(err.response.data)
        } else {
          this.$toaster.error('Failed to get data from server')
          this.$logger.handleError(err, 'Can\'t reset UI', this.filter)
        }
      } finally {
        this.isLoaded = true
      }
    }
  },
  mounted: async function () {
    await this.loadContent()

    this.$nextTick(() => {
      this.onTimeFromInputChange()
      this.onTimeToInputChange()

      this.refDateTimeFrom.onchange = () => this.onTimeFromInputChange()
      this.refDateTimeTo.onchange = () => this.onTimeToInputChange()
    })
  },
  watch: {
    '$route' () {
      this.loadContent() // do not wait for response
    }
  }
}
</script>
