const campaignTypes = Object.freeze({
  onSite: { value: 1, text: 'On Site' },
  offSite: { value: 2, text: 'Off Site' },
  offSiteParent: { value: 3, text: 'Off Site Parent' }
})

const campaignTabTypes = Object.freeze({
  onSite: { value: 1, text: 'On Site' },
  offSite: { value: 2, text: 'Off Site' },
  offSiteParent: { value: 3, text: 'Off Site Parent' },
  unmatched: { value: 4, text: 'Unmatched' }
})

const gaChannelGroupTypes = [
  { value: 0, text: 'Other' },
  { value: 1, text: 'Direct' },
  { value: 2, text: 'Display' },
  { value: 3, text: 'Organic Search' },
  { value: 4, text: 'Paid Search' },
  { value: 5, text: 'Referral' },
  { value: 6, text: 'Social' }
]

const detailsPageSubTypes = [
  { value: 1, text: 'Craigslist' },
  { value: 2, text: 'Auction Wide' },
  { value: 3, text: 'Auction Local' },
  { value: 4, text: 'Window Sticker' }
]

export {
  campaignTypes,
  gaChannelGroupTypes,
  detailsPageSubTypes,
  campaignTabTypes
}
