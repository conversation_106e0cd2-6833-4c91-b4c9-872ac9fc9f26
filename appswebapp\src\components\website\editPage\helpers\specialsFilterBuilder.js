import { specialFilterTypes, specialConditionTypes, specialTypes } from '@/shared/website/constants'

class SpecialsFilterBuilder {
  constructor (specialsCategories) {
    this.specialCategories = specialsCategories
  };
  BuildSpecialsFilters (filters, specialsFilters) {
    if (!filters) {
      return
    }
    filters.map(x => {
      this.BuildSpecialsFilterItem(x, specialsFilters)
    })
  };
  BuildSpecialsFilterItem (filter, specialsFilters) {
    switch (filter.filterType) {
      case specialFilterTypes.specialTypes:
        let res = Object.values(specialTypes).find(x => x.apiValue === filter.filterValue)
        if (res) {
          specialsFilters.type = res.value
        }
        return
      case specialFilterTypes.specialCategories:
        specialsFilters.categories = this.BuildSpecialsCategoriesFilters(filter.filterValue)
        return
      case specialFilterTypes.conditions:
        specialsFilters.conditions = this.BuildSpecialsConditionsFilters(filter.filterValue)
        return
      case specialFilterTypes.yearFrom:
        specialsFilters.yearFrom = filter.filterValue
        return
      case specialFilterTypes.yearTo:
        specialsFilters.yearTo = filter.filterValue
        return
      case specialFilterTypes.makes:
        specialsFilters.makes = (filter.filterValue || '').split(',') || []
        return
      case specialFilterTypes.models:
        specialsFilters.models = (filter.filterValue || '').split(',') || []
        return
      case specialFilterTypes.trims:
        specialsFilters.trims = (filter.filterValue || '').split(',') || []
    }
  };
  BuildSpecialsCategoriesFilters (filterValue) {
    let res = []
    let values = (filterValue || '').split(',') || []
    values.map(x => {
      let category = this.specialCategories.find(cat => +cat.specialCategoryId === +x)
      if (category) {
        res.push(category)
      }
    })
    return res
  };
  BuildSpecialsConditionsFilters (filterValue) {
    let res = []
    let values = (filterValue || '').split(',') || []
    values.map(x => {
      let condition = Object.values(specialConditionTypes).find(con => con.value === +x)
      if (condition) {
        res.push(condition)
      }
    })
    return res
  };
  BuildApiSpecialsFilters (specialsFilters) {
    let filters = []
    if (specialsFilters.type > 0) {
      let res = Object.values(specialTypes).find(x => x.value === specialsFilters.type)
      if (res) {
        filters.push({
          filterType: specialFilterTypes.specialTypes,
          filterValue: res.apiValue
        })
      }
    }
    if (specialsFilters.makes && specialsFilters.makes.length > 0) {
      filters.push({
        filterType: specialFilterTypes.makes,
        filterValue: specialsFilters.makes.join()
      })
    }
    if (specialsFilters.models && specialsFilters.models.length > 0) {
      filters.push({
        filterType: specialFilterTypes.models,
        filterValue: specialsFilters.models.join()
      })
    }
    if (specialsFilters.trims && specialsFilters.trims.length > 0) {
      filters.push({
        filterType: specialFilterTypes.trims,
        filterValue: specialsFilters.trims.join()
      })
    }
    if (specialsFilters.conditions && specialsFilters.conditions.length > 0) {
      filters.push({
        filterType: specialFilterTypes.conditions,
        filterValue: specialsFilters.conditions.map(x => x.value).join()
      })
    }
    if (specialsFilters.categories && specialsFilters.categories.length > 0) {
      filters.push({
        filterType: specialFilterTypes.specialCategories,
        filterValue: specialsFilters.categories.map(x => x.specialCategoryId).join()
      })
    }
    if (specialsFilters.yearFrom) {
      filters.push({
        filterType: specialFilterTypes.yearFrom,
        filterValue: specialsFilters.yearFrom
      })
    }
    if (specialsFilters.yearTo) {
      filters.push({
        filterType: specialFilterTypes.yearTo,
        filterValue: specialsFilters.yearTo
      })
    }

    return filters
  }
}

export default SpecialsFilterBuilder
