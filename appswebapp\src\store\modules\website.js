import axios from 'axios'

const actions = {
  getAccountListing (_, parameters) {
    return axios.get('/api/sites/list', { params: parameters.filters })
  },
  getSiteNavigationSettings (_, parameters) {
    return axios.get(`/api/sites/${parameters.accountId}/navigation/settings`)
  },
  getPageNavigationSettings (_, parameters) {
    return axios.get(`/api/sites/${parameters.accountId}/navigation/${parameters.navId}/settings`)
  },
  getSitesBasicInfo (_, parameters) {
    return axios.post(`/api/sites/basicinfo`, parameters.data)
  },
  changePageNavigationVisible (_, parameters) {
    return axios.get(`/api/sites/${parameters.accountId}/navigation/${parameters.navId}/visible/change`, { params: parameters.apiData })
  },
  changeNavigationMenuItemOrdering (_, parameters) {
    return axios.post(`/api/sites/${parameters.accountId}/navigation/${parameters.navId}/ordering/change`, parameters.data)
  },
  updateSiteNavigationSettings (_, parameters) {
    return axios.post(`/api/sites/${parameters.accountId}/navigation/settings/update`, parameters.data)
  },
  updatePageNavigationSettings (_, parameters) {
    return axios.post(`/api/sites/${parameters.accountId}/navigation/${parameters.navId}/settings/update`, parameters.data)
  },
  deleteNavigation (_, parameters) {
    return axios.post(`/api/sites/${parameters.accountId}/navigation/${parameters.navId}/settings/delete`)
  },
  deleteSiteNavigationSettings (_, parameters) {
    return axios.post(`/api/sites/${parameters.accountId}/navigation/settings/delete`)
  },
  getPageSettings (_, parameters) {
    return axios.get(`/api/sites/${parameters.accountId}/page/${parameters.pageId}`)
  },
  getPhotoHost (_, parameters) {
    return axios.get(`/api/sites/${parameters.accountId}/page/photo/host`)
  },
  refreshBlog (_, parameters) {
    return axios.get(`/api/sites/${parameters.accountId}/page/${parameters.pageId}/blog/refresh`)
  },
  isPageNameAvailable (_, parameters) {
    return axios.get(`/api/sites/${parameters.accountId}/page/${parameters.pageId}/pagename/available`, { params: parameters.apiData })
  },
  changePagePublished (_, parameters) {
    return axios.get(`/api/sites/${parameters.accountId}/page/${parameters.pageId}/published/change`, { params: parameters.apiData })
  },
  createNewPage (_, parameters) {
    return axios.post(`/api/sites/${parameters.accountId}/page/create`, parameters.data)
  },
  updatePage (_, parameters) {
    return axios.post(`/api/sites/${parameters.accountId}/page/${parameters.pageId}/update`, parameters.data)
  },
  updatePhotoItem (_, parameters) {
    return axios.post(`/api/sites/${parameters.accountId}/page/${parameters.pageId}/photo_item/update`, parameters.data, { headers: {'Content-Type': 'multipart/form-data'} })
  },
  deletePhotoItem (_, parameters) {
    return axios.post(`/api/sites/${parameters.accountId}/page/${parameters.pageId}/photo_item/${parameters.photoItemId}/delete`)
  },
  enableWebsiteForAccount (_, parameters) {
    return axios.post(`/api/sites/${parameters.accountId}/website/enable`)
  },
  disableWebsiteForAccount (_, parameters) {
    return axios.post(`/api/sites/${parameters.accountId}/website/disable`)
  },
  enablePASForAccount (_, parameters) {
    return axios.post(`/api/sites/${parameters.accountId}/pas/enable`)
  }
}

export default {
  namespaced: true,
  actions: actions
}
