<template>
  <details-section :title="vehicleHistoryMetadata.name" v-model="mode" @cancel="onCancel" v-if="vehicleHistoryMetadata" :canBeHidden="!!vehicleHistoryMetadata.featureCategory" :visible="isVisible" @visibilityChange='onVisibilityChange'>
    <div class="view" v-if="mode === 'view'">
      <b-row class="info-row mt-3">
        <b-col v-for="i in getCheckboxes" :key="i.id" sm="6" md="4" lg="3" xl='2'>
          <b-form-group>
            <b-form-checkbox-group class="p-0" disabled :checked="[i.value]">
              <b-form-checkbox disabled><span style="color:rgb(78, 81, 85);">{{i.name}}</span></b-form-checkbox>
            </b-form-checkbox-group>
          </b-form-group>
        </b-col>
      </b-row>
      <detail-row titlePosition="start">
        <span slot="title">{{getNotes.name}}:</span>
        <span slot="payload">{{getNotes.value}}</span>
      </detail-row>
    </div>

    <div class="edit" v-else-if="mode === 'edit'">
      <b-row class="mt-3">
        <b-col v-for="i in getCheckboxes" :key="i.id" sm="6" md="4" lg="3" xl='2' class="pb-3">
          <b-form-checkbox v-model="i.value">
            {{i.name}}
          </b-form-checkbox>
        </b-col>
      </b-row>
      <ValidationProvider immediate :name="getNotes.name" rules="min:0|xml" v-slot="{errors}">
      <detail-row fixedPayloadWidth editMode titlePosition="start" :error="errors[0]">
        <span slot="title">{{getNotes.name}}:</span>
        <b-form-textarea slot="payload" v-model="getNotes.value"
          :name ="getNotes.name"
          :rows="3"
          :max-rows="6">
        </b-form-textarea>
      </detail-row>
      </ValidationProvider>
    </div>
  </details-section>
</template>

<script>
import { mapGetters } from 'vuex'
import detailRow from '../helpers/detailRow'
import detailsSection from '@/components/details/detailsSection'

export default {
  name: 'vehicle-history-section',
  data () {
    return {
      mode: 'view'
    }
  },
  computed: {
    ...mapGetters('details', ['vehicleHistoryMetadata']),
    isVisible () {
      return this.vehicleHistoryMetadata.featureCategory && this.vehicleHistoryMetadata.featureCategory.isDisplayed
    },
    getNotes () {
      return this.findFeatures(6)[0]
    },
    getCheckboxes () {
      return this.findFeatures(1)
    }
  },
  methods: {
    findFeatures (...valueType) {
      return this.vehicleHistoryMetadata.features
        .filter(x => Array.prototype.concat(valueType).includes(x.valueType))
    },
    onVisibilityChange (val) {
      this.vehicleHistoryMetadata.featureCategory.isDisplayed = val
    },
    onCancel () {
      this.$forceUpdate()
    }
  },
  components: {
    'details-section': detailsSection,
    'detail-row': detailRow
  }
}
</script>
