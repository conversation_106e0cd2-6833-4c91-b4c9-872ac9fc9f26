<template>
  <div>
    <div>
      <h4>User Auth Log Details</h4>
    </div>
    <b-card v-if="isLoaded">
      <log-node
        v-if="logDetails"
        :data="logDetails"
        :isExpandedShallow="true"
        :isExpandedDeep="false"
      />
      <span v-else class="text-muted">No Log Details</span>
    </b-card>
    <loader v-else size="lg"/>
  </div>
</template>

<script>
import SystemToolsService from '@/services/systemTools/SystemToolsService'

export default {
  name: 'system-tools-user-auth-log-details',
  metaInfo: {
    title: 'User Auth Log Details'
  },
  props: {
    logId: { type: String, required: true }
  },
  data () {
    return {
      isLoaded: false,
      logDetails: null
    }
  },
  components: {
    'log-node': () => import('@/components/_shared/logItemNode.vue'),
    'loader': () => import('@/components/_shared/loader.vue')
  },
  created () {
    this.loadContent()
  },
  methods: {
    loadContent () {
      SystemToolsService.getUserAuthorizationLogDetails(this.logId).then(res => {
        this.logDetails = {
          nodes: res.data.details
        }
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
      }).finally(() => {
        this.isLoaded = true
      })
    }
  }
}
</script>
