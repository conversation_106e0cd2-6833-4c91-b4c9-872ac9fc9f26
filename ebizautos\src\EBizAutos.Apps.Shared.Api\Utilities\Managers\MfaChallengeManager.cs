using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using EBizAutos.Apps.Authentication.CommonLib.Abstract.Repositories;
using EBizAutos.CommonLib.Mail;
using EBizAutos.Apps.CommonLib.Abstract.Repositories.AppsUser;
using EBizAutos.Apps.CommonLib.Models.AppsUser;
using static EBizAutos.Apps.CommonLib.Enums.AuthenticationEnums;
using Microsoft.Extensions.DependencyInjection;
using EBizAutos.Apps.Authentication.CommonLib.Models;
using EBizAutos.Apps.CommonLib.Models.Web;
using EBizAutos.Apps.CommonLib.Abstract.TwilioSms;
using static EBizAutos.Apps.CommonLib.Enums.TwilioMessagingEnums;

namespace EBizAutos.Apps.Shared.Api.Utilities.Managers {
	public class MfaChallengeManager : IApiManager {
		private readonly IUserRepository _userRepository;
		private readonly IMfaChallengeRepository _challengeRepository;
		private readonly ILoginSystemSettingsRepository _loginSystemSettingsRepository;
		private readonly MailSender _mailSender;
		private readonly ITwilioSmsClient _eBizTwilioSmsClient;

		public MfaChallengeManager(IServiceProvider serviceProvider) {
			_userRepository = serviceProvider.GetRequiredService<IUserRepository>();
			_challengeRepository = serviceProvider.GetRequiredService<IMfaChallengeRepository>();
			_loginSystemSettingsRepository = serviceProvider.GetRequiredService<ILoginSystemSettingsRepository>();
			_mailSender = serviceProvider.GetService<MailSender>();
			_eBizTwilioSmsClient = serviceProvider.GetRequiredService<ITwilioSmsClient>();
		}

		public async Task<PromiseResultModel<MfaChallengeModel>> CreateChallengeAsync(User user, string applicationName, string deviceId, string deviceModel, string ipAddress) {
			try {
				var loginSettings = await _loginSystemSettingsRepository.GetAsync();

				var challenge = new MfaChallengeModel {
					Id = Guid.NewGuid().ToString("N"),
					UserObjectId = user.Id,
					AccountId = user.AccountId,
					CreatedAtUtc = DateTime.UtcNow,
					AttemptsUsed = 0,
					OtpCodeVerificationAttemptsLimit = loginSettings.Mfa.OtpCodeVerificationAttemptsLimit,
					ApplicationName = applicationName,
					DeviceId = deviceId,
					DeviceModel = deviceModel,
					IpAddress = ipAddress
				};

				await _challengeRepository.InsertAsync(challenge);
				return PromiseResultModel<MfaChallengeModel>.SuccessResult(challenge);
			} catch (Exception ex) {
				return PromiseResultModel<MfaChallengeModel>.ExceptionResult(
					new Exception($"Failed to create MFA challenge for user {user?.Id}.", ex),
					true
				);
			}
		}

		public async Task<PromiseResultModel<bool>> GenerateAndSendCodeAsync(string challengeId, MfaMethodEnum method) {
			try {
				var loginSettings = await _loginSystemSettingsRepository.GetAsync();
				var challenge = await _challengeRepository.GetAsync(challengeId);
				if (challenge == null)
					return PromiseResultModel<bool>.CanceledResult("Invalid or expired challenge.");

				var user = _userRepository.GetUser(challenge.UserObjectId);
				if (user == null)
					return PromiseResultModel<bool>.CanceledResult("User not found.");

				SetChallengeMethodAndDestination(challenge, method, user);

				PromiseResultModel<bool> blockReason;
				if (HasToBlockByValidationResult(challenge, user, out blockReason))
					return blockReason;

				string code = GenerateNumericCode(loginSettings.Mfa.OtpCodeLength);
				(challenge.CodeHash, challenge.CodeSalt) = Hash(code);

				var ttl = TimeSpan.FromMinutes(loginSettings.Mfa.OtpCodeTtlInMinutes);
				UpdateChallengeTiming(challenge, loginSettings, ttl);

				await _challengeRepository.UpdateAsync(challenge);

				return await SendCodeAsync(code, ttl, loginSettings, challenge, user);
			} catch (Exception ex) {
				return PromiseResultModel<bool>.ExceptionResult(
					new Exception("Failed to process MFA challenge request.", ex), true);
			}
		}

		public async Task<PromiseResultModel<(User user, string applicationName, string deviceId, string deviceModel)>> VerifyAsync(string challengeId, string code) {
			try {
				var challenge = await _challengeRepository.GetAsync(challengeId);
				if (challenge == null) {
					return PromiseResultModel<(User, string, string, string)>.CanceledResult("Invalid or expired challenge.");
				}

				if (!challenge.LastCodeExpiresAtUtc.HasValue) {
					return PromiseResultModel<(User, string, string, string)>.CanceledResult("No code has been sent yet. Please request a new one.");
				}

				var expiresUtc = challenge.LastCodeExpiresAtUtc.Value.ToUniversalTime();
				if (expiresUtc <= DateTime.UtcNow) {
					return PromiseResultModel<(User, string, string, string)>.CanceledResult("Previous code expired. Please request a new one.");
				}

				if (challenge.AttemptsUsed >= challenge.OtpCodeVerificationAttemptsLimit) {
					return PromiseResultModel<(User, string, string, string)>.CanceledResult("Too many attempts.");
				}

				challenge.AttemptsUsed++;
				await _challengeRepository.UpdateAsync(challenge);

				if (!Verify(code, challenge.CodeHash, challenge.CodeSalt)) {
					int attemptsLeft = Math.Max(0, challenge.OtpCodeVerificationAttemptsLimit - challenge.AttemptsUsed);
					if (attemptsLeft == 0) {
						return PromiseResultModel<(User, string, string, string)>.CanceledResult("Too many attempts.");
					}
					return PromiseResultModel<(User, string, string, string)>.CanceledResult($"Invalid code. {attemptsLeft} attempts left.");
				}

				challenge.IsVerified = true;
				challenge.VerifiedAtUtc = DateTime.UtcNow;
				await _challengeRepository.UpdateAsync(challenge);

				var user = _userRepository.GetUser(challenge.UserObjectId);
				if (user == null) {
					return PromiseResultModel<(User, string, string, string)>.CanceledResult("User not found.");
				}

				return PromiseResultModel<(User, string, string, string)>.SuccessResult(
					(user, challenge.ApplicationName, challenge.DeviceId, challenge.DeviceModel));
			} catch (Exception ex) {
				return PromiseResultModel<(User, string, string, string)>.ExceptionResult(
					new Exception("Failed to verify MFA code.", ex), true);
			}
		}

		public async Task<PromiseResultModel<MfaChallengeModel>> CreateChallengeAndSendCodeAsync(int contactId, MfaMethodEnum method, string applicationName, string deviceId, string deviceModel, string ipAddress) {
			try {
				var user = _userRepository.GetUserByContactId(contactId);
				if (user == null) {
					return PromiseResultModel<MfaChallengeModel>.CanceledResult("User not found.");
				}

				var challenge = new MfaChallengeModel {
					Id = Guid.NewGuid().ToString("N"),
					UserObjectId = user.Id,
					AccountId = user.AccountId,
					CreatedAtUtc = DateTime.UtcNow,
					AttemptsUsed = 0,
					OtpCodeVerificationAttemptsLimit = 1,
					ApplicationName = applicationName,
					DeviceId = deviceId,
					DeviceModel = deviceModel,
					IpAddress = ipAddress
				};

				var loginSettings = await _loginSystemSettingsRepository.GetAsync();
				string code = GenerateNumericCode(loginSettings.Mfa.OtpCodeLength);
				(challenge.CodeHash, challenge.CodeSalt) = Hash(code);
				var ttl = TimeSpan.FromMinutes(loginSettings.Mfa.OtpCodeTtlInMinutes);

				SetChallengeMethodAndDestination(challenge, method, user);
				UpdateChallengeTiming(challenge, loginSettings, ttl);
				await _challengeRepository.InsertAsync(challenge);

				var sendResult = await SendCodeAsync(code, ttl, loginSettings, challenge, user);
				if (sendResult.IsRejected || !sendResult.Model) {
					return PromiseResultModel<MfaChallengeModel>.CanceledResult(
						$"Failed to send MFA code via {method}."
					);
				}

				return PromiseResultModel<MfaChallengeModel>.SuccessResult(challenge);
			} catch (Exception ex) {
				return PromiseResultModel<MfaChallengeModel>.ExceptionResult(
					new Exception("Failed to create MFA challenge and send code.", ex), true);
			}
		}

		public async Task<PromiseResultModel<bool>> VerifyMfaMethodAsync(string challengeId, string code) {
			try {
				var challenge = await _challengeRepository.GetAsync(challengeId);
				if (challenge == null)
					return PromiseResultModel<bool>.CanceledResult("Invalid or expired challenge.");

				if (challenge.IsVerified)
					return PromiseResultModel<bool>.CanceledResult("Code already verified.");

				if (!challenge.LastCodeExpiresAtUtc.HasValue) {
					return PromiseResultModel<bool>.CanceledResult("No code has been sent yet. Please request a new one.");
				}

				var expiresUtc = challenge.LastCodeExpiresAtUtc.Value.ToUniversalTime();
				if (expiresUtc <= DateTime.UtcNow) {
					return PromiseResultModel<bool>.CanceledResult("The verification code has expired.");
				}

				if (challenge.AttemptsUsed >= challenge.OtpCodeVerificationAttemptsLimit)
					return PromiseResultModel<bool>.CanceledResult("Too many attempts.");

				challenge.AttemptsUsed++;
				await _challengeRepository.UpdateAsync(challenge);

				if (!Verify(code, challenge.CodeHash, challenge.CodeSalt)) {
					return PromiseResultModel<bool>.CanceledResult("Invalid code.");
				}

				challenge.IsVerified = true;
				challenge.VerifiedAtUtc = DateTime.UtcNow;
				await _challengeRepository.UpdateAsync(challenge);

				var user = _userRepository.GetUser(challenge.UserObjectId);
				if (user == null)
					return PromiseResultModel<bool>.CanceledResult("User not found.");

				switch (challenge.Method) {
					case MfaMethodEnum.Email:
						user.PersonalInformation.IsEmailVerified = true;
						break;
					case MfaMethodEnum.Sms:
						user.PersonalInformation.IsMfaPhoneNumberVerified = true;
						break;
				}

				await _userRepository.UpdateUserAsync(user);

				return PromiseResultModel<bool>.SuccessResult(true);
			} catch (Exception ex) {
				return PromiseResultModel<bool>.ExceptionResult(
					new Exception("Failed to verify MFA code.", ex), true);
			}
		}

		public static string MaskEmail(string email) {
			if (string.IsNullOrWhiteSpace(email) || !email.Contains("@")) return "";
			var parts = email.Split('@');
			var name = parts[0];
			if (name.Length <= 2) return $"{name[0]}***@{parts[1]}";
			return $"{name[0]}{new string('*', name.Length - 2)}{name[name.Length - 1]}@{parts[1]}";
		}

		public static string MaskPhone(string phone) {
			if (string.IsNullOrEmpty(phone) || phone.Length < 7) return "";
			string last4 = phone.Substring(phone.Length - 4, 4);
			return new string('*', phone.Length - 4) + last4;
		}

		private bool HasToBlockByValidationResult(MfaChallengeModel challenge, User user, out PromiseResultModel<bool> blockReason) {
			blockReason = null;

			if (challenge.AttemptsUsed >= challenge.OtpCodeVerificationAttemptsLimit) {
				blockReason = PromiseResultModel<bool>.CanceledResult("Challenge has exceeded maximum attempts.");
				return true;
			}

			if (challenge.OtpCodeResendAvailableAtUtc.HasValue && challenge.OtpCodeResendAvailableAtUtc.Value.ToUniversalTime() > DateTime.UtcNow) {
				var timeLeft = challenge.OtpCodeResendAvailableAtUtc.Value.ToUniversalTime() - DateTime.UtcNow;
				var secondsLeft = (int)Math.Ceiling(timeLeft.TotalSeconds);
				blockReason = PromiseResultModel<bool>.CanceledResult(
					$"Resend is not available yet. Try again in {secondsLeft} seconds.");
				return true;
			}

			bool isDestinationVerified = false;
			switch (challenge.Method) {
				case MfaMethodEnum.Email:
					isDestinationVerified =
						!string.IsNullOrEmpty(user.PersonalInformation?.Email) &&
						user.PersonalInformation?.IsEmailVerified == true;
					break;

				case MfaMethodEnum.Sms:
					isDestinationVerified =
						!string.IsNullOrEmpty(user.PersonalInformation?.MfaPhoneNumber) &&
						user.PersonalInformation?.IsMfaPhoneNumberVerified == true;
					break;
			}

			if (!isDestinationVerified) {
				blockReason = PromiseResultModel<bool>.CanceledResult(
					$"MFA destination for {challenge.Method} is not verified.");
			}

			return !isDestinationVerified;
		}

		private void UpdateChallengeTiming(MfaChallengeModel challenge, LoginSystemSettings loginSettings, TimeSpan ttl) {
			challenge.OtpCodeResendAvailableAtUtc = DateTime.UtcNow.AddSeconds(
				loginSettings.Mfa.OtpCodeResendIntervalInSeconds > 0
					? loginSettings.Mfa.OtpCodeResendIntervalInSeconds
					: 60);
			challenge.LastCodeExpiresAtUtc = DateTime.UtcNow.Add(ttl);
		}

		private void SetChallengeMethodAndDestination(MfaChallengeModel challenge, MfaMethodEnum method, User user) {
			challenge.Method = method;

			if (method == MfaMethodEnum.Email) {
				challenge.MaskedDestination = MaskEmail(user.PersonalInformation?.Email ?? string.Empty);
			} else if (method == MfaMethodEnum.Sms) {
				challenge.MaskedDestination = MaskPhone(user.PersonalInformation?.MfaPhoneNumber ?? string.Empty);
			}
		}

		private async Task<PromiseResultModel<bool>> SendCodeAsync(
			string code, TimeSpan ttl, LoginSystemSettings loginSettings, MfaChallengeModel challenge, User user) {
			var method = challenge.Method;
			try {
				if (method == MfaMethodEnum.Email) {
					string emailBody = string.Format(loginSettings.Email.OtpBodyTemplate, code, (int)ttl.TotalMinutes);
					_mailSender?.Send(
						emailFrom: loginSettings.Email.From,
						emailToCollection: new List<string> { user.PersonalInformation.Email },
						emailCcCollection: null,
						subject: loginSettings.Email.OtpSubject,
						mailBody: emailBody
					);
				} else if (method == MfaMethodEnum.Sms) {
					string smsBody = string.Format(loginSettings.Sms.OtpBodyTemplate, code, (int)ttl.TotalMinutes);
					var message = await _eBizTwilioSmsClient.SendMessageAsync(
						loginSettings.Sms.From,
						user.PersonalInformation.MfaPhoneNumber,
						smsBody);

					if (message.ErrorCode != TwilioErrorCodeEnum.Undefined) {
						return PromiseResultModel<bool>.CanceledResult(
							$"MFA code wasn't sent. Error: {message.ErrorCode} - {message.GetTwilioErrorDescription()}, Status: {message.MessageStatus}.");
					}
				} else {
					return PromiseResultModel<bool>.CanceledResult($"MFA method {method} is not supported.");
				}

				return PromiseResultModel<bool>.SuccessResult(true);
			} catch (Exception ex) {
				return PromiseResultModel<bool>.ExceptionResult(
					new Exception($"Failed to send MFA code to {challenge.MaskedDestination}.", ex), true);
			}
		}

		private static string GenerateNumericCode(int length) {
			var rng = RandomNumberGenerator.Create();
			var bytes = new byte[length];
			rng.GetBytes(bytes);
			var sb = new StringBuilder(length);
			foreach (var b in bytes) sb.Append((b % 10).ToString());
			return sb.ToString();
		}

		private static (string hash, string salt) Hash(string code) {
			var saltBytes = new byte[16];
			RandomNumberGenerator.Fill(saltBytes);
			var salt = Convert.ToBase64String(saltBytes);
			using (var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(salt))) {
				var hash = Convert.ToBase64String(hmac.ComputeHash(Encoding.UTF8.GetBytes(code)));
				return (hash, salt);
			}
		}

		private static bool Verify(string code, string hash, string salt) {
			using (var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(salt))) {
				var computed = Convert.ToBase64String(hmac.ComputeHash(Encoding.UTF8.GetBytes(code)));
				return string.Equals(computed, hash, StringComparison.Ordinal);
			}
		}
	}
}