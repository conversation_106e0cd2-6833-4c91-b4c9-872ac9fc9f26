<template>
  <div>
    <h4>Fetcher</h4>
    <b-tabs v-model="selectedTab" class="nav-tabs-top nav-responsive-sm" no-fade>
      <b-tab v-for="tab in tabOptions" :key="tab.value" :title="tab.title" class="p-3">
        <l-button :loading="fetchingLoadingState[tab.fetchLevel]" variant="primary" @click="fetch(tab.fetchLevel)" :disabled="!fetchingEnabledState[tab.fetchLevel]">{{tab.btnTitle}}</l-button>
        <b-btn v-if="fetchingLoadingState[tab.fetchLevel]" @click="cancelFetch(tab.fetchLevel)">Cancel</b-btn>
        <b-textarea class="mt-3" v-if="tab.fetchLevel !== fetchLevels.categories" v-model="accountsToFetch[tab.fetchLevel]" rows="5">
        </b-textarea>
      </b-tab>
    </b-tabs>
    <b-card>
      <b-progress class="mb-2" :value="progressFetchResult[getFetchLevel]" :max="100" variant="primary" :animated="progressFetchResult[getFetchLevel] !== 100"></b-progress>
      <b-row>
        <b-col>
          <b-btn class="my-2 float-right" size="sm" variant="info btn-round" @click="fetchToggle(getFetchLevel)">Show / Hide</b-btn>
        </b-col>
      </b-row>
      <b-row>
        <b-col>
          <b-collapse class="w-100" :id="getFetchLevel" v-model="fetchCollapseOptions[getFetchLevel]">
            <div class="border p-2" style="overflow-y: auto; height: 300px;" no-body v-html="fetchResults[getFetchLevel]">
            </div>
          </b-collapse>
        </b-col>
      </b-row>
    </b-card>
  </div>
</template>

<script>
import { fetchLevels } from '@/shared/siteBoxManager/common/constants'
import fetcherHub from '@/signalR/fetcherHub'

export default {
  name: 'sitebox-manager-fetcher',
  metaInfo: {
    title: 'Fetcher'
  },
  data () {
    return {
      fetcherTab: 0,
      isConnectionManuallyClosed: false,
      fetchLevels,
      fetchingEnabledState: {
      },
      fetchingLoadingState: {
      },
      accountsToFetch: {
        settings: '',
        vehicles: ''
      },
      fetchCollapseOptions: {},
      progressFetchResult: {},
      fetchResults: {},
      tabOptions: [
        { value: 0, title: 'Fetch Account/Site Settings', fetchLevel: fetchLevels.settings, btnTitle: 'Fetch Settings' },
        { value: 1, title: 'Fetch Vehicle Information', fetchLevel: fetchLevels.vehicles, btnTitle: 'Fetch Vehicles' },
        { value: 2, title: 'Fetch Categories', fetchLevel: fetchLevels.categories, btnTitle: 'Fetch Categories' }
      ]
    }
  },
  computed: {
    selectedTab: {
      get () {
        return this.fetcherTab
      },
      set (val) {
        this.fetcherTab = val
        this.checkStatusService()
      }
    },
    getFetchLevel () {
      return this.tabOptions[this.selectedTab].fetchLevel
    }
  },
  async created () {
    Object.values(fetchLevels).forEach(element => {
      this.$set(this.fetchCollapseOptions, element, true)
    })
    await this.connectToSignalR()
  },
  beforeDestroy () {
    this.disconnectFromSignalR()
  },
  methods: {
    fetchToggle (fetchLevel) {
      let isVisible = !this.fetchCollapseOptions[fetchLevel]
      this.$set(this.fetchCollapseOptions, fetchLevel, isVisible)
    },
    checkStatusService () {
      let res = this.tabOptions.find(x => x.value === this.selectedTab)
      this.$store.dispatch('siteBoxManager/fetcherCheckStatus', { data: { fetchingLevel: res.fetchLevel } }).then(res => {
      }).catch(ex => {
        this.toaster.exception(ex, 'Something went wrong!')
      })
    },
    fetch (fetchLevel) {
      this.$set(this.fetchingLoadingState, fetchLevel, true)
      this.$store.dispatch('siteBoxManager/fetcherStartProcessing', { data: { fetchingLevel: fetchLevel, accountsStr: this.accountsToFetch[fetchLevel] } }).then(res => {
        if (res.data && res.data.length > 0) {
          this.$toaster.warning('The following accounts will be ignored, because settings are empty: ' + res.data.join(', '))
        }
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
        this.$set(this.fetchingLoadingState, fetchLevel, false)
      })
    },
    cancelFetch (fetchLevel) {
      this.$store.dispatch('siteBoxManager/fetcherCancelProcessing', { data: { fetchingLevel: fetchLevel } }).then(res => {
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
      })
    },
    connectToSignalR () {
      fetcherHub.on('writeStatus', (tabId, isSuccess, message, totalProcessed, totalCount) => {
        if (isSuccess) {
          this.appendFetchResult(tabId, `<span class="text-success">${message}</span><br>`)
        } else {
          this.appendFetchResult(tabId, `<span class="text-danger">${message}</span><br>`)
        }
        this.setFetchProgress(tabId, (totalProcessed / totalCount) * 100)
      })
      fetcherHub.on('onFetchingFinished', (tabId, result) => {
        this.appendFetchResult(tabId, `<span class="text-info">${result}</span><br>`)
        this.$set(this.fetchingLoadingState, tabId, false)
        this.setFetchProgress(tabId, 100)
      })
      fetcherHub.on('onCancelFetchingResultReceived', (tabId, isSuccess, message) => {
        if (isSuccess) {
          this.appendFetchResult(tabId, `<span class="text-info">${message}</span><br>`)
          this.$set(this.fetchingLoadingState, tabId, false)
        } else {
          this.appendFetchResult(tabId, `<span class="text-danger">${message}</span><br>`)
        }
      })
      fetcherHub.on('onStartFetchingResultReceived', (tabId, isStarted, message) => {
        this.setFetchProgress(tabId, 0)
        if (isStarted) {
          this.$toaster.success(message, { timeout: 8000 })
          this.appendFetchResult(tabId, `<spam class="text-success">${message}</spam><br>`, true)
        } else {
          this.$toaster.warning(message, { timeout: 8000 })
          this.$set(this.fetchingLoadingState, tabId, false)
        }
      })
      fetcherHub.on('onCheckStatusResultReceived', (tabId, isSuccess, isReady, message) => {
        if (isSuccess) {
          this.$set(this.fetchingEnabledState, tabId, true)
          this.$set(this.fetchingLoadingState, tabId, !isReady)
        } else {
          this.$toaster.error(message, { timeout: 8000 })
          this.$set(this.fetchingEnabledState, tabId, false)
        }
      })
      fetcherHub.onclose(this.onConnectionCloseFromSignalR)
      this.establishConnection()
    },
    onConnectionCloseFromSignalR () {
      if (!this.isConnectionManuallyClosed) {
        this.establishConnection()
      }
    },
    async establishConnection () {
      await fetcherHub.start().then(() => {
        this.checkStatusService()
      }).catch(err => {
        console.error('Failed to connect with hub', err)
        return new Promise((resolve, reject) =>
          setTimeout(() => this.connectToSignalR().then(resolve).catch(reject), 5000))
      })
    },
    disconnectFromSignalR () {
      this.isConnectionManuallyClosed = true
      fetcherHub.stop()
    },
    appendFetchResult (tabId, message, isStarted) {
      let fetchRes = isStarted ? message : (this.fetchResults[tabId] || '') + message
      this.$set(this.fetchResults, tabId, fetchRes)
    },
    setFetchProgress (tabId, progress) {
      this.$set(this.progressFetchResult, tabId, progress)
    }
  }
}
</script>
