<template>
  <div>
    <b-table
      :fields="getTableFields"
      :items='items'
      :sort-by="tableSortBy"
      :sort-desc="tableSortDesc"
      @sort-changed="onSortChanged"
      :bordered="false"
      :no-sort-reset="true"
      :no-local-sorting="true"
      @row-clicked='onSpamMessageDetail'
      hover
      striped
      responsive
      >

      <template #cell(manage)="data">
        <div class="d-flex flex-column">
          <b-btn size="sm" @click="onResubmit(data.item.id)">Resubmit</b-btn>
          <b-btn size='sm' class="mt-1" variant="primary" @click="onDelete(data.item.id)">Delete</b-btn>
        </div>
      </template>
    </b-table>
    <leads-spam-message-details-modal v-if='itemDetails' @hide='hideDetailsModal' :isShowModal='isShowModal' :item='itemDetails'/>
  </div>
</template>

<script>
import { leadType, communicationType } from '@/shared/leads/common'
import { spamMessageSortType } from '@/shared/leads/spam'
import leadsSpamMessageDetailsModal from './leadsSpamMessageDetailsModal'
import moment from 'moment'

export default {
  name: 'leads-spam-message-listing',
  props: {
    items: { type: Array, required: true },
    sort: { type: Number, required: true }
  },
  data () {
    return {
      isShowModal: false,
      itemDetails: null
    }
  },
  components: {
    'leads-spam-message-details-modal': leadsSpamMessageDetailsModal
  },
  computed: {
    getTableFields () {
      return [
        {
          key: 'accountId',
          label: 'Account ID',
          sortable: true,
          sortTypeAsc: spamMessageSortType.accountIdAsc,
          sortTypeDesc: spamMessageSortType.accountIdDesc,
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'firstName',
          label: 'First Name',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'lastName',
          label: 'Last Name',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'email',
          label: 'Email',
          sortable: true,
          sortTypeAsc: spamMessageSortType.emailAsc,
          sortTypeDesc: spamMessageSortType.emailDesc,
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'communicationType',
          label: 'Communication Type',
          tdClass: 'py-2 align-middle',
          formatter: value => {
            let type = Object.values(communicationType).find(x => x.value === value)

            if (type) {
              return type.label
            }
            return ''
          }
        },
        {
          key: 'leadType',
          label: 'Lead Type',
          tdClass: 'py-2 align-middle',
          formatter: value => {
            let type = Object.values(leadType).find(x => x.value === value)

            if (type) {
              return type.label
            }
            return ''
          }
        },
        {
          key: 'dateTimeCreated',
          sortable: true,
          sortTypeAsc: spamMessageSortType.createdDateAsc,
          sortTypeDesc: spamMessageSortType.createdDateDesc,
          label: 'Created Date',
          tdClass: 'py-2 align-middle',
          formatter: value => moment(value).format('MM/DD/YYYY hh:mm A')
        },
        {
          key: 'filterName',
          label: 'Filter Name',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'leadSourceIp',
          label: 'Source IP',
          sortable: true,
          sortTypeAsc: spamMessageSortType.sourceIpAsc,
          sortTypeDesc: spamMessageSortType.sourceIpDesc,
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'classCSubnet',
          label: 'Subnet',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    tableSortBy () {
      const sortedColumn = this.getTableFields.find(x => x.sortTypeAsc === this.sort || x.sortTypeDesc === this.sort)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.getTableFields.find(x => x.sortTypeAsc === this.sort || x.sortTypeDesc === this.sort)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sort
      } else {
        return false
      }
    }
  },
  methods: {
    onSortChanged (value) {
      const sortingColumn = this.getTableFields.find(x => x.key === value.sortBy)
      if (value.sortDesc) {
        this.$emit('sortChange', sortingColumn.sortTypeDesc)
      } else {
        this.$emit('sortChange', sortingColumn.sortTypeAsc)
      }
    },
    onSpamMessageDetail (item) {
      this.$store.dispatch('leads/getSpamMessageDetails', item.id).then(res => {
        this.itemDetails = res.data
        this.isShowModal = true
      }).catch(ex => {
        this.$toaster.exception(ex, `Failed on getting the details of spam message`)
        this.$logger.handleError(ex, `Cannot get the spam message details`)
      })
    },
    hideDetailsModal () {
      this.isShowModal = false
      this.itemDetails = null
    },
    onResubmit (id) {
      this.$emit('resubmit', id)
    },
    onDelete (id) {
      this.$emit('delete', id)
    }
  }
}
</script>
