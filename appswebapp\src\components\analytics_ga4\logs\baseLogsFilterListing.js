export default {
  props: {
    items: { type: Array, required: true },
    sortType: { type: Number, required: true }
  },
  computed: {
    getTableFields () {
      return []
    },
    tableSortBy () {
      const sortedColumn = this.getTableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.getTableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sortType
      } else {
        return false
      }
    }
  },
  methods: {
    onSortChanged (ctx) {
      const sortingColumn = this.getTableFields.find(x => x.key === ctx.sortBy)
      if (!sortingColumn) {
        return
      }
      if (ctx.sortDesc) {
        this.$emit('sort-changed', sortingColumn.sortTypeDesc)
      } else {
        this.$emit('sort-changed', sortingColumn.sortTypeAsc)
      }
    },
    onDelete (id) {
      this.$emit('delete', id)
    }
  }
}
