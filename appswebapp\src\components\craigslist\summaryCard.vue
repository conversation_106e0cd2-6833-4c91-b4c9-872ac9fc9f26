<template>
  <b-card no-body :class="cardClass">
    <b-card-body class="d-flex justify-content-between align-items-center">
      <div>
        <div class="metric-title h5 mb-1">{{label}}</div>
        <div>
          <span class="metric-amount text-xlarge">
            <slot name="value" v-bind="valueScope">
              {{value}}
            </slot>
          </span>
          <span v-if="isDeltaDefined" :class="`font-weight-bold ${ (reverseNot*delta) > 0  ? 'text-success' : 'text-danger'}`">
            <slot name="delta" v-bind="deltaScope">
              {{deltaLabel}}
            </slot>
          </span>
        </div>
        <div v-if="rangeLabel" class="small opacity-75 mt-2">{{rangeLabel}}</div>
      </div>
      <slot></slot>
    </b-card-body>
  </b-card>
</template>

<script>
export default {
  name: 'summary-card',
  props: {
    label: String,
    value: [String, Number],
    reverse: <PERSON><PERSON><PERSON>,
    cardClass: {
      type: String,
      default: 'm-0 bg-transparent'
    },
    delta: {
      required: true,
      validator: prop => prop === null || typeof prop === 'number'
    },
    rangeLabel: String
  },
  data () {
    return {}
  },
  computed: {
    reverseNot () {
      return this.reverse ? -1 : 1
    },
    isDeltaDefined () {
      return this.delta !== undefined && this.delta !== null
    },
    deltaPercent () {
      return Math.round(this.delta * 100)
    },
    deltaLabel () {
      if (this.delta > 0) {
        return `+${this.deltaPercent}%`
      } else if (this.delta < 0) {
        return `${this.deltaPercent}%`
      } else {
        return '0%'
      }
    },
    valueScope () {
      return {
        value: this.value,
        localizedValue: this.$locale.formatNumber(this.value)
      }
    },
    deltaScope () {
      return {
        value: this.delta,
        percentValue: this.deltaPercent
      }
    }
  }
}
</script>
