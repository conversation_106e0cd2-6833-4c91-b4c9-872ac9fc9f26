<template>
  <div>
    <div class="border-bottom pb-1">
      <b-row class="mr-1 ml-1 title-section">
        <b-col class="pl-0">
          <h5 class="font-weight-bold">MFA Settings</h5>
        </b-col>
        <b-col class="text-right pr-0">
          <b-button
            v-if="!isEditing"
            variant="secondary"
            size="sm"
            @click="startEditing"
          >
            <i class="ion ion-md-create mr-1"></i>
            Edit
          </b-button>

          <div v-else>
            <b-button
              variant="primary"
              size="sm"
              :disabled="isSaving"
              @click="saveSettings"
            >
              <b-spinner v-if="isSaving" small class="mr-1"></b-spinner>
              <font-awesome-icon v-else icon="cloud-upload-alt" />
              <span class="btn-title">Save</span>
            </b-button>
            <b-button
              variant="secondary"
              class="ml-2"
              size="sm"
              @click="cancelEditing"
            >
              Cancel
            </b-button>
          </div>
        </b-col>
      </b-row>
    </div>

    <div class="mt-3">
      <b-row>
        <b-col md="6">
          <b-form-group label="MFA Status" label-for="mfaStatus">
            <b-form-checkbox
              id="mfaStatus"
              v-model="editableSettings.isMfaEnabled"
              :disabled="!isEditing"
            >
              Enable Multi-Factor Authentication
            </b-form-checkbox>
          </b-form-group>
        </b-col>
      </b-row>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MfaSettingsSection',
  props: {
    settings: {
      type: Object,
      required: true
    },
    accountId: {
      type: Number,
      required: true
    }
  },
  data () {
    return {
      isEditing: false,
      isSaving: false,
      editableSettings: { ...this.settings },
      originalSettings: { ...this.settings }
    }
  },
  watch: {
    settings: {
      handler (newVal) {
        this.editableSettings = { ...newVal }
        this.originalSettings = { ...newVal }
      },
      deep: true
    }
  },
  methods: {
    startEditing () {
      this.isEditing = true
    },
    cancelEditing () {
      this.isEditing = false
      this.editableSettings = { ...this.originalSettings }
    },
    async saveSettings () {
      this.isSaving = true
      try {
        const updateData = {
          accountId: this.accountId,
          isMfaEnabled: this.editableSettings.isMfaEnabled
        }

        await this.$store.dispatch('accountSettings/updateAccountSettings', updateData)

        this.isEditing = false
        this.$toaster.success('MFA settings updated successfully!')

        this.originalSettings = { ...this.editableSettings }
      } catch (error) {
        this.$logger.handleError(error, `Failed to update MFA settings for account ${this.accountId}`)
        this.$toaster.error('Failed to update MFA settings. Please try again.')
        this.cancelEditing()
      } finally {
        this.isSaving = false
      }
    }
  }
}
</script>
