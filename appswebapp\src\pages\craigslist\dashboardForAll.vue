<template>
  <div>
    <div class="dashboard-header">
    <h2>All Accounts</h2>

     <range-selector
                @input="onRangeChanged"
                :value="filterDateRange"
                class="button-col"
          />

    </div>

    <div class="mt-4">
      <b-card>
        <b-row align-h='between'>
          <b-col text-right cols='4'>
            <div class='h4'>{{ chartType }}</div>
          </b-col>

          <b-col cols='1' style='text-align:end'>
            <b-dropdown
              variant='outline-secondary icon-btn btn-round'
              size='sm'
              right
              no-caret
            >
              <template slot='button-content'>
                <i class='ion ion-ios-more m-0'></i>
              </template>
              <b-dropdown-item @click='setCostDailyTotals()'>Cost</b-dropdown-item>
              <b-dropdown-item @click='setPostDailyTotals()'>Posts</b-dropdown-item>
            </b-dropdown>
          </b-col>
        </b-row>

        <vue-echart
          id='echart'
          :options='barOptions'
          :auto-resize='true'
        ></vue-echart>
      </b-card>
    </div>

    <div class="container-fluid pl-0 pr-0">
      <div class='row mt-4 widget-metric-higlights'>
        <div class='col-6 col-sm-6 col-xl-6  pr-0'>
          <summary-card
            label='Cost'
            :value='cost.value'
            :delta='cost.delta'
            :rangeLabel='currentDateRange'
            cardClass='bg-white'
          >
            <i class='ion-ios-wallet h1 m-0 opacity-25 d-none d-sm-inline'></i>
          </summary-card>
        </div>
        <div class='col-6 col-sm-6 col-xl-6 pl-0'>
          <summary-card
            label='Posts'
            :value='posts.value'
            :delta='posts.delta'
            :rangeLabel='currentDateRange'
            cardClass='bg-white'
          >
            <i
              class='ion-ios-document h1 m-0 opacity-25 d-none d-sm-inline'
              data-src='../../../static/img/apps-icons/icon-messages-green.png'
            ></i>
          </summary-card>
        </div>
      </div>
    </div>

    <b-row class="mb-2 mt-3">
      <b-col class="py-2" xl="5" lg="6" md="6" sm="12">
        <b-input-group>
          <b-form-input v-model="filter.search" @keydown.enter="applySearchFilter" placeholder="Search By Account Name, Account Id"></b-form-input>
          <template #append>
            <b-btn variant="primary" @click="applySearchFilter">Search</b-btn>
          </template>
        </b-input-group>
      </b-col>
      <b-col class="py-2" xl="2" lg="4" md="6" sm="12" style="min-width: 200px;">
        <b-form-select v-model="filter.accountPostingStatus" :options="accountPostingStatusOptions" @input="applySearchFilter"></b-form-select>
      </b-col>
    </b-row>
    <div class="table-responsive">
      <b-card>
        <b-table
          hover
          :items='accountsSummaries'
          :fields='accountsSummariesFields'
          :sort-by="tableSortBy"
          :sort-desc="tableSortDesc"
          @sort-changed="onSortChanged"
          show-empty
          :striped="true"
          :bordered="false"
          :no-sort-reset="true"
          :no-local-sorting="true"
          responsive
          class="products-table card-table"
        >

        <template #cell(isPostingAllowed)="data">
          <checkboxWithConfirm
            :disabled="!hasPermissions"
            v-model="data.item.isPostingAllowed"
            :message="`Are you sure you want to ${data.item.isPostingAllowed ? 'disable' : 'enable'} posting`"
            @confirm="changeAccountPostingAllowed(data.item)"/>
        </template>

        <template #cell(accountID)="data">
          <slot name="accountID" :data="data">
            <listing-link :query='$router.history.current.query' :account-id="data.item.accountID" :force-link-name="'craigslist-dashboard-for-account'" >{{data.item.accountID}}</listing-link>
          </slot>
        </template>

        <template #cell(accountName)="data">
          <slot name="accountName" :data="data">
            <listing-link :query='$router.history.current.query' :account-id="data.item.accountID" :force-link-name="'craigslist-dashboard-for-account'" >{{data.item.accountName}}</listing-link>
          </slot>
        </template>

        </b-table>
        <!-- Pagination -->
        <paging
        :pageNumber="filter.page"
        :pageSize="filter.pageSize"
        :totalItems="accountsSummariesTotal"
        titled
        pageSizeSelector
        @numberChanged="pageChanged"
        @changePageSize="changePageSize" />
      </b-card>
    </div>
  </div>
</template>

<script>
import 'echarts/lib/chart/bar'
import 'echarts/lib/component/tooltip'
import 'echarts/lib/component/legend'
import moment from 'moment'

import AppsListingLink from '../../components/_shared/applicationAccountListing/AppsListingLink'

import numeral from 'numeral'
import dateHelper from '@/plugins/locale/date'
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import Paging from '@/components/_shared/paging.vue'
import Constants from '@/shared/craigslist/constants'
import checkboxWithConfirm from '@/components/_shared/checkboxWithConfirm'
import { mapGetters } from 'vuex'
import permissions from '../../shared/common/permissions'

const defaultValues = new ObjectSchema({
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 10 },
  sort: { type: Number, default: 1 },
  search: { type: String, default: '' },
  accountPostingStatus: { type: Number, default: Constants.accountPostingStatuses.undefined.value }
})
const queryHelper = new QueryStringHelper(defaultValues)

var LRU = require('lru-cache')
const cache = new LRU(50)

export default {
  name: 'dashboard-for-all',
  metaInfo: {
    title: 'Craigslist Dashboard'
  },
  created () {
    if (cache.get('CraiglistDashboardFilter')) {
      this.filter = cache.get('CraiglistDashboardFilter')
    } else {
      this.filter = queryHelper.parseQueryStringToObject(this.$router)
    }
    this.getInitialData()
  },
  components: {
    'vue-echart': () => import('vue-echarts/components/ECharts.vue'),
    'summary-card': () => import('../../components/craigslist/summaryCard'),
    'range-selector': () => import('../../components/craigslist/rangeSelector/craigslistRangeSelector'),
    'listing-link': AppsListingLink,
    'paging': Paging,
    checkboxWithConfirm
  },
  data () {
    return {
      DailyTotals: [],
      filter: defaultValues.getObject(),
      // totals: Object,
      currentDateRange: '03/01/2019 - 03/30/2019',
      posts: { value: 0, delta: 0 },
      cost: { value: 0, delta: 0 },
      rangeInfo: Object,
      chartCost: false,
      typeDailyTotals: 'AmountOfPosts',
      accountsSummaries: [],
      accountsSummariesTotal: 0,
      accountsSummariesFields: [
        {
          key: 'accountID',
          label: 'Account ID',
          sortTypeAsc: Constants.craigslistPostsSortType.accountIdAsc,
          sortTypeDesc: Constants.craigslistPostsSortType.accountIdDesc,
          sortable: true,
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'accountName',
          label: 'Account Name',
          sortTypeAsc: Constants.craigslistPostsSortType.accountNameAsc,
          sortTypeDesc: Constants.craigslistPostsSortType.accountNameDesc,
          sortable: true,
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'isPostingAllowed',
          label: 'Enable Posting',
          sortable: false,
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'budget',
          label: 'Budget',
          sortable: false,
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'amountOfPosts',
          label: 'Posts',
          sortTypeAsc: Constants.craigslistPostsSortType.countAsc,
          sortTypeDesc: Constants.craigslistPostsSortType.countDesc,
          sortable: true,
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'totalPostPrice',
          label: 'Cost',
          sortable: true,
          sortTypeAsc: Constants.craigslistPostsSortType.costAsc,
          sortTypeDesc: Constants.craigslistPostsSortType.costDesc,
          formatter: value => numeral(value).format('$0,0'),
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'scheduledPosts',
          label: 'Scheduled Posts',
          sortTypeAsc: Constants.craigslistPostsSortType.scheduledAsc,
          sortTypeDesc: Constants.craigslistPostsSortType.scheduledDesc,
          sortable: true,
          tdClass: 'py-2 align-middle'
        }
      ]
    }
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user
    },
    hasPermissions () {
      if (this.user) {
        return this.user.hasPermissions(permissions.EbizAutosAdmin)
      }
      return false
    },
    filterDateRange () {
      if ((this.filter || {}).dateFrom && (this.filter || {}).dateTo) {
        return [this.filter.dateFrom, this.filter.dateTo]
      }

      return null
    },
    tableSortBy () {
      const sortedColumn = this.accountsSummariesFields.find(x => x.sortTypeAsc === this.filter.sort || x.sortTypeDesc === this.filter.sort)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.accountsSummariesFields.find(x => x.sortTypeAsc === this.filter.sort || x.sortTypeDesc === this.filter.sort)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.filter.sort
      } else {
        return false
      }
    },
    accountPostingStatusOptions () {
      return Object.values(Constants.accountPostingStatuses)
    },
    chartType () {
      return this.chartCost ? 'Cost' : 'Posts'
    },
    barTimeFormat () {
      if (
        this.rangeInfo &&
        Date.daysBetween(
          new Date(this.rangeInfo.range[1]),
          new Date(this.rangeInfo.range[0]),
          true
        ) > 365
      ) {
        return 'MMM D YYYY'
      }
      return 'MMM.D'
    },
    barOptions () {
      return {
        title: {
          text: this.chartType
        },
        grid: {
          left: '0',
          right: '0',
          bottom: '0',
          top: '5%',
          containLabel: true
        },
        color: '#6b0001',
        tooltip: {
          trigger: 'axis',
          formatter: params => {
            return `${params[0].name}<br />${this.$locale.formatNumber(
              params[0].value
            )}`
          },
          textStyle: {
            fontSize: 13
          }
        },
        xAxis: [
          {
            data: this.DailyTotals.map(x => {
              return dateHelper.getDayFormatted(x.DateTimeFrom, 'MMM.D')
            }),
            axisLabel: {
              color: 'rgba(0, 0, 0, .9)'
            }
          }
        ],
        yAxis: {},
        series: [
          {
            barWidth: '4',
            type: 'bar',
            data: this.DailyTotals.map(x => x[this.typeDailyTotals]),
            itemStyle: {
              normal: {
                color: '#dc3545'
              }
            }
          }
        ],
        animationDuration: 2000
      }
    }
  },
  destroyed () {
    cache.set('CraiglistDashboardFilter', this.filter)
  },
  methods: {
    async onRangeChanged (rangeInfo) {
      if (this.filter.dateFrom === rangeInfo.range[0] && this.filter.dateTo === rangeInfo.range[1] && this.rangeInfo) {
        return
      }
      this.filter.dateFrom = rangeInfo.range[0]
      this.filter.dateTo = rangeInfo.range[1]
      this.rangeInfo = rangeInfo
      this.getInitialData()
    },
    onSortChanged (value) {
      const sortingColumn = this.accountsSummariesFields.find(x => x.key === value.sortBy)
      if (value.sortDesc) {
        this.filter.sort = sortingColumn.sortTypeDesc
      } else {
        this.filter.sort = sortingColumn.sortTypeAsc
      }
      queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.getGlobalDashboard()
    },
    applySearchFilter () {
      this.filter.page = 1
      queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.getGlobalDashboard()
    },
    setCostDailyTotals () {
      this.chartCost = true
      this.typeDailyTotals = 'TotalPostPrice'
    },
    setPostDailyTotals () {
      this.chartCost = false
      this.typeDailyTotals = 'AmountOfPosts'
    },
    pageChanged (newPage) {
      this.filter.page = newPage
      queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.getGlobalDashboard()
    },
    changePageSize (newPageSize) {
      this.filter.page = 1
      this.filter.pageSize = newPageSize
      queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.getGlobalDashboard()
    },
    getInitialData () {
      queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.getTotalSummary()
      this.getDailyTotals()
      this.getGlobalDashboard()
    },
    getTotalSummary () {
      return this.$store.dispatch('craigslist/getTotalSummary', {filter: this.filter}).then(x => {
        this.posts.value = x.data.PostsTotalCount
        this.posts.delta = x.data.PostsDelta
        this.cost.value = numeral(x.data.PostsTotalCost).format('$0,0.00')
        this.cost.delta = x.data.CostDelta
        this.currentDateRange = moment(x.data.DateTimeFrom).format('MMMM D, YYYY') + ' - ' + moment(x.data.DateTimeTo).format('MMMM DD, YYYY')
      })
    },
    getDailyTotals () {
      this.$store.dispatch('craigslist/getDailytotals', {filter: this.filter}).then(x => {
        if (x.data.DailyTotals) {
          this.DailyTotals = x.data.DailyTotals.sort(
            (a, b) => new Date(a.DateTimeFrom) - new Date(b.DateTimeFrom)
          )
        }
      })
    },
    getGlobalDashboard () {
      return this.$store.dispatch('craigslist/getGlobalDashboard', {filter: this.filter}).then(x => {
        this.accountsSummaries = x.data.model.accountsSummaries
        this.accountsSummariesTotal = x.data.model.accountsCount
      })
    },
    changeAccountPostingAllowed (item) {
      this.$store.dispatch('craigslist/changeAccountPostEnabling', {accountId: item.accountID, data: {accountId: item.accountID, isPostingAllowed: item.isPostingAllowed}}).then(res => {
        this.$toaster.success('Changed Successfully')
      }).catch(ex => {
        this.$toaster.error('Something went wrong!')
      }).finally(() => {
        this.getGlobalDashboard()
      })
    }
  }
}
</script>

<style>
.dashboard-header{
  display: flex;
  justify-content: space-between;
}
.dashboard-header h4 {
  margin: 0;
  align-self: center;
}
#echart {
  width: 100%;
  height: 250px;
}
</style>
