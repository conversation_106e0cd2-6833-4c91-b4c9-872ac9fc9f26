using System;
using System.Threading.Tasks;
using EBizAutos.Apps.Shared.Api.Models.Mfa;
using EBizAutos.Apps.Shared.Api.Utilities.Helpers;
using EBizAutos.Apps.CommonLib.Models.Logs.SystemTools;
using EBizAutos.Apps.CommonLib.Models.Web;
using EBizAutos.Apps.Shared.Api.Models;
using EBizAutos.Apps.Shared.Api.Utilities.Managers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using EBizAutos.Apps.Authentication.CommonLib.Models;
using static EBizAutos.Apps.CommonLib.Enums.AuthenticationEnums;
using EBizAutos.Apps.CommonLib.Abstract.Repositories.AppsAccountSettings;

namespace EBizAutos.Apps.Shared.Api.Controllers {
	[Route("auth")]
	public class AuthController : BaseApiController {
		private readonly UserAuthenticationManager _authenticationManager;
		private readonly IAppsAccountSettingsRepository _accountSettingsRepository;
		private readonly MfaChallengeManager _mfaManager;

		public AuthController(IServiceProvider serviceProvider) : base(serviceProvider) {
			_authenticationManager = serviceProvider.GetRequiredService<UserAuthenticationManager>();
			_accountSettingsRepository = serviceProvider.GetRequiredService<IAppsAccountSettingsRepository>();
			_mfaManager = serviceProvider.GetRequiredService<MfaChallengeManager>();
		}

		/// <summary>
		/// Authenticates user via cookies
		/// </summary>
		/// <param name="model">Login credentials</param>
		[HttpPost("login")]
		[AllowAnonymous]
		public async Task<IActionResult> Login([FromBody] LoginRequestModel model) {
			if (model == null) {
				return BadRequest("Model is empty");
			}

			if (!ModelState.IsValid) {
				return InvalidModelApiResult();
			}

			var turnstileResult = await _authenticationManager.ValidateTurnstileTokenAsync(model.TurnstileToken);
			if (turnstileResult.IsRejected) {
				if (turnstileResult.HasUnhandeledException) {
					return StatusCode(
						StatusCodes.Status500InternalServerError,
						"Error validating Turnstile token"
					);
				} else {
					return BadRequest(turnstileResult.CancelationMessage);
				}
			}

			BasicAuthenticationIdentity currentUserIdentity = Identity;
			if (currentUserIdentity != null &&
				currentUserIdentity.IsAuthenticated &&
				string.Compare(model.UserName, currentUserIdentity.UserName, true) == 0
			) {
				return ApiResult(PromiseResultModel<bool>.SuccessResult(true));
			}

			// Try sign-in directly, but if MFA is enabled, return MFA challenge instead of signing in
			var validationResult = await _authenticationManager.ValidateUserCredentialsAsync(model);
			if (validationResult.IsRejected) {
				return ApiResult(validationResult.AsPromise(x => false));
			}

			var user = validationResult.Model;
			bool isMfaRequired = false;
			if (user != null && user.AccountId > 0) {
				var acc = _accountSettingsRepository.GetBasicAccountSettings(user.AccountId);
				isMfaRequired = acc != null && acc.IsMfaEnabled;
			}

			if (!isMfaRequired) {
				var completeResult = await _authenticationManager.CreateSessionAndCompleteCookieSignInAsync(user, model);
				return ApiResult(completeResult.AsPromise(x => !completeResult.IsRejected));
			}

			var availableMethods = new System.Collections.Generic.List<MfaMethodModel>();

			// Add Email method if available and verified
			if (!string.IsNullOrEmpty(user.PersonalInformation?.Email) &&
				user.PersonalInformation?.IsEmailVerified == true) {
				availableMethods.Add(new MfaMethodModel {
					Type = MfaMethodEnum.Email,
					Name = MfaMethodHelper.GetMethodName(MfaMethodEnum.Email),
					MaskedDestination = MfaChallengeManager.MaskEmail(user.PersonalInformation.Email)
				});
			}

			// Add SMS method if available and verified
			if (!string.IsNullOrEmpty(user.PersonalInformation?.MfaPhoneNumber) &&
				user.PersonalInformation?.IsMfaPhoneNumberVerified == true) {
				availableMethods.Add(new MfaMethodModel {
					Type = MfaMethodEnum.Sms,
					Name = MfaMethodHelper.GetMethodName(MfaMethodEnum.Sms),
					MaskedDestination = MfaChallengeManager.MaskPhone(user.PersonalInformation.MfaPhoneNumber)
				});
			}

			// Check if any verified MFA methods are available
			if (availableMethods.Count == 0) {
				return ApiResult(PromiseResultModel<MfaCreateChallengeResponseModel>.CanceledResult(
					"No verified MFA methods available. Please set up and verify your email or phone number first."));
			}

			var challengeResult = await _mfaManager.CreateChallengeAsync(
				user: user,
				applicationName: model.ApplicationName,
				deviceId: model.DeviceId,
				deviceModel: model.DeviceModel,
				ipAddress: HttpContext?.Connection?.RemoteIpAddress?.ToString() ?? string.Empty
			);

			if (challengeResult.IsRejected) {
				return ApiResult(challengeResult.AsPromise(x => false));
			}

			var challenge = challengeResult.Model;

			var response = new MfaCreateChallengeResponseModel {
				IsMfaRequired = true,
				ChallengeId = challenge.Id,
				AvailableMethods = availableMethods.ToArray()
			};
			return ApiResult(PromiseResultModel<MfaCreateChallengeResponseModel>.SuccessResult(response));
		}

		/// <summary>
		/// Terminates user cookie session
		/// </summary>
		[HttpGet("logout")]
		[AllowAnonymous]
		public async Task<IActionResult> Logout() {
			PromiseResultModel<bool> promise = await _authenticationManager.SignOutUserByCookieAsync(User);

			return ApiResult(promise);
		}

		/// <summary>
		/// Terminates user cookie session and redirects to home page
		/// </summary>
		/// <returns></returns>
		[HttpGet("logoutwithredirect")]
		[AllowAnonymous]
		public async Task<IActionResult> LogoutWithRedirect() {
			PromiseResultModel<bool> promise = await _authenticationManager.SignOutUserByCookieAsync(User);

			return Redirect(GetHostUrl(Request.Scheme, Request.Host.Value));
		}

		/// <summary>
		/// Generates user authentication token
		/// </summary>
		/// <param name="model">Login credentials</param>
		/// <response code="200">Generated token</response>
		[Route("generatetoken")]
		[HttpPost]
		[AllowAnonymous]
		[ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
		public async Task<IActionResult> GenerateTokenAsync([FromBody] LoginRequestModel model) {
			if (!ModelState.IsValid) {
				return BadRequest("Invalid request");
			}
			DateTime requestDateTime = DateTime.Now;
			PromiseResultModel<GenerateTokenResponseModel> tokenModelPromise = await _authenticationManager.GenerateJwtTokenAsync(model);
			PublishSystemToolsOperationTriggeredEvent(UserActivityLogItem.ActionTypeEnum.GenerateToken, tokenModelPromise.RaisedException, requestDateTime, model);
			return ApiResult(tokenModelPromise);
		}

		/// <summary>
		/// Invalidates authentication token from header and terminates user session
		/// </summary>
		[Route("invalidatetoken")]
		[HttpPost]
		public async Task<IActionResult> InvalidateTokenAsync() {
			PromiseResultModel<bool> closeSessionPromise = await _authenticationManager.CloseUserSessionAsync(User);

			return ApiResult(closeSessionPromise);
		}

		/// <summary>
		/// Sends password recovery email
		/// </summary>
		/// <param name="model">Login credentials</param>
		[Route("resetpassword")]
		[HttpPost]
		[AllowAnonymous]
		public IActionResult ResetPassword([FromBody] PasswordResetRequestModel model) {
			PromiseResultModel<bool> resetPasswordPromise = _authenticationManager.ResetPassword(model);

			return ApiResult(resetPasswordPromise);
		}

		private string GetHostUrl(string scheme, string host) {
			return $"{scheme}://{host}";
		}
	}
}