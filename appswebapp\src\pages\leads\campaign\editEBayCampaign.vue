<template>
  <div v-if='!isLoading && !isException'>
    <div class="py-2">
      <b-row>
        <b-col class="d-flex align-items-center">
          <h4 class="m-0">Edit EBay Campaign</h4>
        </b-col>
        <b-col class="d-flex justify-content-end">
          <b-btn variant="primary" @click='onSubmit' size="sm" class="mr-2 custom-btn-height" :disabled="isSubmitButtonDisabled">Submit</b-btn>
          <b-btn variant="secondary" size="sm" class="custom-btn-height" @click="$router.go(-1)">Close</b-btn>
        </b-col>
      </b-row>
    </div>
    <b-card>
      <!-- EBay Settings Section -->
      <div class="mt-1">
        <div class="border-bottom">
          <b-row>
            <b-col><h6 class="float-left">EBay Settings</h6></b-col>
          </b-row>
        </div>
        <div>
          <detail-row fixedPayloadWidth>
            <span slot="title">Lead Type:</span>
            <b-form-select v-model="ebayCommunication.leadType" text-field='label' slot="payload" :options='getLeadTypeOptions' disabled/>
          </detail-row>
        </div>
      </div>
      <!-- EBay Settings Section End -->
      <!-- Campaign Settings Section -->
      <div class="mt-4">
        <div class="border-bottom">
          <b-row>
            <b-col><h6 class="float-left">Campaign Settings</h6></b-col>
          </b-row>
        </div>
        <div>
          <detail-row fixedPayloadWidth>
            <span slot="title">Campaign(s):</span>
            <multiselect slot="payload"
              v-model="ebayCommunication.campaignTypes"
              track-by="campaignTypeId"
              :options='ebayCommunication.campaignTypes'
              label='campaignName'
              :multiple="true"
              :showLabels='false'
              disabled
              :limit='3'
            />
          </detail-row>
          <detail-row fixedPayloadWidth>
            <span slot="title">Contacts:</span>
            <multiselect slot="payload"
              v-model='ebayCommunication.contacts'
              :options='getContactsGroup()'
              group-values="data"
              group-label="groupName"
              track-by="contactId"
              label="email"
              :closeOnSelect='false'
              :group-select="true"
              :multiple="true"
              :showLabels='false'
              :showPointer='false'
              :limit='3'
            />
          </detail-row>
          <detail-row fixedPayloadWidth>
            <span slot="title">Legacy Campaign Id:</span>
            <b-form-select v-model="ebayCommunication.legacyCampaignId" :options="availableLegacyCampaignIds" slot="payload" disabled/>
          </detail-row>
        </div>
      </div>
      <!-- Campaign Settings Section End -->
      <!-- Tracking & Notification Settings Section -->
      <div class="mt-4">
        <div class="border-bottom">
          <b-row>
            <b-col><h6 class="float-left">Tracking & Notification Settings</h6></b-col>
          </b-row>
        </div>
        <div>
          <div class="d-flex flex-column mt-2">
            <b-form-checkbox v-model='ebayCommunication.notificationSettings.hasToSendEmailToFormContact' class="mt-3">Send Email to Displayed Contact</b-form-checkbox>
            <b-form-checkbox v-model='ebayCommunication.notificationSettings.hasToSendUserEmail' class="mt-3">Send Auto Response Email to Lead</b-form-checkbox>
            <b-form-checkbox v-if="ebayCommunication.notificationSettings.hasToShowDealerSocketSettings" v-model='ebayCommunication.notificationSettings.hasToSendDealerSocketLead' class="mt-3">Send Dealer Socket Lead</b-form-checkbox>
            <b-form-checkbox v-if="ebayCommunication.notificationSettings.hasToShowShiftDigitalSettings" v-model='ebayCommunication.notificationSettings.hasToSendShiftDigitalLead' class="mt-3">Send Shift Digital Lead</b-form-checkbox>
            <b-form-checkbox v-model='ebayCommunication.notificationSettings.hasToSendDealerEmail' class="mt-3">Send Notification Email to Dealership</b-form-checkbox>
            <div class="mt-3 d-flex flex-row ml-4" v-if='ebayCommunication.notificationSettings.hasToSendDealerEmail'>
              <div>
                <label class="text-muted" for="DealershipEmailTo">Dealership Emails (To):</label>
                <multi-input
                  type="email"
                  validateRules='email'
                  :values='ebayCommunication.notificationSettings.dealerEmailAddresses'
                  placeholder="<EMAIL>"
                  v-model='ebayCommunication.notificationSettings.dealerEmailAddresses'
                />
              </div>
              <div class="ml-4">
                <label class="text-muted" for="DealershipEmailCC">Dealership Emails (CC):</label>
                <multi-input
                  type="email"
                  validateRules='email'
                  :values='ebayCommunication.notificationSettings.dealerEmailAddressesCc'
                  placeholder="<EMAIL>"
                  v-model="ebayCommunication.notificationSettings.dealerEmailAddressesCc"
                />
              </div>
            </div>
            <b-form-checkbox v-model='ebayCommunication.notificationSettings.hasToSendAdfEmail' class="mt-3">Send ADF Email to CRM</b-form-checkbox>
            <div class="mt-3 d-flex flex-row ml-4" v-if='ebayCommunication.notificationSettings.hasToSendAdfEmail'>
              <div>
                <label class="text-muted" for="ADFEmailTo">ADF Emails (To):</label>
                <multi-input
                    type="email"
                    validateRules='email'
                    :values='ebayCommunication.notificationSettings.adfEmailAddresses'
                    placeholder="<EMAIL>"
                    v-model="ebayCommunication.notificationSettings.adfEmailAddresses"
                  />
              </div>
              <div class="ml-4">
                <label class="text-muted" for="ADFEmailCC">ADF Emails (CC):</label>
                <multi-input
                    type="email"
                    validateRules='email'
                    :values='ebayCommunication.notificationSettings.adfEmailAddressesCc'
                    placeholder="<EMAIL>"
                    v-model="ebayCommunication.notificationSettings.adfEmailAddressesCc"
                  />
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- Tracking & Notification Settings Section End -->
    </b-card>
  </div>
  <div v-else-if='isLoading && !isException' class="mt-5">
    <loader size="lg"/>
  </div>
  <div v-else class="mt-5">
    <h4 class="text-center text-muted">Something went wrong! Please reload page</h4>
  </div>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import CommunicationService from '@/services/leads/CommunicationService'
import Multiselect from 'vue-multiselect'
import multiInput from '@/components/_shared/multiInput'
import loader from '@/components/_shared/loader'
import { leadType } from '@/shared/leads/common'

export default {
  name: 'leads-add-edit-ebay-campaign',
  props: {
    accountId: { type: Number, required: true },
    communicationId: { type: String, required: true }
  },
  data () {
    return {
      isLoading: true,
      isSubmitButtonDisabled: false,
      isException: false,
      ebayCommunication: null,
      availableLegacyCampaignIds: [],
      availableContacts: []
    }
  },
  created () {
    this.populateData()
  },
  computed: {
    getLeadTypeOptions () {
      return leadType
    }
  },
  components: {
    'detail-row': detailRow,
    'loader': loader,
    'multi-input': multiInput,
    'multiselect': Multiselect
  },
  methods: {
    getContactsGroup () {
      let options = []
      if (this.availableContacts.length > 0) {
        this.availableContacts.forEach(contact => {
          options.push({
            groupName: contact.contactNameWithDepartments,
            data: [contact]
          })
        })
      }
      return options
    },
    populateData () {
      CommunicationService.getCommunicationSettings(this.accountId, this.communicationId).then(res => {
        this.ebayCommunication = res.data.communication
        this.availableLegacyCampaignIds = res.data.availableLegacyCampaignIds
        this.availableContacts = res.data.availableContacts
      }).catch(ex => {
        this.isException = true
        this.$toaster.exception(ex, 'Cannot get ebay campaign details model', {timeout: 5000})
        this.$logger.handleError(ex, `Cannot get Communication Details Model with id:${this.communicationId} for accountId:${this.accountId}`)
      }).finally(() => {
        this.isLoading = false
      })
    },
    onSubmit () {
      this.isSubmitButtonDisabled = true
      if (this.validate()) {
        CommunicationService.updateCommunication(this.accountId, this.ebayCommunication).then(res => {
          this.$toaster.success('Ebay Campaign Successfully Updated')
        }).catch(ex => {
          this.$toaster.exception(ex, `Cannot updated Ebay Campaign`, {timeout: 5000})
          this.$logger.handleError(ex, 'Cannot updated Ebay Campaign', this.ebayCommunication)
        }).finally(() => {
          this.isLoading = true
          this.$router.push({name: 'leads-dashboard', params: {accountId: this.accountId}})
        })
      } else {
        this.isSubmitButtonDisabled = false
      }
    },
    validate () {
      if (!this.ebayCommunication.legacyCampaignId || this.ebayCommunication.legacyCampaignId === '') {
        this.$toaster.error('Legacy Campaign Id field cannot be empty')
        return false
      }
      if (this.ebayCommunication.notificationSettings.hasToSendDealerEmail &&
          (!this.ebayCommunication.notificationSettings.dealerEmailAddresses ||
          this.ebayCommunication.notificationSettings.dealerEmailAddresses.length === 0)) {
        this.$toaster.error('Dealership Emails (To) field cannot be empty')
        return false
      }
      if (this.ebayCommunication.notificationSettings.hasToSendAdfEmail &&
          (!this.ebayCommunication.notificationSettings.adfEmailAddresses ||
          this.ebayCommunication.notificationSettings.adfEmailAddresses.length === 0)) {
        this.$toaster.error('ADF Emails (To) field cannot be empty')
        return false
      }

      return true
    }
  }
}
</script>

<style>
.custom-btn-height {
  height: 35px;
}

.multiselect__option--group {
  font-size: 12px !important;
  padding-top: 12px !important;
  padding-left: 4px !important;
}
</style>
