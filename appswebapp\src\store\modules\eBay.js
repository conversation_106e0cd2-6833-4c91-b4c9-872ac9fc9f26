import axios from 'axios'

const actions = {
  getAccounts (_, parameters) {
    return axios.get(`/api/ebay/accounts`, { params: parameters.filters })
  },
  getInventoryData (_, parameters) {
    return axios.get(`/api/ebay/${parameters.accountId}/inventory`, { params: parameters.filters })
  },
  checkAccountManagedByApps (_, parameters) {
    return axios.get(`/api/ebay/${parameters.accountId}/managed`)
  },
  getAccountSettings (_, accountId) {
    return axios.get(`/api/ebay/${accountId}/settings`)
  },
  getAutomatedSettings (_, accountId) {
    return axios.get(`/api/ebay/${accountId}/automated`)
  },
  getEBayUserInfo (_, parameters) {
    return axios.get(`/api/ebay/${parameters.accountId}/user/${parameters.contactId}/`)
  },
  getAvailableContacts (_, parameters) {
    return axios.get(`/api/ebay/${parameters.accountId}/contacts/available`)
  },
  getErrorsAndScheduledItems (_, filters) {
    return axios.get(`/api/ebay/admin/errors_and_scheduled`, { params: filters })
  },
  getPostItems (_, parameters) {
    return axios.get(`/api/ebay/${parameters.accountId}/posts`, { params: parameters.filters })
  },
  getEBayHistory (_, parameters) {
    return axios.get(`/api/ebay/${parameters.accountId}/posts/${parameters.vin}/history`)
  },
  getAuctionToEnd (_, parameters) {
    return axios.get(`/api/ebay/${parameters.accountId}/inventory/${parameters.auctionId}/end`)
  },
  getAuctionBestOffers (_, parameters) {
    return axios.get(`/api/ebay/${parameters.accountId}/inventory/${parameters.auctionId}/offers`)
  },
  getUserEBaySignInUrl (_, parameters) {
    return axios.get(
      `/api/ebay/${parameters.accountId}/user/${parameters.contactId}/signinurl`,
      {
        params: {
          redirectionUrl: parameters.redirectionUrl
        }
      }
    )
  },
  updateDepositSettings (_, parameters) {
    return axios.post(`/api/ebay/${parameters.accountId}/settings/deposit`, parameters.data)
  },
  updateGlobalDefaultSettings (_, parameters) {
    return axios.post(`/api/ebay/${parameters.accountId}/settings/global_default`, parameters.data)
  },
  updatePayPalSettings (_, parameters) {
    return axios.post(`/api/ebay/${parameters.accountId}/settings/paypal`, parameters.data)
  },
  updateShippingSettings (_, parameters) {
    return axios.post(`/api/ebay/${parameters.accountId}/settings/shipping`, parameters.data)
  },
  updateAccountPaymentSettings (_, parameters) {
    return axios.post(`/api/ebay/${parameters.accountId}/settings/payment`, parameters.data)
  },
  updateAccountAdminSettings (_, parameters) {
    return axios.post(`/api/ebay/${parameters.accountId}/settings/admin`, parameters.data)
  },
  updateEBayUserInfo (_, parameters) {
    return axios.post(`/api/ebay/${parameters.accountId}/user/${parameters.contactId}/login_info/update`, parameters.data)
  },
  updateAutomatedActivatedSettings (_, parameters) {
    return axios.post(`/api/ebay/${parameters.accountId}/automated/activate`, parameters.data)
  },
  updateAutomatedAuctionListingSettings (_, parameters) {
    return axios.post(`/api/ebay/${parameters.accountId}/automated/auction_listings`, parameters.data)
  },
  updateAutomatedClassifiedListingsSettings (_, parameters) {
    return axios.post(`/api/ebay/${parameters.accountId}/automated/classified_listings`, parameters.data)
  },
  updateAutomatedFixedPriceListingsSettings (_, parameters) {
    return axios.post(`/api/ebay/${parameters.accountId}/automated/fixed_price_listings`, parameters.data)
  },
  updateAutomatedNationalListingPreferenceSettings (_, parameters) {
    return axios.post(`/api/ebay/${parameters.accountId}/automated/national_listing_preference`, parameters.data)
  },
  synchronizeVehicle (_, parameters) {
    return axios.get(`/api/ebay/${parameters.accountId}/inventory/${parameters.vin}/synchronize`)
  },
  checkSynchronizeVehicleStatus (_, parameters) {
    return axios.get(`/api/ebay/${parameters.accountId}/inventory/${parameters.vin}/synchronize/status`, { params: parameters.filter })
  },
  ebayLaunch (_, data) {
    return axios.post(`/api/ebay/admin/scheduled/force`, data)
  },
  recordAuction (_, data) {
    return axios.post(`/api/ebay/admin/auction/record`, data)
  },
  endAuction (_, parameters) {
    return axios.post(`/api/ebay/${parameters.accountId}/inventory/${parameters.auctionId}/end`, parameters.data)
  },
  acceptBestOffer (_, parameters) {
    return axios.post(`/api/ebay/${parameters.accountId}/inventory/${parameters.auctionId}/offers/${parameters.offerId}/accept`, parameters.data)
  },
  declineBestOffer (_, parameters) {
    return axios.post(`/api/ebay/${parameters.accountId}/inventory/${parameters.auctionId}/offers/${parameters.offerId}/decline`, parameters.data)
  },
  declineBestOffers (_, parameters) {
    return axios.post(`/api/ebay/${parameters.accountId}/inventory/${parameters.auctionId}/best_offers/decline`, parameters.data)
  },
  sellVehicleToHighBidder (_, parameters) {
    return axios.post(`/api/ebay/${parameters.accountId}/inventory/${parameters.auctionId}/high_bidder/sell`, parameters.data)
  },
  reviseLocalAuctions (_, accountId) {
    return axios.get(`/api/ebay/admin/${accountId}/auctions/local/revise`)
  },
  endLocalAuctions (_, accountId) {
    return axios.get(`/api/ebay/admin/${accountId}/auctions/local/end`)
  },
  unscheduleErrorLocalItems (_, accountId) {
    return axios.get(`/api/ebay/admin/scheduling/${accountId}/error/items/local/unschedule`)
  }
}

export default {
  namespaced: true,
  actions: actions
}
