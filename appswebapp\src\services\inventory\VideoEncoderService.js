import BaseService from '../BaseService'
import store from '@/store'
import permissions from '@/shared/common/permissions'

function getUserInfo () {
  return (store.getters['users/userInfo'] || {}).user
}

class VideoEncoderService extends BaseService {
  getTracks () {
    return this.axios.get('/api/inventory/video_encoder/track/listing')
  };
  getPhotoToVideoQueueListing (filters) {
    return this.axios.get('/api/inventory/video_encoder/queue/photo_to_video', { params: filters })
  };
  getTextConversionListing (filters) {
    return this.axios.get('/api/inventory/video_encoder/text_conversion/listing', { params: filters })
  };
  getTextConversion (id) {
    return this.axios.get(`/api/inventory/video_encoder/text_conversion/${id}`)
  }
  createTextConversion (textConversion) {
    return this.axios.post('/api/inventory/video_encoder/text_conversion/create', textConversion)
  };
  updateTextConversion (id, textConversion) {
    return this.axios.post(`/api/inventory/video_encoder/text_conversion/${id}/update`, textConversion)
  };
  deleteTextConversion (id) {
    return this.axios.post(`/api/inventory/video_encoder/text_conversion/${id}/delete`)
  };
  getPhotoToVideoSettings () {
    return this.axios.get('/api/inventory/services/photo_to_video/settings')
  };
  updatePhotoToVideoSettings (settings) {
    return this.axios.post('/api/inventory/services/photo_to_video/settings', settings)
  };
  getAccountPhotoToVideoSettings (accountId) {
    return this.axios.get(`/api/inventory/${accountId}/settings/video`)
  };
  updateAccountPhotoToVideoDescriptionSettings (accountId, settings) {
    return this.axios.post(`/api/inventory/${accountId}/settings/photo_to_video/description_text`, settings)
  }
  updateAccountPhotoToVideoMusicSettings (accountId, settings) {
    return this.axios.post(`/api/inventory/${accountId}/settings/photo_to_video/music`, settings)
  }
  updateAccountPhotoToVideoVoiceSettings (accountId, settings) {
    return this.axios.post(`/api/inventory/${accountId}/settings/photo_to_video/voice`, settings)
  }
  async updateAccountPhotoToVideoActivationSettings (accountId, settings) {
    const user = getUserInfo()
    if (user.hasPermissions(permissions.IMFullAccess)) {
      await this.updateAccountPhotoToVideoEncodingStatusInApps(accountId, settings)
    }
    return this.axios.post(`/api/inventory/${accountId}/settings/photo_to_video/activation`, settings)
  }
  updateAccountPhotoToVideoEncodingStatusInApps (accountId, settings) {
    return this.axios.post(
      `/api/inventory/${accountId}/settings/photo_to_video/activation/encoding_status`,
      {
        activationStatus: settings.activationStatus
      }
    )
  }
  downloadExampleVoiceSound (accountId, voiceType) {
    return this.axios.get(`/api/inventory/${accountId}/photo_to_video/voice/download`, { params: {voiceType: voiceType} })
  };
  downloadTrack (trackId) {
    return this.axios.get(`/api/inventory/video_encoder/track/${trackId}/download`)
  };
  downloadAccountSoundtrack (accountId) {
    return this.axios.get(`/api/inventory/${accountId}/photo_to_video/custom_soundtrack/download`)
  };
  uploadAccountSoundtrack (accountId, formData) {
    return this.axios.post(`/api/inventory/${accountId}/photo_to_video/custom_soundtrack/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  };
  getPhotoToVideoProcessingLogs (filters) {
    return this.axios.get(`/api/inventory/logs/video_encoder/photo_to_video/vehicle_processing`, { params: filters })
  };
  getPhotoToVideoQueueMangerLogs (filters) {
    return this.axios.get(`/api/inventory/logs/video_encoder/photo_to_video/queue_manager`, { params: filters })
  };
  getPhotoToVideoProcessingLogDetails (id) {
    return this.axios.get(`/api/inventory/logs/video_encoder/photo_to_video/vehicle_processing/${id}/details`)
  };
  getPhotoToVideoQueueMangerLogDetails (id) {
    return this.axios.get(`/api/inventory/logs/video_encoder/photo_to_video/queue_manager/${id}/details`)
  };
}

export default new VideoEncoderService()
