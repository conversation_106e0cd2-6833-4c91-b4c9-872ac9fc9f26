import vehicleManagementService from './../../services/inventory/VehicleManagementService'
import VehicleDifferenceBuilder from './../../shared/details/vehicleDifferenceBuilder'

export default {
  methods: {
    async updateVehicle (oldVehicle, newVehicle) {
      let diff = VehicleDifferenceBuilder.build(oldVehicle, newVehicle)
      try {
        return await vehicleManagementService.updateVehicle(oldVehicle.accountId, diff, 2)
      } catch (e) {
        if (e.response.data !== '' && e.response.status !== 500) {
          this.$toaster.error(e.response.data, {timeout: 8000})
        } else {
          this.$toaster.error("An error occurred. Can't save the vehicle.", {timeout: 8000})
        }
        this.$logger.handleError(e, `Can't update the vehicle.`, diff)
      }
    },
    async updateDisplayedSections (oldVehicle, newVehicle) {
      let diff = VehicleDifferenceBuilder.build(oldVehicle, newVehicle)

      try {
        let response = await vehicleManagementService.updateVehicle(oldVehicle.accountId, {
          featureCategories: diff.featureCategories,
          isStandardFeaturesTurnedOn: diff.isStandardFeaturesTurnedOn,
          isOptionsPackagesTurnedOn: diff.isOptionsPackagesTurnedOn,
          isFeaturesSpecificationsTurnedOn: diff.isFeaturesSpecificationsTurnedOn,
          vin: oldVehicle.vin
        }, 2)

        this.$store.commit('details/setVehicleData', response.data)
      } catch (e) {
        this.$logger.handleError(e, `Can't update vehicle display sections.`, diff)
      }
    },
    async updateVehiclePhotos (oldVehicle, newVehicle) {
      let newPhotoIndexes = VehicleDifferenceBuilder.buildPhotosDifference(oldVehicle, newVehicle)

      if (!newPhotoIndexes) {
        return
      }

      try {
        let response = await vehicleManagementService.updateVehiclePhotoOrder(newVehicle.accountId, newVehicle.vin, newPhotoIndexes, 2)

        this.$store.commit('details/setVehicleData', response.data)
      } catch (e) {
        this.$logger.handleError(e, 'Can\'t update vehicle photos', {accountId: newVehicle.accountId, vin: newVehicle.vin, newPhotosIndexes: newPhotoIndexes})
        this.$toaster.error(`Can't update vehicle photos`)
      }
    }
  }
}
