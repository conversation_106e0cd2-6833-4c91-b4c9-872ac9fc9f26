import { ObjectSchema } from '../common/objectHelpers'
import constants from './constants'

export default new ObjectSchema({
  condition: { type: Number, default: 0 },
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  sort: { type: Number, default: constants.inventorySortTypes.daysInStockAsc },
  vehicleType: { type: Number, default: 0 },
  bodyStyle: { type: Number, default: 0 },
  make: { type: String, default: '' },
  model: { type: String, default: '' },
  daysFrom: { type: Number, default: -1 },
  daysTo: { type: Number, default: -1 },
  priceFrom: { type: Number, default: -1 },
  priceTo: { type: Number, default: -1 },
  mileageFrom: { type: Number, default: -1 },
  mileageTo: { type: Number, default: -1 },
  photosFrom: { type: Number, default: -1 },
  search: { type: String, default: '' },
  status: { type: Number, default: 0 }
})
