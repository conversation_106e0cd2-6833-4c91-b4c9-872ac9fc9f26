{"runtimeTarget": {"name": ".NETCoreApp,Version=v2.1", "signature": ""}, "compilationOptions": {"defines": ["TRACE", "DEBUG", "NETCOREAPP", "NETCOREAPP2_1", "NETCOREAPP1_0_OR_GREATER", "NETCOREAPP1_1_OR_GREATER", "NETCOREAPP2_0_OR_GREATER", "NETCOREAPP2_1_OR_GREATER"], "languageVersion": "7.3", "platform": "", "allowUnsafe": false, "warningsAsErrors": false, "optimize": false, "keyFile": "", "emitEntryPoint": true, "xmlDoc": true, "debugType": "full"}, "targets": {".NETCoreApp,Version=v2.1": {"EBizAutos.Apps.Shared.Api/1.0.0": {"dependencies": {"EBizAutos.Apps.Authentication.MongoDbRepository": "1.0.0", "EBizAutos.Apps.Common.MongoDbRepository": "1.0.0", "EBizAutos.Apps.CommonLib": "1.0.0", "EBizAutos.Apps.MongoDbRepository": "1.0.0", "EBizAutos.Apps.ServiceBus": "1.0.0", "FluentValidation.AspNetCore": "9.5.4", "HtmlAgilityPack": "1.11.46", "Jurassic": "3.2.6", "Mapster": "7.3.0", "Microsoft.AspNetCore.All": "2.1.6", "Microsoft.NETCore.App": "2.1.0", "CommonLibCore.Reference": "*******", "FoundationCommonLib.Reference": "*******"}, "runtime": {"EBizAutos.Apps.Shared.Api.dll": {}}, "compile": {"EBizAutos.Apps.Shared.Api.dll": {}}}, "Amazon.AspNetCore.DataProtection.SSM/1.1.0": {"dependencies": {"AWSSDK.Extensions.NETCore.Setup": "3.7.1", "AWSSDK.SimpleSystemsManagement": "3.7.3.24", "Microsoft.AspNetCore.DataProtection.Extensions": "2.1.1"}, "runtime": {"lib/netstandard2.0/Amazon.AspNetCore.DataProtection.SSM.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.1.0.0"}}, "compile": {"lib/netstandard2.0/Amazon.AspNetCore.DataProtection.SSM.dll": {}}}, "Amazon.Extensions.Configuration.SystemsManager/2.1.1": {"dependencies": {"AWSSDK.Extensions.NETCore.Setup": "3.7.1", "AWSSDK.SimpleSystemsManagement": "3.7.3.24", "Microsoft.Extensions.Configuration": "2.1.1", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Amazon.Extensions.Configuration.SystemsManager.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Amazon.Extensions.Configuration.SystemsManager.dll": {}}}, "Apache.NMS/2.0.0": {"runtime": {"lib/netstandard2.0/Apache.NMS.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Apache.NMS.dll": {}}}, "Apache.NMS.ActiveMQ/2.0.0": {"dependencies": {"Apache.NMS": "2.0.0", "SharpZipLib": "1.3.3", "System.Runtime": "4.3.1"}, "runtime": {"lib/netstandard2.0/Apache.NMS.ActiveMQ.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Apache.NMS.ActiveMQ.dll": {}}}, "Automatonymous/5.1.3": {"dependencies": {"GreenPipes": "4.0.1"}, "runtime": {"lib/netstandard2.0/Automatonymous.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Automatonymous.dll": {}}}, "AWSSDK.Core/**********": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0"}, "runtime": {"lib/netstandard2.0/AWSSDK.Core.dll": {"assemblyVersion": "*******", "fileVersion": "**********"}}, "compile": {"lib/netstandard2.0/AWSSDK.Core.dll": {}}}, "AWSSDK.Extensions.NETCore.Setup/3.7.1": {"dependencies": {"AWSSDK.Core": "**********", "Microsoft.Extensions.Configuration.Abstractions": "2.1.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1"}, "runtime": {"lib/netstandard2.0/AWSSDK.Extensions.NETCore.Setup.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/AWSSDK.Extensions.NETCore.Setup.dll": {}}}, "AWSSDK.S3/**********": {"dependencies": {"AWSSDK.Core": "**********"}, "runtime": {"lib/netstandard2.0/AWSSDK.S3.dll": {"assemblyVersion": "*******", "fileVersion": "**********"}}, "compile": {"lib/netstandard2.0/AWSSDK.S3.dll": {}}}, "AWSSDK.SecurityToken/3.7.100.14": {"dependencies": {"AWSSDK.Core": "**********"}, "runtime": {"lib/netstandard2.0/AWSSDK.SecurityToken.dll": {"assemblyVersion": "*******", "fileVersion": "3.7.100.14"}}, "compile": {"lib/netstandard2.0/AWSSDK.SecurityToken.dll": {}}}, "AWSSDK.SimpleSystemsManagement/3.7.3.24": {"dependencies": {"AWSSDK.Core": "**********"}, "runtime": {"lib/netstandard2.0/AWSSDK.SimpleSystemsManagement.dll": {"assemblyVersion": "*******", "fileVersion": "3.7.3.24"}}, "compile": {"lib/netstandard2.0/AWSSDK.SimpleSystemsManagement.dll": {}}}, "Castle.Core/4.4.1": {"dependencies": {"NETStandard.Library": "2.0.3", "System.Collections.Specialized": "4.3.0", "System.ComponentModel": "4.3.0", "System.ComponentModel.TypeConverter": "4.3.0", "System.Diagnostics.TraceSource": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.TypeExtensions": "4.4.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard1.5/Castle.Core.dll": {"assemblyVersion": "*******", "fileVersion": "4.4.1.0"}}, "compile": {"lib/netstandard1.5/Castle.Core.dll": {}}}, "CompareNETObjects/4.57.0": {"dependencies": {"Microsoft.CSharp": "4.5.0"}, "runtime": {"lib/netstandard2.0/KellermanSoftware.Compare-NET-Objects.dll": {"assemblyVersion": "4.57.0.0", "fileVersion": "4.57.0.0"}}, "compile": {"lib/netstandard2.0/KellermanSoftware.Compare-NET-Objects.dll": {}}}, "Dapper/1.50.5": {"dependencies": {"System.Data.SqlClient": "4.6.0", "System.Reflection.Emit.Lightweight": "4.7.0", "System.Reflection.TypeExtensions": "4.4.0"}, "runtime": {"lib/netstandard2.0/Dapper.dll": {"assemblyVersion": "1.50.5.0", "fileVersion": "1.50.5.0"}}, "compile": {"lib/netstandard2.0/Dapper.dll": {}}}, "DeepCloner/0.10.2": {"dependencies": {"NETStandard.Library": "2.0.3"}, "runtime": {"lib/netstandard1.3/DeepCloner.dll": {"assemblyVersion": "0.10.0.0", "fileVersion": "0.10.2.0"}}, "compile": {"lib/netstandard1.3/DeepCloner.dll": {}}}, "DnsClient/1.6.1": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0", "System.Buffers": "4.5.1"}, "runtime": {"lib/netstandard2.0/DnsClient.dll": {"assemblyVersion": "1.6.1.0", "fileVersion": "1.6.1.0"}}, "compile": {"lib/netstandard2.0/DnsClient.dll": {}}}, "Elasticsearch.Net/7.1.0": {"dependencies": {"System.Buffers": "4.5.1", "System.Diagnostics.DiagnosticSource": "4.7.1", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.Lightweight": "4.7.0"}, "runtime": {"lib/netstandard2.0/Elasticsearch.Net.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.1.0.0"}}, "compile": {"lib/netstandard2.0/Elasticsearch.Net.dll": {}}}, "EnyimMemcachedCore/2.1.8": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.1", "Microsoft.Extensions.Caching.Abstractions": "2.1.2", "Microsoft.Extensions.Configuration.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "2.1.1", "Newtonsoft.Json": "13.0.1", "Newtonsoft.Json.Bson": "1.0.2"}, "runtime": {"lib/netstandard2.0/EnyimMemcachedCore.dll": {"assemblyVersion": "2.1.8.0", "fileVersion": "2.1.8.0"}}, "compile": {"lib/netstandard2.0/EnyimMemcachedCore.dll": {}}}, "Experimental.System.Messaging/1.0.0": {"runtime": {"lib/netstandard2.0/Experimental.System.Messaging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Experimental.System.Messaging.dll": {}}}, "FluentValidation/9.5.4": {"runtime": {"lib/netstandard2.0/FluentValidation.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/FluentValidation.dll": {}}}, "FluentValidation.AspNetCore/9.5.4": {"dependencies": {"FluentValidation": "9.5.4", "FluentValidation.DependencyInjectionExtensions": "9.5.4", "Microsoft.AspNetCore.Mvc.ViewFeatures": "2.1.3"}, "runtime": {"lib/netcoreapp2.1/FluentValidation.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netcoreapp2.1/FluentValidation.AspNetCore.dll": {}}}, "FluentValidation.DependencyInjectionExtensions/9.5.4": {"dependencies": {"FluentValidation": "9.5.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1"}, "runtime": {"lib/netstandard2.0/FluentValidation.DependencyInjectionExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/FluentValidation.DependencyInjectionExtensions.dll": {}}}, "GreenPipes/4.0.1": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "System.Reflection.Emit": "4.7.0"}, "runtime": {"lib/netstandard2.0/GreenPipes.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/GreenPipes.dll": {}}}, "HtmlAgilityPack/1.11.46": {"runtime": {"lib/netstandard2.0/HtmlAgilityPack.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}, "compile": {"lib/netstandard2.0/HtmlAgilityPack.dll": {}}}, "Jurassic/3.2.6": {"dependencies": {"System.Reflection.Emit.Lightweight": "4.7.0"}, "runtime": {"lib/netstandard2.0/Jurassic.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Jurassic.dll": {}}}, "Magick.NET-Q16-AnyCPU/7.11.0": {"runtime": {"lib/netstandard20/Magick.NET-Q16-AnyCPU.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "runtimeTargets": {"runtimes/linux-x64/native/Magick.NET-Q16-x64.Native.dll.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/Magick.NET-Q16-x64.Native.dll.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/Magick.NET-Q16-x64.Native.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "********"}, "runtimes/win-x86/native/Magick.NET-Q16-x86.Native.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "********"}}, "compile": {"lib/netstandard20/Magick.NET-Q16-AnyCPU.dll": {}}}, "Mapster/7.3.0": {"dependencies": {"Mapster.Core": "1.2.0", "Microsoft.CSharp": "4.5.0", "System.Reflection.Emit": "4.7.0"}, "runtime": {"lib/netstandard2.0/Mapster.dll": {"assemblyVersion": "7.3.0.0", "fileVersion": "7.3.0.0"}}, "compile": {"lib/netstandard2.0/Mapster.dll": {}}}, "Mapster.Core/1.2.0": {"runtime": {"lib/netstandard2.0/Mapster.Core.dll": {"assemblyVersion": "1.2.0.0", "fileVersion": "1.2.0.0"}}, "compile": {"lib/netstandard2.0/Mapster.Core.dll": {}}}, "MassTransit/7.3.1": {"dependencies": {"Automatonymous": "5.1.3", "Microsoft.Bcl.AsyncInterfaces": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "NewId": "3.0.3", "Newtonsoft.Json": "13.0.1", "Newtonsoft.Json.Bson": "1.0.2", "System.Diagnostics.DiagnosticSource": "4.7.1", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.Lightweight": "4.7.0", "System.Text.Json": "5.0.2", "System.Threading.Channels": "4.7.1", "System.ValueTuple": "4.5.0"}, "runtime": {"lib/netstandard2.0/MassTransit.dll": {"assemblyVersion": "7.3.1.0", "fileVersion": "7.3.1.0"}}, "compile": {"lib/netstandard2.0/MassTransit.dll": {}}}, "MassTransit.ActiveMQ/7.3.3": {"dependencies": {"Apache.NMS.ActiveMQ": "2.0.0", "MassTransit": "7.3.1"}, "runtime": {"lib/netstandard2.0/MassTransit.ActiveMqTransport.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/MassTransit.ActiveMqTransport.dll": {}}}, "MassTransit.Extensions.DependencyInjection/7.3.1": {"dependencies": {"MassTransit": "7.3.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1"}, "runtime": {"lib/netstandard2.0/MassTransit.ExtensionsDependencyInjectionIntegration.dll": {"assemblyVersion": "7.3.1.0", "fileVersion": "7.3.1.0"}}, "compile": {"lib/netstandard2.0/MassTransit.ExtensionsDependencyInjectionIntegration.dll": {}}}, "MassTransit.MongoDb/7.3.1": {"dependencies": {"MassTransit": "7.3.1", "MongoDB.Driver": "2.21.0", "MongoDB.Driver.GridFS": "2.21.0"}, "runtime": {"lib/netstandard2.0/MassTransit.MongoDbIntegration.dll": {"assemblyVersion": "7.3.1.0", "fileVersion": "7.3.1.0"}}, "compile": {"lib/netstandard2.0/MassTransit.MongoDbIntegration.dll": {}}}, "Microsoft.Bcl.AsyncInterfaces/5.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "compile": {"lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll": {}}}, "Microsoft.IdentityModel.Logging/5.3.0": {"dependencies": {"System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "5.3.0.51005"}}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll": {}}}, "Microsoft.IdentityModel.Tokens/5.3.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "5.3.0", "Newtonsoft.Json": "13.0.1", "System.Collections": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Runtime.Serialization.Xml": "4.3.0", "System.Security.Claims": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "5.3.0.51005"}}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll": {}}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "compile": {"ref/netstandard2.0/Microsoft.Win32.Registry.dll": {}}}, "Microsoft.Win32.SystemEvents/4.5.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "MongoDB.Bson/2.21.0": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "runtime": {"lib/netstandard2.0/MongoDB.Bson.dll": {"assemblyVersion": "2.21.0.0", "fileVersion": "2.21.0.0"}}, "compile": {"lib/netstandard2.0/MongoDB.Bson.dll": {}}}, "MongoDB.Driver/2.21.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "2.1.1", "MongoDB.Bson": "2.21.0", "MongoDB.Driver.Core": "2.21.0", "MongoDB.Libmongocrypt": "1.8.0"}, "runtime": {"lib/netstandard2.0/MongoDB.Driver.dll": {"assemblyVersion": "2.21.0.0", "fileVersion": "2.21.0.0"}}, "compile": {"lib/netstandard2.0/MongoDB.Driver.dll": {}}}, "MongoDB.Driver.Core/2.21.0": {"dependencies": {"AWSSDK.SecurityToken": "3.7.100.14", "DnsClient": "1.6.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "MongoDB.Bson": "2.21.0", "MongoDB.Libmongocrypt": "1.8.0", "SharpCompress": "0.30.1", "Snappier": "1.0.0", "System.Buffers": "4.5.1", "ZstdSharp.Port": "0.6.2"}, "runtime": {"lib/netstandard2.0/MongoDB.Driver.Core.dll": {"assemblyVersion": "2.21.0.0", "fileVersion": "2.21.0.0"}}, "compile": {"lib/netstandard2.0/MongoDB.Driver.Core.dll": {}}}, "MongoDB.Driver.GridFS/2.21.0": {"dependencies": {"MongoDB.Bson": "2.21.0", "MongoDB.Driver": "2.21.0", "MongoDB.Driver.Core": "2.21.0"}, "runtime": {"lib/netstandard2.0/MongoDB.Driver.GridFS.dll": {"assemblyVersion": "2.21.0.0", "fileVersion": "2.21.0.0"}}, "compile": {"lib/netstandard2.0/MongoDB.Driver.GridFS.dll": {}}}, "MongoDB.Libmongocrypt/1.8.0": {"runtime": {"lib/netstandard2.0/MongoDB.Libmongocrypt.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}}, "runtimeTargets": {"runtimes/linux/native/libmongocrypt.so": {"rid": "linux", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx/native/libmongocrypt.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win/native/mongocrypt.dll": {"rid": "win", "assetType": "native", "fileVersion": "0.0.0.0"}}, "compile": {"lib/netstandard2.0/MongoDB.Libmongocrypt.dll": {}}}, "MongolianBarbecue/1.0.0": {"dependencies": {"MongoDB.Driver": "2.21.0"}, "runtime": {"lib/netstandard2.0/MongolianBarbecue.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/MongolianBarbecue.dll": {}}}, "NEST/7.1.0": {"dependencies": {"Elasticsearch.Net": "7.1.0"}, "runtime": {"lib/netstandard2.0/Nest.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.1.0.0"}}, "compile": {"lib/netstandard2.0/Nest.dll": {}}}, "NewId/3.0.3": {"runtime": {"lib/netstandard2.0/NewId.dll": {"assemblyVersion": "3.0.3.0", "fileVersion": "3.0.3.0"}}, "compile": {"lib/netstandard2.0/NewId.dll": {}}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.1.25517"}}, "compile": {"lib/netstandard2.0/Newtonsoft.Json.dll": {}}}, "Newtonsoft.Json.Bson/1.0.2": {"dependencies": {"Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.2.22727"}}, "compile": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {}}}, "runtime.native.System.Data.SqlClient.sni/4.5.0": {"dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "Scrutor/3.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1", "Microsoft.Extensions.DependencyModel": "2.1.0"}, "runtime": {"lib/netstandard2.0/Scrutor.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Scrutor.dll": {}}}, "SharpCompress/0.30.1": {"dependencies": {"System.Memory": "4.5.5", "System.Text.Encoding.CodePages": "5.0.0"}, "runtime": {"lib/netstandard2.0/SharpCompress.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "compile": {"lib/netstandard2.0/SharpCompress.dll": {}}}, "SharpZipLib/1.3.3": {"runtime": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "compile": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {}}}, "Snappier/1.0.0": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Snappier.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Snappier.dll": {}}}, "Swashbuckle.AspNetCore/4.0.1": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "4.0.1", "Swashbuckle.AspNetCore.SwaggerGen": "4.0.1", "Swashbuckle.AspNetCore.SwaggerUI": "4.0.1"}, "runtime": {"lib/netstandard2.0/Swashbuckle.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Swashbuckle.AspNetCore.dll": {}}}, "Swashbuckle.AspNetCore.Annotations/4.0.1": {"dependencies": {"Swashbuckle.AspNetCore.SwaggerGen": "4.0.1"}, "runtime": {"lib/netstandard2.0/Swashbuckle.AspNetCore.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Swashbuckle.AspNetCore.Annotations.dll": {}}}, "Swashbuckle.AspNetCore.Filters/4.5.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "2.1.1", "Scrutor": "3.0.2", "Swashbuckle.AspNetCore.Annotations": "4.0.1", "Swashbuckle.AspNetCore.SwaggerGen": "4.0.1"}, "runtime": {"lib/netstandard2.0/Swashbuckle.AspNetCore.Filters.dll": {"assemblyVersion": "4.5.5.0", "fileVersion": "4.5.5.0"}}, "compile": {"lib/netstandard2.0/Swashbuckle.AspNetCore.Filters.dll": {}}}, "Swashbuckle.AspNetCore.Swagger/4.0.1": {"dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.1.3", "Microsoft.AspNetCore.Mvc.Formatters.Json": "2.1.3"}, "runtime": {"lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.dll": {}}}, "Swashbuckle.AspNetCore.SwaggerGen/4.0.1": {"dependencies": {"Microsoft.AspNetCore.Mvc.ApiExplorer": "2.1.3", "Microsoft.AspNetCore.Mvc.Core": "2.1.3", "Microsoft.AspNetCore.Mvc.DataAnnotations": "2.1.3", "Swashbuckle.AspNetCore.Swagger": "4.0.1"}, "runtime": {"lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {}}}, "Swashbuckle.AspNetCore.SwaggerUI/4.0.1": {"dependencies": {"Microsoft.AspNetCore.Routing": "2.1.1", "Microsoft.AspNetCore.StaticFiles": "2.1.1", "Microsoft.Extensions.FileProviders.Embedded": "2.1.1", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {}}}, "System.Buffers/4.5.1": {}, "System.ComponentModel/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.ComponentModel.Primitives/4.3.0": {"dependencies": {"System.ComponentModel": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1"}}, "System.ComponentModel.TypeConverter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Collections.NonGeneric": "4.3.0", "System.Collections.Specialized": "4.3.0", "System.ComponentModel": "4.3.0", "System.ComponentModel.Primitives": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.4.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Configuration.ConfigurationManager/4.5.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "4.5.0", "System.Security.Permissions": "4.5.0"}, "runtime": {"lib/netstandard2.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}, "compile": {"ref/netstandard2.0/System.Configuration.ConfigurationManager.dll": {}}}, "System.Data.SqlClient/4.6.0": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0", "System.Security.Principal.Windows": "5.0.0", "System.Text.Encoding.CodePages": "5.0.0", "runtime.native.System.Data.SqlClient.sni": "4.5.0"}, "runtime": {"lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.27110.4"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.27110.4"}, "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.27110.4"}}, "compile": {"ref/netcoreapp2.1/System.Data.SqlClient.dll": {}}}, "System.Diagnostics.DiagnosticSource/4.7.1": {"runtime": {"lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.21406"}}, "compile": {"lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll": {}}}, "System.Diagnostics.EventLog/4.5.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.Win32.Registry": "5.0.0", "System.Security.Permissions": "4.5.0", "System.Security.Principal.Windows": "5.0.0", "System.Threading.AccessControl": "4.5.0"}, "runtime": {"lib/netstandard2.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}, "compile": {"ref/netstandard2.0/System.Diagnostics.EventLog.dll": {}}}, "System.Diagnostics.TraceSource/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Drawing.Common/4.5.1": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.Win32.SystemEvents": "4.5.0"}, "runtime": {"lib/netstandard2.0/System.Drawing.Common.dll": {"assemblyVersion": "4.0.0.1", "fileVersion": "4.6.26919.2"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "4.0.0.1", "fileVersion": "4.6.26919.2"}, "runtimes/win/lib/netcoreapp2.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.0.1", "fileVersion": "4.6.26919.2"}}, "compile": {"ref/netstandard2.0/System.Drawing.Common.dll": {}}}, "System.Memory/4.5.5": {}, "System.Private.DataContractSerialization/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.7.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.4.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0", "System.Xml.XmlDocument": "4.3.0", "System.Xml.XmlSerializer": "4.3.0"}}, "System.Private.ServiceModel/4.5.3": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Reflection.DispatchProxy": "4.5.0", "System.Security.Principal.Windows": "5.0.0"}, "runtimeTargets": {"runtimes/unix/lib/netstandard2.0/System.Private.ServiceModel.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}, "runtimes/win/lib/netstandard2.0/System.Private.ServiceModel.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}}}, "System.Reflection.DispatchProxy/4.5.0": {}, "System.Reflection.Emit/4.7.0": {}, "System.Reflection.Emit.Lightweight/4.7.0": {}, "System.Reflection.TypeExtensions/4.4.0": {}, "System.Runtime/4.3.1": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.1.0"}}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"runtime": {"lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "compile": {"ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {}}}, "System.Runtime.Serialization.Xml/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Private.DataContractSerialization": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Security.AccessControl/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "compile": {"ref/netstandard2.0/System.Security.AccessControl.dll": {}}}, "System.Security.Cryptography.ProtectedData/4.5.0": {"runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.Security.Principal.Windows/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "compile": {"ref/netstandard2.0/System.Security.Principal.Windows.dll": {}}}, "System.ServiceModel.Duplex/4.5.3": {"dependencies": {"System.Private.ServiceModel": "4.5.3", "System.ServiceModel.Primitives": "4.5.3"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.Duplex.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}}, "compile": {"ref/netstandard2.0/System.ServiceModel.Duplex.dll": {}}}, "System.ServiceModel.Http/4.5.3": {"dependencies": {"System.Private.ServiceModel": "4.5.3", "System.ServiceModel.Primitives": "4.5.3"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.Http.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}}, "compile": {"ref/netstandard2.0/System.ServiceModel.Http.dll": {}}}, "System.ServiceModel.NetTcp/4.5.3": {"dependencies": {"System.Private.ServiceModel": "4.5.3", "System.ServiceModel.Primitives": "4.5.3"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.NetTcp.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}}, "compile": {"ref/netstandard2.0/System.ServiceModel.NetTcp.dll": {}}}, "System.ServiceModel.Primitives/4.5.3": {"dependencies": {"System.Private.ServiceModel": "4.5.3"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}, "lib/netstandard2.0/System.ServiceModel.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}}, "compile": {"ref/netstandard2.0/System.ServiceModel.Primitives.dll": {}, "ref/netstandard2.0/System.ServiceModel.dll": {}}}, "System.ServiceModel.Security/4.5.3": {"dependencies": {"System.Private.ServiceModel": "4.5.3", "System.ServiceModel.Primitives": "4.5.3"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.Security.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}}, "compile": {"ref/netstandard2.0/System.ServiceModel.Security.dll": {}}}, "System.ServiceProcess.ServiceController/4.5.0": {"dependencies": {"System.Diagnostics.EventLog": "4.5.0"}, "runtime": {"lib/netstandard2.0/System.ServiceProcess.ServiceController.dll": {"assemblyVersion": "4.2.1.0", "fileVersion": "4.6.26515.6"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.ServiceProcess.ServiceController.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.2.1.0", "fileVersion": "4.6.26515.6"}}, "compile": {"ref/netstandard2.0/System.ServiceProcess.ServiceController.dll": {}}}, "System.Text.Encoding.CodePages/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Text.Encoding.CodePages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "compile": {"lib/netstandard2.0/System.Text.Encoding.CodePages.dll": {}}}, "System.Text.Encodings.Web/5.0.1": {"runtime": {"lib/netstandard2.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "5.0.0.1", "fileVersion": "5.0.421.11614"}}, "compile": {"lib/netstandard2.0/System.Text.Encodings.Web.dll": {}}}, "System.Text.Json/5.0.2": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Text.Encodings.Web": "5.0.1"}, "runtime": {"lib/netstandard2.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.521.16609"}}, "compile": {"lib/netstandard2.0/System.Text.Json.dll": {}}}, "System.Threading.AccessControl/4.5.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Threading.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Threading.AccessControl.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.Threading.Channels/4.7.1": {"runtime": {"lib/netstandard2.0/System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.21406"}}, "compile": {"lib/netstandard2.0/System.Threading.Channels.dll": {}}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Xml.XmlSerializer/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.4.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}}, "TimeZoneConverter/5.0.0": {"runtime": {"lib/netstandard2.0/TimeZoneConverter.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/TimeZoneConverter.dll": {}}}, "Twilio/5.20.1": {"dependencies": {"Microsoft.IdentityModel.Logging": "5.3.0", "Microsoft.IdentityModel.Tokens": "5.3.0", "NETStandard.Library": "2.0.3", "Newtonsoft.Json": "13.0.1", "System.Collections.Specialized": "4.3.0", "System.IdentityModel.Tokens.Jwt": "5.2.0"}, "runtime": {"lib/netstandard1.4/Twilio.dll": {"assemblyVersion": "5.20.1.0", "fileVersion": "5.20.1.0"}}, "compile": {"lib/netstandard1.4/Twilio.dll": {}}}, "Twilio.AspNet.Common/5.20.1": {"dependencies": {"NETStandard.Library": "2.0.3"}, "runtime": {"lib/netstandard1.0/Twilio.AspNet.Common.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard1.0/Twilio.AspNet.Common.dll": {}}}, "Twilio.AspNet.Core/5.20.1": {"dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.1.3", "NETStandard.Library": "2.0.3", "Twilio": "5.20.1", "Twilio.AspNet.Common": "5.20.1"}, "runtime": {"lib/netstandard1.6/Twilio.AspNet.Core.dll": {"assemblyVersion": "5.20.1.0", "fileVersion": "5.20.1.0"}}, "compile": {"lib/netstandard1.6/Twilio.AspNet.Core.dll": {}}}, "UAParser/3.0.0": {"dependencies": {"NETStandard.Library": "2.0.3"}, "runtime": {"lib/netstandard1.3/UAParser.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.0.0"}}, "compile": {"lib/netstandard1.3/UAParser.dll": {}}}, "ZstdSharp.Port/0.6.2": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "System.Memory": "4.5.5"}, "runtime": {"lib/netstandard2.0/ZstdSharp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/ZstdSharp.dll": {}}}, "CommonLibCore/1.0.0": {"dependencies": {"Castle.Core": "4.4.1", "CompareNETObjects": "4.57.0", "Dapper": "1.50.5", "DeepCloner": "0.10.2", "EnyimMemcachedCore": "2.1.8", "Experimental.System.Messaging": "1.0.0", "GreenPipes": "4.0.1", "Microsoft.AspNetCore.Http.Abstractions": "2.1.1", "Microsoft.AspNetCore.WebUtilities": "2.1.1", "Microsoft.Extensions.Primitives": "2.1.6", "MongoDB.Driver": "2.21.0", "MongoDB.Driver.GridFS": "2.21.0", "MongolianBarbecue": "1.0.0", "NEST": "7.1.0", "Newtonsoft.Json": "13.0.1", "System.Configuration.ConfigurationManager": "4.5.0", "System.Data.SqlClient": "4.6.0", "System.Private.ServiceModel": "4.5.3", "System.ServiceModel.Duplex": "4.5.3", "System.ServiceModel.Http": "4.5.3", "System.ServiceModel.NetTcp": "4.5.3", "System.ServiceModel.Security": "4.5.3", "System.ServiceProcess.ServiceController": "4.5.0", "TimeZoneConverter": "5.0.0", "UAParser": "3.0.0"}, "runtime": {"CommonLibCore.dll": {"assemblyVersion": "1.0.0", "fileVersion": ""}}, "compile": {"CommonLibCore.dll": {}}}, "EBizAutos.ApplicationCommonLib/1.0.0": {"dependencies": {"AWSSDK.S3": "**********", "Amazon.Extensions.Configuration.SystemsManager": "2.1.1", "CommonLibCore": "1.0.0", "Magick.NET-Q16-AnyCPU": "7.11.0", "Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.Extensions.Configuration": "2.1.1", "Microsoft.Extensions.Configuration.Abstractions": "2.1.1", "Microsoft.Extensions.Configuration.Binder": "2.1.1", "Microsoft.Extensions.Configuration.EnvironmentVariables": "2.1.1", "Microsoft.Extensions.Configuration.FileExtensions": "2.1.1", "Microsoft.Extensions.Configuration.Json": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "System.Drawing.Common": "4.5.1"}, "runtime": {"ApplicationCommonLibCore.dll": {"assemblyVersion": "1.0.0", "fileVersion": ""}}, "compile": {"ApplicationCommonLibCore.dll": {}}}, "EBizAutos.Apps.Authentication.CommonLib/1.0.0": {"dependencies": {"Amazon.AspNetCore.DataProtection.SSM": "1.1.0", "CommonLibCore": "1.0.0", "EBizAutos.ApplicationCommonLib": "1.0.0", "EBizAutos.Apps.CommonLib": "1.0.0", "Microsoft.AspNetCore.Authentication.Cookies": "2.1.2", "Microsoft.AspNetCore.Authentication.JwtBearer": "2.1.2", "Microsoft.AspNetCore.Authorization": "2.1.2", "Microsoft.AspNetCore.DataProtection.Extensions": "2.1.1", "Microsoft.AspNetCore.Mvc": "2.1.3", "Microsoft.AspNetCore.Mvc.Core": "2.1.3", "Microsoft.IdentityModel.Tokens": "5.3.0", "System.Security.Claims": "4.3.0"}, "runtime": {"EBizAutos.Apps.Authentication.CommonLib.dll": {"assemblyVersion": "1.0.0", "fileVersion": "0.0.0.0"}}, "compile": {"EBizAutos.Apps.Authentication.CommonLib.dll": {}}}, "EBizAutos.Apps.Authentication.MongoDbRepository/1.0.0": {"dependencies": {"EBizAutos.Apps.Authentication.CommonLib": "1.0.0", "EBizAutos.Apps.CommonLib": "1.0.0"}, "runtime": {"EBizAutos.Apps.Authentication.MongoDbRepository.dll": {"assemblyVersion": "1.0.0", "fileVersion": "0.0.0.0"}}, "compile": {"EBizAutos.Apps.Authentication.MongoDbRepository.dll": {}}}, "EBizAutos.Apps.Common.MongoDbRepository/1.0.0": {"dependencies": {"EBizAutos.Apps.CommonLib": "1.0.0", "Mapster": "7.3.0"}, "runtime": {"EBizAutos.Apps.Common.MongoDbRepository.dll": {"assemblyVersion": "1.0.0", "fileVersion": "0.0.0.0"}}, "compile": {"EBizAutos.Apps.Common.MongoDbRepository.dll": {}}}, "EBizAutos.Apps.CommonLib/1.0.0": {"dependencies": {"CommonLibCore": "1.0.0", "EBizAutos.ApplicationCommonLib": "1.0.0", "FoundationCommonLib": "1.0.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.1.1", "Microsoft.AspNetCore.Routing.Abstractions": "2.1.1", "Microsoft.Extensions.DependencyInjection": "2.1.1", "Newtonsoft.Json": "13.0.1", "Swashbuckle.AspNetCore": "4.0.1", "Swashbuckle.AspNetCore.Filters": "4.5.5", "System.ComponentModel.Annotations": "4.5.0", "Twilio.AspNet.Core": "5.20.1"}, "runtime": {"EBizAutos.Apps.CommonLib.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}, "compile": {"EBizAutos.Apps.CommonLib.dll": {}}}, "EBizAutos.Apps.MongoDbRepository/1.0.0": {"dependencies": {"EBizAutos.Apps.CommonLib": "1.0.0", "FoundationCommonLib": "1.0.0"}, "runtime": {"EBizAutos.Apps.MongoDbRepository.dll": {"assemblyVersion": "1.0.0", "fileVersion": "0.0.0.0"}}, "compile": {"EBizAutos.Apps.MongoDbRepository.dll": {}}}, "EBizAutos.Apps.ServiceBus/1.0.0": {"dependencies": {"CommonLibCore": "1.0.0", "EBizAutos.Apps.CommonLib": "1.0.0", "MassTransit.ActiveMQ": "7.3.3", "MassTransit.Extensions.DependencyInjection": "7.3.1", "MassTransit.MongoDb": "7.3.1", "Scrutor": "3.0.2"}, "runtime": {"EBizAutos.Apps.ServiceBus.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}, "compile": {"EBizAutos.Apps.ServiceBus.dll": {}}}, "FoundationCommonLib/1.0.0": {"dependencies": {"CommonLibCore": "1.0.0", "EBizAutos.ApplicationCommonLib": "1.0.0", "Microsoft.Extensions.Caching.Abstractions": "2.1.2", "Microsoft.Extensions.Options.ConfigurationExtensions": "2.1.1"}, "runtime": {"FoundationCommonLib.dll": {"assemblyVersion": "1.0.0", "fileVersion": ""}}, "compile": {"FoundationCommonLib.dll": {}}}, "CommonLibCore.Reference/*******": {"runtime": {"CommonLibCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"CommonLibCore.dll": {}}}, "FoundationCommonLib.Reference/*******": {"runtime": {"FoundationCommonLib.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"FoundationCommonLib.dll": {}}}, "Libuv/1.10.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0"}, "compileOnly": true}, "MessagePack/*******": {"dependencies": {"System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.Lightweight": "4.7.0", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Threading.Tasks.Extensions": "4.5.4", "System.ValueTuple": "4.5.0"}, "compile": {"lib/netstandard2.0/MessagePack.dll": {}}, "compileOnly": true}, "Microsoft.ApplicationInsights/2.4.0": {"dependencies": {"NETStandard.Library": "2.0.3", "System.Diagnostics.DiagnosticSource": "4.7.1", "System.Diagnostics.StackTrace": "4.3.0"}, "compile": {"lib/netstandard1.3/Microsoft.ApplicationInsights.dll": {}}, "compileOnly": true}, "Microsoft.ApplicationInsights.AspNetCore/2.1.1": {"dependencies": {"Microsoft.ApplicationInsights": "2.4.0", "Microsoft.ApplicationInsights.DependencyCollector": "2.4.1", "Microsoft.AspNetCore.Hosting": "2.1.1", "Microsoft.Extensions.Configuration": "2.1.1", "Microsoft.Extensions.Configuration.Json": "2.1.1", "Microsoft.Extensions.DiagnosticAdapter": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "NETStandard.Library": "2.0.3", "System.Net.NameResolution": "4.3.0", "System.Text.Encodings.Web": "5.0.1"}, "compile": {"lib/netstandard1.6/Microsoft.ApplicationInsights.AspNetCore.dll": {}}, "compileOnly": true}, "Microsoft.ApplicationInsights.DependencyCollector/2.4.1": {"dependencies": {"Microsoft.ApplicationInsights": "2.4.0", "Microsoft.Extensions.PlatformAbstractions": "1.1.0", "NETStandard.Library": "2.0.3", "System.Diagnostics.DiagnosticSource": "4.7.1", "System.Diagnostics.StackTrace": "4.3.0"}, "compile": {"lib/netstandard1.6/Microsoft.AI.DependencyCollector.dll": {}}, "compileOnly": true}, "Microsoft.AspNet.WebApi.Client/5.2.6": {"dependencies": {"Newtonsoft.Json": "13.0.1", "Newtonsoft.Json.Bson": "1.0.2"}, "compile": {"lib/netstandard2.0/System.Net.Http.Formatting.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore/2.1.6": {"dependencies": {"Microsoft.AspNetCore.Diagnostics": "2.1.1", "Microsoft.AspNetCore.HostFiltering": "2.1.1", "Microsoft.AspNetCore.Hosting": "2.1.1", "Microsoft.AspNetCore.Routing": "2.1.1", "Microsoft.AspNetCore.Server.IISIntegration": "2.1.2", "Microsoft.AspNetCore.Server.Kestrel": "2.1.3", "Microsoft.AspNetCore.Server.Kestrel.Https": "2.1.3", "Microsoft.Extensions.Configuration.CommandLine": "2.1.1", "Microsoft.Extensions.Configuration.EnvironmentVariables": "2.1.1", "Microsoft.Extensions.Configuration.FileExtensions": "2.1.1", "Microsoft.Extensions.Configuration.Json": "2.1.1", "Microsoft.Extensions.Configuration.UserSecrets": "2.1.1", "Microsoft.Extensions.Logging": "2.1.1", "Microsoft.Extensions.Logging.Configuration": "2.1.1", "Microsoft.Extensions.Logging.Console": "2.1.1", "Microsoft.Extensions.Logging.Debug": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.All/2.1.6": {"dependencies": {"Microsoft.AspNet.WebApi.Client": "5.2.6", "Microsoft.AspNetCore": "2.1.6", "Microsoft.AspNetCore.Antiforgery": "2.1.1", "Microsoft.AspNetCore.ApplicationInsights.HostingStartup": "2.1.1", "Microsoft.AspNetCore.Authentication": "2.1.2", "Microsoft.AspNetCore.Authentication.Abstractions": "2.1.1", "Microsoft.AspNetCore.Authentication.Cookies": "2.1.2", "Microsoft.AspNetCore.Authentication.Core": "2.1.1", "Microsoft.AspNetCore.Authentication.Facebook": "2.1.2", "Microsoft.AspNetCore.Authentication.Google": "2.1.2", "Microsoft.AspNetCore.Authentication.JwtBearer": "2.1.2", "Microsoft.AspNetCore.Authentication.MicrosoftAccount": "2.1.2", "Microsoft.AspNetCore.Authentication.OAuth": "2.1.2", "Microsoft.AspNetCore.Authentication.OpenIdConnect": "2.1.2", "Microsoft.AspNetCore.Authentication.Twitter": "2.1.2", "Microsoft.AspNetCore.Authentication.WsFederation": "2.1.2", "Microsoft.AspNetCore.Authorization": "2.1.2", "Microsoft.AspNetCore.Authorization.Policy": "2.1.2", "Microsoft.AspNetCore.AzureAppServices.HostingStartup": "2.1.1", "Microsoft.AspNetCore.AzureAppServicesIntegration": "2.1.1", "Microsoft.AspNetCore.Connections.Abstractions": "2.1.3", "Microsoft.AspNetCore.CookiePolicy": "2.1.2", "Microsoft.AspNetCore.Cors": "2.1.1", "Microsoft.AspNetCore.Cryptography.Internal": "2.1.1", "Microsoft.AspNetCore.Cryptography.KeyDerivation": "2.1.1", "Microsoft.AspNetCore.DataProtection": "2.1.1", "Microsoft.AspNetCore.DataProtection.Abstractions": "2.1.1", "Microsoft.AspNetCore.DataProtection.AzureKeyVault": "2.1.1", "Microsoft.AspNetCore.DataProtection.AzureStorage": "2.1.1", "Microsoft.AspNetCore.DataProtection.Extensions": "2.1.1", "Microsoft.AspNetCore.Diagnostics": "2.1.1", "Microsoft.AspNetCore.Diagnostics.Abstractions": "2.1.1", "Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore": "2.1.1", "Microsoft.AspNetCore.HostFiltering": "2.1.1", "Microsoft.AspNetCore.Hosting": "2.1.1", "Microsoft.AspNetCore.Hosting.Abstractions": "2.1.1", "Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.1.1", "Microsoft.AspNetCore.Html.Abstractions": "2.1.1", "Microsoft.AspNetCore.Http": "2.1.1", "Microsoft.AspNetCore.Http.Abstractions": "2.1.1", "Microsoft.AspNetCore.Http.Connections": "1.0.4", "Microsoft.AspNetCore.Http.Connections.Common": "1.0.4", "Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.AspNetCore.Http.Features": "2.1.1", "Microsoft.AspNetCore.HttpOverrides": "2.1.1", "Microsoft.AspNetCore.HttpsPolicy": "2.1.1", "Microsoft.AspNetCore.Identity": "2.1.6", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "2.1.6", "Microsoft.AspNetCore.Identity.UI": "2.1.6", "Microsoft.AspNetCore.JsonPatch": "2.1.1", "Microsoft.AspNetCore.Localization": "2.1.1", "Microsoft.AspNetCore.Localization.Routing": "2.1.1", "Microsoft.AspNetCore.MiddlewareAnalysis": "2.1.1", "Microsoft.AspNetCore.Mvc": "2.1.3", "Microsoft.AspNetCore.Mvc.Abstractions": "2.1.3", "Microsoft.AspNetCore.Mvc.Analyzers": "2.1.3", "Microsoft.AspNetCore.Mvc.ApiExplorer": "2.1.3", "Microsoft.AspNetCore.Mvc.Core": "2.1.3", "Microsoft.AspNetCore.Mvc.Cors": "2.1.3", "Microsoft.AspNetCore.Mvc.DataAnnotations": "2.1.3", "Microsoft.AspNetCore.Mvc.Formatters.Json": "2.1.3", "Microsoft.AspNetCore.Mvc.Formatters.Xml": "2.1.3", "Microsoft.AspNetCore.Mvc.Localization": "2.1.3", "Microsoft.AspNetCore.Mvc.Razor": "2.1.3", "Microsoft.AspNetCore.Mvc.Razor.Extensions": "2.1.2", "Microsoft.AspNetCore.Mvc.Razor.ViewCompilation": "2.1.1", "Microsoft.AspNetCore.Mvc.RazorPages": "2.1.3", "Microsoft.AspNetCore.Mvc.TagHelpers": "2.1.3", "Microsoft.AspNetCore.Mvc.ViewFeatures": "2.1.3", "Microsoft.AspNetCore.NodeServices": "2.1.1", "Microsoft.AspNetCore.Owin": "2.1.1", "Microsoft.AspNetCore.Razor": "2.1.2", "Microsoft.AspNetCore.Razor.Design": "2.1.2", "Microsoft.AspNetCore.Razor.Language": "2.1.2", "Microsoft.AspNetCore.Razor.Runtime": "2.1.2", "Microsoft.AspNetCore.ResponseCaching": "2.1.1", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "2.1.1", "Microsoft.AspNetCore.ResponseCompression": "2.1.1", "Microsoft.AspNetCore.Rewrite": "2.1.1", "Microsoft.AspNetCore.Routing": "2.1.1", "Microsoft.AspNetCore.Routing.Abstractions": "2.1.1", "Microsoft.AspNetCore.Server.HttpSys": "2.1.1", "Microsoft.AspNetCore.Server.IISIntegration": "2.1.2", "Microsoft.AspNetCore.Server.Kestrel": "2.1.3", "Microsoft.AspNetCore.Server.Kestrel.Core": "2.1.3", "Microsoft.AspNetCore.Server.Kestrel.Https": "2.1.3", "Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions": "2.1.3", "Microsoft.AspNetCore.Server.Kestrel.Transport.Libuv": "2.1.3", "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets": "2.1.3", "Microsoft.AspNetCore.Session": "2.1.1", "Microsoft.AspNetCore.SignalR": "1.0.4", "Microsoft.AspNetCore.SignalR.Common": "1.0.4", "Microsoft.AspNetCore.SignalR.Core": "1.0.4", "Microsoft.AspNetCore.SignalR.Protocols.Json": "1.0.4", "Microsoft.AspNetCore.SignalR.Redis": "1.0.4", "Microsoft.AspNetCore.SpaServices": "2.1.1", "Microsoft.AspNetCore.SpaServices.Extensions": "2.1.1", "Microsoft.AspNetCore.StaticFiles": "2.1.1", "Microsoft.AspNetCore.WebSockets": "2.1.1", "Microsoft.AspNetCore.WebUtilities": "2.1.1", "Microsoft.CodeAnalysis.Razor": "2.1.2", "Microsoft.Data.Sqlite": "2.1.0", "Microsoft.Data.Sqlite.Core": "2.1.0", "Microsoft.EntityFrameworkCore": "2.1.4", "Microsoft.EntityFrameworkCore.Abstractions": "2.1.4", "Microsoft.EntityFrameworkCore.Analyzers": "2.1.4", "Microsoft.EntityFrameworkCore.Design": "2.1.4", "Microsoft.EntityFrameworkCore.InMemory": "2.1.4", "Microsoft.EntityFrameworkCore.Relational": "2.1.4", "Microsoft.EntityFrameworkCore.SqlServer": "2.1.4", "Microsoft.EntityFrameworkCore.Sqlite": "2.1.4", "Microsoft.EntityFrameworkCore.Sqlite.Core": "2.1.4", "Microsoft.EntityFrameworkCore.Tools": "2.1.4", "Microsoft.Extensions.Caching.Abstractions": "2.1.2", "Microsoft.Extensions.Caching.Memory": "2.1.2", "Microsoft.Extensions.Caching.Redis": "2.1.2", "Microsoft.Extensions.Caching.SqlServer": "2.1.2", "Microsoft.Extensions.Configuration": "2.1.1", "Microsoft.Extensions.Configuration.Abstractions": "2.1.1", "Microsoft.Extensions.Configuration.AzureKeyVault": "2.1.1", "Microsoft.Extensions.Configuration.Binder": "2.1.1", "Microsoft.Extensions.Configuration.CommandLine": "2.1.1", "Microsoft.Extensions.Configuration.EnvironmentVariables": "2.1.1", "Microsoft.Extensions.Configuration.FileExtensions": "2.1.1", "Microsoft.Extensions.Configuration.Ini": "2.1.1", "Microsoft.Extensions.Configuration.Json": "2.1.1", "Microsoft.Extensions.Configuration.KeyPerFile": "2.1.1", "Microsoft.Extensions.Configuration.UserSecrets": "2.1.1", "Microsoft.Extensions.Configuration.Xml": "2.1.1", "Microsoft.Extensions.DependencyInjection": "2.1.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1", "Microsoft.Extensions.DiagnosticAdapter": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.1", "Microsoft.Extensions.FileProviders.Composite": "2.1.1", "Microsoft.Extensions.FileProviders.Embedded": "2.1.1", "Microsoft.Extensions.FileProviders.Physical": "2.1.1", "Microsoft.Extensions.FileSystemGlobbing": "2.1.1", "Microsoft.Extensions.Hosting": "2.1.1", "Microsoft.Extensions.Hosting.Abstractions": "2.1.1", "Microsoft.Extensions.Http": "2.1.1", "Microsoft.Extensions.Identity.Core": "2.1.6", "Microsoft.Extensions.Identity.Stores": "2.1.6", "Microsoft.Extensions.Localization": "2.1.1", "Microsoft.Extensions.Localization.Abstractions": "2.1.1", "Microsoft.Extensions.Logging": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.AzureAppServices": "2.1.1", "Microsoft.Extensions.Logging.Configuration": "2.1.1", "Microsoft.Extensions.Logging.Console": "2.1.1", "Microsoft.Extensions.Logging.Debug": "2.1.1", "Microsoft.Extensions.Logging.EventSource": "2.1.1", "Microsoft.Extensions.Logging.TraceSource": "2.1.1", "Microsoft.Extensions.ObjectPool": "2.1.6", "Microsoft.Extensions.Options": "2.1.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "2.1.1", "Microsoft.Extensions.Primitives": "2.1.6", "Microsoft.Extensions.WebEncoders": "2.1.1", "Microsoft.Net.Http.Headers": "2.1.1", "Microsoft.VisualStudio.Web.BrowserLink": "2.1.1", "System.IO.Pipelines": "4.5.2"}, "compileOnly": true}, "Microsoft.AspNetCore.Antiforgery/2.1.1": {"dependencies": {"Microsoft.AspNetCore.DataProtection": "2.1.1", "Microsoft.AspNetCore.Http.Abstractions": "2.1.1", "Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.AspNetCore.WebUtilities": "2.1.1", "Microsoft.Extensions.ObjectPool": "2.1.6"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Antiforgery.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ApplicationInsights.HostingStartup/2.1.1": {"dependencies": {"Microsoft.ApplicationInsights.AspNetCore": "2.1.1", "Microsoft.AspNetCore.Hosting": "2.1.1", "Microsoft.AspNetCore.Razor.Runtime": "2.1.2", "Microsoft.Extensions.Configuration.Json": "2.1.1", "Microsoft.Extensions.DiagnosticAdapter": "2.1.0", "Microsoft.Extensions.Logging": "2.1.1", "Microsoft.Extensions.Logging.Configuration": "2.1.1"}, "compile": {"lib/netcoreapp2.1/Microsoft.AspNetCore.ApplicationInsights.HostingStartup.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication/2.1.2": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.1.1", "Microsoft.AspNetCore.DataProtection": "2.1.1", "Microsoft.AspNetCore.Http": "2.1.1", "Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1", "Microsoft.Extensions.WebEncoders": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Abstractions/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Cookies/2.1.2": {"dependencies": {"Microsoft.AspNetCore.Authentication": "2.1.2"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Cookies.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Core/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.1.1", "Microsoft.AspNetCore.Http": "2.1.1", "Microsoft.AspNetCore.Http.Extensions": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Facebook/2.1.2": {"dependencies": {"Microsoft.AspNetCore.Authentication.OAuth": "2.1.2"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Facebook.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Google/2.1.2": {"dependencies": {"Microsoft.AspNetCore.Authentication.OAuth": "2.1.2"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Google.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.JwtBearer/2.1.2": {"dependencies": {"Microsoft.AspNetCore.Authentication": "2.1.2", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "5.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.MicrosoftAccount/2.1.2": {"dependencies": {"Microsoft.AspNetCore.Authentication.OAuth": "2.1.2"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.MicrosoftAccount.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.OAuth/2.1.2": {"dependencies": {"Microsoft.AspNetCore.Authentication": "2.1.2", "Newtonsoft.Json": "13.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.OAuth.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.OpenIdConnect/2.1.2": {"dependencies": {"Microsoft.AspNetCore.Authentication.OAuth": "2.1.2", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "5.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.OpenIdConnect.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Twitter/2.1.2": {"dependencies": {"Microsoft.AspNetCore.Authentication.OAuth": "2.1.2"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Twitter.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.WsFederation/2.1.2": {"dependencies": {"Microsoft.AspNetCore.Authentication": "2.1.2", "Microsoft.IdentityModel.Protocols.WsFederation": "5.2.0", "System.IdentityModel.Tokens.Jwt": "5.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.WsFederation.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authorization/2.1.2": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authorization.Policy/2.1.2": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.1.1", "Microsoft.AspNetCore.Authorization": "2.1.2"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.Policy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.AzureAppServices.HostingStartup/2.1.1": {"dependencies": {"Microsoft.AspNetCore.AzureAppServicesIntegration": "2.1.1", "Microsoft.Extensions.Configuration.EnvironmentVariables": "2.1.1"}, "compile": {"lib/netcoreapp2.1/Microsoft.AspNetCore.AzureAppServices.HostingStartup.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.AzureAppServicesIntegration/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Hosting": "2.1.1", "Microsoft.Extensions.Logging.AzureAppServices": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.AzureAppServicesIntegration.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Connections.Abstractions/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.1.1", "System.IO.Pipelines": "4.5.2"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Connections.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.CookiePolicy/2.1.2": {"dependencies": {"Microsoft.AspNetCore.Http": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.CookiePolicy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Cors/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.Extensions.Configuration.Abstractions": "2.1.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Cors.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Cryptography.Internal/2.1.1": {"compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "2.1.1"}, "compile": {"lib/netcoreapp2.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "2.1.1", "Microsoft.AspNetCore.DataProtection.Abstractions": "2.1.1", "Microsoft.AspNetCore.Hosting.Abstractions": "2.1.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1", "Microsoft.Win32.Registry": "5.0.0", "System.Security.Cryptography.Xml": "4.5.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection.Abstractions/2.1.1": {"compile": {"lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection.AzureKeyVault/2.1.1": {"dependencies": {"Microsoft.AspNetCore.DataProtection": "2.1.1", "Microsoft.Azure.KeyVault": "2.3.2", "Microsoft.IdentityModel.Clients.ActiveDirectory": "3.14.2"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.AzureKeyVault.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection.AzureStorage/2.1.1": {"dependencies": {"Microsoft.AspNetCore.DataProtection": "2.1.1", "WindowsAzure.Storage": "8.1.4"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.AzureStorage.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection.Extensions/2.1.1": {"dependencies": {"Microsoft.AspNetCore.DataProtection": "2.1.1", "Microsoft.Extensions.DependencyInjection": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.Extensions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Diagnostics.Abstractions": "2.1.1", "Microsoft.AspNetCore.Hosting.Abstractions": "2.1.1", "Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.AspNetCore.WebUtilities": "2.1.1", "Microsoft.Extensions.FileProviders.Physical": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1", "System.Diagnostics.DiagnosticSource": "4.7.1", "System.Reflection.Metadata": "1.6.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Diagnostics.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics.Abstractions/2.1.1": {"compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Diagnostics.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.1", "Microsoft.EntityFrameworkCore.Relational": "2.1.4"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HostFiltering/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.1.1", "Microsoft.AspNetCore.Http": "2.1.1", "Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.HostFiltering.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.1.1", "Microsoft.AspNetCore.Http": "2.1.1", "Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.Extensions.Configuration": "2.1.1", "Microsoft.Extensions.Configuration.EnvironmentVariables": "2.1.1", "Microsoft.Extensions.Configuration.FileExtensions": "2.1.1", "Microsoft.Extensions.DependencyInjection": "2.1.1", "Microsoft.Extensions.FileProviders.Physical": "2.1.1", "Microsoft.Extensions.Hosting.Abstractions": "2.1.1", "Microsoft.Extensions.Logging": "2.1.1", "Microsoft.Extensions.Options": "2.1.1", "System.Diagnostics.DiagnosticSource": "4.7.1", "System.Reflection.Metadata": "1.6.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting.Abstractions/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.1.1", "Microsoft.AspNetCore.Http.Abstractions": "2.1.1", "Microsoft.Extensions.Hosting.Abstractions": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.1.1", "Microsoft.Extensions.Configuration.Abstractions": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Html.Abstractions/2.1.1": {"dependencies": {"System.Text.Encodings.Web": "5.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Html.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.1", "Microsoft.AspNetCore.WebUtilities": "2.1.1", "Microsoft.Extensions.ObjectPool": "2.1.6", "Microsoft.Extensions.Options": "2.1.1", "Microsoft.Net.Http.Headers": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Abstractions/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.1.1", "System.Text.Encodings.Web": "5.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Connections/1.0.4": {"dependencies": {"Microsoft.AspNetCore.Authorization.Policy": "2.1.2", "Microsoft.AspNetCore.Hosting.Abstractions": "2.1.1", "Microsoft.AspNetCore.Http": "2.1.1", "Microsoft.AspNetCore.Http.Connections.Common": "1.0.4", "Microsoft.AspNetCore.Routing": "2.1.1", "Microsoft.AspNetCore.WebSockets": "2.1.1", "Newtonsoft.Json": "13.0.1"}, "compile": {"lib/netcoreapp2.1/Microsoft.AspNetCore.Http.Connections.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Connections.Common/1.0.4": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "2.1.3", "Newtonsoft.Json": "13.0.1", "System.Buffers": "4.5.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Connections.Common.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Extensions/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.1", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.1", "Microsoft.Net.Http.Headers": "2.1.1", "System.Buffers": "4.5.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Features/2.1.1": {"dependencies": {"Microsoft.Extensions.Primitives": "2.1.6"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HttpOverrides/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.HttpOverrides.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HttpsPolicy/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.1.1", "Microsoft.AspNetCore.Http": "2.1.1", "Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.Extensions.Configuration.Binder": "2.1.1", "Microsoft.Extensions.Options": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.HttpsPolicy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Identity/2.1.6": {"dependencies": {"Microsoft.AspNetCore.Authentication.Cookies": "2.1.2", "Microsoft.AspNetCore.Cryptography.KeyDerivation": "2.1.1", "Microsoft.AspNetCore.Hosting.Abstractions": "2.1.1", "Microsoft.Extensions.Identity.Core": "2.1.6"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Identity.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/2.1.6": {"dependencies": {"Microsoft.AspNetCore.Identity": "2.1.6", "Microsoft.EntityFrameworkCore.Relational": "2.1.4", "Microsoft.Extensions.Identity.Stores": "2.1.6"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Identity.EntityFrameworkCore.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Identity.UI/2.1.6": {"dependencies": {"Microsoft.AspNetCore.Identity": "2.1.6", "Microsoft.AspNetCore.Mvc": "2.1.3", "Microsoft.AspNetCore.StaticFiles": "2.1.1", "Microsoft.Extensions.FileProviders.Embedded": "2.1.1", "Microsoft.Extensions.Identity.Stores": "2.1.6"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Identity.UI.Views.dll": {}, "lib/netstandard2.0/Microsoft.AspNetCore.Identity.UI.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.JsonPatch/2.1.1": {"dependencies": {"Microsoft.CSharp": "4.5.0", "Newtonsoft.Json": "13.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.JsonPatch.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Localization/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.Extensions.Localization.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Localization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Localization.Routing/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Localization": "2.1.1", "Microsoft.AspNetCore.Routing.Abstractions": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Localization.Routing.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.MiddlewareAnalysis/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.1.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1", "System.Diagnostics.DiagnosticSource": "4.7.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.MiddlewareAnalysis.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Mvc.ApiExplorer": "2.1.3", "Microsoft.AspNetCore.Mvc.Cors": "2.1.3", "Microsoft.AspNetCore.Mvc.DataAnnotations": "2.1.3", "Microsoft.AspNetCore.Mvc.Formatters.Json": "2.1.3", "Microsoft.AspNetCore.Mvc.Localization": "2.1.3", "Microsoft.AspNetCore.Mvc.Razor.Extensions": "2.1.2", "Microsoft.AspNetCore.Mvc.RazorPages": "2.1.3", "Microsoft.AspNetCore.Mvc.TagHelpers": "2.1.3", "Microsoft.AspNetCore.Mvc.ViewFeatures": "2.1.3", "Microsoft.AspNetCore.Razor.Design": "2.1.2", "Microsoft.Extensions.Caching.Memory": "2.1.2", "Microsoft.Extensions.DependencyInjection": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Abstractions/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Routing.Abstractions": "2.1.1", "Microsoft.Net.Http.Headers": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Analyzers/2.1.3": {"compileOnly": true}, "Microsoft.AspNetCore.Mvc.ApiExplorer/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.1.3"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Core/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.1.1", "Microsoft.AspNetCore.Authorization.Policy": "2.1.2", "Microsoft.AspNetCore.Hosting.Abstractions": "2.1.1", "Microsoft.AspNetCore.Http": "2.1.1", "Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.AspNetCore.Mvc.Abstractions": "2.1.3", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "2.1.1", "Microsoft.AspNetCore.Routing": "2.1.1", "Microsoft.Extensions.DependencyInjection": "2.1.1", "Microsoft.Extensions.DependencyModel": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "System.Diagnostics.DiagnosticSource": "4.7.1", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Cors/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Cors": "2.1.1", "Microsoft.AspNetCore.Mvc.Core": "2.1.3"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Cors.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.DataAnnotations/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.1.3", "Microsoft.Extensions.Localization": "2.1.1", "System.ComponentModel.Annotations": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Formatters.Json/2.1.3": {"dependencies": {"Microsoft.AspNetCore.JsonPatch": "2.1.1", "Microsoft.AspNetCore.Mvc.Core": "2.1.3"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Formatters.Json.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Formatters.Xml/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.1.3"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Formatters.Xml.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Localization/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Localization": "2.1.1", "Microsoft.AspNetCore.Mvc.Razor": "2.1.3", "Microsoft.Extensions.DependencyInjection": "2.1.1", "Microsoft.Extensions.Localization": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Localization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Razor/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Mvc.Razor.Extensions": "2.1.2", "Microsoft.AspNetCore.Mvc.ViewFeatures": "2.1.3", "Microsoft.AspNetCore.Razor.Runtime": "2.1.2", "Microsoft.CodeAnalysis.CSharp": "2.8.0", "Microsoft.CodeAnalysis.Razor": "2.1.2", "Microsoft.Extensions.Caching.Memory": "2.1.2", "Microsoft.Extensions.FileProviders.Composite": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Razor.Extensions/2.1.2": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "2.1.2", "Microsoft.CodeAnalysis.Razor": "2.1.2"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.Extensions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Razor.ViewCompilation/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Hosting": "2.1.1", "Microsoft.AspNetCore.Mvc.RazorPages": "2.1.3"}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.RazorPages/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Mvc.Razor": "2.1.3"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.RazorPages.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.TagHelpers/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Mvc.Razor": "2.1.3", "Microsoft.AspNetCore.Razor.Runtime": "2.1.2", "Microsoft.AspNetCore.Routing.Abstractions": "2.1.1", "Microsoft.Extensions.Caching.Memory": "2.1.2", "Microsoft.Extensions.FileSystemGlobbing": "2.1.1", "Microsoft.Extensions.Primitives": "2.1.6"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.TagHelpers.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.ViewFeatures/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Antiforgery": "2.1.1", "Microsoft.AspNetCore.Diagnostics.Abstractions": "2.1.1", "Microsoft.AspNetCore.Html.Abstractions": "2.1.1", "Microsoft.AspNetCore.Mvc.Core": "2.1.3", "Microsoft.AspNetCore.Mvc.DataAnnotations": "2.1.3", "Microsoft.AspNetCore.Mvc.Formatters.Json": "2.1.3", "Microsoft.Extensions.WebEncoders": "2.1.1", "Newtonsoft.Json.Bson": "1.0.2"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.ViewFeatures.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.NodeServices/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Console": "2.1.1", "Newtonsoft.Json": "13.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.NodeServices.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Owin/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Owin.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Razor/2.1.2": {"dependencies": {"Microsoft.AspNetCore.Html.Abstractions": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Razor.Design/2.1.2": {"compileOnly": true}, "Microsoft.AspNetCore.Razor.Language/2.1.2": {"compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Razor.Runtime/2.1.2": {"dependencies": {"Microsoft.AspNetCore.Html.Abstractions": "2.1.1", "Microsoft.AspNetCore.Razor": "2.1.2"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Runtime.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCaching/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http": "2.1.1", "Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "2.1.1", "Microsoft.Extensions.Caching.Memory": "2.1.2", "Microsoft.Extensions.Logging.Abstractions": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.ResponseCaching.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.1.1": {"dependencies": {"Microsoft.Extensions.Primitives": "2.1.6"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCompression/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.ResponseCompression.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Rewrite/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.1.1", "Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.Extensions.Configuration.Abstractions": "2.1.1", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Rewrite.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Routing/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.AspNetCore.Routing.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.ObjectPool": "2.1.6", "Microsoft.Extensions.Options": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Routing.Abstractions/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.HttpSys/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.1.1", "Microsoft.AspNetCore.Hosting": "2.1.1", "Microsoft.Net.Http.Headers": "2.1.1", "Microsoft.Win32.Registry": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.HttpSys.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.IISIntegration/2.1.2": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.1.1", "Microsoft.AspNetCore.Hosting.Abstractions": "2.1.1", "Microsoft.AspNetCore.Http": "2.1.1", "Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.AspNetCore.HttpOverrides": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1", "System.Buffers": "4.5.1", "System.IO.Pipelines": "4.5.2", "System.Memory": "4.5.5", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.IISIntegration.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Hosting": "2.1.1", "Microsoft.AspNetCore.Server.Kestrel.Core": "2.1.3", "Microsoft.AspNetCore.Server.Kestrel.Https": "2.1.3", "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets": "2.1.3"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.Kestrel.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Core/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.1.1", "Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions": "2.1.3", "Microsoft.AspNetCore.WebUtilities": "2.1.1", "Microsoft.Extensions.Configuration.Binder": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1", "Microsoft.Net.Http.Headers": "2.1.1", "System.Memory": "4.5.5", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Security.Cryptography.Cng": "4.5.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/netcoreapp2.1/Microsoft.AspNetCore.Server.Kestrel.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Https/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.1", "Microsoft.AspNetCore.Server.Kestrel.Core": "2.1.3"}, "compile": {"lib/netcoreapp2.1/Microsoft.AspNetCore.Server.Kestrel.Https.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "2.1.3"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Libuv/2.1.3": {"dependencies": {"Libuv": "1.10.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.1.1", "Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions": "2.1.3", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.Kestrel.Transport.Libuv.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.1.1", "Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions": "2.1.3", "Microsoft.Extensions.Options": "2.1.1"}, "compile": {"lib/netcoreapp2.1/Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Session/2.1.1": {"dependencies": {"Microsoft.AspNetCore.DataProtection": "2.1.1", "Microsoft.AspNetCore.Http.Abstractions": "2.1.1", "Microsoft.Extensions.Caching.Abstractions": "2.1.2", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Session.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR/1.0.4": {"dependencies": {"Microsoft.AspNetCore.Http.Connections": "1.0.4", "Microsoft.AspNetCore.SignalR.Core": "1.0.4"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.SignalR.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Common/1.0.4": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "2.1.3", "Microsoft.Extensions.Options": "2.1.1", "Newtonsoft.Json": "13.0.1", "System.Buffers": "4.5.1"}, "compile": {"lib/netcoreapp2.1/Microsoft.AspNetCore.SignalR.Common.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Core/1.0.4": {"dependencies": {"Microsoft.AspNetCore.Authorization": "2.1.2", "Microsoft.AspNetCore.SignalR.Common": "1.0.4", "Microsoft.AspNetCore.SignalR.Protocols.Json": "1.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "System.Reflection.Emit": "4.7.0", "System.Threading.Channels": "4.7.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.SignalR.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Protocols.Json/1.0.4": {"dependencies": {"Microsoft.AspNetCore.SignalR.Common": "1.0.4", "Newtonsoft.Json": "13.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.SignalR.Protocols.Json.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Redis/1.0.4": {"dependencies": {"MessagePack": "*******", "Microsoft.AspNetCore.SignalR.Core": "1.0.4", "Microsoft.Extensions.Options": "2.1.1", "StackExchange.Redis.StrongName": "1.2.6"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.SignalR.Redis.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SpaServices/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Mvc.TagHelpers": "2.1.3", "Microsoft.AspNetCore.Mvc.ViewFeatures": "2.1.3", "Microsoft.AspNetCore.NodeServices": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.SpaServices.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SpaServices.Extensions/2.1.1": {"dependencies": {"Microsoft.AspNetCore.SpaServices": "2.1.1", "Microsoft.AspNetCore.StaticFiles": "2.1.1", "Microsoft.AspNetCore.WebSockets": "2.1.1", "Microsoft.Extensions.FileProviders.Physical": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.SpaServices.Extensions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.StaticFiles/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.1.1", "Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.WebEncoders": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.StaticFiles.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.WebSockets/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1", "System.Net.WebSockets.WebSocketProtocol": "4.5.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.WebSockets.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.WebUtilities/2.1.1": {"dependencies": {"Microsoft.Net.Http.Headers": "2.1.1", "System.Text.Encodings.Web": "5.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll": {}}, "compileOnly": true}, "Microsoft.Azure.KeyVault/2.3.2": {"dependencies": {"Microsoft.Azure.KeyVault.WebKey": "2.0.7", "Microsoft.Rest.ClientRuntime": "2.3.8", "Microsoft.Rest.ClientRuntime.Azure": "3.3.7", "NETStandard.Library": "2.0.3", "Newtonsoft.Json": "13.0.1", "System.Net.Http": "4.3.0"}, "compile": {"lib/netstandard1.4/Microsoft.Azure.KeyVault.dll": {}}, "compileOnly": true}, "Microsoft.Azure.KeyVault.WebKey/2.0.7": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "NETStandard.Library": "2.0.3", "Newtonsoft.Json": "13.0.1", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Runtime": "4.3.1", "System.Security.Cryptography.Algorithms": "4.3.0"}, "compile": {"lib/netstandard1.4/Microsoft.Azure.KeyVault.WebKey.dll": {}}, "compileOnly": true}, "Microsoft.Azure.Services.AppAuthentication/1.0.1": {"dependencies": {"Microsoft.IdentityModel.Clients.ActiveDirectory": "3.14.2", "NETStandard.Library": "2.0.3", "System.Diagnostics.Process": "4.3.0"}, "compile": {"lib/netstandard1.4/Microsoft.Azure.Services.AppAuthentication.dll": {}}, "compileOnly": true}, "Microsoft.CodeAnalysis.Analyzers/1.1.0": {"compileOnly": true}, "Microsoft.CodeAnalysis.Common/2.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "1.1.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Collections.Immutable": "1.5.0", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.FileVersionInfo": "4.3.0", "System.Diagnostics.StackTrace": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Globalization": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Metadata": "1.6.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.CodePages": "5.0.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Parallel": "4.3.0", "System.Threading.Thread": "4.3.0", "System.ValueTuple": "4.5.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0", "System.Xml.XPath.XDocument": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "compile": {"lib/netstandard1.3/Microsoft.CodeAnalysis.dll": {}}, "compileOnly": true}, "Microsoft.CodeAnalysis.CSharp/2.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "2.8.0"}, "compile": {"lib/netstandard1.3/Microsoft.CodeAnalysis.CSharp.dll": {}}, "compileOnly": true}, "Microsoft.CodeAnalysis.Razor/2.1.2": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "2.1.2", "Microsoft.CodeAnalysis.CSharp": "2.8.0", "Microsoft.CodeAnalysis.Common": "2.8.0"}, "compile": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll": {}}, "compileOnly": true}, "Microsoft.CSharp/4.5.0": {"compileOnly": true}, "Microsoft.Data.Edm/5.8.2": {"compile": {"lib/netstandard1.1/Microsoft.Data.Edm.dll": {}}, "compileOnly": true}, "Microsoft.Data.OData/5.8.2": {"dependencies": {"Microsoft.Data.Edm": "5.8.2", "System.Spatial": "5.8.2"}, "compile": {"lib/netstandard1.1/Microsoft.Data.OData.dll": {}}, "compileOnly": true}, "Microsoft.Data.Sqlite/2.1.0": {"dependencies": {"Microsoft.Data.Sqlite.Core": "2.1.0", "SQLitePCLRaw.bundle_green": "1.1.11"}, "compileOnly": true}, "Microsoft.Data.Sqlite.Core/2.1.0": {"dependencies": {"SQLitePCLRaw.core": "1.1.11"}, "compile": {"lib/netstandard2.0/Microsoft.Data.Sqlite.dll": {}}, "compileOnly": true}, "Microsoft.DotNet.PlatformAbstractions/2.1.0": {"dependencies": {"System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Reflection.TypeExtensions": "4.4.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0"}, "compile": {"lib/netstandard1.3/Microsoft.DotNet.PlatformAbstractions.dll": {}}, "compileOnly": true}, "Microsoft.EntityFrameworkCore/2.1.4": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "2.1.4", "Microsoft.EntityFrameworkCore.Analyzers": "2.1.4", "Microsoft.Extensions.Caching.Memory": "2.1.2", "Microsoft.Extensions.DependencyInjection": "2.1.1", "Microsoft.Extensions.Logging": "2.1.1", "Remotion.Linq": "2.2.0", "System.Collections.Immutable": "1.5.0", "System.ComponentModel.Annotations": "4.5.0", "System.Diagnostics.DiagnosticSource": "4.7.1", "System.Interactive.Async": "3.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.dll": {}}, "compileOnly": true}, "Microsoft.EntityFrameworkCore.Abstractions/2.1.4": {"compile": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.EntityFrameworkCore.Analyzers/2.1.4": {"compileOnly": true}, "Microsoft.EntityFrameworkCore.Design/2.1.4": {"dependencies": {"Microsoft.CSharp": "4.5.0", "Microsoft.EntityFrameworkCore.Relational": "2.1.4"}, "compile": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.Design.dll": {}}, "compileOnly": true}, "Microsoft.EntityFrameworkCore.InMemory/2.1.4": {"dependencies": {"Microsoft.EntityFrameworkCore": "2.1.4"}, "compile": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.InMemory.dll": {}}, "compileOnly": true}, "Microsoft.EntityFrameworkCore.Relational/2.1.4": {"dependencies": {"Microsoft.EntityFrameworkCore": "2.1.4"}, "compile": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.Relational.dll": {}}, "compileOnly": true}, "Microsoft.EntityFrameworkCore.Sqlite/2.1.4": {"dependencies": {"Microsoft.EntityFrameworkCore.Sqlite.Core": "2.1.4", "SQLitePCLRaw.bundle_green": "1.1.11"}, "compileOnly": true}, "Microsoft.EntityFrameworkCore.Sqlite.Core/2.1.4": {"dependencies": {"Microsoft.Data.Sqlite.Core": "2.1.0", "Microsoft.EntityFrameworkCore.Relational": "2.1.4"}, "compile": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {}}, "compileOnly": true}, "Microsoft.EntityFrameworkCore.SqlServer/2.1.4": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "2.1.4", "System.Data.SqlClient": "4.6.0"}, "compile": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {}}, "compileOnly": true}, "Microsoft.EntityFrameworkCore.Tools/2.1.4": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "2.1.4"}, "compileOnly": true}, "Microsoft.Extensions.Caching.Abstractions/2.1.2": {"dependencies": {"Microsoft.Extensions.Primitives": "2.1.6"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Caching.Memory/2.1.2": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "2.1.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Caching.Redis/2.1.2": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "2.1.2", "Microsoft.Extensions.Options": "2.1.1", "StackExchange.Redis.StrongName": "1.2.6"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Redis.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Caching.SqlServer/2.1.2": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "2.1.2", "Microsoft.Extensions.Options": "2.1.1", "System.Data.SqlClient": "4.6.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Caching.SqlServer.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Abstractions/2.1.1": {"dependencies": {"Microsoft.Extensions.Primitives": "2.1.6"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.AzureKeyVault/2.1.1": {"dependencies": {"Microsoft.Azure.KeyVault": "2.3.2", "Microsoft.Azure.Services.AppAuthentication": "1.0.1", "Microsoft.Extensions.Configuration": "2.1.1", "Microsoft.Extensions.Configuration.FileExtensions": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.AzureKeyVault.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Binder/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.CommandLine/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.EnvironmentVariables/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.FileExtensions/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration": "2.1.1", "Microsoft.Extensions.FileProviders.Physical": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Ini/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration": "2.1.1", "Microsoft.Extensions.Configuration.FileExtensions": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Ini.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Json/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration": "2.1.1", "Microsoft.Extensions.Configuration.FileExtensions": "2.1.1", "Newtonsoft.Json": "13.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.KeyPerFile/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration": "2.1.1", "Microsoft.Extensions.FileProviders.Physical": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.KeyPerFile.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.UserSecrets/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration.Json": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Xml/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration": "2.1.1", "Microsoft.Extensions.Configuration.FileExtensions": "2.1.1", "System.Security.Cryptography.Xml": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Xml.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.DependencyInjection/2.1.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1"}, "compile": {"lib/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.DependencyInjection.Abstractions/2.1.1": {"compile": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.DependencyModel/2.1.0": {"dependencies": {"Microsoft.DotNet.PlatformAbstractions": "2.1.0", "Newtonsoft.Json": "13.0.1", "System.Diagnostics.Debug": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Linq": "4.3.0"}, "compile": {"lib/netstandard1.6/Microsoft.Extensions.DependencyModel.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.DiagnosticAdapter/2.1.0": {"dependencies": {"System.Diagnostics.DiagnosticSource": "4.7.1"}, "compile": {"lib/netcoreapp2.0/Microsoft.Extensions.DiagnosticAdapter.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Abstractions/2.1.1": {"dependencies": {"Microsoft.Extensions.Primitives": "2.1.6"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Composite/2.1.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Composite.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Embedded/2.1.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Physical/2.1.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "2.1.1", "Microsoft.Extensions.FileSystemGlobbing": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileSystemGlobbing/2.1.1": {"compile": {"lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Hosting/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration": "2.1.1", "Microsoft.Extensions.DependencyInjection": "2.1.1", "Microsoft.Extensions.FileProviders.Physical": "2.1.1", "Microsoft.Extensions.Hosting.Abstractions": "2.1.1", "Microsoft.Extensions.Logging": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Hosting.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Hosting.Abstractions/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "2.1.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Http/2.1.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1", "Microsoft.Extensions.Logging": "2.1.1", "Microsoft.Extensions.Options": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Http.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Identity.Core/2.1.6": {"dependencies": {"Microsoft.AspNetCore.Cryptography.KeyDerivation": "2.1.1", "Microsoft.Extensions.Logging": "2.1.1", "Microsoft.Extensions.Options": "2.1.1", "System.ComponentModel.Annotations": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Identity.Core.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Identity.Stores/2.1.6": {"dependencies": {"Microsoft.Extensions.Identity.Core": "2.1.6", "Microsoft.Extensions.Logging": "2.1.1", "System.ComponentModel.Annotations": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Identity.Stores.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Localization/2.1.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1", "Microsoft.Extensions.Localization.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Localization.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Localization.Abstractions/2.1.1": {"compile": {"lib/netstandard2.0/Microsoft.Extensions.Localization.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "2.1.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Abstractions/2.1.1": {"compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.AzureAppServices/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration.EnvironmentVariables": "2.1.1", "Microsoft.Extensions.Configuration.Json": "2.1.1", "Microsoft.Extensions.Logging": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Configuration": "2.1.1", "System.ValueTuple": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.AzureAppServices.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Configuration/2.1.1": {"dependencies": {"Microsoft.Extensions.Logging": "2.1.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Console/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "2.1.1", "Microsoft.Extensions.Logging": "2.1.1", "Microsoft.Extensions.Logging.Configuration": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Console.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Debug/2.1.1": {"dependencies": {"Microsoft.Extensions.Logging": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.EventSource/2.1.1": {"dependencies": {"Microsoft.Extensions.Logging": "2.1.1", "Newtonsoft.Json": "13.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.EventSource.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.TraceSource/2.1.1": {"dependencies": {"Microsoft.Extensions.Logging": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.TraceSource.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.ObjectPool/2.1.6": {"compile": {"lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Options/2.1.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1", "Microsoft.Extensions.Primitives": "2.1.6"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Options.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Options.ConfigurationExtensions/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "2.1.1", "Microsoft.Extensions.Configuration.Binder": "2.1.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.PlatformAbstractions/1.1.0": {"dependencies": {"NETStandard.Library": "2.0.3", "System.Reflection.TypeExtensions": "4.4.0"}, "compile": {"lib/netstandard1.3/Microsoft.Extensions.PlatformAbstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Primitives/2.1.6": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Primitives.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.WebEncoders/2.1.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1", "System.Text.Encodings.Web": "5.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.WebEncoders.dll": {}}, "compileOnly": true}, "Microsoft.IdentityModel.Clients.ActiveDirectory/3.14.2": {"dependencies": {"NETStandard.Library": "2.0.3", "System.Runtime.Serialization.Json": "4.0.2", "System.Runtime.Serialization.Primitives": "4.3.0"}, "compile": {"lib/netstandard1.3/Microsoft.IdentityModel.Clients.ActiveDirectory.Platform.dll": {}, "lib/netstandard1.3/Microsoft.IdentityModel.Clients.ActiveDirectory.dll": {}}, "compileOnly": true}, "Microsoft.IdentityModel.Protocols/5.2.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "5.3.0", "Microsoft.IdentityModel.Tokens": "5.3.0", "NETStandard.Library": "2.0.3", "System.Collections.Specialized": "4.3.0", "System.Diagnostics.Contracts": "4.3.0", "System.Net.Http": "4.3.0"}, "compile": {"lib/netstandard1.4/Microsoft.IdentityModel.Protocols.dll": {}}, "compileOnly": true}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/5.2.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "5.2.0", "NETStandard.Library": "2.0.3", "Newtonsoft.Json": "13.0.1", "System.Dynamic.Runtime": "4.3.0", "System.IdentityModel.Tokens.Jwt": "5.2.0"}, "compile": {"lib/netstandard1.4/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {}}, "compileOnly": true}, "Microsoft.IdentityModel.Protocols.WsFederation/5.2.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "5.2.0", "Microsoft.IdentityModel.Tokens.Saml": "5.2.0", "Microsoft.IdentityModel.Xml": "5.2.0", "NETStandard.Library": "2.0.3", "System.Xml.XmlDocument": "4.3.0"}, "compile": {"lib/netstandard1.4/Microsoft.IdentityModel.Protocols.WsFederation.dll": {}}, "compileOnly": true}, "Microsoft.IdentityModel.Tokens.Saml/5.2.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "5.3.0", "Microsoft.IdentityModel.Xml": "5.2.0", "NETStandard.Library": "2.0.3"}, "compile": {"lib/netstandard1.4/Microsoft.IdentityModel.Tokens.Saml.dll": {}}, "compileOnly": true}, "Microsoft.IdentityModel.Xml/5.2.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "5.3.0", "NETStandard.Library": "2.0.3"}, "compile": {"lib/netstandard1.4/Microsoft.IdentityModel.Xml.dll": {}}, "compileOnly": true}, "Microsoft.Net.Http.Headers/2.1.1": {"dependencies": {"Microsoft.Extensions.Primitives": "2.1.6", "System.Buffers": "4.5.1"}, "compile": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {}}, "compileOnly": true}, "Microsoft.NETCore.App/2.1.0": {"dependencies": {"Microsoft.NETCore.DotNetHostPolicy": "2.1.0", "Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.1.0", "NETStandard.Library": "2.0.3"}, "compile": {"ref/netcoreapp2.1/Microsoft.CSharp.dll": {}, "ref/netcoreapp2.1/Microsoft.VisualBasic.dll": {}, "ref/netcoreapp2.1/Microsoft.Win32.Primitives.dll": {}, "ref/netcoreapp2.1/System.AppContext.dll": {}, "ref/netcoreapp2.1/System.Buffers.dll": {}, "ref/netcoreapp2.1/System.Collections.Concurrent.dll": {}, "ref/netcoreapp2.1/System.Collections.Immutable.dll": {}, "ref/netcoreapp2.1/System.Collections.NonGeneric.dll": {}, "ref/netcoreapp2.1/System.Collections.Specialized.dll": {}, "ref/netcoreapp2.1/System.Collections.dll": {}, "ref/netcoreapp2.1/System.ComponentModel.Annotations.dll": {}, "ref/netcoreapp2.1/System.ComponentModel.DataAnnotations.dll": {}, "ref/netcoreapp2.1/System.ComponentModel.EventBasedAsync.dll": {}, "ref/netcoreapp2.1/System.ComponentModel.Primitives.dll": {}, "ref/netcoreapp2.1/System.ComponentModel.TypeConverter.dll": {}, "ref/netcoreapp2.1/System.ComponentModel.dll": {}, "ref/netcoreapp2.1/System.Configuration.dll": {}, "ref/netcoreapp2.1/System.Console.dll": {}, "ref/netcoreapp2.1/System.Core.dll": {}, "ref/netcoreapp2.1/System.Data.Common.dll": {}, "ref/netcoreapp2.1/System.Data.dll": {}, "ref/netcoreapp2.1/System.Diagnostics.Contracts.dll": {}, "ref/netcoreapp2.1/System.Diagnostics.Debug.dll": {}, "ref/netcoreapp2.1/System.Diagnostics.FileVersionInfo.dll": {}, "ref/netcoreapp2.1/System.Diagnostics.Process.dll": {}, "ref/netcoreapp2.1/System.Diagnostics.StackTrace.dll": {}, "ref/netcoreapp2.1/System.Diagnostics.TextWriterTraceListener.dll": {}, "ref/netcoreapp2.1/System.Diagnostics.Tools.dll": {}, "ref/netcoreapp2.1/System.Diagnostics.TraceSource.dll": {}, "ref/netcoreapp2.1/System.Diagnostics.Tracing.dll": {}, "ref/netcoreapp2.1/System.Drawing.Primitives.dll": {}, "ref/netcoreapp2.1/System.Drawing.dll": {}, "ref/netcoreapp2.1/System.Dynamic.Runtime.dll": {}, "ref/netcoreapp2.1/System.Globalization.Calendars.dll": {}, "ref/netcoreapp2.1/System.Globalization.Extensions.dll": {}, "ref/netcoreapp2.1/System.Globalization.dll": {}, "ref/netcoreapp2.1/System.IO.Compression.Brotli.dll": {}, "ref/netcoreapp2.1/System.IO.Compression.FileSystem.dll": {}, "ref/netcoreapp2.1/System.IO.Compression.ZipFile.dll": {}, "ref/netcoreapp2.1/System.IO.Compression.dll": {}, "ref/netcoreapp2.1/System.IO.FileSystem.DriveInfo.dll": {}, "ref/netcoreapp2.1/System.IO.FileSystem.Primitives.dll": {}, "ref/netcoreapp2.1/System.IO.FileSystem.Watcher.dll": {}, "ref/netcoreapp2.1/System.IO.FileSystem.dll": {}, "ref/netcoreapp2.1/System.IO.IsolatedStorage.dll": {}, "ref/netcoreapp2.1/System.IO.MemoryMappedFiles.dll": {}, "ref/netcoreapp2.1/System.IO.Pipes.dll": {}, "ref/netcoreapp2.1/System.IO.UnmanagedMemoryStream.dll": {}, "ref/netcoreapp2.1/System.IO.dll": {}, "ref/netcoreapp2.1/System.Linq.Expressions.dll": {}, "ref/netcoreapp2.1/System.Linq.Parallel.dll": {}, "ref/netcoreapp2.1/System.Linq.Queryable.dll": {}, "ref/netcoreapp2.1/System.Linq.dll": {}, "ref/netcoreapp2.1/System.Memory.dll": {}, "ref/netcoreapp2.1/System.Net.Http.dll": {}, "ref/netcoreapp2.1/System.Net.HttpListener.dll": {}, "ref/netcoreapp2.1/System.Net.Mail.dll": {}, "ref/netcoreapp2.1/System.Net.NameResolution.dll": {}, "ref/netcoreapp2.1/System.Net.NetworkInformation.dll": {}, "ref/netcoreapp2.1/System.Net.Ping.dll": {}, "ref/netcoreapp2.1/System.Net.Primitives.dll": {}, "ref/netcoreapp2.1/System.Net.Requests.dll": {}, "ref/netcoreapp2.1/System.Net.Security.dll": {}, "ref/netcoreapp2.1/System.Net.ServicePoint.dll": {}, "ref/netcoreapp2.1/System.Net.Sockets.dll": {}, "ref/netcoreapp2.1/System.Net.WebClient.dll": {}, "ref/netcoreapp2.1/System.Net.WebHeaderCollection.dll": {}, "ref/netcoreapp2.1/System.Net.WebProxy.dll": {}, "ref/netcoreapp2.1/System.Net.WebSockets.Client.dll": {}, "ref/netcoreapp2.1/System.Net.WebSockets.dll": {}, "ref/netcoreapp2.1/System.Net.dll": {}, "ref/netcoreapp2.1/System.Numerics.Vectors.dll": {}, "ref/netcoreapp2.1/System.Numerics.dll": {}, "ref/netcoreapp2.1/System.ObjectModel.dll": {}, "ref/netcoreapp2.1/System.Reflection.DispatchProxy.dll": {}, "ref/netcoreapp2.1/System.Reflection.Emit.ILGeneration.dll": {}, "ref/netcoreapp2.1/System.Reflection.Emit.Lightweight.dll": {}, "ref/netcoreapp2.1/System.Reflection.Emit.dll": {}, "ref/netcoreapp2.1/System.Reflection.Extensions.dll": {}, "ref/netcoreapp2.1/System.Reflection.Metadata.dll": {}, "ref/netcoreapp2.1/System.Reflection.Primitives.dll": {}, "ref/netcoreapp2.1/System.Reflection.TypeExtensions.dll": {}, "ref/netcoreapp2.1/System.Reflection.dll": {}, "ref/netcoreapp2.1/System.Resources.Reader.dll": {}, "ref/netcoreapp2.1/System.Resources.ResourceManager.dll": {}, "ref/netcoreapp2.1/System.Resources.Writer.dll": {}, "ref/netcoreapp2.1/System.Runtime.CompilerServices.VisualC.dll": {}, "ref/netcoreapp2.1/System.Runtime.Extensions.dll": {}, "ref/netcoreapp2.1/System.Runtime.Handles.dll": {}, "ref/netcoreapp2.1/System.Runtime.InteropServices.RuntimeInformation.dll": {}, "ref/netcoreapp2.1/System.Runtime.InteropServices.WindowsRuntime.dll": {}, "ref/netcoreapp2.1/System.Runtime.InteropServices.dll": {}, "ref/netcoreapp2.1/System.Runtime.Loader.dll": {}, "ref/netcoreapp2.1/System.Runtime.Numerics.dll": {}, "ref/netcoreapp2.1/System.Runtime.Serialization.Formatters.dll": {}, "ref/netcoreapp2.1/System.Runtime.Serialization.Json.dll": {}, "ref/netcoreapp2.1/System.Runtime.Serialization.Primitives.dll": {}, "ref/netcoreapp2.1/System.Runtime.Serialization.Xml.dll": {}, "ref/netcoreapp2.1/System.Runtime.Serialization.dll": {}, "ref/netcoreapp2.1/System.Runtime.dll": {}, "ref/netcoreapp2.1/System.Security.Claims.dll": {}, "ref/netcoreapp2.1/System.Security.Cryptography.Algorithms.dll": {}, "ref/netcoreapp2.1/System.Security.Cryptography.Csp.dll": {}, "ref/netcoreapp2.1/System.Security.Cryptography.Encoding.dll": {}, "ref/netcoreapp2.1/System.Security.Cryptography.Primitives.dll": {}, "ref/netcoreapp2.1/System.Security.Cryptography.X509Certificates.dll": {}, "ref/netcoreapp2.1/System.Security.Principal.dll": {}, "ref/netcoreapp2.1/System.Security.SecureString.dll": {}, "ref/netcoreapp2.1/System.Security.dll": {}, "ref/netcoreapp2.1/System.ServiceModel.Web.dll": {}, "ref/netcoreapp2.1/System.ServiceProcess.dll": {}, "ref/netcoreapp2.1/System.Text.Encoding.Extensions.dll": {}, "ref/netcoreapp2.1/System.Text.Encoding.dll": {}, "ref/netcoreapp2.1/System.Text.RegularExpressions.dll": {}, "ref/netcoreapp2.1/System.Threading.Overlapped.dll": {}, "ref/netcoreapp2.1/System.Threading.Tasks.Dataflow.dll": {}, "ref/netcoreapp2.1/System.Threading.Tasks.Extensions.dll": {}, "ref/netcoreapp2.1/System.Threading.Tasks.Parallel.dll": {}, "ref/netcoreapp2.1/System.Threading.Tasks.dll": {}, "ref/netcoreapp2.1/System.Threading.Thread.dll": {}, "ref/netcoreapp2.1/System.Threading.ThreadPool.dll": {}, "ref/netcoreapp2.1/System.Threading.Timer.dll": {}, "ref/netcoreapp2.1/System.Threading.dll": {}, "ref/netcoreapp2.1/System.Transactions.Local.dll": {}, "ref/netcoreapp2.1/System.Transactions.dll": {}, "ref/netcoreapp2.1/System.ValueTuple.dll": {}, "ref/netcoreapp2.1/System.Web.HttpUtility.dll": {}, "ref/netcoreapp2.1/System.Web.dll": {}, "ref/netcoreapp2.1/System.Windows.dll": {}, "ref/netcoreapp2.1/System.Xml.Linq.dll": {}, "ref/netcoreapp2.1/System.Xml.ReaderWriter.dll": {}, "ref/netcoreapp2.1/System.Xml.Serialization.dll": {}, "ref/netcoreapp2.1/System.Xml.XDocument.dll": {}, "ref/netcoreapp2.1/System.Xml.XPath.XDocument.dll": {}, "ref/netcoreapp2.1/System.Xml.XPath.dll": {}, "ref/netcoreapp2.1/System.Xml.XmlDocument.dll": {}, "ref/netcoreapp2.1/System.Xml.XmlSerializer.dll": {}, "ref/netcoreapp2.1/System.Xml.dll": {}, "ref/netcoreapp2.1/System.dll": {}, "ref/netcoreapp2.1/WindowsBase.dll": {}, "ref/netcoreapp2.1/mscorlib.dll": {}, "ref/netcoreapp2.1/netstandard.dll": {}}, "compileOnly": true}, "Microsoft.NETCore.DotNetAppHost/2.1.0": {"compileOnly": true}, "Microsoft.NETCore.DotNetHostPolicy/2.1.0": {"dependencies": {"Microsoft.NETCore.DotNetHostResolver": "2.1.0"}, "compileOnly": true}, "Microsoft.NETCore.DotNetHostResolver/2.1.0": {"dependencies": {"Microsoft.NETCore.DotNetAppHost": "2.1.0"}, "compileOnly": true}, "Microsoft.NETCore.Targets/2.1.0": {"compileOnly": true}, "Microsoft.Rest.ClientRuntime/2.3.8": {"dependencies": {"NETStandard.Library": "2.0.3", "Newtonsoft.Json": "13.0.1"}, "compile": {"lib/netstandard1.4/Microsoft.Rest.ClientRuntime.dll": {}}, "compileOnly": true}, "Microsoft.Rest.ClientRuntime.Azure/3.3.7": {"dependencies": {"Microsoft.Rest.ClientRuntime": "2.3.8", "NETStandard.Library": "2.0.3", "Newtonsoft.Json": "13.0.1"}, "compile": {"lib/netstandard1.4/Microsoft.Rest.ClientRuntime.Azure.dll": {}}, "compileOnly": true}, "Microsoft.VisualStudio.Web.BrowserLink/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.1.1", "Microsoft.AspNetCore.Http.Abstractions": "2.1.1", "Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.Extensions.FileProviders.Physical": "2.1.1"}, "compile": {"lib/netstandard2.0/Microsoft.VisualStudio.Web.BrowserLink.dll": {}}, "compileOnly": true}, "Microsoft.Win32.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.1.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "NETStandard.Library/2.0.3": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0"}, "compileOnly": true}, "Remotion.Linq/2.2.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Linq.Queryable": "4.0.1", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"lib/netstandard1.0/Remotion.Linq.dll": {}}, "compileOnly": true}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"compileOnly": true}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"compileOnly": true}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"compileOnly": true}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.1.0"}, "compileOnly": true}, "runtime.native.System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.1.0"}, "compileOnly": true}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.1.0"}, "compileOnly": true}, "runtime.native.System.Net.Security/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.1.0"}, "compileOnly": true}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}, "compileOnly": true}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}, "compileOnly": true}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"compileOnly": true}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"compileOnly": true}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"compileOnly": true}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"compileOnly": true}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"compileOnly": true}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"compileOnly": true}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"compileOnly": true}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"compileOnly": true}, "SQLitePCLRaw.bundle_green/1.1.11": {"dependencies": {"SQLitePCLRaw.core": "1.1.11", "SQLitePCLRaw.lib.e_sqlite3.linux": "1.1.11", "SQLitePCLRaw.lib.e_sqlite3.osx": "1.1.11", "SQLitePCLRaw.lib.e_sqlite3.v110_xp": "1.1.11", "SQLitePCLRaw.provider.e_sqlite3.netstandard11": "1.1.11"}, "compile": {"lib/netcoreapp/SQLitePCLRaw.batteries_green.dll": {}, "lib/netcoreapp/SQLitePCLRaw.batteries_v2.dll": {}}, "compileOnly": true}, "SQLitePCLRaw.core/1.1.11": {"dependencies": {"NETStandard.Library": "2.0.3"}, "compile": {"lib/netstandard1.1/SQLitePCLRaw.core.dll": {}}, "compileOnly": true}, "SQLitePCLRaw.lib.e_sqlite3.linux/1.1.11": {"compileOnly": true}, "SQLitePCLRaw.lib.e_sqlite3.osx/1.1.11": {"compileOnly": true}, "SQLitePCLRaw.lib.e_sqlite3.v110_xp/1.1.11": {"compileOnly": true}, "SQLitePCLRaw.provider.e_sqlite3.netstandard11/1.1.11": {"dependencies": {"NETStandard.Library": "2.0.3", "SQLitePCLRaw.core": "1.1.11"}, "compile": {"lib/netstandard1.1/SQLitePCLRaw.provider.e_sqlite3.dll": {}}, "compileOnly": true}, "StackExchange.Redis.StrongName/1.2.6": {"dependencies": {"NETStandard.Library": "2.0.3", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Collections.NonGeneric": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Linq": "4.3.0", "System.Net.NameResolution": "4.3.0", "System.Net.Security": "4.3.0", "System.Net.Sockets": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.Lightweight": "4.7.0", "System.Reflection.TypeExtensions": "4.4.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Thread": "4.3.0", "System.Threading.ThreadPool": "4.3.0", "System.Threading.Timer": "4.3.0"}, "compile": {"lib/netstandard1.5/StackExchange.Redis.StrongName.dll": {}}, "compileOnly": true}, "System.AppContext/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.1.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compileOnly": true}, "System.Collections.Immutable/1.5.0": {"compileOnly": true}, "System.Collections.NonGeneric/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compileOnly": true}, "System.Collections.Specialized/4.3.0": {"dependencies": {"System.Collections.NonGeneric": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compileOnly": true}, "System.ComponentModel.Annotations/4.5.0": {"compileOnly": true}, "System.Console/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.1.0", "System.IO": "4.3.0", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0"}, "compileOnly": true}, "System.Diagnostics.Contracts/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.1.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Diagnostics.FileVersionInfo/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Reflection.Metadata": "1.6.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}, "compileOnly": true}, "System.Diagnostics.Process/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.Win32.Primitives": "4.3.0", "Microsoft.Win32.Registry": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Thread": "4.3.0", "System.Threading.ThreadPool": "4.3.0", "runtime.native.System": "4.3.0"}, "compileOnly": true}, "System.Diagnostics.StackTrace/4.3.0": {"dependencies": {"System.IO.FileSystem": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Metadata": "1.6.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Diagnostics.Tools/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.1.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.1.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Dynamic.Runtime/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.4.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compileOnly": true}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.1.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.1.0", "System.Globalization": "4.3.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}, "compileOnly": true}, "System.IdentityModel.Tokens.Jwt/5.2.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "5.3.0", "NETStandard.Library": "2.0.3", "Newtonsoft.Json": "13.0.1"}, "compile": {"lib/netstandard1.4/System.IdentityModel.Tokens.Jwt.dll": {}}, "compileOnly": true}, "System.Interactive.Async/3.1.1": {"dependencies": {"NETStandard.Library": "2.0.3"}, "compile": {"lib/netstandard1.3/System.Interactive.Async.dll": {}}, "compileOnly": true}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.1.0", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compileOnly": true}, "System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Buffers": "4.5.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}, "compileOnly": true}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compileOnly": true}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}, "compileOnly": true}, "System.IO.Pipelines/4.5.2": {"compile": {"ref/netstandard1.3/System.IO.Pipelines.dll": {}}, "compileOnly": true}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}, "compileOnly": true}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.7.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.4.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compileOnly": true}, "System.Linq.Queryable/4.0.1": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "4.7.1", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}, "compileOnly": true}, "System.Net.NameResolution/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Principal.Windows": "5.0.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0"}, "compileOnly": true}, "System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.1.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}, "compileOnly": true}, "System.Net.Security/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.Win32.Primitives": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Claims": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Security.Principal": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.ThreadPool": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Security": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}, "compileOnly": true}, "System.Net.Sockets/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.1.0", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Threading.Tasks": "4.3.0"}, "compileOnly": true}, "System.Net.WebSockets.WebSocketProtocol/4.5.1": {"compile": {"ref/netstandard2.0/System.Net.WebSockets.WebSocketProtocol.dll": {}}, "compileOnly": true}, "System.Numerics.Vectors/4.5.0": {"compileOnly": true}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Threading": "4.3.0"}, "compileOnly": true}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Reflection.Metadata/1.6.0": {"compileOnly": true}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.1.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.1.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.1.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}, "compileOnly": true}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}, "compileOnly": true}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}, "compileOnly": true}, "System.Runtime.Serialization.Json/4.0.2": {"dependencies": {"System.IO": "4.3.0", "System.Private.DataContractSerialization": "4.3.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Runtime.Serialization.Primitives/4.3.0": {"dependencies": {"System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Security.Claims/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Security.Principal": "4.3.0"}, "compileOnly": true}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}, "compileOnly": true}, "System.Security.Cryptography.Cng/4.5.0": {"compile": {"ref/netcoreapp2.1/System.Security.Cryptography.Cng.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}, "compileOnly": true}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}, "compileOnly": true}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}, "compileOnly": true}, "System.Security.Cryptography.Pkcs/4.5.0": {"dependencies": {"System.Security.Cryptography.Cng": "4.5.0"}, "compileOnly": true}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compileOnly": true}, "System.Security.Cryptography.X509Certificates/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.5.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}, "compileOnly": true}, "System.Security.Cryptography.Xml/4.5.0": {"dependencies": {"System.Security.Cryptography.Pkcs": "4.5.0", "System.Security.Permissions": "4.5.0"}, "compile": {"ref/netstandard2.0/System.Security.Cryptography.Xml.dll": {}}, "compileOnly": true}, "System.Security.Permissions/4.5.0": {"dependencies": {"System.Security.AccessControl": "5.0.0"}, "compile": {"ref/netstandard2.0/System.Security.Permissions.dll": {}}, "compileOnly": true}, "System.Security.Principal/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Spatial/5.8.2": {"compile": {"lib/netstandard1.1/System.Spatial.dll": {}}, "compileOnly": true}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.1.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.1.0", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0"}, "compileOnly": true}, "System.Text.RegularExpressions/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.1", "System.Threading.Tasks": "4.3.0"}, "compileOnly": true}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.1.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Threading.Tasks.Parallel/4.3.0": {"dependencies": {"System.Collections.Concurrent": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compileOnly": true}, "System.Threading.Thread/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}, "compileOnly": true}, "System.Threading.ThreadPool/4.3.0": {"dependencies": {"System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}, "compileOnly": true}, "System.Threading.Timer/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "2.1.0", "System.Runtime": "4.3.1"}, "compileOnly": true}, "System.ValueTuple/4.5.0": {"compileOnly": true}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "compileOnly": true}, "System.Xml.XDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}, "compileOnly": true}, "System.Xml.XmlDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}, "compileOnly": true}, "System.Xml.XPath/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}, "compileOnly": true}, "System.Xml.XPath.XDocument/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0", "System.Xml.XPath": "4.3.0"}, "compileOnly": true}, "WindowsAzure.Storage/8.1.4": {"dependencies": {"Microsoft.Data.OData": "5.8.2", "NETStandard.Library": "2.0.3", "Newtonsoft.Json": "13.0.1", "System.Spatial": "5.8.2"}, "compile": {"lib/netstandard1.3/Microsoft.WindowsAzure.Storage.dll": {}}, "compileOnly": true}}}, "libraries": {"EBizAutos.Apps.Shared.Api/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Amazon.AspNetCore.DataProtection.SSM/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-2OG9McdQ0zheFSp0bVpHRzYik40W6rgvLZS94A/BCXhuxD8m9GaKuVoFQUZc43egJUSQli/J0gesxaBpeUmNmw==", "path": "amazon.aspnetcore.dataprotection.ssm/1.1.0", "hashPath": "amazon.aspnetcore.dataprotection.ssm.1.1.0.nupkg.sha512"}, "Amazon.Extensions.Configuration.SystemsManager/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-ejZvpCO9fifoM+14uy+BKKazW+9My61IpVuYp+b0MfaPJoPkz6LNKTgRXjNiZWO/9mRr3Zi/fEKsSDwEtMnvaA==", "path": "amazon.extensions.configuration.systemsmanager/2.1.1", "hashPath": "amazon.extensions.configuration.systemsmanager.2.1.1.nupkg.sha512"}, "Apache.NMS/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uWsheNh+Bg1rGegOlsWuyrs5dO8xTd+7x4ZsCkrh3vnLBjQ1lsG1kloYBvMj5BBNn2Gcd6jP8YKIgp1Re0oiBA==", "path": "apache.nms/2.0.0", "hashPath": "apache.nms.2.0.0.nupkg.sha512"}, "Apache.NMS.ActiveMQ/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-pJWWXS8SCWKGMoTzZB5LxuMhzGaXh1eg9yDMiTvmkGtaDfLRJI2n1yN/Wpiy1e62acCMiBxvnH07U8JwX08KjQ==", "path": "apache.nms.activemq/2.0.0", "hashPath": "apache.nms.activemq.2.0.0.nupkg.sha512"}, "Automatonymous/5.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-7B0upgJUTJ7fIwFjGEIxOMVJTxqBPyjtnua5i+VyClvHJd31t64SPIm4nhYxI7PF4afmvv5a3dtFCz1W1tQc0A==", "path": "automatonymous/5.1.3", "hashPath": "automatonymous.5.1.3.nupkg.sha512"}, "AWSSDK.Core/**********": {"type": "package", "serviceable": true, "sha512": "sha512-akydySw5e74IM9Y95X8XQ21e4b9oCZmz756ZsLW4eSt/ZI988zdoq8NObVr6nbLNlG7DHNM53d6AAAIx7q/0tQ==", "path": "awssdk.core/**********", "hashPath": "awssdk.core.**********.nupkg.sha512"}, "AWSSDK.Extensions.NETCore.Setup/3.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-cm7D75uispA3h/WwMEQVBMmFPPFwRvFQz8fyLwJEB9uqWYfTOMrRTcynMxHnrdoMyQnfCjByRTzHuFgWELmB6Q==", "path": "awssdk.extensions.netcore.setup/3.7.1", "hashPath": "awssdk.extensions.netcore.setup.3.7.1.nupkg.sha512"}, "AWSSDK.S3/**********": {"type": "package", "serviceable": true, "sha512": "sha512-rAath6wuVmv8kaeXJK2vUzjvEpe2qMt7dkL5wWgQ11juqh3wyizWzytlsaBKyAmXs1EKqc9gF5GVW5PbjbWnaA==", "path": "awssdk.s3/**********", "hashPath": "awssdk.s3.**********.nupkg.sha512"}, "AWSSDK.SecurityToken/3.7.100.14": {"type": "package", "serviceable": true, "sha512": "sha512-dGCVuVo0CFUKWW85W8YENO+aREf8sCBDjvGbnNvxJuNW4Ss+brEU9ltHhq2KfZze2VUNK1/wygbPG1bmbpyXEw==", "path": "awssdk.securitytoken/3.7.100.14", "hashPath": "awssdk.securitytoken.3.7.100.14.nupkg.sha512"}, "AWSSDK.SimpleSystemsManagement/3.7.3.24": {"type": "package", "serviceable": true, "sha512": "sha512-fyGmcOFFitWMm+smKJj6MvNR/xU+jJZheP7K4hId33PQjokJVsNPLfRKUyvyPe7iUGdSMPWuU923lc1bbr+7eA==", "path": "awssdk.simplesystemsmanagement/3.7.3.24", "hashPath": "awssdk.simplesystemsmanagement.3.7.3.24.nupkg.sha512"}, "Castle.Core/4.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-zanbjWC0Y05gbx4eGXkzVycOQqVOFVeCjVsDSyuao9P4mtN1w3WxxTo193NGC7j3o2u3AJRswaoC6hEbnGACnQ==", "path": "castle.core/4.4.1", "hashPath": "castle.core.4.4.1.nupkg.sha512"}, "CompareNETObjects/4.57.0": {"type": "package", "serviceable": true, "sha512": "sha512-0uCvlZwCtYAjEvMwRZAqFnXtkGiXZCCgW1Y/fzH6k/yIVlN4pqaRwSfxRSKezFlg6UG1XXoSZtvmAEt7X2AKhg==", "path": "comparenetobjects/4.57.0", "hashPath": "comparenetobjects.4.57.0.nupkg.sha512"}, "Dapper/1.50.5": {"type": "package", "serviceable": true, "sha512": "sha512-1vPpX7WQmQCIb7rwlGOUoVs/yWZhVKvdhuG7WrJV+V+qsP8btnrrCqVWHENAlJxBAnUw5rhWfmuba9/Egei9MA==", "path": "dapper/1.50.5", "hashPath": "dapper.1.50.5.nupkg.sha512"}, "DeepCloner/0.10.2": {"type": "package", "serviceable": true, "sha512": "sha512-QJTEz5Y9m74S0gzarkIdljlbyx3gNOKM/UT9WZR5bS/pYZb6UX59QQWzLnu8KZc5jajQXtr/rHJcAGB39Ne6CA==", "path": "deepcloner/0.10.2", "hashPath": "deepcloner.0.10.2.nupkg.sha512"}, "DnsClient/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-4H/f2uYJOZ+YObZjpY9ABrKZI+JNw3uizp6oMzTXwDw6F+2qIPhpRl/1t68O/6e98+vqNiYGu+lswmwdYUy3gg==", "path": "dnsclient/1.6.1", "hashPath": "dnsclient.1.6.1.nupkg.sha512"}, "Elasticsearch.Net/7.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-MFjokKp8YrdnB1CR+LSveVa6j+pKWrHJtGV/b9A300eOyq+RWacJfIa0Qv/SsDqaUErga63J6RmqMM/ICi4vHA==", "path": "elasticsearch.net/7.1.0", "hashPath": "elasticsearch.net.7.1.0.nupkg.sha512"}, "EnyimMemcachedCore/2.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-BjyIhnS1/NVJ2WIxNaFQMFqsAIFgz9AUTOdvDkdRqJn09zb7QnW5hvWH9OJi/RLGQvI52I+hn6OFTUJRU/EAXw==", "path": "enyimmemcachedcore/2.1.8", "hashPath": "enyimmemcachedcore.2.1.8.nupkg.sha512"}, "Experimental.System.Messaging/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3iV3yEL3cq5UL0onKmdyF8Z180uRF4bQzOcKwS798Tx8L792aNXTLWiYuViKOT2J6tNqVpJh9agfPa2GnIWNsQ==", "path": "experimental.system.messaging/1.0.0", "hashPath": "experimental.system.messaging.1.0.0.nupkg.sha512"}, "FluentValidation/9.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-dnAz4PUtSSa+8EGWDUmEZujOa9rvzxln4BgokZZfO2Gbcj0612TMISU4vkvL60qIy5DI/sDF9Oy1iMXr7sJW3A==", "path": "fluentvalidation/9.5.4", "hashPath": "fluentvalidation.9.5.4.nupkg.sha512"}, "FluentValidation.AspNetCore/9.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-nb6RUGLlnMz0Ay/ihDqjagC/Rd2pg24qdxoPDLed5grCaHx6cXBmyzLqCCzGTtvpYS4i9bOwNvhbpRkXhV+VIA==", "path": "fluentvalidation.aspnetcore/9.5.4", "hashPath": "fluentvalidation.aspnetcore.9.5.4.nupkg.sha512"}, "FluentValidation.DependencyInjectionExtensions/9.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-jn1fxwhWJ20FJGYDJ1roozCBtnohtSIy//H/KTGgKBNriS7fUp9fTGs2GnTajK+Ei5k8UO1TUcPx6wBGEb32Zw==", "path": "fluentvalidation.dependencyinjectionextensions/9.5.4", "hashPath": "fluentvalidation.dependencyinjectionextensions.9.5.4.nupkg.sha512"}, "GreenPipes/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-nc90y7DAhj8isRbqioVQF3/ExztBZSXRrRoplZvEjckNFC5wP1r+ssfsgl8BptWdQrnMdgkOYhQ6EnHetyFW1Q==", "path": "greenpipes/4.0.1", "hashPath": "greenpipes.4.0.1.nupkg.sha512"}, "HtmlAgilityPack/1.11.46": {"type": "package", "serviceable": true, "sha512": "sha512-dLMn4EVfJBHWmWK4Uh0XGD76FPLHI0qr2Tm0s1m/xmgiHb1JUb9zB8AzO8HtrkBBlMN6JfCUBYddhqC0hZNR+g==", "path": "htmlagilitypack/1.11.46", "hashPath": "htmlagilitypack.1.11.46.nupkg.sha512"}, "Jurassic/3.2.6": {"type": "package", "serviceable": true, "sha512": "sha512-1UOQ8got1UW63iXQ99cpo9UzjjsBEsIdOna2DbcGrZmEdJY3PELsOYv7hLUIPKVqkTDVKEKnRBGKMphQNUTD8w==", "path": "jurassic/3.2.6", "hashPath": "jurassic.3.2.6.nupkg.sha512"}, "Magick.NET-Q16-AnyCPU/7.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-F6usuiffkAblVeAJ2Yq1InH0XW6XhNi0PzYLjQoVEszOy2jRJLzKWleOR+tIs0H4sDqCpbtXD/MZ5sLMMSK2Og==", "path": "magick.net-q16-anycpu/7.11.0", "hashPath": "magick.net-q16-anycpu.7.11.0.nupkg.sha512"}, "Mapster/7.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-NrCUX/rJa5PTyo6iW4AL5dZLU9PDNlYnrJOVjgdpo5OQM9EtWH2CMHnC5sSuJWC0d0b0SnmeRrIviEem6WxtuQ==", "path": "mapster/7.3.0", "hashPath": "mapster.7.3.0.nupkg.sha512"}, "Mapster.Core/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-TNdqZk2zAuBYfJF88D/3clQTOyOdqr1crU81yZQtlGa+e7FYWhJdK/buBWT+TpM3qQko9UzmzfOT4iq3JCs/ZA==", "path": "mapster.core/1.2.0", "hashPath": "mapster.core.1.2.0.nupkg.sha512"}, "MassTransit/7.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-dMOmC1ucSpgpW1M7ewQIw2Jf/d0lC4sx5LdUU5CSuFhlw4L7hssAB/glrbwvYZB9B90ZYvLJB8JLV1K/xhHBMA==", "path": "masstransit/7.3.1", "hashPath": "masstransit.7.3.1.nupkg.sha512"}, "MassTransit.ActiveMQ/7.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-nPoa3bKRI/e0ESeQ4cJdyXpOEFmicgof+yrsveB3pOPLM5KFjMIJItEHXRNCwzpefU7SZk5sssffOxZyuhntrw==", "path": "masstransit.activemq/7.3.3", "hashPath": "masstransit.activemq.7.3.3.nupkg.sha512"}, "MassTransit.Extensions.DependencyInjection/7.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-s1gnkYUngHNP9m2+Jx1vuUAvoVm4KB3oVbtFJUlXNtBBzhRFq1N3Y0w5iiew60ZBLm0j0RQcSRbI0ahMOkL+og==", "path": "masstransit.extensions.dependencyinjection/7.3.1", "hashPath": "masstransit.extensions.dependencyinjection.7.3.1.nupkg.sha512"}, "MassTransit.MongoDb/7.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-OpEmL39lEBeK5ySDB8zkskHdFoV6XEMDJ+1chgSVJKaIqvDla/B4prbJFQo78xYYBeslvBjLkk2xpWLXcthxtg==", "path": "masstransit.mongodb/7.3.1", "hashPath": "masstransit.mongodb.7.3.1.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-W8<PERSON>QjkMScOMTtJbPwmPyj9c3zYSFGawDW3jwlBOOsnY+EzZFLgNQ/UMkK35JmkNOVPdCyPr2Tw7Vv9N+KA3ZQ==", "path": "microsoft.bcl.asyncinterfaces/5.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.5.0.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/5.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-o+bBauEMOi6ZI0MlJEC69Sw9UPwKLFmN+lD942g9UCx5pfiLFvJBKp8OPmxtGFL02ZxzXCIUyhyKn85izBDsnQ==", "path": "microsoft.identitymodel.logging/5.3.0", "hashPath": "microsoft.identitymodel.logging.5.3.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/5.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/piauST4FL0qzVI6oqLWxqhFReg12KwVGy0jRlnVOpGMeOVSKdtNVtHsN/hARc25hOOPEp9WKMce5ILzyMx/tQ==", "path": "microsoft.identitymodel.tokens/5.3.0", "hashPath": "microsoft.identitymodel.tokens.5.3.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-LuI1oG+24TUj1ZRQQjM5Ew73BKnZE5NZ/7eAdh1o8ST5dPhUnJvIkiIn2re3MwnkRy6ELRnvEbBxHP8uALKhJw==", "path": "microsoft.win32.systemevents/4.5.0", "hashPath": "microsoft.win32.systemevents.4.5.0.nupkg.sha512"}, "MongoDB.Bson/2.21.0": {"type": "package", "serviceable": true, "sha512": "sha512-QT+D1I3Jz6r6S6kCgJD1L9dRCLVJCKlkGRkA+tJ7uLpHRmjDNcNKy4D1T+L9gQrjl95lDN9PHdwEytdvCW/jzA==", "path": "mongodb.bson/2.21.0", "hashPath": "mongodb.bson.2.21.0.nupkg.sha512"}, "MongoDB.Driver/2.21.0": {"type": "package", "serviceable": true, "sha512": "sha512-VxKj1wuhadiXhaXkykCWRgsYOysdaOYJ202hJFz25UjkrqC/tHA8RS4hdS5HYfGWoI//fypBXnxZCkEjXLXdfw==", "path": "mongodb.driver/2.21.0", "hashPath": "mongodb.driver.2.21.0.nupkg.sha512"}, "MongoDB.Driver.Core/2.21.0": {"type": "package", "serviceable": true, "sha512": "sha512-Ac44U3bQfinmdH5KNFjTidJe9LKW87SxkXJ3YuIUJQMITEc4083YF1yvjJxaSeYF9er0YgHSmwhHpsZv0Fwplg==", "path": "mongodb.driver.core/2.21.0", "hashPath": "mongodb.driver.core.2.21.0.nupkg.sha512"}, "MongoDB.Driver.GridFS/2.21.0": {"type": "package", "serviceable": true, "sha512": "sha512-8a6n05xXWGh/CsY/egbZi8NF5S14c7IO05/eqi6cEsInlu4Dd1ZofFd3e4v0vRYOjtjUXImQ4xFTgOFvFTIAgg==", "path": "mongodb.driver.gridfs/2.21.0", "hashPath": "mongodb.driver.gridfs.2.21.0.nupkg.sha512"}, "MongoDB.Libmongocrypt/1.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-fgNw8Dxpkq7mpoaAYes8cfnPRzvFIoB8oL9GPXwi3op/rONftl0WAeg4akRLcxfoVuUvuUO2wGoVBr3JzJ7Svw==", "path": "mongodb.libmongocrypt/1.8.0", "hashPath": "mongodb.libmongocrypt.1.8.0.nupkg.sha512"}, "MongolianBarbecue/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Qeg8JGFCOGh8CmBMVJiE2ABujgFXtX1lDQtXJ+3cUfp2Ut55EO0Da9sMxKUBSx4Lzpivzy27TONGUMbgoAAiEQ==", "path": "mongolianbarbecue/1.0.0", "hashPath": "mongolianbarbecue.1.0.0.nupkg.sha512"}, "NEST/7.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-/Ij6XS5SnTeeZudTHypO8IlopiMZmyeljim7fz02UxwQPZ7duSmuw/bZu+ixKgXSWXGDH0thkdawv/81RZZKdA==", "path": "nest/7.1.0", "hashPath": "nest.7.1.0.nupkg.sha512"}, "NewId/3.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-72cBqYQ+pZpsfuTerhi5Q/ZkfK81s/Dkz2u0WNytirWVLktUm6sy4mRNepM+DL1AB0V61QlYesZHDf0wlmMU5A==", "path": "newid/3.0.3", "hashPath": "newid.3.0.3.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-QYFyxhaABwmq3p/21VrZNYvCg3DaEoN/wUuw5nmfAf0X3HLjgupwhkEWdgfb9nvGAUIv3osmZoD3kKl4jxEmYQ==", "path": "newtonsoft.json.bson/1.0.2", "hashPath": "newtonsoft.json.bson.1.0.2.nupkg.sha512"}, "runtime.native.System.Data.SqlClient.sni/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-AJfX7owAAkMjWQYhoml5IBfXh8UyYPjktn8pK0BFGAdKgBS7HqMz1fw5vdzfZUWfhtTPDGCjgNttt46ZyEmSjg==", "path": "runtime.native.system.data.sqlclient.sni/4.5.0", "hashPath": "runtime.native.system.data.sqlclient.sni.4.5.0.nupkg.sha512"}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "Scrutor/3.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-eevFT6R8vDIB4bVDxFeEGQQJX3o7xUYcKMluf6XnnyPJEV3P4OP2oQmKXxgcy6qEkJXgEMddkVvgPl9J0dx09A==", "path": "scrutor/3.0.2", "hashPath": "scrutor.3.0.2.nupkg.sha512"}, "SharpCompress/0.30.1": {"type": "package", "serviceable": true, "sha512": "sha512-XqD4TpfyYGa7QTPzaGlMVbcecKnXy4YmYLDWrU+JIj7IuRNl7DH2END+Ll7ekWIY8o3dAMWLFDE1xdhfIWD1nw==", "path": "sharpcompress/0.30.1", "hashPath": "sharpcompress.0.30.1.nupkg.sha512"}, "SharpZipLib/1.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-N8+hwhsKZm25tDJfWpBSW7EGhH/R7EMuiX+KJ4C4u+fCWVc1lJ5zg1u3S1RPPVYgTqhx/C3hxrqUpi6RwK5+Tg==", "path": "sharpziplib/1.3.3", "hashPath": "sharpziplib.1.3.3.nupkg.sha512"}, "Snappier/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rFtK2KEI9hIe8gtx3a0YDXdHOpedIf9wYCEYtBEmtlyiWVX3XlCNV03JrmmAi/Cdfn7dxK+k0sjjcLv4fpHnqA==", "path": "snappier/1.0.0", "hashPath": "snappier.1.0.0.nupkg.sha512"}, "Swashbuckle.AspNetCore/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Zq6lkFieuFPNDwXwQ+e8i5zy2VMrexcRFU8mQORxqIc8r7Y+qKX63vg57yL1HeGCINHQGGzxGfw2rP63IeEqhg==", "path": "swashbuckle.aspnetcore/4.0.1", "hashPath": "swashbuckle.aspnetcore.4.0.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.Annotations/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Eke+OE1T+ogLsSmVFdhFPaVlsCBvGfkO/qpOgmD8pBns5vUMaI3pHjKq4sg9FfK8lLK/cxFbLx8Q+/iGogJ+Xw==", "path": "swashbuckle.aspnetcore.annotations/4.0.1", "hashPath": "swashbuckle.aspnetcore.annotations.4.0.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.Filters/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-/nLPAqxdWu9KbpXSNJn3uchv/Ct8gNrNF3zaAiTGG2KdWC8DodgX+1kIKoHO/yGoHM5cHKS/z2233wFL3dGMvg==", "path": "swashbuckle.aspnetcore.filters/4.5.5", "hashPath": "swashbuckle.aspnetcore.filters.4.5.5.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-rqzS3vHrjz+tR5j0nZOKZyaMTDfLGbVYkwMq205aYuGbsiGwbOlNU0Q8lq4Q0ptQPMKVkUf8XouCIdJ3qpK17w==", "path": "swashbuckle.aspnetcore.swagger/4.0.1", "hashPath": "swashbuckle.aspnetcore.swagger.4.0.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ztAj0T1U+2AqQNA8b+nr8yVkDW9XzNaAfez6d1jO13sdn2A/JW5Syn9TThsakrHxYNLt6y6aQCXbyBfQXpcQwA==", "path": "swashbuckle.aspnetcore.swaggergen/4.0.1", "hashPath": "swashbuckle.aspnetcore.swaggergen.4.0.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-d2U7NyV0e4UhyCzAVK9QHm0iz2QoVPUa9XzJ/Gr0rn/jBZWFpVLvigKv0vxFzO2E793sY605+4h885gvCdKSxQ==", "path": "swashbuckle.aspnetcore.swaggerui/4.0.1", "hashPath": "swashbuckle.aspnetcore.swaggerui.4.0.1.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.ComponentModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyGn1jGRZVfxnh8EdvDCi71v3bMXrsu8aYJOwoV7SNDLVhiEqwP86pPMyRGsDsxhXAm2b3o9OIqeETfN5qfezw==", "path": "system.componentmodel/4.3.0", "hashPath": "system.componentmodel.4.3.0.nupkg.sha512"}, "System.ComponentModel.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-j8GUkCpM8V4d4vhLIIoBLGey2Z5bCkMVNjEZseyAlm4n5arcsJOeI3zkUP+zvZgzsbLTYh4lYeP/ZD/gdIAPrw==", "path": "system.componentmodel.primitives/4.3.0", "hashPath": "system.componentmodel.primitives.4.3.0.nupkg.sha512"}, "System.ComponentModel.TypeConverter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-16pQ6P+EdhcXzPiEK4kbA953Fu0MNG2ovxTZU81/qsCd1zPRsKc3uif5NgvllCY598k6bI0KUyKW8fanlfaDQg==", "path": "system.componentmodel.typeconverter/4.3.0", "hashPath": "system.componentmodel.typeconverter.4.3.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-UIFvaFfuKhLr9u5tWMxmVoDPkFeD+Qv8gUuap4aZgVGYSYMdERck4OhLN/2gulAc0nYTEigWXSJNNWshrmxnng==", "path": "system.configuration.configurationmanager/4.5.0", "hashPath": "system.configuration.configurationmanager.4.5.0.nupkg.sha512"}, "System.Data.SqlClient/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-gwItUWW1BMCckicFO85c8frFaMK8SGqYn5IeA3GSX4Lmid+CjXETfoHz7Uv+Vx6L0No7iRc/7cBL8gd6o9k9/g==", "path": "system.data.sqlclient/4.6.0", "hashPath": "system.data.sqlclient.4.6.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/4.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-j81Lovt90PDAq8kLpaJfJKV/rWdWuEk6jfV+MBkee33vzYLEUsy4gXK8laa9V2nZlLM9VM9yA/OOQxxPEJKAMw==", "path": "system.diagnostics.diagnosticsource/4.7.1", "hashPath": "system.diagnostics.diagnosticsource.4.7.1.nupkg.sha512"}, "System.Diagnostics.EventLog/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QaQAhEk18QSBPSu4VjXcznvjlg45IoXcJJNS5hcoqyyLj58g/SzQwpYXUrdzo+UtHV0grmOzFwABxhCYSTTp5Q==", "path": "system.diagnostics.eventlog/4.5.0", "hashPath": "system.diagnostics.eventlog.4.5.0.nupkg.sha512"}, "System.Diagnostics.TraceSource/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VnYp1NxGx8Ww731y2LJ1vpfb/DKVNKEZ8Jsh5SgQTZREL/YpWRArgh9pI8CDLmgHspZmLL697CaLvH85qQpRiw==", "path": "system.diagnostics.tracesource/4.3.0", "hashPath": "system.diagnostics.tracesource.4.3.0.nupkg.sha512"}, "System.Drawing.Common/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-GiyeGi/v4xYDz1vCNFwFvhz9k1XddOG7VD3jxRqzRBCbTHji+s3HxxbxtoymuK4OadEpgotI8zQ5+GEEH9sUEQ==", "path": "system.drawing.common/4.5.1", "hashPath": "system.drawing.common.4.5.1.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Private.DataContractSerialization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yDaJ2x3mMmjdZEDB4IbezSnCsnjQ4BxinKhRAaP6kEgL6Bb6jANWphs5SzyD8imqeC/3FxgsuXT6ykkiH1uUmA==", "path": "system.private.datacontractserialization/4.3.0", "hashPath": "system.private.datacontractserialization.4.3.0.nupkg.sha512"}, "System.Private.ServiceModel/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-ancrQgJagx+yC4SZbuE+eShiEAUIF0E1d21TRSoy1C/rTwafAVcBr/fKibkq5TQzyy9uNil2tx2/iaUxsy0S9g==", "path": "system.private.servicemodel/4.5.3", "hashPath": "system.private.servicemodel.4.5.3.nupkg.sha512"}, "System.Reflection.DispatchProxy/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-+UW1hq11TNSeb+16rIk8hRQ02o339NFyzMc4ma/FqmxBzM30l1c2IherBB4ld1MNcenS48fz8tbt50OW4rVULA==", "path": "system.reflection.dispatchproxy/4.5.0", "hashPath": "system.reflection.dispatchproxy.4.5.0.nupkg.sha512"}, "System.Reflection.Emit/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-VR4kk8XLKebQ4MZuKuIni/7oh+QGFmZW3qORd1GvBq/8026OpW501SzT/oypwiQl4TvT8ErnReh/NzY9u+C6wQ==", "path": "system.reflection.emit/4.7.0", "hashPath": "system.reflection.emit.4.7.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-a4OLB4IITxAXJeV74MDx49Oq2+PsF6Sml54XAFv+2RyWwtDBcabzoxiiJRhdhx+gaohLh4hEGCLQyBozXoQPqA==", "path": "system.reflection.emit.lightweight/4.7.0", "hashPath": "system.reflection.emit.lightweight.4.7.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-dkmh/ySlwnXJp/1qYP9uyKkCK1CXR/REFzl7abHcArxBcV91mY2CgrrzSRA5Z/X4MevJWwXsklGRdR3A7K9zbg==", "path": "system.reflection.typeextensions/4.4.0", "hashPath": "system.reflection.typeextensions.4.4.0.nupkg.sha512"}, "System.Runtime/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-abhfv1dTK6NXOmu4bgHIONxHyEqFjW8HwXPmpY9gmll+ix9UNo4XDcmzJn6oLooftxNssVHdJC1pGT9jkSynQg==", "path": "system.runtime/4.3.1", "hashPath": "system.runtime.4.3.1.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZD9TMpsmYJLrxbbmdvhwt9YEgG5WntEnZ/d1eH8JBX9LBp+Ju8BSBhUGbZMNVHHomWo2KVImJhTDl2hIgw/6MA==", "path": "system.runtime.compilerservices.unsafe/5.0.0", "hashPath": "system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512"}, "System.Runtime.Serialization.Xml/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-nUQx/5OVgrqEba3+j7OdiofvVq9koWZAC7Z3xGI8IIViZqApWnZ5+lLcwYgTlbkobrl/Rat+Jb8GeD4WQESD2A==", "path": "system.runtime.serialization.xml/4.3.0", "hashPath": "system.runtime.serialization.xml.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "path": "system.security.accesscontrol/5.0.0", "hashPath": "system.security.accesscontrol.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-wLBKzFnDCxP12VL9ANydSYhk59fC4cvOr9ypYQLPnAj48NQIhqnjdD2yhP8yEKyBJEjERWS9DisKL7rX5eU25Q==", "path": "system.security.cryptography.protecteddata/4.5.0", "hashPath": "system.security.cryptography.protecteddata.4.5.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.ServiceModel.Duplex/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-cXn6zfl2od9Au3sDpArjUXo7zmNPLw77sjOrAUqjrh3TsImy8SPMSC4/F58jJGJrxUiyPo0DDwalRaF5JXZqsQ==", "path": "system.servicemodel.duplex/4.5.3", "hashPath": "system.servicemodel.duplex.4.5.3.nupkg.sha512"}, "System.ServiceModel.Http/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-zxR4z6G/FFK/uAUbo7+3IJOqm0w4/lyfHSQDf+hhUHRTc7XSeReGS5iKQq95gyl1ighHEuayqOiB7iacrB6ZUg==", "path": "system.servicemodel.http/4.5.3", "hashPath": "system.servicemodel.http.4.5.3.nupkg.sha512"}, "System.ServiceModel.NetTcp/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-Bx4oVK4ApBvZ0C3J62A8p3j6U0XK54JjN0byK52Qw4EgK89Uc48XzbF+0m1Oysc2bnnbrur+SwFWw7J8co3jTQ==", "path": "system.servicemodel.nettcp/4.5.3", "hashPath": "system.servicemodel.nettcp.4.5.3.nupkg.sha512"}, "System.ServiceModel.Primitives/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-Wc9Hgg4Cmqi416zvEgq2sW1YYCGuhwWzspDclJWlFZqY6EGhFUPZU+kVpl5z9kAgrSOQP7/Uiik+PtSQtmq+5A==", "path": "system.servicemodel.primitives/4.5.3", "hashPath": "system.servicemodel.primitives.4.5.3.nupkg.sha512"}, "System.ServiceModel.Security/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-e0c5f536zJ2qEp8sDbI88Tm+NLkx9eqGiXQbQx5fQEtCfQ/dqPOwluu/3aAj/9Bc5XdBAaQcElmr1kyjr2j3EA==", "path": "system.servicemodel.security/4.5.3", "hashPath": "system.servicemodel.security.4.5.3.nupkg.sha512"}, "System.ServiceProcess.ServiceController/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-8DGUtcNHf9TlvSVemKMFiqcOWJ4OdGBgvpcGL/cYossGf5ApMQdPUQS8vXHTBmlbYAcG+JXsjMFGAHp2oJrr+Q==", "path": "system.serviceprocess.servicecontroller/4.5.0", "hashPath": "system.serviceprocess.servicecontroller.4.5.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NyscU59xX6Uo91qvhOs2Ccho3AR2TnZPomo1Z0K6YpyztBPM/A5VbkzOO19sy3A3i1TtEnTxA7bCe3Us+r5MWg==", "path": "system.text.encoding.codepages/5.0.0", "hashPath": "system.text.encoding.codepages.5.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KmJ+CJXizDofbq6mpqDoRRLcxgOd2z9X3XoFNULSbvbqVRZkFX3istvr+MUjL6Zw1RT+RNdoI4GYidIINtgvqQ==", "path": "system.text.encodings.web/5.0.1", "hashPath": "system.text.encodings.web.5.0.1.nupkg.sha512"}, "System.Text.Json/5.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-I47dVIGiV6SfAyppphxqupertT/5oZkYLDCX6vC3HpOI4ZLjyoKAreUoem2ie6G0RbRuFrlqz/PcTQjfb2DOfQ==", "path": "system.text.json/5.0.2", "hashPath": "system.text.json.5.0.2.nupkg.sha512"}, "System.Threading.AccessControl/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZU4JNV9eHPw3TAdIJCDH07u9EfGFGgNJnaga8aFjcdvIIZKq4A+ZqaQNvUMFIbdCMPceYzt8JT5MdYIXAOlJ9A==", "path": "system.threading.accesscontrol/4.5.0", "hashPath": "system.threading.accesscontrol.4.5.0.nupkg.sha512"}, "System.Threading.Channels/4.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-6akRtHK/wab3246t4p5v3HQrtQk8LboOt5T4dtpNgsp3zvDeM4/Gx8V12t0h+c/W9/enUrilk8n6EQqdQorZAA==", "path": "system.threading.channels/4.7.1", "hashPath": "system.threading.channels.4.7.1.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Xml.XmlSerializer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-MYoTCP7EZ98RrANESW05J5ZwskKDoN0AuZ06ZflnowE50LTpbR5yRg3tHckTVm5j/m47stuGgCrCHWePyHS70Q==", "path": "system.xml.xmlserializer/4.3.0", "hashPath": "system.xml.xmlserializer.4.3.0.nupkg.sha512"}, "TimeZoneConverter/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-U7Oilf3Ya6Rmu6gOaBfWyT3q0kwy2av6a5PfTn05CF54C+7DvuLsE3ljASvYmCpsSQeJvpnqU5Uzag6+ysWUeA==", "path": "timezoneconverter/5.0.0", "hashPath": "timezoneconverter.5.0.0.nupkg.sha512"}, "Twilio/5.20.1": {"type": "package", "serviceable": true, "sha512": "sha512-UKfpDK6032qFzcSAFZbSUlvphr/XZcOQ2epWXaV3w6O3d6DAUWA+pKtIxIBlocbCRJqVxvIJeKzF9DFgrX0Atw==", "path": "twilio/5.20.1", "hashPath": "twilio.5.20.1.nupkg.sha512"}, "Twilio.AspNet.Common/5.20.1": {"type": "package", "serviceable": true, "sha512": "sha512-Bexs6ktjjteOZWFWOSp6JHynA9WW2SeOZxjzQTBGBwlNrKvp+d0neRSpbbPGwakHd7BMCWA/SiKR6GC/Hc4eHA==", "path": "twilio.aspnet.common/5.20.1", "hashPath": "twilio.aspnet.common.5.20.1.nupkg.sha512"}, "Twilio.AspNet.Core/5.20.1": {"type": "package", "serviceable": true, "sha512": "sha512-1s/8rqdfyR5pUaoYDdzm3XADTePiJ1qcp2mQsUOYeAWSuKeJ5eDjsPRDydx8BJdQUPVSJVbBi3WdOOFFoh3MYQ==", "path": "twilio.aspnet.core/5.20.1", "hashPath": "twilio.aspnet.core.5.20.1.nupkg.sha512"}, "UAParser/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-q3HQawDOf6+8lzYyc+WDSPO2Sz0F5SpD7jaBm22bBy1i1/DtHTw7rPfuX5J7IRDVXxkJXp5QTL1NIvXCaKTkvQ==", "path": "uaparser/3.0.0", "hashPath": "uaparser.3.0.0.nupkg.sha512"}, "ZstdSharp.Port/0.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-jPao/LdUNLUz8rn3H1D8W7wQbZsRZM0iayvWI4xGejJg3XJHT56gcmYdgmCGPdJF1UEBqUjucCRrFB+4HbJsbw==", "path": "zstdsharp.port/0.6.2", "hashPath": "zstdsharp.port.0.6.2.nupkg.sha512"}, "CommonLibCore/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "EBizAutos.ApplicationCommonLib/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "EBizAutos.Apps.Authentication.CommonLib/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "EBizAutos.Apps.Authentication.MongoDbRepository/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "EBizAutos.Apps.Common.MongoDbRepository/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "EBizAutos.Apps.CommonLib/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "EBizAutos.Apps.MongoDbRepository/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "EBizAutos.Apps.ServiceBus/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "FoundationCommonLib/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "CommonLibCore.Reference/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "FoundationCommonLib.Reference/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Libuv/1.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-GsCf4q+eyaI49rCPlgYxdxa1SQCysXFFdSJWdstrwxytg4+VPYLYrXD4AT2rjHVJ+UF7SSWX9CapWEYaU4ejVQ==", "path": "libuv/1.10.0", "hashPath": "libuv.1.10.0.nupkg.sha512"}, "MessagePack/*******": {"type": "package", "serviceable": true, "sha512": "sha512-QSKZVq6BmNaz/B4n0bAM/moQnc4E6XZ7uXRbJcEXxUzZufJiDYDE6awYqDtNz6acupYoPt2QupGV4rpyPCRRtg==", "path": "messagepack/*******", "hashPath": "messagepack.*******.nupkg.sha512"}, "Microsoft.ApplicationInsights/2.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-4dX/zu3Psz9oM3ErU64xfOHuSxOwMxN6q5RabSkeYbX42Yn6dR/kDToqjs+txCRjrfHUxyYjfeJHu+MbCfvAsg==", "path": "microsoft.applicationinsights/2.4.0", "hashPath": "microsoft.applicationinsights.2.4.0.nupkg.sha512"}, "Microsoft.ApplicationInsights.AspNetCore/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-kiGmzl9Cav34dF7AHVMoJxdJJQEeLB8KZGNwX1LjImG9iem5hGk4DkHpW7/m9Nh3DrL8IKSL3mqQo+IPqWfMRQ==", "path": "microsoft.applicationinsights.aspnetcore/2.1.1", "hashPath": "microsoft.applicationinsights.aspnetcore.2.1.1.nupkg.sha512"}, "Microsoft.ApplicationInsights.DependencyCollector/2.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-RWxdX90MY6tNF8S5lwRvJcHiBMIWwVLCxd4TGIEl3X0yAKaolY2vs4zTCvyCIVkEAMs1aInTgWkYwOjzYvAHWw==", "path": "microsoft.applicationinsights.dependencycollector/2.4.1", "hashPath": "microsoft.applicationinsights.dependencycollector.2.4.1.nupkg.sha512"}, "Microsoft.AspNet.WebApi.Client/5.2.6": {"type": "package", "serviceable": true, "sha512": "sha512-owAlEIUZXWSnkK8Z1c+zR47A0X6ykF4XjbPok4lQKNuciUfHLGPd6QnI+rt/8KlQ17PmF+I4S3f+m+Qe4IvViw==", "path": "microsoft.aspnet.webapi.client/5.2.6", "hashPath": "microsoft.aspnet.webapi.client.5.2.6.nupkg.sha512"}, "Microsoft.AspNetCore/2.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-8evH1sKqYgOQMSPjfX11aZL7fRLCC3jQBEYzr9Egl5y7U9OeixhiWf+xd8ZBja86cJMV7cRw5NfHkr3VoPdyIg==", "path": "microsoft.aspnetcore/2.1.6", "hashPath": "microsoft.aspnetcore.2.1.6.nupkg.sha512"}, "Microsoft.AspNetCore.All/2.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-blAzb5khmtq8C/CDEUVKg8ulT5ssaRef8Kt3vi+AbxOiKHFx/hAuneSRfQ2cXx9soH91AuEToR1sCZgGb8LzzQ==", "path": "microsoft.aspnetcore.all/2.1.6", "hashPath": "microsoft.aspnetcore.all.2.1.6.nupkg.sha512"}, "Microsoft.AspNetCore.Antiforgery/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-De4NysQJXeWiyzjCH+zE+hVeB7mgCelz00zsBFqkrFtgLWaint5Xt/4qACxRVLUGHQsUo48V6lG0entMJMwv3Q==", "path": "microsoft.aspnetcore.antiforgery/2.1.1", "hashPath": "microsoft.aspnetcore.antiforgery.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.ApplicationInsights.HostingStartup/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-jP7ww3d5HBV35jZEkPANGVWQHM0aJ2z0x08dPgMOgtTN2wxTDdR8DgpD1uOlbRxsBuA/8bM2zny2HJ/5dP7INg==", "path": "microsoft.aspnetcore.applicationinsights.hostingstartup/2.1.1", "hashPath": "microsoft.aspnetcore.applicationinsights.hostingstartup.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-RYM3HHMm/MNwsbUh1xnrhosAGNeZV2Q/FmNQrblgytIK1HIZ6UqNMorFI+kz2MW7gNKHKn6TBLTUXPRmqC6iRQ==", "path": "microsoft.aspnetcore.authentication/2.1.2", "hashPath": "microsoft.aspnetcore.authentication.2.1.2.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Smj5TGeE9629+hGHPk/DZUfCMYGvQwCajAsU/OVExRb8JXfeua4uXZFzT9Kh3pJY2MThPSt1lbDnkL2KaDyw/A==", "path": "microsoft.aspnetcore.authentication.abstractions/2.1.1", "hashPath": "microsoft.aspnetcore.authentication.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Cookies/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-XwE4/p9QHJOkoSWYdgx3u3Jhx6+NQZuRWGJT7jsdlpfDJeS3gJWEqIM9pBmrdt803sX2WZDpgm8hxGIAtiJcQQ==", "path": "microsoft.aspnetcore.authentication.cookies/2.1.2", "hashPath": "microsoft.aspnetcore.authentication.cookies.2.1.2.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Core/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Zo6SLzqxrW0PFg1AB0xSb+Rta4hCuX8hgOY425ldhFq4kKcmw45oJQ2zOIeeW/6EuBtEy+hwDB96baxTmXtfeA==", "path": "microsoft.aspnetcore.authentication.core/2.1.1", "hashPath": "microsoft.aspnetcore.authentication.core.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Facebook/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-5p5VeM2M9GdxprQRUOFkA/XFAnJad7s/9mRs3I2oIM8I6U4RMmn/kOJPnULrZyoPBJraRSvg9fE2khTtCv05Bg==", "path": "microsoft.aspnetcore.authentication.facebook/2.1.2", "hashPath": "microsoft.aspnetcore.authentication.facebook.2.1.2.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Google/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-glh6ph3/0Q6iwceQCeE1fIH+AWWJB3id+36xUopVtIl247VEfCOfIZEhRj477y5vqqUoazTfTdtgLg52JEquLw==", "path": "microsoft.aspnetcore.authentication.google/2.1.2", "hashPath": "microsoft.aspnetcore.authentication.google.2.1.2.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-hedJ6YxyZRIJPXC2olml9YM5t3+yIFt9hcyBXp/q5+z4XpJMY5gUwbc1F5QRGEdcvwSCpFmmRLVaPPh4Kiaq0g==", "path": "microsoft.aspnetcore.authentication.jwtbearer/2.1.2", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.2.1.2.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.MicrosoftAccount/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-qeraPHfzag8lY/o6A63vxiOXrWC3t0y05FxE54y4gEDz/CbQ8xYYZ4yfKJscs2HruAjLSNTTI7j4XQD5GM22Bg==", "path": "microsoft.aspnetcore.authentication.microsoftaccount/2.1.2", "hashPath": "microsoft.aspnetcore.authentication.microsoftaccount.2.1.2.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.OAuth/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-oY0gzBiLXjfbJJYK3zDh8KjqYzvje+kKrK6ZpbojWNy826XO2KVLapjmk1/olrjg2bAKLTL6HHVk0xmZhPRBRA==", "path": "microsoft.aspnetcore.authentication.oauth/2.1.2", "hashPath": "microsoft.aspnetcore.authentication.oauth.2.1.2.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.OpenIdConnect/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-XdbxZFGrppRVFpRfPyu6qH1tGAHqBkrn/0uTGDQngTCJOnHtpHXNyQgOg/yj2UUCeJxjOX9p19F065ljiYBIxg==", "path": "microsoft.aspnetcore.authentication.openidconnect/2.1.2", "hashPath": "microsoft.aspnetcore.authentication.openidconnect.2.1.2.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Twitter/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-UhMS0G20OYLascJsKjgKfSgqKfwrUvx6E17cMYdaLkVGbwDQ6tboQ8LvrrVsKeVpSjtqOb7LA1uCk/7KigJKDA==", "path": "microsoft.aspnetcore.authentication.twitter/2.1.2", "hashPath": "microsoft.aspnetcore.authentication.twitter.2.1.2.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.WsFederation/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-KJj1Cel2e8cxBYQl819pWxAKTTgjW205OuCp4GvDEJZmFUvQS7BEAOpq9eivvskoIRB+LQiH3JkbBSLRUE8X5g==", "path": "microsoft.aspnetcore.authentication.wsfederation/2.1.2", "hashPath": "microsoft.aspnetcore.authentication.wsfederation.2.1.2.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-EiMovaF/QszHjp9SBJH2gUPecAmBDUZCm7sNsCSAf32dhfJMrsbXtiFz91LPXkQOz483u9P5fz8q24INJP/WTQ==", "path": "microsoft.aspnetcore.authorization/2.1.2", "hashPath": "microsoft.aspnetcore.authorization.2.1.2.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization.Policy/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-sf3Ai2X0/os2MVptDvWNmBfDt6YZN5N2AQ8z6vk+JU97oW+Nr6HH5h7aWaiacpj2cWPTgBQdUocGvA4ckYdCqQ==", "path": "microsoft.aspnetcore.authorization.policy/2.1.2", "hashPath": "microsoft.aspnetcore.authorization.policy.2.1.2.nupkg.sha512"}, "Microsoft.AspNetCore.AzureAppServices.HostingStartup/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-G36yI0jmnk2MCk+Jpn2J22uUlfIC862ZE//t0zhLI329HpigriB9kBHRdjED0uwO9rsDXswQYdUeSrzHOSoQHg==", "path": "microsoft.aspnetcore.azureappservices.hostingstartup/2.1.1", "hashPath": "microsoft.aspnetcore.azureappservices.hostingstartup.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.AzureAppServicesIntegration/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-ZjU0JDgyw5JpwHpK+/JHo7ayV7Scf9kS9IZuxlpwej178WyG7U/RMbyD89AhR4nkKBiqKysrtlGsPlUyyvDvZQ==", "path": "microsoft.aspnetcore.azureappservicesintegration/2.1.1", "hashPath": "microsoft.aspnetcore.azureappservicesintegration.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Connections.Abstractions/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-ZpizM+XToYZUBK9F/V5mVgQ5yFA8H7t9Ba4UiDrBuYkzBGVh/e+87A29OHZSaJh+dns8GUTP6IucQ6713ZQwQQ==", "path": "microsoft.aspnetcore.connections.abstractions/2.1.3", "hashPath": "microsoft.aspnetcore.connections.abstractions.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.CookiePolicy/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-R/Nf9lJ1gXDvx5PpHBlD7/FljSjvJsL8tKCmNP9jrBHsnbGiAUuuprXsaR6ON+6FPCKtlAyDsyTk0zwxGTaAtQ==", "path": "microsoft.aspnetcore.cookiepolicy/2.1.2", "hashPath": "microsoft.aspnetcore.cookiepolicy.2.1.2.nupkg.sha512"}, "Microsoft.AspNetCore.Cors/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-5b3xfO8ycP9fEm76HGdExptlxURKNbmGnlA2mN+FQMaWPEuFH1te6GReBcKCQp4oeSSWuLfV9xSo+8LpU24u1A==", "path": "microsoft.aspnetcore.cors/2.1.1", "hashPath": "microsoft.aspnetcore.cors.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.Internal/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-guY3jMNkcUi2hrMJ4/vPnUUFwudxTVSJ809gCfpq+xR0UgV6P9ZHZLOI5q/07QHDZY+kKPXxipXGyJXQpq2k0g==", "path": "microsoft.aspnetcore.cryptography.internal/2.1.1", "hashPath": "microsoft.aspnetcore.cryptography.internal.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Dgr1YF3+UK8i60n/Ae3gml4WgUxd2YcJEMADToRReOO4Nl4++mz8HjZtxsb3WWeGRtGPkrIgNhJD5MO0bjFkTg==", "path": "microsoft.aspnetcore.cryptography.keyderivation/2.1.1", "hashPath": "microsoft.aspnetcore.cryptography.keyderivation.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-OPZDPAAL3OwOCrz870F9goq//NJOmPl4Lv3dz+v0cRQe8EpsbCe0c6IRI8vdlFwM13Qy57D5rLQlysb+tLpENA==", "path": "microsoft.aspnetcore.dataprotection/2.1.1", "hashPath": "microsoft.aspnetcore.dataprotection.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-dcH52SMIIUOwBeDZ2QQEY3hWXJz50Dk2YzC/B2hxDLB78Il75BHGOhClIw6/0H+dKZCwItUytxoMNYtCSmG+aQ==", "path": "microsoft.aspnetcore.dataprotection.abstractions/2.1.1", "hashPath": "microsoft.aspnetcore.dataprotection.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection.AzureKeyVault/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-dJNdh1TKeOi1lPdpM4y0uDDXozD+RVH2Xfqm8mn4zXZEOomq806KsjwMYnP7Bo+d/3ZcCegk0QbrI19bcVpiBQ==", "path": "microsoft.aspnetcore.dataprotection.azurekeyvault/2.1.1", "hashPath": "microsoft.aspnetcore.dataprotection.azurekeyvault.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection.AzureStorage/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-8JUmnpZYQNg5IyHrrWfhqU4JAHivKTWieVMlwoY9pyZ+OC17JMO2j/n2B4cUv6j9XdTpgQqeilWD1KC4Ei0Feg==", "path": "microsoft.aspnetcore.dataprotection.azurestorage/2.1.1", "hashPath": "microsoft.aspnetcore.dataprotection.azurestorage.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection.Extensions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-ceD0XYTCxNACik38XmHEIPgjqMdL66jDOu68pjLm9R+VPT2PWAWww3ihTmGOfLPnQuCnf9gCcQxR33rwRcdR9Q==", "path": "microsoft.aspnetcore.dataprotection.extensions/2.1.1", "hashPath": "microsoft.aspnetcore.dataprotection.extensions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Diagnostics/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-N0s12z4ZOa2Gxj+c23RRjj7MnGrgX3eeBUSenz2yUb4DLY48CBQt+m6ROPv+imY7evhGPRP7HvAtRsJhKJ2UVg==", "path": "microsoft.aspnetcore.diagnostics/2.1.1", "hashPath": "microsoft.aspnetcore.diagnostics.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Diagnostics.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-W4V3uJY3mIUZbmon6MKOVr16r/NPgn/ey06L+BKf6uzXPua1Tzwlkz5h101b/Ncaown0iEJz5Pm6heYj+Fr/WQ==", "path": "microsoft.aspnetcore.diagnostics.abstractions/2.1.1", "hashPath": "microsoft.aspnetcore.diagnostics.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-w83RRqB1P8T/SiNV8BXdlTmWouPa0Ev9DjvVdvGZTo0ZTR3pq29ZtwVz/EgKStK6Y0n/TNJUBdOxW7+8Xg7K4A==", "path": "microsoft.aspnetcore.diagnostics.entityframeworkcore/2.1.1", "hashPath": "microsoft.aspnetcore.diagnostics.entityframeworkcore.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.HostFiltering/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-tTlWJ/2Br7W7AtBj5ufWKD0oZBs1rJ5/GIN15PLIHmDPMWCHgxeX+F5tLFgkSoCmQWOJAPy+thltfgpz9Gkp6g==", "path": "microsoft.aspnetcore.hostfiltering/2.1.1", "hashPath": "microsoft.aspnetcore.hostfiltering.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-MqYc0DUxrhAPnb5b4HFspxsoJT+gJlLsliSxIgovf4BsbmpaXQId0/pDiVzLuEbmks2w1/lRfY8w0lQOuK1jQQ==", "path": "microsoft.aspnetcore.hosting/2.1.1", "hashPath": "microsoft.aspnetcore.hosting.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-76cKcp2pWhvdV2TXTqMg/DyW7N6cDzTEhtL8vVWFShQN+Ylwv3eO/vUQr2BS3Hz4IZHEpL+FOo2T+MtymHDqDQ==", "path": "microsoft.aspnetcore.hosting.abstractions/2.1.1", "hashPath": "microsoft.aspnetcore.hosting.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-+vD7HJYzAXNq17t+NgRkpS38cxuAyOBu8ixruOiA3nWsybozolUdALWiZ5QFtGRzajSLPFA2YsbO3NPcqoUwcw==", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.1.1", "hashPath": "microsoft.aspnetcore.hosting.server.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Html.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-CS/2N0d0JUdhYOrnd9Ll6O2Lb++CQaToKem6NyF+9RIgdL3tEZJOJHXcFWSXUSDqML98XQzbtnV+dCT22cBrRw==", "path": "microsoft.aspnetcore.html.abstractions/2.1.1", "hashPath": "microsoft.aspnetcore.html.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Http/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-pPDcCW8spnyibK3krpxrOpaFHf5fjV6k1Hsl6gfh77N/8gRYlLU7MOQDUnjpEwdlHmtxwJKQJNxZqVQOmJGRUw==", "path": "microsoft.aspnetcore.http/2.1.1", "hashPath": "microsoft.aspnetcore.http.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-kQUEVOU4loc8CPSb2WoHFTESqwIa8Ik7ysCBfTwzHAd0moWovc9JQLmhDIHlYLjHbyexqZAlkq/FPRUZqokebw==", "path": "microsoft.aspnetcore.http.abstractions/2.1.1", "hashPath": "microsoft.aspnetcore.http.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Connections/1.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-PCheQjOXi2pmGRmrixfIlYQuEwl4y/jVIk6MpgOdgEWyHL3nlPTjQpyOuIKYuE56xg2LDNYuNIKgHTCTvy4ZWA==", "path": "microsoft.aspnetcore.http.connections/1.0.4", "hashPath": "microsoft.aspnetcore.http.connections.1.0.4.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Connections.Common/1.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-vllvIeTpVzCDs5kPEdICbvcmxLmRAlJUxG6bG55tf29Wfp/ehHQza5P14S1hFl4Ff7kuxJw5VoqSUHD0gaJMKg==", "path": "microsoft.aspnetcore.http.connections.common/1.0.4", "hashPath": "microsoft.aspnetcore.http.connections.common.1.0.4.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Extensions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-ncAgV+cqsWSqjLXFUTyObGh4Tr7ShYYs3uW8Q/YpRwZn7eLV7dux5Z6GLY+rsdzmIHiia3Q2NWbLULQi7aziHw==", "path": "microsoft.aspnetcore.http.extensions/2.1.1", "hashPath": "microsoft.aspnetcore.http.extensions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-VklZ7hWgSvHBcDtwYYkdMdI/adlf7ebxTZ9kdzAhX+gUs5jSHE9mZlTamdgf9miSsxc1QjNazHXTDJdVPZKKTw==", "path": "microsoft.aspnetcore.http.features/2.1.1", "hashPath": "microsoft.aspnetcore.http.features.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.HttpOverrides/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-7oPPKBQLOWwcdhjcLO8ItuP7Br0Ytjpdq+x5j65XaTeKiD9JPSVadP8ceLoyzttnf7mhY3PuCsyTPbmsDzcclw==", "path": "microsoft.aspnetcore.httpoverrides/2.1.1", "hashPath": "microsoft.aspnetcore.httpoverrides.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.HttpsPolicy/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-NpdDAjvK2ElehzeOO8nB3tHj8SOFxbSvTSTsPHA5hfeY782BqSvEl9+o5YMVosIRES0o5jkqgzJDlLdn3kT2OQ==", "path": "microsoft.aspnetcore.httpspolicy/2.1.1", "hashPath": "microsoft.aspnetcore.httpspolicy.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Identity/2.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-vKCHmKW04KIONhGtbt/mSXEbrjn1q17MKhi47olOugvMflozcLJ5QoqAp4HmCsvhcmE8BSSTxKuMJyDk/sLKyw==", "path": "microsoft.aspnetcore.identity/2.1.6", "hashPath": "microsoft.aspnetcore.identity.2.1.6.nupkg.sha512"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/2.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-zwFpzjQ1OcOq4osV5sdRTP5wCfoDPdsBMggRvE81KsKfht0mR6lFmG7sbFsaXALa28kVowHA7MXCzDmmkc9skw==", "path": "microsoft.aspnetcore.identity.entityframeworkcore/2.1.6", "hashPath": "microsoft.aspnetcore.identity.entityframeworkcore.2.1.6.nupkg.sha512"}, "Microsoft.AspNetCore.Identity.UI/2.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-36HxNdX22BYx0Aov4RO+l7yBmCFon6O6wKpSLOaq46dUI4hN4s81CqiouagjkaS/bO/9nGSqpwxnXPXHkzBNyg==", "path": "microsoft.aspnetcore.identity.ui/2.1.6", "hashPath": "microsoft.aspnetcore.identity.ui.2.1.6.nupkg.sha512"}, "Microsoft.AspNetCore.JsonPatch/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-VjTsHQQG5H8Gjw6oi3jLUc6Wnc9Gnj1alQIwVsbfxuoXS5j0rTpzIKcRNyppEf0eQfI5fV/IDPJxgxV0NK5Xgw==", "path": "microsoft.aspnetcore.jsonpatch/2.1.1", "hashPath": "microsoft.aspnetcore.jsonpatch.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Localization/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-vq/zYk4PxqLdhQq269RgmT9Tp44cEMYFm4aFU6B61TMzUyHIjiIYTvNcuAI+5VVBU6n6GfExxeF11J3U4Pzupw==", "path": "microsoft.aspnetcore.localization/2.1.1", "hashPath": "microsoft.aspnetcore.localization.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Localization.Routing/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-8EvpC+Crv3pkrPioRo+/mzEDYeCQ550oeYYPXjpiP6RWCQ/miUQa6ZdYvMYlcRawDFYGqlCYeeSBZCn0lcwu6Q==", "path": "microsoft.aspnetcore.localization.routing/2.1.1", "hashPath": "microsoft.aspnetcore.localization.routing.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.MiddlewareAnalysis/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-dBj5AUA488Clf+J9eOO/en8FBb0sq9sYS0Ptghw+jm9XLUtSCKte3PKGmKg3dz0sC2OroF60Qf3q4P3RzSr6bQ==", "path": "microsoft.aspnetcore.middlewareanalysis/2.1.1", "hashPath": "microsoft.aspnetcore.middlewareanalysis.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-3FMnUZwgIr6VmvkLvozRuSEYuPgUHC1l81PLN4SmR/UDdJYKFDwIknGVYHZBAHDUB3WB/joCYHjuUENM6PvuYg==", "path": "microsoft.aspnetcore.mvc/2.1.3", "hashPath": "microsoft.aspnetcore.mvc.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Abstractions/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-G0oQiJ9okq+QbH9HBbmxu8/+Vhv063Dt06RzJPzsw7/uFT7Tvq5XHU5LI3b9qudyotJIRfYBbJRNeZyXEc+ALw==", "path": "microsoft.aspnetcore.mvc.abstractions/2.1.3", "hashPath": "microsoft.aspnetcore.mvc.abstractions.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Analyzers/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-+jCrExxJmYHN8E1/cKWb7qU6rwYHaC5kzPxIZNBM17kvzO6HMOev11QtCxM4uyPbQ1JeKRXIGJhBhwZSzCsd6g==", "path": "microsoft.aspnetcore.mvc.analyzers/2.1.3", "hashPath": "microsoft.aspnetcore.mvc.analyzers.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.ApiExplorer/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-KQVzqCWhizoCkXDXe6DU1NkSfN7/X9v8juVRV/yRh2HbsLZlEwiMPLI8qA6x/OIjP3U6dwHMemsMQCbNH3YbCw==", "path": "microsoft.aspnetcore.mvc.apiexplorer/2.1.3", "hashPath": "microsoft.aspnetcore.mvc.apiexplorer.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Core/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-qu2EOWIqz/KFw2WV0IDltHLoKjfWr60mWl9waPJwuwpjwycaDimu8fjOEigY941tMZoWjv/ZUi2kQGKHov10/g==", "path": "microsoft.aspnetcore.mvc.core/2.1.3", "hashPath": "microsoft.aspnetcore.mvc.core.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Cors/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-apH1jf7/D88YJbVRGnzrslOgEMKBRDnDtfSudQiMH/w13uu7FwfdJSAvFFwHQvODgGPVnmeZetsUXcvd4ySATQ==", "path": "microsoft.aspnetcore.mvc.cors/2.1.3", "hashPath": "microsoft.aspnetcore.mvc.cors.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.DataAnnotations/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-ume8mo6v/hCk2OmkYP45Au5rg+FUYCpSWSbDQGHlAo4NLspHa6MB+D4INiiEzvTXC4d738E4DzkdaKc7+PYcAQ==", "path": "microsoft.aspnetcore.mvc.dataannotations/2.1.3", "hashPath": "microsoft.aspnetcore.mvc.dataannotations.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Formatters.Json/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-a3tyBmMy1onYZbDHrbJ7nuE4xEQUSdD76T2KlE68s7xtANhIdbC/mW1FGTEZKzXawBygOaVVS7A1OzIiduxjUw==", "path": "microsoft.aspnetcore.mvc.formatters.json/2.1.3", "hashPath": "microsoft.aspnetcore.mvc.formatters.json.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Formatters.Xml/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-sRbWD1FTQG0V/+fdKSQO3NlU77VT09IK1SqC0gdiJfi5QghpS0yDYT/P365ro95tMR92actd+SoHplBkTaWA0Q==", "path": "microsoft.aspnetcore.mvc.formatters.xml/2.1.3", "hashPath": "microsoft.aspnetcore.mvc.formatters.xml.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Localization/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-sTG7gccAlN5xcwYgR+DLwoGL2Nsf9YTo7JpNHnLz44BdcoIt2WrsQnBy6Xg0+X0iK6sCn2JcB6i8sHMA/QAxgA==", "path": "microsoft.aspnetcore.mvc.localization/2.1.3", "hashPath": "microsoft.aspnetcore.mvc.localization.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Razor/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-A6H7GV9NdMswPXui/MKi337kk3uKqXyLCQ6qjjL3XXmS4kn5G7AOhkOJ5YswbtW1ssP/rBC1E5iK2QX3bFCHew==", "path": "microsoft.aspnetcore.mvc.razor/2.1.3", "hashPath": "microsoft.aspnetcore.mvc.razor.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Razor.Extensions/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-2IBPT1QfTgJ8whML78zmqL7tDC5wzA732gQ/z55GXQZjOWQ6VI/n9fxF+hrzTayFSHBUuGRwKj5/gg0N/NcLww==", "path": "microsoft.aspnetcore.mvc.razor.extensions/2.1.2", "hashPath": "microsoft.aspnetcore.mvc.razor.extensions.2.1.2.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Razor.ViewCompilation/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-kLys2AJY1GK4aOMJokvLX8U/V7/KF7bhfVwRwZHh4yxY6cgOJaNxWlJvdFFTpfGb0hcoSP4fRjfUFlFBp8L+gQ==", "path": "microsoft.aspnetcore.mvc.razor.viewcompilation/2.1.1", "hashPath": "microsoft.aspnetcore.mvc.razor.viewcompilation.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.RazorPages/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-9XAjyPY2n1V9jRDm0XqvsgURtOI99N+Wvhu1C719lOM0dmst6tMYmed2MJCdZ7EzzLxGoK112It33/zZheXWig==", "path": "microsoft.aspnetcore.mvc.razorpages/2.1.3", "hashPath": "microsoft.aspnetcore.mvc.razorpages.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.TagHelpers/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-YQ3L/NDtmYXO36hcsvFmG0bBDpO1U2mPtQb9h8ueOKVHrwYJQX1oBKTKyNoW1CxGkunmXoi2IeVwXeTl7HrVkA==", "path": "microsoft.aspnetcore.mvc.taghelpers/2.1.3", "hashPath": "microsoft.aspnetcore.mvc.taghelpers.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.ViewFeatures/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-UwFP+BjvqzF5V9NVga0kLb4oS5LceNkYPFtMvd9imezn0+/vHKSxiIp0cWvHuksNGMld/9JjSH2KQMt0i3zkzA==", "path": "microsoft.aspnetcore.mvc.viewfeatures/2.1.3", "hashPath": "microsoft.aspnetcore.mvc.viewfeatures.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.NodeServices/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-zULLPbtIXzz8KFmDVr3lDwn6WhqtGP2MBbc602ViI9ymXFlPRBL7jrvfUg6+PhBxDnpHmOaZNJLIl+8rJha46w==", "path": "microsoft.aspnetcore.nodeservices/2.1.1", "hashPath": "microsoft.aspnetcore.nodeservices.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Owin/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-OKovgdeKNc2XE31363rCa5ON30FFlcjC4zfsXRokpHZdVUX1A0cllNlXyNggJf1K+5DepBr/fv6BuuX6x/ZZYQ==", "path": "microsoft.aspnetcore.owin/2.1.1", "hashPath": "microsoft.aspnetcore.owin.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Razor/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-0P/1wSjvZPURzUcvdMjc+gHTIZmFwpkspVJVD7OKTLTcAj8TlTYcP1Py/Av4UU/SkUyhtlMrY/Mo4RCjUn3kZg==", "path": "microsoft.aspnetcore.razor/2.1.2", "hashPath": "microsoft.aspnetcore.razor.2.1.2.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Design/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-fe1+dpy+eM4IX1zodsvkihO1Spuj0NecfStWzvkYoMo7/ziOsqSv4vFK36ZpBfHa4gpdCEwaU46EPzdq+ZDYhQ==", "path": "microsoft.aspnetcore.razor.design/2.1.2", "hashPath": "microsoft.aspnetcore.razor.design.2.1.2.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Language/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-sTajqea7Ef0bIB5z+/ByhmznbsqhA+Q4lQw6zSAZC873o6b5yQTu9/5ejKO2B8Kl2S3mdYOJIGY6Eq/IRd788w==", "path": "microsoft.aspnetcore.razor.language/2.1.2", "hashPath": "microsoft.aspnetcore.razor.language.2.1.2.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Runtime/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-3TpndT7RJsBR7cf14eocdkMUo6NMXOnhyFbWeJWHjL+MSQnRAE17V9BmI9fhQblsPBpLRb3XNN+WXfatVujMsA==", "path": "microsoft.aspnetcore.razor.runtime/2.1.2", "hashPath": "microsoft.aspnetcore.razor.runtime.2.1.2.nupkg.sha512"}, "Microsoft.AspNetCore.ResponseCaching/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-c<PERSON>ottukasno+Z711nAMe7Pp0961/PhxquLhzWv5Jlbt/EE6RjYTnggBg3weE7N0oWXPe8SkgQURqUKuqZcrrQQ==", "path": "microsoft.aspnetcore.responsecaching/2.1.1", "hashPath": "microsoft.aspnetcore.responsecaching.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-sTJvhc408h4J8ml66gfhuN/r2WfrasvgERq2ZLIDz3YZYqSXmkpwDjbxSlhzuHQFKMlyx1Tg1uWoF+6eRrKjDA==", "path": "microsoft.aspnetcore.responsecaching.abstractions/2.1.1", "hashPath": "microsoft.aspnetcore.responsecaching.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.ResponseCompression/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-IsPhTWXqouyu+vionm5ih2ZJnSh/XmOrm8X77Ty/APnzy8mwgWy6VxxjtQQTgb4zCaTWs1aVJvM+fLtWGuoksg==", "path": "microsoft.aspnetcore.responsecompression/2.1.1", "hashPath": "microsoft.aspnetcore.responsecompression.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Rewrite/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-8hFPHYCoy5yeWoOyWKFWy4XH7OxbVIOj48zkH1+pAhLuIDhTKm7A4gMS/ocdomFCy0F5+AOUhksaANwjCWjndg==", "path": "microsoft.aspnetcore.rewrite/2.1.1", "hashPath": "microsoft.aspnetcore.rewrite.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Routing/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-U39z3M0oTrquVBohK32Nh20PWQkb9fuO1dbVPTI43Dr3n6qCx6vAFNGWuCzFeINLy152LivmVlLn4rMOzWudug==", "path": "microsoft.aspnetcore.routing/2.1.1", "hashPath": "microsoft.aspnetcore.routing.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Routing.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Aa88Bi0/HI8dPReC0XqByPiVGYDRfj6Xh2eVsNCisnlgFHonDdW9CQsNPhVSK+uWQl3kDMFxFpeJ1ktz/wUHsQ==", "path": "microsoft.aspnetcore.routing.abstractions/2.1.1", "hashPath": "microsoft.aspnetcore.routing.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Server.HttpSys/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-/5UtIMm6I3Y5gVe5nERpbPEmENbsXNekQTx86Juy8zSqj1k6RczkheIsI0/efTF8lku6A+d2MdJD2mz4SqlHAA==", "path": "microsoft.aspnetcore.server.httpsys/2.1.1", "hashPath": "microsoft.aspnetcore.server.httpsys.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Server.IISIntegration/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-uBxvetiZnJODcd54aY4H/Qn9/1ZWDvTX1Q6COG1TGgDl3W78IX61mc5aUj5FMzsiE7BV1yG7/8fv9T/4I6H17A==", "path": "microsoft.aspnetcore.server.iisintegration/2.1.2", "hashPath": "microsoft.aspnetcore.server.iisintegration.2.1.2.nupkg.sha512"}, "Microsoft.AspNetCore.Server.Kestrel/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-+JDnYueC3ptdD98D8QoIddBWwzeWAFQiS0j1f3R0A8O2VulFnm0F8HGPHEOYonuqYI4mcJ1UErf/PQHwduDy+A==", "path": "microsoft.aspnetcore.server.kestrel/2.1.3", "hashPath": "microsoft.aspnetcore.server.kestrel.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Server.Kestrel.Core/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-Oz8zNBhSHINGtOVqujnBMKDq9oVv54riu6zIKXcIcJxBfRAfbpAm2Uq9HhHYY0bqHn1XS+lvDJpZbV1++sbCSw==", "path": "microsoft.aspnetcore.server.kestrel.core/2.1.3", "hashPath": "microsoft.aspnetcore.server.kestrel.core.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Server.Kestrel.Https/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-rR2P+W1zxTZk+W0U6cfnGs5q3ksCXbuvvEtU9RHoLJ2rLz2IDFv9JJf3PGoD0ONu0JzOCbwZzLcsBMxdes/mVA==", "path": "microsoft.aspnetcore.server.kestrel.https/2.1.3", "hashPath": "microsoft.aspnetcore.server.kestrel.https.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-eWuUt5eSDdpBwNRZdFf7FiKcpNBZ/wETKSgf5weAfTxpGKwmfftrViiMJQgCwxlntgLzL1MYCZf51A8O2Xq9Ng==", "path": "microsoft.aspnetcore.server.kestrel.transport.abstractions/2.1.3", "hashPath": "microsoft.aspnetcore.server.kestrel.transport.abstractions.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Libuv/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-+thBX8y671RlDLMZ3ASeea2wrfc/+hHBjdU56EjHBVbmfC4on2Fy6/GNSfXEq+eog3kBD8VDXVBmVXwB93yZCg==", "path": "microsoft.aspnetcore.server.kestrel.transport.libuv/2.1.3", "hashPath": "microsoft.aspnetcore.server.kestrel.transport.libuv.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-qaEW5rAhAoSKNqO6nRWFMHp5CpgSxnz+jk6e4g+AOQhQZroJrS/DtnhY2WxvbyzJ6V7kKv7Mis7vgylTtmcrFg==", "path": "microsoft.aspnetcore.server.kestrel.transport.sockets/2.1.3", "hashPath": "microsoft.aspnetcore.server.kestrel.transport.sockets.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Session/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-hQ4PHIHw1RmqoqjZKRfT4eL6msUd7K+GwcLUGtd1WZT7mOzqmt2oXkzL0Q+qudgXsNdWmH+zpe0zzqKM8Hz45w==", "path": "microsoft.aspnetcore.session/2.1.1", "hashPath": "microsoft.aspnetcore.session.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR/1.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-eluSYbLdU7Tb0MmKOJqyFWRVjO23STLv93NGfKkpgVsF5veX+IwTk8A9V6CHGBY2bYEJ9Cld3YAKh/i9txzz+Q==", "path": "microsoft.aspnetcore.signalr/1.0.4", "hashPath": "microsoft.aspnetcore.signalr.1.0.4.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Common/1.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-6ZcC3+S3gNn24WcOzd+n2CreYXEsmRqdHylC3qw/ApwBSysWBkWohz4OYvuPsJNyMRKyw0XaAdrTj5aqBo7Zqw==", "path": "microsoft.aspnetcore.signalr.common/1.0.4", "hashPath": "microsoft.aspnetcore.signalr.common.1.0.4.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Core/1.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-cOW+gyYIe8fU9gbDo+FK9pqiSb6AbXjvkzsTvQivGqpdbX8vrwVgEpPdcn6rzNrvD9cJQUHRxGhhpyJq2CJ8JA==", "path": "microsoft.aspnetcore.signalr.core/1.0.4", "hashPath": "microsoft.aspnetcore.signalr.core.1.0.4.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Protocols.Json/1.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-DvursqtEJZhnOUYk8efNjudSs6oQJdHYWQOtZbaQB8CdJBvcMaf4+mZ5q9y+2nIGLCxMZ5id+yV9eRaVzAFdDA==", "path": "microsoft.aspnetcore.signalr.protocols.json/1.0.4", "hashPath": "microsoft.aspnetcore.signalr.protocols.json.1.0.4.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Redis/1.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-LOdElYW9MCwlnMpCIdhmfzYZwxw8yt/PMm1ln5iULCZpJqeki4PFfanukucURFfZMt8yL3hOGaWHmveryKQrDg==", "path": "microsoft.aspnetcore.signalr.redis/1.0.4", "hashPath": "microsoft.aspnetcore.signalr.redis.1.0.4.nupkg.sha512"}, "Microsoft.AspNetCore.SpaServices/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-pPQr67lzfZzLEk4UXw4Y3zQZrrh3drsnB223q5citrB9y0QualC7Oqpmq3Vq48nsaTBnwYPM5IoEOlWL5gYmPg==", "path": "microsoft.aspnetcore.spaservices/2.1.1", "hashPath": "microsoft.aspnetcore.spaservices.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.SpaServices.Extensions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-zlrjDE0kKN20bZ3ObwtyE5Oj14/OjSn+zyIC2hhYatVP5c6lVnpFqR0Th0ISSl2W1DueinlScmDxbk8Ccr7iCQ==", "path": "microsoft.aspnetcore.spaservices.extensions/2.1.1", "hashPath": "microsoft.aspnetcore.spaservices.extensions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.StaticFiles/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-THLu6XGauf9kdAI0OyjoqvY/11Ap/Ra/ZNHfWQjrsS4b0AhvzUZgyuq5xYrmdA4+3goRxkqbH2xvrIISGGsukA==", "path": "microsoft.aspnetcore.staticfiles/2.1.1", "hashPath": "microsoft.aspnetcore.staticfiles.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.WebSockets/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-wvp85LiIDuFAtbn5FiD4dpAXUBI203yBEtKeNE1I1ipSrUugY2lJVpZAP+C5F5AJ1RZtWvBl+AP1mhkuDNWpag==", "path": "microsoft.aspnetcore.websockets/2.1.1", "hashPath": "microsoft.aspnetcore.websockets.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-PGKIZt4+412Z/XPoSjvYu/QIbTxcAQuEFNoA1Pw8a9mgmO0ZhNBmfaNyhgXFf7Rq62kP0tT/2WXpxdcQhkFUPA==", "path": "microsoft.aspnetcore.webutilities/2.1.1", "hashPath": "microsoft.aspnetcore.webutilities.2.1.1.nupkg.sha512"}, "Microsoft.Azure.KeyVault/2.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-A82ESUdfLz2wMhYuPxrwf/fA7JVt3IARgeMCG3TsaLtxUxa9RBKX3f0zdnKmvBvJ/u1/5g03OLR26GPekqY5HQ==", "path": "microsoft.azure.keyvault/2.3.2", "hashPath": "microsoft.azure.keyvault.2.3.2.nupkg.sha512"}, "Microsoft.Azure.KeyVault.WebKey/2.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-MVSYao62R9rwl9KF+IsJm+XBLupJj1ma2lfwNeFlSWziXGAopnYK+YkDWqABOqNIV9kpza/MvNBxITzhlJIyIw==", "path": "microsoft.azure.keyvault.webkey/2.0.7", "hashPath": "microsoft.azure.keyvault.webkey.2.0.7.nupkg.sha512"}, "Microsoft.Azure.Services.AppAuthentication/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-oSVQ+YLgn0PfDLBRwOtHrkw06Pc6HvqIVrtFFWMg/1eafY+Hbrm1CSXnzXKXJwqUUv0ynvuwqzjN+IFR3g588w==", "path": "microsoft.azure.services.appauthentication/1.0.1", "hashPath": "microsoft.azure.services.appauthentication.1.0.1.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-HS3iRWZKcUw/8eZ/08GXKY2Bn7xNzQPzf8gRPHGSowX7u7XXu9i9YEaBeBNKUXWfI7qjvT2zXtLUvbN0hds8vg==", "path": "microsoft.codeanalysis.analyzers/1.1.0", "hashPath": "microsoft.codeanalysis.analyzers.1.1.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/2.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-06AzG7oOLKTCN1EnoVYL1bQz+Zwa10LMpUn7Kc+PdpN8CQXRqXTyhfxuKIz6t0qWfoatBNXdHD0OLcEYp5pOvQ==", "path": "microsoft.codeanalysis.common/2.8.0", "hashPath": "microsoft.codeanalysis.common.2.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/2.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-RizcFXuHgGmeuZhxxE1qQdhFA9lGOHlk0MJlCUt6LOnYsevo72gNikPcbANFHY02YK8L/buNrihchY0TroGvXQ==", "path": "microsoft.codeanalysis.csharp/2.8.0", "hashPath": "microsoft.codeanalysis.csharp.2.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Razor/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-Lx/LGkG0Il/sFFHptpAO4Ry4MXyl6XwwHUyGAcJlaXymFThkfx+p2PuvZtFE9ftf5w9RFWOETJzhGOT06PJnlQ==", "path": "microsoft.codeanalysis.razor/2.1.2", "hashPath": "microsoft.codeanalysis.razor.2.1.2.nupkg.sha512"}, "Microsoft.CSharp/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-kaj6Wb4qoMuH3HySFJhxwQfe8R/sJsNJnANrvv8WdFPMoNbKY5htfNscv+LHCu5ipz+49m2e+WQXpLXr9XYemQ==", "path": "microsoft.csharp/4.5.0", "hashPath": "microsoft.csharp.4.5.0.nupkg.sha512"}, "Microsoft.Data.Edm/5.8.2": {"type": "package", "serviceable": true, "sha512": "sha512-P/d8DxA6MFHroBEn/jW0LMQSIKnsRRibrZtRCLfov2boQfrQ1R1BVgkJ5oIhcQsOm0l4POv+I2ny6RBsclNbOw==", "path": "microsoft.data.edm/5.8.2", "hashPath": "microsoft.data.edm.5.8.2.nupkg.sha512"}, "Microsoft.Data.OData/5.8.2": {"type": "package", "serviceable": true, "sha512": "sha512-oEIUtXcRiKogF0yZxA+QdgxoBJ34989qL/5xOSrTfxAhzNJV5Hw6DRdWgUCpeXFMoJUQx7ptbHCN+My/LCQfsg==", "path": "microsoft.data.odata/5.8.2", "hashPath": "microsoft.data.odata.5.8.2.nupkg.sha512"}, "Microsoft.Data.Sqlite/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-eXVnRIBYQd1ZPlCvqJukQqwcPd6eUDJInZ4fT1D7IJslrIcevW4nWaI+k6Yt/oww4woK0Dqb9G3rey2uX/aXmQ==", "path": "microsoft.data.sqlite/2.1.0", "hashPath": "microsoft.data.sqlite.2.1.0.nupkg.sha512"}, "Microsoft.Data.Sqlite.Core/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-VJ0kOuf3wp9xyarhBla0JJlwdxz28g09rI4NP8ly8I8/ZhrOZZLtlYvjiMZAPNm0+45+dZ+VXUiO7n4IrP52Zw==", "path": "microsoft.data.sqlite.core/2.1.0", "hashPath": "microsoft.data.sqlite.core.2.1.0.nupkg.sha512"}, "Microsoft.DotNet.PlatformAbstractions/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-9KPDwvb/hLEVXYruVHVZ8BkebC8j17DmPb56LnqRF74HqSPLjCkrlFUjOtFpQPA2DeADBRTI/e69aCfRBfrhxw==", "path": "microsoft.dotnet.platformabstractions/2.1.0", "hashPath": "microsoft.dotnet.platformabstractions.2.1.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/2.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-Rt2SIF3LPQfBzQYbhOv8LwQKy7UkZh+zJcls5eLTOlbPahLXH7IWCkk3mQpzTvljG55vkhzNcOjzrVLcO0uICA==", "path": "microsoft.entityframeworkcore/2.1.4", "hashPath": "microsoft.entityframeworkcore.2.1.4.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/2.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-3djJy9gM2PZQFz6NHPSVzAN8azBu7afehDLwGkCR8yCMVhNzkkVdT/JZMfsSzuNZ302wIFWbzEYSAEI6lBddMA==", "path": "microsoft.entityframeworkcore.abstractions/2.1.4", "hashPath": "microsoft.entityframeworkcore.abstractions.2.1.4.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/2.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-VEyuULTq48tbPjwLXo9j5WdObg0ViHYI4CwvdaYG23r13wIP2MJqpn4Nc9HpUQPPxGDqL2FO9HRvSY3Xufgtcw==", "path": "microsoft.entityframeworkcore.analyzers/2.1.4", "hashPath": "microsoft.entityframeworkcore.analyzers.2.1.4.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/2.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-UWn5ufxQZiFl18MSzD7/JM4kNUtbMtJQuNncZ9k4Is7J/oogx6ApbG/nsznoEcM2PfeZc6CZG9u14TJZbXRoig==", "path": "microsoft.entityframeworkcore.design/2.1.4", "hashPath": "microsoft.entityframeworkcore.design.2.1.4.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.InMemory/2.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-m6/Ssk5nDD0tWJTuDxojj53UER2FbB/fPeUDcrY8NFXwiR/g0csLhxA7RK4PrVz1Ze2S8GurYy6SbIreS3Crgw==", "path": "microsoft.entityframeworkcore.inmemory/2.1.4", "hashPath": "microsoft.entityframeworkcore.inmemory.2.1.4.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/2.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-Ekl7Kjo5YrPahQ1FJJVSd1ZTnmbtCPdkbI8CMZfbGBQMtrYgBttHxE4bTA4+Ys69+BTRMc/AdWoCfRyioxZA+w==", "path": "microsoft.entityframeworkcore.relational/2.1.4", "hashPath": "microsoft.entityframeworkcore.relational.2.1.4.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite/2.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-d/3Eih3qphowTtkASx5HgzGOrDsYiKdXYPQnyLeJtpe9hJgOi2HrF11oQiPu6gaw9C0G+ALXicFNDB27R442Mg==", "path": "microsoft.entityframeworkcore.sqlite/2.1.4", "hashPath": "microsoft.entityframeworkcore.sqlite.2.1.4.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite.Core/2.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-lmQhiLh+oXtcyIRXOu8F7VFdv56eYlGem36d7bHFiosK2jA6RBpynPe3hdVMcNWXoABUXIhS/WfN8+/I8ynmKg==", "path": "microsoft.entityframeworkcore.sqlite.core/2.1.4", "hashPath": "microsoft.entityframeworkcore.sqlite.core.2.1.4.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.SqlServer/2.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-f5Zl534MVOrVhe5kAYE9eFN+4AYW6IUw7+i5NeNQ+8eJQpAjCW5HlzGBkFVerHwzkjcM/Z3tlqMTzrdpO+f0HA==", "path": "microsoft.entityframeworkcore.sqlserver/2.1.4", "hashPath": "microsoft.entityframeworkcore.sqlserver.2.1.4.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/2.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-0lXy6vP64gkrWHEUh+1R5CxZiVPRCnLhJ2+7iMQzX/f2e91qGh8hQju2T7rkvVM7ZYgEN1F3AUmXS1LdnoBJiQ==", "path": "microsoft.entityframeworkcore.tools/2.1.4", "hashPath": "microsoft.entityframeworkcore.tools.2.1.4.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-3d5xYNZhqosir31mohbApOE1Iuci/WOfWit+/Cj6ZxS5SR28SbgskgP8M+ZmBZ5Gy7l1DT7fYikgqpC2tQE/nA==", "path": "microsoft.extensions.caching.abstractions/2.1.2", "hashPath": "microsoft.extensions.caching.abstractions.2.1.2.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-97bIvx+m0XZYdXHWZgOa+KDzzaa8y/eq8fBqBogFGzKdN1+g4P1izA/Ar724G5Oc5t0kvLq2iZR64Tz1UL+TLg==", "path": "microsoft.extensions.caching.memory/2.1.2", "hashPath": "microsoft.extensions.caching.memory.2.1.2.nupkg.sha512"}, "Microsoft.Extensions.Caching.Redis/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-G+RM+8AqAcxSe97icXUtrUAM04TyNh/YMp+RP/rQqyp+f859LY6kiED7hC65OKJeXImEbagZLdT1BgbOYqjcNw==", "path": "microsoft.extensions.caching.redis/2.1.2", "hashPath": "microsoft.extensions.caching.redis.2.1.2.nupkg.sha512"}, "Microsoft.Extensions.Caching.SqlServer/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-cJC/+bTzMYz9dByw13XIv95wbxhEA0sM6qiT6L/d1pPNGiLW7/oMb/qDwGaK3/kCszWOYpptwwZ/gJK7rR42eg==", "path": "microsoft.extensions.caching.sqlserver/2.1.2", "hashPath": "microsoft.extensions.caching.sqlserver.2.1.2.nupkg.sha512"}, "Microsoft.Extensions.Configuration/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-LjVKO6P2y52c5ZhTLX/w8zc5H4Y3J/LJsgqTBj49TtFq/hAtVNue/WA0F6/7GMY90xhD7K0MDZ4qpOeWXbLvzg==", "path": "microsoft.extensions.configuration/2.1.1", "hashPath": "microsoft.extensions.configuration.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-VfuZJNa0WUshZ/+8BFZAhwFKiKuu/qOUCFntfdLpHj7vcRnsGHqd3G2Hse78DM+pgozczGM63lGPRLmy+uhUOA==", "path": "microsoft.extensions.configuration.abstractions/2.1.1", "hashPath": "microsoft.extensions.configuration.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.AzureKeyVault/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-2de8Qpq4Iq9nfOdftuN93ZZ1m5ahlIFkGeHJot+IBbtEmxFd7sRH5l9in3XHXUovOluxzk72nNvmKqC3zOV8qw==", "path": "microsoft.extensions.configuration.azurekeyvault/2.1.1", "hashPath": "microsoft.extensions.configuration.azurekeyvault.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-fcLCTS03poWE4v9tSNBr3pWn0QwGgAn1vzqHXlXgvqZeOc7LvQNzaWcKRQZTdEc3+YhQKwMsOtm3VKSA2aWQ8w==", "path": "microsoft.extensions.configuration.binder/2.1.1", "hashPath": "microsoft.extensions.configuration.binder.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-ZF<PERSON><PERSON>cp9gZdOoINRGg6sUYqEUU6X4HRShPPLbY9tY/r+PTWyVBwucYzuueHLE7k5yxJTNBnIHpxtJ8PMvxjjBQ==", "path": "microsoft.extensions.configuration.commandline/2.1.1", "hashPath": "microsoft.extensions.configuration.commandline.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-6xMxFIfKL+7J/jwlk8zV8I61sF3+DRG19iKQxnSfYQU+iMMjGbcWNCHFF/3MHf3o4sTZPZ8D6Io+GwKFc3TIZA==", "path": "microsoft.extensions.configuration.environmentvariables/2.1.1", "hashPath": "microsoft.extensions.configuration.environmentvariables.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-CDk5CwG0YzlRgvl65J0iK6ahrX12yMRrEat3yVTXjWC+GN9Jg9zHZu2IE4cQIPAMA/IiAI5KjgL08fhP3fPCkw==", "path": "microsoft.extensions.configuration.fileextensions/2.1.1", "hashPath": "microsoft.extensions.configuration.fileextensions.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Ini/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-+/7imv6queNr3UrU7ynXR9ZZ0rz/HW+HcpUnAjwxIxn8KcoBVv44/UlHYzt3AipVJYbswFiB1FjsQ0IQhffBiA==", "path": "microsoft.extensions.configuration.ini/2.1.1", "hashPath": "microsoft.extensions.configuration.ini.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-IFpONpvdhVEE3S3F4fTYkpT/GyIHtumy2m0HniQanJ80Pj/pUF3Z4wjrHEp1G78rPD+WTo5fRlhdJfuU1Tv2GQ==", "path": "microsoft.extensions.configuration.json/2.1.1", "hashPath": "microsoft.extensions.configuration.json.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.KeyPerFile/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-GzFVvC9RK2e3GM7wrVZqS76XtX8ANzoKtFrFeFr9Qq2T3yPmWtr7E4LO+tXPSidNQsEiA+x3bxNHyuyJA44uRw==", "path": "microsoft.extensions.configuration.keyperfile/2.1.1", "hashPath": "microsoft.extensions.configuration.keyperfile.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-/HeMnhc9a6Ou9V+QIdGYHtYuOf0t0RQ//odFUrJ249F6W78pJyVDZY7RnhH4UMF+WLOJpo6hh010DIlW2nqqSA==", "path": "microsoft.extensions.configuration.usersecrets/2.1.1", "hashPath": "microsoft.extensions.configuration.usersecrets.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Xml/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-DDqm0Lqc8+Be2oB6g/xKtz3n/W9DOXOCz0DAgUXTgwsZ2XnNzy6Areop9SmPKd0ezSZWZ/soOAZbhlu5otoKDg==", "path": "microsoft.extensions.configuration.xml/2.1.1", "hashPath": "microsoft.extensions.configuration.xml.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-RVdgNWT/73M0eCpreGpWv5NmbHFGQzzW+G7nChK8ej84m+d1nzeWrtqcRYnEpKNx3B8V/Uek4tNP0WCaCNjYnQ==", "path": "microsoft.extensions.dependencyinjection/2.1.1", "hashPath": "microsoft.extensions.dependencyinjection.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-MgYpU5cwZohUMKKg3sbPhvGG+eAZ/59E9UwPwlrUkyXU+PGzqwZg9yyQNjhxuAWmoNoFReoemeCku50prYSGzA==", "path": "microsoft.extensions.dependencyinjection.abstractions/2.1.1", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-nS2XKqi+1A1umnYNLX2Fbm/XnzCxs5i+zXVJ3VC6r9t2z0NZr9FLnJN4VQpKigdcWH/iFTbMuX6M6WQJcTjVIg==", "path": "microsoft.extensions.dependencymodel/2.1.0", "hashPath": "microsoft.extensions.dependencymodel.2.1.0.nupkg.sha512"}, "Microsoft.Extensions.DiagnosticAdapter/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-pwvvDrlJJTV8NiUgVHrr9WfbACMpy9DkjZtYxxQNedVO5x+Wfxcf5Don2ZybPvygbhl8i8duUTRR5nqpMtCIKQ==", "path": "microsoft.extensions.diagnosticadapter/2.1.0", "hashPath": "microsoft.extensions.diagnosticadapter.2.1.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-UEQB5/QPuLYaCvScZQ9llhcks5xyEUKh41D615FoehRAF9UgGVmXHcCSOH8idHHLRoKm+OJJjEy1oywvuaL33w==", "path": "microsoft.extensions.fileproviders.abstractions/2.1.1", "hashPath": "microsoft.extensions.fileproviders.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Composite/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-fduNXRROUeV1bvFr7xkeRkTU/gVfqu5hmfqxiJiciOjwH3Q+UOADiXAWoPfnQiwpZEmsCC6z+hIIyBOnO4i5Yw==", "path": "microsoft.extensions.fileproviders.composite/2.1.1", "hashPath": "microsoft.extensions.fileproviders.composite.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Embedded/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-TYyZBm9rxNtXvAK81E53VOxWnEbnbDZVzWjwbvgox5oHMUTm3Blm4p6MyK2Rlj2d/tEMK0ofG4ooUEaKYS8Lpg==", "path": "microsoft.extensions.fileproviders.embedded/2.1.1", "hashPath": "microsoft.extensions.fileproviders.embedded.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-kVCvLm1ePchUgRrQZrno07Mn6knDAzR7vl6eRaI/fem0u6ODg+RTwOYLs4XL39Ttuu+BzEwqzHu3DtDgXT8+vQ==", "path": "microsoft.extensions.fileproviders.physical/2.1.1", "hashPath": "microsoft.extensions.fileproviders.physical.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-4QDzyCN8cJnThY6mK9SnzovyCZ8KCG9jmC9KqHfFGtazJvmNZP1gcyBkPmqMjP0qwbmEUUyqyA9LLn3FrYXTGw==", "path": "microsoft.extensions.filesystemglobbing/2.1.1", "hashPath": "microsoft.extensions.filesystemglobbing.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Hosting/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-2LcCTDVNdtJkLlL3w//TaD/gjaVHlH7pW/V22jp0Q8116yJcxX+4WCGvO0RIjRNVFTb+6+gwtMDN6URODxV2hQ==", "path": "microsoft.extensions.hosting/2.1.1", "hashPath": "microsoft.extensions.hosting.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-kVVdHnOFJbcXxgZzrT6nwkrWZTHL+47LT59S9J2Jp0BNO3EQWNEZHUUZMb/kKFV7LtW+bp+EuAOPNUqEcqI++Q==", "path": "microsoft.extensions.hosting.abstractions/2.1.1", "hashPath": "microsoft.extensions.hosting.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Http/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-GOly249seL3HL2+lgfLWHirsggRwK4EmSa6zUb+sPbgXHN+f9w/y/6XV3DPjYjtyt3v38FkPTD6odPcJJKtvlg==", "path": "microsoft.extensions.http/2.1.1", "hashPath": "microsoft.extensions.http.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Identity.Core/2.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-ikSUlGZ9KocgGpDvei7mF3R4NCcXFeB52aGErOY3ss3AVtfV77+PtjTGmuJW3PSlVB3B4fT8FTeMg9ilVlV7qA==", "path": "microsoft.extensions.identity.core/2.1.6", "hashPath": "microsoft.extensions.identity.core.2.1.6.nupkg.sha512"}, "Microsoft.Extensions.Identity.Stores/2.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-+y0RlwL/C0Ujw3Hx/LPqho5x2CeQVWAT4uAnTa+lLumIcROWjR/Mhr7+ggg3fY2t20+XZidxAYZhGGo/nXndPQ==", "path": "microsoft.extensions.identity.stores/2.1.6", "hashPath": "microsoft.extensions.identity.stores.2.1.6.nupkg.sha512"}, "Microsoft.Extensions.Localization/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-6v66lA0RqutBDseLtX6MAZHUcaTBk2xfhnfHpcBeLtlx7jySHg/CNociGLPW7oHJtrJ+POZ8xDEoAyQp5RbWXw==", "path": "microsoft.extensions.localization/2.1.1", "hashPath": "microsoft.extensions.localization.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Localization.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-bsDw+b5BaiFej/Nei6IiJFhsOtiXdDmJCabkU45WC3DQafHOLUWuArpVar8Vv2VxHrXGkOWRA7gX31LASqcaMA==", "path": "microsoft.extensions.localization.abstractions/2.1.1", "hashPath": "microsoft.extensions.localization.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Logging/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-hh+mkOAQDTp6XH80xJt3+wwYVzkbwYQl9XZRCz4Um0JjP/o7N9vHM3rZ6wwwtr+BBe/L6iBO2sz0px6OWBzqZQ==", "path": "microsoft.extensions.logging/2.1.1", "hashPath": "microsoft.extensions.logging.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-XRzK7ZF+O6FzdfWrlFTi1Rgj2080ZDsd46vzOjadHUB0Cz5kOvDG8vI7caa5YFrsHQpcfn0DxtjS4E46N4FZsA==", "path": "microsoft.extensions.logging.abstractions/2.1.1", "hashPath": "microsoft.extensions.logging.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.AzureAppServices/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-xRPB1gJJF2BUax54KASplTP7LWvkfkt3zDMDYna3W+sCpxdkZSRnpasdYCq+yBnCeO5L7Actx7INtDnt8qovpA==", "path": "microsoft.extensions.logging.azureappservices/2.1.1", "hashPath": "microsoft.extensions.logging.azureappservices.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Z3AzFM21fL/ux0kZAbTE+HDPQ46vuh0dqzhlBm6w7/029RxZLvV6bUUsAs70i2r4JfShhCjBYZ+bTjR42diFVA==", "path": "microsoft.extensions.logging.configuration/2.1.1", "hashPath": "microsoft.extensions.logging.configuration.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-6dYephpuOacAiXE6eJcWu0myEub8qglrWSgzsYUdzWXGanAAlTVzpms/Wp5yeLpw4hsP8KFey8ySwt5KvVv/uw==", "path": "microsoft.extensions.logging.console/2.1.1", "hashPath": "microsoft.extensions.logging.console.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-72k7rBz2DL3ev59gX+uwOmA/pEegGzi5SRZhysPIi7+2+JoyLlIRBPscJ8OzOI344Bq27cTByGHDoYWOrq73vg==", "path": "microsoft.extensions.logging.debug/2.1.1", "hashPath": "microsoft.extensions.logging.debug.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-PTcuIm3n549z4jUM4S3PK0LkIXHT08hPjBJ2DYxA/IyzL8b8HFroDUWYh2KkxvDEA3d5szK2MQzcatCO90+caQ==", "path": "microsoft.extensions.logging.eventsource/2.1.1", "hashPath": "microsoft.extensions.logging.eventsource.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.TraceSource/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-a9U6WrHkJk//VQQ6cMfDrHWGxQKVNWXlnoXtA56ItMxyWT5YXU+/KE9aiUvcrbn4kDw/gjlTv95HSXvKGetjKw==", "path": "microsoft.extensions.logging.tracesource/2.1.1", "hashPath": "microsoft.extensions.logging.tracesource.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/2.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-4Xva9hlVx/2zv5bUr9QlPSCQBh5MwVkMoJ8jd9lgHNgx2C2Ol35/0KZrNsmKffcaKjb+Bj6btBq1QjPozhqTPQ==", "path": "microsoft.extensions.objectpool/2.1.6", "hashPath": "microsoft.extensions.objectpool.2.1.6.nupkg.sha512"}, "Microsoft.Extensions.Options/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-V7lXCU78lAbzaulCGFKojcCyG8RTJicEbiBkPJjFqiqXwndEBBIehdXRMWEVU3UtzQ1yDvphiWUL9th6/4gJ7w==", "path": "microsoft.extensions.options/2.1.1", "hashPath": "microsoft.extensions.options.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-NpGh3Y/VOBs6hvjKHMsdbtrvGvMO+cBqZ7YT/Rc4iFy0C4ogSnl1lBAq69L1LS6gzlwDBZDZ7WcvzSDzk5zfzA==", "path": "microsoft.extensions.options.configurationextensions/2.1.1", "hashPath": "microsoft.extensions.options.configurationextensions.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.PlatformAbstractions/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-H6ZsQzxYw/6k2DfEQRXdC+vQ6obd6Uba3uGJrnJ2vG4PRXjQZ7seB13JdCfE72abp8E6Fk3gGgDzfJiLZi5ZpQ==", "path": "microsoft.extensions.platformabstractions/1.1.0", "hashPath": "microsoft.extensions.platformabstractions.1.1.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/2.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-uf0zvl2v/OoDUkz9JnPi1z4B2YbjkgN+pyOKoWD/qwB3ytYoAHFg03rTZ9K8pLdjp/YQlH5B2gY0Li8vrLYNQw==", "path": "microsoft.extensions.primitives/2.1.6", "hashPath": "microsoft.extensions.primitives.2.1.6.nupkg.sha512"}, "Microsoft.Extensions.WebEncoders/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-XIuJXPNUAX/ZV/onarixNoq3kO7Q9/RXXOY8hhYydsDwHI9PqPeJH6WE3LmPJJDmB+7y3+MT6ZmW78gZZDApBA==", "path": "microsoft.extensions.webencoders/2.1.1", "hashPath": "microsoft.extensions.webencoders.2.1.1.nupkg.sha512"}, "Microsoft.IdentityModel.Clients.ActiveDirectory/3.14.2": {"type": "package", "serviceable": true, "sha512": "sha512-TNsJJMiRnkeby1ovThVoV9yFsPWjAdluwOA+Nf0LtSsBVVrKQv8Qp4kYOgyNwMVj+pDwbhXISySk+4HyHVWNZQ==", "path": "microsoft.identitymodel.clients.activedirectory/3.14.2", "hashPath": "microsoft.identitymodel.clients.activedirectory.3.14.2.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/5.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-pakGqbE3FRort3vb0qqWI0Qfy84IOXs8sG7ygANUpoRT+544svQ62JfvCX4UPnqf5bCUpSxVc3rDh8yCQBtc7w==", "path": "microsoft.identitymodel.protocols/5.2.0", "hashPath": "microsoft.identitymodel.protocols.5.2.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/5.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-hMjsfdvgI/Gk/HWPgyVnju6fy3iULralgn1XU6eL17KkkFN2rJ1fDzJX3RKrjr888Y5S+hTSQAUcGzb4Fe3aBA==", "path": "microsoft.identitymodel.protocols.openidconnect/5.2.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.5.2.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.WsFederation/5.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-7yohKgLzTObwy+Yq/WNshe2ar+9MZJischkn+L+IIQhpZCKWixr0QFR0V/1TzvGVeXBR/AJY/luZRLx84RlzJw==", "path": "microsoft.identitymodel.protocols.wsfederation/5.2.0", "hashPath": "microsoft.identitymodel.protocols.wsfederation.5.2.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens.Saml/5.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-db9y9zHTxeVwTi91mqBu4u1h5tlseQxhXMlGBd7bousED/FcEuhRiVK1maXjoHyQTnYbFDGPvYKXxznDI5jBGQ==", "path": "microsoft.identitymodel.tokens.saml/5.2.0", "hashPath": "microsoft.identitymodel.tokens.saml.5.2.0.nupkg.sha512"}, "Microsoft.IdentityModel.Xml/5.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-0WB90AfR16LT0ANCQTb+183yWrusPt4QK1F3f9eL59ZiDKeZLx2AeXgrkDUO+7kG55nCPqmeOUDjHDVK4gsRgA==", "path": "microsoft.identitymodel.xml/5.2.0", "hashPath": "microsoft.identitymodel.xml.5.2.0.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-lP<PERSON>phl8b2EuhOE9dMH6EZDmu7pS882O+HMi5BJNsigxHaWlBrYxZHFZgE18cyaPp6SSZcTkKkuzfjV/RRQKlA==", "path": "microsoft.net.http.headers/2.1.1", "hashPath": "microsoft.net.http.headers.2.1.1.nupkg.sha512"}, "Microsoft.NETCore.App/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-JNHhG+j5eIhG26+H721IDmwswGUznTwwSuJMFe/08h0X2YarHvA15sVAvUkA/2Sp3W0ENNm48t+J7KTPRqEpfA==", "path": "microsoft.netcore.app/2.1.0", "hashPath": "microsoft.netcore.app.2.1.0.nupkg.sha512"}, "Microsoft.NETCore.DotNetAppHost/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-vMn8V3GOp/SPOG2oE8WxswzAWZ/GZmc8EPiB3vc2EZ6us14ehXhsvUFXndYopGNSjCa9OdqC6L6xStF1KyUZnw==", "path": "microsoft.netcore.dotnetapphost/2.1.0", "hashPath": "microsoft.netcore.dotnetapphost.2.1.0.nupkg.sha512"}, "Microsoft.NETCore.DotNetHostPolicy/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-vBUwNihtLUVS2HhO6WocYfAktRmfFihm6JB8/sJ53caVW+AelvbnYpfiGzaZDpkWjN6vA3xzOKPu9Vu8Zz3p8Q==", "path": "microsoft.netcore.dotnethostpolicy/2.1.0", "hashPath": "microsoft.netcore.dotnethostpolicy.2.1.0.nupkg.sha512"}, "Microsoft.NETCore.DotNetHostResolver/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-o0PRql5qOHFEY3d1WvzE+T7cMFKtOsWLMg8L1oTeGNnI4u5AzOj8o6AdZT3y2GxFA1DAx7AQ9qZjpCO2/bgZRw==", "path": "microsoft.netcore.dotnethostresolver/2.1.0", "hashPath": "microsoft.netcore.dotnethostresolver.2.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-x188gIZXOwFXkPXyGavEcPGcR6RGvjFOES2QzskN4gERZjWPN34qhRsZVMC0CLJfQLGSButarcgWxPPM4vmg0w==", "path": "microsoft.netcore.targets/2.1.0", "hashPath": "microsoft.netcore.targets.2.1.0.nupkg.sha512"}, "Microsoft.Rest.ClientRuntime/2.3.8": {"type": "package", "serviceable": true, "sha512": "sha512-Hj96LBoCwKY2VQKfSCVGGPV1sSumVjuYnrlpBwL4JSTnSK4b6ZxjLtXj8LgmKav8xJ2gps+UN7eI3hHVFKvBFw==", "path": "microsoft.rest.clientruntime/2.3.8", "hashPath": "microsoft.rest.clientruntime.2.3.8.nupkg.sha512"}, "Microsoft.Rest.ClientRuntime.Azure/3.3.7": {"type": "package", "serviceable": true, "sha512": "sha512-6u8JIuvrztse4tPOcvNzAJuzGBP0uY+Ijggk8ZYhp0siGEZ1XfZylf1vpNGUicvwcrhhoIgDW73Z1L6QGssr2g==", "path": "microsoft.rest.clientruntime.azure/3.3.7", "hashPath": "microsoft.rest.clientruntime.azure.3.3.7.nupkg.sha512"}, "Microsoft.VisualStudio.Web.BrowserLink/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Fq4cFAvVNU5TYdsur/3DRRTdiS5ApKShYaBlxQabR/nUWCymqGXL4Fi1jl1YWq82e4I0c6eAao4YGZiruevrCA==", "path": "microsoft.visualstudio.web.browserlink/2.1.1", "hashPath": "microsoft.visualstudio.web.browserlink.2.1.1.nupkg.sha512"}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "path": "microsoft.win32.primitives/4.3.0", "hashPath": "microsoft.win32.primitives.4.3.0.nupkg.sha512"}, "NETStandard.Library/2.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-st47PosZSHrjECdjeIzZQbzivYBJFv6P2nv4cj2ypdI204DO+vZ7l5raGMiX4eXMJ53RfOIg+/s4DHVZ54Nu2A==", "path": "netstandard.library/2.0.3", "hashPath": "netstandard.library.2.0.3.nupkg.sha512"}, "Remotion.Linq/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-fK/76UmpC0FXBlGDFVPLJHQlDLYnGC+XY3eoDgCgbtrhi0vzbXDQ3n/IYHhqSKqXQfGw/u04A1drWs7rFVkRjw==", "path": "remotion.linq/2.2.0", "hashPath": "remotion.linq.2.2.0.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-HdSSp5MnJSsg08KMfZThpuLPJpPwE5hBXvHwoKWosyHHfe8Mh5WKT0ylEOf6yNzX6Ngjxe4Whkafh5q7Ymac4Q==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+yH1a49wJMy8Zt4yx5RhJrxO/DBDByAiCzNwiETI+1S4mPdCu0OY4djdciC7Vssk0l22wQaDLrXxXkp+3+7bVA==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c3YNH1GQJbfIPJeCnr4avseugSqPrxwIqzthYyZDN6EuOyNOzq+y2KSUfRcXauya1sF4foESTgwM5e1A8arAKw==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "path": "runtime.native.system.io.compression/4.3.0", "hashPath": "runtime.native.system.io.compression.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Security/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-M2nN92ePS8BgQ2oi6Jj3PlTUzadYSIWLdZrHY1n1ZcW9o4wAQQ6W+aQ2lfq1ysZQfVCgDwY58alUdowrzezztg==", "path": "runtime.native.system.net.security/4.3.0", "hashPath": "runtime.native.system.net.security.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-NS1U+700m4KFRHR5o4vo9DSlTmlCKu/u7dtE5sUHVIPB+xpXxYQvgBgA6wEIeCz6Yfn0Z52/72WYsToCEPJnrw==", "path": "runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-b3pthNgxxFcD+Pc0WSEoC0+md3MyhRS6aCEeenvNE3Fdw1HyJ18ZhRFVJJzIeR/O/jpxPboB805Ho0T3Ul7w8A==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KeLz4HClKf+nFS7p/6Fi/CqyLXh81FpiGzcmuS8DGi9lUqSnZ6Es23/gv2O+1XVGfrbNmviF7CckBpavkBoIFQ==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X7IdhILzr4ROXd8mI1BUCQMSHSQwelUlBjF1JyTKCjXaOGn2fB4EKBxQbCK2VjO3WaWIdlXZL3W6TiIVnrhX4g==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-nyFNiCk/r+VOiIqreLix8yN+q3Wga9+SE8BCgkf+2BwEKiNx6DyvFjCgkfV743/grxv8jHJ8gUK4XEQw7yzRYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ytoewC6wGorL7KoCAvRfsgoJPJbNq+64k2SqW6JcOAebWsFUvCCYgfzQMrnpvPiEl4OrblUlhF2ji+Q1+SVLrQ==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I8bKw2I8k58Wx7fMKQJn2R8lamboCAiHfHeV/pS65ScKWMMI0+wJkLYlEKvgW1D/XvSl/221clBoR2q9QNNM7A==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VB5cn/7OzUfzdnC8tqAIMQciVLiq2epm2NrAm1E9OjNRyG4lVhfR61SMcLizejzQP8R8Uf/0l5qOIbUEi+RdEg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "SQLitePCLRaw.bundle_green/1.1.11": {"type": "package", "serviceable": true, "sha512": "sha512-Hqp8IF/RLjbypkLm+t+1RqAX8EKuWEkj7wkLXplfENQoPnE127cvTmaDvaQKtt7a5QsD/HNjFDr1z5f9q2wdPg==", "path": "sqlitepclraw.bundle_green/1.1.11", "hashPath": "sqlitepclraw.bundle_green.1.1.11.nupkg.sha512"}, "SQLitePCLRaw.core/1.1.11": {"type": "package", "serviceable": true, "sha512": "sha512-QT17CyQAQIbTCu6pW5+uVA8BXqRzquiDI1tr5Un6c1YZcQydPmGPuYYng9xMs2UHUDYHbTu+JDkp55WR+qo9bQ==", "path": "sqlitepclraw.core/1.1.11", "hashPath": "sqlitepclraw.core.1.1.11.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3.linux/1.1.11": {"type": "package", "serviceable": true, "sha512": "sha512-yt79OujiYBueEUdhTQI+fIdmRpFEGcaMLziSCfhG2dsAERDvCQ+FB5tQgZBdFkB2kmVl0llskzBtwSbg3WEIdQ==", "path": "sqlitepclraw.lib.e_sqlite3.linux/1.1.11", "hashPath": "sqlitepclraw.lib.e_sqlite3.linux.1.1.11.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3.osx/1.1.11": {"type": "package", "serviceable": true, "sha512": "sha512-o6rxxAFS5Hya6AXCA/eU+1QqwpGVHJBaJjIG+Mui3kIqcV6d+qz0zi46i1QNK6TAel8Sj4QlXO85wNPN70BMEg==", "path": "sqlitepclraw.lib.e_sqlite3.osx/1.1.11", "hashPath": "sqlitepclraw.lib.e_sqlite3.osx.1.1.11.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3.v110_xp/1.1.11": {"type": "package", "serviceable": true, "sha512": "sha512-h1Jng85sdFV/IC+Lri+buN1r5Kd0kgh0lh/pc4jlHd3tljZhc2Si2GW1VD0hJc4p/P4oSVa2426hBAi1y6/goA==", "path": "sqlitepclraw.lib.e_sqlite3.v110_xp/1.1.11", "hashPath": "sqlitepclraw.lib.e_sqlite3.v110_xp.1.1.11.nupkg.sha512"}, "SQLitePCLRaw.provider.e_sqlite3.netstandard11/1.1.11": {"type": "package", "serviceable": true, "sha512": "sha512-6q2qXc7GA11ZFh3ATFj9LjzF0HfMtlN1hDi8QATSoML4sR+inquXdionvyX8nUegutOAGXjKDp+8MziaUV5Pkg==", "path": "sqlitepclraw.provider.e_sqlite3.netstandard11/1.1.11", "hashPath": "sqlitepclraw.provider.e_sqlite3.netstandard11.1.1.11.nupkg.sha512"}, "StackExchange.Redis.StrongName/1.2.6": {"type": "package", "serviceable": true, "sha512": "sha512-UFmT1/JYu1PLiRwkyvEPVHk/tVTJa8Ka2rb9yzidzDoQARvhBVRpaWUeaP81373v54jupDBvAoGHGl0EY/HphQ==", "path": "stackexchange.redis.strongname/1.2.6", "hashPath": "stackexchange.redis.strongname.1.2.6.nupkg.sha512"}, "System.AppContext/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fKC+rmaLfeIzUhagxY17Q9siv/sPrjjKcfNg1Ic8IlQkZLipo8ljcaZQu4VtI4Jqbzjc2VTjzGLF6WmsRXAEgA==", "path": "system.appcontext/4.3.0", "hashPath": "system.appcontext.4.3.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/1.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-EXKiDFsChZW0RjrZ4FYHu9aW6+P4MCgEDCklsVseRfhoO0F+dXeMSsMRAlVXIo06kGJ/zv+2w1a2uc2+kxxSaQ==", "path": "system.collections.immutable/1.5.0", "hashPath": "system.collections.immutable.1.5.0.nupkg.sha512"}, "System.Collections.NonGeneric/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-prtjIEMhGUnQq6RnPEYLpFt8AtLbp9yq2zxOSrY7KJJZrw25Fi97IzBqY7iqssbM61Ek5b8f3MG/sG1N2sN5KA==", "path": "system.collections.nongeneric/4.3.0", "hashPath": "system.collections.nongeneric.4.3.0.nupkg.sha512"}, "System.Collections.Specialized/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Epx8PoVZR0iuOnJJDzp7pWvdfMMOAvpUo95pC4ScH2mJuXkKA2Y4aR3cG9qt2klHgSons1WFh4kcGW7cSXvrxg==", "path": "system.collections.specialized/4.3.0", "hashPath": "system.collections.specialized.4.3.0.nupkg.sha512"}, "System.ComponentModel.Annotations/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-UxYQ3FGUOtzJ7LfSdnYSFd7+oEv6M8NgUatatIN2HxNtDdlcvFAf+VIq4Of9cDMJEJC0aSRv/x898RYhB4Yppg==", "path": "system.componentmodel.annotations/4.5.0", "hashPath": "system.componentmodel.annotations.4.5.0.nupkg.sha512"}, "System.Console/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHDrIxiqk1h03m6khKWV2X8p/uvN79rgSqpilL6uzpmSfxfU5ng8VcPtW4qsDsQDHiTv6IPV9TmD5M/vElPNLg==", "path": "system.console/4.3.0", "hashPath": "system.console.4.3.0.nupkg.sha512"}, "System.Diagnostics.Contracts/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-eelRRbnm+OloiQvp9CXS0ixjNQldjjkHO4iIkR5XH2VIP8sUB/SIpa1TdUW6/+HDcQ+MlhP3pNa1u5SbzYuWGA==", "path": "system.diagnostics.contracts/4.3.0", "hashPath": "system.diagnostics.contracts.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.FileVersionInfo/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-omCF64wzQ3Q2CeIqkD6lmmxeMZtGHUmzgFMPjfVaOsyqpR66p/JaZzManMw1s33osoAb5gqpncsjie67+yUPHQ==", "path": "system.diagnostics.fileversioninfo/4.3.0", "hashPath": "system.diagnostics.fileversioninfo.4.3.0.nupkg.sha512"}, "System.Diagnostics.Process/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-J0wOX07+QASQblsfxmIMFc9Iq7KTXYL3zs2G/Xc704Ylv3NpuVdo6gij6V3PGiptTxqsK0K7CdXenRvKUnkA2g==", "path": "system.diagnostics.process/4.3.0", "hashPath": "system.diagnostics.process.4.3.0.nupkg.sha512"}, "System.Diagnostics.StackTrace/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiHg0vgtd35/DM9jvtaC1eKRpWZxr0gcQd643ABG7GnvSlf5pOkY2uyd42mMOJoOmKvnpNj0F4tuoS1pacTwYw==", "path": "system.diagnostics.stacktrace/4.3.0", "hashPath": "system.diagnostics.stacktrace.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "path": "system.diagnostics.tools/4.3.0", "hashPath": "system.diagnostics.tools.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.Dynamic.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SNVi1E/vfWUAs/WYKhE9+qlS6KqK0YVhnlT0HQtr8pMIA8YX3lwy3uPMownDwdYISBdmAF/2holEIldVp85Wag==", "path": "system.dynamic.runtime/4.3.0", "hashPath": "system.dynamic.runtime.4.3.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "path": "system.globalization.calendars/4.3.0", "hashPath": "system.globalization.calendars.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/5.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-E8tNMfMWPvlSF5fvmMIVZZHlGuIZzE5uktuR+GN2gFdngh0k6xoZquxfjKC02d0NqfsshNQVTCdSKXD5e9/lpA==", "path": "system.identitymodel.tokens.jwt/5.2.0", "hashPath": "system.identitymodel.tokens.jwt.5.2.0.nupkg.sha512"}, "System.Interactive.Async/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-hZccYiIE5RS1/J9Tb/BNtosAGVggdlsJm4Ojdu+gDV0p4AIi+LUfUogMKkRacljQEJd2AG6vYzvcjhQFkqoZmw==", "path": "system.interactive.async/3.1.1", "hashPath": "system.interactive.async.3.1.1.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "path": "system.io.compression/4.3.0", "hashPath": "system.io.compression.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.IO.Pipelines/4.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-NOC/SO4gSX6t0tB25xxDPqPEzkksuzW7NVFBTQGAkjXXUPQl7ZtyE83T7tUCP2huFBbPombfCKvq1Ox1aG8D9w==", "path": "system.io.pipelines/4.5.2", "hashPath": "system.io.pipelines.4.5.2.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Linq.Queryable/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Yn/WfYe9RoRfmSLvUt2JerP0BTGGykCZkQPgojaxgzF2N0oPo+/AhB8TXOpdCcNlrG3VRtsamtK2uzsp3cqRVw==", "path": "system.linq.queryable/4.0.1", "hashPath": "system.linq.queryable.4.0.1.nupkg.sha512"}, "System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-sYg+FtILtRQuYWSIAuNOELwVuVsxVyJGWQyOnlAzhV4xvhyFnON1bAzYYC+jjRW8JREM45R0R5Dgi8MTC5sEwA==", "path": "system.net.http/4.3.0", "hashPath": "system.net.http.4.3.0.nupkg.sha512"}, "System.Net.NameResolution/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-AFYl08R7MrsrEjqpQWTZWBadqXyTzNDaWpMqyxhb0d6sGhV6xMDKueuBXlLL30gz+DIRY6MpdgnHWlCh5wmq9w==", "path": "system.net.nameresolution/4.3.0", "hashPath": "system.net.nameresolution.4.3.0.nupkg.sha512"}, "System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "path": "system.net.primitives/4.3.0", "hashPath": "system.net.primitives.4.3.0.nupkg.sha512"}, "System.Net.Security/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-IgJKNfALqw7JRWp5LMQ5SWHNKvXVz094U6wNE3c1i8bOkMQvgBL+MMQuNt3xl9Qg9iWpj3lFxPZEY6XHmROjMQ==", "path": "system.net.security/4.3.0", "hashPath": "system.net.security.4.3.0.nupkg.sha512"}, "System.Net.Sockets/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-m6icV6TqQOAdgt5N/9I5KNpjom/5NFtkmGseEH+AK/hny8XrytLH3+b5M8zL/Ycg3fhIocFpUMyl/wpFnVRvdw==", "path": "system.net.sockets/4.3.0", "hashPath": "system.net.sockets.4.3.0.nupkg.sha512"}, "System.Net.WebSockets.WebSocketProtocol/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-FquLjdb/0CeMqb15u9Px6TwnyFl306WztKWu6sKKc5kWPYMdpi5BFEkdxzGoieYFp9UksyGwJnCw4KKAUfJjrw==", "path": "system.net.websockets.websocketprotocol/4.5.1", "hashPath": "system.net.websockets.websocketprotocol.4.5.1.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Metadata/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-COC1aiAJjCoA5GBF+QKL2uLqEBew4JsCkQmoHKbN3TlOZKa2fKLz5CpiRQKDz0RsAOEGsVKqOD5bomsXq/4STQ==", "path": "system.reflection.metadata/1.6.0", "hashPath": "system.reflection.metadata.1.6.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Json/4.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-+7DIJhnKYgCzUgcLbVTtRQb2l1M0FP549XFlFkQM5lmNiUBl44AfNbx4bz61xA8PzLtlYwfmif4JJJW7MPPnjg==", "path": "system.runtime.serialization.json/4.0.2", "hashPath": "system.runtime.serialization.json.4.0.2.nupkg.sha512"}, "System.Runtime.Serialization.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wz+0KOukJGAlXjtKr+5Xpuxf8+c8739RI1C+A2BoQZT+wMCCoMDDdO8/4IRHfaVINqL78GO8dW8G2lW/e45Mcw==", "path": "system.runtime.serialization.primitives/4.3.0", "hashPath": "system.runtime.serialization.primitives.4.3.0.nupkg.sha512"}, "System.Security.Claims/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-P/+BR/2lnc4PNDHt/TPBAWHVMLMRHsyYZbU1NphW4HIWzCggz8mJbTQQ3MKljFE7LS3WagmVFuBgoLcFzYXlkA==", "path": "system.security.claims/4.3.0", "hashPath": "system.security.claims.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-WG3r7EyjUe9CMPFSs6bty5doUqT+q9pbI80hlNzo2SkPkZ4VTuZkGWjpp77JB8+uaL4DFPRdBsAY+DX3dBK92A==", "path": "system.security.cryptography.cng/4.5.0", "hashPath": "system.security.cryptography.cng.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "path": "system.security.cryptography.openssl/4.3.0", "hashPath": "system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-TGQX51gxpY3K3I6LJlE2LAftVlIMqJf0cBGhz68Y89jjk3LJCB6SrwiD+YN1fkqemBvWGs+GjyMJukl6d6goyQ==", "path": "system.security.cryptography.pkcs/4.5.0", "hashPath": "system.security.cryptography.pkcs.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "path": "system.security.cryptography.x509certificates/4.3.0", "hashPath": "system.security.cryptography.x509certificates.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Xml/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-i2Jn6rGXR63J0zIklImGRkDIJL4b1NfPSEbIVHBlqoIb12lfXIigCbDRpDmIEzwSo/v1U5y/rYJdzZYSyCWxvg==", "path": "system.security.cryptography.xml/4.5.0", "hashPath": "system.security.cryptography.xml.4.5.0.nupkg.sha512"}, "System.Security.Permissions/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-9gdyuARhUR7H+p5CjyUB/zPk7/Xut3wUSP8NJQB6iZr8L3XUXTMdoLeVAg9N4rqF8oIpE7MpdqHdDHQ7XgJe0g==", "path": "system.security.permissions/4.5.0", "hashPath": "system.security.permissions.4.5.0.nupkg.sha512"}, "System.Security.Principal/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I1tkfQlAoMM2URscUtpcRo/hX0jinXx6a/KUtEQoz3owaYwl3qwsO8cbzYVVnjxrzxjHo3nJC+62uolgeGIS9A==", "path": "system.security.principal/4.3.0", "hashPath": "system.security.principal.4.3.0.nupkg.sha512"}, "System.Spatial/5.8.2": {"type": "package", "serviceable": true, "sha512": "sha512-0RfZZJ8RlrfjoBPAF6pczX4Nd2kyLM8EX1PCP5Rqs/jOhJBUPYhpXjIsVAYN7kocj9IJ9XoJWAxWgXIDtJY2Ag==", "path": "system.spatial/5.8.2", "hashPath": "system.spatial.5.8.2.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RpT2DA+L660cBt1FssIE9CAGpLFdFPuheB7pLpKpn6ZXNby7jDERe8Ua/Ne2xGiwLVG2JOqziiaVCGDon5sKFA==", "path": "system.text.regularexpressions/4.3.0", "hashPath": "system.text.regularexpressions.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Parallel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbjBNZHf/vQCfcdhzx7knsiygoCKgxL8mZOeocXZn5gWhCdzHIq6bYNKWX0LAJCWYP7bds4yBK8p06YkP0oa0g==", "path": "system.threading.tasks.parallel/4.3.0", "hashPath": "system.threading.tasks.parallel.4.3.0.nupkg.sha512"}, "System.Threading.Thread/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OHmbT+Zz065NKII/ZHcH9XO1dEuLGI1L2k7uYss+9C1jLxTC9kTZZuzUOyXHayRk+dft9CiDf3I/QZ0t8JKyBQ==", "path": "system.threading.thread/4.3.0", "hashPath": "system.threading.thread.4.3.0.nupkg.sha512"}, "System.Threading.ThreadPool/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-k/+g4b7vjdd4aix83sTgC9VG6oXYKAktSfNIJUNGxPEj7ryEOfzHHhfnmsZvjxawwcD9HyWXKCXmPjX8U4zeSw==", "path": "system.threading.threadpool/4.3.0", "hashPath": "system.threading.threadpool.4.3.0.nupkg.sha512"}, "System.Threading.Timer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Z6YfyYTCg7lOZjJzBjONJTFKGN9/NIYKSxhU5GRd+DTwHSZyvWp1xuI5aR+dLg+ayyC5Xv57KiY4oJ0tMO89fQ==", "path": "system.threading.timer/4.3.0", "hashPath": "system.threading.timer.4.3.0.nupkg.sha512"}, "System.ValueTuple/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "path": "system.valuetuple/4.5.0", "hashPath": "system.valuetuple.4.5.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5zJ0XDxAIg8iy+t4aMnQAu0MqVbqyvfoUVl1yDV61xdo3Vth45oA2FoY4pPkxYAH5f8ixpmTqXeEIya95x0aCQ==", "path": "system.xml.xdocument/4.3.0", "hashPath": "system.xml.xdocument.4.3.0.nupkg.sha512"}, "System.Xml.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lJ8AxvkX7GQxpC6GFCeBj8ThYVyQczx2+f/cWHJU8tjS7YfI6Cv6bon70jVEgs2CiFbmmM8b9j1oZVx0dSI2Ww==", "path": "system.xml.xmldocument/4.3.0", "hashPath": "system.xml.xmldocument.4.3.0.nupkg.sha512"}, "System.Xml.XPath/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-v1JQ5SETnQusqmS3RwStF7vwQ3L02imIzl++sewmt23VGygix04pEH+FCj1yWb+z4GDzKiljr1W7Wfvrx0YwgA==", "path": "system.xml.xpath/4.3.0", "hashPath": "system.xml.xpath.4.3.0.nupkg.sha512"}, "System.Xml.XPath.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-jw9oHHEIVW53mHY9PgrQa98Xo2IZ0ZjrpdOTmtvk+Rvg4tq7dydmxdNqUvJ5YwjDqhn75mBXWttWjiKhWP53LQ==", "path": "system.xml.xpath.xdocument/4.3.0", "hashPath": "system.xml.xpath.xdocument.4.3.0.nupkg.sha512"}, "WindowsAzure.Storage/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-W6ZZ0/o7+3Qb77mRAQyLjPudHG3OMeeQ4p9yY13PUdJArmRCx2cLMm5F4tpIjJXxzHC0ew0oK7DMDGILMdfCnw==", "path": "windowsazure.storage/8.1.4", "hashPath": "windowsazure.storage.8.1.4.nupkg.sha512"}}}