<template>
  <div>
    <b-btn variant="secondary btn-round select-all" size="sm" @click="selectAll">Select All</b-btn>
    <b-table striped hover :items="contacts" :fields="fields">
      <template #cell(isSelected)="data">
        <b-checkbox :checked="data.item.isSelected" @change="toggleSelect($event, data.item.userId)" />
      </template>
      <template #cell(userName)="data">
        {{data.item.userName}}
      </template>
      <template #cell(firstName)="data">
        {{data.item.firstName}}
      </template>
      <template #cell(lastName)="data">
        {{data.item.lastName}}
      </template>
    </b-table>
  </div>
</template>

<script>
import groupsManagementService from '@/services/accounts/groups/GroupsManagementService'

export default {
  name: 'contacts-listing',
  props: {
    accountId: {
      type: Number,
      required: true
    }
  },
  async mounted () {
    await this.populateContacts()
  },
  data () {
    return {
      contacts: [],
      selectedUserIds: [],
      fields: [{
        key: 'isSelected',
        label: 'Select'
      }, 'firstName', 'lastName', 'userName']
    }
  },
  methods: {
    async populateContacts () {
      const response = await groupsManagementService.getAccountUsers(this.accountId)
      this.contacts = (response.data || []).map(x => ({
        ...x,
        isSelected: false
      }))
    },
    toggleSelect (val, id) {
      this.contacts.find(x => x.userId === id).isSelected = val
      this.$emit('input', this.contacts.filter(x => x.isSelected).map(x => x.userId))
    },
    selectAll () {
      let selectValue = true
      if (this.contacts.length > 0) {
        selectValue = !!this.contacts.find(x => !x.isSelected)
      }
      this.contacts.forEach(x => {
        x.isSelected = selectValue
      })

      this.$emit('input', this.contacts.filter(x => x.isSelected).map(x => x.userId))
    }
  }
}
</script>

<style scoped>
  .select-all {
    position: absolute;
    z-index: 999;
    top: 34px;
  }
</style>
