<template>
  <nav class="layout-footer footer" :class="getLayoutFooterBg()">
    <div class="container-fluid pb-3">
      Copyright © 2001-{{getCurrentYear}} eBizAutos |
      <a href="https://www.ebizautos.com/privacy-policy" target="_blank" class="footer-link pt-3">Privacy Policy</a> |
      <a href="https://www.ebizautos.com/cookie-policy" target="_blank" class="footer-link pt-3">Cookie Policy</a> |
      <a href="https://www.ebizautos.com/terms" target="_blank" class="footer-link pt-3">Terms of Service</a> |
      <a href="https://app.termly.io/notify/14e037ad-840a-4f0e-8931-965e0accdf8a" target="_blank" class="footer-link pt-3">Do Not Sell My Info</a>
    </div>
  </nav>
</template>

<script>
import moment from 'moment'

export default {
  name: 'app-layout-footer',
  computed: {
    getCurrentYear () {
      return moment().format('YYYY')
    }
  },
  methods: {
    getLayoutFooterBg () {
      return `bg-${this.layoutFooterBg}`
    }
  }
}
</script>
