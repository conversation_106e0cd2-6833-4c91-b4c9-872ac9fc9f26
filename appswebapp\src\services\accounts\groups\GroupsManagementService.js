import BaseService from '../../BaseService'

class GroupsService extends BaseService {
  getGroups (params) {
    return this.axios.get(`/api/accounts/groups`, {params: params})
  }
  getGroup (groupId) {
    return this.axios.get(`/api/accounts/groups/${groupId}`)
  }
  createGroup (group) {
    return this.axios.post(`/api/accounts/groups`, group)
  }
  updateGroup (group) {
    return this.axios.post(`/api/accounts/groups/${group.groupId}`, group)
  }
  deleteGroup (groupId) {
    return this.axios.post(`/api/accounts/groups/${groupId}/delete`)
  }
  getAccountUsers (accountId) {
    return this.axios.get(`/api/accounts/groups/permissions/users/${accountId}`)
  }
  getGroupPemissionRules (groupId) {
    return this.axios.get(`/api/accounts/groups/${groupId}/permissions`)
  }
  getGroupAccountPermissionRules (groupId, accountId, filters) {
    return this.axios.get(`/api/accounts/groups/${groupId}/accountpermissions/${accountId}`, { params: filters })
  }
  getUserGroupPermissionRulesFiltered (groupId, accountId, userId, filters) {
    return this.axios.get(`/api/accounts/groups/${groupId}/accountpermissions/${accountId}/${userId}`, { params: filters })
  }
  getPermissionRule (groupId, ruleId) {
    return this.axios.get(`/api/accounts/groups/${groupId}/permissions/${ruleId}`)
  }
  createPermissionRule (groupId, rule) {
    return this.axios.post(`/api/accounts/groups/${groupId}/permissions`, rule)
  }
  createPermissionRules (groupId, insertModel) {
    return this.axios.post(`/api/accounts/groups/${groupId}/permissions/create/multiple`, insertModel)
  }
  updatePermissionRule (groupId, rule) {
    return this.axios.post(`/api/accounts/groups/${groupId}/permissions/${rule.ruleId}`, rule)
  }
  updatePermissionRules (groupId, updateModel) {
    return this.axios.post(`/api/accounts/groups/${groupId}/permissions/update/multiple`, updateModel)
  }
  deletePermissionRule (groupId, ruleId) {
    return this.axios.post(`/api/accounts/groups/${groupId}/permissions/${ruleId}/delete`)
  }
  sendSelectedUsers (groupId, selectedUsers) {
    return this.axios.post(`/api/accounts/groups/${groupId}/permissions/copy`, selectedUsers)
  }
}

export default new GroupsService()
