<template>
  <div>
    <h4>Log Details</h4>
    <b-card v-if="isLoaded">
      <b-table
        :items="items"
        :fields="getTableFields"
      >
        <template #cell(renderedMessage)="data">
          <div class="rendered-message-box" v-html="getMessageDetails(data.item)"></div>
        </template>
      </b-table>
    </b-card>
    <loader v-else size="lg"/>
  </div>
</template>

<style>
.rendered-message-box {
  word-break: break-word;
}
</style>

<script>
import loader from '@/components/_shared/loader'
import videoEncoderTypes from '@/shared/inventory/videoEncoderTypes'
import VideoEncoderService from '@/services/inventory/VideoEncoderService'
import moment from 'moment'

export default {
  name: 'inventory-autovideo-log-details',
  metaInfo: {
    title: 'AutoVideo Log Details'
  },
  props: {
    batchId: { type: String, required: true },
    logType: { type: String, required: true }
  },
  data () {
    return {
      isLoaded: false,
      items: []
    }
  },
  created () {
    let requestProcessor = function (getFunc) {
      getFunc().then(res => {
        this.items = res.data
      }).catch(ex => {
        this.$toaster.error('Failed on receiving data from server')
      }).finally(() => {
        this.isLoaded = true
      })
    }.bind(this)
    if (this.logType === videoEncoderTypes.photoToVideoLogTypes.processing) {
      requestProcessor(() => VideoEncoderService.getPhotoToVideoProcessingLogDetails(this.batchId))
    } else if (this.logType === videoEncoderTypes.photoToVideoLogTypes.queueManager) {
      requestProcessor(() => VideoEncoderService.getPhotoToVideoQueueMangerLogDetails(this.batchId))
    } else {
      this.$toaster.error('Not valid log type')
      this.isLoaded = true
    }
  },
  components: {
    loader
  },
  computed: {
    getTableFields () {
      return [
        {
          key: 'renderedMessage',
          label: 'Message',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'timestamp',
          label: 'Date',
          tdClass: 'py-2 align-middle',
          formatter: value => {
            return moment(value).format('MM/DD/YYYY hh:mm:ss A')
          }
        }
      ]
    }
  },
  methods: {
    getMessageDetails (logItem) {
      let details = logItem.renderedMessage
      if (logItem.exception) {
        details = details.concat(': ', logItem.exception)
      }

      if (details.includes('Video url')) {
        let expression = /(https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|www\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9]+\.[^\s]{2,}|www\.[a-zA-Z0-9]+\.[^\s]{2,})/gi
        let regex = new RegExp(expression)
        details = details.replaceAll('"', '').replace(regex, `<a class="text-primary" href="$1?timestamp=${new Date().getTime()}" target="_blank" >$1</a>`)
      }

      return details
    }
  }
}
</script>
