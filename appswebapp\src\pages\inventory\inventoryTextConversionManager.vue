<template>
  <div>
    <div class="d-flex flex-row mb-2">
      <h4>{{ getTitle }}</h4>
      <b-btn class="ml-auto" @click="cancel">Cancel</b-btn>
      <l-button :loading="isInProcessing" class="ml-2" variant="primary" @click="saveChanges">Save Changes</l-button>
    </div>
    <b-card>
      <h6 class="w-100 pb-2 border-bottom">Video Conversion Information</h6>
      <detail-row :fixed-payload-width="true">
        <span slot="title">From:</span>
        <b-form-input v-model="textConversion.fromValue" slot="payload"></b-form-input>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">To:</span>
        <b-form-input v-model="textConversion.toValue" slot="payload"></b-form-input>
      </detail-row>
      <detail-row :fixed-payload-width="true">
        <span slot="title">Replace Type:</span>
        <b-form-select v-model="textConversion.type" :options="getTextConversionTypeOptions" slot="payload"></b-form-select>
      </detail-row>
    </b-card>
  </div>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import videoEncoderTypes from '@/shared/inventory/videoEncoderTypes'
import VideoEncoderService from '../../services/inventory/VideoEncoderService'

const textConversionTypes = videoEncoderTypes.textConversionTypes

export default {
  name: 'inventory-text-conversion-manger',
  props: {
    id: { type: Number }
  },
  metaInfo: {
    title: 'Text Conversion'
  },
  data () {
    return {
      isInProcessing: false,
      textConversion: {
        type: textConversionTypes.caseSensitive.value
      }
    }
  },
  components: {
    detailRow
  },
  created () {
    this.populateData()
  },
  computed: {
    isEditMode () {
      return !!this.id
    },
    getTitle () {
      return this.isEditMode ? 'Edit Setting' : 'Add New Setting'
    },
    getTextConversionTypeOptions () {
      return Object.values(textConversionTypes)
    }
  },
  methods: {
    populateData () {
      if (this.isEditMode) {
        VideoEncoderService.getTextConversion(this.id).then(res => {
          this.textConversion = res.data
        }).catch(ex => {
          this.$toaster.exception(ex, 'Something went wrong!')
        })
      }
    },
    createTextConversion () {
      VideoEncoderService.createTextConversion(this.textConversion).then(res => {
        this.$toaster.success('Created Successfully')
        this.goToListingPage()
      }).catch(ex => {
        this.$toaster.error('Failed on creating')
      }).finally(() => {
        this.isInProcessing = false
      })
    },
    updateTextConversion () {
      VideoEncoderService.updateTextConversion(this.id, this.textConversion).then(res => {
        this.$toaster.success('Updated Successfully')
        this.goToListingPage()
      }).catch(ex => {
        this.$toaster.error('Failed on updating')
      }).finally(() => {
        this.isInProcessing = false
      })
    },
    saveChanges () {
      this.isInProcessing = true
      if (this.isEditMode) {
        this.updateTextConversion()
      } else {
        this.createTextConversion()
      }
    },
    cancel () {
      this.goToListingPage()
    },
    goToListingPage () {
      this.$router.push({name: 'inventory-global-autovideo-settings'})
    }
  }
}
</script>
