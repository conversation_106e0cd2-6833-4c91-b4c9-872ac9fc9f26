<template>
  <b-card>
    <b-row class="border-bottom mb-2 py-2">
      <b-col>
        <strong>{{reviseHeader}}</strong>
      </b-col>
      <c-button v-if="!isDisabled" :message="`Are you sure you want ${btnDesc}?`" variant="primary" size="sm" @confirm="onConfirm">
        {{btnDesc}}
      </c-button>
      <loader class="mr-5" v-else size="sm"/>
    </b-row>
    <detail-row bootstrapMode :fixed-payload-width="true">
      <span slot="title">Listing ID:</span>
      <b-link slot="payload" :href="getListingUrl" class="text-info"><u>{{revise.AuctionId}}</u></b-link>
    </detail-row>
    <detail-row titlePosition="start" bootstrapMode :extra-large-payload-width="true">
        <span slot="title">Description Text:</span>
        <b-form-group
          slot="payload"
          class="w-100"
        >
          <quill-editor :options="options" v-model="data.DescriptionText" class="w-100"></quill-editor>
          <b-form-text class="text-dark">
            Enter text above to be added to your item description.
            <br>
            This text will be added to the bottom of your auction description along with a Date/Time stamp.
          </b-form-text>
        </b-form-group>
      </detail-row>
  </b-card>
</template>

<style src="@/vendor/libs/vue-quill-editor/typography.scss" lang="scss"></style>
<style src="@/vendor/libs/vue-quill-editor/editor.scss" lang="scss"></style>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import loader from '@/components/_shared/loader'
import constants from '@/shared/ebay/constants'
import {mapGetters} from 'vuex'

export default {
  props: {
    btnDesc: {
      type: String,
      default: 'Revise'
    },
    reviseHeader: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      data: {
        DescriptionText: ''
      },
      isDisabled: false,
      options: {
        modules: {
          toolbar: [
            [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
            ['bold', 'italic', 'underline', 'strike'],
            [{ 'color': [] }, { 'background': [] }],
            ['blockquote'],
            [{ 'list': 'ordered' }, { 'list': 'bullet' }]
          ]
        }
      }
    }
  },
  components: {
    detailRow,
    loader,
    'quill-editor': () => import('vue-quill-editor/dist/vue-quill-editor').then(m => m.quillEditor).catch(() => {})
  },
  computed: {
    ...mapGetters('eBayRevise', ['revise']),
    getListingUrl () {
      return constants.eBayInfoUrls.ebayItemUrl(this.revise.AuctionId)
    }
  },
  methods: {
    onConfirm () {
      if (!this.data.DescriptionText.trim()) {
        this.$toaster.error('Please fill description field')
        return
      }

      let apiParams = {
        accountId: this.revise.AccountId,
        auctionId: this.revise.AuctionId,
        data: this.data
      }
      this.isDisabled = true
      this.$store.dispatch('eBayRevise/addToItemDescription', apiParams).then(res => {
        this.$toaster.success('Operation Successfully')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
        if (ex.response && ex.response.status !== 400) {
          this.$logger.handleError(ex, `Exception occurred on ${this.reviseHeader}`)
        }
      }).finally(() => {
        this.isDisabled = false
        setTimeout(() => this.$router.go(), 4000)
      })
    }
  }
}
</script>
