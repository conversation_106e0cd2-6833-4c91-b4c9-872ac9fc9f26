import vehicleTasks from '@/shared/common/vehicle/vehicleTasks'
import vehicleConditions from '@/shared/common/vehicle/vehicleConditions'
import { ObjectSchema } from '../common/objectHelpers'

export default new ObjectSchema({
  alertType: { type: Number, default: vehicleTasks.all.value },
  condition: { type: Number, default: vehicleConditions.all },
  pageNumber: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  sortType: { type: Number, default: 15 },
  search: { type: String, default: '' }
})
