<template>
  <div>

    <div class="fullwidth-element bg-dark dark text-white">

      <div class="container-fluid container-p-y">

        <h4 class="d-flex justify-content-between align-items-center w-100 font-weight-bold pb-0 mb-4">
          <div>Channel Segments</div>
          <b-dropdown right text="September 23, 2018" variant="primary btn-round" class="d-block" size="md">
            <div role="group" class="ddown-menu p-3" style="width:280px;">
              <b-form>
                <b-row class="mb-3 align-items-center">
                  <b-col cols="3">
                    <label for="daterange-year">Year</label>
                  </b-col>
                  <b-col cols="9">
                    <b-form-input id="daterange-year" size="md" type="text" value="2018"></b-form-input>
                  </b-col>
                </b-row>
                <b-row class="mb-3 align-items-center">
                  <b-col cols="3">
                    <label for="daterange-quarter">Quarter</label>
                  </b-col>
                  <b-col cols="9">
                    <b-form-select id="daterange-quarter" size="md" v-model="selectedQuarter" :options="optionsQuarter" />
                  </b-col>
                </b-row>
                <b-row class="mb-3 align-items-center">
                  <b-col cols="3">
                    <label for="daterange-month">Month</label>
                  </b-col>
                  <b-col cols="9">
                    <b-form-select id="daterange-month" size="md" v-model="selectedMonth" :options="optionsMonth" />
                  </b-col>
                </b-row>
                <b-row>
                  <b-col>
                    <b-button variant="primary btn-round" class="ml-auto d-block">Apply</b-button>
                  </b-col>
                </b-row>
              </b-form>
            </div>
          </b-dropdown>
        </h4>

        <div class="chart-filters text-center">
          <b-nav pills class="justify-content-between justify-content-lg-end">
            <b-nav-item active><i class="ion ion-md-browsers d-block d-lg-none"></i> Sessions</b-nav-item>
            <b-nav-item><i class="ion ion-md-eye d-block d-lg-none"></i> Views</b-nav-item>
            <b-nav-item><i class="ion ion-md-time d-block d-lg-none"></i> Time</b-nav-item>
            <b-nav-item><i class="ion ion-md-done-all d-block d-lg-none"></i> Actions</b-nav-item>
            <b-nav-item><i class="ion ion-md-chatboxes d-block d-lg-none"></i> Leads</b-nav-item>
          </b-nav>
        </div>

        <div class="row">
          <div class="col-md-8">
            <vue-echart :options="barOptions" :auto-resize="true"></vue-echart>
          </div>
          <div class="col-md-4">
            <div class="d-flex align-items-center position-relative mt-4" style="height:300px;">
              <vue-echart :options="pieOptions" :auto-resize="true"></vue-echart>
            </div>
          </div>
        </div>

        <div class="row mt-3 widget-metric-higlights">

          <div class="col-6 col-sm-6 col-lg-6 col-xl">
            <b-card no-body class="m-0 bg-transparent">
              <b-card-body class="d-flex justify-content-between align-items-center">
                <div>
                  <div class="metric-title h5 mb-1">Sessions</div>
                  <div>
                    <span class="metric-amount text-xlarge">60,0037</span>
                    <span class="text-success font-weight-bold">+48%</span>
                  </div>
                  <div class="small opacity-75 mt-2">In the last 7 days</div>
                </div>
                <i class="ion ion-md-browsers h1 m-0 opacity-25 d-none d-sm-inline"></i>
              </b-card-body>
            </b-card>
          </div>

          <div class="col-6 col-sm-6 col-lg-6 col-xl">
            <b-card no-body class="m-0 bg-transparent">
              <b-card-body class="d-flex justify-content-between align-items-center">
                <div>
                  <div class="metric-title h5 mb-1">Page Views</div>
                  <div>
                      <span class="metric-amount text-xlarge">280,992</span>
                      <span class="font-weight-bold text-success">+24%</span>
                  </div>
                  <div class="small opacity-75 mt-2">In the last 7 days</div>
                </div>
                <i class="ion ion-md-eye h1 m-0 opacity-25 d-none d-sm-inline"></i>
              </b-card-body>
            </b-card>
          </div>

          <div class="col-6 col-sm-6 col-lg-6 col-xl">
            <b-card no-body class="m-0 bg-transparent">
              <b-card-body class="d-flex justify-content-between align-items-center">
                <div>
                  <div class="metric-title h5 mb-1">Avg. Time on Site</div>
                  <div>
                    <span class="metric-amount text-xlarge">00:03:10</span>
                  </div>
                  <div class="small opacity-75 mt-2">In the last 7 days</div>
                </div>
                <i class="ion ion-md-time h1 m-0 opacity-25 d-none d-sm-inline"></i>
              </b-card-body>
            </b-card>
          </div>

          <div class="col-6 col-sm-6 col-lg-6 col-xl">
            <b-card no-body class="m-0 bg-transparent">
              <b-card-body class="d-flex justify-content-between align-items-center">
                <div>
                  <div class="metric-title h5 mb-1">Leads</div>
                  <div>
                      <span class="metric-amount text-xlarge">2,025</span>
                      <span class="font-weight-bold text-danger">-3%</span>
                  </div>
                  <div class="small opacity-75 mt-2">In the last 7 days</div>
                </div>
                <i class="ion ion-md-chatboxes h1 m-0 opacity-25 d-none d-sm-inline"></i>
              </b-card-body>
            </b-card>
          </div>

        </div>

      </div>

    </div>

    <div class="row mt-3">

        <div class="col">

          <b-card no-body>
            <div class="table-responsive">
              <b-table striped hover :items="items" :fields="fields" class="card-table">
                <template #cell(show_details)="row">
                  <b-button size="sm" @click.stop="row.toggleDetails" class="text-center">
                   {{ row.detailsShowing ? 'Hide' : 'Show' }} Details
                  </b-button>
                </template>
                <template #row-details="row">
                  <b-card>
                    <b-row class="mb-2">
                      <b-col sm="12" md="4" lg="3" xl="2"><b>New Sessions:</b></b-col>
                      <b-col col>{{ row.item.new_sessions }}</b-col>
                    </b-row>
                    <b-row class="mb-2">
                      <b-col sm="12" md="4" lg="3" xl="2"><b>Page Views Per Session:</b></b-col>
                      <b-col col>{{ row.item.views_per_session }}</b-col>
                    </b-row>
                    <b-row class="mb-2">
                      <b-col sm="12" md="4" lg="3" xl="2"><b>Time on Site:</b></b-col>
                      <b-col>{{ row.item.time_on_site }}</b-col>
                    </b-row>
                    <b-row class="mb-2">
                      <b-col sm="12" md="4" lg="3" xl="2"><b>Form Leads:</b></b-col>
                      <b-col>{{ row.item.form_leads }}</b-col>
                    </b-row>
                    <b-row class="mb-2">
                      <b-col sm="12" md="4" lg="3" xl="2"><b>SMS Leads:</b></b-col>
                      <b-col>{{ row.item.sms_leads }}</b-col>
                    </b-row>
                    <b-row class="mb-2">
                      <b-col sm="12" md="4" lg="3" xl="2"><b>Phone Leads:</b></b-col>
                      <b-col>{{ row.item.phone_leads }}</b-col>
                    </b-row>
                    <b-button size="sm" @click="row.toggleDetails">Hide Details</b-button>
                  </b-card>
                </template>
              </b-table>
            </div>
          </b-card>

        </div>

    </div>

  </div>
</template>

<style src="@/vendor/styles/pages/analytics.scss" lang="scss"></style>

<script>
import ECharts from 'vue-echarts/components/ECharts.vue'

import 'echarts/lib/chart/bar'
import 'echarts/lib/chart/pie'
import 'echarts/lib/component/tooltip'
import 'echarts/lib/component/legend'

const dataAxis = ['Sept. 1', 'Sept. 2', 'Sept. 3', 'Sept. 4', 'Sept. 5', 'Sept. 6', 'Sept. 7', 'Sept. 8', 'Sept. 9', 'Sept. 10', 'Sept. 11', 'Sept. 12', 'Sept. 13', 'Sept. 14', 'Sept. 15', 'Sept. 16', 'Sept. 17', 'Sept. 18', 'Sept. 19', 'Sept. 20', 'Sept. 21', 'Sept. 22', 'Sept. 23']
const colors = ['#dc3545', '#28a745', '#007bff', '#ffc107', '#7751bd', '#1abc9c', '#e67e22', '#3498db', '#ff5722', '#ff4514', '#647c8a', '#3f51b5', '#2196f3', '#00b862', '#afdf0a']

export default {
  name: 'channel-segments',
  metaInfo: {
    title: 'Analytics - Channel Segments'
  },
  components: {
    'vue-echart': ECharts
  },
  data: () => ({
    selectedQuarter: '0',
    optionsQuarter: [
      { value: '0', disabled: false, text: 'Not Selected' },
      { value: '1', disabled: false, text: 'Quarter #1 (Jan, Feb, Mar)' },
      { value: '2', disabled: false, text: 'Quarter #2 (Apr, May, Jun)' },
      { value: '3', disabled: false, text: 'Quarter #3 (Jul, Aug, Sep)' },
      { value: '4', disabled: true, text: 'Quarter #4 (Oct, Nov, Dec)' }
    ],
    selectedMonth: '9',
    optionsMonth: [
      { value: '0', disabled: false, text: 'Not Selected' },
      { value: '1', disabled: false, text: 'January' },
      { value: '2', disabled: false, text: 'February' },
      { value: '3', disabled: false, text: 'March' },
      { value: '4', disabled: false, text: 'April' },
      { value: '5', disabled: false, text: 'May' },
      { value: '6', disabled: false, text: 'June' },
      { value: '7', disabled: false, text: 'July' },
      { value: '8', disabled: false, text: 'August' },
      { value: '9', disabled: false, text: 'September' },
      { value: '10', disabled: false, text: 'October' },
      { value: '11', disabled: false, text: 'November' },
      { value: '12', disabled: false, text: 'December' }
    ],
    barOptions: {
      color: '#6b0001',
      tooltip: {
        trigger: 'axis',
        formatter: '{b}<br /> {c} sessions',
        axisPointer: {
          type: 'line',
          lineStyle: {
            color: '#444'
          }
        },
        textStyle: {
          fontSize: 13
        }
      },
      grid: {
        left: '0',
        right: '0',
        bottom: '3%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          data: dataAxis,
          axisTick: {
            show: true,
            alignWithLabel: true
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#444'
            }
          },
          axisLabel: {
            color: 'rgba(255, 255, 255, .9)'
          }
        }
      ],
      yAxis: [
        {
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#444'
            }
          },
          axisLabel: {
            color: 'rgba(255, 255, 255, .9)'
          },
          type: 'value'
        }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 50,
          shadowColor: 'rgba(0, 0, 0, 0.3)'
        }
      },
      series: [
        {
          barWidth: '4',
          type: 'bar',
          data: [2138, 1618, 2191, 2085, 2054, 1852, 1950, 1921, 1561, 2139, 2046, 1886, 2018, 2162, 1988, 1576, 2147, 2175, 2029, 2040, 2015, 1387, 1187],
          itemStyle: {
            normal: {
              color: '#dc3545'
            },
            emphasis: {
              shadowBlur: 50,
              shadowColor: '#dc3545',
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0.5, color: '#dc3545'
                }, {
                  offset: 1, color: '#ff5a44'
                }],
                globalCoord: false
              }
            }
          }
        }],
      animationDuration: 2000
    },
    pieOptions: {
      color: colors,
      tooltip: {
        show: true,
        trigger: 'item',
        formatter: '{b}<br />{c} ({d}%)',
        textStyle: {
          fontSize: 13
        }
      },
      legend: {
        data: ['Organic', 'Paid', 'Direct', 'Referral', '(Other)', 'Social', 'Display'],
        show: false,
        y: 'top',
        x: 'right',
        orient: 'vertical',
        textStyle: {
          color: 'rgba(255, 255, 255, .9)'
        }
      },
      visualMap: {
        show: true,
        min: 80,
        max: 600,
        inRange: {
          colorLightness: [0, 1]
        }
      },
      series: [{
        type: 'pie',
        radius: ['55%', '80%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        minAngle: 10,
        label: {
          normal: {
            show: false,
            position: 'center',
            formatter: '{b}\n{d}%'
          },
          emphasis: {
            show: true,
            textStyle: {
              fontSize: '18',
              fontWeight: 'bold'
            }
          }
        },
        labelLine: {
          normal: {
            show: false
          },
          emphasis: {
            show: false
          }
        },
        data: [
          { value: 22924, name: 'Organic' },
          { value: 17175, name: 'Paid' },
          { value: 12774, name: 'Direct' },
          { value: 3299, name: 'Referral' },
          { value: 2858, name: '(Other)' },
          { value: 575, name: 'Social' },
          { value: 432, name: 'Display' }
        ],
        itemStyle: {
          normal: {
            shadowBlur: 50,
            shadowColor: 'rgba(0, 0, 0, 0.1)'
          },
          emphasis: {
            shadowBlur: 50,
            shadowColor: 'rgba(0, 0, 0, 0.3)'
          }
        }
      }],
      animationDuration: 2000
    },
    fields: [
      {
        key: 'channel',
        sortable: true
      },
      {
        key: 'sessions',
        sortable: true
      },
      {
        key: 'users',
        sortable: true
      },
      {
        key: 'views',
        label: 'Page Views',
        sortable: true
      },
      {
        key: 'avg_time_on_site',
        label: 'Avg. Time on Site',
        sortable: true
      },
      {
        key: 'web_actions',
        sortable: true
      },
      {
        key: 'total_leads',
        sortable: true
      },
      {
        key: 'show_details'
      }
    ],
    items: [
      {
        channel: 'Organic',
        sessions: '22924',
        users: '13563',
        new_sessions: '45.81%',
        views: '148792',
        views_per_session: '6.49%',
        avg_time_on_site: '00:04:31',
        time_on_site: '72.02:17:48',
        web_actions: '3204',
        form_leads: '185',
        phone_leads: '476',
        sms_leads: '0',
        total_leads: '661'
      },
      {
        channel: 'Paid',
        sessions: '17175',
        users: '10744',
        new_sessions: '51.11%',
        views: '45691',
        views_per_session: '2.66%',
        avg_time_on_site: '00:01:31',
        time_on_site: '18.03:01:45',
        web_actions: '967',
        form_leads: '82',
        phone_leads: '697',
        sms_leads: '6',
        total_leads: '785'
      },
      {
        channel: 'Direct',
        sessions: '12774',
        users: '8045',
        new_sessions: '56%',
        views: '57677',
        views_per_session: '4.52%',
        avg_time_on_site: '00:03:10',
        time_on_site: '28.05:21:49',
        web_actions: '1410',
        form_leads: '111',
        phone_leads: '177',
        sms_leads: '0',
        total_leads: '288'
      },
      {
        channel: 'Referral',
        sessions: '3299',
        users: '1678',
        new_sessions: '38.38%',
        views: '17466',
        views_per_session: '5.29%',
        avg_time_on_site: '00:05:05',
        time_on_site: '11.15:36:29',
        web_actions: '403',
        form_leads: '1',
        phone_leads: '263',
        sms_leads: '0',
        total_leads: '264'
      },
      {
        channel: '(Other)',
        sessions: '2858',
        users: '1736',
        new_sessions: '38.87%',
        views: '7474',
        views_per_session: '2.62%',
        avg_time_on_site: '00:01:26',
        time_on_site: '2.20:35:51',
        web_actions: '96',
        form_leads: '0',
        phone_leads: '0',
        sms_leads: '0',
        total_leads: '0'
      },
      {
        channel: 'Social',
        sessions: '575',
        users: '454',
        new_sessions: '50.78%',
        views: '2873',
        views_per_session: '5%',
        avg_time_on_site: '00:02:46',
        time_on_site: '1.02:31:51',
        web_actions: '61',
        form_leads: '3',
        phone_leads: '0',
        sms_leads: '0',
        total_leads: '3'
      },
      {
        channel: 'Display',
        sessions: '432',
        users: '371',
        new_sessions: '78.7%',
        views: '1019',
        views_per_session: '2.36%',
        avg_time_on_site: '00:01:01',
        time_on_site: '07:20:45',
        web_actions: '10',
        form_leads: '12',
        phone_leads: '12',
        sms_leads: '0',
        total_leads: '24'
      }
    ]
  })

}
</script>
