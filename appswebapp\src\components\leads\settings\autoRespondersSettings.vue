<template>
  <div class='mb-3'>
    <div class="border-bottom">
      <b-row class='mr-1 ml-1'>
        <b-col class="p-0"><h6 class="float-left">Existing Auto Response Templates</h6></b-col>
        <b-col class="p-0">
          <b-btn variant="primary btn-round" @click="onOpenAddNewTemplateModal" class= "float-right" size="sm">
            <span class="ion ion-ios-add"></span><span class="d-none d-sm-inline">&nbsp; Add New Template</span>
          </b-btn>
        </b-col>
      </b-row>
    </div>
    <b-table class="products-table card-table mt-3"
    :fields="getTableFields"
    :items="items"
    striped
    fixed
    responsive
    outlined>
      <template #cell(action)="row">
        <div class="media align-items-center">
          <b-btn variant="secondary" size="sm" class="fixed-sizes mb-2 ml-1" @click="showEditDetails(row)">
            <span v-if='row.detailsShowing'>Cancel</span>
            <div v-else><font-awesome-icon  icon="pencil-alt" size="sm" /> <span class="btn-title">Edit</span></div>
          </b-btn>
          <b-btn variant="primary" class="fixed-sizes mb-2 ml-1" size='sm' v-if='row.item.canDelete' @click="onDelete(row.item)">
            Delete
          </b-btn>
        </div>
      </template>
      <template #row-details="row">
        <b-card>
          <div class="border-bottom">
            <b-row class='pl-3'><h6 class="float-left">Message:</h6></b-row>
          </div>
          <div v-if='row.item.details'>
            <detail-row fixedPayloadWidth>
              <span slot="title">Template Name:</span>
              <b-form-input slot="payload" v-model='row.item.details.name'/>
            </detail-row>
            <detail-row fixedPayloadWidth>
              <span slot="title">Email Subject:</span>
              <b-form-input slot="payload" v-model='row.item.details.subject'/>
            </detail-row>
            <detail-row fixedPayloadWidth>
              <span slot="title">Include Vehicle Details:</span>
              <b-form-checkbox v-model='row.item.details.hasToDisplayVehicleDetails' slot="payload" />
            </detail-row>
            <detail-row bigPayloadWidth>
              <span slot="title">Message:</span>
              <b-form-textarea slot="payload" v-model='row.item.details.messageBody' rows='5'/>
            </detail-row>
            <detail-row fixedPayloadWidth>
              <span slot="title">Include Email Signature:</span>
              <b-form-checkbox v-model='row.item.details.hasToEnableSignature' slot="payload"/>
            </detail-row>
            <detail-row fixedPayloadWidth>
              <span slot="title"></span>
              <div class='d-flex flex-column' slot="payload">
                <span>Contact Person</span>
                <span class="text-muted">Dealership Name</span>
                <span class="text-muted">City, State</span>
              </div>
            </detail-row>
            <detail-row largePayloadWidth>
              <span slot="title">Used Standard Links:</span>
              <div slot="payload" >
                <b-form-radio v-model='row.item.details.hasToUseCustomLink' @change="onChangeChecked(row.item.details, false)" :value='false' :name="row.item.details.id">
                <span>Link:</span>
                <a href="#" class="text-primary h6"><u>View Presentation</u></a>
                <span>|</span>
                <a href="#" class="text-primary h6"><u>View Our Other Vehicles</u></a>
                </b-form-radio>
              </div>
            </detail-row>
            <detail-row largePayloadWidth>
              <span slot="title">Used Custom Links:</span>
              <div slot="payload" class="custom-flex-position">
                <b-form-radio v-model='row.item.details.hasToUseCustomLink' @change="onChangeChecked(row.item.details, true)" :value='true' :name="row.item.details.id" class="mt-2 mr-0">
                </b-form-radio>
                <span class="mt-2 mr-2">Link Text:</span>
                <b-form-input class="custom-input-width" v-model='row.item.details.customLinkText' :disabled='!row.item.details.hasToUseCustomLink' placeholder="Link Text"></b-form-input>
              </div>
            </detail-row>
            <detail-row largePayloadWidth>
              <span slot="title"></span>
              <div slot="payload" class="custom-flex-position">
                <span class="mt-2 mr-2 custom-margin">Link:</span>
                <b-form-input class="custom-input-width" placeholder="www.linkexample.com" v-model='row.item.details.customLinkHref' :disabled='!row.item.details.hasToUseCustomLink'></b-form-input>
              </div>
            </detail-row>
            <b-btn variant="primary" size="sm" @click="onSave(row)" class="fixed-sizes float-right"><font-awesome-icon icon="cloud-upload-alt" /> <span class="btn-title">Save</span></b-btn>
            <b-btn variant="secondary" size="sm" class="fixed-sizes mr-2 float-right" @click="showEditDetails(row)">Cancel</b-btn>
          </div>
        </b-card>
      </template>
    </b-table>
    <create-new-template-modal v-if='modalModel' @add='onAddNewEmailTemplate' :model='modalModel' :isModalVisible='isModalVisible' @hide='hide()'/>
  </div>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import createNewTemplateModal from './createNewTemplateModal'
import CustomEmailTemplate from '@/services/leads/CustomEmailTemplateService'

export default {
  name: 'leads-auto-responders-settings',
  props: {
    items: { type: Array, required: true }
  },
  data () {
    return {
      accountId: +this.$route.params.accountId,
      modalModel: null,
      isModalVisible: false
    }
  },
  components: {
    'detail-row': detailRow,
    'create-new-template-modal': createNewTemplateModal
  },
  computed: {
    getTableFields () {
      return [
        {
          key: 'name',
          label: 'Template Name',
          tdClass: 'py-2 align-middle text-truncate'
        },
        {
          key: 'subject',
          label: 'Email Subject',
          tdClass: 'py-2 align-middle text-truncate'
        },
        {
          key: 'messageBody',
          label: 'Email Message',
          tdClass: 'py-2 align-middle text-truncate'
        },
        {
          key: 'action',
          label: 'Action',
          tdClass: 'py-2 align-middle'
        }
      ]
    }
  },
  methods: {
    hide () {
      this.isModalVisible = false
    },
    onOpenAddNewTemplateModal () {
      CustomEmailTemplate.getCustomEmailTemplatePrototype(this.accountId).then(res => {
        this.modalModel = res.data
        this.isModalVisible = true
      }).catch(ex => {
        this.$toaster.error('Cannot get email template prototype')
        this.$logger.handleError(ex, 'Cannot get email template prototype')
      })
    },
    onAddNewEmailTemplate (model) {
      CustomEmailTemplate.createCustomEmailTemplates(this.accountId, model).then(res => {
        if (res.data) {
          this.$toaster.success('New Email Template Successfully Created')
          this.isModalVisible = false
        } else {
          this.$toaster.error('Cannot create new email template')
        }
      }).catch(ex => {
        this.$toaster.error(`Cannot create new email template. Message: ${ex.response.data}`)
        this.$logger.handleError(ex, 'Cannot create new email template', model)
      }).finally(() => {
        this.$emit('reload')
      })
    },
    async showEditDetails (row) {
      if (row.item.details) {
        row.toggleDetails()
      } else {
        try {
          const apiResult = await CustomEmailTemplate.getCustomEmailTemplateDetails(row.item.accountId, row.item.id)

          this.$set(row.item, 'details', apiResult.data)

          row.toggleDetails()
        } catch (err) {
          this.$toaster.error('Failed to get data from server')
          this.$logger.handleError(err, 'Can\'t load log details', row.item)
        }
      }
    },
    onSave (row) {
      if (row.item.details) {
        CustomEmailTemplate.updateCustomEmailTemplate({accountId: row.item.details.accountId, id: row.item.details.id, data: row.item.details}).then(res => {
          if (res.data) {
            this.$toaster.success('Email Template Successfully Updated')
          } else {
            this.$toaster.error('Cannot update email template')
          }
        }).catch(ex => {
          this.$toaster.error('Cannot update email template')
          this.$logger.handleError(ex, 'Cannot update email template', row.item.details)
        }).finally(() => {
          this.$emit('reload')
        })
      } else {
        this.showEditDetails(row)
      }
    },
    onDelete (item) {
      CustomEmailTemplate.deleteCustomEmailTemplate(item.accountId, item.id).then(res => {
        if (res.data) {
          this.$toaster.success('Email Template Successfully Deleted')
        } else {
          this.$toaster.error('Cannot delete email template')
        }
      }).catch(ex => {
        this.$toaster.error('Cannot delete email template')
        this.$logger.handleError(ex, `Cannot delete email template. Item: ${item}`)
      }).finally(() => {
        this.$emit('reload')
      })
    },
    onChangeChecked (item, checked) {
      item.hasToUseCustomLink = checked
    }
  }
}
</script>

<style scoped>
.custom-font-size {
  font-size: 11px;
}
.custom-flex-position {
  display: flex;
  flex-direction: row;
  flex-shrink: 1;
}
.custom-margin {
  margin-left: 3.5rem;
}

.custom-input-width {
  width: 65%;
}

@media (max-width: 576px) {
  .custom-flex-position {
   flex-direction: column;
  }
  .custom-margin {
    margin-left: 0;
  }
  .custom-input-width {
    width: 100%;
  }
}
</style>
