<template>
  <div>
    <traffic-sources-summary
      :pieSessionsItems="bar.pieSessionsItems"
      :pieLeadsItems="bar.pieLeadsItems"
    ></traffic-sources-summary>

    <traffic-sources-by-source-table
      :tableItems="table.items"
      :totalItems="table.totalItems"
      :pageNumber="page.filter.pageNumber"
      :pageSize="page.filter.pageSize"
      :sortType="page.filter.sortType"
      @pageNumberChanged="onPageNumberChanged"
      @pageSizeChanged="onPageSizeChanged"
      @sortTypeChanged="onSortTypeChanged"
    ></traffic-sources-by-source-table>
  </div>
</template>

<style src="@/vendor/styles/pages/analytics.scss" lang="scss"></style>

<script>
import analyticsConstants from './../../shared/analytics/constants'
import analyticsBuilders from './../../shared/analytics/builders'
import analyticsHelper from './helpers.js'
import baseReportPage from './baseReportPage.js'

import TrafficSourcesSummary from '../../components/analytics_ga4/summaries/trafficSourcesSummary'
import TrafficSourcesBySourceTable from '../../components/analytics_ga4/tables/trafficSourcesBySourceTable'

const filterManager = analyticsBuilders.getFilterManager({
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' },
  pageNumber: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  sortType: { type: Number, default: analyticsConstants.trafficSourceSortTypes.sessionsDesc }
})

export default {
  mixins: [baseReportPage],
  name: 'traffic-sources',
  metaInfo: {
    title: 'Analytics - Traffic Sources'
  },
  components: {
    TrafficSourcesBySourceTable,
    TrafficSourcesSummary
  },
  created () {
    this.$store.commit('analyticsGa4/setAnalyticsName', 'Traffic Sources')
    this.$store.commit('analyticsGa4/setBlackThemeOn', true)
  },
  data () {
    return {
      page: {
        filter: filterManager.defaultValue
      },
      bar: {
        pieSessionsItems: [],
        pieLeadsItems: []
      }
    }
  },
  computed: {
    filterManager () {
      return filterManager
    }
  },
  methods: {
    async updateStatistics () {
      try {
        await Promise.all([
          this.updateAccountLevelGraph(),
          this.updateAccountLevelTable()
        ])
      } catch (err) {
        this.$toaster.error('Failed to get data from server', { timeout: 8000 })
        this.$logger.handleError(err, 'Can\'t update statistics', { filter: this.page.filter, cache: this.cache })
      }
    },
    async updateAccountLevelGraph () {
      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getTrafficSourcesGraph',
        {
          accountId: this.accountId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo,
          count: 10
        }
      )

      this.bar.pieSessionsItems = store.graph.data.sessionsbysource
      this.bar.pieLeadsItems = store.graph.data.totalleadsbysource
    },
    async updateAccountLevelTable () {
      const store = await analyticsHelper.fetchOrGetCachedApiResult(
        this.cache,
        'analyticsGa4/getTrafficSourcesDetailsPage',
        {
          accountId: this.accountId,
          dateFrom: this.page.filter.dateFrom,
          dateTo: this.page.filter.dateTo,
          pageNumber: this.page.filter.pageNumber,
          pageSize: this.page.filter.pageSize,
          sortType: this.page.filter.sortType
        }
      )

      this.table.items = store.detailedData.data.items
      this.table.totalItems = store.detailedData.data.totalItems
    }
  }
}
</script>
