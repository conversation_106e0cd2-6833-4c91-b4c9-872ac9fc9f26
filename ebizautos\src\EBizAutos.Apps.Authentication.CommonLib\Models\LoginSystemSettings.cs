﻿using System;
using MongoDB.Bson.Serialization.Attributes;
using System.ComponentModel.DataAnnotations;

namespace EBizAutos.Apps.Authentication.CommonLib.Models {
	[BsonIgnoreExtraElements]
	public class LoginSystemSettings {
		[BsonId]
		[Required]
		public string Id { get; set; }

		public MfaSettings Mfa { get; set; } = new MfaSettings();
		public EmailSettings Email { get; set; } = new EmailSettings();
		public SmsSettings Sms { get; set; } = new SmsSettings();

		[BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
		public DateTime UpdatedAtUtc { get; set; }
	}
}