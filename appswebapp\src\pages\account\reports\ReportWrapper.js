import ReportService from '@/services/reports/ReportService'
import constants from '../../../shared/reports/constants'

export default {
  contactsReport: {
    USER_STORAGE_FILTER_KEY: 'CONTACTS_REPORT_FILTERS_KEY',
    filters: {
      search: '',
      includeRelations: false,
      displayFilterType: 0,
      locations: '',
      campaignTypeGroups: ''
    },
    generate: (filters) => {
      return ReportService.generateContactsReport(filters)
    },
    tableFields: constants.contactsReportTableFields,
    csvFields: constants.contactsReportTableFields.filter(x => x.key !== 'manageLink').map(x => x.key),
    csvLabels: constants.contactsReportTableFields.filter(x => x.key !== 'manageLink')
  },
  accountsLeadsSettingsReport: {
    USER_STORAGE_FILTER_KEY: 'LEADS_SETTINGS_REPORT_FILTERS_KEY',
    filters: {
      search: '',
      includeRelations: false
    },
    generate: (filters) => {
      return ReportService.generateLeadsSettingsReport(filters)
    },
    tableFields: constants.leadsSettingsReportTableFields.filter(x => !['autoArchiveLeadsDaysAfter', 'deleteArchiveLeadsDaysAfter', 'twilioSmsFee', 'twilioCallFee'].includes(x.key)),
    csvFields: constants.leadsSettingsReportTableFields.filter(x => x.key !== 'manageLink').map(x => x.key),
    csvLabels: constants.leadsSettingsReportTableFields.filter(x => x.key !== 'manageLink')
  },
  accountsLeadsWebFormReport: {
    USER_STORAGE_FILTER_KEY: 'LEADS_WEB_FORM_REPORT_FILTERS_KEY',
    filters: {
      search: '',
      includeRelations: false
    },
    generate: (filters) => {
      return ReportService.generateLeadsWebFormReport(filters)
    },
    tableFields: constants.leadsWebFormReportTableFields,
    csvFields: constants.leadsWebFormReportTableFields.filter(x => x.key !== 'manageLink').map(x => x.key),
    csvLabels: constants.leadsWebFormReportTableFields.filter(x => x.key !== 'manageLink')
  }
}
