import { subjectPlaceHolder } from './constant'

class LeadsEmailBuilder {
  constructor (vehicle, dealerInfo, contactInfo, userInfo, templateSettings, sendEmailOptions, siteUrlWithProtocol) {
    this.vehicle = vehicle
    this.dealerInfo = dealerInfo
    this.contactInfo = contactInfo
    this.userInfo = userInfo
    this.templateSettings = templateSettings
    this.sendEmailOptions = sendEmailOptions
    this.siteUrlWithProtocol = siteUrlWithProtocol
  };
  build () {
    let email = {
      from: this.contactInfo.email,
      fromName: this.contactInfo.contactNameWithDepartments,
      to: [this.userInfo.email],
      subject: this.buildEmailSubject(),
      emailBody: this.buildEmailBody(),
      isHtml: true
    }

    return email
  };
  buildEmailSubject () {
    let subject = this.sendEmailOptions ? this.sendEmailOptions.subject : ''
    if (this.sendEmailOptions) {
      if (this.sendEmailOptions.subject.includes(subjectPlaceHolder.yearPlaceHolder)) {
        subject = subject.replace(subjectPlaceHolder.yearPlaceHolder, this.vehicle ? this.vehicle.year : '')
      }
      if (this.sendEmailOptions.subject.includes(subjectPlaceHolder.makePlaceHolder)) {
        subject = subject.replace(subjectPlaceHolder.makePlaceHolder, this.vehicle ? this.vehicle.make : '')
      }
      if (this.sendEmailOptions.subject.includes(subjectPlaceHolder.modelPlaceHolder)) {
        subject = subject.replace(subjectPlaceHolder.modelPlaceHolder, this.vehicle ? this.vehicle.model : '')
      }
      if (this.sendEmailOptions.subject.includes(subjectPlaceHolder.trimPlaceHolder)) {
        subject = subject.replace(subjectPlaceHolder.trimPlaceHolder, this.vehicle ? this.vehicle.trim : '')
      }
    }
    if (this.sendEmailOptions && this.sendEmailOptions.subject.includes(subjectPlaceHolder.companyPlaceHolder)) {
      subject = subject.replace(subjectPlaceHolder.companyPlaceHolder, this.dealerInfo.companyName)
    }

    return subject
  };
  buildEmailBody () {
    let emailBody = `<html>` +
    `<head>` +
      `<style type='text/css'>` +
        `.EmailText12 {{FONT-SIZE: 12px; COLOR: #252525; FONT-FAMILY: Verdana,Arial,Helvetica;}}` +
        `a.emaillink12 {{FONT-SIZE: 12px; color: #0052A4; FONT-FAMILY: Verdana,Arial,Helvetica;}}` +
        `a.emaillink12:hover {{FONT-SIZE: 12px; color: #000000; FONT-FAMILY: Verdana,Arial,Helvetica;}}` +
        `.emailText16 {{FONT-SIZE: 16px; COLOR: #252525; font-weight : bold; FONT-FAMILY: Verdana,Arial,Helvetica;}}` +
        `.emailText16a {{FONT-SIZE: 16px; COLOR: #252525; font-weight : bold; FONT-FAMILY: Trebuchet MS,Verdana,Arial,Helvetica;}}` +
      `</style>` +
    `</head>` +
    `<body>` +
      `<table width='550' cellpadding='0' cellspacing='0' border='0' style='border:1px solid #CCCCCC;' class='EmailText12'>`
    emailBody = emailBody + this.buildDealerAndContactInfo()
    emailBody = emailBody + this.buildVehicleDetailPart()
    emailBody = emailBody + this.buildMessagePart()
    emailBody = emailBody + this.buildLinkPart()
    emailBody = emailBody + `</table></body></html>`

    return emailBody
  };
  buildVehicleDetailPart () {
    if (!this.templateSettings.hasToDisplayVehicleDetails || !this.vehicle) {
      return ''
    }
    let htmlPart = `<tr>` +
      `<td width="25%" style="border-bottom:dashed 1px; border-color:#aaa; height:90px; padding-left:10px; padding-top:10px; vertical-align:top;  padding-bottom:10px;">` +
        `<img src='${this.vehicle.presentationPhotoPath}' width='108' height='80'/></td>` +
      `<td colspan="3" style="border-bottom:dashed 1px; border-color:#aaa; vertical-align:top; padding-top:15px; padding-left:10px; line-height:17px;" width="75%">` +
        `<div style="font-weight:bold;" class="emailText16a">${this.vehicle.year} ${this.vehicle.make} ${this.vehicle.model} ${this.vehicle.trim}</div><br>` +
        `<span>${this.vehicle.mileage ? this.vehicle.mileage : ''}</span><span>${this.vehicle.stockNumber ? 'Stock #: ' + this.vehicle.stockNumber : ''}</span><br>` +
        `<a href='${this.siteUrlWithProtocol}${this.vehicle.vehicleDetailsLink}' target='_blank' class='emaillink12'><span style="background-color: #0188e0; border: none; color: white; padding: 3px 15px; text-align: center;` +
        `text-decoration: none;` +
        `display: inline-block;` +
        `font-size: 12px;">View Details</span></a>` +
        `</td>`

    return htmlPart
  };
  buildDealerAndContactInfo () {
    let htmlPart = `<tr style='line-height:20px;'>` +
        `<td colspan='2' style='padding-left:10px; height:50px; background-color:#EDF3FE;' width='50%'>` +
          `<div style='font-weight:bold;' class='emailText16a'>${this.dealerInfo ? this.dealerInfo.companyName : ''}</div>` +
          `<span>${this.dealerInfo.address ? this.dealerInfo.address.city : ''}</span>` +
          `<span>${this.dealerInfo.address && this.dealerInfo.address.state ? this.dealerInfo.address.state.name : ''}</span>` +
       `</td>` +
        `<td colspan='2' align='right' style='background-color:#EDF3FE; padding-right:10px' width='50%'>` +
          `<div style='font-weight:bold;' class='emailText16a'>${this.contactInfo & this.contactInfo.selectedPhone ? this.contactInfo.selectedPhone.title : ''}</div>` +
          `<a href='mailTo:${this.contactInfo ? this.contactInfo.email : ''}'>${this.contactInfo ? this.contactInfo.email : ''}</a>` +
        `</td>` +
      `</tr>`

    return htmlPart
  };
  buildMessagePart () {
    let htmlPart = ''
    if (this.sendEmailOptions && this.sendEmailOptions.emailMessage) {
      htmlPart = `<tr>` +
        `<td colspan='4' style='padding-left:10px; padding-top: 10px'>` +
          `<span>${this.sendEmailOptions.emailMessage}</span>` +
        `</td>` +
      `</tr>`
    } else {
      htmlPart = `<tr>` +
        `<td colspan='4' style='padding-left:10px; padding-top: 10px'>` +
          `<span>${this.templateSettings ? this.templateSettings.messageBody : ''}</span>` +
        `</td>` +
      `</tr>`
    }

    return htmlPart
  };
  buildLinkPart () {
    let htmlPart = `<tr> <td style='line-height:20px; padding:5px 0px 10px 10px; ' colspan='4'><b>Links: </b>`
    if (this.templateSettings && this.templateSettings.hasToUseCustomLink) {
      htmlPart = htmlPart + `<a href='${this.templateSettings.customLinkHref}' target='_blank' class='emaillink12'>${this.templateSettings.customLinkText}</a>`
    } else {
      if (this.vehicle) {
        htmlPart = htmlPart + `<a href='${this.siteUrlWithProtocol}${this.vehicle.vehicleDetailsLink}' target='_blank' class='emaillink12'>View Presentation</a> | `
      }
      htmlPart = htmlPart + `<a href='${this.siteUrlWithProtocol}/inventory.aspx' target='_blank' class='emaillink12'>View Our Vehicles</a>`
    }
    htmlPart = htmlPart + `</td></tr>`

    return htmlPart
  };
}

export default LeadsEmailBuilder
