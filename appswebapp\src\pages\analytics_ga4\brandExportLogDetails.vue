<template>
  <div>
    <div v-if="!isLoading && log">
        <div class="d-flex bd-highlight">
          <div class="mr-auto py-2 bd-highlight">
            <h4 class="float-left">Brand Export Details Log</h4>
          </div>
          <div class="py-2 bd-highlight">
            <b-btn @click="$router.go(-1)" class="float-right">Close</b-btn>
          </div>
        </div>
      <b-card>
        <log-node
          v-if="log"
          :data="log"
          :isExpandedShallow="true"
          :isExpandedDeep="false"
        />
      </b-card>
    </div>
    <div v-else-if="isLoading" class="text-muted">
      Loading...
    </div>
    <div class="ml-4 font-weight-bolder" v-else>Not found</div>
  </div>
</template>

<script>
import BrandExportService from '@/services/analytics/BrandExportService'

export default {
  name: 'analytics-brand-export-log-details',
  props: {
    logId: { type: String, required: true }
  },
  data () {
    return {
      log: null,
      isLoading: true
    }
  },
  components: {
    'log-node': () => import('@/components/_shared/logItemNode.vue')
  },
  created () {
    this.loadContent()
  },
  methods: {
    loadContent () {
      BrandExportService.getBrandExportsLogDetails(this.logId).then(res => {
        this.log = res.data.model
      }).catch(ex => {
        this.$toaster.error('Something went wrong')
        this.$logger.handleError(ex, 'Cannot get brand export log detail item')
      }).finally(() => {
        this.isLoading = false
      })
    }
  }
}
</script>
