class VehicleDifferenceBuilder {
  static build (oldModel, newModel) {
    let updateModel = buildRootModel(oldModel, newModel)
    updateModel.miscellaneousVehicleDetails = buildVehicleMiscellaneousDetailsUpdateModel(oldModel.miscellaneousVehicleDetails, newModel.miscellaneousVehicleDetails)
    updateModel.colors = buildVehicleColorDetailsUpdateModel(oldModel.colors, newModel.colors)
    updateModel.pricing = buildVehiclePricingDetailsUpdateModel(oldModel.pricing, newModel.pricing)
    updateModel.payment = buildVehiclePaymentDetailsUpdateModel(oldModel.payment, newModel.payment)
    updateModel.warrantyInformation = buildVehicleWarrantyInformationUpdateModel(oldModel.warrantyInformation, newModel.warrantyInformation)
    updateModel.certificationInformation = buildVehicleCertificationDetailsUpdateModel(oldModel.certificationInformation, newModel.certificationInformation)
    updateModel.marketingInformation = buildVehicleMarketingInformationUpdateModel(oldModel.marketingInformation, newModel.marketingInformation)
    updateModel.galleryIntroDescription = buildVehicleIntroTextUpdateModel(oldModel.galleryIntroDescription, newModel.galleryIntroDescription)
    updateModel.eBayIntroDescription = buildVehicleIntroTextUpdateModel(oldModel.eBayIntroDescription, newModel.eBayIntroDescription)
    updateModel.features = buildVehicleFeaturesUpdateModel(oldModel.features, newModel.features)
    updateModel.featureCategories = buildVehicleFeatureCategoriesUpdateModel(oldModel.featureCategories, newModel.featureCategories)
    updateModel.oemPackages = buildVehicleOEMPackagesUpdateModel(oldModel.oemPackages, newModel.oemPackages)
    updateModel.oemOptions = buildVehicleOEMOptionsUpdateModel(oldModel.oemOptions, newModel.oemOptions)
    updateModel.reportInformation = buildVehicleReportInformation(oldModel.reportInformation, newModel.reportInformation)
    updateModel.videos = buildVideoUpdateModel(oldModel.videos, newModel.videos)

    return updateModel
  }

  static buildPhotosDifference (oldModel, newModel) {
    let photos = ((newModel.photos || {}).actualPhotos || {}).photoItems || []
    let oldPhotos = (((oldModel.photos || {}).actualPhotos || {}).photoItems) || []

    let isModified = photos.length !== oldPhotos.length

    if (photos.length === oldPhotos.length) {
      for (let i = 0; i < photos.length; i++) {
        if (photos[i].index !== oldPhotos[i].index) {
          isModified = true
          break
        }
      }
    }

    return isModified ? photos.map(x => x.index) : null
  }

  static isVehicleChanged (oldModel, newModel) {
    let vehicleDiff = {
      ...VehicleDifferenceBuilder.build(oldModel, newModel),
      photoIndexes: VehicleDifferenceBuilder.buildPhotosDifference(oldModel, newModel)
    }

    if (newModel.isModified) {
      vehicleDiff.isModified = true
    }

    let diffObj = JSON.parse(JSON.stringify(vehicleDiff, (key, value) => {
      if (!(value === null || key === 'vin')) { // vin is present even in unchanged vehicle
        return value
      }
    }))

    return Object.keys(diffObj).length > 0
  }
}

let buildRootModel = function (oldModel, newModel) {
  if (oldModel.vin !== newModel.vin) {
    throw new Error('Vin number should match')
  }

  let result = {}

  result.vin = newModel.vin
  result.vehicleStatus = getUpdatedField(oldModel.vehicleStatus, newModel.vehicleStatus).value
  result.stockNumber = getUpdatedField(oldModel.stockNumber, newModel.stockNumber).value
  result.year = getUpdatedField(oldModel.year, newModel.year).value
  result.make = getUpdatedField(oldModel.make, newModel.make).value
  result.model = getUpdatedField(oldModel.model, newModel.model).value
  result.bodyStyleId = getUpdatedField(oldModel.bodyStyleId, newModel.bodyStyleId).value
  result.engineFuelId = getUpdatedField(oldModel.engineFuelId, newModel.engineFuelId).value
  result.engine = getUpdatedField(oldModel.engine, newModel.engine).value
  result.eBayEngineSize = getUpdatedField(oldModel.eBayEngineSize, newModel.eBayEngineSize).value
  result.doors = getUpdatedField(oldModel.doors, newModel.doors).value
  result.cylindersType = getUpdatedField(oldModel.cylindersType, newModel.cylindersType).value
  result.transTypeId = getUpdatedField(+oldModel.transTypeId, +newModel.transTypeId).value
  result.transGearsId = getUpdatedField(+oldModel.transGearsId, +newModel.transGearsId).value
  result.drivetrainId = getUpdatedField(oldModel.drivetrainId, newModel.drivetrainId).value
  result.cabStyleId = getUpdatedField(oldModel.cabStyleId, newModel.cabStyleId).value
  result.bedStyleId = getUpdatedField(oldModel.bedStyleId, newModel.bedStyleId).value
  result.vehicleStyleId = getUpdatedField(oldModel.vehicleStyleId, newModel.vehicleStyleId).value
  result.vehicleYearModelId = getUpdatedField(oldModel.vehicleYearModelId, newModel.vehicleYearModelId).value
  result.trim = getUpdatedField(oldModel.trim, newModel.trim).value
  result.isStandardFeaturesTurnedOn = getUpdatedField(oldModel.isStandardFeaturesTurnedOn, newModel.isStandardFeaturesTurnedOn).value
  result.isOptionsPackagesTurnedOn = getUpdatedField(oldModel.isOptionsPackagesTurnedOn, newModel.isOptionsPackagesTurnedOn).value
  result.isFeaturesSpecificationsTurnedOn = getUpdatedField(oldModel.isFeaturesSpecificationsTurnedOn, newModel.isFeaturesSpecificationsTurnedOn).value

  return result
}

let buildVehicleMiscellaneousDetailsUpdateModel = function (oldM, newM) {
  if (newM == null) {
    return null
  }

  if (oldM == null) {
    oldM = {}
  }

  let result = {}
  let isModified = false
  let updatedField

  updatedField = getUpdatedField(oldM.titleTypeId, newM.titleTypeId)
  result.titleTypeId = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.mileage, newM.mileage)
  result.mileage = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.hasToDisplayMpg, newM.hasToDisplayMpg)
  result.hasToDisplayMpg = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.isIncomingInventory, newM.isIncomingInventory)
  result.isIncomingInventory = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.inStockDate, newM.inStockDate)
  result.inStockDate = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.contactId, newM.contactId)
  result.contactId = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.importStatusCode, newM.importStatusCode)
  result.importStatusCode = updatedField.value
  isModified |= updatedField.isModified

  return isModified ? result : null
}

let buildVehicleColorDetailsUpdateModel = function (oldM, newM) {
  if (newM == null) {
    return null
  }

  if (oldM == null) {
    oldM = {}
  }

  let result = {}
  let isModified = false
  let updatedField

  updatedField = getUpdatedField(oldM.extBasicColorId, newM.extBasicColorId)
  result.extBasicColorId = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.extColorName, newM.extColorName)
  result.extColorName = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.extColorHex, newM.extColorHex)
  result.extColorHex = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.intBasicColorId, newM.intBasicColorId)
  result.intBasicColorId = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.intColorName, newM.intColorName)
  result.intColorName = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.intColorHex, newM.intColorHex)
  result.intColorHex = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.interiorSurface, newM.interiorSurface)
  result.interiorSurface = updatedField.value
  isModified |= updatedField.isModified

  return isModified ? result : null
}

let buildVehiclePricingDetailsUpdateModel = function (oldM, newM) {
  if (newM == null) {
    return null
  }

  if (oldM == null) {
    oldM = {}
  }

  let result = {}
  let isModified = false
  let updatedField

  updatedField = getUpdatedField(oldM.highPrice, newM.highPrice)
  result.highPrice = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.highPriceLabel, newM.highPriceLabel)
  result.highPriceLabel = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.highPriceLockedBy, newM.highPriceLockedBy)
  result.highPriceLockedBy = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.lowPrice, newM.lowPrice)
  result.lowPrice = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.lowPriceLabel, newM.lowPriceLabel)
  result.lowPriceLabel = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.lowPriceLockedBy, newM.lowPriceLockedBy)
  result.lowPriceLockedBy = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.KBBRetail, newM.KBBRetail)
  result.KBBRetail = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.KBBWholesale, newM.KBBWholesale)
  result.KBBWholesale = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.vehicleCost, newM.vehicleCost)
  result.vehicleCost = updatedField.value
  isModified |= updatedField.isModified

  return isModified ? result : null
}

let buildVehiclePaymentDetailsUpdateModel = function (oldM, newM) {
  if (newM == null) {
    return null
  }

  if (oldM == null) {
    oldM = {}
  }

  let result = {}

  if (newM.loan) {
    result.loan = buildVehicleLoanPaymentUpdateModel(oldM.loan, newM.loan)
  }

  if (newM.lease) {
    result.lease = buildVehicleLoanPaymentUpdateModel(oldM.lease, newM.lease)
  }

  return (result.loan || result.lease)
    ? result
    : null
}

let buildVehicleLoanPaymentUpdateModel = function (oldM, newM) {
  if (newM == null) {
    return null
  }

  if (oldM == null) {
    oldM = {}
  }

  let result = {}
  let isModified = false

  result.salesTax = newM.salesTax
  isModified |= (oldM.salesTax !== newM.salesTax)

  result.term = newM.term
  isModified |= (oldM.term !== newM.term)

  result.interestRate = newM.interestRate
  isModified |= (oldM.interestRate !== newM.interestRate)

  result.downPayment = newM.downPayment
  isModified |= (oldM.downPayment !== newM.downPayment)

  result.loanPer1000Borrowed = newM.loanPer1000Borrowed
  isModified |= (oldM.loanPer1000Borrowed !== newM.loanPer1000Borrowed)

  result.paymentAmount = newM.paymentAmount
  isModified |= (oldM.paymentAmount !== newM.paymentAmount)

  result.hasToDisplay = newM.hasToDisplay
  isModified |= (oldM.hasToDisplay !== newM.hasToDisplay)

  return isModified ? result : null
}

/*
let buildVehicleLeasePaymentUpdateModel = function (oldM, newM) {
  if (newM == null) {
    return null
  }

  if (oldM == null) {
    oldM = {}
  }

  let result = {}
  let isModified = false

  result.salesTax = newM.salesTax
  isModified |= (oldM.salesTax !== newM.salesTax)

  result.term = newM.term
  isModified |= (oldM.term !== newM.term)

  result.interestRate = newM.interestRate
  isModified |= (oldM.interestRate !== newM.interestRate)

  result.downPayment = newM.downPayment
  isModified |= (oldM.downPayment !== newM.downPayment)

  result.residualValue = newM.residualValue
  isModified |= (oldM.residualValue !== newM.residualValue)

  result.paymentAmount = newM.paymentAmount
  isModified |= (oldM.paymentAmount !== newM.paymentAmount)

  result.hasToDisplay = newM.hasToDisplay
  isModified |= (oldM.hasToDisplay !== newM.hasToDisplay)

  return isModified ? result : null
}
*/

let buildVehicleWarrantyInformationUpdateModel = function (oldM, newM) {
  if (newM == null) {
    return null
  }

  if (oldM == null) {
    oldM = {}
  }

  let result = {}
  let isModified = false
  let updatedField

  updatedField = getUpdatedField(oldM.warrantyTypeId, newM.warrantyTypeId)
  result.warrantyTypeId = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.buyersGuideId, newM.buyersGuideId)
  result.buyersGuideId = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.buyersGuide2Id, newM.buyersGuide2Id)
  result.buyersGuide2Id = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.description, newM.description)
  result.description = updatedField.value
  isModified |= updatedField.isModified

  return isModified ? result : null
}

let buildVehicleCertificationDetailsUpdateModel = function (oldM, newM) {
  if (newM == null) {
    return null
  }

  if (oldM == null) {
    oldM = {}
  }

  let result = {}
  let isModified = false
  let updatedField

  updatedField = getUpdatedField(oldM.isOEMCpo, newM.isOEMCpo)
  result.isOEMCpo = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.oemCpoLockedBy, newM.oemCpoLockedBy)
  result.oemCpoLockedBy = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.isDealerCpo, newM.isDealerCpo)
  result.isDealerCpo = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.dealerGroupCpoId, newM.dealerGroupCpoId)
  result.dealerGroupCpoId = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.cpoInspectionNumber, newM.cpoInspectionNumber)
  result.cpoInspectionNumber = updatedField.value
  isModified |= updatedField.isModified

  return isModified ? result : null
}

let buildVehicleMarketingInformationUpdateModel = function (oldM, newM) {
  if (newM == null) {
    return null
  }

  if (oldM == null) {
    oldM = {}
  }

  let result = {}
  let isModified = false
  let updatedField

  updatedField = getUpdatedField(oldM.eBizKeywords, newM.eBizKeywords)
  result.eBizKeywords = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.isHomepageFeatured, newM.isHomepageFeatured)
  result.isHomepageFeatured = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.hasToIncludePromotionalFlag, newM.hasToIncludePromotionalFlag)
  result.hasToIncludePromotionalFlag = updatedField.value
  isModified |= updatedField.isModified

  return isModified ? result : null
}

let buildVehicleIntroTextUpdateModel = function (oldM, newM) {
  if (newM == null) {
    return null
  }

  if (oldM == null) {
    oldM = {}
  }

  let result = {}
  let isModified = false
  let updatedField

  updatedField = getUpdatedField(oldM.scrollingText, newM.scrollingText)
  result.scrollingText = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.hasCustomDescription, newM.hasCustomDescription)
  result.hasCustomDescription = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.customDescriptionText, newM.customDescriptionText)
  result.customDescriptionText = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.autoDescriptionText, newM.autoDescriptionText)
  result.autoDescriptionText = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.siblingAutoDescriptionText, newM.siblingAutoDescriptionText)
  result.siblingAutoDescriptionText = updatedField.value
  isModified |= updatedField.isModified

  updatedField = getUpdatedField(oldM.descriptionLockedBy, newM.descriptionLockedBy)
  result.descriptionLockedBy = updatedField.value
  isModified |= updatedField.isModified

  return isModified ? result : null
}

let buildVehicleReportInformation = function (oldM, newM) {
  if (newM == null) {
    return null
  }

  let result = {}
  let isModified = false

  let updatedField = getUpdatedField(oldM.vehicleHistoryType, newM.vehicleHistoryType)
  result.vehicleHistoryType = updatedField.value
  isModified |= updatedField.isModified

  if (oldM.autocheck || newM.autocheck) {
    updatedField = getUpdatedField((oldM.autocheck || {}).hasToBlockAutoLink, (newM.autocheck || {}).hasToBlockAutoLink)
    result.autocheck = {
      hasToBlockAutoLink: updatedField.value
    }
    isModified |= updatedField.isModified
  }

  if (oldM.carfax || newM.carfax) {
    updatedField = getUpdatedField((oldM.carfax || {}).hasToBlockAutoLink, (newM.carfax || {}).hasToBlockAutoLink)
    result.carfax = {
      hasToBlockAutoLink: updatedField.value
    }
    isModified |= updatedField.isModified
  }

  return isModified ? result : null
}

let buildVideoUpdateModel = function (oldM, newM) {
  if (newM == null) {
    return null
  }

  let result = []
  let isModified = false

  oldM.videoItems.forEach(oldVideo => {
    let newVideo = newM.videoItems.find(x => x.id === oldVideo.id)

    if (
      (newVideo != null && newVideo.hasToDisplay !== oldVideo.hasToDisplay) ||
      (newVideo != null && newVideo.hasToDelete)
    ) {
      isModified = true

      result.push({
        Id: newVideo.id,
        hasToDisplay: newVideo.hasToDisplay,
        hasToDelete: newVideo.hasToDelete
      })
    }
  })

  return isModified ? { videoItems: result } : null
}

let buildVehicleFeaturesUpdateModel = function (oldM, newM) {
  if (newM == null) {
    return null
  }

  let result = []
  let isModified = false

  newM.forEach(function (feature) {
    let oldFeature = oldM.find(x => x.id === feature.id)

    if (oldFeature == null || (oldFeature.value !== feature.value)) {
      isModified = true

      result.push({
        Id: feature.id,
        ValueType: feature.valueType,
        Value: feature.value
      })
    }
  })

  return isModified ? result : null
}

let buildVehicleFeatureCategoriesUpdateModel = function (oldM, newM) {
  if (newM == null) {
    return null
  }

  let result = []
  let isModified = false

  newM.forEach(function (featureCategory) {
    let oldFeatureCategory = oldM.find(x => x.featureCategoryType === featureCategory.featureCategoryType)

    if (oldFeatureCategory == null || (oldFeatureCategory.isDisplayed !== featureCategory.isDisplayed)) {
      isModified = true

      result.push({
        featureCategoryTypeId: featureCategory.featureCategoryType,
        isDisplayed: featureCategory.isDisplayed
      })
    }
  })

  return isModified ? result : null
}

let buildVehicleOEMPackagesUpdateModel = function (oldM, newM) {
  let oldIds = oldM && oldM.map(x => x.packageCodeId)
  let newIds = oldM && newM.map(x => x.packageCodeId)

  if (newIds == null || (oldIds && oldIds.equals(newIds, true))) {
    return null
  }

  return newIds
}

let buildVehicleOEMOptionsUpdateModel = function (oldM, newM) {
  let oldIds = oldM && oldM.map(x => x.optionCodeId)
  let newIds = oldM && newM.map(x => x.optionCodeId)

  if (newIds == null || (oldIds && oldIds.equals(newIds, true))) {
    return null
  }

  return newIds
}

let getUpdatedField = function (oldF, newF) {
  // eslint-disable-next-line
  if (oldF == newF || (Number.isNaN(oldF) && Number.isNaN(oldF))) {
    return {
      value: null,
      isModified: false
    }
  } else {
    return {
      value: newF,
      isModified: true
    }
  }
}

export default VehicleDifferenceBuilder
