{"Kestrel": {"EndPoints": {"Http": {"Url": "http://0.0.0.0:9000"}}}, "AppSettings": {"ApplicationName": "Apps Shared API (dev)", "IsDev": "true", "DataAccessEncryptKey": "fpABRddPOOg0hbm1PUHIjw==", "DataAccessEncryptIV": "AAAAAAAAAAAAAAAAAAAAAA==", "LegacyDataAccessEncryptKey": "7kkRcaz5l2TvAQzGxfKLF4a7oFlsrn6W", "LegacyDataAccessEncryptIV": "7rmR5t39n2sd", "AuthenticationDetails": ["API supports two types of authentication:", "1. Server cookies", "2. JWT token in request header. Example:", "<code>'Authorization: Bear<PERSON> {your_token}'</code>"]}, "DbSettings": {"UsersMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/cp?retryWrites=true&w=majority", "SettingsMongoDbRepositoryConnectionStrings": "mongodb+srv://ebizdev:<EMAIL>/cp?retryWrites=true&w=majority", "AppsAccountsMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/accounts?retryWrites=true&w=majority", "AppsSiteSettingsMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/sites?retryWrites=true&w=majority", "SystemToolsLogsMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/appssystemtoolslogs?retryWrites=true&w=majority"}, "ExceptionSettings": {"ErrorWebServiceLocation": "http://errors.aws.ebizautos.com/queuedservice.asmx", "ApplicationCategoryId": 9, "MailServer": "l2ms04.ebizautos.colo", "ErrorEmailFrom": "<EMAIL>", "ErrorEmailTo": ["alex.i<PERSON><PERSON><PERSON>@ebizautos.com"], "EmailCc": [], "HasToUseServiceBus": false}, "TwilioSettings": {"AccountSid": "**********************************", "AuthToken": "2c8d79bc082dc1ae81107e556d4745cb"}, "ServiceBusSettings": {"Host": "b-14921ad0-337a-4458-84ee-c042b1327094-1.mq.us-east-1.amazonaws.com", "Port": 61617, "Username": "ebizeventbus", "Password": "z$32s0d-nd8G62!js83aPuhpUh", "FailoverHosts": ["b-14921ad0-337a-4458-84ee-c042b1327094-1.mq.us-east-1.amazonaws.com"], "HasToUseSsl": true, "PublishSettings": {"RetryAttempts": 3, "MinRetryDelayInMs": 5000, "MaxRetryDelayInMs": 10000}}, "MobileSettings": {"PlayMarketCPMobileLink": "https://play.google.com/store/apps/details?id=com.ebizautos.cpmobile", "ITunesCpMobileLink": "https://itunes.apple.com/lookup?bundleId=com.ebizautos.cpmobile"}, "TurnstileSettings": {"IsValidationDisabled": true, "SecretKey": "0x4AAAAAAA1U5yeI1P_OHDgWKLStvUcB7Gc", "SiteVerifyUrl": "https://challenges.cloudflare.com/turnstile/v0/siteverify", "BypassHeaders": ["eBiz-Rapid7 Scan", "eBiz-Internal-Testing"]}, "EmailSMTPSettings": {"EmailServer": "email-smtp.us-east-1.amazonaws.com", "Port": 587, "UserName": "AKIAJ63DJM7JPLD4746A", "Password": "AkQS3wwECygPDx6tSkdHWmvtVW5P76sm5QbBak3Y6JjY", "EnableSsl": true, "EmailFrom": "<EMAIL>", "PasswordRecoveryEmailBody": ["<!DOCTYPE html PUBLIC '-//W3C//DTD XHTML 1.0 Transitional//EN' 'http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd'>", "<html xmlns='http://www.w3.org/1999/xhtml'>", "<head>", "\t<meta http-equiv='X-UA-Compatible' content='IE=EDGE' />", "\t<meta http-equiv='Content-Type' content='text/html; charset=UTF-8' />", "\t<title>Recovering the password email</title>", "</head>", "<body>", "\t<div style=\"background-color:#185C9B;padding:20px;\">", "\t\t<a href=\"http://www.ebizautos.com?src=eBizEmail\" target=\"_blank\"><img width=\"149\" height=\"21\" style=\"border-width:0px\" alt=\"eBizAutos\" src=\"http://images.ebizautos.media/leads/ebizleademaillogo.png\"></a>", "\t</div>", "\t<div style=\"border: 1px solid #185C9B;padding: 20px;\">", "\t\tYou requested password for your account.", "\t\t<br />", "\t\tIf you haven't done it, just ignore this message.", "\t\t<br />", "\t\t<br />", "\t\t<br />", "\t\tUsername: {0}", "\t\t<br />", "\t\tPassword: {1}", "\t</div>", "</body>", "</html>"]}}