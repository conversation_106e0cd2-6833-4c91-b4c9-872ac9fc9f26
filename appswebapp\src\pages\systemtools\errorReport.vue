<template>
  <div>
    <b-row>
      <b-col>
        <h4>Error Report Tool</h4>
      </b-col>
      <b-col v-if="hasUserAccessToAddRule">
        <b-btn class="btn-round float-right" size="sm" variant="dark" @click="onOpenAddErrorRuleModal"><span class="ion ion-ios-add mr-1"></span><span>Add New Error Rule</span></b-btn>
      </b-col>
    </b-row>
   <b-tabs v-model="selectedTab" class="nav-tabs-top nav-responsive-sm" no-fade>
      <b-tab v-for="tab in getTabOptions" :key="tab.key" :title="tab.title">
      </b-tab>
      <error-report-viewer :hasUserFullAccess="hasUserFullAccess" v-if="selectedTab === tabOptions.errorReportViewer.key"/>
      <rule-manager v-if="selectedTab === tabOptions.ruleManager.key"/>
      <custom-errors-tool v-if="selectedTab === tabOptions.customErrorsTool.key"/>
    </b-tabs>
    <b-modal
      name="add-new-error-rule-modal"
      title="Add New Error Rule Modal"
      no-close-on-backdrop
      :visible="isAddNewErrorRuleModalOpen"
      @hide="onHideAddNewErrorRuleModal"
    >
      <b-form-radio-group
        v-model="errorRuleModel.errorReportType"
        :options="errorReportTypeOptions"
        name="error-report-types"
      />
      <detail-row :large-payload-width="true">
        <span slot="title">Error Regex:</span>
        <b-form-input slot="payload" w-90 placeholder="ex: first rule item%second rule item%..." v-model="errorRuleModel.errorRuleBody"></b-form-input>
      </detail-row>
      <detail-row :large-payload-width="true">
        <span slot="title">Error Comment:</span>
        <b-form-input slot="payload" w-90 v-model="errorRuleModel.errorRuleComment"></b-form-input>
      </detail-row>
      <template #modal-footer>
        <b-btn @click="onAddNewErrorRule" size="sm" variant="primary">Submit</b-btn>
        <b-btn @click="onHideAddNewErrorRuleModal" size="sm">Close</b-btn>
      </template>
    </b-modal>
  </div>
</template>

<script>
import customErrorsTool from '@/components/errorReport/customErrorsTool'
import errorReportViewer from '@/components/errorReport/errorReportViewer'
import ruleManager from '@/components/errorReport/ruleManager'
import detailRow from '@/components/details/helpers/detailRow'
import { errorReportTypes } from '@/shared/errorReport/constants'
import { mapGetters } from 'vuex'
import permissions from '../../shared/common/permissions'

export default {
  name: 'error-report',
  metaInfo: {
    title: 'Error Report'
  },
  data () {
    return {
      errorRuleModel: {
        errorReportType: 1,
        errorRuleBody: '',
        errorRuleComment: ''
      },
      isAddNewErrorRuleModalOpen: false,
      selectedTab: 0,
      tabOptions: {
        errorReportViewer: { key: 0, title: 'Error Report Viewer' },
        ruleManager: { key: 1, title: 'Rule Manager' },
        customErrorsTool: { key: 2, title: 'Custom Errors Tool' }
      }
    }
  },
  components: {
    'error-report-viewer': errorReportViewer,
    'rule-manager': ruleManager,
    'custom-errors-tool': customErrorsTool,
    'detail-row': detailRow
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {hasPermissions: () => false}).user
    },
    hasUserAccessToAddRule () {
      return this.hasUserFullAccess
    },
    hasUserFullAccess () {
      return this.user.hasPermissions(permissions.FullAccess)
    },
    errorReportTypeOptions () {
      return Object.values(errorReportTypes)
    },
    getTabOptions () {
      let options = [this.tabOptions.errorReportViewer]
      if (this.hasUserFullAccess) {
        options.push(this.tabOptions.ruleManager)
        options.push(this.tabOptions.customErrorsTool)
      }
      return options
    }
  },
  methods: {
    onOpenAddErrorRuleModal () {
      this.isAddNewErrorRuleModalOpen = true
    },
    onHideAddNewErrorRuleModal () {
      this.isAddNewErrorRuleModalOpen = false
    },
    onAddNewErrorRule () {
      this.isAddNewErrorRuleModalOpen = false
      this.$store.dispatch('systemTools/createErrorRule', this.errorRuleModel).then(res => {
        this.$toaster.success(`New Error Rule Successfully Created`)
      }).catch(ex => {
        if (ex.response && ex.response.status === 400 && ex.response.data && ex.response.data.executionResultMessage) {
          this.$toaster.error(ex.response.data.executionResultMessage)
        } else {
          let message = ex.response ? ex.response.data : ex.message
          this.$toaster.error(`Error Occurred on call api. Message: ${message}`)
          this.$logger.handleError(ex, 'Error Report Exception. Method: onAddNewErrorRule')
        }
      }).finally(() => {
        this.errorRuleModel.errorRuleBody = ''
        this.errorRuleModel.errorRuleComment = ''
      })
    }
  }
}
</script>
